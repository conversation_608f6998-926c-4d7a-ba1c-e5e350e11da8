<template>
  <el-dialog :model-value="visible" :close-on-click-modal="false" width="600" title="" @close="close">
    <template #header>
      <div font-semibold text-center>回访计划详情</div>
    </template>
    <el-scrollbar wrap-style="max-height: calc(100vh - 320px)">
      <div>
        <div border-bottom class="p-20px mb-20px">
          <div font-semibold class="text-16px">
            {{ managementPlan && managementPlan.planName }}
          </div>
          <div class="flex pt-20px">
            <div class="pr-10px w-100px" style="flex-shrink: 0">计划应用范围:</div>
            <div>{{ managementPlan && managementPlan.planDetail }}</div>
          </div>
        </div>
        <div class="px-20px pb-20px" font-semibold>回访计划任务节点</div>
        <div class="ml-20px pr-15px">
          <task-timeline @saveSuccess="onSaveSuccess" :taskList="managementPlan ? managementPlan.taskList : []" :noEdit="true"></task-timeline>
        </div>
      </div>
    </el-scrollbar>
    <template #footer>
      <slot name="footer">
        <div text-center>
          <el-button type="primary" @click="close()">关闭</el-button>
        </div>
      </slot>
    </template>
  </el-dialog>
</template>

<script setup>
import taskTimeline from "./task-timeline.vue";
const emit = defineEmits(["close"]);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  managementPlan: {
    type: Object,
    default: {},
  },
});
function close() {
  emit("close");
}
</script>

<style lang="scss"></style>