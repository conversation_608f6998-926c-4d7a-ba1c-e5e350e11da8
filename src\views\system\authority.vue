<template>
  <my-layout bg-fff common-shadow>
    <layout-item>
      <div class="query-bar">
        <el-button type="primary" plain @click="addRole">新增角色</el-button>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <el-table stripe height="100%" :data="list">
        <el-table-column property="roleName" label="角色" />
        <el-table-column property="member" label="权限">
          <template #default="{ row: { menuList } }">功能：{{ getMenuConut(menuList) }}</template>
        </el-table-column>
        <el-table-column fixed="right" class-name="el-table-fixed-column--shadow" label="操作" width="150">
          <template #default="scope">
            <el-button text="primary" class="table-action-btn" type="primary" size="small" @click="readRoleDetial(scope.row)">详情</el-button>
            <el-button text="primary" class="no-ml table-action-btn" type="primary" size="small" @click="editRole(scope.row)" v-if="scope.row.roleId !== 'admin' && scope.row.roleId !== 'member' && scope.row.roleId !== 'memberLeader'">编辑</el-button>

            <!-- <el-button text="primary" class="no-ml table-action-btn" type="danger" size="small"
              v-if="scope.row.roleId !== 'admin'" @click="removeRole(scope.row)">删除</el-button> -->
          </template>
        </el-table-column>
      </el-table>
    </layout-main>
  </my-layout>
  <authority-detial :can-rename="currentRole.roleId === 'admin'" ref="authorityDetialRef" @remove="removeRole" @on-add-role="onAddRole" />
</template>
<script setup>
import { ref } from "vue";
import { memberStore } from "@/store/member";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import authorityDetial from "./components/authorityDetial.vue";
import { ElMessageBox } from "element-plus";
import { deleteRole, getRolesList } from "@/api/corp";
const { memberInfo } = memberStore();
const list = ref([]);
const authorityDetialRef = ref();
const managerList = ref([]);
getList();

const currentRole = ref({});
function getMenuConut(arr = []) {
  let list = arr.filter((item) => {
    const regex = /^[1-9]0*$/;
    return !regex.test(item);
  });
  return list.length;
}
async function getList() {
  let { data, success } = await getRolesList();
  if (success) list.value = data.data;
}

function onAddRole() {
  getList();
}

function addRole() {
  authorityDetialRef.value.openDialog("add", []);
}
function editRole(e) {
  authorityDetialRef.value.openDialog("edit", e);
}
function readRoleDetial(e) {
  currentRole.value = e;
  authorityDetialRef.value.openDialog("read", e);
}

async function removeRole(item) {
  ElMessageBox.confirm("是否删除角色?", { type: "warning" })
    .then(async () => {
      await deleteRole(item._id);
      getList();
    })
    .catch(() => {
      // catch error
    });
}
</script>
<style scoped lang="scss">
:deep(.el-tree-virtual-list) {
  height: 400px;
}

.p-15 {
  padding: 15px;
}

.query-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
}

.table-action-btn {
  padding-left: 0;
  background: transparent !important;
}

.no-ml {
  margin-left: 0;
}
</style>
