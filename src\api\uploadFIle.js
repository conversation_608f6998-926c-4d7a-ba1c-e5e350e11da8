import { postFile } from "./axios";
import { getApiUrlByCorpId } from "../config/apiConfig";
export async function updateFile(name, file, corpId, sendType, getType) {
  const formData = new FormData();
  formData.append("file", file);
  formData.append("fileName", name);
  formData.append("corpId", localStorage.getItem("corpId")); // 添加 corpId 到表单数据
  formData.append("sendType", sendType);
  formData.append("getType", getType);
  const { filePath, success, message, media_id, url } = await postFile(formData);
  if (getType === "mediaId") {
    return { success, message, media_id };
  }
  const baseURL = getApiUrlByCorpId(corpId);
  const download_url = success ? `${baseURL}${filePath}` : "";
  return {
    download_url,
    url,
    success,
    message,
  };
}
