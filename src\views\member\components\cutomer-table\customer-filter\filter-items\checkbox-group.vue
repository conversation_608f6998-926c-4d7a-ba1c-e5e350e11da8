<template>
  <el-checkbox v-if="list.length" v-model="allChecked" label="全部" />
  <el-checkbox-group v-if="list.length" v-model="modelValue">
    <div v-for="item in list" :key="item.value">
      <el-checkbox :label="item.value" :value="item.value">{{ item.label }}</el-checkbox>
    </div>
  </el-checkbox-group>
</template>
<script setup>
import { computed } from 'vue';
import { useVModels } from '@vueuse/core';

const props = defineProps({
  list: { type: Array, default: () => [] },
  modelValue: { type: String, default: [] }
})
const emits = defineEmits(['update:modelValue'])
const { modelValue } = useVModels(props, emits);
const allChecked = computed({
  get() {
    return modelValue.value.length === props.list.length && props.list.length > 0
  },
  set(value) {
    if (value) {
      modelValue.value = props.list.map(item => item.value)
    } else {
      modelValue.value = []
    }
  }
})

</script>
<style lang="scss" scoped></style>
