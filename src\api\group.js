import { post } from "./axios";
async function useknow(data) {
  const res = await post("member", data);
  return res;
}

export async function createGroup(params, teamId) {
  const res = await useknow({
    type: "createGroup",
    corpId: localStorage.getItem("corpId"),
    teamId,
    params,
  });
  return res;
}
export async function getGroups({ page, pageSize, teamId, groupName, groupType }) {
  const res = await useknow({
    type: "getGroups",
    teamId,
    corpId: localStorage.getItem("corpId"),
    page,
    pageSize,
    groupName,
    groupType,
  });
  return res;
}

export async function orderTeamGroups(data) {
  const res = await useknow({
    type: "orderTeamGroups",
    ...data,
  });
  return res;
}

export async function getGroupsByCorpGroupId(data) {
  const res = await useknow({
    type: "getGroupsByCorpGroupId",
    ...data,
  });
  return res;
}

export async function removeGroup({ id, groupType, teamId }) {
  const res = await useknow({
    type: "removeGroup",
    corpId: localStorage.getItem("corpId"),
    id,
    teamId,
    groupType,
  });
  return res;
}
export async function updateGroup(id, params) {
  const res = await useknow({
    type: "updateGroup",
    id,
    params,
    corpId: localStorage.getItem("corpId"),
  });
  return res;
}

export async function addGroupIdForMember(memberId, toGroupId, fromGroupId) {
  const res = await useknow({
    type: "addGroupIdForMember",
    memberId,
    toGroupId,
    fromGroupId,
  });
  return res;
}
