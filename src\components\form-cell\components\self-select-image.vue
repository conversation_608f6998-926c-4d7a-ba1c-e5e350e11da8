<template>
  <div v-if="isRead">
    <img :src="value" alt="" class="h-40px w-auto" />
  </div>
  <div class="flex" v-else>
    <img :src="image" alt="" v-for="image in item.range" :class="image === value ? 'h-40px w-auto pointer mr-10px active' : 'h-40px w-auto pointer mr-10px'" @click="change(image)" />
  </div>
</template>
  <script setup>
const $emit = defineEmits(["change"]);
const props = defineProps({
  item: {
    type: Object,
    default: {},
  },
  value: {
    type: String,
    default: "",
  },
  isRead: {
    type: Boolean,
    default: false,
  },
});

function change(value) {
  $emit("change", {
    title: props.item.title,
    value,
  });
}
</script>
  
  <style>
.active {
  border: 1px solid #409eff;
}
</style>