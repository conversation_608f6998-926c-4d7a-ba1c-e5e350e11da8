<template>
  <div v-if="externalUserId && contactName[externalUserId]" class="inline-flex w-full items-center group"
    :class="isFriendMap[externalUserId] ? 'cursor-pointer text-blue-500' : 'text-gray-500'" @click.stop="chat(row)">
    <div class="truncate underline-offset-2 group-hover:underline">
      {{ contactName[externalUserId] || '-' }}
    </div>
    <div v-if="contactName[externalUserId]" class="flex-shrink-0 ml-5px text-12px text-[#66CB70]">@微信</div>
    <el-icon v-if="canUnbind && unbindLoadingMap[row._id]" class="text-gray-500 animate-spin transform translate-y-2px">
      <Loading />
    </el-icon>
    <div v-else-if="canUnbind"
      class="flex-shrink-0 ml-auto pl-10px whitespace-nowrap hidden cursor-pointer text-14px text-gray-400 group-hover:block"
      @click.stop="unbind(row)">解绑</div>
  </div>
  <div v-else class="inline-flex w-full items-center group text-14px text-gray-500">
    <template v-if="row._id && loadingMap[row._id]">
      <div class="truncate">绑定客户微信</div>
      <el-icon class="animate-spin transform translate-y-2px">
        <Loading />
      </el-icon>
    </template>
    <div v-else class="truncate">
      <span class="inline-block group-hover:hidden"> - </span>
      <span class="hidden cursor-pointer underline underline-offset-2 group-hover:inline-block"
        @click.stop="setWechat(row)">绑定客户微信</span>
    </div>
  </div>
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { ElMessage, ElMessageBox } from "element-plus";
import { updateMember, getUnionidByExternalUserId } from "@/api/member";
import wxContact from "@/store/wxContact";
import { openChatWindow } from "@/utils/common";
import { selectExternalContact } from "@/utils/jssdk";

const emits = defineEmits(["change"]);
const props = defineProps({
  canUnbind: { type: Boolean, default: false },
  externalUserId: { type: String, default: '' },
  customerId: { type: String, default: '' },
  row: { type: Object, default: () => ({}) }
})
const loadingMap = ref({});
const unbindLoadingMap = ref({});
const { getContacts } = wxContact();
const { contactFollowUsers, contactName } = storeToRefs(wxContact());
const isFriendMap = computed(() => {
  const userId = localStorage.getItem("userId");
  const m = {};
  Object.keys(contactFollowUsers.value).forEach(key => {
    const arr = Array.isArray(contactFollowUsers.value[key]) ? contactFollowUsers.value[key] : [];
    m[key] = arr.includes(userId);
  })
  return m
})

function chat() {
  if (props.customerId && props.externalUserId && isFriendMap.value[props.externalUserId]) {
    openChatWindow(props.customerId, props.externalUserId);
  }
}

function unbind() {
  if (!props.customerId) return;
  ElMessageBox.confirm("确定解绑该客户微信吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    if (unbindLoadingMap.value[props.customerId]) return;
    unbindLoadingMap.value[props.customerId] = true;
    let { success, message } = await updateMember(props.customerId, { externalUserId: "", unionid: "", unbindExternalUserId: props.externalUserId });
    if (success) {
      emits("change");
      ElMessage({
        message: "解绑成功",
        type: "success",
      });
    } else {
      ElMessage({
        message: message,
        type: "error",
      });
    }
    unbindLoadingMap.value[props.customerId] = false;
  });
}

async function setWechat() {
  if (loadingMap.value[props.customerId]) return;
  try {
    const [success, params] = await bindWechat(props.customerId);
    if (success) emits("change");
  } catch (e) { }
  loadingMap.value[props.customerId] = false;
}

async function bindWechat(_id) {
  let res = await selectExternalContact();
  const { err_msg, userIds } = res;
  if (err_msg === "selectExternalContact:cancel") return;
  if (err_msg !== "selectExternalContact:ok") {
    ElMessage({
      message: err_msg,
      type: "error",
    });
    return;
  }
  if (userIds.length > 1) {
    ElMessage({
      message: "只能选择一个用户!",
      type: "warning",
    });
    return;
  }
  let params = {
    externalUserId: userIds[0],
  };
  loadingMap.value[_id] = true;
  const unionId = await getUnionid(userIds[0]);
  if (unionId) params.unionid = unionId;
  let { success } = await updateMember(_id, params);
  getContacts([userIds[0]], true)
  loadingMap.value[_id] = false;
  if (success) {
    ElMessage({
      message: "更新成功",
      type: "success",
    });
    // 页面刷新
    return [success, params];
  }
  return [false]
}

async function getUnionid(externalUserId) {
  const res = await getUnionidByExternalUserId(externalUserId);
  if (res.data && res.data.data && res.data.data.unionid) {
    return res.data.data.unionid;
  } else {
    return "";
  }
}

watch(() => props.externalUserId, (val) => {
  if (val) getContacts([val])
}, { immediate: true })

</script>
