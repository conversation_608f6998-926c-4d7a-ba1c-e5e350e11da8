<template>
  <el-scrollbar class="h-full">
    <div class="flex justify-between items-center py-5px text-gray-500">
      <div class="text-15px text-gray-600">数据更新截止时间：{{ yesterday }}</div>
    </div>
    <div class="flex items-center mt-5px">
      <div v-for="(item, idx) in friendCount" class="rounded bg-white px-20px py-15px flex-grow" :class="idx > 0 ? 'ml-15px' : ''">
        <div class="flex items-center">
          <div class="text-18px font-semibold mr-5px">{{ item.title }}</div>
          <el-tooltip placement="right" effect="light" :content="item.tip">
            <el-icon class="text-16px text-gray-400 hover:text-blue-500">
              <QuestionFilled />
            </el-icon>
          </el-tooltip>
        </div>
        <div class="mt-10px">
          <count-to-bar class="text-32px font-semibold text-blue-700" :end-val="item.count" :duration="1000" />
        </div>
      </div>
    </div>
    <div v-loading="trendLoading" class="mt-10px bg-white rounded">
      <card-head title="数据趋势" :tabs="trendTabs" @change="onTrendChange" :staffList="staffList" />
      <div ref="trendChart" class="h-280px"></div>
    </div>
    <div v-loading="tableLoading" class="mt-10px bg-white rounded">
      <card-head title="数据明细" :tabs="detailTabs" @change="onDetailChange" :staffList="staffList"></card-head>
      <el-table stripe :data="employeeList" :height="300">
        <el-table-column label="员工姓名" type="userid" min-width="160">
          <template #default="{ row: { userId } }">
            <ww-user :openid="userId" />
          </template>
        </el-table-column>
        <el-table-column label="聊天好友数" min-width="160">
          <template #default="{ row: { chatCnt } }">
            {{ chatCnt || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="发送消息数" prop="sendMsgNum" min-width="160">
          <template #default="{ row: { messageCnt } }">
            {{ messageCnt || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="及时回复占比" prop="replyRate" min-width="160">
          <template #default="{ row: { replyPercentage } }">
            {{ replyPercentage.toFixed(2) || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="平均首次回复时长（分钟）" prop="replyDuration" min-width="160">
          <template #default="{ row: { avgReplyTime } }">
            {{ avgReplyTime.toFixed(0) || 0 }}
          </template>
        </el-table-column>
      </el-table>
      <div class="px-15px flex justify-end items-center text-14px text-gray-600">
        <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize" @handle-current-change="changePage" />
      </div>
    </div>
  </el-scrollbar>
</template>
<script setup>
import { computed, onMounted, ref } from "vue";
import { watchDebounced } from "@vueuse/core";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import { staffStore } from "@/store/staff";
import BigNumber from "bignumber.js";
import { getStaffQrcodeCateList, getQrcodeCustomerStatsByCateId } from "@/api/knowledgeBase";
import { getBehaviorStatistics } from "@/api/corp";
import { getCorpAllStaffUseBehaviorDataToday } from "@/api/wecom";
import { memberStore } from "@/store/member";
import useElPagination from "@/hooks/useElPagination";
import useLineChart from "@/hooks/useLineChart";
import useClassifyList from "@/components/classify-list/useClassifyList";
import pagination from "@/components/pagination/pagination.vue";
import cardHead from "./card-head.vue";
import countToBar from "../count-to-bar.vue";

const yesterday = dayjs().subtract(1, "day").endOf("day").format("YYYY-MM-DD HH:mm:ss");
const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());
const allContactCount = ref(0);
const allNegativeFeedbackCnt = ref(0);
const total = ref(0);
const yesterdayTotalStats = ref({});
const friendCount = computed(() => {
  return [
    { title: "昨日聊天好友数", count: yesterdayTotalStats.value?.chatCnt || 0, tip: "昨日本平台成员主动发送过消息的单聊患者数（含群发）" },
    { title: "昨日发送消息数", count: yesterdayTotalStats.value?.messageCnt || 0, tip: "昨日本平台成员在单聊中主动发送消息的总条数（含群发）" },
    { title: "昨日及时回复占比(%)", count: yesterdayTotalStats.value?.replyPercentage || 0, tip: "昨日，患者首先发消息的会话中（不含群聊），本平台成员回复了消息的单聊数/患者主动发起的单聊数*100%" },
    { title: "昨日平均首次回复时长(分钟)", count: yesterdayTotalStats.value?.avgReplyTime || 0, tip: "昨日，患者首先发消息（不含群聊）至本平台成员回复之间的时长，为首次回复时长。平均首次回复时长=所有首次回复总时长 / 已回复的单聊总数" },
  ];
});

const trendTabs = [
  { title: "聊天好友数", key: "chatCnt" },
  { title: "发送消息数", key: "messageCnt" },
  { title: "及时回复占比", key: "replyPercentage" },
  { title: "平均首次回复时长", key: "avgReplyTime" },
];
const trendChart = ref(null);
const currentTab = ref("staff");
const staffQuery = ref({});
const list = ref([]);
const trendData = ref([]);
const source = ref([]);
const { xAxisData, seriesData } = useLineChart(trendChart, { grid: { left: 30, right: 40 } });
const { page, pageSize, changePage, changeSize } = useElPagination();

const trendLoading = ref(false);
const tableLoading = ref(false);
const employeeList = computed(() => {
  const start = (page.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  list.value.sort((a, b) => b.newContactCount - a.newContactCount);
  let arr = list.value;
  if (staffQuery.value.userId) arr = list.value.filter((i) => i.userId == staffQuery.value.userId);
  return arr.slice(start, end);
});
async function onTrendChange({ dates, userId, tab }) {
  if (dates.length == 0) dates = [dayjs().startOf("isoWeek").format("YYYY-MM-DD"), dayjs().endOf("isoWeek").format("YYYY-MM-DD")];
  trendLoading.value = true;
  const x = getDates(dates);
  const s = await getSeriesData(dates, userId, tab);
  xAxisData.value = x;
  seriesData.value = s;
  trendLoading.value = false;
}
function onDetailChange({ tab, type, ...data }) {
  currentTab.value = tab;
  staffQuery.value = data;
  getweChatStatisticsByUser({ type: "userId", dates: staffQuery.value.dates });
}
async function getweChatStatisticsByUser({ type, dates, userId }) {
  tableLoading.value = true;
  let { data } = await getBehaviorStatistics({ statisticType: type, dates, userId });
  tableLoading.value = false;
  // dates 为空时, 获取全部数据
  if (type === "userId") {
    const totalStats = calculateTotals(data.totalStats);
    list.value = Object.entries(data.totalStats).map(([id, stats]) => ({
      userId: id,
      ...stats,
    }));
    total.value = list.value.length;
    allContactCount.value = totalStats.newContactCount;
    allNegativeFeedbackCnt.value = totalStats.negativeFeedbackCnt;
  } else {
    trendData.value = data.totalStats;
  }
  tableLoading.value = false;
}
async function getYesterdayweChatStatistics() {
  // 获取到昨日的日期
  const yesterdayTime = dayjs().subtract(1, "day").format("YYYY-MM-DD");
  const dates = [yesterdayTime, yesterdayTime];
  let { data, success } = await getBehaviorStatistics({ statisticType: "statTime", dates });
  if (success) yesterdayTotalStats.value = calculateTotals(data.totalStats);
}

function calculateTotals(data) {
  // 如果对象为空，直接返回初始值
  if (Object.keys(data).length === 0) {
    return {
      chatCnt: 0,
      messageCnt: 0,
      avgReplyTime: 0,
      replyPercentage: 0,
    };
  }
  const totals = {
    chatCnt: 0,
    messageCnt: 0,
    avgReplyTime: 0,
    replyPercentage: 0,
  };
  // 遍历每个数据项并累加
  Object.values(data).forEach((item) => {
    totals.chatCnt += item.chatCnt;
    totals.messageCnt += item.messageCnt;
    totals.avgReplyTime += item.avgReplyTime;
    totals.replyPercentage += item.replyPercentage;
  });
  return totals;
}
onMounted(async () => {
  if (!staffList.value.length) await getStaffList();
  await getYesterdayweChatStatistics();
});
watchDebounced(
  source,
  () => {
    getCodeList();
  },
  { debounce: 500 }
);
// mock......
function getDates(dates) {
  if (Array.isArray(dates) && dates.length === 2) {
    let [startDate, endDate] = dates;
    const res = [];
    while (dayjs(endDate).isSameOrAfter(dayjs(startDate)) && dayjs().isAfter(dayjs(startDate))) {
      res.push(dayjs(startDate).format("YYYY/MM/DD"));
      startDate = dayjs(startDate).add(1, "day").format("YYYY-MM-DD");
    }
    return res;
  }
  return [];
}
async function getSeriesData(dates, userId, tab) {
  if (dates.length === 0) return [];
  await getweChatStatisticsByUser({ type: "statTime", dates, userId });
  let list = fillMissingDatesAndConvertToArray(dates, trendData.value, tab);
  list = list.map((i) => {
    return i % 1 === 0 ? i : i.toFixed(1);
  });
  return list;
}

function fillMissingDatesAndConvertToArray(dateArray, obj, tab) {
  // 获取日期范围内的所有时间戳
  const dateRange = generateDateRange(new Date(dateArray[0]).getTime(), new Date(dateArray[1]).getTime());
  // 创建一个新的对象用于存放补全后的数据
  const result = [];
  // 遍历日期范围，检查每个时间戳是否在 obj 中
  dateRange.forEach((dateTimestamp) => {
    // 检查时间戳对应的日期是否存在
    const date = dayjs(dateTimestamp).format("YYYY-MM-DD");
    const newDateTimestamp = dayjs(date).unix();
    if (obj[newDateTimestamp]) {
      result.push(obj[newDateTimestamp][tab]);
    } else {
      result.push(0); // 补全的日期，num 为 0
    }
  });
  // 按时间戳顺序返回 num 值的数组
  return result;
}

function generateDateRange(startDate, endDate) {
  const dates = [];
  let currentDate = new Date(startDate);
  const end = new Date(endDate);

  while (currentDate <= end) {
    // 获取当前日期的时间戳，并存入数组
    dates.push(currentDate.getTime());
    currentDate.setDate(currentDate.getDate() + 1);
  }
  return dates;
}
</script>
<style lang="scss" scoped></style>