<template>
  <div class="pb-5px px-10px overflow-hidden">
    <el-row :gutter="5">
      <el-col v-for="(i, idx) in tools" :key="i.icon" :span="6">
        <div text-center pointer class="py-10px bg-white rounded-4px shadow-xl" :class="idx > 3 ? 'mt-6px' : ''"
          @click="show(i)">
          <svg-icon class="mx-auto mb-3px" :name="i.icon" :size="25" />
          <div font-12 class="mx-auto whitespace-nowrap"> {{ i.text }}</div>
        </div>
      </el-col>
    </el-row>
  </div>
  <div v-if="visible && currentCom" class="h-screen w-screen fixed inset-0 z-99">
    <component v-bind="attrs" :customer="customer" :is="currentCom" :unionId="unionId" :teams="[team]"
      @close="close()" @send="handleSend" />
  </div>
  <visit-record-confirm :visible="modalVisible" :files="dropFiles" :width="width"
    :name="customer && customer.name ? customer.name : ''" @close="cancel()" @confirm="confirm($event)" />
  <rate-modal :visible="rateVisible" :type="rateType" :width="width" @close="closeRateModal" @addService="addService" />
</template>
<script setup>
import { ref, toRefs, onMounted, onUnmounted } from 'vue';
import { storeToRefs } from 'pinia'
import { ElMessage } from 'element-plus';
import useModal from '@/hooks/useModal';
import { memberStore } from "@/store/member";
import { getCustomMemberInfo } from '@/api/corp.js'
import { addRateRecord } from '@/api/knowledgeBase.js'
import { getTodayServiceCustomerCount } from "@/api/todo";
import useChatBarSub from '@/views/chatBar/chat-bar/useChatBarSub';

import articleComponent from './article';
import newFollowUp from './new-follow-up'
import newService from './new-service.vue';
import newVisitRecord from './new-visit-record.vue';
import visitRecordConfirm from './visit-record-confirm.vue';
import surveryComponent from './survery';
import appointmentRecommend from './appointment-recommend';
import SvgIcon from '@/components/svg-icon';
import RateModal from './rate-modal.vue';

const props = defineProps({
  customer: { type: Object, default: () => ({}) },
  team: { type: Object, default: () => ({}) },
  unionId: { type: String, default: '' }
})
const emits = defineEmits(['send'])
const { memberInfo } = storeToRefs(memberStore())
const { customer, team } = toRefs(props);
const { sub, trigger } = useChatBarSub();
sub('editVisitRecord', editVisitRecord)

const attrs = ref({})
const tools = [
  { text: '发文章', icon: 'article', com: articleComponent },
  { text: '发问卷', icon: 'survery', com: surveryComponent },
  { text: '加待办', icon: 'todo', com: newFollowUp },
  { text: '加服务记录', icon: 'service', com: newService },
  { text: '新增健康档案', icon: 'archives' },
  { text: '服务评价', icon: 'rate-flower' },
  { text: '预约推荐', icon: 'appointment', com: appointmentRecommend }
]
const visible = ref(false);
const currentCom = ref(null)
function show(i) {
  attrs.value = {}
  if (i.text === '新增健康档案') {
    dropFiles.value = [];
    showModal()
  } else if (i.text === '服务评价') {
    getTodayRecord()
  } else {
    currentCom.value = i.com
    visible.value = true
  }
}

function close() {
  visible.value = false
  currentCom.value = null
}

function handleSend(data) {
  // 转发send事件到父组件
  emits('send', data)
  close()
}

/**
 * 取消新增档案
 */
function cancel() {
  dropFiles.value = [];
  closeModal();
}
/**
 * 确认新增档案
 */
function confirm(data) {
  closeModal();
  attrs.value = data;
  currentCom.value = newVisitRecord;
  visible.value = true
}
function editVisitRecord(record) {
  if (!record) return;
  const files = Array.isArray(record.files) ? record.files : [];
  const templateType = record.medicalType;
  confirm({ files, templateType, record })
}


/**
 * 监听文件拖入事件
 */
const { close: closeModal, show: showModal, visible: modalVisible, width } = useModal(); //  新增健康档案弹窗
const dropFiles = ref([]);
onMounted(() => trigger.$invoke('sub-sidebar-dropFiles', onDrop))
onUnmounted(() => trigger.$invoke('unsub-sidebar-dropFiles', onDrop))
async function onDrop(files) {
  if (files.length) {
    dropFiles.value = files.slice(0, 3);
    showModal();
  }
}

const { close: closeRateModal, show: showRateModal, visible: rateVisible } = useModal(); //  新增健康档案弹窗
const rateType = ref('')
async function getTodayRecord() {
  await verfiyTodayService()
  const info = await getMemberInfo()
  const params = {
    userId: memberInfo.value.userid,
    userName: memberInfo.value.anotherName,
    corpId: memberInfo.value.corpId,
    customerId: customer.value._id,
    customerName: customer.value.name,
    teamId: team.value.teamId,
    teamName: team.value.name,
    externalUserId: customer.value.externalUserId,
    ...info
  }
  const { data, message } = await addRateRecord(params)
  if (data && data.id) {
    if (data.rated) {
      rateType.value = 'rated';
      showRateModal();
    } else {
      sendRateMessage(data.id)
    }
  } else {
    ElMessage.error(message)
  }
}

async function getMemberInfo() {
  const { success, data } = await getCustomMemberInfo({
    corpId: memberInfo.value.corpId,
    userid: memberInfo.value.userid,
    id: memberInfo.value._id,
    fields: ['avatar', 'anotherName', 'job']
  })
  if (success && data.data) {
    return {
      anotherName: data.data.anotherName || '',
      avatar: data.data.avatar || '',
      job: Array.isArray(data.data.memberJob) && data.data.memberJob[0] && data.data.memberJob[0].name ? data.data.memberJob[0].name : ''
    }
  } else {
    ElMessage.error('获取成员信息失败')
    return Promise.reject()
  }

}

async function verfiyTodayService() {
  const { data, success, message } = await getTodayServiceCustomerCount({ customerId: customer.value._id, teamId: team.value.teamId });
  if (success && data.data === 0) {
    rateType.value = 'noService';
    showRateModal();
    // ElMessage.info('今天未服务，无需评价')
    return Promise.reject()
  } else if (success && data.data > 0) {
    return true
  } else if (success) {
    ElMessage.info('获取今日服务记录失败')
    return Promise.reject()
  } else {
    ElMessage.error(message)
    return Promise.reject()
  }

  // if (res.data > 0) {
  //   ElMessage.error('今日已服务，请勿重复评价')
  //   return Promise.reject()
  // } else {
  //   return Promise.resolve()
  // }
}


function addService() {
  const item = tools.find(i => i.text === '加服务记录');
  if (item) show(item)
}

function sendRateMessage(id) {
  const url = `${import.meta.env.VITE_PATIENT_PAGE_BASE_URL}pages/service-rate/rate?id=${id}&corpId=${memberInfo.value.corpId}`;
  emits('send', {
    title: '服务评价',
    desc: `尊敬的${customer.value.name}，请您对我的今日服务做出评价。`,
    url
  })
}

</script>
