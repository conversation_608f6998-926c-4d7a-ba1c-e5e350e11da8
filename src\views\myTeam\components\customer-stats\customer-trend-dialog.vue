<template>
  <el-dialog :model-value="visible" :width="800" @close="close">
    <template #header="{ titleClass }">
      <div :class="titleClass" flex items-center justify-between>
        <span active-title-bar>
          {{ title }} -
          <ww-user v-if="userId" :openid="userId" />
        </span>
        <div class="w-360px">
          <el-date-picker v-model="dates" type="daterange"  :unlink-panels="true"
 range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD" :disabled-date="disabledFn.disabledDate" @calendar-change="disabledFn.calendarChange" />
        </div>
      </div>
    </template>
    <div v-loading="loading" class="h-300px" ref="trendRef"></div>
  </el-dialog>
</template>
<script setup>
import { ref, toRefs, watch, inject , computed } from "vue";
import dayjs from "dayjs";
import { dates } from "./useDateTag";
import useLineChart from "@/hooks/useLineChart";
import WwUser from "@/components/ww-user/index.vue";
import { getMemberTrendByTeamId, getServiceTrendByTeamId } from "@/api/member.js";
const emits = defineEmits("close");
const props = defineProps({
  // dates: { type: Array, default: () => [] },
  disabledFn: {
    type: Object,
    default: () => ({ calendarChange: () => {}, disabledDate: () => {} }),
  },
  visible: { type: Boolean, default: false },
  userId: { type: String, default: "" },
  currentTeam: {
    type: Object,
    default: {},
  },
  cardType: {
    type: String,
    default: "",
  },
});
const { visible, disabledFn, userId } = toRefs(props);
const title = computed(() => {
  switch (props.cardType) {
    case "increaseCount":
      return "新增客户趋势";
    case "peopleCount":
      return "累计服务人次趋势";
    case "visitCount":
      return "累计报到人次趋势";
    default:
      return "新增客户趋势";
  }
});
watch(
  userId,
  () => {
    getList();
  },
  { immediate: false }
);

watch(dates, () => {
  if (visible.value && trendRef.value) {
    getList();
  }
});

let xAxisData = null;
let seriesData = null;
const loading = ref(false);
const trendRef = ref();
async function getData(xData, sData) {
  loading.value = true;
  setTimeout(function mock() {
    loading.value = false;
    xAxisData.value = xData;
    seriesData.value = sData;
  }, 1000);
}
watch(trendRef, (n) => {
  if (n && !xAxisData && !seriesData) {
    const res = useLineChart(trendRef, {
      grid: { right: 20 },
      autoSize: false,
    });
    xAxisData = res.xAxisData;
    seriesData = res.seriesData;
    getList();
  }
});
async function getList() {
  let { data, success } = await getMemberTrendByTeamId(dates.value, userId.value, props.cardType);
  if (success) {
    let xData = data.data.map((item) => {
      return item._id;
    });
    let sData = data.data.map((item) => {
      return item.count;
    });
    getData(xData, sData);
  }
}
</script>
