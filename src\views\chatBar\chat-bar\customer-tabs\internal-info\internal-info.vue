<template>
  <!-- <div v-loading="loading" class="bg-white shadow-xl">
    <display-form :tempList="tempList" :format="format" :form="customer" />
  </div> -->
  <el-lock-scrollbar v-loading="loading" :lock="lock" :max-height="maxHeight" class="relative bg-white shadow-xl"
    @scroll="handleScroll">
    <div v-if="tempList.length"
      class="absolute right-3.5 top-1/2 w-40px h-40px flex items-center justify-center text-blue-500 cursor-pointer bg-white rounded-full shadow-xl border-1 border-gray-100">
      <el-icon :size="20" @click="edit()">
        <EditPen />
      </el-icon>
    </div>
    <display-form :tempList="displayTempList" :format="format" :form="customer" />

    11
  </el-lock-scrollbar>
  <editInternalInfo :visible="visible" :customer="customer" @close="visible = false" />
</template>
<script setup>
import { computed, onMounted, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { templateStore } from "@/store/template";
import { memberStore } from "@/store/member";
import useLockScrollbar from '../useLockScrollbar.js'

import elLockScrollbar from '@/components/el-lock-scrollbar/el-lock-scrollbar';
import displayForm from '../../form-template/display/index.vue'
import editInternalInfo from './edit-internal-info.vue';

const props = defineProps({
  maxHeight: { type: Number, default: 300 },
  customer: { type: Object, default: () => ({}) },
})

const format = {
  customerSource: value => Array.isArray(value) ? value.join('/') : ''
}

const { reloadCorpInfo } = memberStore()
const store = templateStore();
const { corpTemplateList, loading } = storeToRefs(store);
const { getCorpTemplate } = store;
const tempList = computed(() => getTemplate('internalTemplate'));

const hiddenItems = ['tagIds', 'notes', 'phone1', 'phone2', 'phone3', 'mobile']
const displayTempList = computed(() => tempList.value.filter(i => !hiddenItems.includes(i.title)));

function getTemplate(templateType) {
  const temp = Array.isArray(corpTemplateList.value) ? corpTemplateList.value.find(i => i.templateType === templateType) : [];
  return temp && Array.isArray(temp.templateList) ? temp.templateList.filter(i => i && i.fieldStatus !== "disable") : [];
}

onMounted(() => {
  if (!Array.isArray(corpTemplateList.value) || corpTemplateList.value.length === 0) {
    getCorpTemplate();
  }
})

const { lock, handleScroll } = useLockScrollbar()

const visible = ref(false);

function edit() {
  reloadCorpInfo(['tags', 'customerSourceList'])
  visible.value = true
}

</script>
<style lang="scss" scoped></style>
