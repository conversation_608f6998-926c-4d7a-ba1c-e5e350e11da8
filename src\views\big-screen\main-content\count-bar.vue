<template>
  <div class="flex-shrink-0 flex">
      <div class="bg-white rounded w-1/4 mr-10px py-12px px-20px">
        <div class="h-36px flex items-center">
          <div class="flex-shrink-0 text-14px leading-18px font-semibold">应用开通<br /> 员工数：</div>
          <div class="w-0 flex-grow ml-10px text-right font-semibold text-28px" color-normal>
            <adjust-text :text="20" />
          </div>
        </div>
        <div class="mt-10px h-36px flex items-center">
          <div class="flex-shrink-0 text-14px leading-18px font-semibold">会话监管<br /> 员工数：</div>
          <div class="w-0 flex-grow ml-10px text-right font-semibold text-28px" color-normal>
            <adjust-text :text="20" />
          </div>
        </div>
      </div>
      <div class="bg-white rounded w-1/4 text-center mr-10px">
        <div class="h-1/2 flex w-full justify-center items-center text-24px font-semibold">
          <adjust-text :text="'2,3456'" />
        </div>
        <div class="h-1/2 flex w-full justify-center items-center">客户总数 </div>
      </div>
      <div class="bg-white rounded w-1/4 text-center mr-10px">
        <div class="h-1/2 flex w-full justify-center items-center text-24px font-semibold">
          <adjust-text :text="'2,3456'" />
        </div>
        <div class="h-1/2 flex w-full justify-center items-center">客户总数 </div>
      </div>
      <div class="bg-white rounded w-1/4 text-center">
        <div class="h-1/2 flex w-full justify-center items-center text-24px font-semibold">
          <adjust-text :text="'2,3456'" />
        </div>
        <div class="h-1/2 flex w-full justify-center items-center">客户总数 </div>
      </div>
    </div>
</template>
<script setup>
import adjustText from './adjust-text.vue'
</script>
