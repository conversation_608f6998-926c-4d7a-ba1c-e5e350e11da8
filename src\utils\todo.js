export function formatTodoFiles(todo) {
  const fileList = todo && Array.isArray(todo.fileList) ? todo.fileList : [];
  const files = fileList.map((item) => {
    if (item.type === 'image') {
      return {
        showName: `【图片】${item.file && item.file.name ? item.file.name : ''}`,
        _type: 'image',
        ...item
      }
    }
    if (item.type === 'link' && item.file && item.file.type === 'article') {
      return {
        showName: `【文章】${item.file && item.file.name ? item.file.name : ''}`,
        _type: 'article',
        ...item
      }
    }
    if (item.type === 'link' && item.file && item.file.type === 'questionnaire') {
      return {
        showName: `【问卷】${item.file && item.file.name ? item.file.name : ''}`,
        _type: 'questionnaire',
        
        ...item
      }
    }
    if (item.type === 'link') {
      return {
        showName: `【链接】${item.file && item.file.name ? item.file.name : ''}`,
        _type: 'link',
        ...item
      }
    }
  }).filter(<PERSON><PERSON><PERSON>)
  return { files }
}