<template>
  <my-layout>
    <layout-item>
      <div class="flex flex-wrap justify-between items-center px-15px pb-10px">
        <div v-if="hasAction" class="flex items-center pt-10px">
          <div v-if="canSelect" class="mr-15px">
            已选择：
            <span class="inline-block min-w-30px text-center text-red-500">
              {{ selectCustomers.length }}
            </span>
            位
          </div>
          <el-button v-if="canBatchEdit" plain type="primary" @click="batchAction(showEditModal)">批量编辑</el-button>
          <el-button v-if="canBatchMove" plain type="primary" @click="batchAction(showMoveModal)">转移</el-button>
          <el-button v-if="canBatchShare" plain type="primary" @click="batchAction(showShareModal)">共享</el-button>
          <el-button v-if="canBatchDelete" plain type="danger" @click="batchAction(showRemoveModal)">删除</el-button>
          <el-button v-if="canMoveGroup" plain type="primary" @click="batchAction(showGroupModal)">分配团队</el-button>
        </div>
        <div class="flex items-center pt-10px">
          <slot name="left"></slot>
          <el-button v-if="canAdd" type="primary" class="mr-15px" @click="addMember()">新增</el-button>
          <el-input v-model="keyword" :prefix-icon="Search" :placeholder="placeholder" class="w-280px" @keyup.enter="search()">
            <template #prepend>
              <el-select v-model="searchType" class="w-100px" placeholder="请选择">
                <el-option label="姓名" value="name"></el-option>
                <el-option label="手机号" value="mobile"></el-option>
              </el-select>
            </template>
          </el-input>
        </div>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <el-table border stripe :data="list" ref="tableRef" height="100%" @selection-change="handleSelectionChange">
        <el-table-column v-if="canSelect" align="center" type="selection" width="55" :selectable="selectable" />
        <component v-for="{ component, name, props } in columns" :is="component" v-bind="props" :key="name" @cellClick="handeCellClick" @to-customer-detail="toCustomerDetail" />
        <right-action-column v-if="$slots.fixedRightColumn && actionColumnWidth" :width="actionColumnWidth">
          <template #default="{ row }">
            <slot name="fixedRightColumn" :row="row"></slot>
          </template>
        </right-action-column>
        <!-- 自动填充列 防止固定列宽 不够表格宽度 自动拉伸 使用下面的列填充剩余宽度 -->
        <el-table-column v-if="showFillColumn" label=" " prop="fillColumn" />
      </el-table>
    </layout-main>
    <layout-item>
      <div class="px-15px py-10px flex justify-end border-t border-gray-200">
        <el-pagination small layout="total, prev, pager, next, sizes, jumper" :current-page="page" :page-size="pageSize" :page-sizes="[10, 20, 50]" :total="total" @current-change="changePage" @size-change="changeSize"></el-pagination>
      </div>
    </layout-item>
    <batch-edit-modal :customers="selectCustomers" :visible="editVisible" :width="editWidth" @close="closeEditModal" @change="handleEdit" />
    <batch-move-modal :customers="selectCustomers" :visible="moveVisible" :width="moveWidth" @close="closeMoveModal" @change="handleMove" />
    <batch-share-modal :customers="selectCustomers" :visible="shareVisible" :width="shareWidth" @close="closeShareModal" @change="handleShare" />
    <edit-notes-modal :customer="customer" :visible="visible" :width="width" @change="changeNotes($event)" @close="close" />
    <edit-customer-team :visible="editTeamVisible" :teamIds="customer.teamIds" @onSaveEditTeam="saveEditTeam" :memberId="customer._id" @onCloseEditTeam="closeEditTeam" />
    <remove-modal :customers="selectCustomers" :visible="removeVisible" :width="width" @close="closeRemoveModal" @change="handleRmoveCustomer" />
    <batch-move-group-modal :fixed-type="fixedGroupType" :customers="selectCustomers" :visible="groupVisible" :width="width" @close="closeGroupModal" @change="handleRemove" />
  </my-layout>

  <el-dialog v-model="addVisible" class="archive-modal" :width="560">
    <template #title>
      <div class="text-center text-16px font-semibold">请选择建档方式</div>
    </template>
    <div color-normal class="px-10px">
      <!-- <div class="text-16px font-semibold text-center py-20px">请选择建档方式</div> -->
      <div class="flex items-center border border-gray-200 rounded-4px px-15px py-12px cursor-pointer hover:border-blue-400" @click="addNewMember()">
        <div class="flex-grow">
          <div class="font-semibold text-15px pb-6px">新患者建档</div>
          <!-- <div class="text-14px text-zinc-500">未在院内系统建档过的客户</div> -->
        </div>
        <el-icon class="text-zinc-500" size="16">
          <ArrowRight />
        </el-icon>
      </div>
      <div v-if="corpInfo.isConnectHis" class="flex items-center border mt-15px mb-10px border-gray-200 rounded-4px px-15px py-12px cursor-pointer hover:border-blue-400" @click="addOldMember()">
        <div class="flex-grow">
          <div class="font-semibold text-15px pb-6px">老患者建档</div>
          <!-- <div class="text-14px text-zinc-500">已在院内系统建档过的客户</div> -->
        </div>
        <el-icon class="text-zinc-500" size="16">
          <ArrowRight />
        </el-icon>
      </div>
      <!-- <div class="flex items-center border mt-15px mb-10px border-gray-200 rounded-4px px-15px py-12px cursor-pointer hover:border-blue-400" @click="correlationMember">
        <div class="flex-grow">
          <div class="font-semibold text-15px pb-6px">老患者关联</div>

        </div>
        <el-icon class="text-zinc-500" size="16">
          <ArrowRight />
        </el-icon>
      </div> -->
    </div>
    <template #footer>
      <div class="flex items-center justify-center">
        <el-button class="w-100px" @click="addVisible = false">取消</el-button>
      </div>
    </template>
  </el-dialog>
  <bind-member :visible="addOldMemberVisible" @success="search" @addCustomer="addNewMember" @cancel="addOldMemberVisible = false" @acceptCustomer="acceptCustomer"></bind-member>
  <add-customer :visible="addCustomerVisible" @close="addCustomerVisible = false" @update="getList" title="新增患者" :viewType="customerType || 'customer'" :customer="newCustomerInfo" />
  <customer-detail v-if="customerDetailVisible" :visible="customerDetailVisible" @close="customerDetailVisible = false" :customerId="customerId" title="客户详情" @update="getList" :customerType="customerType" :corpType="corpType" />
  <correlation-customer :visible="correlationVisible" @close="correlationVisible = false" @success="search" @addCustomer="addNewMember" />
</template>
<script setup>
import { computed, ref, provide, nextTick } from "vue";
import { useVModels } from "@vueuse/core";
import { useRouter } from "vue-router";
import { storeToRefs } from "pinia";
import { ElMessage } from "element-plus";
import { searchCorpCustomer } from "@/api/member";
import useElPagination from "@/hooks/useElPagination";
import useModal from "@/hooks/useModal";
import { teamStore } from "@/store/team";
import wxContact from "@/store/wxContact";
import { Search } from "@element-plus/icons-vue";
import { configStore } from "@/store/config";
import addCustomer from "@/views/member/customer/add-customer.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import ageSexColumn from "./table-columns/age-sex-column.vue";
import arriveTimeColumn from "./table-columns/arrive-time-column.vue";
import concactUserColumn from "./table-columns/concact-user-column/concact-user-column.vue";
import createTimeColumn from "./table-columns/create-time-column.vue";
import customerStageColumn from "./table-columns/customer-stage-column.vue";
import customerSourceColumn from "./table-columns/customer-source-column.vue";
import personResponsibleColumn from "./table-columns/person-responsible-column.vue";
import groupColumn from "./table-columns/group-column.vue";
import planColumn from "./table-columns/plan-column.vue";
import mobileColumn from "./table-columns/mobile-column.vue";
import nameColumn from "./table-columns/name-column.vue";
import notesColumn from "./table-columns/notes-column.vue";
import serviceTimeColumn from "./table-columns/service-time-column.vue";
import tagsColumn from "./table-columns/tags-column/tags-column.vue";
import teamNameColumn from "./table-columns/team-name-column.vue";
import wxContactColumn from "./table-columns/wx-contact-column/wx-contact-column.vue";
import managePlanNameColumn from "./table-columns/manage-plan-column/manage-plan-name-column.vue";
import managePlanDateColumn from "./table-columns/manage-plan-column/manage-plan-date-column.vue";
import managePlanStateColumn from "./table-columns/manage-plan-column/manage-plan-state-column.vue";
import rightActionColumn from "./table-columns/right-action-column.vue";
import batchEditModal from "./modals/batch-edit-modal/batch-edit-modal.vue";
import batchMoveModal from "./modals/batch-move-modal/batch-move-modal.vue";
import batchMoveGroupModal from "./modals/batch-move-group-modal/batch-move-group-modal.vue";
import batchShareModal from "./modals/batch-share-modal/batch-share-modal.vue";
import editCustomerTeam from "./modals/edit-customer-team/edit-customer-team.vue";
import EditNotesModal from "./modals/edit-notes-modal/edit-notes-modal.vue";
import removeModal from "./modals/remove-modal/remove-modal.vue";
import bindMember from "../../bind-customer.vue";
import customerDetail from "@/views/member/detail/index.vue";
import correlationCustomer from "@/components/correlation-customer";
import { memberStore } from "@/store/member";

const { corpInfo, isHangzheng } = storeToRefs(memberStore());
const emits = defineEmits(["update:loading"]);
const props = defineProps({
  canAdd: { type: Boolean, default: false },
  canBatchDelete: { type: Boolean, default: false },
  canBatchEdit: { type: Boolean, default: false },
  canBatchMove: { type: Boolean, default: false },
  canBatchShare: { type: Boolean, default: false },
  canMoveGroup: { type: Boolean, default: false },
  canSelect: { type: Boolean, default: false },
  customerType: { type: String, default: "" },
  columns: { type: Array, default: () => [] },
  list: { type: Array, default: () => [] },
  listFormat: { type: [String, Function], default: "" },
  loading: { type: Boolean, default: false },
  params: { type: Object, default: () => ({}) },
  showFillColumn: { type: Boolean, default: false },
  actionColumnWidth: { type: Number, default: 200 },
  fixedGroupType: { type: String, default: "" },
  corpType: { type: String, default: "" },
});

const { loading } = useVModels(props, emits);
const columnsComponents = {
  ageSex: { component: ageSexColumn, name: "age-sex-column" },
  arriveTime: { component: arriveTimeColumn, name: "arrive-time-column" },
  createTime: { component: createTimeColumn, name: "create-time-column" },
  // customerStage: { component: customerStageColumn, name: "customer-stage-column" },
  customerSource: { component: customerSourceColumn, name: "customer-source-column" },
  group: { component: groupColumn, name: "group-column" },
  mobile: { component: mobileColumn, name: "mobile-column" },
  name: { component: nameColumn, name: "name-column" },
  notes: { component: notesColumn, name: "notes-column" },
  personResponsible: { component: personResponsibleColumn, name: "person-responsible-column" },
  plan: { component: planColumn, name: "plan-column" },
  serviceTime: { component: serviceTimeColumn, name: "service-time-column" },
  tagIds: { component: tagsColumn, name: "tags-column" },
  serviceTeam: { component: teamNameColumn, name: "team-name-column" },
  wxContact: { component: wxContactColumn, name: "wx-contact-column" },
  concactUser: { component: concactUserColumn, name: "concact-user-column" },
  managePlanNameColumn: { component: managePlanNameColumn, name: "manage-plan-name-column" },
  managePlanDateColumn: { component: managePlanDateColumn, name: "manage-plan-date-column" },
  managePlanStateColumn: { component: managePlanStateColumn, name: "manage-plan-state-column" },
};
const columns = computed(() =>
  props.columns
    .map((key) => columnsComponents[key])
    .filter(Boolean)
    .map((i) => {
      if (i.name === "name-column") return { ...i, props: { customerType: props.customerType } };
      else if (i.name === "wx-contact-column") return { ...i, props: { canUnbind: "corpCustomer" === props.customerType } }; // 机构客户支持解绑微信号
      return { ...i, props: {} };
    })
);

// 引用Store和路由
const { allTeams, currentTeam } = storeToRefs(teamStore());
const contactStore = wxContact();
const { getContacts } = contactStore;
const { getGroupTag } = configStore();
// 分页和搜索相关状态
const { page, pageSize, changePage, changeSize } = useElPagination(getList);
const list = ref([]);
const total = ref(0);
const tableRef = ref();
const selectCustomers = ref([]);
const searchType = ref("name");
const keyword = ref("");
const customer = ref({});
const searchParams = ref({});
const addVisible = ref(false);
const addOldMemberVisible = ref(false);
const addCustomerVisible = ref(false);
const newCustomerInfo = ref({});
// 模态框状态
const { close, show, visible, width } = useModal(); //  编辑客户备注弹窗
const { close: closeEditModal, show: showEditModal, visible: editVisible, width: editWidth } = useModal(); //  批量编辑客户弹窗
const { close: closeMoveModal, show: showMoveModal, visible: moveVisible, width: moveWidth } = useModal(); //  批量转移客户弹窗
const { close: closeShareModal, show: showShareModal, visible: shareVisible, width: shareWidth } = useModal(); //  批量转移客户弹窗
const { close: closeEditTeam, show: showEditTeam, visible: editTeamVisible } = useModal(); //  编辑客户团队弹窗
const { close: closeRemoveModal, show: showRemoveModal, visible: removeVisible } = useModal(); //  批量删除客户弹窗
const { close: closeGroupModal, show: showGroupModal, visible: groupVisible } = useModal(); //  编辑客户备注弹窗

// 计算属性
const placeholder = computed(() => {
  if (searchType.value === "name") return "请输入姓名";
  if (searchType.value === "mobile") return "请输入手机号";
  return "请选择";
});
const hasAction = computed(() => {
  return props.canSelect || props.canBatchEdit || props.canBatchMove || props.canBatchShare || props.canMoveGroup;
});

// 批量操作方法
function batchAction(fn) {
  if (selectCustomers.value.length > 0) {
    typeof fn === "function" && fn(selectCustomers.value);
  } else {
    ElMessage.warning("请选择");
  }
}

// 搜索方法
function search(reset = false) {
  searchParams.value = { ...props.params };
  if (reset) {
    searchType.value = "name";
    keyword.value = "";
  }
  if (keyword.value.trim() !== "" && searchType.value === "name") {
    searchParams.value.name = keyword.value.trim();
  } else if (keyword.value.trim() !== "" && searchType.value === "mobile") {
    searchParams.value.mobile = keyword.value.trim();
  }
  changePage(1);
}

function handleEdit() {
  closeEditModal();
  changePage(1);
}
function handleMove() {
  closeMoveModal();
  changePage(1);
}
function handleShare() {
  closeShareModal();
  changePage(1);
}

function handleRemove() {
  closeGroupModal();
  changePage(1);
}
function handleRmoveCustomer() {
  closeRemoveModal();
  changePage(1);
}
// 跳转新增页面
function addMember() {
  addVisible.value = true;

  // if (corpInfo.isConnectHis) {
  //   addVisible.value = true;
  //   return;
  // }
  // addNewMember();
}

const correlationVisible = ref(false);

function correlationMember() {
  addVisible.value = false;
  correlationVisible.value = true;
}

function addNewMember() {
  addVisible.value = false;
  addOldMemberVisible.value = false;
  correlationVisible.value = false;
  addCustomerVisible.value = true;
  newCustomerInfo.value = { cardType: "身份证", relationship: "本人" };
}

function acceptCustomer(data) {
  addOldMemberVisible.value = false;
  addCustomerVisible.value = true;
  newCustomerInfo.value = data;
}

const customerId = ref("");
const customerDetailVisible = ref(false);
async function toCustomerDetail(id) {
  customerId.value = id;
  await nextTick();
  customerDetailVisible.value = true;
}

function addOldMember() {
  addVisible.value = false;
  addOldMemberVisible.value = true;
}

// 处理表格选择变化
function handleSelectionChange(e) {
  selectCustomers.value = e.map((i) => ({ ...i }));
}

// 清空选择的客户
function clearSelectCustomers() {
  selectCustomers.value = [];
  tableRef.value && tableRef.value.clearSelection();
}
// 编辑备注
function editNotes(item) {
  customer.value = item;
  show();
}
// 修改备注
function changeNotes(notes) {
  const index = list.value.findIndex((i) => i._id === customer.value._id);
  if (index > -1) list.value[index].notes = notes;
}

// 获取用户信息
async function getList() {
  if (loading.value) return;
  loading.value = true;
  const params = {
    ...searchParams.value,
    corpId: localStorage.getItem("corpId"),
    userId: localStorage.getItem("userId"),
    page: page.value,
    pageSize: pageSize.value,
  };
  const { data } = await searchCorpCustomer(params);
  loading.value = false;
  clearSelectCustomers();
  await getGroupTag();
  const arr = Array.isArray(data.list) ? data.list.map((i) => formatCustomerData(i)) : [];
  if (typeof props.listFormat === "function") {
    list.value = props.listFormat(arr);
  } else {
    list.value = arr;
  }
  total.value = data.total;
  getContacts(list.value.map((i) => i.externalUserId));
}
// 格式化客户数据
function formatCustomerData(customer) {
  const tagIds = Array.isArray(customer.tagIds) ? customer.tagIds : [];
  return {
    ...customer,
    ...getTeam(customer.teamId),
    tagIds,
    personResponsible: Array.isArray(customer.personResponsibles) && customer.personResponsibles.length && customer.personResponsibles.find((i) => i.teamId === currentTeam.value.teamId) ? customer.personResponsibles.find((i) => i.teamId === currentTeam.value.teamId).corpUserId : "",
    corpUserId: Array.isArray(customer.corpUserId) ? customer.corpUserId : [],
  };
}
function handeCellClick({ type, data }) {
  if (type === "editNotes") editNotes(data);
  else if (type === "editTeams") editTeams(data);
  else if (type === "loading") loading.value = data;
  else if (type === "reload") getList();
}
function getTeam(teamId) {
  const allTeam = [...allTeams.value].filter(Boolean);
  const teamIds = [];
  if (typeof teamId === "string" && teamId) teamIds.push(teamId);
  else if (Array.isArray(teamId)) teamIds.push(...teamId);
  const teamNames = teamIds
    .map((i) => allTeam.find((item) => item.teamId === i))
    .filter(Boolean)
    .map((item) => item.name)
    .join("、");
  return { teamIds, teamNames };
}

function editTeams(data) {
  customer.value = data;
  showEditTeam();
}
function saveEditTeam() {
  closeEditTeam();
  getList();
}

function changeCustomData({ key, value, _id }) {
  const index = list.value.findIndex((i) => i._id === _id);
  if (index >= 0) {
    list.value[index][key] = value;
  }
}
provide("change-customer-table-row-data", changeCustomData);

defineExpose({
  search,
});
</script>
<style>
.archive-modal .el-dialog__header {
  border-bottom: 0;
}
</style>