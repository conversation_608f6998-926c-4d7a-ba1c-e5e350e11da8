<template>
  <el-dialog :model-value="props.visible" :width="width" title="报备详情" @close="close()">
    <el-scrollbar wrap-class="el-dialog-body-full-scroll">
      <el-form class="p-15px" label-suffiix="：" label-position="top" label-suffix="：">
        <el-form-item class="is-required" label="客户电话">
          <el-input v-model="form.mobile" placeholder="请输入客户电话" />
        </el-form-item>
        <el-form-item class="is-required" label="客户姓名">
          <el-input v-model="form.name" placeholder="请输入客户姓名" />
        </el-form-item>
        <el-form-item label="性别">
          <el-radio-group v-model="form.sex">
            <el-radio label="男">男</el-radio>
            <el-radio label="女">女</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item class="is-required" label="客户来源">
        </el-form-item>
        <el-form-item label="意向项目">
        </el-form-item>
        <el-form-item label="报备说明">
          <el-input type="textarea" v-model="form.desc" placeholder="请输入项目描述" maxlength="500"
            :autosize="{minRows: 6, maxRows: 10}" resize="none" show-word-limit></el-input>
        </el-form-item>
        <el-form-item label="报备人">
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button plain class="w-100px" type="primary" @click="close()">取消</el-button>
        <el-button class="w-100px" :loading="loading" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ElMessage } from "element-plus";
import { computed, ref, watch } from "vue";
const emits = defineEmits(['close', 'change']);
const props = defineProps({
  id: { type: String, default: '' },
  visible: { type: Boolean, default: false },
  cateTree: { type: Array, default: () => [] },
  updateMethod: { type: Function, default: () => { } },
  width: { type: String, default: '500px' },
})
const form = ref({})
const cateId = ref('');
const cateIdStr = computed(() => {
  if (typeof cateId.value === 'string') return cateId.value;
  return Array.isArray(cateId.value) ? cateId.value[cateId.value.length - 1] || '' : '';
})

function close() {
  emits('close')
}

const loading = ref(false);
async function confirm() {
  if (props.id === cateIdStr.value) {
    close()
    return
  }
  loading.value = true;
  const { success, message } = await props.updateMethod(cateIdStr.value);
  if (success) {
    ElMessage.success(message);
    close();
  } else {
    ElMessage.error(message);
  }
  loading.value = false
}

watch(() => props.visible, (n) => {
  if (n) {
    cateId.value = props.id;
  }
})

</script>