import { computed, ref, watch } from "vue";
import { getCustomers } from "@/api/member";
import { getHisCustomerArchive } from "@/api/customerHisSync";
import { memberStore } from "@/store/member";
let { corpInfo } = memberStore();
export default function repeatComponentApi() {
  const customer = ref({});
  const repeatVisible = ref(false);
  const idCardIsRepeat = ref(false);
  const repeatType = ref("");

  async function getReatCustomer(value, title) {
    idCardIsRepeat.value = false;
    const [item, item1] = await Promise.all([getCustomer({ value, title }), getHisCustomer({ value, title })]);
    customer.value = { ...item, ...item1 };
    repeatType.value = "idCard";
    if (customer.value["idCard"]) repeatVisible.value = true;
  }

  async function getHisCustomer({ value, title }) {
    if (!corpInfo.isConnectHis) return {};
    const query = title === "idCard" ? { idCard: value } : { idNo: value };
    const { success, data } = await getHisCustomerArchive(query);
    if (success && data.list.length > 0) {
      const item = data.list[0];
      if (item) item.isConnectHis = true;
      return item;
    }
    return {};
  }

  async function getCustomer({ value, title }) {
    const query = { [title]: value };
    // 判断 query 是否为空
    if (!query[title]) return {};
    const { data, success } = await getCustomers(query);
    if (success && data.data.length > 0) {
      idCardIsRepeat.value = true;
      return data.data[0];
    }
    return {};
  }

  return { customer, repeatVisible, idCardIsRepeat, getReatCustomer, getCustomer };
}
