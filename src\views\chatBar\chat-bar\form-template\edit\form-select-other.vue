<template>
  <el-form-item class="px-15px py-6px el-form-item--autoheight" :class="required ? 'is-required' : ''" :label="name" border-bottom>
    <div class="flex align-center w-full">
      <el-select ref="selectRef" class="w-full" clearable :multiple="mult" :model-value="selectValue" @change="change($event)">
        <el-option v-for="opt in options" :key="opt.value" :value="opt.value">
          {{ opt.label }}
        </el-option>
      </el-select>
      <el-input class="ml-10px" placeholder="请输入" v-if="showOther" v-model="otherValue" @update:model-value="changeOther($event)"></el-input>
    </div>
  </el-form-item>
</template>
  <script setup>
import { computed, nextTick, ref, watch } from "vue";
const emits = defineEmits(["change"]);
const props = defineProps({
  form: { type: Object, default: () => ({}) },
  mult: { type: Boolean, default: false },
  name: { type: String, default: "" },
  otherFiled: { type: String, default: "" },
  range: { type: Array, default: () => [] },
  required: { type: Boolean, default: false },
  title: { type: String, default: "" },
  rangeKey: { type: String, default: "" },
});
let showOther = ref(false);
let selectValue = ref("");
let otherValue = ref("");
const value = computed(() => props.form[props.title] || "");
const selectRef = ref();

const options = computed(() => {
  if (props.rangeKey) return props.range.map((opt) => ({ label: opt.label, value: opt[props.rangeKey] }));
  return props.range.map((opt) => ({ label: opt, value: opt }));
});

watch(
  () => value,
  (val) => {
    getSelectValue();
  },
  { immediate: true, deep: true }
);
function getSelectValue() {
  if (props.range.includes(value.value) || !value.value) {
    selectValue.value = value.value;
    showOther.value = false;
    otherValue.value = "";
    return;
  }
  selectValue.value = props.otherFiled;
  showOther.value = true;
  otherValue.value = value.value;
}

async function change(value) {
  if (value === props.otherFiled) {
    showOther.value = true;
    selectValue.value = value;
  } else {
    showOther.value = false;
    emits("change", { title: props.title, value });
  }
  await nextTick();
  const clearIcon = selectRef.value.$el.querySelector(".el-input__clear");
  if (clearIcon) {
    clearIcon.style.display = "block";
  }
}
async function changeOther(value) {
  emits("change", { title: props.title, value });
}
</script>
  <style lang="scss" scoped></style>
  