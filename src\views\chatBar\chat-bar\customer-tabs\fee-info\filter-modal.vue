<template>
  <el-dialog :model-value="visible" :width="width" title="更多筛选" @close="close">
    <el-form label-position="top" class="px-5px">
      <el-form-item label="支付状态">
        <div class="flex-grow flex">
          <div v-for="i in statusList" :key="i"
            class="mr-10px px-10px py-4px border border-gray-200 text-sm rounded cursor-pointer"
            :class="currentStatus === i ?'text-blue-500 border-blue-500':''" @click="changStatus(i)">
            {{i}}
          </div>
        </div>
      </el-form-item>
      <el-form-item label="开单时间">
        <div class="flex-grow flex justify-between items-center">
          <el-date-picker v-model="start" format="YYYY-MM-DD" placeholder="开始时间" type="date" value-format="YYYY-MM-DD"
            class="flex-shrink-0" style="width:45%" @change="changeDate()" />
          <span>至</span>
          <el-date-picker v-model="end" format="YYYY-MM-DD" placeholder="结束时间" type="date" value-format="YYYY-MM-DD"
            class="flex-shrink-0" style="width:45%" @change="changeDate()" />
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="text-center">
        <el-button class="w-100px" :loading="loading" plain @click="close()">取消</el-button>
        <el-button class="w-100px" :loading="loading" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref, watch } from 'vue';
import dayjs from 'dayjs';
import { ElMessage } from 'element-plus';

const emits = defineEmits(['change', 'close'])
const props = defineProps({
  width: { type: Number },
  status: { type: String, default: '' },
  start: { type: String, default: '' },
  end: { type: String, default: '' },
  visible: { type: Boolean, default: false },
})
const statusList = ['已结算', '未结算', '已退费'];
const currentStatus = ref('');
function changStatus(i) {
  currentStatus.value = i === currentStatus.value ? '' : i
}
const start = ref('');
const end = ref('');
function changeDate() {
  if (start.value && end.value) {
    if (dayjs(start.value).isAfter(dayjs(end.value))) {
      [start.value, end.value] = [end.value, start.value]
    }
  }
}

function confirm() {
  if (start.value && !end.value) {
    ElMessage.info('请选择结束时间')
  } else if (!start.value && end.value) {
    ElMessage.info('请选择开始时间')
  } else if (start.value && end.value && dayjs(start.value).isAfter(dayjs(end.value))) {
    ElMessage.info('开始时间不能大于结束时间')
  } else {
    emits('change', { status: currentStatus.value, start: start.value, end: end.value })
    close()
  }
}

function close() {
  emits('close')
}

watch(() => props.visible, val => {
  if (val) {
    currentStatus.value = props.status || '';
    start.value = props.start && dayjs(props.start).isValid() ? dayjs(props.start).format('YYYY-MM-DD') : '';
    end.value = props.end && dayjs(props.end).isValid() ? dayjs(props.end).format('YYYY-MM-DD') : '';
    changeDate()
  }
})

</script>
