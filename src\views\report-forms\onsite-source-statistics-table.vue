<template>
  <div class="report-container">
    <div class="report-header">
      <div class="report-actions">
        <div class="filter-section">
          <div class="filter-row">
            <div class="filter-item">
              <span class="filter-label">到院日期：</span>
              <el-date-picker v-model="visitDateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :shortcuts="dateShortcuts" :disabled-date="disabledDate" @change="handleDateChange" class="date-range" value-format="YYYY-MM-DD" />
            </div>
            <filter-info-source v-model="infoSource" />
            <div class="filter-item">
              <check-box-filter v-model="consultantFilter" label="咨询师" :list="consultantList">
                <template #item="{ item }">
                  <ww-user :openid="item.value"></ww-user>
                </template>
              </check-box-filter>
            </div>
            <div class="filter-item">
              <check-box-filter v-model="developerUserIds" label="开发人员" :list="developerList">
                <template #item="{ item }">
                  <ww-user :openid="item.value"></ww-user>
                </template>
              </check-box-filter>
            </div>
            <!-- <div class="filter-item">
              <filter-customer-source v-model="customerSource" label="开发渠道" />
            </div> -->
            <!-- <div class="filter-item">
              <span class="filter-label">咨询状态：</span>
              <el-select v-model="consultStatus" multiple placeholder="请选择咨询状态" class="consult-status-select">
                <el-option label="初诊" value="first"></el-option>
                <el-option label="复诊" value="revisit"></el-option>
                <el-option label="再消费" value="repurchase"></el-option>
              </el-select>
            </div> -->
            <check-box-filter v-model="consultStages" label="接诊状态" :list="ConsultStage" />
            <radio-filter v-model="tradeStatus" label="成交状态" :list="TradeStatus"></radio-filter>
            <div class="filter-item">
              <span class="filter-label">咨询科室：</span>
              <dept-select v-model="deptId" placeholder="请选择科室" @change="handleDeptChange" class="dept-select" />
            </div>
            <div class="filter-item">
              <span class="filter-label">咨询项目：</span>
              <project-intent-select v-model="projectIntentIds" :deptId="deptId" placeholder="请选择或搜索咨询项目" @change="handleProjectChange" />
            </div>
            <div class="filter-actions">
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button type="success" @click="exportToExcel">导出Excel</el-button>
              <el-button @click="resetFilters">重置</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="table-container">
      <table class="performance-table">
        <colgroup>
          <col style="width: 12%" />
          <!-- 来源 -->
          <col style="width: 10%" />
          <!-- 来院人次 -->
          <col style="width: 8%" />
          <!-- 来院占比 -->
          <col style="width: 10%" />
          <!-- 成交人次 -->
          <col style="width: 10%" />
          <!-- 成交占比 -->
          <col style="width: 10%" />
          <!-- 未成交人次 -->
          <col style="width: 10%" />
          <!-- 未成交占比 -->
          <col style="width: 14%" />
          <!-- 消费总额 -->
        </colgroup>
        <thead>
          <tr>
            <th>信息来源</th>
            <th>来院人次</th>
            <th>来院占比</th>
            <th>成交人次</th>
            <th>成交占比</th>
            <th>未成交人次</th>
            <th>未成交占比</th>
            <th>消费总额</th>
          </tr>
        </thead>
        <tbody>
          <template v-for="(sourceGroup, groupIndex) in sourceGroupedData" :key="groupIndex">
            <!-- 一级来源行 -->
            <tr v-for="(item, sIndex) in sourceGroup.sources" :key="`${item.sourceKey}-${sIndex}`">
              <td>{{ item.sourceName || "未知来源" }}</td>
              <td>{{ item.visitCount }}</td>
              <td>{{ formatPercentage(item.visitCount / totalVisitCount) }}</td>
              <td>{{ item.dealCount }}</td>
              <td>{{ formatPercentage(item.dealCount / totalDealCount) }}</td>
              <td>{{ item.visitCount - item.dealCount }}</td>
              <td>{{ formatPercentage((item.visitCount - item.dealCount) / (totalVisitCount - totalDealCount)) }}</td>
              <td>{{ formatCurrency(item.totalAmount) }}</td>
            </tr>
          </template>
          <tr class="total-row">
            <td class="total-label">总计：</td>
            <td>{{ totalVisitCount }}</td>
            <td></td>
            <td>{{ totalDealCount }}</td>
            <td></td>
            <td>{{ totalVisitCount - totalDealCount }}</td>
            <td></td>
            <td>{{ formatCurrency(totalAmount) }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="footer-info">
      <span class="date-limit-info">* 查询限制为一个月内的数据</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { ElButton, ElMessage, ElDatePicker, ElSelect, ElOption } from "element-plus";
import { CheckBoxFilter, filterInfoSource, RadioFilter } from "@/components/filter-bar";
import { storeToRefs } from "pinia";
import { staffStore } from "@/store/staff";
import { memberStore } from "@/store/member";
import { teamStore } from "@/store/team";
import ProjectIntentSelect from "@/components/benefit-management/project-intent-select.vue";
import DeptSelect from "@/components/dept-components/dept-select.vue";
import { useDeptStore } from "@/components/dept-components/dept-store";
import { getConsultantSourceStatistics } from "@/api/consult"; // 导入 getConsultRecord
import { ConsultStage, TradeStatus } from "@/baseData";
// 用户筛选
const visitDateRange = ref([]);
const infoSource = ref([]);
const consultantFilter = ref([]);
const developerUserIds = ref([]);
const customerSource = ref([]);
const consultStatus = ref([]);
const projectIntentIds = ref([]);
const deptId = ref("");
const selectedProjects = ref([]);
const selectedDept = ref({});
const consultStages = ref([]);
const tradeStatus = ref("");
// 初始化部门商店
const deptStore = useDeptStore();

// 从store获取员工列表
const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());
const { isAdmin } = storeToRefs(memberStore());
const { managerList } = storeToRefs(teamStore());
const { judgmentIsAdmin } = memberStore();

// 添加开发人员列表计算属性，只显示开发人员角色
const developerList = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("onlineCustomerService")).map((i) => ({ value: i.userid }));
});

// 添加咨询师列表计算属性，只显示咨询师角色
const consultantList = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("consultant")).map((i) => ({ value: i.userid }));
});

// 项目和科室选择变更处理
const handleProjectChange = (projects) => {
  selectedProjects.value = projects;
};

// 添加科室变化处理函数
function handleDeptChange(dept) {
  selectedDept.value = dept;
  projectIntentIds.value = []; 
}

// 日期快捷选项
const dateShortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近半个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
];

// 禁用超过一个月范围的日期
const disabledDate = (time) => {
  if (!visitDateRange.value || visitDateRange.value.length === 0) {
    return false;
  }

  const startDate = new Date(visitDateRange.value[0]);
  const endDate = new Date(visitDateRange.value[1]);
  const currentDate = new Date(time);

  // 如果已选择了开始日期，限制结束日期不能超过开始日期后的31天
  if (visitDateRange.value.length === 1) {
    const oneMonthLater = new Date(startDate);
    oneMonthLater.setDate(startDate.getDate() + 31);
    return currentDate > oneMonthLater;
  }

  return false;
};

// 监听日期变化，确保不超过一个月
watch(visitDateRange, (newValue) => {
  if (newValue && newValue.length === 2) {
    const start = new Date(newValue[0]);
    const end = new Date(newValue[1]);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays > 31) {
      // 如果超过31天，将结束日期设置为开始日期后的31天
      const maxEnd = new Date(start);
      maxEnd.setDate(start.getDate() + 31);
      const formattedMaxEnd = formatDate(maxEnd);

      visitDateRange.value = [newValue[0], formattedMaxEnd];
      ElMessage.warning("查询范围已限制为一个月内");
    }
  }
});

// 格式化日期为YYYY-MM-DD
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  return `${year}-${month}-${day}`;
};

// 格式化货币
const formatCurrency = (value) => {
  if (value === undefined || value === null || isNaN(value)) {
    return "¥0.00";
  }
  return new Intl.NumberFormat("zh-CN", { style: "currency", currency: "CNY" }).format(value);
};

// 原始数据和筛选后的表格数据
const originalData = ref([]);
const tableData = ref([]);

// 修改计算总计数据的逻辑，使用接口返回的总计数据
const totalVisitCount = ref(0);
const totalDealCount = ref(0);
const totalAmount = ref(0);

async function fetchOnsiteStatistics() {
  try {
    // 构建查询参数 - 包含所有筛选条件
    const today = new Date();
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(today.getMonth() - 1);

    const params = {
      startDate: visitDateRange.value?.[0] || formatDate(oneMonthAgo),
      endDate: visitDateRange.value?.[1] || formatDate(today),
    };

    if (consultantFilter.value && consultantFilter.value.length > 0) {
      params.consultantFilter = consultantFilter.value;
    }
    if (developerUserIds.value && developerUserIds.value.length > 0) {
      params.developerUserIds = developerUserIds.value;
    }
    // 开发渠道筛选
    if (customerSource.value && customerSource.value.length > 0) {
      params.customerSource = customerSource.value;
    }

    // 咨询状态筛选
    if (consultStatus.value && consultStatus.value.length > 0) {
      params.consultStatus = consultStatus.value;
    }

    // 信息来源筛选
    if (infoSource.value && infoSource.value.length > 0) {
      params.infoSource = infoSource.value;
    }

    // 科室筛选
    if (deptId.value) {
      params.deptId = deptId.value;
      params.projectIds = (await deptStore.fetchProjectIdsByDeptId(deptId.value)) || [];
    }

    // 项目筛选
    if (projectIntentIds.value && projectIntentIds.value.length > 0) {
      params.projectIds = projectIntentIds.value;
    }

    if (consultStages.value && consultStages.value.length > 0) {
      params.consultStages = consultStages.value;
    }

    if (tradeStatus.value) {
      params.tradeStatus = tradeStatus.value;
    }

    // 调用API获取数据 - 使用 getConsultRecord
    const { data, success, message } = await getConsultantSourceStatistics(params);

    if (success) {
      // 保存原始数据
      originalData.value = data.data || [];

      // 直接使用接口返回的来源数据
      if (data.data && data.data.sources && Array.isArray(data.data.sources)) {
        // 处理数据：按来源分组
        const sourceGroups = {};

        data.data.sources.forEach((sourceData) => {
          // 获取来源信息，有可能是数组或字符串
          let sources = [];
          if (Array.isArray(sourceData.source)) {
            sources = sourceData.source;
          } else if (sourceData.source) {
            sources = [sourceData.source];
          } else {
            sources = ["未知来源"];
          }

          // 对每个来源创建记录
          sources.forEach((source) => {
            const sourceKey = source || "unknown";

            if (!sourceGroups[sourceKey]) {
              sourceGroups[sourceKey] = {
                sourceKey,
                sourceName: source || "未知来源",
                visitCount: 0,
                dealCount: 0, // 使用 successCount
                totalAmount: 0,
                records: [],
              };
            }

            // 累加统计数据
            sourceGroups[sourceKey].visitCount += sourceData.visitCount || 0;
            sourceGroups[sourceKey].dealCount += sourceData.successCount || 0; // 使用 successCount 替换 dealCount
            sourceGroups[sourceKey].totalAmount += sourceData.totalAmount || 0;

            // 保存原始记录用于详细查看
            sourceGroups[sourceKey].records.push(sourceData);
          });
        });

        // 转换为数组
        tableData.value = Object.values(sourceGroups);
      } else {
        tableData.value = [];
      }

      // 使用接口返回的总计数据
      totalVisitCount.value = data.data.totalVisitCount || 0;
      totalDealCount.value = data.data.totalSuccessCount || 0;
      totalAmount.value = data.data.totalAmount || 0;

      console.log("获取现场咨询来源统计数据成功，共 " + tableData.value.length + " 条记录");
    } else {
      ElMessage.error(message || "获取统计数据失败");
      tableData.value = [];
    }
  } catch (error) {
    ElMessage.error("获取统计数据出错：" + (error.message || error));
    tableData.value = [];
    console.error("获取统计数据出错:", error);
  }
}

// 简化后的筛选表格数据 - 直接使用API返回的结果
const filteredTableData = computed(() => {
  return tableData.value;
});

// 按来源分组的数据
const sourceGroupsMap = computed(() => {
  // 创建来源分组
  const sourceMap = {};

  filteredTableData.value.forEach((item) => {
    // 获取来源类型
    // 这里假设有一级来源、二级来源的区分逻辑
    // 简单实现，之后可根据实际数据结构调整
    const sourceType = item.sourceKey.includes("-") ? item.sourceKey.split("-")[0] : item.sourceKey;

    if (!sourceMap[sourceType]) {
      sourceMap[sourceType] = {
        sourceType,
        sources: [],
        totalVisitCount: 0,
        totalDealCount: 0,
        totalAmount: 0,
      };
    }

    // 添加来源数据
    sourceMap[sourceType].sources.push(item);

    // 更新来源组汇总数据
    sourceMap[sourceType].totalVisitCount += item.visitCount || 0;
    sourceMap[sourceType].totalDealCount += item.dealCount || 0;
    sourceMap[sourceType].totalAmount += item.totalAmount || 0;
  });

  return sourceMap;
});

// 列表形式的来源分组数据
const sourceGroupedData = computed(() => {
  return Object.values(sourceGroupsMap.value);
});

// 格式化百分比
const formatPercentage = (value) => {
  if (value === undefined || value === null || isNaN(value)) {
    return "0.00%";
  }
  return `${(value * 100).toFixed(2)}%`;
};

// 处理日期变化
const handleDateChange = () => {
  // 日期改变时，重新调用接口获取数据
  if (visitDateRange.value && visitDateRange.value.length === 2) {
    fetchOnsiteStatistics();
  }
};

// 重置筛选条件
const resetFilters = () => {
  // 设置默认日期范围为当月
  const today = new Date();
  const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
  const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

  visitDateRange.value = [formatDate(firstDayOfMonth), formatDate(lastDayOfMonth)];
  infoSource.value = [];
  consultantFilter.value = [];
  customerSource.value = []; // 重置customerSource
  consultStatus.value = [];
  projectIntentIds.value = [];
  selectedProjects.value = [];
  deptId.value = "";
  selectedDept.value = {};
  developerUserIds.value = [];
  // 重置后重新获取数据
  fetchOnsiteStatistics();
};

// 处理查询
const handleSearch = () => {
  if (visitDateRange.value && visitDateRange.value.length === 2) {
    const start = new Date(visitDateRange.value[0]);
    const end = new Date(visitDateRange.value[1]);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays > 31) {
      ElMessage.warning("查询范围已限制为一个月内");
      return;
    }

    // 获取数据
    fetchOnsiteStatistics();
  } else {
    ElMessage.warning("请选择到院日期范围");
  }
};

// 导出Excel
const exportToExcel = () => {
  // 准备导出数据
  const exportData = [];

  // 添加标题信息
  exportData.push(["现场咨询信息来源表"]);

  // 添加筛选条件信息
  if (visitDateRange.value && visitDateRange.value.length === 2) {
    exportData.push(["到院日期范围", `${visitDateRange.value[0]} 至 ${visitDateRange.value[1]}`]);
  }

  if (consultantFilter.value.length > 0) {
    exportData.push(["咨询师", consultantFilter.value.join(", ")]);
  }

  if (developerUserIds.value.length > 0) {
    const developerNames = developerUserIds.value
      .map((id) => {
        const staff = staffList.value.find((s) => s.userid === id);
        return staff ? staff.anotherName || id : id;
      })
      .join(", ");
    exportData.push(["开发人员", developerNames]);
  }

  if (customerSource.value.length > 0) {
    exportData.push(["开发渠道", customerSource.value.join(", ")]);
  }

  if (consultStatus.value.length > 0) {
    const statusText = consultStatus.value
      .map((status) => {
        if (status === "first") return "初诊";
        if (status === "revisit") return "复诊";
        if (status === "repurchase") return "再消费";
        return status;
      })
      .join(", ");
    exportData.push(["咨询状态", statusText]);
  }

  if (projectIntentIds.value.length > 0) {
    const projectsText = selectedProjects.value.map((p) => p.projectName || p.id).join(", ");
    exportData.push(["咨询项目", projectsText]);
  }

  if (infoSource.value.length > 0) {
    exportData.push(["信息来源", infoSource.value.join(", ")]);
  }

  if (deptId.value && selectedDept.value) {
    exportData.push(["咨询科室", selectedDept.value.deptName || deptId.value]);
  }

  exportData.push([]); // 空行

  // 添加表头
  exportData.push(["信息来源", "来院人次", "来院占比", "成交人次", "成交占比", "未成交人次", "未成交占比", "消费总额"]);

  // 添加按来源分组的数据
  sourceGroupedData.value.forEach((sourceGroup) => {
    // 添加每个来源的数据
    sourceGroup.sources.forEach((item) => {
      exportData.push([item.sourceName || "未知来源", item.visitCount, `${((item.visitCount / totalVisitCount) * 100).toFixed(2)}%`, item.dealCount, `${((item.dealCount / totalDealCount) * 100).toFixed(2)}%`, item.visitCount - item.dealCount, `${(((item.visitCount - item.dealCount) / (totalVisitCount - totalDealCount)) * 100).toFixed(2)}%`, item.totalAmount]);
    });

    // 添加小计行
    if (sourceGroup.sources.length > 1) {
      exportData.push(["小计", sourceGroup.totalVisitCount, `${((sourceGroup.totalVisitCount / totalVisitCount) * 100).toFixed(2)}%`, sourceGroup.totalDealCount, `${((sourceGroup.totalDealCount / totalDealCount) * 100).toFixed(2)}%`, sourceGroup.totalVisitCount - sourceGroup.totalDealCount, `${(((sourceGroup.totalVisitCount - sourceGroup.totalDealCount) / (totalVisitCount - totalDealCount)) * 100).toFixed(2)}%`, sourceGroup.totalAmount]);
    }
  });

  // 添加总计行
  exportData.push(["总计", totalVisitCount.value, formatPercentage(totalVisitCount.value / totalVisitCount.value), totalDealCount.value, formatPercentage(totalDealCount.value / totalDealCount.value), totalVisitCount.value - totalDealCount.value, formatPercentage((totalVisitCount.value - totalDealCount.value) / (totalVisitCount.value - totalDealCount.value)), totalAmount.value]);

  // 创建工作簿和工作表
  const wb = XLSX.utils.book_new();
  const ws = XLSX.utils.aoa_to_sheet(exportData);

  // 设置列宽
  const colWidths = [
    { wch: 18 }, // 信息来源
    { wch: 10 }, // 来院人次
    { wch: 10 }, // 来院占比
    { wch: 10 }, // 成交人次
    { wch: 10 }, // 成交占比
    { wch: 12 }, // 未成交人次
    { wch: 12 }, // 未成交占比
    { wch: 14 }, // 消费总额
  ];

  ws["!cols"] = colWidths;

  // 合并标题单元格
  ws["!merges"] = [
    { s: { r: 0, c: 0 }, e: { r: 0, c: 7 } }, // 标题行
  ];

  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(wb, ws, "现场咨询信息来源表");

  // 生成Excel文件并下载
  const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
  const blob = new Blob([excelBuffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });

  // 使用当前日期作为文件名的一部分
  const now = new Date();
  const fileName = `现场咨询信息来源表_${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}.xlsx`;

  saveAs(blob, fileName);

  ElMessage.success("导出Excel成功");
};

onMounted(async () => {
  // 确保科室列表已加载
  await deptStore.fetchDeptList();
  await judgmentIsAdmin();

  // 加载员工列表
  if (staffList.value.length === 0) await getStaffList();

  const end = new Date();
  const start = new Date(end.getFullYear(), end.getMonth(), 1);
  visitDateRange.value = [formatDate(start), formatDate(end)];

  // 初始加载数据
  fetchOnsiteStatistics();
});

// 监听所有筛选条件变化
watch([consultantFilter, developerUserIds, customerSource, consultStatus, projectIntentIds, infoSource, deptId, consultStages, tradeStatus], () => {
  if (visitDateRange.value && visitDateRange.value.length === 2) {
    fetchOnsiteStatistics();
  }
});
</script>

<style scoped>
.report-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
  background-color: #f5f7fa;
  overflow: hidden;
  min-width: 1200px;
}

.report-header {
  background-color: white;
  padding: 15px 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
}

.filter-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
  margin-bottom: 10px;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-label {
  white-space: nowrap;
  margin-right: 5px;
  font-size: 14px;
  color: #606266;
}

.filter-actions {
  margin-left: auto;
  display: flex;
  gap: 10px;
}

.date-range {
  min-width: 320px;
}

.dept-select,
.consultant-select,
.channel-select,
.consult-status-select {
  min-width: 180px;
}

:deep(.el-date-editor.el-input__wrapper),
:deep(.el-select) {
  width: 100%;
  flex: 1;
}

.table-container {
  flex: 1;
  margin: 15px 0 0 0;
  background-color: white;
  overflow: auto;
  position: relative;
  border-top: 1px solid #ebeef5;
  border-bottom: 1px solid #ebeef5;
}

.performance-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  table-layout: fixed;
}

.performance-table th,
.performance-table td {
  border: 1px solid #ebeef5;
  padding: 12px 8px;
  text-align: center;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.performance-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 10;
}

.total-row {
  background-color: #ebeef5;
  font-weight: bold;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.subtotal-row {
  background-color: #f8f8f8;
  font-weight: bold;
}

.total-label {
  text-align: right;
  padding-right: 10px;
}

.footer-info {
  background-color: white;
  padding: 10px 20px;
  margin: 0;
  display: flex;
  justify-content: end;
  color: #606266;
  font-size: 14px;
  flex-shrink: 0;
  border-top: 1px solid #ebeef5;
}

.date-limit-info {
  color: #e6a23c;
  font-style: italic;
}

@media print {
  .report-container {
    background-color: white;
    height: auto;
  }

  .report-header {
    box-shadow: none;
  }

  .report-actions,
  .footer-info {
    display: none;
  }

  .table-container {
    margin: 0;
    border: none;
    overflow: visible;
    height: auto;
  }

  .performance-table th,
  .performance-table td {
    font-size: 12px;
    padding: 8px 4px;
  }
}

@media (max-width: 768px) {
  .filter-item {
    max-width: 100%;
  }

  .report-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .filters-row {
    flex-direction: column;
  }

  .filter-item {
    width: 100%;
  }

  .filter-controls {
    margin-top: 10px;
    width: 100%;
    justify-content: space-between;
  }
}

@media (min-width: 1400px) {
  .performance-table .amount {
    min-width: 120px;
    max-width: 150px;
  }
}
</style>
