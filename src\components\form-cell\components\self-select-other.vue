<template>
  <div v-if="isRead">
    {{ value && Array.isArray(value) ? value.join("、") : value }}
  </div>
  <div v-else class="flex">
    <!-- <el-select :model-value="selectValue" placeholder="请选择" clearable @update:model-value="change($event)" class="w-200px">
      <el-option v-for="label in item.range" :label="label" :value="label" />
    </el-select> -->

    <div class="drawer">
      <el-tag v-for="i in item.range" :key="i" size="large" class="pointer drawer mr-10px mt-5px"
        :type="i === selectValue ? 'primary' : 'info'" :effect="i === selectValue ? 'light' : 'plain'"
        @click="change(i)">
        {{ i }}
      </el-tag>
      <el-input v-if="showOther" placeholder="请填写" v-model="otherValue" class="w-200px mr-10px mt-5px"
        @focus="focusing=true" @blur="handleBlur" @update:model-value="changeOther($event)"></el-input>
    </div>
  </div>
</template>
<script setup>
import { ref, watch } from "vue";

const $emit = defineEmits(["change"]);
const props = defineProps({
  item: {
    type: Object,
    default: {},
  },
  value: {
    type: String,
    default: "",
  },
  isRead: {
    type: Boolean,
    default: false,
  },
});
let showOther = ref(false);
let selectValue = ref("");
let otherValue = ref("");
const focusing = ref(false);
watch(
  () => props.value,
  (val) => {
    getSelectValue();
  },
  { immediate: true }
);

function handleBlur() {
  focusing.value = false
  getSelectValue()
}
function getSelectValue() {
  if (focusing.value) return
  if (props.item.range.includes(props.value) || props.value === "") {
    selectValue.value = props.value;
    showOther.value = false;
    otherValue.value = "";
    return;
  }
  selectValue.value = props.item.otherFiled;
  showOther.value = true;
  otherValue.value = props.value;
}
function change(value) {
  if (value === selectValue.value) {
    value = "";
    selectValue.value = "";
  }
  if (value === props.item.otherFiled) {
    showOther.value = true;
    selectValue.value = value;
  } else {
    showOther.value = false;
    $emit("change", {
      title: props.item.title,
      value,
    });
  }
}

function changeOther(value) {
  $emit("change", {
    title: props.item.title,
    value,
  });
}
</script>

<style></style>