<template>
  <my-layout bg-fff>
    <layout-item mb-10>
      <statistics :detial="detial" :pannedEventResult="pannedEventResult">
      </statistics>
    </layout-item>
    <layout-main style="overflow-y: hidden">
      <div class="mx-20px overflow-y-hidden mt-20px">
        <el-tabs
          v-model="currentTab"
          @tab-change="change($event)"
          class="border-top-2"
        >
          <el-tab-pane
            v-for="tab in tabs"
            :key="tab.name"
            :label="tab.label"
            :name="tab.name"
          ></el-tab-pane>
        </el-tabs>
        <component
          :is="Comp"
          :detial="detial"
          :pannedEventResult="pannedEventResult"
          @onGetData="getData"
          @reload="getCustomer()"
        ></component>
      </div>
    </layout-main>
  </my-layout>
</template>
<script setup>
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import statistics from "./components/statistics.vue";
import { useRoute, useRouter } from "vue-router";
import { onMounted, ref, computed, reactive } from "vue";
import { getPannedEventById, getPannedEventResult } from "@/api/member";
import taskContent from "./components/taskContent.vue";
import executionList from "./components/executionList.vue";
import plannedCustomers from "./components/plannedCustomers.vue";
const tabs = [
  { label: "任务内容", name: "taskContent", comp: taskContent },
  { label: "员工执行详情", name: "executionList", comp: executionList },
  { label: "加入计划客户", name: "plannedCustomers", comp: plannedCustomers },
];
const Comp = computed(() => {
  const tab = tabs.find((i) => i.name === currentTab.value);
  return tab && tab.comp ? tab.comp : EmptyData;
});
const currentTab = ref("taskContent");
function change(name) {
  currentTab.value = name;
}
const route = useRoute();
const router = useRouter();
let detial = ref({});
let pannedEventResult = ref({});
onMounted(async () => {
  if (route.params.id) {
    await getData(route.params.id);
    await getPannedEventResultAction();
  }
});
async function getData(id) {
  let { data } = await getPannedEventById(id);
  detial.value = data.data[0];
}
async function getPannedEventResultAction() {
  const params = {
    _id: route.params.id,
    corpId: localStorage.getItem("corpId"),
  };
  const { data } = await getPannedEventResult(params);
  pannedEventResult.value = data.data;
}
</script>
<style scoped>
.c-666 {
  color: #666;
}

:deep(.el-tabs__active-bar) {
  height: 4px;
  border-radius: 4px 4px 0 0;
}
</style>
