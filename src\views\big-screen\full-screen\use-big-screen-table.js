import { nextTick, onMounted, onUnmounted, ref, watch } from 'vue';

export default function useBigScreenTable(tableRef, autoScroll = true) {
  const scrollTimer = ref(null);
  const coverStyle = {
    '--el-table-border': 'none',
    '--el-table-bg-color': 'transparent',
    '--el-table-tr-bg-color': 'transparent',
  }

  function getRowStyle({ rowIndex }) {
    // return rowIndex % 2 === 1 ? 'background-color: #010e3b' : ''
    return rowIndex % 2 === 1 ? 'background-color:rgba(100, 176, 239, 0.1)' : ''
  }


  function scrollUp() {
    nextTick(() => {
      const wrap = tableRef.value.$refs.bodyWrapper.getElementsByClassName('el-scrollbar__wrap')[0]
      const tableScroll = ref(true)
      wrap.addEventListener('mouseover', () => {
        tableScroll.value = false
      })
      wrap.addEventListener('mouseout', () => {
        tableScroll.value = true
      })
      scrollTimer.value = setInterval(() => {
        if (tableScroll.value) {
          wrap.scrollTop += 1
          if (wrap.clientHeight + wrap.scrollTop >= wrap.scrollHeight) {
            wrap.scrollTop = 0
          }
        }
      }, 200)
    })
  }
  onMounted(() => autoScroll ? scrollUp() : null)
  onUnmounted(() => scrollTimer.value = null)
  watch(scrollTimer, (n, o) => {
    if (o) clearInterval(o)
  })

  const tableConfig = {
    style: coverStyle,
    rowStyle: getRowStyle,
    headerCellClassName: 'text-white'
  }
  return { tableConfig }

}