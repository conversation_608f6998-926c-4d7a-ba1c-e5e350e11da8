<template>
  <my-layout bg-fff common-shadow>
    <layout-item>
      <div class="top-module">
        <div class="flex justify-around h-70px align-center" border-bottom border-left border-right>
          <div border-right class="f-box">
            <div class="t_1">已结算金额</div>
            <div>¥ {{ freeStatistic.paidTotalPayment }}</div>
          </div>
          <div border-left class="f-box">
            <div class="t_1">未结算金额</div>
            <div>¥ {{ freeStatistic.noPayTotalPayment }}</div>
          </div>
          <div border-left class="f-box">
            <div class="t_1">退费/金额</div>
            <div>¥ {{ freeStatistic.refundTotalPayment }}</div>
          </div>
        </div>
        <div bg-fff class="query mt-15px">
          <el-form-item class="query__item" style="width: 250px">
            <el-date-picker v-model="dates" type="daterange"  :unlink-panels="true"
 value-format="YYYY-MM-DD" placeholder="开单时间" @change="handleChange" />
          </el-form-item>
          <el-form-item class="query__item">
            <el-input flex-grow placeholder="输入项目名称" class="w-180px" v-model="orderName" @input="search" />
          </el-form-item>
          <el-form-item class="query__item">
            <el-select v-model="orderStatus" placeholder="支付状态" clearable @change="search">
              <el-option v-for="item in payStatusList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item class="query__item">
            <el-select v-model="doctorAdvice" placeholder="类型" clearable @change="search">
              <el-option v-for="item in ProjectType" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item class="query__item">
            <el-select v-model="billType" placeholder="类别" clearable @change="search">
              <el-option v-for="item in billTypeObj" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item class="query__item query__item--auto">
            <el-button type="primary" @click="handleChange">查询</el-button>
            <el-button type="primary" @click="reset">重置</el-button>
          </el-form-item>
        </div>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <div h-full px-15>
        <el-table stripe v-loading="loading" border height="100%" :data="searchData">
          <el-table-column property="doctorAdvice" label="类型" />
          <el-table-column property="projectId" label="医嘱代码" />
          <el-table-column property="orderName" label="项目名称" />
          <el-table-column property="payType" label="费别" />
          <el-table-column property="billType" label="类别" />
          <el-table-column property="projectCount" label="数量" />
          <el-table-column property="projectPrice" label="单价" />
          <el-table-column property="payment" label="总额 " />
          <el-table-column property="orderNo" label="订单号" />
          <el-table-column property="settlementTime" label="结算时间" width="150" />
          <el-table-column property="billTime" label="开单时间" width="150" />
          <el-table-column property="billDept" label="开单科室" />
          <el-table-column property="billDoctor" label="开单医生" />
          <el-table-column property="orderStatus" label="支付状态" />
        </el-table>
      </div>
    </layout-main>
  </my-layout>
</template>
<script setup>
import dayjs from "dayjs";
import { ref, onMounted, computed } from "vue";
import { getHisFeeRecord } from "@/api/customerHisSync";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import { ProjectType } from "@/baseData";
import BigNumber from "bignumber.js";
const orderName = ref("");
const orderStatus = ref("");
const doctorAdvice = ref("");
const billType = ref("");
const { customer } = defineProps(["customer"]);
const loading = ref(false);
const listData = ref([]);
const billTypeObj = [
  {
    label: "门诊",
    value: "门诊",
  },
  {
    label: "住院",
    value: "住院",
  },
];
const dates = ref([dayjs().startOf("month").format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")]);
const freeStatistic = computed(() => {
  return {
    noPayTotalPayment: addpayment("为结算"),
    paidTotalPayment: addpayment("已结算"),
    refundTotalPayment: addpayment("已退费"),
  };
});

const searchData = computed(() => {
  let list = listData.value;
  if (orderName.value) list = list.filter((i) => i.orderName.includes(orderName.value));
  if (orderStatus.value) list = list.filter((i) => i.orderStatus === orderStatus.value);
  if (doctorAdvice.value) list = list.filter((i) => i.doctorAdvice === doctorAdvice.value);
  if (billType.value) list = list.filter((i) => i.billType === billType.value);
  return list;
});

function addpayment(type) {
  const validOrders = searchData.value.filter((item) => item.orderStatus === type);
  // 使用 BigNumber 对 `payment` 字段相加
  let totalAmount = new BigNumber(0);
  validOrders.forEach((order) => {
    totalAmount = totalAmount.plus(new BigNumber(order.payment)); // 或者 totalFee
  });
  return totalAmount;
}
const payStatusList = [
  {
    label: "已结算",
    value: "已结算",
  },
  {
    label: "未结算",
    value: "未结算",
  },
  {
    label: "已退费",
    value: "已退费",
  },
];
onMounted(async () => {
  await getList();
});
// 获取服务记录
async function getList() {
  if (!customer.customerNumber) return;
  if (dates.value.length === 0) {
    ElMessage.warning("请选择结算时间!");
    return;
  }
  loading.value = true;
  const params = {
    idNo: customer.customerNumber,
    startTime: dates.value[0],
    endTime: dates.value[1],
  };
  const { success, data } = await getHisFeeRecord(params);
  listData.value = success ? data.list : [];
  // 模糊匹配

  loading.value = false;
}
async function handleChange() {
  getList();
}

async function reset() {
  dates.value = [dayjs().startOf("month").format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")];
  orderName.value = "";
  orderStatus.value = "";
  doctorAdvice.value = "";
  billType.value = "";
  getList();
}
</script>
<style scoped lang="scss">
:deep(.el-table__inner-wrapper) {
  height: 100% !important;
}

.query {
  display: flex;
  flex-wrap: wrap;
  padding: 0 15px;

  .query__item {
    width: 180px;
    margin-bottom: 15px;
    margin-right: 10px;

    @at-root &--large {
      width: 240px;
    }

    @at-root &--small {
      width: 140px;
    }

    @at-root &--small {
      width: 140px;
    }

    @at-root &--auto {
      width: auto;
    }

    @at-root &:last-of-type {
      margin-left: auto;
      margin-right: 0;
    }
  }
}

.f-box {
  width: 33.33%;
  height: 70px;
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;

  .t_1 {
    font-size: 14px;
    color: #333;
  }
}
</style>