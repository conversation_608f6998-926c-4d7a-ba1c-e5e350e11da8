<template>
  <my-layout class="bg-[#f2f3f4]">
    <layout-item>
      <!-- 统计数据 -->
      <div class="stats-container flex mb-10px">
        <div class="stat-item flex-1 bg-white p-15px text-center">
          <div class="text-18px font-bold">{{ stats.total || 0 }}</div>
          <div class="text-14px text-gray-500">网络咨询人次</div>
        </div>
        <div class="stat-item flex-1 bg-white p-15px text-center ml-10px">
          <div class="text-18px font-bold">{{ stats.visited || 0 }}</div>
          <div class="text-14px text-gray-500">到院人次</div>
        </div>
        <div class="stat-item flex-1 bg-white p-15px text-center ml-10px">
          <div class="text-18px font-bold">{{ stats.notVisited || 0 }}</div>
          <div class="text-14px text-gray-500">未到院人次</div>
        </div>
      </div>

      <!-- 筛选条件 -->
      <div class="py-5px flex bg-white mb-10px">
        <expand-filter-box v-model:can-expand="canExpand" :enable-expand="true" class="flex-grow" :expand="expand">
          <!-- 咨询时间 -->
          <date-range-filter v-model:dates="consultTimeRange" label="咨询时间" :text="consultTimeRangeText" :width="320" @change="handleDateRangeChange" @clear="clearDateRangeChange" />
          <!-- 登记人 -->
          <check-box-filter v-model="registrantUserIds" label="登记人" :list="developersOptions" @clear="handleClearRegistrant" v-if="isManager">
            <template #item="{ item }">
              <ww-user :openid="item.value"></ww-user>
            </template>
          </check-box-filter>
          <!-- 所属开发 -->
          <check-box-filter v-model="queryParams.developerIds" label="所属开发" :list="developersOptions" @clear="handleClearDeveloper">
            <template #item="{ item }">
              <ww-user :openid="item.value"></ww-user>
            </template>
          </check-box-filter>
          <!-- 信息来源 -->
          <filter-info-source v-model="queryParams.source" />
          <!-- 咨询科室 -->
          <div class="text-gray-500 text-14px flex items-center ml-10px">
            <div class="filter-label pr-5px">咨询科室:</div>
            <dept-select v-model="deptIds" placeholder="请选择科室" @change="handleDeptChange" />
          </div>
          <!-- 意向项目 -->
          <div class="text-gray-500 text-14px flex items-center ml-10px">
            <div class="filter-label pr-5px">意向项目:</div>
            <project-intent-select v-model="queryParams.projectIds" @change="handleProjectChange" placeholder="请选择意向项目" type="filter" style="width: 200px" :deptId="deptIds" multiple />
          </div>
          <!-- 到院状态 -->
          <radio-filter
            v-model="queryParams.isArrived"
            label="到院状态"
            :list="[
              { label: '全部', value: '' },
              { label: '未到院', value: false },
              { label: '已到院', value: true },
            ]"
          ></radio-filter>

          <!-- 客户姓名 -->
          <input-filter v-model="queryParams.customerName" label="客户姓名" placeholder="请输入" :width="200" />

          <!-- 客户手机号 -->
          <input-filter v-model="queryParams.mobile" label="客户手机号" placeholder="请输入" :width="200" />

        </expand-filter-box>

        <div class="flex-shrink-0 flex items-center h-40px pr-15px">
          <el-button v-if="canExpand" text plain @click="expand = !expand">
            <span class="mr-5px">{{ expand ? "收起筛选" : "更多筛选" }}</span>
            <el-icon v-if="expand">
              <ArrowUpBold />
            </el-icon>
            <el-icon v-else>
              <ArrowDownBold />
            </el-icon>
          </el-button>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button type="success" @click="exportToExcel">导出Excel</el-button>
          <el-button @click="handleReset">重置</el-button>
        </div>
      </div>
    </layout-item>

    <!-- 表格数据 -->
    <layout-main :scroll="false">
      <el-table border stripe :data="tableData" v-loading="loading" height="100%">
        <el-table-column label="到院状态" width="100">
          <template #default="{ row: { inHospitalTimes } }">
            <div :style="{ color: isInHospital(inHospitalTimes) ? 'green' : 'red' }">
              {{ isInHospital(inHospitalTimes) ? "已到院" : "未到院" }}
            </div>
          </template>
        </el-table-column>
        <el-common-column prop="date" label="咨询时间" :min-width="120" />
        <el-common-column prop="customerName" label="姓名" :min-width="100">
          <template #default="{ row }">
            <span @click.stop="toCustomerDetail(row.customerId)" class="text-blue-500 cursor-pointer">{{ row.customerName }}</span>
          </template>
        </el-common-column>
        <el-common-column prop="mobile" label="联系方式 " :min-width="120">
          <template #default="{ row: { customerMobile } }">
            <span>{{ maskPhone(customerMobile, 3, 4) }}</span>
          </template>
        </el-common-column>
        <el-common-column prop="project" label="意向项目" :min-width="160">
          <template #default="{ row }">
            <el-tooltip :disabled="!row.projectNames" placement="top" effect="light" :content="row.projectNames" popper-class="max-w-480px">
              <span>{{ row.projectNames }}</span>
            </el-tooltip>
          </template>
        </el-common-column>
        <el-common-column prop="developer" label="所属开发" :min-width="120">
          <template #default="{ row }">
            <ww-user v-if="row.userId" :openid="row.userId" />
          </template>
        </el-common-column>
        <el-common-column prop="source" label="信息来源" :min-width="100">
          <template #default="{ row }">
            <el-tooltip placement="top" effect="light" :disabled="!row.sourceName || row.sourceName.length < 7" :content="row.sourceName">
              <span>{{ row.sourceName }}</span>
            </el-tooltip>
          </template>
        </el-common-column>
        <el-common-column prop="department" label="咨询科室" :min-width="120">
          <template #default="{ row: { projectDeptIds } }">
            <dept-name-transformer :dept-id="projectDeptIds" />
          </template>
        </el-common-column>
        <el-common-column prop="remark" label="备注说明" :min-width="240">
          <template #default="{ row }">
            <el-popover placement="top" width="400" trigger="click" v-if="row.reportDesc">
              <template #reference>
                <div class="cursor-pointer whitespace-pre-wrap leading-5 max-h-10 overflow-hidden line-clamp-2 hover:text-blue-500 transition-colors" @click.stop>
                  {{ row.reportDesc }}
                </div>
              </template>
              <div class="whitespace-pre-wrap text-14px leading-6 max-h-300px overflow-y-auto">
                {{ row.reportDesc }}
              </div>
            </el-popover>
            <div v-else class="text-gray-400">暂无备注说明</div>
          </template>
        </el-common-column>
        <el-common-column prop="register" label="登记人" :min-width="140">
          <template #default="{ row }">
            <ww-user v-if="row.registrantUserId || row.userId" :openid="row.registrantUserId || row.userId" />
          </template>
        </el-common-column>
        <el-common-column fixed="right" prop="action" label="操作" :width="160">
          <template #default="{ row }">
            <span class="cursor-pointer text-blue-500" @click="viewConsultDetail(row)">详情</span>
            <span v-if="canEdit(row)" class="cursor-pointer text-blue-500 ml-10px" @click="editConsult(row)">编辑</span>
            <span class="cursor-pointer text-blue-500 ml-10px" @click="toAppointment(row)">预约</span>
          </template>
        </el-common-column>
      </el-table>
    </layout-main>

    <layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize" @handle-current-change="changePage" />
    </layout-item>
  </my-layout>

  <!-- 弹窗组件 -->
  <add-e-consult :customer="currentCustomer" :record="currentConsult" :visible="addEConsultVisible" :mode="consultMode" @close="closeAddConsult" @change="changeSuccess" />
  <!-- 客户详情弹窗 -->
  <customer-detail :enableAction="['managementPlan']" :visible="customerDetailVisible" @close="customerDetailVisible = false" :customerId="customerId" title="客户详情" @update="handleSearch" customerType="corpCustomer" />
  <!-- 预约弹窗 -->
  <appointment-modal :customer="appointmentCustomer" :visible="appointmentVisible" :width="appointmentWidth" @close="closeAppointment" @success="onAppointmentSuccess" />
</template>

<script setup>
import { ref, computed, onMounted, reactive, watch } from "vue";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import { getEConsuleRecord } from "@/api/consult";
import { memberStore } from "@/store/member";
import { teamStore } from "@/store/team";
import { staffStore } from "@/store/staff";
import useElPagination from "@/hooks/useElPagination";
import { useDeptStore } from "@/components/dept-components/dept-store";
import { ElMessage } from "element-plus";
import addEConsult from "./add-e-consult.vue";
import { maskPhone } from "@/utils";
import elCommonColumn from "@/components/el-common-column/el-common-column.vue";
import MyLayout, { LayoutMain } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import { CheckBoxFilter, DateRangeFilter, InputFilter, filterInfoSource, expandFilterBox, RadioFilter } from "@/components/filter-bar";
import DeptSelect from "@/components/dept-components/dept-select.vue";
import projectIntentSelect from "@/components/benefit-management/project-intent-select.vue";
import customerDetail from "@/views/member/detail/index.vue"; // 导入客户详情组件
import DeptNameTransformer from "@/components/dept-components/dept-name-transformer.vue";
import appointmentModal from "@/components/appointment-modal/index.vue";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { ElLoading } from "element-plus";

const { corpInfo, isAdmin } = storeToRefs(memberStore());
const { judgmentIsAdmin } = memberStore();
const { currentTeamId, managerList } = storeToRefs(teamStore());
const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());
const deptStore = useDeptStore();
const userId = localStorage.getItem("userId");
const deptIds = ref([]);
// 数据加载状态
const loading = ref(false);
const stats = ref({
  total: 0,
  visited: 0,
  notVisited: 0,
});
const total = ref(0);
// 分页相关
const { page, pageSize, changePage, changeSize } = useElPagination(getList);
const tableData = ref([]);
// 筛选相关
const canExpand = ref(false);
const expand = ref(false);
const consultTimeRange = ref([dayjs().format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")]);
const consultTimeRangeText = computed(() => consultTimeRange.value.join(" ~ "));

// 新增变量
const selectedDept = ref({});

// 新增登记人相关变量
const hasExplicitlyClearedRegistrants = ref(false);
const selectedRegistrants = ref([]);

// 登记人计算属性
const registrantUserIds = computed({
  get() {
    if (selectedRegistrants.value.length > 0) {
      return selectedRegistrants.value;
    }
    if (hasExplicitlyClearedRegistrants.value) {
      return [];
    }
    const currentUserId = localStorage.getItem("userId");
    if (!isManager.value) {
      // 不是主管，只能看到自己
      return [currentUserId];
    } else {
      // 主管
      const inList = developersOptions.value.some((i) => i.value === currentUserId);
      if (inList) {
        return [currentUserId];
      } else {
        // 展示全部
        return [];
      }
    }
  },
  set(newVal) {
    selectedRegistrants.value = newVal;
    hasExplicitlyClearedRegistrants.value = false;
  },
});

const queryParams = reactive({
  startDate: dayjs().format("YYYY-MM-DD"),
  endDate: dayjs().format("YYYY-MM-DD"),
  developerIds: [],
  customerName: "",
  mobile: "",
  projectIds: [],
  isArrived: "",
  source: [],
});

function clearDateRangeChange() {
  queryParams.startDate = "";
  queryParams.endDate = "";
}

function isInHospital(inHospitalTimes) {
  return inHospitalTimes && Array.isArray(inHospitalTimes) && inHospitalTimes.length > 0;
}

// 详情、编辑相关
const addEConsultVisible = ref(false);
const currentConsult = ref({});
const currentCustomer = ref({});
const consultMode = ref("edit");
// 客户详情相关
const customerDetailVisible = ref(false);
const customerId = ref("");

// 权限控制
const isManager = computed(() => {
  return managerList.value.includes(10026) || isAdmin.value;
});

// 开发人员选项
const developersOptions = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("onlineCustomerService")).map((i) => ({ value: i.userid }));
});

// 咨询人员选项
const consultantsOptions = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("consultant")).map((i) => ({ value: i.userid }));
});

// 监听团队ID变化时刷新数据
watch(
  () => currentTeamId.value,
  () => {
    handleSearch();
  }
);

onMounted(async () => {
  try {
    await judgmentIsAdmin();
    await getStaffList(true);
    await deptStore.fetchDeptList();
    await initPage();
  } catch (error) {
    ElMessage.error('页面初始化失败，请刷新重试');
  }
});

// 初始化页面
function initPage() {
  handleSearch();
}

// 获取列表数据和统计数据
async function getList() {
  loading.value = true;
  try {
    let requestParams = {
      ...buildRequestParams(),
      page: page.value,
      pageSize: pageSize.value,
      isStatistics: true, // 添加统计标记，同时获取统计数据
    };
    if (!isManager.value) {
      requestParams.registrantUserIds = [userId];
    } else if (registrantUserIds.value.length > 0) {
      requestParams.registrantUserIds = registrantUserIds.value;
    }
    // 处理科室相关参数
    if (deptIds.value && deptIds.value.length > 0) {
      requestParams.projectIds = requestParams.projectIds && requestParams.projectIds.length > 0 ? requestParams.projectIds : (await deptStore.fetchProjectIdsByDeptId(deptIds.value)) || [];
    }

    const { data, message, success } = await getEConsuleRecord(requestParams);

    if (success) {
      stats.value = data.statistics || {};
      const formattedList = Array.isArray(data.list)
        ? data.list.map((item) => ({
            ...item,
            projectNames: Array.isArray(item.projectNames) ? item.projectNames.join("、") : "",
            sourceName: Array.isArray(item.source) ? item.source.join("-") : "",
            isArrived: item.isArrived || false,
          }))
        : [];

      tableData.value = formattedList;
      total.value = data.total > 0 ? data.total : 0;
      // 更新统计数据
      if (data.stats) {
        stats.value = {
          totalConsultations: data.stats.total || 0,
          arrivedCount: data.stats.arrivedCount || 0,
          notArrivedCount: data.stats.notArrivedCount || 0,
        };
      }
    } else {
      ElMessage.error(message || "获取数据失败");
    }
  } catch (error) {
    ElMessage.error("获取数据出错");
    console.error("获取数据出错", error);
  } finally {
    loading.value = false;
  }
}

// 构建请求参数
function buildRequestParams() {
  const params = { ...queryParams };

  // 处理时间范围
  if (consultTimeRange.value && consultTimeRange.value.length === 2) {
    params.startDate = consultTimeRange.value[0];
    params.endDate = consultTimeRange.value[1];
  }

  return params;
}

// 日期范围变更
function handleDateRangeChange(dates) {
  if (dates && dates.length === 2) {
    queryParams.startDate = dates[0];
    queryParams.endDate = dates[1];
  } else {
    queryParams.startDate = "";
    queryParams.endDate = "";
  }
}

// 科室变更
function handleDeptChange(dept) {
  // 更新选中的科室对象，与e-store-report.vue保持一致
  selectedDept.value = dept;

  // 当科室变更时，刷新项目列表
  if (dept) {
    deptStore.fetchProjectIdsByDeptId(dept);
    // 清空已选择的项目，避免科室变更后项目不匹配
    queryParams.projectIds = [];
  }
}

// 项目变更
function handleProjectChange(projects) {
  console.log("Selected projects:", projects);
  // 项目变更时的额外逻辑可以在这里添加
}

// 清除开发人员
function handleClearDeveloper() {
  queryParams.developerIds = [];
}

// 清除登记人
function handleClearRegistrant() {
  hasExplicitlyClearedRegistrants.value = true;
  selectedRegistrants.value = [];
}

// 搜索
function handleSearch() {
  changePage(1);
}

// 重置
function handleReset() {
  consultTimeRange.value = [dayjs().format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")];
  hasExplicitlyClearedRegistrants.value = false;
  selectedRegistrants.value = [];
  Object.assign(queryParams, {
    startDate: dayjs().format("YYYY-MM-DD"),
    endDate: dayjs().format("YYYY-MM-DD"),
    registrantUserIds: [],
    developerIds: [],
    customerName: "",
    mobile: "",
    projectIds: [],
    isArrived: "",
    source: [],
  });

  handleSearch();
}

// 查看详情
function viewConsultDetail(row) {
  currentConsult.value = { ...row };
  currentCustomer.value = { _id: row.customerId };
  consultMode.value = "view";
  addEConsultVisible.value = true;
}

// 编辑记录
function editConsult(row) {
  currentConsult.value = { ...row };
  currentCustomer.value = { _id: row.customerId };
  consultMode.value = "edit";
  addEConsultVisible.value = true;
}

// 查看客户详情
function toCustomerDetail(id) {
  customerId.value = id;
  customerDetailVisible.value = true;
}

// 关闭弹窗
function closeAddConsult() {
  addEConsultVisible.value = false;
}

// 保存成功后的回调
function changeSuccess() {
  addEConsultVisible.value = false;
  handleSearch();
}

// 判断是否可以编辑记录
function canEdit(row) {
  if (!row) return false;

  // 判断是否是当天记录
  const isToday = dayjs(row.date).format("YYYY-MM-DD") === dayjs().format("YYYY-MM-DD");

  // 如果是管理员或有主管权限，可以编辑所有当天记录
  if (isManager.value && isToday) {
    return true;
  }

  // 普通用户只能编辑自己的当天记录
  const isSelfRecord = (row.registrantUserId || row.userId) === userId;

  return isToday && isSelfRecord;
}

// 预约相关
const appointmentCustomer = ref({});
const appointmentVisible = ref(false);
const appointmentWidth = ref(600);

function toAppointment(row) {
  // 将行数据映射为预约组件期望的客户数据格式
  appointmentCustomer.value = {
    _id: row.customerId,
    name: row.customerName,
    mobile: row.customerMobile,
    introducerRecord: row.userId ? [{ introducer: row.userId }] : [],
    lastAppointmentTime: row.lastAppointmentTime || null,
  };
  appointmentVisible.value = true;
}

function closeAppointment() {
  appointmentVisible.value = false;
}

function onAppointmentSuccess() {
  appointmentVisible.value = false;
  handleSearch();
}

// 导出Excel
async function exportToExcel() {
  if (loading.value) {
    ElMessage.warning("数据正在加载中，请稍后再试");
    return;
  }

  // 显示加载状态
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '正在获取数据，请稍候...',
    background: 'rgba(0, 0, 0, 0.7)'
  });

  try {
    // 先获取总数据量
    let totalParams = {
      ...buildRequestParams(),
      page: 1,
      pageSize: 1, // 只获取总数，不需要实际数据
      isStatistics: false, // 不需要统计数据
    };

    if (!isManager.value) {
      totalParams.registrantUserIds = [userId];
    } else if (registrantUserIds.value.length > 0) {
      totalParams.registrantUserIds = registrantUserIds.value;
    }

    // 处理科室相关参数
    if (deptIds.value && deptIds.value.length > 0) {
      totalParams.projectIds = totalParams.projectIds && totalParams.projectIds.length > 0 ? totalParams.projectIds : (await deptStore.fetchProjectIdsByDeptId(deptIds.value)) || [];
    }

    // 获取总数据量
    const totalResult = await getEConsuleRecord(totalParams);
    const totalCount = totalResult.data.total;

    if (totalCount === 0) {
      loadingInstance.close();
      ElMessage.warning("暂无数据可导出");
      return;
    }

    // 计算需要分多少批
    const pageSize = 1000; // 每批1000条
    const totalPages = Math.ceil(totalCount / pageSize);
    
    loadingInstance.setText(`正在下载数据，共${totalCount}条记录，请稍候...`);

    // 分批获取所有数据
    const allData = [];
    for (let currentPage = 1; currentPage <= totalPages; currentPage++) {
      loadingInstance.setText(`正在下载数据 ${currentPage}/${totalPages} 批，共${totalCount}条记录...`);
      
      const batchParams = {
        ...totalParams,
        page: currentPage,
        pageSize: pageSize
      };

      const batchResult = await getEConsuleRecord(batchParams);
      if (batchResult.success && batchResult.data.list) {
        const batchData = batchResult.data.list.map((item) => ({
          ...item,
          projectNames: Array.isArray(item.projectNames) ? item.projectNames.join("、") : "",
          sourceName: Array.isArray(item.source) ? item.source.join("-") : "",
          isArrived: item.isArrived || false,
        }));

        allData.push(...batchData);
      }

      // 添加小延迟，避免请求过于频繁
      if (currentPage < totalPages) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    loadingInstance.setText('正在生成Excel文件...');

    // 准备导出数据
    const exportData = [];

    // 添加标题
    exportData.push(["网电咨询记录数据表"]);

    // 添加筛选条件信息
    if (consultTimeRange.value && consultTimeRange.value.length === 2) {
      exportData.push(["咨询时间范围", `${consultTimeRange.value[0]} 至 ${consultTimeRange.value[1]}`]);
    }

    if (queryParams.customerName) {
      exportData.push(["客户姓名", queryParams.customerName]);
    }

    if (queryParams.mobile) {
      exportData.push(["客户手机号", queryParams.mobile]);
    }

    if (queryParams.source && queryParams.source.length > 0) {
      exportData.push(["信息来源", queryParams.source.join(", ")]);
    }

    if (queryParams.isArrived !== "") {
      const arrivalText = queryParams.isArrived === true ? "已到院" : queryParams.isArrived === false ? "未到院" : "全部";
      exportData.push(["到院状态", arrivalText]);
    }

    // 添加统计信息
    exportData.push(["网络咨询人次", stats.value.total || 0]);
    exportData.push(["到院人次", stats.value.visited || 0]);
    exportData.push(["未到院人次", stats.value.notVisited || 0]);

    exportData.push([`总计导出记录数: ${allData.length} 条`]);
    exportData.push([]); // 空行

    // 添加表头
    exportData.push([
      "到院状态",
      "咨询时间", 
      "姓名",
      "联系方式",
      "意向项目",
      "所属开发",
      "信息来源",
      "咨询科室",
      "备注说明",
      "登记人"
    ]);

    // 添加数据行
    allData.forEach((row) => {
      const arrivalStatusText = isInHospital(row.inHospitalTimes) ? "已到院" : "未到院";
      const contactInfo = row.customerMobile;

      exportData.push([
        arrivalStatusText,
        row.date || "",
        row.customerName || "",
        contactInfo || "",
        row.projectNames || "",
        "", // 所属开发，需要通过ww-user组件获取名字，这里暂时留空
        row.sourceName || "",
        "", // 咨询科室，需要通过dept-name-transformer组件获取，这里暂时留空
        row.reportDesc || "",
        "" // 登记人，需要通过ww-user组件获取名字，这里暂时留空
      ]);
    });

    // 创建工作簿和工作表
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(exportData);

    // 设置列宽
    const colWidths = [
      { wch: 10 }, // 到院状态
      { wch: 18 }, // 咨询时间
      { wch: 12 }, // 姓名
      { wch: 15 }, // 联系方式
      { wch: 30 }, // 意向项目
      { wch: 12 }, // 所属开发
      { wch: 20 }, // 信息来源
      { wch: 15 }, // 咨询科室
      { wch: 40 }, // 备注说明
      { wch: 12 }  // 登记人
    ];

    ws["!cols"] = colWidths;

    // 合并标题单元格
    ws["!merges"] = [
      { s: { r: 0, c: 0 }, e: { r: 0, c: 9 } }, // 标题行
    ];

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, "网电咨询记录数据");

    // 生成Excel文件并下载
    const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
    const blob = new Blob([excelBuffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });

    // 使用当前日期作为文件名的一部分
    const now = new Date();
    const fileName = `网电咨询记录数据_${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}.xlsx`;

    saveAs(blob, fileName);

    loadingInstance.close();
    ElMessage.success(`导出Excel成功，共导出 ${allData.length} 条记录`);

  } catch (error) {
    loadingInstance.close();
    console.error('导出Excel失败:', error);
    ElMessage.error("导出Excel失败，请稍后重试");
  }
}
</script>

<style scoped>
.stats-container .stat-item {
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.filter-label {
  white-space: nowrap;
}
</style>
