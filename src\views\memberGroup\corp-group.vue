<template>
  <my-layout bg-fff common-shadow>
    <layout-item>
      <div flex items justify-between p-15>
        <div flex items-center>
          <el-input placeholder="输入分组名称搜索" v-model="searchName" @input="inputChange" />
        </div>
        <el-button type="primary" plain @click="addGroup()">新增分组</el-button>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <el-table stripe height="100%" :data="list" v-loading="loading" :header-cell-style="{ background: '#ecf4ff' }">
        <el-table-column property="index" label="序号" width="80" />
        <el-table-column property="groupName" label="分组名称"></el-table-column>
        <el-table-column property="description" label="使用说明"></el-table-column>
        <el-table-column fixed="right" class-name="el-table-fixed-column--shadow" label="操作" :width="70">
          <template #default="{ row }">
            <el-button text class="table-action-btn mx-auto" type="primary" size="small" @click="edit(row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </layout-main>
    <layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="currentPage" @handle-size-change="onSizeChange" @handle-current-change="onCurrentChange" />
    </layout-item>
    <el-dialog :title="type === 'edit' ? '编辑分组' : '新增分组'" v-model="visible" width="500">
      <div p-15>
        <el-form :model="form" :label-width="100" :rules="rules" ref="planFormRef">
          <el-form-item label="分组名称：" class="is-required" prop="groupName">
            <el-input v-model="form.groupName" placeholder="请输入分组名称"></el-input>
          </el-form-item>
          <el-form-item label="使用说明：">
            <el-input v-model="form.description" maxlength="100" :autosize="{ minRows: 5, maxRows: 8 }" resize="none" show-word-limit type="textarea" placeholder="请输入使用说明"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div text-center>
          <el-button class="w-100px" @click="visible = false">取消</el-button>
          <el-button v-if="type === 'edit'" type="danger" class="w-100px" @click="remove()">删除</el-button>
          <el-button class="w-100px" type="primary" @click="save(planFormRef)">保存</el-button>
        </div>
      </template>
    </el-dialog>
  </my-layout>
</template>
  <script setup>
import { computed, ref, reactive } from "vue";
import { ElMessage } from "element-plus";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import { ElMessageBox } from "element-plus";
import { createGroup, getGroups, removeGroup, updateGroup } from "@/api/group";
import { watchDebounced } from "@vueuse/core";
const loading = ref(false);
const pageSize = ref(10);
const currentPage = ref(1);
const list = ref([]);
const total = ref(0);
const type = ref("add");
const visible = ref(false);
const planFormRef = ref("");
const searchName = ref("");
const selectRow = ref({});

const form = ref({
  groupName: "",
  description: "",
});
let rules = reactive({
  groupName: [{ required: true, message: "请输入分组名称", trigger: "blur" }],
});
fetchData();
function onSizeChange(e) {
  currentPage.value = 1;
  pageSize.value = e;
  fetchData();
}
function onCurrentChange(e) {
  currentPage.value = e;
  fetchData();
}
async function fetchData() {
  loading.value = true;
  const { success, data } = await getGroups({ page: currentPage.value, pageSize: pageSize.value, groupName: searchName.value, groupType: "corp" });
  loading.value = false;
  if (!success) {
    ElMessage.error("获取分组列表失败");
    return;
  }
  list.value = data.data.map((item, index) => {
    return {
      ...item,
      index: (currentPage.value - 1) * pageSize.value + index + 1,
    };
  });
  total.value = data.total;
}
function addGroup() {
  type.value = "add";
  form.value = {
    groupName: "",
    description: "",
  };
  visible.value = true;
}
function save(formEl) {
  if (!formEl) return;
  formEl.validate(async (valid) => {
    if (valid) {
      const { success, message } = await operation();
      if (!success) {
        ElMessage.error(message);
        return;
      }
      visible.value = false;
      currentPage.value = 1;
      fetchData();
    }
  });
}
async function operation() {
  if (type.value === "add") {
    const query = {
      ...form.value,
      groupType: "corp",
      creator: localStorage.getItem("userId"),
    };
    return await createGroup(query);
  } else {
    if (selectRow.value.groupName === form.value.groupName && selectRow.value.description === form.value.description) {
      return;
    }
    return await updateGroup(selectRow.value._id, form.value);
  }
}
function edit(row) {
  const { groupName = "", description = "" } = row;
  selectRow.value = row;
  type.value = "edit";
  form.value = { groupName, description };
  visible.value = true;
}
function remove() {
  if (selectRow.value.memberCount > 0) {
    ElMessageBox.alert("该机构分组还存在客户，请将客户转组后再删除本分组!", "提示", {
      confirmButtonText: "知道了",
    });
    return;
  }
  ElMessageBox.confirm("确定删除该分组吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    let { success, message } = await removeGroup({
      id: selectRow.value._id,
      groupType: "corp",
    });
    if (success) {
      visible.value = false;
      currentPage.value = 1;
      fetchData();
    } else {
      ElMessage.error(message);
    }
  });
}
watchDebounced(
  searchName,
  () => {
    currentPage.value = 1;
    fetchData();
  },
  { immediate: false, debounce: 750, maxWait: 5 * 1000 }
);
</script>
  
  <style scoped>
.table-action-btn {
  padding-left: 0;
  padding-right: 0;
}
</style>