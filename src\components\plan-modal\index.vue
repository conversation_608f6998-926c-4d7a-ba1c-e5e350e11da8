<template>
  <el-dialog :model-value="props.visible" :width="width" title="新增更进计划" @close="close()">
    <el-scrollbar wrap-class="el-dialog-body-full-scroll">
      <el-form class="p-15px" label-suffix="：">
        <el-row class="border-b mb-15px border-gray-200">
          <el-col :span="12">
            <el-form-item class="mb-15px" label="客户">{{ 王大海 }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item class="mb-15px" label="跟进人">
              <el-select>
                <el-option label="张三" value="1" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="跟进计划">
              <el-button text type="primary"><el-icon class="mr-5px">
                  <CirclePlusFilled />
                </el-icon>添加</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <template v-for="i in 3" :key="i">
          <el-row :class="i > 1 ? 'mt-15px':''">
            <el-col :span="12">
              <el-form-item label="跟进类型">
                <el-select>
                  <el-option label="电话" value="1" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="跟进时间">
                <el-select>
                  <el-option label="电话" value="1" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-input type="textarea" v-model="form.desc" placeholder="请输入跟进内容" maxlength="500"
                :autosize="{minRows: 4, maxRows: 6}" resize="none" show-word-limit></el-input>
            </el-col>
          </el-row>
        </template>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button plain class="w-100px" type="primary" :icon="Plus" @click="close()">取消</el-button>
        <el-button class="w-100px" :loading="loading" type="primary" :icon="Plus" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ElMessage } from "element-plus";
import { computed, ref, watch } from "vue";
const emits = defineEmits(['close', 'change']);
const props = defineProps({
  id: { type: String, default: '' },
  visible: { type: Boolean, default: false },
  cateTree: { type: Array, default: () => [] },
  updateMethod: { type: Function, default: () => { } },
  width: { type: String, default: '500px' },
})
const form = ref({})
const cateId = ref('');
const cateIdStr = computed(() => {
  if (typeof cateId.value === 'string') return cateId.value;
  return Array.isArray(cateId.value) ? cateId.value[cateId.value.length - 1] || '' : '';
})

function close() {
  emits('close')
}

const loading = ref(false);
async function confirm() {
  if (props.id === cateIdStr.value) {
    close()
    return
  }
  loading.value = true;
  const { success, message } = await props.updateMethod(cateIdStr.value);
  if (success) {
    ElMessage.success(message);
    close();
  } else {
    ElMessage.error(message);
  }
  loading.value = false
}

watch(() => props.visible, (n) => {
  if (n) {
    cateId.value = props.id;
  }
})

</script>