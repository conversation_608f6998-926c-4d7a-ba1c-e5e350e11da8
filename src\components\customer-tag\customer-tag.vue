<template>
  <slot v-if="custom && tagName" :tagName="tagName"></slot>
  <div v-else-if="tagName" class="bg-blue-linear px-12px py-6px text-12px leading-12px text-white rounded-4px mr-5px mb-5px">
    {{ tagName }}
  </div>
</template>
<script setup>
import { computed, onMounted } from "vue";
import { configStore } from "@/store/config";
import { storeToRefs } from "pinia";
const { getGroupTag } = configStore();
const { tags } = storeToRefs(configStore());
const props = defineProps({
  custom: { type: Boolean, default: false },
  tagId: { type: [Number, String] },
});
const tagArr = computed(() => {
  const tagGroup = Array.isArray(tags.value) ? tags.value : [];
  return tagGroup.reduce((acc, item) => {
    const groupTags = item && Array.isArray(item.tag) ? [...item.tag] : [];
    return [...acc, ...groupTags];
  }, []);
});
const tagName = computed(() => {
  const tag = tagArr.value.find((i) => props.tagId && i.id === props.tagId);
  return tag && tag.name ? tag.name : "";
});
onMounted(() => tags.value.length === 0 && getGroupTag());
</script>
<style lang="scss" scoped>
.bg-blue-linear {
  background-image: linear-gradient(180deg, #4391f5 60%, #70c4e8);
}
</style>
