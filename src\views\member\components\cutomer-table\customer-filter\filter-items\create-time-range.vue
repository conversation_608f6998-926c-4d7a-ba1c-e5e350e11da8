<template>
  <filter-item type="base" label="建档时间" :text="text" :width="320" @onClick="show()">
    <date-range v-model="createTime" v-model:text="text" v-model:visible="visible" />
  </filter-item>
</template>
<script setup>
import { computed, ref, onMounted } from 'vue';
import dayjs from 'dayjs'
import dateRange from './date-range.vue'
import FilterItem from './filter-item.vue';

const createTime = ref([]);
const visible = ref(false)
const text = ref('');
const props = defineProps({
  initValue: {
    type: Array,
    default: () => []
  }
})

onMounted(() => {
  const [start, end] = (Array.isArray(props.initValue) ? props.initValue : []);
  if( start && dayjs(start).isValid()) {
    createTime.value[0] = start
  }
  if( end && dayjs(end).isValid()) {
    createTime.value[1] = end
  }
  text.value = `${createTime.value[0] || ''} - ${createTime.value[1] || ''}`
})

function show() {
  visible.value = true
}

function reset() { createTime.value = [] }

function getParam() {
  const params = {};
  if (createTime.value[0] && dayjs(createTime.value[0]).isValid()) {
    params.startCreateTime = createTime.value[0];
  }
  if (createTime.value[1] && dayjs(createTime.value[1]).isValid()) {
    params.endCreateTime = createTime.value[1];
  }
  return params
}

defineExpose({
  getParam,
  reset
})
</script>
<style lang="scss" scoped></style>
