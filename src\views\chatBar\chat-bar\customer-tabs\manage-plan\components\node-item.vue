<template>
  <div class="flex items-center justify-between pt-14px pb-10px">
    <div class="text-14px font-semibold">
      {{ task.key }}
    </div>
    <div class="flex items-center text-blue-500 cursor-pointer" @click="expand = !expand">
      <div>{{ expand ? "收起" : "展开" }}</div>
      <el-icon :size="18" class="ml-4px transform -translate-y-1px">
        <ArrowUp v-if="expand" />
        <ArrowDown v-else />
      </el-icon>
    </div>
  </div>
  <div :class="expand ? '' : 'hidden'">
    <div v-for="(item, idx) in task.list" class="text-14px bg-white shadow-xl rounded-8px"
      :class="idx > 0 ? 'mt-10px' : ''">
      <div v-if="item.eventType && ToDoEventType[item.eventType]" class="px-10px pt-10px">
        <div class="mb-10px text-gray-500">待办类型：</div>
        <div class="pb-10px leading-tight truncate">{{ item.eventType && ToDoEventType[item.eventType] }}</div>
        <div class="border-b border-gray-200"></div>
      </div>
      <div class="px-10px pt-10px">
        <div class="mb-10px text-gray-500">任务内容：</div>
        <div class="pb-10px leading-tight truncate">{{ item.taskContent }}</div>
        <div class="border-b border-gray-200"></div>
      </div>
      <div class="p-10px">
        <div class="text-gray-500">向客户发送：</div>
        <div v-if="item.sendContent" class="mt-10px">
          <text class="text-gray-500" style="margin-left: -8px">【提醒】</text>
          <text class="leading-tight">{{ item.sendContent }}</text>
        </div>
        <div
          v-if="!item.sendContent && (!item.pannedEventSendFile || (item.pannedEventSendFile && Object.keys(item.pannedEventSendFile).length === 0))"
          class="mt-10px">暂无发送内容</div>
        <div class="mt-10px" v-if="item.pannedEventSendFile && Object.keys(item.pannedEventSendFile).length !== 0">
          <span grew-color style="margin-left: -8px">【{{ item.pannedEventSendFile.type === "article" ? "文章" : "问卷"
            }}】</span>
          <span class="pointer">
            {{ item.pannedEventSendFile.name }}
          </span>
        </div>
      </div>
      <div v-if="item.events && item.events.length > 0" class="flex justify-end items-center border-t border-gray-200">
        <div class="pr-10px" v-if="item.events[0].eventStatus !== 'untreated'">
          {{ dayjs(item.events[0].endTime).format("YYYY-MM-DD HH:mm") }}
        </div>
        <div class="text-right p-10px text-dark-100">
          {{ PlanTaskStatusObj[item.events[0].eventStatus] }}
        </div>
      </div>
      <div v-else class="flex justify-end p-10px border-t border-gray-200">
        <div v-if="item.taskStatus === 'closed'">
          <span v-if="planForm.endTime">
            {{ dayjs(planForm.endTime).format("YYYY-MM-DD HH:mm") }}
          </span>
          已取消
        </div>
        <div v-else class="flex border-gray-200">
          <div class="text-blue-500 mr-15px pointer" @click="editTask(item)">编辑</div>
          <div class="text-red-500 pointer" @click="remove(item)">删除</div>
        </div>
      </div>
    </div>
  </div>

  <el-dialog :model-value="visible" :width="width" title="选择日期" @close="close">
    <div class="py-12px" color-666>
      <el-date-picker v-model="pickerDate" style="width: 100%" placeholder="请选择日期" />
    </div>
    <template #footer>
      <div text-center>
        <el-button class="w-100px" @click="close">取消</el-button>
        <el-button class="w-100px" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref } from "vue";
import dayjs from "dayjs";
import useModal from "@/hooks/useModal";
import { storeToRefs } from "pinia";
import { ElMessage, ElMessageBox } from "element-plus";
import { PlanTaskStatusObj, ToDoEventType } from "@/baseData";
import { taskStore } from "@/store/managePlan/task";
import { memberMangePlanStore } from "@/store/managePlan/memberMangePlan";
const { planForm, taskList } = storeToRefs(memberMangePlanStore());
const { taskForm } = storeToRefs(taskStore());
const { removeTask } = taskStore();
const props = defineProps({
  planExecutionTime: { default: "" },
  task: { default: [] },
});
const emits = defineEmits(["add", "remove", "edit"]);

const date = computed(() => {
  const days = 1;
  const startDate = dayjs(props.startDate);
  return props.startDate && startDate.isValid() ? startDate.add(days, "day").format("YYYY-MM-DD") : "";
});

function add() {
  emits("add");
}
async function remove(item) {
  await ElMessageBox.confirm("确定删除吗？", "提示", { type: "warning" });
  if (!planForm.value._id) {
    let index = taskList.value.findIndex((i) => i.taskId === item.taskId);
    taskList.value.splice(index, 1);
  } else {
    removeTask(item._id, planForm.value._id);
  }
}
function editTask(item) {
  taskForm.value = item;
  emits("edit");
}
const pickerDate = ref("");
const { close, show, visible, width } = useModal(); //  选择客户弹窗

function confirm() {
  if (pickerDate.value && dayjs(pickerDate.value).isValid()) {
    close();
  } else {
    ElMessage.info("请选择日期");
  }
}

const expand = ref(true);
</script>
<style lang="scss" scoped></style>
