<template>
  <popover-filter :clearable="modelValue && modelValue.length" :label="label" :text="text" :width="width" @clear="clear">
    <el-checkbox v-if="list.length" v-model="allChecked" label="全部" />
    <el-checkbox-group v-if="list.length" v-model="modelValue">
      <div v-for="item in list" :key="item.value">
        <el-checkbox :label="item.value" :value="item.value">
          <slot name="item" :item="item">{{ item.label }}</slot>
        </el-checkbox>
      </div>
    </el-checkbox-group>
  </popover-filter>
</template>
<script setup>
import { computed } from 'vue';
import { useVModels } from '@vueuse/core';

import PopoverFilter from './popover-filter-item.vue';

const emits = defineEmits(['update:modelValue']);
const props = defineProps({
  list: { type: Array, default: () => [] },
  label: { type: String, default: '' },
  text: { type: String, default: '' },
  width: { type: [Number, String], default: 280 },
  modelValue: { type: Array, default: () => [] }
})
const { modelValue } = useVModels(props, emits);

const clear = () => modelValue.value = []
const allChecked = computed({
  get() {
    return modelValue.value.length === props.list.length && props.list.length > 0
  },
  set(value) {
    if (value) {
      modelValue.value = props.list.map(item => item.value)
    } else {
      modelValue.value = []
    }
  }
})
const text = computed(() => props.text || (props.modelValue.length ? `已选择${props.modelValue.length}项` : ''))
</script>
<style lang="scss" scoped></style>
