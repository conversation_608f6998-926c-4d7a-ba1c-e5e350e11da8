<template>
  <div v-if="currentPage" class="h-screen w-screen fixed inset-0 z-99 bg-white">
    <div class="flex flex-col h-full">
      <div class="flex-shrink-0 flex items-center justify-between px-20px py-12px border-b border-gray-100"
        common-shadow>
        <el-icon class="flex-shrink-0 cursor-pointer" :size="16" @click="close">
          <CloseBold />
        </el-icon>
        <div class="mx-20px w-0 flex-grow text-center truncate text-16px font-semibold">
          {{ currentPage.title }}
        </div>
        <el-icon class="opacity-0" :size="16">
          <CloseBold />
        </el-icon>
      </div>
      <div class="flex-grow relative">
        <div class="absolute inset-0">
          <div v-for="item in pageStack" :key="item.pageKey"
            :style="item.pageKey == currentPage.pageKey ? '' : 'display:none'" class="h-full">
            <component :is="item.component" v-bind="item.props || {}" @addNewPlan="addNewPlan()" :team="team"
              :customer="customer" @cancel="cancel" @editPlanNote="editPlanNote" @selectPlan="selectPlan"
              @selectFile="selectFile" @complete="complete" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { computed, defineAsyncComponent, ref } from "vue";
import { useThrottleFn } from "@vueuse/core";
const props = defineProps({
  customer: { type: Object, default: () => ({}) },
  team: { type: Object, default: () => ({}) },
});
const planList = defineAsyncComponent(() => import("./plan-list.vue"));
const editPlan = defineAsyncComponent(() => import("./edit-plan.vue"));
const newPlan = defineAsyncComponent(() => import("./new-plan.vue"));
const editNode = defineAsyncComponent(() => import("./edit-node.vue"));
const articleList = defineAsyncComponent(() => import("./article-list.vue"));
const surveryList = defineAsyncComponent(() => import("./survery-list.vue"));
const emits = defineEmits(["close"]);
const pageStack = ref([]);
const currentPage = computed(() => pageStack.value[pageStack.value.length - 1]);
const cancel = useThrottleFn(popStack, 500);
const push = useThrottleFn(addStack, 500);

function addPlan() {
  push({ pageKey: "plan-list", title: "选择回访计划", component: planList });
}
// 新增回访计划
function addNewPlan() {
  push({ pageKey: "new plan", title: "新增回访计划", component: newPlan });
}

// 编辑回访计划节点
function editPlanNote(operationType) {
  push({ pageKey: "edit plan node", title: operationType == "edit" ? "编辑任务内容" : "新增任务项", component: editNode, props: { operationType } });
}
// 选择回访计划附件
async function selectFile({ type, eventName }) {
  if (type === "article") {
    push({ pageKey: "article list", title: "文章列表", component: articleList, props: { eventName } });
  } else if (type === "survery") {
    push({ pageKey: "survery list", title: "问卷列表", component: surveryList, props: { eventName } });
  }
}
// 编辑回访计划
function selectPlan(type) {
  push({ pageKey: "edit", title: type === "add" ? "新增回访计划" : "编辑回访计划", component: editPlan });
}
// 关闭当前页面
function close() {
  emits("close");
}
function addStack(page) {
  pageStack.value = [...pageStack.value, page];
}
function popStack() {
  if (pageStack.value.length > 1) {
    pageStack.value.pop();
  } else {
    close();
  }
}
function complete() {
  pageStack.value = [];
}
function addTask(operationType) {
  pageStack.value = [{ pageKey: "edit plan node", title: operationType == "edit" ? "编辑任务内容" : "新增任务项", component: editNode, props: { operationType } }];
}

defineExpose({ addTask, addPlan });
</script>
