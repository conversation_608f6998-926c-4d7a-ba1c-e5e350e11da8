import { computed, ref } from "vue";
import { watchDebounced } from "@vueuse/core";
import dayjs from "dayjs";
import { getLastServiceTime } from "@/api/todo";

export default function useService(customer, teams) {
  const serviceTimes = ref([]);

  const lastServiceTime = ref("--");

  const teammateIds = computed(() => {
    return teams.value
      .filter((i) => Array.isArray(i.memberList) && i.memberList.includes(localStorage.getItem("userId")))
      .map((i) => i.memberList)
      .reduce((list, ids) => [...list, ...ids], []);
  });
  const corpUserId = computed(() => {
    const list = customer.value && Array.isArray(customer.value.personResponsible) ? customer.value.personResponsible : [];
    const { uniqueIds } = list.reduce(
      (val, id) => {
        if (!val.m[id] && id) {
          val.uniqueIds.push(id);
          val.m[id] = id;
        }
        return val;
      },
      { uniqueIds: [], m: {} }
    );
    // filter函数作用: 非所属团队的成员id过滤, 非所属团队的负责人不可见
    // sort函数作用:  当前员工排首位
    return uniqueIds.filter((i) => teammateIds.value.includes(i)).sort((a) => (a === localStorage.getItem("userId") ? -1 : 0));
  });
  async function getServiceTimes() {
    if (customer.value && customer.value._id) {
      const res = await getLastServiceTime(customer.value._id);
      const list = res && Array.isArray(res.data.data) ? res.data.data : [];
      serviceTimes.value = corpUserId.value.map((id) => {
        const item = list.find((i) => i.id === id);
        const executionTime = item && item.executionTime ? dayjs(item.executionTime).format("YYYY-MM-DD HH:mm") : "";
        return { id, executionTime };
      });
      const time = list.reduce((max, item) => {
        if (item.executionTime > max) return item.executionTime;
        return max;
      }, 0);
      lastServiceTime.value = time > 0 ? dayjs(time).format("YYYY-MM-DD HH:mm") : "--";
    } else {
      serviceTimes.value = [];
      lastServiceTime.value = "--";
    }
  }

  watchDebounced(
    () => (customer.value ? customer.value._id : ""),
    (n, o = {}) => {
      if (n && n !== o) getServiceTimes();
    },
    { immediate: true, debounce: 500, maxWait: 1000 }
  );

  return { lastServiceTime, serviceTimes };
}
