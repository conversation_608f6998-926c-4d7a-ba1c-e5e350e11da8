<template>
  <el-date-picker v-model="modelValue" class="create-time-range" popper-class="create-time-range__popper"
    ref="dateRangeRef" type="daterange"  :unlink-panels="true"
 value-format="YYYY-MM-DD" :popper-options="option" :shortcuts="shortcuts"
    @visible-change="handleVisible" />
</template>
<script setup>
import { ref, watch } from 'vue';
import { useVModels } from '@vueuse/core';
import dayjs from 'dayjs';

const props = defineProps({
  visible: { type: Boolean, default: false },
  modelValue: { type: Array, default: () => [] },
  text: { type: String, default: '' }
})
const emits = defineEmits(['update:modelValue', 'update:visible', 'update:text'])
const dateRangeRef = ref()
const { modelValue, text, visible } = useVModels(props, emits);
const option = {
  modifiers: [{ name: 'flip', options: { fallbackPlacements: ['bottom'], allowedAutoPlacements: ['bottom'] } }]
}

function handleVisible(show) {
  if (show !== visible.value) {
    visible.value = show
  }
}

watch(visible, n => {
  if (dateRangeRef.value) {
    if (n) dateRangeRef.value.handleOpen()
    else dateRangeRef.value.handleClose()
  }
})

watch(modelValue, val => {
  text.value = val.join(' ~ ')
})

const shortcuts = [
  {
    text: '全部',
    value() {
      modelValue.value = [];
      dateRangeRef.value && dateRangeRef.value.handleClose()
    }
  },
  {
    text: '今天',
    value() {
      return [dayjs().toDate(), dayjs().toDate()]
    }
  },
  {
    text: '昨天',
    value() {
      return [dayjs().subtract(1, 'day').toDate(), dayjs().subtract(1, 'day').toDate()]
    }
  },
  {
    text: '本周',
    value: () => {
      const start = dayjs().startOf('week').toDate()
      const end = dayjs().toDate()
      return [start, end]
    }
  },
  {
    text: '上周',
    value: () => {
      const start = dayjs().subtract(1, 'week').startOf('week').toDate()
      const end = dayjs().subtract(1, 'week').endOf('week').toDate()
      return [start, end]
    },
  },
  {
    text: '本月',
    value: () => {
      const start = dayjs().startOf('month').toDate()
      const end = dayjs().toDate()
      return [start, end]
    }
  },
  {
    text: '上月',
    value: () => {
      const start = dayjs().subtract(1, 'month').startOf('month').toDate()
      const end = dayjs().subtract(1, 'month').endOf('month').toDate()
      return [start, end]
    },
  }
]

</script>
<style lang="scss">
.create-time-range.el-date-editor--daterange {
  position: absolute;
  left: 0;
  right: 0;
  top: 100%;
  height: 0;
  opacity: 0;
  overflow: hidden;
}

.create-time-range__popper .el-picker-panel__sidebar {
  width: 80px;
}

.create-time-range__popper .el-picker-panel__shortcut {
  text-align: center;
}
</style>
