<template>
  <el-dialog
    :model-value="visible"
    :title="getDialogTitle()"
    :width="600"
    @close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      label-position="left"
      v-loading="editLoading"
      element-loading-text="正在加载账号信息..."
    >
      <!-- 账号ID -->
      <el-form-item label="账号ID:" prop="userid">
        <el-input
          v-model="form.userid"
          :disabled="isEdit"
          :placeholder="isEdit ? '' : '点击选择企业微信通讯录'"
          :readonly="!isEdit"
          @click="!isEdit && openWechatQuery()"
        >
          <template #suffix v-if="!isEdit">
            <el-icon class="text-gray-400 cursor-pointer">
              <User />
            </el-icon>
          </template>
        </el-input>
      </el-form-item>

      <!-- 账号名称 -->
      <el-form-item label="账号名称:" prop="anotherName">
        <el-input
          v-model="form.anotherName"
          placeholder="请搜索或下拉选择账号使用人员的信息"
          class="w-full"
          readonly
          @click="openStaffSelectModal"
          style="color:black;"
        >
          <template #suffix>
            <el-icon class="text-gray-400 cursor-pointer">
              <ArrowRight />
            </el-icon>
          </template>
        </el-input>
      </el-form-item>

      <!-- 所属团队 -->
      <el-form-item label="所属团队:">
        <div class="w-full">
          <div
            class="team-selector border border-gray-200 rounded px-12px py-8px min-h-32px cursor-pointer hover:border-gray-300"
            @click="openTeamSelector"
          >
            <div v-if="selectedTeams.length === 0" class="text-gray-400">
              请选择该员工所属团队
            </div>
            <div v-else class="team-display">
              {{ formatTeamDisplay(selectedTeams) }}
            </div>
          </div>
        </div>
      </el-form-item>

      <!-- 平台角色 -->
      <el-form-item label="平台角色:">
        <div class="w-full">
          <div
            class="role-selector border border-gray-200 rounded px-12px py-8px min-h-32px cursor-pointer hover:border-gray-300"
            @click="openRoleSelector"
          >
            <div v-if="selectedRoles.length === 0" class="text-gray-400">
              请选择平台角色
            </div>
            <div v-else class="flex flex-wrap gap-5px">
              <el-button
                v-for="role in selectedRoles"
                :key="role._id"
                type="primary"
                size="small"
                class="mb-5px"
              >
                {{ role.roleName }}
              </el-button>
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="text-center">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleConfirm"
        >
          {{ getConfirmButtonText() }}
        </el-button>
      </div>
    </template>

    <!-- 团队选择弹窗 -->
    <belong-team 
      :visible="teamVisible" 
      @close="teamVisible = false" 
      :belongTeamList="teamList" 
      :userId="form.userid" 
      @addTeam="onAddTeam"
    />

    <!-- 角色选择弹窗 -->
    <staff-role
      :visible="roleVisible"
      @close="roleVisible = false"
      :roleIds="form.roleIds"
      @addRole="onAddRole"
      :member="currentMember"
    />

    <!-- 员工选择弹窗 -->
    <staff-select-modal
      :visible="staffSelectVisible"
      :current-row="{ accountName: form.anotherName }"
      @close="staffSelectVisible = false"
      @select="handleStaffSelect"
    />


  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, ArrowRight } from '@element-plus/icons-vue'
import BelongTeam from './belong-team.vue'
import StaffRole from './staff-role.vue'
import StaffSelectModal from './staff-select-modal.vue'
import { addCorpMember, updateCorpMember } from '@/api/corp'
import { selectEnterpriseContact } from '@/utils/jssdk'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  corpMemberAndcustomtorCount: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  },
  editData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['close', 'success'])

// 表单数据
const form = ref({
  userid: '',
  anotherName: '',
  teamIds: [],
  roleIds: []
})

// 表单验证规则 - 移除必填限制
const rules = {
  // 不设置必填验证，允许用户自由填写
}

// 状态管理
const formRef = ref()
const loading = ref(false)
const editLoading = ref(false) // 编辑数据加载状态
const teamVisible = ref(false)
const roleVisible = ref(false)
const teamList = ref([])
const roleList = ref([])

// 员工选择弹窗状态
const staffSelectVisible = ref(false)

// 计算属性
const selectedTeams = computed(() => {
  return teamList.value.filter(team => team.isBelongTeam)
})

const selectedRoles = computed(() => {
  if (!roleList.value.length || !form.value.roleIds.length) return []
  return roleList.value.filter(role => form.value.roleIds.includes(role._id))
})

const currentMember = computed(() => ({
  userid: form.value.userid,
  roleType: 'member'
}))

const isTransfer = computed(() => {
  return props.editData && props.editData.isTransfer
})

// 获取弹窗标题
const getDialogTitle = () => {
  if (isTransfer.value) {
    return '账号转让'
  } else if (props.isEdit) {
    return '账号编辑'
  } else {
    return '账号开通'
  }
}

// 获取确认按钮文本
const getConfirmButtonText = () => {
  if (isTransfer.value) {
    return '确认转让'
  } else if (props.isEdit) {
    return '保存'
  } else {
    return '确认开通'
  }
}

// 监听弹窗显示状态
watch(() => props.visible, async (newVal) => {
  if (newVal) {
    if (props.isEdit && props.editData) {
      // 编辑模式，填充数据
      await loadEditData()
    } else {
      resetForm()
    }
  }
})

// 加载编辑数据
const loadEditData = async () => {
  try {
    // 显示加载状态
    editLoading.value = true
    // 基本信息回填
    form.value = {
      userid: props.editData.accountId || '',
      anotherName: props.editData.accountName || '',
      teamIds: [],
      roleIds: []
    }

    const originalData = props.editData.originalData
    if (!originalData) {
      console.warn('缺少原始数据，无法完整回填')
      ElMessage.warning('账号数据不完整，部分信息可能无法显示')
      return
    }

    console.log('原始账号数据:', originalData)

    // 加载团队数据并设置选中状态
    await loadTeamDataForEdit(originalData.userid)

    // 加载角色数据并设置选中状态
    await loadRoleDataForEdit(originalData)

  } catch (error) {
    console.error('加载编辑数据失败:', error)
    ElMessage.error('加载账号信息失败，请重试')
  } finally {
    // 隐藏加载状态
    editLoading.value = false
  }
}

// 加载团队数据用于编辑
const loadTeamDataForEdit = async (userid) => {
  try {
    // 导入团队API
    const { getCorpTeams } = await import('@/api/corp.js')
    const corpId = localStorage.getItem('corpId')

    if (!corpId) {
      console.warn('未获取到企业ID，跳过团队数据加载')
      return
    }

    // 添加超时处理
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('团队数据加载超时')), 15000) // 15秒超时
    })

    const apiPromise = getCorpTeams(1, 999, corpId)

    const { data, success } = await Promise.race([apiPromise, timeoutPromise])

    if (success && data.list) {
      // 设置团队数据并标记用户所属的团队
      teamList.value = data.list.map(team => ({
        ...team,
        memberList: Array.isArray(team.memberList) ? team.memberList.filter(Boolean) : [],
        memberLeaderList: Array.isArray(team.memberLeaderList) ? team.memberLeaderList.filter(Boolean) : [],
        isBelongTeam: team.memberList && team.memberList.includes(userid),
        isTeamLeader: team.memberLeaderList && team.memberLeaderList.includes(userid)
      }))

      // 更新表单中的团队ID
      updateTeamIds()

      console.log('团队数据回填完成:', teamList.value.filter(t => t.isBelongTeam))
    } else {
      console.warn('团队数据加载失败:', data)
      teamList.value = []
    }
  } catch (error) {
    console.error('加载团队数据失败:', error)
    teamList.value = []

    // 如果是超时错误，给用户友好提示
    if (error.message && error.message.includes('超时')) {
      ElMessage.warning('团队数据加载超时，请稍后重试')
    }
  }
}

// 加载角色数据用于编辑
const loadRoleDataForEdit = async (originalData) => {
  try {
    // 导入角色API
    const { getRolesList } = await import('@/api/corp.js')

    // 添加超时处理
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('角色数据加载超时')), 15000) // 15秒超时
    })

    const apiPromise = getRolesList()

    const { data, success } = await Promise.race([apiPromise, timeoutPromise])

    if (success && data.data) {
      roleList.value = data.data

      // 根据原始数据设置角色
      if (originalData.roleType) {
        // 如果有roleType，查找对应的角色
        const matchedRole = roleList.value.find(role => role.roleId === originalData.roleType)
        if (matchedRole) {
          form.value.roleIds = [matchedRole._id]
        }
      } else if (originalData.roleIds && Array.isArray(originalData.roleIds)) {
        // 如果有roleIds数组，直接使用
        form.value.roleIds = originalData.roleIds
      }

      console.log('角色数据回填完成:', form.value.roleIds, selectedRoles.value)
    } else {
      console.warn('角色数据加载失败:', data)
      roleList.value = []
    }
  } catch (error) {
    console.error('加载角色数据失败:', error)
    roleList.value = []

    // 如果是超时错误，给用户友好提示
    if (error.message && error.message.includes('超时')) {
      ElMessage.warning('角色数据加载超时，请稍后重试')
    }
  }
}

// 格式化团队显示
const formatTeamDisplay = (teams) => {
  if (!teams || teams.length === 0) return ''

  return teams.map(team => {
    let display = team.name
    if (team.isTeamLeader) {
      display += ' 负责人'
    }
    return display
  }).join('、')
}

// 加载团队数据
const loadTeamData = async () => {
  try {
    // 导入团队API
    const { getCorpTeams } = await import('@/api/corp.js')
    const corpId = localStorage.getItem('corpId')

    if (!corpId) {
      console.warn('未获取到企业ID，跳过团队数据加载')
      return
    }

    // 添加超时处理
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('团队数据加载超时')), 10000) // 10秒超时
    })

    const apiPromise = getCorpTeams(1, 999, corpId)

    const { data, success } = await Promise.race([apiPromise, timeoutPromise])

    if (success && data.list) {
      teamList.value = data.list.map(team => ({
        ...team,
        memberList: Array.isArray(team.memberList) ? team.memberList.filter(Boolean) : [],
        isBelongTeam: false,
        isTeamLeader: false
      }))
      console.log('团队数据加载成功:', teamList.value.length)
    } else {
      console.warn('团队数据加载失败:', data)
      teamList.value = []
    }
  } catch (error) {
    console.error('加载团队数据失败:', error)
    teamList.value = []
    // 不显示错误提示，避免影响用户体验
  }
}

// 加载角色数据
const loadRoleData = async () => {
  try {
    // 导入角色API
    const { getRolesList } = await import('@/api/corp.js')

    // 添加超时处理
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('角色数据加载超时')), 10000) // 10秒超时
    })

    const apiPromise = getRolesList()

    const { data, success } = await Promise.race([apiPromise, timeoutPromise])

    if (success && data.data) {
      roleList.value = data.data
      console.log('角色数据加载成功:', roleList.value.length)
    } else {
      console.warn('角色数据加载失败:', data)
      roleList.value = []
    }
  } catch (error) {
    console.error('加载角色数据失败:', error)
    roleList.value = []
    // 不显示错误提示，避免影响用户体验
  }
}

// 重置表单
const resetForm = () => {
  form.value = {
    userid: '',
    anotherName: '',
    teamIds: [],
    roleIds: []
  }

  teamList.value = []
  roleList.value = []
  formRef.value?.clearValidate()

  // 异步加载数据，不阻塞界面
  Promise.all([
    loadTeamData(),
    loadRoleData()
  ]).catch(error => {
    console.error('数据加载失败:', error)
  })
}



// 打开团队选择器
const openTeamSelector = () => {
  teamVisible.value = true
}

// 打开团队列表页面
const openTeamList = () => {
  // 这里可以跳转到团队列表页面或打开新窗口
  ElMessage.info('跳转到团队列表页面')
}

// 打开角色选择器
const openRoleSelector = () => {
  roleVisible.value = true
}

// 打开角色列表页面
const openRoleList = () => {
  // 这里可以跳转到角色列表页面或打开新窗口
  ElMessage.info('跳转到角色列表页面')
}

// 移除团队
const removeTeam = (teamId) => {
  const teamIndex = teamList.value.findIndex(team => team.teamId === teamId)
  if (teamIndex !== -1) {
    teamList.value[teamIndex].isBelongTeam = false
    teamList.value[teamIndex].isTeamLeader = false
  }
  updateTeamIds()
}

// 移除角色
const removeRole = (roleId) => {
  form.value.roleIds = form.value.roleIds.filter(id => id !== roleId)
}

// 团队选择回调
const onAddTeam = (teams) => {
  teamList.value = teams
  teamVisible.value = false
  updateTeamIds()
}

// 角色选择回调
const onAddRole = (roleIds, roles) => {
  form.value.roleIds = roleIds
  roleList.value = roles
  roleVisible.value = false
}

// 更新团队ID
const updateTeamIds = () => {
  form.value.teamIds = teamList.value
    .filter(team => team.isBelongTeam)
    .map(team => team.teamId)
}

// 打开员工选择弹窗
const openStaffSelectModal = () => {
  staffSelectVisible.value = true
}

// 处理员工选择
const handleStaffSelect = (staff) => {
  form.value.anotherName = staff.name
  console.log('选择员工:', staff)
}

// 打开企微通讯录
const openWechatQuery = async () => {
  if (import.meta.env.VITE_NEED_LOGIN === "true") {
    try {
      const { value: userid } = await ElMessageBox.prompt("请输入userId", "手动输入", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        inputValidator: (value) => {
          if (!value) {
            return "请输入userId";
          }
          return true;
        },
      });
      form.value.userid = userid
    } catch (e) {
      console.log("用户取消输入");
    }
    return;
  }

  try {
    const res = await selectEnterpriseContact()
    const { selectedUserList } = res
    if (selectedUserList.length > 1) {
      ElMessage.error("只能选择一名员工!")
      return
    }
    const user = selectedUserList && selectedUserList[0]
    const userid = user && user.id
    if (!userid) {
      ElMessage.error("员工还未添加应用!")
      return
    }
    form.value.userid = userid
  } catch (error) {
    console.error('选择企业联系人失败:', error)
  }
}

// 关闭弹窗
const handleClose = () => {
  emit('close')
}

// 确认开通/保存/转让
const handleConfirm = async () => {
  try {
    loading.value = true

    if (isTransfer.value) {
      // 转让逻辑
      await handleTransfer()
    } else {
      // 开通/编辑逻辑
      await handleOpenOrEdit()
    }
  } catch (error) {
    console.error('操作失败:', error)
    const errorMessage = isTransfer.value ? '账号转让失败' :
                        (props.isEdit ? '账号编辑失败' : '账号开通失败')
    ElMessage.error(errorMessage)
  } finally {
    loading.value = false
  }
}

// 处理转让
const handleTransfer = async () => {
  // 准备转让数据
  const params = {
    userid: form.value.userid,
    anotherName: form.value.anotherName,
    roleIds: form.value.roleIds,
    corpId: localStorage.getItem('corpId'),
    transferFrom: props.editData.transferFrom // 被转让的账号信息
  }

  // 如果是开发环境，添加open_userid
  if (import.meta.env.VITE_NEED_LOGIN === "true" && !params.open_userid) {
    params.open_userid = form.value.userid
  }

  // 这里需要调用转让API，暂时使用addCorpMember
  const res = await addCorpMember(params)

  if (res.success) {
    ElMessage.success('账号转让成功!')
    emit('success', params.userid, 'transfer')
    handleClose()
  } else {
    ElMessage.error(res.message || '账号转让失败')
  }
}

// 处理开通/编辑
const handleOpenOrEdit = async () => {
  // 准备提交数据
  const params = {
    userid: form.value.userid,
    anotherName: form.value.anotherName,
    roleIds: form.value.roleIds,
    corpId: localStorage.getItem('corpId')
  }

  // 如果是开发环境，添加open_userid
  if (import.meta.env.VITE_NEED_LOGIN === "true" && !params.open_userid) {
    params.open_userid = form.value.userid
  }

  let res
  if (props.isEdit && props.editData && props.editData.originalData) {
    // 编辑模式：使用updateCorpMember
    res = await updateCorpMember(props.editData.originalData._id, params)
  } else {
    // 新增模式：使用addCorpMember
    res = await addCorpMember(params)
  }

  if (res.success) {
    ElMessage.success(props.isEdit ? '账号编辑成功!' : '账号开通成功!')
    emit('success', params.userid)
    handleClose()
  } else {
    ElMessage.error(res.message || (props.isEdit ? '账号编辑失败' : '账号开通失败'))
  }
}
</script>

<style scoped>
.team-selector,
.role-selector {
  transition: border-color 0.3s;
}

.team-selector:hover,
.role-selector:hover {
  border-color: #409eff;
}

.is-required .el-form-item__label::before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

/* 员工选择下拉框样式 */
.el-select {
  width: 100%;
}

.el-option .flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
