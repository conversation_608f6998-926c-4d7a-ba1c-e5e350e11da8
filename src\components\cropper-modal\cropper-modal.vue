<template>
  <el-dialog :model-value="visible" :title="title" :width="560" @close="close()">
    <div class="flex">
      <div class="mr-20px h-240px w-240px flex-shrink-0">
        <vue-cropper v-if="visible" ref="cropperRef" :img="option.img" :output-size="option.size" :output-type="option.outputType" :info="true" :full="option.full" :fixed="fixed" :fixed-number="fixedNumber" :can-move="option.canMove" :can-move-box="option.canMoveBox" :fixed-box="option.fixedBox" :original="option.original" :auto-crop="option.autoCrop" :center-box="option.centerBox" @real-time="realTime" :high="option.high" @img-load="imgLoad" :max-img-size="option.max" @crop-moving="cropMoving" mode="cover"></vue-cropper>
      </div>
      <div class="flex flex-col justify-between">
        <div class="show-preview min-h-1" :style="{ width: previews.w + 'px', height: previews.h + 'px', overflow: 'hidden', margin: '5px' }">
          <div :style="previews.div">
            <img :src="previews.url" style="max-width: none" :style="previews.img" />
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div flex items-center justify-center>
        <el-button class="w-100px" @click="close">取消</el-button>
        <div class="mx-10px">
          <el-upload action="#" accept="image/*" :show-file-list="false" list-type="picture" :auto-upload="false" @change="changeFile">
            <template #trigger>
              <el-button plain class="w-100px" type="primary" :icon="UploadFilled">上传</el-button>
            </template>
          </el-upload>
        </div>
        <el-button class="w-100px" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref, watch } from "vue";
import { ElMessage, ElLoading } from "element-plus";
import { updateFile } from "@/api/uploadFIle.js";

import { UploadFilled } from "@element-plus/icons-vue";
import { VueCropper } from "vue-cropper";
import "vue-cropper/dist/index.css";

const emits = defineEmits(["close", "confirm", "remove"]);
const props = defineProps({
  handle: { type: Function, default: () => {} },
  src: { type: String, default: "" },
  title: { type: String, default: "" },
  visible: { type: Boolean, default: false },
  width: { type: Number },
});

const option = ref({
  img: "",
  size: 1,
  full: false,
  outputType: "png",
  canMove: true,
  fixedBox: false,
  original: false,
  canMoveBox: true,
  autoCrop: true,
  fixed: true,
  fixedNumber: [1, 1],
  centerBox: true,
  high: true,
  max: 99999,
});

const previews = ref({});

function realTime(data) {
  previews.value = data;
}

function imgLoad(msg) {
  // console.log(msg);
}

function close() {
  emits("close");
}

const cropperRef = ref();
const loading = ref(false);
function confirm() {
  if (loading.value) return;
  cropperRef.value.getCropBlob(async (data) => {
    loading.value = true;
    const file = new File([data], `${localStorage.getItem("corpId")}_logo_${new Date().getTime()}.png`);
    const url = await upload(file);
    if (url) {
      try {
        if (typeof props.handle === "function") {
          await props.handle(url, data);
        }
        close();
      } catch (e) {
        console.log("剪切出错");
        console.log(e);
      }
      loading.value = false;
    }
  });
}

async function upload(file) {
  const loadingInstance = ElLoading.service({ fullscreen: true, text: "正在上传" });
  const res = await updateFile(`${file.name}`, file, localStorage.getItem("corpId"));
  if (res) {
    loadingInstance.close();
    return res.download_url;
  } else {
    loadingInstance.close();
    ElMessage.error(`图片上传失败`);
    return false;
  }
}

async function changeFile(uploadFile) {
  const { raw: file } = uploadFile; // blobUrl仅仅本地预览
  const reader = new FileReader();
  reader.onload = (e) => {
    let data;
    if (!e || !e.target) return;
    if (typeof e.target.result === "object") {
      // 把Array Buffer转化为blob 如果是base64不需要
      data = window.URL.createObjectURL(new Blob([e.target.result]));
    } else {
      data = e.target.result;
    }
    option.value.img = data;
  };
  reader.readAsArrayBuffer(file);
}

watch(
  () => props.visible,
  (n) => {
    if (n) {
      option.value.img = props.src || "";
    }
  }
);
</script>