<template>
  <el-tabs v-if="tabs.length" :model-value="currentTab" class="-mb-15px" @tab-change="changeTab">
    <el-tab-pane v-for="tab in tabs" :value="tab.name" :name="tab.name">
      <template #label>
        <span class="px-15px">{{ tab.label }}</span>
      </template>
    </el-tab-pane>
  </el-tabs>
  <div class="flex justify-between px-15px">
    <div class="flex flex-wrap">
      <component v-for="({ component, name, key }, index) in currentItems" :is="component" :init-value="key && initValue && initValue[key]" :key="name" :ref="(el) => (filterItemRef[index] = el)" />
    </div>
    <div class="flex-shrink-0 flex items-center h-40px pl-15px">
      <el-button type="primary" @click="search()">搜索</el-button>
      <el-button @click="reset()">重置</el-button>
    </div>
  </div>
</template>
<script setup>
import { computed, nextTick, ref } from "vue";
import ageRange from "./filter-items/age-range.vue";
import arriveTimeRange from "./filter-items/arrive-time-range.vue";
import createTimeRange from "./filter-items/create-time-range.vue";
import customerSource from "./filter-items/customer-source.vue";
import customerTeam from "./filter-items/customer-team.vue";
import customDaysRange from "./filter-items/custom-days-range.vue";
import currentStage from "./filter-items/current-stage.vue";
import customerTags from "./filter-items/customer-tags.vue";
import groupRadio from "./filter-items/group-radio.vue";
import serviceTimeRange from "./filter-items/service-time-range.vue";
import sexGroup from "./filter-items/sex-radio.vue";
import teammatePicker from "./filter-items/teammate-picker.vue";
import arriveStatusRange from "./filter-items/arrive-status-range.vue";

const layoutComponents = {
  age: { component: ageRange, name: "age-range" },
  arriveTimeRange: { component: arriveTimeRange, name: "arrive-time-range" },
  arriveStatusRange: { component: arriveStatusRange, name: "arrive-status-range" },
  createTimeRange: { component: createTimeRange, name: "create-time-range", key: "createTime" },
  customerSource: { component: customerSource, name: "customer-source" },
  customerTeam: { component: customerTeam, name: "customer-team" },
  // currentStage: { component: currentStage, name: "current-stage" },
  customDaysRange: { component: customDaysRange, name: "custom-days-range" },
  customerTags: { component: customerTags, name: "customer-tags" },
  group: { component: groupRadio, name: "group-radio" },
  serviceTimeRange: { component: serviceTimeRange, name: "service-time-range" },
  sex: { component: sexGroup, name: "sex-group" },
  teammatePicker: { component: teammatePicker, name: "teammate-picker" },
};

const emits = defineEmits(["search", "changeTab"]);
const filterItemRef = ref([]);
const props = defineProps({
  currentTab: { type: String, default: "" },
  initValue: { type: Object, default: () => ({}) },
  layout: { type: Array, default: () => [] },
  tabs: { type: Array, default: () => [] },
});

const currentItems = computed(() => props.layout.map((key) => layoutComponents[key]).filter(Boolean));

async function changeTab(name) {
  emits("changeTab", name);
  await nextTick();
  reset();
}

function search(reset = false) {
  emits("search", { reset, params: getParams() });
}
function reset() {
  filterItemRef.value.forEach((i) => i && typeof i.reset === "function" && i.reset());
  search(true);
}
function getParams() {
  let query = filterItemRef.value.reduce((acc, i) => {
    let param = i && typeof i.getParam === "function" ? i.getParam() : {};
    return { ...acc, ...param };
  }, {});
  return query;
}

defineExpose({
  search,
});
</script>
