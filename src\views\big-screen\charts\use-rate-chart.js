import { markRaw, ref, onMounted } from "vue";
import { watchDebounced, useWindowSize } from "@vueuse/core";
import echarts from "@/utils/echarts";

function getOptions(datas = [], color) {
  // let datas = [{ name: '市场活动', value: 4288 }, { name: '员工推荐', value: 1567 }, { name: '客户推荐', value: 2255 }, { name: '其他', value: 3676 }]

  const option = {
    grid: {
      top: 0, // 图表距离容器顶部的距离
      bottom: 0, // 图表距离容器底部的距离
      left: 0,
      right: 0,
    },
    tooltip: {
      trigger: "item",
      triggerOn: "click",
      formatter: "{b}: {c}人",
    },
    color,
    series: [
      {
        type: "pie",
        // roseType: 'area',
        // radius: ['15', '60'],
        avoidLabelOverlap: false,
        label: {
          show: false
        },

        labelLine: {
          show: false,
        },
        data: datas,
      },
    ],
  };
  return option;
}

export default function useRateChart(chartRef, data, colors) {
  const chart = ref(null);
  const { width, height } = useWindowSize();

  function setChart() {
    chart.value = markRaw(echarts.init(chartRef.value));
  }

  function paint(repaint = false) {
    if (chartRef.value && !chart.value) {
      setChart();
      repaint = false;
    }
    if (!chart.value) return;
    const option = getOptions(data.value, colors);
    chart.value.setOption(option, repaint);
  }
  watchDebounced(
    [width, height],
    () => {
      chart.value && chart.value.resize();
    },
    { debounce: 500, maxWait: 1500 }
  );
  return paint;
}
