import { teamStore } from "@/store/team";
import { post } from "./axios";
async function useCorp(data) {
  const res = await post("corp", data);
  return res;
}

export async function login(data) {
  const res = await useCorp({
    type: "login",
    corpId: import.meta.env.VITE_CORPID || localStorage.getItem("corpId"),
    ...data,
  });
  return res;
}

export async function updatePassword(data) {
  const res = await useCorp({
    type: "updatePassword",
    corpId: import.meta.env.VITE_CORPID || localStorage.getItem("corpId"),
    ...data,
  });
  return res;
}
