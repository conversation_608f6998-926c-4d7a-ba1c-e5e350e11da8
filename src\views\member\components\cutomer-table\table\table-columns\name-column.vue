<template>
  <el-table-column fixed="left" label="姓名" prop="name" :width="160">
    <template #default="{ row: { name, _id, createType } }">
      <div v-if="editId === _id" ref="editRef" class="flex items-center">
        <el-input v-model="current.name" :placeholder="placeholder" :maxlength="10" ref="inputRef" class="w-100px"></el-input>
        <el-icon class="ml-10px cursor-pointer text-gray-500" @click="editId = ''">
          <CloseBold />
        </el-icon>
        <el-icon v-if="loading" class="ml-10px animate-spin text-gray-500">
          <Refresh />
        </el-icon>
        <el-icon v-else class="ml-10px cursor-pointer text-green-500" @click="confirm()">
          <Select />
        </el-icon>
      </div>
      <div v-else class="flex align-center group">
        <el-popover placement="bottom-start" :width="300" :content="content">
          <template #reference>
            <img v-if="createType === 'auto'" src="@/assets/tixing-red.svg" alt="" class="w-16px h-16px pointer" />
          </template>
        </el-popover>
        <td-wrapper classnames="cursor-pointer text-blue-500 underline-offset-2 hover:underline ml-10px" @click="goDetail(_id)">
          {{ name }}
        </td-wrapper>
        <div class="flex-shrink-0 hidden translate-y-4px transform ml-5px text-blue-500 cursor-pointer group-hover:block" @click="edit(_id, name)">
          <el-icon size="16">
            <EditPen />
          </el-icon>
        </div>
      </div>
    </template>
  </el-table-column>
</template>



<script setup>
import { nextTick, ref, inject } from "vue";
import { onClickOutside } from "@vueuse/core";
import { ElMessage } from "element-plus";
import { updateMember } from "@/api/member";

import { useRouter } from "vue-router";
import tdWrapper from "./td-wrapper.vue";
const content = ref("该档案为系统自动建档，以客户微信昵称命名，请尽快更新客户档案");
const props = defineProps({ customerType: String });
const router = useRouter();
const emit = defineEmits(["to-customer-detail"]);

function goDetail(id) {
  emit("to-customer-detail", id);
}

const editId = ref(false);
const editRef = ref();
const inputRef = ref();
const placeholder = ref("");
const loading = ref(false);
const current = ref({ name: "", _id: "" });
onClickOutside(editRef, () => (editId.value = ""));
async function edit(_id, name) {
  current.value = { name, _id };
  placeholder.value = name;
  editId.value = _id;
  await nextTick();
  inputRef.value.focus();
}

const changeCustomData = inject("change-customer-table-row-data");
async function confirm() {
  if (loading.value) return;
  if (current.value.name.trim()) {
    loading.value = true;
    const { success, message } = await updateMember(current.value._id, { name: current.value.name.trim() });
    if (success) {
      editId.value = "";
      ElMessage.success("修改成功");
      if (typeof changeCustomData === "function") {
        changeCustomData({ _id: current.value._id, key: "name", value: current.value.name.trim() });
      }
    } else {
      ElMessage.error(message);
    }
    loading.value = false;
  }
}
</script>
<style lang="scss" scoped></style>