export default {
  install(app) {
    // 添加点击事件监听器到 document 上
    const clickHandler = function (event) {
      // 检查点击的元素是否有类名为 el-button
      let elButton = null;
      const target = event.target;
      const parentElement = target.parentElement;
      if (target.classList.contains('el-button')) elButton = target;
      else if (parentElement && parentElement.classList.contains('el-button')) elButton = parentElement;
      if (elButton) elButton.blur()
    };
    document.addEventListener('click', clickHandler);
    app.unmount = function () {
      document.removeEventListener('click', clickHandler);
    };
  }
};