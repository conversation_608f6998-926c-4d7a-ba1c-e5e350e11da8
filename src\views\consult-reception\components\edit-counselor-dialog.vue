<template>
  <el-dialog v-model="dialogVisible" title="修改所属咨询" width="400px" :close-on-click-modal="false" destroy-on-close>
    <div class="p-4">
      <div class="mb-4">请选择所属咨询人员：</div>
      <el-select v-model="selectedId" placeholder="请选择" style="width: 100%">
        <el-option v-for="item in counselorList" :key="item.value" :label="item.label" :value="item.value">
          <ww-user :openid="item.value" />
        </el-option>
      </el-select>
    </div>
    <template #footer>
      <div class="flex justify-end">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="submit" :loading="submitting">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  currentId: {
    type: String,
    default: ''
  },
  counselorList: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:visible', 'submit']);

const dialogVisible = ref(false);
const selectedId = ref('');
const submitting = ref(false);

watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val;
    if (val) {
      selectedId.value = props.currentId;
    }
  }
);

watch(
  () => dialogVisible.value,
  (val) => {
    if (!val) {
      emit('update:visible', false);
    }
  }
);

function closeDialog() {
  dialogVisible.value = false;
}

function submit() {
  submitting.value = true;
  
  emit('submit', {
    counselorUserId: selectedId.value
  });
  
  // 重置组件状态 (实际提交操作由父组件处理)
  submitting.value = false;
}
</script>
