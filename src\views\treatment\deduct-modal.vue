<template>
  <el-drawer v-model="visible" title="治疗划扣" size="80%" style="--el-drawer-padding-primary: 0">
    <my-layout class="bg-[#f2f3f4]">
      <layout-main>
        <customer-detail :customer="customer" @showCustomerDetail="handleDetail" :request="true"
          @reload="customerReload" />
        <div class="bg-white mt-10px">
          <div class="flex justify-between align-center">
            <div class="px-10px text-16px font-semibold">待治疗记录</div>
            <el-button class="m-10px h-25px" type="primary" @click="showBillVisible" plain>增加治疗项目</el-button>
          </div>
          <el-table stripe border height="100%" :data="list" @selection-change="selectTreatmentRecord">
            <el-table-column type="selection" width="50" label="合并治疗" />
            <el-table-column property="projectName" min-width="120" label="项目" />
            <el-table-column property="projectCount" label="剩余/总数" min-width="100">
              <template #default="{ row }">{{ row.restUsageCount }}/{{ row.usageCount }}</template>
            </el-table-column>
            <el-table-column property="treatmentDeptName" label="治疗科室" min-width="110" />
            <el-table-column property="treatmentDoctorUserId" label="预约治疗医生" min-width="160">
              <template #default="{ row: { treatmentDoctorUserId } }">
                <ww-user v-if="treatmentDoctorUserId" :openid="treatmentDoctorUserId" />
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column property="billdCreateTime" label="开单时间" min-width="110">
              <template #default="{ row: { billdCreateTime } }">
                <div v-if="billdCreateTime">{{ dayjs(billdCreateTime).format("YYYY-MM-DD") }}</div>
              </template>
            </el-table-column>
            <el-table-column property="billCreator" label="开单人" min-width="120">
              <template #default="{ row: { creator } }">
                <ww-user :openid="creator" />
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="bg-white mt-10px pt-20px">
          <div>
            <div class="px-10px text-16px font-semibold">治疗登记及划扣</div>
            <el-form :label-position="left">
              <el-row class="mx-10px border-gray-200 mt-15px">
                <el-col :span="6">
                  <el-form-item required label="治疗科室：">
                    {{ treatmentDeptName }}
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item required label="治疗时间：" label-width="120px">
                    <el-date-picker v-model="treatmentTime" placeholder="请选择治疗时间" style="width: 150px" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
          <el-table stripe border height="100%" width="100%" :data="deductList"
            @selection-change="selectTreatmentRecord">
            <el-table-column property="projectName" min-width="130" label="项目" />
            <el-table-column property="restUsageCount" label="待治疗总数" min-width="100" />
            <el-table-column property="currentRestUsageCount" label="划扣后剩余次数" min-width="120" />
            <el-table-column property="deductUsageCount" label="划扣数" min-width="80">
              <template #default="{ row }">
                <el-input type="number" v-model="row.deductUsageCount" @blur="inputDeductUsageCount(row)" />
              </template>
            </el-table-column>
            <el-table-column property="treatmentArea" label="治疗部位" min-width="120">
              <template #default="{ row }">
                <el-input v-model="row.treatmentArea" />
              </template>
            </el-table-column>
            <el-table-column property="treatmentDoctorUserId" label="医生" min-width="160">
              <template #default="{ row }">
                <el-select v-model="row.treatmentDoctorUserId" class="el-select--hiddenValue max-w-full w-full">
                  <template #prefix>
                    <div class="pl-10px h-30px truncate" color-666>
                      <ww-user v-if="row.treatmentDoctorUserId" :openid="row.treatmentDoctorUserId"></ww-user>
                    </div>
                  </template>
                  <el-option v-for="userId in assistantDoctorList" :key="userId" :label="userId" :value="userId">
                    <ww-user :openid="userId"></ww-user>
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column property="treatmentDoctorUserId" label="配台" min-width="160">
              <template #default="{ row }">
                <el-select v-model="row.assistantDoctors" multiple collapse-tags
                  class="el-select--hiddenValue w-full max-w-full">
                  <template #prefix>
                    <div class="max-w-full h-30px flex-grow">
                      <div v-if="row.assistantDoctors.length > 0" class="max-w-full pl-10px h-30px flex items-center"
                        color-666>
                        <div class="truncate">
                          <ww-user :openid="row.assistantDoctors[0]"></ww-user>
                        </div>
                        <span v-if="row.assistantDoctors.length > 1" class="flex-shrink-0 ml-5px">+ {{
                          row.assistantDoctors.length - 1 }}</span>
                      </div>
                    </div>
                  </template>
                  <el-option-group label="">
                    <el-option v-for="userId in nurseList" :key="userId" :label="userId" :value="userId">
                      <ww-user :openid="userId"></ww-user>
                    </el-option>
                  </el-option-group>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column property="treatmentDoctorUserId" label="下次治疗时间" min-width="160">
              <template #default="{ row }">
                <el-date-picker v-model="row.nextTreatmentTime" placeholder=" " class="w-full" />
              </template>
            </el-table-column>
          </el-table>
          <div class="px-10px text-16px font-semibold pt-15px">治疗备注</div>
          <div class="mx-10px my-15px bg-[#f7f8fa] rounded-8px p-16px">
            <el-input show-word-limit :maxlength="2000" type="textarea" v-model="treatmentRemark"
              class="custom-remark-input" rows="6" placeholder="请输入治疗备注内容" />
          </div>
          <div class="image-upload-group">
            <div class="group-title">知情同意书</div>
            <div class="button-row">
              <el-upload multiple action="#" accept="image/*" :show-file-list="false" list-type="picture"
                :auto-upload="false" @change="(file) => changeConsentFile(file)">
                <template #trigger>
                  <el-button size="small" type="primary">本地上传</el-button>
                </template>
              </el-upload>
              <el-button size="small" class="ml-10px" @click="openThirdPartySelector('consent')">三方导入</el-button>
            </div>
            <div class="image-list-wrapper">
              <template v-if="consentImageList.length === 0">
                <div class="empty-image">
                  <el-icon class="empty-icon">
                    <Picture />
                  </el-icon>
                  <div class="empty-text">无图片</div>
                </div>
              </template>
              <template v-else>
                <div v-for="(image, idx) in consentImageList" :key="image.key" v-loading="uploadLoading[image.key]"
                  class="image-card" element-loading-text="正在上传">
                  <img class="image-preview" :src="image.href || image.url" :alt="image.name"
                    @click="previewImage(image.href || image.url)" />
                  <div class="image-name" :class="image.error ? 'text-gray-500 line-through' : ''" :title="image.name">
                    {{ image.name }}</div>
                  <div v-if="image.error" class="image-error">上传失败，请重试</div>
                  <el-icon class="image-remove" @click="removeConsentImage(idx)">
                    <CircleCloseFilled />
                  </el-icon>
                </div>
              </template>
            </div>
          </div>
          <div class="image-upload-group">
            <div class="group-title">术前照片</div>
            <div class="button-row">
              <el-upload multiple action="#" accept="image/*" :show-file-list="false" list-type="picture"
                :auto-upload="false" @change="(file) => changeBeforeFile(file)">
                <template #trigger>
                  <el-button size="small" type="primary">本地上传</el-button>
                </template>
              </el-upload>
              <el-button size="small" class="ml-10px" @click="openThirdPartySelector('before')">三方导入</el-button>
            </div>
            <div class="image-list-wrapper">
              <template v-if="beforeImageList.length === 0">
                <div class="empty-image">
                  <el-icon class="empty-icon">
                    <Picture />
                  </el-icon>
                  <div class="empty-text">无图片</div>
                </div>
              </template>
              <template v-else>
                <div v-for="(image, idx) in beforeImageList" :key="image.key" v-loading="uploadLoading[image.key]"
                  class="image-card" element-loading-text="正在上传">
                  <img class="image-preview" :src="image.href || image.url" :alt="image.name"
                    @click="previewImage(image.href || image.url)" />
                  <div class="image-name" :class="image.error ? 'text-gray-500 line-through' : ''" :title="image.name">
                    {{ image.name }}</div>
                  <div v-if="image.error" class="image-error">上传失败，请重试</div>
                  <el-icon class="image-remove" @click="removeBeforeImage(idx)">
                    <CircleCloseFilled />
                  </el-icon>
                </div>
              </template>
            </div>
          </div>
          <div class="image-upload-group">
            <div class="group-title">术中照片</div>
            <div class="button-row">
              <el-upload multiple action="#" accept="image/*" :show-file-list="false" list-type="picture"
                :auto-upload="false" @change="(file) => changeDuringFile(file)">
                <template #trigger>
                  <el-button size="small" type="primary">本地上传</el-button>
                </template>
              </el-upload>
              <el-button size="small" class="ml-10px" @click="openThirdPartySelector('during')">三方导入</el-button>
            </div>
            <div class="image-list-wrapper">
              <template v-if="duringImageList.length === 0">
                <div class="empty-image">
                  <el-icon class="empty-icon">
                    <Picture />
                  </el-icon>
                  <div class="empty-text">无图片</div>
                </div>
              </template>
              <template v-else>
                <div v-for="(image, idx) in duringImageList" :key="image.key" v-loading="uploadLoading[image.key]"
                  class="image-card" element-loading-text="正在上传">
                  <img class="image-preview" :src="image.href || image.url" :alt="image.name"
                    @click="previewImage(image.href || image.url)" />
                  <div class="image-name" :class="image.error ? 'text-gray-500 line-through' : ''" :title="image.name">
                    {{ image.name }}</div>
                  <div v-if="image.error" class="image-error">上传失败，请重试</div>
                  <el-icon class="image-remove" @click="removeDuringImage(idx)">
                    <CircleCloseFilled />
                  </el-icon>
                </div>
              </template>
            </div>
          </div>
          <div class="image-upload-group">
            <div class="group-title">术后照片</div>
            <div class="button-row">
              <el-upload multiple action="#" accept="image/*" :show-file-list="false" list-type="picture"
                :auto-upload="false" @change="(file) => changeAfterFile(file)">
                <template #trigger>
                  <el-button size="small" type="primary">本地上传</el-button>
                </template>
              </el-upload>
              <el-button size="small" class="ml-10px" @click="openThirdPartySelector('after')">三方导入</el-button>
            </div>
            <div class="image-list-wrapper">
              <template v-if="afterImageList.length === 0">
                <div class="empty-image">
                  <el-icon class="empty-icon">
                    <Picture />
                  </el-icon>
                  <div class="empty-text">无图片</div>
                </div>
              </template>
              <template v-else>
                <div v-for="(image, idx) in afterImageList" :key="image.key" v-loading="uploadLoading[image.key]"
                  class="image-card" element-loading-text="正在上传">
                  <img class="image-preview" :src="image.href || image.url" :alt="image.name"
                    @click="previewImage(image.href || image.url)" />
                  <div class="image-name" :class="image.error ? 'text-gray-500 line-through' : ''" :title="image.name">
                    {{ image.name }}</div>
                  <div v-if="image.error" class="image-error">上传失败，请重试</div>
                  <el-icon class="image-remove" @click="removeAfterImage(idx)">
                    <CircleCloseFilled />
                  </el-icon>
                </div>
              </template>
            </div>
          </div>
        </div>
      </layout-main>
      <layout-item>
        <div class="text-center mt-10px py-10px bg-white" common-shadow--r>
          <el-button plain class="w-100px" @click="visible = false">取消</el-button>
          <el-button class="w-100px" type="primary" :loading="loading" @click="sumbit">划扣</el-button>
        </div>
      </layout-item>
    </my-layout>
  </el-drawer>
  <customer-bill-record :visible="billVisible" @close="closeBillVisible" :customer="customer"
    :treatmentOrderId="project.treatmentOrderId" @success="createBillSuccess" />
  <el-image-viewer v-if="showViewer" :url-list="previewList" @close="cancelPreview" />
  <ThirdPartyPhotoSelector v-model="thirdPartyVisible" :customer-name="customer?.name" :customerId="customer?._id"
    @confirm="handleThirdPartyConfirm" />
</template>
<script setup>
import { computed, ref, watch, onMounted } from "vue";
import { useVModel } from "@vueuse/core";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import { ElMessage } from "element-plus";
import { getDeductRecord, updateTreatmentRecord, updateDeductRecord } from "@/api/treatment-record";
import { updateFile } from "@/api/uploadFIle.js";
import useModal from "@/hooks/useModal";
import { staffStore } from "@/store/staff";

import customerBillRecord from "./customer-bill-record.vue";
import customerDetail from "@/components/customer-detail";
import MyLayout, { LayoutMain } from "@/components/layout";
import wwUser from "@/components/ww-user/index.vue";
import BigNumber from "bignumber.js";
import ThirdPartyPhotoSelector from "@/components/third-party-photo-selector.vue";

const emit = defineEmits(["update:modelValue", "success", "showCustomerDetail"]);
const props = defineProps({
  project: { type: Object, default: () => ({}) },
  modelValue: {
    type: Object,
    default: () => ({}),
  },
});
const loading = ref(false);
const deductList = ref([]);
const list = ref([]);
const treatmentRemark = ref("");
const treatmentTime = ref("");
const customer = computed(() => props.project.customerInfo);
const visible = useVModel(props, "modelValue", emit);
const treatmentDeptName = computed(() => props.project.treatmentDeptName);
const { staffList } = storeToRefs(staffStore());
const { getStaffList } = staffStore();
const { close: closeBillVisible, show: showBillVisible, visible: billVisible } = useModal(800);

const consentImageList = ref([]); // 知情同意书照片
const beforeImageList = ref([]); // 治疗前照片
const duringImageList = ref([]); // 治疗中照片
const afterImageList = ref([]); // 治疗后照片

const showViewer = ref(false);
const previewList = ref([]);
const uploadLoading = ref({});

// 三方照片选择器相关
const thirdPartyVisible = ref(false);
const currentPhotoType = ref('');

function previewImage(url) {
  showViewer.value = true;
  previewList.value = [url];
}

function cancelPreview() {
  showViewer.value = false;
}

// 打开三方照片选择器
function openThirdPartySelector(type) {
  currentPhotoType.value = type;
  thirdPartyVisible.value = true;
}

// 处理三方照片选择确认
function handleThirdPartyConfirm(selectedPhotos) {
  if (selectedPhotos && selectedPhotos.length > 0) {
    selectedPhotos.forEach((photo, idx) => {
      const imageData = {
        name: photo.name,
        key: `${Date.now()}_${Math.floor(Math.random() * 10000)}_${idx}`,
        href: photo.url,
        url: photo.url
      };
      switch (currentPhotoType.value) {
        case 'consent':
          consentImageList.value.push(imageData);
          break;
        case 'before':
          beforeImageList.value.push(imageData);
          break;
        case 'during':
          duringImageList.value.push(imageData);
          break;
        case 'after':
          afterImageList.value.push(imageData);
          break;
      }
    });
  }
}

function handleDetail() {
  emit("showCustomerDetail", customer.value._id);
}

const assistantDoctorList = computed(() => {
  const allDoctors = staffList.value.filter((i) => {
    const roles = ["doctor", "expertDoctor"];
    if (i.deptIds && Array.isArray(i.deptIds) && i.deptIds.includes(props.project.treatmentDept_id)) {
      return Array.isArray(i.job) && i.job.some((job) => roles.includes(job));
    } else {
      return false;
    }
  });
  return allDoctors.map((i) => i.userid);
});

// 护士列表
const nurseList = computed(() => {
  const allDoctors = staffList.value.filter((i) => {
    const roles = ["nurse", "assistant"];
    if (i.deptIds && Array.isArray(i.deptIds) && i.deptIds.includes(props.project.treatmentDept_id)) {
      return Array.isArray(i.job) && i.job.some((job) => roles.includes(job));
    } else {
      return false;
    }
  });
  return allDoctors.map((i) => i.userid);
});

const deptDoctors = computed(() => {
  return getDoctors(props.project);
});

function changeConsentFile({ raw: file, name, uid: key, url }) {
  consentImageList.value.push({ name, key, url, file });
}

function changeBeforeFile({ raw: file, name, uid: key, url }) {
  beforeImageList.value.push({ name, key, url, file });
}

function changeDuringFile({ raw: file, name, uid: key, url }) {
  duringImageList.value.push({ name, key, url, file });
}

function changeAfterFile({ raw: file, name, uid: key, url }) {
  afterImageList.value.push({ name, key, url, file });
}

function removeConsentImage(idx) {
  consentImageList.value.splice(idx, 1);
}

function removeBeforeImage(idx) {
  beforeImageList.value.splice(idx, 1);
}

function removeDuringImage(idx) {
  duringImageList.value.splice(idx, 1);
}

function removeAfterImage(idx) {
  afterImageList.value.splice(idx, 1);
}

function customerReload() {
  emit("success");
}

watch(
  () => visible.value,
  (n) => {
    if (n) {
      consentImageList.value = [];
      beforeImageList.value = [];
      duringImageList.value = [];
      afterImageList.value = [];
      treatmentRemark.value = "";
      treatmentTime.value = dayjs().format("YYYY-MM-DD");
      getBillProjectData();
    }
  }
);

onMounted(() => {
  loading.value = false;
  if (staffList.value.length === 0) getStaffList();
});
function createBillSuccess() {
  getBillProjectData();
  emit("success");
}
function getDoctors(project) {
  if (!project.treatmentDept_id) return [];
  let userIds =
    staffList.value && Array.isArray(staffList.value)
      ? staffList.value
        .filter((item) => {
          if (item.deptIds && Array.isArray(item.deptIds)) {
            return item.deptIds.includes(project.treatmentDept_id);
          } else {
            return false;
          }
        })
        .map((item) => {
          return item.userid;
        })
      : [];
  return userIds;
}
function selectTreatmentRecord(val) {
  deductList.value = val;
}

async function getBillProjectData() {
  const query = {
    customerId: customer.value._id,
    treatmentOrderId: props.project.treatmentOrderId,
    restUsageCount: props.project.restUsageCount,
    deductStatus: ["pending"],
  };
  let { success, data } = await getDeductRecord(query);
  if (success) {
    list.value = data.list.map((item) => {
      item.currentRestUsageCount = item.restUsageCount;
      return item;
    });
  }
}
async function sumbit() {
  if (deductList.value.length === 0) {
    ElMessage.warning("请选择划扣项目");
    return;
  }
  if (!treatmentTime.value) {
    ElMessage.warning("请选择治疗时间");
    return;
  }
  for (const item of deductList.value) {
    if (!item.deductUsageCount || Number(item.deductUsageCount) <= 0 || !item.treatmentDoctorUserId) {
      ElMessage.warning("每项的划扣数和医生均为必填项，请检查后再提交");
      return;
    }
  }
  loading.value = true;

  await Promise.all([
    uploadImages(consentImageList.value, 'consent'),
    uploadImages(beforeImageList.value, 'before'),
    uploadImages(duringImageList.value, 'during'),
    uploadImages(afterImageList.value, 'after')
  ]);

  if ([...consentImageList.value, ...beforeImageList.value, ...duringImageList.value, ...afterImageList.value]
    .some((item) => item.error || item.file)) {
    loading.value = false;
    return;
  }

  for (const key in deductList.value) {
    const item = deductList.value[key];
    await updateTreatment(item);
  }
  loading.value = false;
  emit("success");
  visible.value = false;
}
async function updateTreatment(item) {
  let { treatmentDoctorUserId, assistantDoctors, billId, deductUsageCount, treatmentArea, currentRestUsageCount, totalPrice, usageCount, nextTreatmentTime, projectCateId, isFree, projectName, treatmentDeptId, treatmentDeptName, treatmentId } = item;
  const deductedPrice = new BigNumber(totalPrice).dividedBy(new BigNumber(usageCount)).times(deductUsageCount).toFixed(2);
  let params = {
    treatmentDoctorUserId,
    restUsageCount: currentRestUsageCount,
    treatmentTime: dayjs(treatmentTime.value).valueOf(),
    assistantDoctors,
    deductStatus: "deducted",
    deductType: "直接划扣",
    treatmentRemark: treatmentRemark.value,
    deductOperator: localStorage.getItem("userId"),
    deductOperatTime: dayjs().valueOf(),
    deductUsageCount: Number(deductUsageCount),
    deductedPrice: Number(deductedPrice),
    treatmentArea,
    nextTreatmentTime,
    projectCateId,
    consentPics: consentImageList.value.map(i => ({ name: i.name, href: i.href })),
    pics: beforeImageList.value.map(i => ({ name: i.name, href: i.href })),
    duringPics: duringImageList.value.map(i => ({ name: i.name, href: i.href })),
    afterPics: afterImageList.value.map(i => ({ name: i.name, href: i.href })),
    isFree,
  };
  const query = {
    id: item._id,
    operateType: "deduct",
    billId,
    projectName,
    treatmentDeptName,
    treatmentDeptId,
    params,
  };
  await updateDeductRecord(query);
  await updateTreatmentStatus({ id: treatmentId, currentRestUsageCount, deductUsageCount });
}

// 更新治疗状态
async function updateTreatmentStatus({ id, currentRestUsageCount, deductUsageCount }) {
  const query = {
    id,
    params: {
      treatmentStatus: currentRestUsageCount > 0 ? "init" : "treated",
      restUsageCount: currentRestUsageCount,
      deductUsageCount,
    },
  };
  await updateTreatmentRecord(query);
}
async function inputDeductUsageCount(row) {
  if (Number(row.deductUsageCount) > Number(row.restUsageCount)) {
    try {
      ElMessage.warning("划扣数不能大于剩余次数");
      row.deductUsageCount = "";
      row.currentRestUsageCount = row.restUsageCount;
    } catch {
      row.deductUsageCount = "";
      row.currentRestUsageCount = row.restUsageCount;
    }
  } else {
    row.currentRestUsageCount = row.restUsageCount - row.deductUsageCount;
  }
}

async function uploadImages(imageList, type) {
  const m = new Map();
  for (let item of imageList) {
    if (item.file) {
      uploadLoading.value[item.key] = true;
      try {
        const res = await updateFile(`${type}_${+new Date()}_${Math.ceil(Math.random() * 100000)}_${item.name}`, item.file, localStorage.getItem("corpId"));
        if (res.download_url) {
          m.set(item.key, { name: item.name, href: res.download_url, key: item.key });
        } else {
          m.set(item.key, { ...item, error: true });
        }
      } catch (e) {
        m.set(item.key, { ...item, error: true });
      }
      uploadLoading.value[item.key] = false;
    } else {
      m.set(item.key, { name: item.name, href: item.href, key: item.key });
    }
  }

  for (let i = 0; i < imageList.length; i++) {
    const item = imageList[i];
    imageList[i] = m.get(item.key);
  }
}
</script>
<style lang="scss" scoped>
:deep(.hidden-inner-content > .select-trigger > .el-input > .el-input__wrapper > .el-input__inner) {
  opacity: 0;
}

.el-select.el-select--hiddenValue .el-select__tags>span {
  display: none;
}

:deep(.el-select.el-select--hiddenValue .el-input__prefix) {
  flex-grow: 1;
  max-width: 100%;
}

:deep(.el-select.el-select--hiddenValue .el-input__prefix .el-input__prefix-inner) {
  max-width: 100%;
}

:deep(.el-select.el-select--hiddenValue .el-input__prefix .el-input__prefix-inner > div) {
  max-width: 100%;
}

:deep(.select-trigger .el-input__wrapper) {
  max-width: 100%;
  overflow: hidden;
}

.custom-remark-input textarea {
  background: transparent;
  border-radius: 8px;
  min-height: 120px;
  font-size: 15px;
  color: #333;
}

:deep(.el-input__count) {
  right: 12px !important;
  bottom: 8px !important;
  color: #b0b0b0 !important;
  font-size: 13px !important;
}

.image-card {
  position: relative;
  width: 160px;
  margin-right: 10px;
  margin-bottom: 10px;
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 8px 0 #e5e6eb;
  background: #fff;
  overflow: hidden;
  transition: box-shadow 0.2s, transform 0.2s;
}

.image-card:hover {
  box-shadow: 0 4px 16px 0 #d0d1d6;
  transform: translateY(-2px) scale(1.02);
}

.image-preview {
  width: 100%;
  height: 100px;
  object-fit: cover;
  border-radius: 8px 8px 0 0;
  cursor: pointer;
}

.image-name {
  font-size: 14px;
  margin-top: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.image-error {
  font-size: 12px;
  color: #f56c6c;
  margin-top: 10px;
}

.image-remove {
  position: absolute;
  right: 6px;
  top: 6px;
  background: #fff;
  border-radius: 50%;
  color: #f56c6c;
  cursor: pointer;
  font-size: 18px;
  box-shadow: 0 2px 8px 0 #e5e6eb;
  z-index: 2;
}

.image-upload-group {
  background: #fff;
  border: 1px solid #e5e6eb;
  border-radius: 8px;
  margin: 20px 0 0 0;
  padding: 16px 20px 10px 20px;
}

.group-title {
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 8px;
}

.button-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.image-list-wrapper {
  min-height: 120px;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  background: #fafbfc;
  border-radius: 8px;
  padding: 16px 0 6px 0;
  border: 1px dashed #e5e6eb;
  margin-bottom: 0;
  min-width: 100px;
}

.empty-image {
  width: 100%;
  min-height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #b0b0b0;
  font-size: 15px;
}

.empty-icon {
  font-size: 36px;
  margin-bottom: 6px;
}

.empty-text {
  font-size: 14px;
  color: #b0b0b0;
}
</style>