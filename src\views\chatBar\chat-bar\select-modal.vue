<template>
  <el-dialog :model-value="visible" :width="width" title="切换客户信息" @close="close">
    <div px-15 class="flex justify-between items-center pt-6px pb-12px">
      <span class="text-14px" color-666>请选择客户</span>
      <!-- <el-text class="cursor-pointer" type="primary" @click="add()">
        <el-icon size="14">
          <Plus />
        </el-icon>
        新增客户
      </el-text> -->
    </div>
    <el-scrollbar wrap-style="max-height: calc(100vh - 360px)">
      <div v-for="item in customers" :key="item._id"
        class="px-15px pointer rounded-4px customer-item flex items-center">
        <div flex-grow> <customer-bar :member="item" @click="select(item)" /></div>
        <span color-primary flex-shrink-0 class="ml-10px" :class="memberId === item._id ? '' : 'opacity-0'">
          <el-icon size="20"><Select /></el-icon>
        </span>
      </div>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <!-- <el-button type="primary" plain class="w-100px" @click="send()">发送建档链接</el-button> -->
        <el-button class="w-100px" type="primary" @click="confirm()"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ElMessage } from 'element-plus';
import { ref, toRefs, watch } from 'vue';
import CustomerBar from "../components/customer-bar";

const props = defineProps({
  customer: { type: Object, default: () => ({}) },
  customers: { type: Array, default: () => [] },
  visible: { type: Boolean, default: false },
  unionId: { type: String, default: '' },
  width: { type: Number, default: 520 }
})
const { customer, customers, visible, width } = toRefs(props);
const emits = defineEmits(['add', 'close', 'select', 'send']);

const memberId = ref('');
function select(customer) {
  memberId.value = customer._id
}
function confirm() {
  if (customers.value.some(i => i._id === memberId.value && memberId.value)) {
    close()
    emits('select', memberId.value)
  } else {
    ElMessage.info('请选择客户')
  }
}

function close() {
  emits('close')
}
function send() {
  close()
  emits('send')
}
function add() {
  close()
  emits('add')
}

watch(visible, n => {
  if (n) memberId.value = customer.value && customer.value._id ? customer.value._id : '';
})
</script>
<style lang="scss" scoped>
.customer-item:hover {
  background: #FFFAFA;
}
</style>
