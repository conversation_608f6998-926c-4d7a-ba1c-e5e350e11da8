<template>
  <!-- <div class="table table-auto w-full text-14px leading-21px">
    <div v-for="item in tempList" :key="tempList.title" class="table-row">
      <div class="table-cell pl-15px pr-10px py-4px" border-bottom>
        <div class="flex items-center w-90px text-14px leading-20px min-h-40px text-gray-500">
          {{ item.name }}
        </div>
      </div>
      <div v-if="CellComponent[item.type]" class="table-cell pl-15px pr-10px py-12px" border-bottom>
        <component :is="CellComponent[item.type]" :form="form" :title="item.title" />
      </div>
      <div v-else class="table-cell pl-15px pr-10px py-12px" border-bottom>
        {{ getValue(item) }}
      </div>
    </div>
  </div> -->
  <table class="table-auto text-14px leading-21px">
    <tbody>
      <tr v-for="item in tempList" :key="tempList.title" border-bottom>
        <template v-if="customContent[item.type]">
          <td colspan="2">
            <div class="flex items-center pl-15px pr-10px py-4px text-14px leading-20px min-h-40px text-gray-500">
              {{ item.name }}
            </div>
            <component v-if="CellComponent[item.type]" :is="CellComponent[item.type]" :form="form" :title="item.title" />
          </td>
        </template>
        <template v-else>
          <td width="90">
            <div class="flex items-center pl-15px pr-10px py-4px w-90px text-14px leading-20px min-h-40px text-gray-500" style="width: 90px">
              {{ item.name }}
            </div>
          </td>
          <td>
            <div v-if="CellComponent[item.type]" class="py-12px">
              <component :is="CellComponent[item.type]" :form="form" :title="item.title" />
            </div>
            <div color-normal v-else class="pl-15px pr-10px py-12px">
              <div v-if="item.type === 'selectAndImage'">
                <img :src="getValue(item)" alt="" class="h-40px w-auto" />
              </div>
              <div v-else>{{ getValue(item) }}</div>
            </div>
          </td>
        </template>
      </tr>
    </tbody>
  </table>
</template>
<script setup>
import { defineAsyncComponent } from "vue";
import dayjs from "dayjs";
const CellReference = defineAsyncComponent(() => import("./cell-reference.vue"));
const CellTags = defineAsyncComponent(() => import("./cell-tags.vue"));
const CellUpload = defineAsyncComponent(() => import("./cell-upload.vue"));
const CellWwuser = defineAsyncComponent(() => import("./cell-wwuser.vue"));
const CellStage = defineAsyncComponent(() => import("./cell-stage.vue"));
const CellSelectMobile = defineAsyncComponent(() => import("./cell-select-mobile.vue"));
const CellCreateTime = defineAsyncComponent(() => import("./cell-create-time.vue"));
const CellBMI = defineAsyncComponent(() => import("./cell-bmi.vue"));
const CellBloodPressure = defineAsyncComponent(() => import("./cell-blood-pressure.vue"));
const CellCreator = defineAsyncComponent(() => import("./cell-creator.vue"));
const positiveFind = defineAsyncComponent(() => import("./cell-positive-find.vue"));

const props = defineProps({
  tempList: { type: Array, default: () => [] },
  form: { type: Object, default: () => ({}) },
  format: { type: Object, default: () => ({}) },
});

const customContent = {
  positiveFind: true,
};

const CellComponent = {
  tag: CellTags,
  reference: CellReference,
  files: CellUpload,
  wwUser: CellWwuser,
  customerStage: CellStage,
  createTime: CellCreateTime,
  selectMobile: CellSelectMobile,
  BMI: CellBMI,
  creator: CellCreator,
  bloodPressure: CellBloodPressure,
  positiveFind,
};

function getValue(item) {
  const value = props.form[item.title];
  if (typeof props.format[item.title] === "function") {
    return props.format[item.title](value);
  }
  if (item.type === "date") return value && dayjs(value).isValid() ? dayjs(value).format(item.format || "YYYY-MM-DD") : "";
  if (Array.isArray(value)) return value.join(" ");
  return value;
}
</script>
<style lang="scss" scoped></style>
