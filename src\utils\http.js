import axios from "axios";

// 创建一个 Axios 实例并进行全局配置
const instance = axios.create({
  baseURL: "https://www.youcan365.com", // 设置基础 URL
  timeout: 5000, // 设置请求超时时间
  // 其他配置项...
});

// 请求拦截器
instance.interceptors.request.use(
  (config) => {
    // 在请求发送之前可以进行一些处理，例如添加请求头等
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
instance.interceptors.response.use(
  (response) => {
    // 对响应数据进行处理，例如解析数据等
    return response;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 导出封装好的 Axios 实例
export default instance;
