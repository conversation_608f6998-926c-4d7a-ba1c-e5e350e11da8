<template>
  <my-layout>
    <layout-main>
      <el-row>
        <el-col class="mb-15px" :span="24">
          <count-header :qrcodeInfo="qrcodeInfo"></count-header>
        </el-col>
        <el-col class="mb-15px" :span="24">
          <count-bar :qrcodeInfo="qrcodeInfo" @reload="reload"></count-bar>
        </el-col>
      </el-row>
      <customer-stats :qrcodeInfo="qrcodeInfo" />
    </layout-main>
  </my-layout>
</template>
  <script setup>
import CountBar from "./components/count-bar.vue";
import countHeader from "./components/count-header";
import CustomerStats from "./components/customer-stats/customer-stats";
import MyLayout, { LayoutMain } from "@/components/layout";
import { useRoute, useRouter } from "vue-router";
import { getStaffQrCodeDetail } from "@/api/knowledgeBase";
import { ref } from "vue";
const route = useRoute();
let id = route.params.id;
let qrcodeInfo = ref({});
getData();
async function getData() {
  const { success, data } = await getStaffQrCodeDetail(id);
  if (success) {
    qrcodeInfo.value = data.data;
  }
}
function reload() {
  getData();
}
</script>
  