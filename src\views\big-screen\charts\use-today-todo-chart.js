import { markRaw, ref, onMounted } from 'vue';
import { watchDebounced, useWindowSize } from "@vueuse/core";
import echarts from '@/utils/echarts';

function getServiceOptions(todoData = [], finishData = []) {
  return {
    title: {
      text: ''
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
          Color: '#fff'
        }
      }
    },
    legend: {
      textStyle: {
        fontSize: 14,
        color: '#a7cdff'
      },
      bottom: 0,
      data: ['待办数量', '已完成']
    },
    grid: {
      top: '3%',
      left: '5%',
      right: '5%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: [{
      type: 'category',
      //刻度线
      axisTick: {
        show: false
      },
      // boundaryGap: false,
      data: new Array(8).fill(6).map((i, idx) => `${`${i + 2 * idx}`.padStart(2, '0')}:00`),
      axisLine: {
        lineStyle: {
          color: '#a7cdff',
          width: 0, //这里是为了突出显示加上的
        }
      },
    },],
    yAxis: [{
      type: 'value',
      min: 0,
      boundaryGap: [0.2, 0.2],
      //刻度线
      axisTick: { //y轴刻度线
        show: false
      },
      //字体颜色
      axisLine: {
        lineStyle: {
          color: '#a7cdff',
          width: 0, //这里是为了突出显示加上的
        }
      },
      //网格线
      splitLine: {
        show: true,
        lineStyle: {
          color: ['#23569b'],
          width: 1,
          type: 'dashed'
        }
      }
    }],
    color: ['#6dc1ff', '#f9b008'],
    series: [{
      name: '待办数量',
      type: 'line',

      areaStyle: {
        normal: {
          //颜色渐变函数 前四个参数分别表示四个位置依次为左、下、右、上
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(109,193,255,0.6)' },
            { offset: .8, color: 'rgba(56,155,255,0.4)' },
            { offset: 1, color: 'rgba(38,197,254,0.00)' }
          ])
        }
      },
      data: todoData
    },
    {
      name: '已完成',
      type: 'line',
      areaStyle: {
        normal: {
          //颜色渐变函数 前四个参数分别表示四个位置依次为左、下、右、上
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(249,176,8,0.8)' },
            { offset: .34, color: 'rgba(249,176,8,0.4)' },
            { offset: 1, color: 'rgba(249,176,8,0.1)' }
          ])
        }
      },
      data: finishData
    }
    ],

  }
}

export default function useTodayTodoChart(chartRef) {
  const chart = ref(null);
  const todoData = ref([100, 120, 170, 178, 210, 240, 266, 280]);
  const finishData = ref([0, 80, 100, 150, 180, 220, 230, 270]);
  const { width, height } = useWindowSize();

  function setChart() {
    chart.value = markRaw(echarts.init(chartRef.value));
  }

  function paint(repaint = false) {
    if (chartRef.value && !chart.value) {
      setChart();
      repaint = false;
    };
    if (!chart.value) return;
    const option = getServiceOptions(todoData.value, finishData.value);
    chart.value.setOption(option, repaint);
  }
  onMounted(paint)
  watchDebounced([todoData, finishData], n => {
    paint(true)
  }, { debounce: 500, maxWait: 1000 })
  watchDebounced([width, height], () => {
    chart.value && chart.value.resize();
  }, { debounce: 500, maxWait: 1500 })

  return { todoData, finishData }
}