<template>
  <my-layout>
    <layout-item>
      <div px-15 py-10 flex items-center justify-between>
        <div flex-shrink-0 font-semibold class="text-20px">机构回访计划列表</div>
        <div flex items-center>
          <el-button class="" type="primary" @click="toEdit()">新增</el-button>
        </div>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <div flex h-full>
        <div bg-fff h-full flex-shrink-0 class="w-3/10 min-w-240px max-w-320px mr-10px border-r border-gray-200">
          <classify-list v-bind="componentProps" :checked-cate="current" :data="cateList"
            @change="onChangeCurrent($event)" @search="getCateList()" />
        </div>
        <div v-if="list.length" h-full flex-grow>
          <my-layout>
            <layout-main>
              <div v-for="item in list" :key="item._id" bg-fff rounded-8 mb-10 px-15 common-shadow>
                <div flex items-center justify-between py-15 border-bottom>
                  <div font-16 font-semibold mr-10 class="truncate">{{ item.planName }}</div>
                  <div class="flex-shrink-0 mx-15px">
                    <span class="text-14px text-gray-500">使用团队：</span>
                    <span v-if="item.fitAllTeams" class="text-14px">全部</span>
                    <el-tooltip v-else-if="item.teamIds && item.teamIds.length" effect="light">
                      <span class="text-blue-500">{{item.teamIds.length}}</span>
                      <template #content>
                        <el-scrollbar wrap-class="max-h-50vh">
                          <div v-for="teamId in item.teamIds" :key="teamId" font-14 color-666>
                            团队名称: <team-data :teamId="teamId" />
                          </div>
                        </el-scrollbar>
                      </template>
                    </el-tooltip>
                  </div>
                </div>
                <div flex items-center justify-between py-15 flex-wrap>
                  <div flex items-center flex-wrap font-14 color-666>
                    <span>创建人：</span><el-text class="mr-15px" min-w-80>
                      <ww-user v-if="item.createor" :openid="item.createor"></ww-user>
                    </el-text>
                    <span>创建时间：</span><el-text class="mr-15px">
                      {{ item.createTime ?dayjs(item.createTime).format('YYYY-MM-DD HH:mm') : '--' }}
                    </el-text>
                    <span>发布状态 ：</span><el-text :type="item.planStatus ? 'primary' : ''">
                      {{ item.planStatus ? '已启用' : '已停用' }}
                    </el-text>
                  </div>
                  <div flex items-center>
                    <el-text pointer class="mr-15px" type="primary" @click="showDetail(item)">详情</el-text>
                    <el-text pointer class="mr-15px" type="primary" @click="toEdit(item._id, true)">复制</el-text>
                    <template v-if="!item.planStatus">
                      <el-text pointer class="mr-15px" type="primary" @click="toEdit(item._id)">编辑</el-text>
                      <el-text pointer class="mr-15px" type="danger" @click="remove(item._id)">删除</el-text>
                    </template>
                    <el-text pointer class="mr-15px" :type="item.planStatus ? 'danger':'primary'"
                      @click="toggle(item._id, item.planStatus)">
                      {{ item.planStatus ? '停用' : '启用' }}
                    </el-text>
                  </div>
                </div>
              </div>
            </layout-main>
            <layout-item>
              <pagination :bgWhite="false" :totalRow="total" :pageSize="pageSize" :currentPage="page"
                @handle-size-change="changeSize" @handle-current-change="changePage" />
            </layout-item>
          </my-layout>
        </div>
        <div v-else h-full flex flex-col items-center justify-center flex-grow>
          <empty-data title="暂无机构回访计划"></empty-data>
        </div>
      </div>
    </layout-main>
  </my-layout>
  <task-detial :visible="taskVisible" :managementPlan="managementPlan" @close="taskVisible = false"></task-detial>

</template>
<script setup>
import { onMounted, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import dayjs from 'dayjs';
import { ElMessage, ElMessageBox } from 'element-plus';
import { addManageMentPlanCate, deleteManageMentPlanCate, getManageMentPlanCate, updateManageMentPlanCate, sortManageMentPlanCate, getManagementPlanList, removeManagementPlan } from "@/api/managementPlan";

import useClassifyList from '@/components/classify-list/useClassifyList';
import useElPagination from "@/hooks/useElPagination";
import { managePlanStore } from "@/store/managePlan/managePlan.js";
import { tagsStore } from "@/store/tags";

import classifyList from '@/components/classify-list/classify-list.vue';
import EmptyData from '@/components/empty-data.vue';
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import WwUser from '@/components/ww-user/index.vue';
import taskDetial from "./components/task-detial";
import TeamData from '@/components/team-data/team-data.vue'

const router = useRouter();
const route = useRoute();
const total = ref(0);
const managementPlan = ref({});
const taskVisible = ref(false)

const options = {
  add: addManageMentPlanCate,
  remove: deleteManageMentPlanCate,
  update: updateManageMentPlanCate,
  sort: sortManageMentPlanCate,
  getList: getManageMentPlanCate,
  callback: getList
}

const { stopPlan, enablePlan } = managePlanStore();
const { subTagClosed } = tagsStore();
const { cateList, current, cateTree, getCateList, total: allCount, changeCurrent, componentProps } = useClassifyList(options);
const { list, page, pageSize, changePage, changeSize } = useElPagination(getList);

// const cateMap = computed(() => cateList.value.reduce((acc, cur) => {
//   acc[cur._id] = cur.label;
//   return acc;
// }, {}))
function onChangeCurrent(val) {
  const _id = sessionStorage.getItem('CORPVISITPLAN_CATEID');
  const _page = sessionStorage.getItem('CORPVISITPLAN_PAGE');
  const item = cateList.value.find(item => item._id === _id);
  if (item && val._id !== _id) {
    sessionStorage.removeItem('CORPVISITPLAN_CATEID');
    sessionStorage.removeItem('CORPVISITPLAN_PAGE');
    changeCurrent(item)
    page.value = Number.isInteger(_page) ? _page : 1;
  } else {
    page.value = 1
    changeCurrent(val)
  }
}

function toEdit(id = '', copy = false) {
  sessionStorage.setItem('CORPVISITPLAN_CATEID', current.value._id);
  sessionStorage.setItem('CORPVISITPLAN_PAGE', page.value);
  router.push({ name: "MANAGEMENTPLANDETAIL", params: { id, type: copy ? 'copy' : '' }, state: { planType: 'corp', cateId: current.value._id } });
}

async function getList() {
  const cateIds = Array.isArray(current.value.childrenIds) ? [current.value._id, ...(current.value.childrenIds || [])] : [current.value._id];
  const params = {
    page: page.value,
    pageSize: pageSize.value,
    corpId: localStorage.getItem("corpId"),
    planType: 'corp',
    cateIds,
    userId: localStorage.getItem("userId"),
  }
  const { success, data, message } = await getManagementPlanList(params);
  list.value = Array.isArray(data.data) ? data.data : [];
  total.value = data.total;
  if (!success) {
    ElMessage.error(message);
  }
}

async function toggle(_id, enable) {
  const res = await (enable ? stopPlan(_id, false) : enablePlan(_id));
  res && getList()
}
async function remove(id) {
  await ElMessageBox.confirm('确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
  const { success, message } = await removeManagementPlan(id);
  if (success) {
    ElMessage.success('删除成功');
    getList()
  } else {
    ElMessage.error(message);
  }
}

onMounted(() => {
  subTagClosed(route.fullPath, () => {
    sessionStorage.removeItem('CORPVISITPLAN_CATEID');
    sessionStorage.removeItem('CORPVISITPLAN_PAGE');
  })
})

function showDetail(item) {
  taskVisible.value = true;
  managementPlan.value = item || {};
}

</script>
