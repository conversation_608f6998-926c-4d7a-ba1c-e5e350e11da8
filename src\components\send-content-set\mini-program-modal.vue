<template>
  <el-dialog :model-value="visible" title="添加小程序" :width="width" @close="close">
    <div class="px-15px pt-5px text-14px">
      <el-icon class="mr-5px transform translate-y-2px text-red-500">
        <Bell />
      </el-icon>
      <span class="text-gray-400">请填写企业微信后台绑定的小程序id和路径，否则会造成发送失败。</span>
    </div>
    <el-form class="p-15px" label-position="top">
      <el-form-item class="is-required" label="小程序标题">
        <el-input v-model="form.name" placeholder="请输入小程序标题" clearable></el-input>
      </el-form-item>
      <el-form-item class="is-required mt-10px" label="小程序appid">
        <el-input v-model="form.appid" placeholder="请输入小程序appid" clearable></el-input>
      </el-form-item>
      <el-form-item class="is-required mt-10px" label="小程序路径">
        <el-input v-model="form.path" placeholder="请输入小程序路径" clearable></el-input>
      </el-form-item>
      <el-form-item class="mt-10px" label="小程序封面">
        <el-upload action="#" accept="image/*" :show-file-list="false" list-type="picture" :auto-upload="false"
          @change="changeFile">
          <template #trigger>
            <div class="relative flex items-center justify-center w-80px h-80px rounded"
              :class="form.cover ?'':'border-1 border-dashed text-blue-500 border-blue-500'">
              <template v-if="form.cover">
                <img :src="form.cover" class="w-full h-full">
                <el-icon class="absolute -top-10px -right-16px text-red-500 text-20px cursor-pointer"
                  @click.stop="remove()">
                  <CloseBold />
                </el-icon>
              </template>
              <el-icon v-else>
                <Plus />
              </el-icon>
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>
    <template #footer>
      <div text-center>
        <el-button class="w-100px" plain @click="close()">取消</el-button>
        <el-button class="w-100px" type="primary" @click="confirm()"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref, toRefs, watch } from 'vue';
import { ElLoading, ElMessage } from 'element-plus';
import { getRandomStr, imageToBase64 } from '@/utils'
import { updateFile } from "@/api/uploadFIle.js";

const emits = defineEmits(['close', 'change']);
const props = defineProps({
  miniProgram: { type: Object, default: () => ({}) },
  visible: { type: Boolean, default: false },
  width: { type: Number, default: 300 },
})

const form = ref({});
const coverFile = ref();

function close() {
  emits('close')
}

async function confirm() {
  if (typeof form.value.name !== 'string' || form.value.name.trim() === '') return ElMessage.error('请输入小程序标题')
  if (typeof form.value.appid !== 'string' || form.value.appid.trim() === '') return ElMessage.error('请输入小程序appid')
  if (typeof form.value.path !== 'string' || form.value.path.trim() === '') return ElMessage.error('请输入小程序路径')
  if (coverFile.value) {
    const res = await imageToBase64(coverFile.value.file)
    if (res === false) return;
    form.value.cover = res
  }
  emits('change', { ...form.value })
  close()
}

function remove() {
  form.value.cover = ''
  coverFile.value = null
}

async function changeFile(uploadFile) {
  const { raw: file } = uploadFile; // blobUrl仅仅本地预览
  if (/^image\//.test(file.type)) {
    const base64String = await imageToBase64(file);
    const base64WithPrefix = `data:${file.type};base64,${base64String}`;
    form.value.cover = base64WithPrefix;
  }
  coverFile.value = file
}

async function upload() {
  const loadingInstance = ElLoading.service({ fullscreen: true, text: '正在上传' })
  try {
    const res = await updateFile(`${+new Date()}_${Math.ceil(Math.random() * 100000)}_${coverFile.value.name}`, coverFile.value, localStorage.getItem('corpId'));
    if (res) {
      return res.download_url
    } else {
      loadingInstance.close()
      ElMessage.error(`$封面上传失败`);
      return false;
    }
  } catch (e) {
    loadingInstance.close()
    ElMessage.error(`${props.name} ${item.file.name}上传失败`);
    return false;
  }
}


watch(() => props.visible, n => {
  if (n) {
    const { name = '', appid = '', path = '', cover = '' } = (props.miniProgram || {})
    form.value = { name, appid, path, cover }
    coverFile.value = null
  }
})
</script>