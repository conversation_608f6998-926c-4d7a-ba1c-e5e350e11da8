<template>
  <div class="text-13px">
    <div class="flex px-15px py-12px border-b border-gray-200">
      <div class="w-0 flex-grow">
        <div class="flex items-center">
          <div class="text-15px font-semibold truncate" style="max-width: calc(100% - 40px)">
            {{ item.name }}
          </div>
          <el-icon class="ml-5px text-16px text-blue-500 cursor-pointer" @click="show()">
            <Document />
          </el-icon>
        </div>
        <div class="mt-10px truncate text-orange-400">￥ {{ item.packagePrice >= 0 ? Number(item.packagePrice).toFixed(2) : "0.00" }}</div>
        <div class="text-14px text-gray-400 mt-10px">
          <span v-if="item.packageType === 'free'">自由组合：{{ item.projects.length }} 选 {{ item.projectNumber }}</span>
          <span v-else>固定组合</span>
        </div>
      </div>
      <div class="flex flex-col justify-between">
        <el-icon v-if="selectMap[item._id]" class="mt-4px text-18px text-green-500 cursor-pointer" @click="toggleSelect(item)">
          <Check />
        </el-icon>
        <el-icon v-else class="mt-4px text-18px text-gray-400 hover:text-blue-500 cursor-pointer" @click="toggleSelect(item)">
          <CirclePlusFilled />
        </el-icon>
        <el-icon v-if="expandMap[item._id]" class="mb-4px text-18px text-gray-300 hover:text-blue-500 cursor-pointer" @click="expandMap[item._id] = !expandMap[item._id]">
          <ArrowUpBold />
        </el-icon>
        <el-icon v-else class="mb-4px text-18px text-gray-300 hover:text-blue-500 cursor-pointer" @click="expandMap[item._id] = !expandMap[item._id]">
          <ArrowDownBold />
        </el-icon>
      </div>
    </div>
    <div v-if="expandMap[item._id]">
      <div v-for="(project, idx) in item.projects" :key="`${item._id}_${project._id}`" class="flex items-center px-15px py-12px border-b border-gray-200">
        <div class="flex-grow flex w-0 mr-10px">
          <div class="w-0 flex-grow truncate">{{ project.projectName }}</div>
          <div v-if="project.number > 0" class="flex-shrink-0 text-gray-500">x {{ project.number }}</div>
        </div>
        <div class="min-w-80px text-right flex-shrink-0 ml-auto text-14px text-orange-400">￥ {{ project.price >= 0 ? Number(project.price).toFixed(2) : "0.00" }}</div>
      </div>
    </div>
  </div>
</template>
<script setup>
const emits = defineEmits(["select", "show", "expand"]);
const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
  selectMap: {
    type: Object,
    default: () => ({}),
  },
  expandMap: {
    type: Object,
    default: () => ({}),
  },
});

function toggleExpand() {
  emits("expand", props.item);
}

function toggleSelect() {
  emits("select", props.item);
}

function show() {
  emits("show", props.item);
}
</script>
<style lang="scss" scoped></style>
