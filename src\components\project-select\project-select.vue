<template>
  <el-select v-model="projects" class="w-full" multiple filterable remote reserve-keyword placeholder="请搜索"
    :remote-method="search" :loading="loading" :remote-show-suffix="true" collapse-tags>
    <el-option v-for="item in _options" :key="item.value" :label="item.label" :value="item.value" />
  </el-select>
</template>
<script setup>
import { computed, ref, watch } from 'vue';
import { useVModel, useDebounceFn } from "@vueuse/core";
import { getProjectList } from "@/api/project-manage";

const emit = defineEmits(["update:modelValue"]);
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
});
const projects = useVModel(props, "modelValue", emit);
const loading = ref(false);
const options = ref([]);
const optionMap = ref({});
const _options = computed(() => {
  const valueOptions = projects.value.map(i => optionMap.value[i])
  const list = options.value.filter(i => !projects.value.includes(i.value))
  return [...valueOptions, ...list]
})

const search = useDebounceFn(searchProjects, 1000)

async function searchProjects(text) {
  if (text.trim()) {
    options.value = await getOptions({
      page: 1,
      pageSize: 50,
      corpId: localStorage.getItem("corpId"),
      userId: localStorage.getItem("userId"),
      projectName: text.trim(),
      projectStatus: 'enable'
    })
  }
}

async function getOptions(params) {
  loading.value = true;
  const { data } = await getProjectList(params);
  loading.value = false
  const res = data && Array.isArray(data.list) ? data.list.map(i => ({
    ...i,
    label: i.projectName,
    value: i._id,//projectNo
  })) : []
  console.log(res)
  return res
}

watch(projects, n => {
  if (Array.isArray(n)) {
    n.forEach(i => {
      if (!optionMap.value[i]) {
        optionMap.value[i] = options.value.find(j => j.value === i)
      }
    })
  }
})


</script>
<style lang="scss" scoped></style>
