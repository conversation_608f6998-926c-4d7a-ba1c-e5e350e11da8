<template>
  <el-dialog :model-value="visible" :width="width" title="选择标签" @close="close">
    <el-scrollbar wrap-style="max-height: 50vh;">
      <div v-for="group in tags" :key="group.id" class="px-15px">
        <div class="text-14px py-12px font-semibold">
          {{ group.groupName }}
          <span class="pl-5px text-12px" main-color v-if="group.createType == 'corpAsync'">(企微标签)</span>
        </div>
        <div class="flex flex-wrap tag-group">
          <div v-for="item in group.tag" :key="item.id" :class="selectMap[item.id] ? 'active' : ''" class="px-12px py-6px text-12px tag-item text-center rounded-full" @click="toggle(item, group.tag)">
            {{ item.name }}
          </div>
        </div>
      </div>
    </el-scrollbar>
    <template #footer>
      <div class="text-center">
        <el-button plain class="w-100px" @click="close()">取消</el-button>
        <el-button type="primary" class="w-100px" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, onMounted, ref, watch } from "vue";
import { configStore } from "@/store/config";
import { storeToRefs } from "pinia";
import { ElMessage } from "element-plus";
import useCustomerApi from "../../chat-bar/useCustomerApi";
const { getGroupTag } = configStore();
const { tags } = storeToRefs(configStore());
const props = defineProps({
  customerHandle: { type: Boolean, default: false },
  memberId: { type: String, default: "" },
  visible: { type: Boolean, default: false },
  value: { type: Array, default: () => [] },
});
onMounted(async () => {
  if (!tags.value.length) await getGroupTag();
});
const selectMap = ref({});
function toggle(item, options) {
  selectMap.value[item.id] = selectMap.value[item.id] ? "" : item.id;
  // if (selectMap.value[item.id]) {
  //   selectMap.value[item.id] = null
  // } else {
  //   options.forEach(opt => {
  //     selectMap.value[opt.id] = opt.id === item.id ? item.id : null
  //   })
  // }
}
const emits = defineEmits(["change", "close"]);
// const useCustomer = () => ({})
const { update } = useCustomerApi();
async function confirm() {
  const tagIds = Object.values(selectMap.value).filter(Boolean);
  if (props.customerHandle) {
    emits("change", tagIds);
    return;
  }
  const { success, message } = await update(props.memberId, { tagIds });
  if (success) {
    emits("change", { tagIds });
    close();
  } else ElMessage.error(message);
}
function close() {
  emits("close");
}

watch(
  () => props.visible,
  (n) => {
    if (n) {
      const data = Array.isArray(props.value) ? props.value : [];
      selectMap.value = data.reduce((val, i) => {
        val[i] = i;
        return val;
      }, {});
    }
  }
);
</script>
<style lang="scss" scoped>
.tag-item {
  border: 1px solid #eee;
  margin-bottom: 12px;
  margin-right: 12px;

}

.tag-group {
  margin-bottom: -12px;
}

.tag-item.active {
  background-color: var(--yc-primary-color);
  border-color: var(--yc-primary-color);
  color: #fff;
}
</style>