<template>
  <my-layout class="pt-10px" bg-fff>
    <layout-item v-if="operateType === 'add'">
      <!-- <div font-18 font-semibold p-15>新增</div> -->
    </layout-item>
    <layout-main ref="mainRef">
      <el-button v-if="operateType === 'edit' && isRead && !immediateEdit" text type="primary" class="editPointer" @click="editAction">编辑</el-button>
      <div px-15 class="pb-50px">
        <el-form v-if="isCanEdit" key="editForm" :label-width="200" label-suffix="：" ref="formRef" :model="form">
          <div v-for="(item, idx) in selfTemplateList" :key="item.type">
            <div v-if="item.templateList && item.templateList.length > 0">
              <div class="group-title pt-15px">{{ item.name }}</div>
              <el-row>
                <template v-for="field in item.templateList" :key="field.sort">
                  <el-col v-bind="Object.keys(col).length !== 0 && isCanEdit ? col : field.col" v-if="showField(field)">
                    <el-form-item :label="field.name" class="el-form-item--autoHeight" :prop="field.title" :rules="ruleGroup[idx][field.title]">
                      <form-cell :item="field" :value="form[field.title]" class="w-full" @change="change" :isRead="!isCanEdit" :form="form"></form-cell>
                    </el-form-item>
                  </el-col>
                </template>
              </el-row>
            </div>
          </div>
          <slot name="other"></slot>
        </el-form>
        <el-form v-else key="displayForm" :label-width="160" label-suffix="：" ref="formRef" :model="form">
          <div v-for="(item, idx) in selfTemplateList" :key="item.type">
            <div v-if="item.templateList && item.templateList.length > 0">
              <div class="group-title">{{ item.name }}</div>
              <el-row>
                <template v-for="field in item.templateList" :key="field.sort">
                  <el-col v-bind="Object.keys(col).length !== 0 && isCanEdit ? col : field.col" v-if="showField(field)">
                    <el-form-item :label="field.name" class="el-form-item--autoHeight" :prop="field.title" :rules="ruleGroup[idx][field.title]">
                      <form-cell :item="field" :value="form[field.title]" class="w-full" @change="change" :isRead="!isCanEdit" :form="form"></form-cell>
                    </el-form-item>
                  </el-col>
                </template>
              </el-row>
            </div>
            <customer-transfer :customerId="form._id" v-if="item.type === 'internalTemplate'"></customer-transfer>
          </div>
          <slot name="other"></slot>
        </el-form>
      </div>
    </layout-main>
    <layout-item v-if="isCanEdit">
      <div text-center common-shadow--r py-10 class="relative z-2">
        <el-button class="w-100px" plain @click="onClose()">取消</el-button>
        <el-button v-if="canRemove" type="danger" class="w-100px" @click="remove()">删除</el-button>
        <el-button class="w-100px" :loading="loading" type="primary" @click="save()">保存</el-button>
      </div>
    </layout-item>
  </my-layout>
  <customer-repeat :visible="repeatVisible" :customer="customer" @acceptCustomer="acceptCustomer" @close="repeatVisible = false" :operateType="operateType" :repeatType="repeatType"></customer-repeat>
  <mobile-repeat :visible="mobileRepeatVisible" :customers="mobileReatCustomers" @close="mobileRepeatVisible = false"></mobile-repeat>
</template>
<script setup>
import { computed, onMounted, ref, watch } from "vue";
import dayjs from "dayjs";
import formCell from "@/components/form-cell";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import validate from "@/utils/validate";
import { memberStore } from "@/store/member";
import { storeToRefs } from "pinia";
import customerRepeat from "../components/customer-repeat/index.vue";
import repeatComponentApi from "@/views/member/components/customer-repeat/api";
import mobileRepeat from "@/components/mobile-repeat/index.vue";
import mobileRepeatApi from "@/components/mobile-repeat/api";
import customerTransfer from "@/components/customer-transfer";
import { ElMessage } from "element-plus";
import specialRule from "./special-rule";
const form = ref({});
const isRead = ref(true);
const loading = ref(false);
const { repeatVisible, customer, getReatCustomer, idCardIsRepeat, repeatType } = repeatComponentApi();
const { mobileReatCustomers, mobileRepeatVisible, getMobileReatCustomer } = mobileRepeatApi();
const { corpInfo } = storeToRefs(memberStore());
const emits = defineEmits(["onClose", "onSuccess", "onRemove"]);
const props = defineProps({
  operateType: {
    type: String,
    default: "add",
  },
  templateList: {
    type: Array,
    default: () => [],
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
  headerHeight: {
    type: Number,
    default: 60,
  },
  col: {
    type: Object,
    default: () => ({}),
  },
  styleClass: {
    type: String,
    default: "",
  },
  canRemove: {
    type: Boolean,
    default: false,
  },
  filterRule: {
    type: Object,
    default: () => ({}),
  },
});
let editFieleds = {};
const mainRef = ref();

function scollToTop() {
  mainRef.value.scrollTo(0);
}

watch(
  () => [props.templateList, props.formData],
  async ([, formData]) => {
    form.value = JSON.parse(JSON.stringify(formData));
    isRead.value = true;
  },
  { immediate: true, deep: true }
);

const selfTemplateList = computed(() => {
  return props.templateList.map((temp) => {
    const item = { ...temp };
    let list = Array.isArray(item.templateList) ? item.templateList : [];
    list = list.filter((i) => {
      const filterOnlyRead = props.operateType === "add" ? item.operateType !== "onlyRead" : true;
      const ruleFilter = props.filterRule && typeof props.filterRule[i.title] === "function" ? props.filterRule[i.title](form.value) : true;
      return filterOnlyRead && ruleFilter;
    });
    return { ...item, templateList: list };
  });
});
const ruleGroup = computed(() => {
  return selfTemplateList.value.map((i) => {
    const templateList = Array.isArray(i.templateList) ? i.templateList : [];
    return templateList.reduce((val, item) => {
      if (item.title && typeof specialRule[item.type] === "function") {
        val[item.title] = specialRule[item.type](form, item.required);
      } else if (item.title) {
        val[item.title] = { required: item.required, trigger: "blur", message: `请输入${item.name}` };
      }
      return val;
    }, {});
  });
});

function showField(item) {
  return (!item.referenceField || (item.referenceField && form.value[item.referenceField] === item.referenceValue)) && item.fieldStatus !== "disable";
}
const isCanEdit = computed(() => {
  return props.operateType === "add" || (props.operateType === "edit" && !isRead.value);
});
function change(item) {
  changeForm(item);
  if (props.operateType === "edit") changeEditFieleds(item);
}

async function changeForm(item) {
  const { title, value } = item;
  if (title === "reference") {
    form.value = Object.assign(form.value, value);
    return;
  }
  if (title === "phone1" || title === "phone2" || title === "phone3") {
    const { phone, note } = value;
    form.value[title] = phone;
    form.value[title + "Note"] = note;
    return;
  }
  if (title === "idCard") {
    // 身份证性别和年龄
    //身份证号获取出生日期
    if (value.length === 18) {
      !customer.value.idCard && getReatCustomer(value, title);
      form.value.age = validate.getAge(value);
      form.value.sex = validate.getGender(value);
      form.value.birthday = validate.getBirthdateFromId(value);
    } else {
      form.value.age = "";
      form.value.sex = "";
      form.value.birthday = "";
    }
  }
  if (title === "customerNumber" && corpInfo.value.isConnectHis) {
    getReatCustomer(value, title);
  }
  if (title === "mobile" && validate.isMobile(value)) {
    getMobileReatCustomer(value);
  }
  if (title === "birthday" && value && dayjs(value).isValid()) {
    form.value.age = getAgeFromBirthday(dayjs(value).format("YYYY-MM-DD"));
  }
  form.value[title] = value;
}
// 接收数据库里的档案数据
function acceptCustomer() {
  form.value = JSON.parse(JSON.stringify(customer.value));
  if (form.value.idCard) {
    form.value.age = validate.getAge(form.value.idCard);
    form.value.sex = validate.getGender(form.value.idCard);
    form.value.birthday = validate.getBirthdateFromId(form.value.idCard);
    form.value.relationship = "本人";
  }
  if (props.operateType === "edit") editFieleds = JSON.parse(JSON.stringify(form.value));
  repeatVisible.value = false;
}

function changeEditFieleds(item) {
  const { title, value } = item;
  if (title === "reference") {
    editFieleds = Object.assign(editFieleds, value);
    return;
  }
  if (title === "phone1" || title === "phone2" || title === "phone3") {
    const { phone, note } = value;
    editFieleds[title] = phone;
    editFieleds[title + "Note"] = note;
    return;
  }
  if (title === "idCard") {
    // 身份证性别和年龄
    //身份证号获取出生日期
    if (value.length === 18) {
      editFieleds.age = validate.getAge(value);
      editFieleds.sex = validate.getGender(value);
      editFieleds.birthday = validate.getBirthdateFromId(value);
    } else {
      editFieleds.age = "";
      editFieleds.sex = "";
      editFieleds.birthday = "";
    }
  }
  editFieleds[title] = value;
}
function editAction() {
  isRead.value = false;
}
// 保存
const formRef = ref(null);
async function save() {
  formRef.value.validate(async (valid) => {
    if (valid) {
      if (props.operateType !== "edit" && idCardIsRepeat.value) {
        ElMessage.error("该档案已经存在，不允许重复添加");
        return;
      }
      let params = props.operateType === "edit" ? editFieleds : form.value;
      // 手机号格式验证
      if (params.mobile && !validate.isMobile(params.mobile)) {
        ElMessage.error("请输入正确的手机号");
        return;
      }
      if (params.phone1 && !validate.isMobile(params.phone1)) {
        ElMessage.error("请输入正确的手机号");
        return;
      }
      if (params.phone2 && !validate.isMobile(params.phone2)) {
        ElMessage.error("请输入正确的手机号");
        return;
      }
      if (params.phone3 && !validate.isMobile(params.phone3)) {
        ElMessage.error("请输入正确的手机号");
        return;
      }
      // 身份证格式验证
      if (params.idCard && !validate.isChinaId(params.idCard)[0]) {
        ElMessage.error("请输入正确的身份证号");
        return;
      }
      loading.value = true;
      emits("onSuccess", params, (type) => {
        loading.value = false;
        if (type === "success") isRead.value = true;
      });
      editFieleds = {};
    }
  });
}

function onClose() {
  // 返回到客户列表页面
  editFieleds = {};
  isRead.value = true;
  form.value = JSON.parse(JSON.stringify(props.formData));
  emits("onClose", props.operateType);
}

function remove() {
  emits("onRemove");
}

function getAgeFromBirthday(birthday) {
  const today = new Date();
  const birthDate = new Date(birthday);
  let age = today.getFullYear() - birthDate.getFullYear();
  const m = today.getMonth() - birthDate.getMonth();
  if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return Math.max(age, 1);
}

function getInitData(data = {}) {
  repeatVisible.value = false;
  idCardIsRepeat.value = false;
  customer.value = data;
  form.value = JSON.parse(JSON.stringify(data));
}

defineExpose({
  cancelEdit: () => (isRead.value = true),
  getInitData,
  editAction,
  scollToTop,
});
</script>

<style lang="scss">
.group-title {
  padding: 0 12px 16px;
  color: var(--yc-main-text-color);
  font-weight: 600;
  font-size: 14px;
}

.editPointer {
  position: absolute;
  right: 20px;
  top: 0;
  z-index: 1;
}

.el-form-item__label {
  line-height: normal;
  align-items: center;
}
</style>
