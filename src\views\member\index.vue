<template>
  <my-layout v-loading="loading">
    <layout-item>
      <div common-shadow class="bg-white rounded mb-10px">
        <customer-filter ref="filterRef" :init-value="initValue" :currentTab="currentTab" :tabs="tabs" :layout="layout" @changeTab="changeTab" @search="search($event)" />
      </div>
    </layout-item>
    <layout-main :scroll="false" class="bg-white rounded">
      <customer-table canAdd canBatchEdit canBatchMove canBatchShare canSelect ref="customerTableRef" v-model:loading="loading" :columns="columns" :params="tableParams" :actionColumnWidth="120">
        <template #fixedRightColumn="{ row }">
          <el-button text type="primary" @click="showVisitPlan(row)">回访计划</el-button>
        </template>
      </customer-table>
    </layout-main>
  </my-layout>
  <component v-if="ManagePlanDrawer" :customer="currentCustomer" :is="ManagePlanDrawer" :visible="drawerVisible" @close="closeDrawer" />
</template>
<script setup>
import { computed, nextTick, onMounted, onBeforeMount, ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { configStore } from "@/store/config";
import { teamStore } from "@/store/team";
import useManagePlanDrawer from "@/composables/useManagePlanDrawer";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import CustomerFilter from "./components/cutomer-table/customer-filter/customer-filter.vue";
import CustomerTable from "./components/cutomer-table/table/table.vue";

const config = configStore();
const loading = ref(false);
const { getStage } = config;
const { stageList } = storeToRefs(config);
const { currentTeam } = storeToRefs(teamStore());
const customerTableRef = ref();
const filterParams = ref({});
const initValue = ref({});
const currentTab = ref("team");
const currentCustomer = ref({});
watch(currentTeam, (newItem, oldItem) => {
  if (newItem && oldItem && newItem.teamId !== oldItem.teamId) {
    search();
  }
});
const tabs = [
  { label: "团队", name: "team" },
  { label: "我的", name: "myCustomer" },
];
const teamCustomerLayout = ["sex", "age", "currentStage", "customerSource", "customerTags", "group", "plan", "createTimeRange", "arriveTimeRange", "arriveStatusRange", "serviceTimeRange", "teammatePicker", "customDaysRange"];
const myCustomerLayout = ["sex", "age", "currentStage", "customerSource", "customerTags", "group", "plan", "createTimeRange", "arriveTimeRange", "arriveStatusRange", "serviceTimeRange", "customDaysRange"];
const layout = computed(() => {
  return currentTab.value === "team" ? teamCustomerLayout : myCustomerLayout;
});
const columns = ["name", "ageSex", "mobile", "wxContact", "concactUser", "personResponsible", "tagIds", "notes", "customerStage", "customerSource", "group", "plan", "createTime", "arriveTime", "serviceTime"];
const { visible: drawerVisible, ManagePlanDrawer, openDrawer, closeDrawer } = useManagePlanDrawer();

function changeTab(val) {
  currentTab.value = val;
}
const tableParams = computed(() => ({
  ...filterParams.value,
  teamId: currentTeam.value.teamId,
  showHasPlan: "YES",
  customerType: currentTab.value,
}));
// 搜索方法
async function search(data = {}) {
  const { params, reset = false } = data;
  filterParams.value = params;
  await nextTick();
  customerTableRef.value && customerTableRef.value.search(reset);
}

function showVisitPlan(row) {
  currentCustomer.value = row;
  openDrawer();
}

// 生命周期钩子
const filterRef = ref();
onMounted(async () => {
  if (stageList.value.length === 0) await getStage();
  filterRef.value && filterRef.value.search();
});

onBeforeMount(() => {
  currentTab.value = history.state.tab || "team";
  if (history.state.dates && typeof history.state.dates === "string") {
    const dates = history.state.dates.split(",");
    initValue.value["createTime"] = dates;
  }
});
</script>
