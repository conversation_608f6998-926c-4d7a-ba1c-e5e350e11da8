<template>
  <el-dialog :model-value="props.visible" :close-on-click-modal="false" :width="props.width" :title="title" @close="close">
    <div px-15>
      <div class="flex pb-10px" border-bottom>
        <div class="">{{ customer.isConnectHis && !customer._id ? "身份证号：" : "重复号码：" }}</div>
        <div>{{ customer.idCard }}</div>
      </div>
      <div class="flex justify-between pt-10px">
        <div>
          <div class="flex">
            <div>客户姓名：</div>
            <div>{{ customer.name }}</div>
          </div>
          <div class="flex pt-15px">
            <div>性别/年龄：</div>
            <div v-if="customer.idCard">{{ validate.getAge(customer.idCard) }}/{{ validate.getGender(customer.idCard) }}</div>
          </div>
          <div class="flex pt-15px" v-if="customer.customerNumber">
            <div>his门诊号：</div>
            <div>{{ customer.customerNumber }}</div>
          </div>
        </div>
        <div>
          <el-button type="primary" class="mt-15px" @click="acceptCustomer" v-if="operateType === 'add' || inHospitalCustomer">获取档案</el-button>
        </div>
      </div>
      <div class="flex pt-15px">
        <div>手机号码：</div>
        <div>{{ customer.mobile }}</div>
      </div>
      <div class="flex pt-15px" v-if="teamNames">
        <div>服务团队：</div>
        <div>{{ teamNames }}</div>
      </div>
    </div>
  </el-dialog>
</template>
    <script setup>
import { computed } from "vue";
import validate from "@/utils/validate";
import { teamStore } from "@/store/team";
const { allTeams } = teamStore();
const props = defineProps({
  visible: { type: Boolean, defautl: false },
  customer: { type: Object, default: {} },
  width: { type: Number, default: 420 },
  operateType: { type: String, default: "add" },
});

const title = computed(() => {
  return inHospitalCustomer.value ? "院内档案" : "身份证号重复";
});

const inHospitalCustomer = computed(() => {
  return props.customer.isConnectHis && !props.customer._id;
});
const emits = defineEmits(["close", "acceptCustomer"]);
function close() {
  emits("close");
}
function acceptCustomer() {
  emits("acceptCustomer");
}
const teamNames = computed(() => {
  let teamId = props.customer.teamId;
  if (!teamId) return "";
  if (typeof teamId === "string") teamId = [teamId];
  return allTeams
    .filter((team) => teamId.includes(team.teamId))
    .map((team) => team.name)
    .join("、");
});
</script>
