<template>
  <el-dialog :model-value="visible" :width="width" title="套餐详情" @close="close()">
    <el-scrollbar wrap-class="el-dialog-body-full-scroll text-14px">
      <el-form class="px-15px pb-15px" label-position="left" :label-width="100" label-suffix="：">
        <el-form-item class="mb-0px" label="套餐名称">
          <template #label> <span class="text-gray-500">套餐名称：</span> </template>
          <div class="text-14px" color-normal>{{ data.name }}</div>
        </el-form-item>
        <el-form-item class="mb-0px" label="套餐描述">
          <template #label> <span class="text-gray-500">套餐描述：</span> </template>
        </el-form-item>
        <div class="my-5px text-14px leading-5" color-normal>
          {{ data.desc }}
        </div>
        <el-form-item class="mb-0px" v-if="data.hasValidDays && data.validDays > 0" label="套餐有效期">
          <template #label> <span class="text-gray-500">套餐有效期：</span> </template>
          <div class="text-14px" color-normal> {{ data.validDays }}天</div>
        </el-form-item>
        <el-form-item class="mb-0px" v-if="data.enableDiscount && data.minDiscount > 0" label="折扣">
          <template #label> <span class="text-gray-500">折扣：</span> </template>
          <div class="text-14px" color-normal>支持折扣 最低折扣{{ data.minDiscount }}折</div>
        </el-form-item>
        <div class="mb-15px h-1px border-b border-gray-200"></div>
        <el-form-item class="mb-0px" label="套餐价格">
          <template #label> <span class="text-gray-500">套餐价格：</span> </template>
          <div class="text-14px" color-normal> <span class="text-orange-500">￥{{ data.packagePrice }}</span></div>
        </el-form-item>
        <el-form-item class="mb-0px" label="套餐组合形式" :label-width="120">
          <template #label> <span class="text-gray-500">套餐组合形式：</span> </template>
          <div class="text-14px" color-normal>
            <span class="mr-10px">{{ProjectPackageTypeMap[data.packageType] }}</span>
            <span v-if="data.packageType === 'free'"> {{ data.projects.length }} 选 {{ data.projectNumber }}</span>
          </div>
        </el-form-item>
        <el-form-item class="mb-0px" label="套餐项目">
          <template #label> <span class="text-gray-500">套餐项目：</span> </template>
        </el-form-item>
        <el-table class="mt-5px" :data="tableProjects">
          <el-table-column prop="projectName" label="项目名称" :min-width="240" />
          <el-table-column prop="price" label="原单价" :min-width="100" />
          <el-table-column prop="number" label="数量" :min-width="120" />
          <el-table-column v-if="data.packageType === 'fixed'" prop="discount" label="折扣" :min-width="120" />
          <el-table-column v-if="data.packageType === 'fixed'" prop="totalPrice" label="总价" :min-width="120" />
          <el-table-column prop="required" align="center" label="必选" width="80">
            <template #default="{ row }">
              <el-checkbox :model-value="true" />
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button plain class="w-100px" type="primary" @click="close()">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed } from 'vue'
import BigNumber from "bignumber.js";
import { ProjectPackageTypeMap } from "@/baseData";

const emits = defineEmits(["close"]);
const props = defineProps({
  data: { type: Object, default: () => ({}) },
  visible: { type: Boolean, default: false },
  width: { type: [String, Number], default: 600 },
})
const tableProjects = computed(() => {
  const list = Array.isArray(props.data.projects) ? props.data.projects : [];
  return list.map(i => {
    const num = i.number > 0 ? Number(i.number) : 0;
    const price = i.price > 0 ? Number(i.price) : 0;
    const discount = props.data.packageType === 'free' ? 1 : (i.discount > 0 ? new BigNumber(i.discount).div(10).toNumber() : 0);
    return {
      ...i,
      totalPrice: (num > 0 && price > 0 && discount > 0) ? new BigNumber(num).times(price).times(discount).toNumber() : '',
    }
  })
})
function close() {
  emits("close");
}
</script>