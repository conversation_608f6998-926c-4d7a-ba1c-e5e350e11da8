<template>
  <el-select v-if="show" clearable :model-value="showValue" popper-class="auto-el-option-height" filterable remote
    reserve-keyword :placeholder="placeholder" :disabled="disabled" :remote-method="searchCustomer" :loading="loading"
    @change="changeCustomer" @clear="handlerClear()">
    <template #empty>
      <div class="py-10px text-center text-14px text-gray-400" :style="optionStyle">{{ loading ? "请求中" : "暂无数据" }}</div>
    </template>
    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" :style="optionStyle">
      <div flex items-center>
        <user-avatar flex-shrink-0 :sex="item.sex" />
        <div flex-grow class="w-0 mx-10px customer-info">
          <div class="truncate">
            <span font-16>{{ item.name }}</span>
            <span color-666 font-12 class="ml-6px">{{ item.sex || "" }}</span>
          </div>
          <div font-14 color-666 class="truncate">
            <el-icon class="mr-4px">
              <CreditCard />
            </el-icon>
            {{ item.showIdCard }}
          </div>
          <div font-14 color-666 class="truncate">
            <el-icon class="mr-4px">
              <Iphone />
            </el-icon>
            {{ item.showMobile }}
          </div>
        </div>
        <div flex-shrink-0>
          <el-radio-group :model-value="value">
            <el-radio :label="item._id">{{ " " }}</el-radio>
          </el-radio-group>
        </div>
      </div>
    </el-option>
  </el-select>
</template>
<script setup>
import { computed, nextTick, onMounted, ref, toRefs } from "vue";
import { watchDebounced } from "@vueuse/core";
import { getCorpCustomer, getCustomerInfoById } from "@/api/member";
import { maskIdNo, maskPhone } from "@/utils";

import UserAvatar from "@/components/user-avatar";

const emits = defineEmits(["change"]);
const props = defineProps({
  defaultName: { default: "" },
  optionWidth: { type: Number, default: 0 },
  disabled: { type: false },
  placeholder: { default: "请搜索" },
  value: { default: "" },
});
const { disabled, placeholder, value, optionWidth } = toRefs(props);
const optionStyle = computed(() => (optionWidth.value > 0 ? { width: optionWidth.value + "px" } : ""));
const showValue = computed(() => currentCustomer.value._id || ""); // 避免没有查询到客户 下拉框选择_id

const currentCustomer = ref({});
const customerList = ref([]);
const searchText = ref("");
const loading = ref(false);
const options = computed(() => {
  const list = currentCustomer.value._id ? [currentCustomer.value] : [];
  return [...list, ...customerList.value.filter((i) => i._id !== currentCustomer.value._id)].map((i) => ({
    ...i,
    showIdCard: maskIdNo(i.idCard),
    showMobile: maskPhone(i.mobile),
    value: i._id,
    label: i.name,
  }));
});
function changeCustomer(value) {
  const customer = options.value.find((i) => i.value === value);
  if (customer) {
    currentCustomer.value = customer;
    emits("change", customer);
  }
}

function searchCustomer(val) {
  if (val) {
    loading.value = true;
    searchText.value = val;
  }
}
async function getMembers() {
  const { data } = await getCorpCustomer({ name: searchText.value, page: 1, pageSize: 99, corpId: localStorage.getItem("corpId") });
  customerList.value = Array.isArray(data.data) ? data.data : [];
  return customerList.value;
}

watchDebounced(
  searchText,
  async () => {
    if (searchText.value) {
      await getMembers();
    }
    loading.value = false;
  },
  { debounce: 1000 }
);

const show = ref(false);
onMounted(() => {
  if (props.value && !currentCustomer._id) {
    currentCustomer.value = { _id: props.value, name: props.defaultName, label: props.defaultName, value: props.value };
    initCurrentCustomer(props.value);
  } else {
    show.value = true;
  }
});
async function initCurrentCustomer(id) {
  loading.value = true;
  const { data } = await getCustomerInfoById(localStorage.getItem("userId"), {
    _id: id,
  });
  const customer = data && data.data[0] ? data.data[0] : {};
  currentCustomer.value = customer;
  await nextTick();
  changeCustomer(customer);
  loading.value = false;
  show.value = true;
}

function handlerClear() {
  currentCustomer.value = {};
  emits("change", { name: "", _id: "" });
}
</script>
<style lang="scss" scoped>
.auto-el-option-height .el-select-dropdown__item {
  height: auto;
}

.auto-el-option-height .customer-info {
  padding: 6px 0;
  line-height: 1.5em;
  color: rgba(0, 0, 0, 0.9);
  font-weight: normal;
}
</style>
