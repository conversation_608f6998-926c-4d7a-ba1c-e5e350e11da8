<template>
  <div class="flex flex-col items-center justify-center h-screen">
    <img src="@/assets/zanwushuju.png" class="w-120px mb-10px" />
    <div class="text-14px mb-40px">暂无数据</div>
    <div class="w-160px text-center text-14px py-8px px-15px rounded-4px text-white bg-blue" @click="toCreate('link')">
      点击发送建档链接
    </div>
    <div class="w-160px text-center mt-10px text-14px py-8px px-15px rounded-4px text-white bg-orange"
      @click="toCreate('selectTeam')">
      新建档案
    </div>
  </div>
</template>
<script setup>
defineProps({
  unionId: { type: String, default: '' },
  teams: { type: Array, default: () => [] }
})
const emits = defineEmits(['create']);
function toCreate(type) {
  emits('create', type)
}
</script>
<style lang="scss" scoped>
.bg-blue {
  background: #328dff;
}

.bg-orange {
  background: #E6A23C;
}
</style>
