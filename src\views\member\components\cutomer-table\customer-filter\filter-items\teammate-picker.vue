<template>
  <filter-item label="责任人" :text="text">
    <el-checkbox v-if="memberList.length" v-model="allChecked" label="全部" />
    <el-checkbox-group v-if="options.length" v-model="userIds">
      <div v-for="item in options" :key="item.value">
        <el-checkbox :label="item.value" :value="item.value">
          <span v-if="item.label">{{ item.label }}</span>
          <ww-user v-else :openid="item.value"></ww-user>
        </el-checkbox>
      </div>
    </el-checkbox-group>
  </filter-item>
</template>
<script setup>
import { computed, ref } from "vue";
import { storeToRefs } from "pinia";
import { teamStore } from "@/store/team";

import FilterItem from "./filter-item.vue";

const { currentTeam } = storeToRefs(teamStore());
const userIds = ref([]);
const text = computed(() => {
  const hasNoUser = userIds.value.includes("NO");
  const length = userIds.value.length - (hasNoUser ? 1 : 0);
  return length > 0 ? `已选${length}人` : hasNoUser ? "无责任人" : "";
});
const memberList = computed(() => (Array.isArray(currentTeam.value.memberList) ? currentTeam.value.memberList.map((i) => ({ value: i })) : []));
const options = computed(() => [{ label: "无责任人", value: "NO" }, ...memberList.value]);

const allChecked = computed({
  get() {
    return userIds.value.length === options.value.length && options.value.length > 0;
  },
  set(value) {
    if (value) {
      userIds.value = options.value.map((item) => item.value);
    } else {
      userIds.value = [];
    }
  },
});

function getParam() {
  if (userIds.value.length) return { personResponsibles: userIds.value };
  return {};
}

function reset() {
  userIds.value = [];
}

defineExpose({
  getParam,
  reset,
});
</script>
<style lang="scss" scoped></style>
