<template>
  <el-dialog title="团队回访计划" :model-value="visible" :width="width" @close="close()">
    <div class="pb-10px border-b border-gray-200 px-15px">
      <el-input v-model.trim="keyword" placeholder="输入机构回访计划名称搜索" :prefix-icon="Search" clearable />
    </div>
    <el-scrollbar v-loading="loading" style="height:40vh" color-normal>
      <empty-data v-if="list.length === 0" top="10vh" title="暂无数据" padding="0 0 15px" text-top="10px" :image-width="120"></empty-data>
      <div v-for="(item, idx) in list" :key="`search_${item._id}`">
        <div class="py-10px px-15px cursor-pointer flex items-center border-b border-gray-200" @click="toggle(item)">
          <div class="flex-grow w-0 break-all mr-15px">{{ item.planName }}</div>
          <el-checkbox :model-value="Boolean(map[item._id])" />
        </div>
      </div>
      <div v-if="more"
        class="mt-10px px-10px flex items-center justify-center py-4px cursor-pointer text-gray-400 hover:text-blue-500"
        @click="loadMore()">
        <div class="flex-grow h-1px bg-gray-200"></div>
        <div class="text-14px ml-10px">加载更多</div>
        <el-icon class="flex-shrink-0 ml-5px mr-10px text-14px">
          <ArrowDownBold />
        </el-icon>
        <div class="flex-grow h-1px bg-gray-200"></div>
      </div>
    </el-scrollbar>
    <template #footer>
      <span class="block text-center">
        <el-button class="w-100px" @click="close()">取消</el-button>
        <el-button class="w-100px" type="primary" @click="confirm()">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { watchDebounced } from "@vueuse/core";
import { getManagementPlanList } from "@/api/managementPlan";
import { memberStore } from "@/store/member";
import useElPagination from "@/hooks/useElPagination";

import { Search } from "@element-plus/icons-vue";
import EmptyData from "@/components/empty-data.vue";

const emits = defineEmits(['close', 'change'])
const props = defineProps({
  data: [],
  visible: { type: Boolean, default: false },
  width: { type: [String, Number], default: '600px' },
  teamId: { type: String, default: '' }
})
const keyword = ref('');
const total = ref(false);
const loading = ref(false)
const { memberInfo } = storeToRefs(memberStore())
const { list, page, pageSize, changePage, more, loadMore } = useElPagination(getPlans);

const selections = ref([]);
const map = computed(() => {
  return selections.value.reduce((acc, cur) => {
    acc[cur._id] = true
    return acc
  }, {})
})

function close() {
  emits('close')
}

function confirm() {
  emits('change', selections.value[0])
  close()
}

function toggle(item) {
  if (map.value[item._id]) {
    selections.value = []
  } else {
    selections.value = [item]
  }
}

async function getPlans() {
  loading.value = true
  const params = {
    page: page.value,
    pageSize: pageSize.value,
    planName: keyword.value.trim(),
    planStatus: true,
    corpId: memberInfo.value.corpId,
    userId: memberInfo.value.userid,
    planType: 'team',
    teamId: props.teamId
  }
  const { data } = await getManagementPlanList(params);
  const arr = Array.isArray(data.data) ? data.data : [];
  list.value = page.value === 1 ? arr : [...list, ...arr];
  more.value = total.value > list.value.length;
  loading.value = false
}

watchDebounced(() => props.visible, n => {
  if (n) {
    keyword.value = '';
    changePage(1)
  }
})
watchDebounced(keyword, n => {
  loading.value = true
  changePage(1)
}, { debounce: 500 })
</script>
<style lang="scss" scoped></style>
