<template>
  <div class="report-container">
    <div class="report-header">
      <!-- <h2 class="report-title">现场咨询工作业绩排行表</h2>
      <div class="report-date">{{ formatDateRange }}</div> -->
      <div class="report-actions">
        <div class="filter-section">
          <div class="filter-row">
            <div class="filter-item date-range">
              <span class="filter-label">日期：</span>
              <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" :shortcuts="dateShortcuts" :disabledDate="disabledDate" @change="handleDateChange" />
            </div>

            <div class="filter-item">
              <check-box-filter v-model="consultantFilter" label="咨询师" :list="consultantList" v-if="isManager" @clear="handleConsultantFilterClear">
                <template #item="{ item }">
                  <ww-user :openid="item.value"></ww-user>
                </template>
              </check-box-filter>
            </div>

            <div class="filter-item">
              <check-box-filter v-model="consultStageFilter" label="接诊类型" :list="ConsultStage" />
            </div>

            <!-- <div class="filter-item">
              <base-filter-item :clearable="projectCategories.length > 0" class="cursor-pointer" label="项目分类" :text="categoryText" @clear="clearCategories" @onClick="showCategoryPicker()" />
            </div>
            <div class="filter-item">
              <base-filter-item :clearable="projectFilter.length > 0" class="cursor-pointer" label="开单项目" :text="projectText" @clear="clearProjects" @onClick="showProjectModal()" />
            </div> -->
            <div class="filter-actions">
              <!-- 移除查询按钮 -->
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button type="success" @click="exportToExcel">导出Excel</el-button>
              <el-button @click="resetFilters">重置</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="table-container" v-loading="loading" element-loading-text="数据加载中...">
      <table class="ranking-table">
        <colgroup>
          <col style="width: 6%" />
          <col style="width: 12%" />
          <col style="width: 10%" />
          <col style="width: 10%" />
          <col style="width: 8%" />
          <col style="width: 14%" />
          <col style="width: 14%" />
          <col style="width: 14%" />
          <col style="width: 12%" />
        </colgroup>
        <thead>
          <tr>
            <th>排名</th>
            <th>咨询师</th>
            <th>咨询人次</th>
            <th>成交人次</th>
            <th>成交率</th>
            <th>成交金额</th>
            <th>退款金额</th>
            <th>实收金额</th>
            <th>客单价</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in tableData" :key="index">
            <td>{{ index + 1 }}</td>
            <td>{{ item.consultantName }}</td>
            <td>{{ item.consultCount }}</td>
            <td>{{ item.successCount }}</td>
            <td>{{ formatPercent(item.successRate) }}</td>
            <td class="amount">{{ formatCurrency(item.successAmount) }}</td>
            <td class="amount">{{ formatCurrency(item.refundAmount) }}</td>
            <td class="amount">{{ formatCurrency(item.netAmount) }}</td>
            <td class="amount">{{ formatCurrency(item.avgOrderValue) }}</td>
          </tr>
          <tr class="total-row">
            <td colspan="2" class="total-label">合计：</td>
            <td>{{ totalConsultCount }}</td>
            <td>{{ totalSuccessCount }}</td>
            <td>{{ formatPercent(totalSuccessRate) }}</td>
            <td class="amount">{{ formatCurrency(totalSuccessAmount) }}</td>
            <td class="amount">{{ formatCurrency(totalRefundAmount) }}</td>
            <td class="amount">{{ formatCurrency(totalNetAmount) }}</td>
            <td class="amount">{{ formatCurrency(totalAvgOrderValue) }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="footer-info">
      <span>共 {{ tableData.length }} 条记录</span>
      <span class="date-limit-info">* 查询限制为一个月内的数据</span>
    </div>
  </div>
  <project-list-picker :value="projectFilter" :visible="projectModalVisible" :width="projectModalWidth" :current-category="projectCategories" @close="closeProjectModal()" @change="changeProjects" />
  <category-picker ref="categoryPickerRef" :value="projectCategories" :visible="categoryPickerVisible" :width="categoryPickerWidth" @close="closeCategoryPicker" @change="changeCategories" @categoryChange="handleCategoryChange" />
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { storeToRefs } from "pinia";
import { staffStore } from "@/store/staff";
import { teamStore } from "@/store/team"; // 新增导入 teamStore
import { memberStore } from "@/store/member"; // 新增导入 memberStore
import { ElDatePicker, ElButton, ElMessage } from "element-plus";
import { getConsultantSalesRanking } from "@/api/bill-record";
import projectListPicker from "@/components/project-picker/project-list-picker.vue";
import categoryPicker from "@/components/project-picker/category-picker.vue";
import useModal from "@/hooks/useModal";
import { CheckBoxFilter, baseFilterItem } from "@/components/filter-bar";
import { ConsultStage } from "@/baseData"; // 导入 ConsultStage

const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());
const { currentTeamId, managerList } = storeToRefs(teamStore()); // 获取团队ID和主管列表
const { isAdmin } = storeToRefs(memberStore()); // 获取管理员状态
const { judgmentIsAdmin } = memberStore();

// 判断是否是主管权限
const isManager = computed(() => {
  return managerList.value.includes(90003) || isAdmin.value;
});

const selectedConsultants = ref([]);
const hasExplicitlyClearedConsultants = ref(false);

// consultantFilter 默认值逻辑调整为 computed
const consultantFilter = computed({
  get() {
    if (selectedConsultants.value.length > 0) {
      return selectedConsultants.value;
    }
    if (hasExplicitlyClearedConsultants.value) {
      return [];
    }
    const currentUserId = localStorage.getItem("userId");
    if (!isManager.value) {
      // 不是主管，只能看到自己
      return [currentUserId];
    } else {
      // 主管
      const inList = consultantList.value.some((i) => i.value === currentUserId);
      if (inList) {
        return [currentUserId];
      } else {
        // 展示全部
        return [];
      }
    }
  },
  set(val) {
    selectedConsultants.value = val;
    hasExplicitlyClearedConsultants.value = false;
  },
});

// 处理咨询师筛选器的清除事件
function handleConsultantFilterClear() {
  hasExplicitlyClearedConsultants.value = true;
  selectedConsultants.value = [];
}

const consultStageFilter = ref([]); // 添加接诊类型筛选变量

const consultantList = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("consultant")).map((i) => ({ value: i.userid }));
});

/* ===============  项目分类筛选 start ================= */
const projectCategories = ref([]);
const categoryPickerRef = ref(null);
const currentCategory = ref({});
const { close: closeCategoryPicker, show: showCategoryPicker, visible: categoryPickerVisible, width: categoryPickerWidth } = useModal(600);

const categoryText = computed(() => {
  if (projectCategories.value.length === 0) return "";
  if (projectCategories.value.length > 1) return `已选择${projectCategories.value.length}个分类`;
  return projectCategories.value[0].name;
});

function clearCategories() {
  projectCategories.value = [];
  if (categoryPickerRef.value) {
    categoryPickerRef.value.clear();
  }
  // 清空分类后自动触发查询
  handleSearch();
}

function changeCategories(data) {
  // 将分类数据处理为包含级别信息的数组
  projectCategories.value = data.categories;
  // 当分类变更时，清空已选项目
  projectFilter.value = [];
  // 变更后自动触发查询
  handleSearch();
}



function handleCategoryChange(category) {
  currentCategory.value = category;
  // 当前分类变化时，清空已选项目
  projectFilter.value = [];
}
/* ===============  项目分类筛选 end ================= */

/* ===============  项目筛选start ================= */
const projectFilter = ref([]);
const { close: closeProjectModal, show: showProjectModal, visible: projectModalVisible, width: projectModalWidth } = useModal(800);
const projectText = computed(() => {
  if (projectFilter.value.length === 0) return "";
  if (projectFilter.value.length > 1) return `已选择${projectFilter.value.length}个项目`;
  return projectFilter.value[0].projectName;
});

function changeProjects(data) {
  projectFilter.value = data.projects;
  closeProjectModal();
  // 修改项目后自动触发查询
  handleSearch();
}

function clearProjects() {
  projectFilter.value = [];
  // 清空项目后自动触发查询
  handleSearch();
}
/* ===============  项目筛选 end================= */

// 表格数据
const tableData = ref([]);
// 日期筛选
const dateRange = ref([]);
// 加载状态
const loading = ref(false);

// 修改查询参数构建逻辑，根据权限添加不同的筛选条件
async function handleGetConsultantSalesRanking() {
  loading.value = true;
  try {
    const params = {
      startDate: dateRange.value[0],
      endDate: dateRange.value[1],
      projectIds: projectFilter.value?.length > 0 ? projectFilter.value.map((proj) => proj._id) : undefined,
      consultStages: consultStageFilter.value.length > 0 ? consultStageFilter.value : undefined, // 添加接诊类型筛选条件
    };

    // 添加项目分类筛选
    if (projectCategories.value?.length > 0) {
      params.projectCategories = projectCategories.value.map((cat) => ({
        id: cat._id,
        level: cat.level,
      }));
    }

    // 根据权限添加咨询师筛选条件
    if (isManager.value) {
      params.consultantFilter = consultantFilter.value.length > 0 ? consultantFilter.value : undefined;
    } else {
      params.consultantFilter = [localStorage.getItem("userId")];
    }

    const { data, success, message } = await getConsultantSalesRanking(params);
    if (!success) {
      ElMessage.error(message);
      return;
    }
    tableData.value = data.data.map((i) => {
      const consultantName = staffList.value.find((staff) => staff.userid === i.consultant)?.anotherName || "未知医生";
      return {
        ...i,
        consultantName,
      };
    });
  } finally {
    loading.value = false;
  }
}

// 日期快捷选项
const dateShortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近半个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
];

// 禁用超过一个月范围的日期
const disabledDate = (time) => {
  if (!dateRange.value || dateRange.value.length === 0) {
    return false;
  }

  const startDate = new Date(dateRange.value[0]);
  const endDate = new Date(dateRange.value[1]);
  const currentDate = new Date(time);

  // 如果已选择了开始日期，限制结束日期不能超过开始日期后的31天
  if (dateRange.value.length === 1) {
    const oneMonthLater = new Date(startDate);
    oneMonthLater.setDate(startDate.getDate() + 31);
    return currentDate > oneMonthLater;
  }

  return false;
};

// 监听日期变化，确保不超过一个月，并自动触发查询
watch(dateRange, (newValue) => {
  if (newValue && newValue.length === 2) {
    const start = new Date(newValue[0]);
    const end = new Date(newValue[1]);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays > 31) {
      // 如果超过31天，将结束日期设置为开始日期后的31天
      const maxEnd = new Date(start);
      maxEnd.setDate(start.getDate() + 31);
      const formattedMaxEnd = formatDate(maxEnd);

      dateRange.value = [newValue[0], formattedMaxEnd];
      ElMessage.warning("查询范围已限制为一个月内");
    }
    handleSearch();
  }
});

// 格式化显示的日期范围
const formatDateRange = computed(() => {
  if (dateRange.value && dateRange.value.length === 2) {
    return `${dateRange.value[0]} 至 ${dateRange.value[1]}`;
  }
  return "全部日期";
});

// 格式化日期为YYYY-MM-DD
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  return `${year}-${month}-${day}`;
};

// 计算合计
const totalConsultCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + item.consultCount, 0);
});

const totalSuccessCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + item.successCount, 0);
});

const totalSuccessRate = computed(() => {
  if (totalConsultCount.value === 0) return 0;
  return totalSuccessCount.value / totalConsultCount.value;
});

const totalSuccessAmount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + item.successAmount, 0);
});

const totalRefundAmount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + item.refundAmount, 0);
});

const totalNetAmount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + item.netAmount, 0);
});

const totalAvgOrderValue = computed(() => {
  if (totalSuccessCount.value === 0) return 0;
  return totalSuccessAmount.value / totalSuccessCount.value;
});

// 格式化货币
const formatCurrency = (value) => {
  if (value === undefined || value === null || isNaN(value)) {
    return "¥0.00";
  }
  return new Intl.NumberFormat("zh-CN", {
    style: "currency",
    currency: "CNY",
    minimumFractionDigits: 2,
  }).format(value);
};

// 格式化百分比
const formatPercent = (value) => {
  return new Intl.NumberFormat("zh-CN", {
    style: "percent",
    minimumFractionDigits: 2,
  }).format(value);
};

// 处理日期变化
const handleDateChange = (val) => {
  if (val && val.length === 2) {
    const start = new Date(val[0]);
    const end = new Date(val[1]);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays > 31) {
      ElMessage.warning("查询范围已限制为一个月内");
    }
    // 日期选择器变化时的查询已通过 watch 处理
  }
};

// 监听咨询师筛选条件变化
watch(
  consultantFilter,
  () => {
    handleSearch();
  },
  { deep: true }
);

// 监听接诊类型筛选条件变化
watch(
  consultStageFilter,
  () => {
    handleSearch();
  },
  { deep: true }
);

// 监听团队ID变化，重新获取数据
watch(
  () => currentTeamId.value,
  () => {
    if (dateRange.value && dateRange.value.length === 2) {
      handleGetConsultantSalesRanking();
    }
  }
);

// 监听项目分类筛选器变化
watch(
  projectCategories,
  () => {
    if (projectCategories.value.length === 0) {
      // 如果分类被清空，可能需要也清空依赖于分类的项目筛选
      projectFilter.value = [];
    }
  },
  { deep: true }
);

// 处理查询
const handleSearch = () => {
  if (dateRange.value && dateRange.value.length === 2) {
    const start = new Date(dateRange.value[0]);
    const end = new Date(dateRange.value[1]);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    if (diffDays > 31) {
      ElMessage.warning("查询范围已限制为一个月内");
      return;
    }

    handleGetConsultantSalesRanking();
  } else {
    ElMessage.warning("请选择日期范围");
  }
};

// 重置筛选条件
const resetFilters = () => {
  // 设置默认日期范围为最近一个月
  const end = new Date();
  const start = new Date();
  start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);

  dateRange.value = [formatDate(start), formatDate(end)];
  projectFilter.value = [];
  projectCategories.value = []; // 重置项目分类筛选
  consultStageFilter.value = []; // 重置接诊类型筛选条件
  selectedConsultants.value = [];
  categoryPickerRef.value.clear();
  // 重置后可以立即查询
  handleSearch();
};

// 导出Excel功能
const exportToExcel = () => {
  // 准备导出数据
  const exportData = [];

  // 添加标题
  exportData.push(["咨询业绩排行表"]);
  exportData.push([]);

  // 添加筛选条件信息
  exportData.push(["查询条件"]);
  exportData.push(["日期范围:", formatDateRange.value]);

  // 添加咨询师筛选信息
  let consultantsText = "全部";
  if (consultantFilter.value && consultantFilter.value.length > 0) {
    consultantsText = consultantFilter.value
      .map((id) => {
        const staff = staffList.value.find((s) => s.userid === id);
        return staff ? staff.anotherName : id;
      })
      .join(", ");
  }
  exportData.push(["咨询师:", consultantsText]);

  // 添加接诊类型筛选信息
  let consultStagesText = "全部";
  if (consultStageFilter.value && consultStageFilter.value.length > 0) {
    consultStagesText = consultStageFilter.value
      .map((stage) => {
        const stageItem = ConsultStage.find((item) => item.value === stage);
        return stageItem ? stageItem.label : stage;
      })
      .join(", ");
  }
  exportData.push(["接诊类型:", consultStagesText]);

  // 添加项目分类筛选信息
  let categoriesText = "全部";
  if (projectCategories.value && projectCategories.value.length > 0) {
    categoriesText = projectCategories.value.map((cat) => cat.name).join(", ");
  }
  exportData.push(["项目分类:", categoriesText]);

  // 添加项目筛选信息
  let projectsText = "全部";
  if (projectFilter.value && projectFilter.value.length > 0) {
    projectsText = projectFilter.value.map((p) => p.projectName).join(", ");
  }
  exportData.push(["项目:", projectsText]);
  exportData.push([]);

  // 添加表头
  exportData.push(["排名", "咨询师", "咨询人次", "成交人次", "成交率", "成交金额", "退款金额", "实收金额", "客单价"]);

  // 添加数据行
  tableData.value.forEach((item, index) => {
    exportData.push([
      index + 1,
      item.consultantName,
      item.consultCount,
      item.successCount,
      formatPercentRaw(item.successRate), // 格式化成百分比但不带%符号
      formatNumberRaw(item.successAmount), // 格式化金额为带2位小数的数字
      formatNumberRaw(item.refundAmount),
      formatNumberRaw(item.netAmount),
      formatNumberRaw(item.avgOrderValue),
    ]);
  });

  // 添加总计行
  exportData.push(["合计", "", totalConsultCount.value, totalSuccessCount.value, formatPercentRaw(totalSuccessRate.value), formatNumberRaw(totalSuccessAmount.value), formatNumberRaw(totalRefundAmount.value), formatNumberRaw(totalNetAmount.value), formatNumberRaw(totalAvgOrderValue.value)]);

  // 创建工作簿和工作表
  const wb = XLSX.utils.book_new();
  const ws = XLSX.utils.aoa_to_sheet(exportData);

  // 设置列宽
  const colWidths = [
    { wch: 6 }, // 排名
    { wch: 12 }, // 咨询师
    { wch: 10 }, // 咨询人次
    { wch: 10 }, // 成交人次
    { wch: 10 }, // 成交率
    { wch: 12 }, // 成交金额
    { wch: 12 }, // 退款金额
    { wch: 12 }, // 实收金额
    { wch: 12 }, // 客单价
  ];

  ws["!cols"] = colWidths;

  // 合并标题单元格
  ws["!merges"] = [
    { s: { r: 0, c: 0 }, e: { r: 0, c: 8 } }, // 合并标题行
    { s: { r: 2, c: 0 }, e: { r: 2, c: 8 } }, // 合并"查询条件"行
  ];

  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(wb, ws, "咨询师业绩排名");

  // 生成Excel文件并下载
  const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
  const blob = new Blob([excelBuffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });

  // 使用当前日期作为文件名的一部分
  const now = new Date();
  const fileName = `咨询师业绩排名_${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}.xlsx`;

  saveAs(blob, fileName);

  ElMessage.success("导出Excel成功");
};

// 格式化百分比为纯数字（用于Excel导出）
const formatPercentRaw = (value) => {
  if (value === undefined || value === null || isNaN(value)) {
    return "0.00%";
  }
  return (value * 100).toFixed(2) + "%";
};

// 格式化数字为固定两位小数（用于Excel导出）
const formatNumberRaw = (value) => {
  if (value === undefined || value === null || isNaN(value)) {
    return "0.00";
  }
  return value.toFixed(2);
};

onMounted(async () => {
  await judgmentIsAdmin(); // 初始化管理员状态
  if (staffList.value.length === 0) await getStaffList();
  // 设置默认日期范围为最近一个月
  const end = new Date();
  const start = new Date(end.getFullYear(), end.getMonth(), 1);

  dateRange.value = [formatDate(start), formatDate(end)];

  handleGetConsultantSalesRanking();
});
</script>

<style scoped>
.report-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
  background-color: #f5f7fa;
  overflow: hidden;
  min-width: 1200px;
}

.report-header {
  background-color: white;
  padding: 15px 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
}

.report-title {
  text-align: center;
  font-weight: bold;
  font-size: 20px;
  margin-bottom: 5px;
  color: #303133;
}

.report-date {
  text-align: center;
  font-size: 14px;
  margin-bottom: 15px;
  color: #606266;
}

.filter-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-label {
  white-space: nowrap;
  margin-right: 5px;
  font-size: 14px;
  color: #606266;
}

.filter-actions {
  margin-left: auto;
  display: flex;
  gap: 10px;
}

.date-range {
  min-width: 320px;
}

:deep(.el-date-editor.el-input__wrapper) {
  width: 350px;
}

.filter-controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.table-container {
  flex: 1;
  margin: 15px 0 0 0;
  background-color: white;
  overflow: auto;
  position: relative;
  border-top: 1px solid #ebeef5;
  border-bottom: 1px solid #ebeef5;
}

.ranking-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  table-layout: fixed;
}

.ranking-table th,
.ranking-table td {
  border: 1px solid #ebeef5;
  padding: 12px 8px;
  text-align: center;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ranking-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 10;
}

.ranking-table .amount {
  text-align: right;
  padding-right: 10px;
}

.total-row {
  background-color: #ebeef5;
  font-weight: bold;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.total-label {
  text-align: right;
  padding-right: 10px;
}

.footer-info {
  background-color: white;
  padding: 10px 20px;
  margin: 0;
  display: flex;
  justify-content: space-between;
  color: #606266;
  font-size: 14px;
  flex-shrink: 0;
  border-top: 1px solid #ebeef5;
}

.date-limit-info {
  color: #e6a23c;
  font-style: italic;
}

@media print {
  .report-container {
    background-color: white;
    height: auto;
  }

  .report-header {
    box-shadow: none;
  }

  .report-actions,
  .footer-info {
    display: none;
  }

  .table-container {
    margin: 0;
    border: none;
    overflow: visible;
    height: auto;
  }

  .ranking-table th,
  .ranking-table td {
    font-size: 12px;
    padding: 8px 4px;
  }
}

@media (max-width: 768px) {
  .report-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .date-range {
    width: 100%;
  }

  :deep(.el-date-editor.el-input__wrapper) {
    width: 100%;
  }

  .filter-controls {
    flex-direction: column;
    align-items: stretch;
  }
}
</style>