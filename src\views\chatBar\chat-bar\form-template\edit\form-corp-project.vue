<template>
  <!-- <form-select v-bind="$attrs" :range="range" @change="change" /> -->
  <el-form-item class="px-15px py-6px el-form-item--autoheight" :class="required ? 'is-required' : ''" :label="name"
    border-bottom>
    <el-select clearable filterable remote reserve-keyword :remote-method="search" :loading="loading" :model-value="value"
      class="w-full" :multiple="mult" @update:model-value="change($event)">
      <el-option v-for="opt in options" :key="opt.value" :value="opt.value">
        {{ opt.label }}
      </el-option>
    </el-select>
  </el-form-item>
</template>
<script setup>
import { computed, ref } from "vue";
import { watchDebounced } from "@vueuse/core";
import { storeToRefs } from "pinia";
import { memberStore } from '@/store/member';
import { getProjectList } from "@/api/project-manage";

const emits = defineEmits(['change']);
const props = defineProps({
  form: { type: Object, default: () => ({}) },
  mult: { type: Boolean, default: false },
  name: { type: String, default: "" },
  range: { type: Array, default: () => [] },
  required: { type: Boolean, default: false },
  title: { type: String, default: "" },
  rangeKey: { type: String, default: "" },
});

const { memberInfo } = storeToRefs(memberStore());
const loading = ref(false);
const list = ref([]);
const text = ref('');
const value = computed(() => props.form[props.title] || "");
const arrValue = computed(() => (Array.isArray(value.value) ? value.value : [value.value]).filter(Boolean));
const valueOptions = computed(() => arrValue.value.map(item => ({ label: item, value: item })));
const options = computed(() => {
  const str = text.value.trim();
  const data = list.value.filter(i => !valueOptions.value.some(item => item.value === i.value))
  if (str) {
    return [...valueOptions.value.filter(item => item.label.includes(str)), ...data]
  }
  return [...valueOptions.value, ...data];
})

function search(val) {
  if (val) {
    loading.value = true;
    text.value = val;
  }
}

function change(value) {
  emits("change", { title: props.title, value });
}

async function getProjects() {
  const payload = {
    page: 1,
    pageSize: 15,
    corpId: memberInfo.value.corpId,
    projectStatus: 'enable',
    projectName: text.value.trim(),
  }
  const { data } = await getProjectList(payload);
  list.value = data && Array.isArray(data.list) ? data.list.map(i => ({ value: i.projectName, label: i.projectName })) : [];
  loading.value = false
}

watchDebounced(
  text,
  async () => {
    if (text.value) {
      await getProjects();
    }
    loading.value = false;
  },
  { debounce: 1000 }
);


</script>
<style lang="scss" scoped></style>
