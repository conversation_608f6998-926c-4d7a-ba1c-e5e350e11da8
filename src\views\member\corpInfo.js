import { memberStore } from "../../store/member";

function getIntenteds() {
  let { corpInfo } = memberStore();
  let intentedProjectList = corpInfo.intentedProjectList || [];
  let intenteds = intentedProjectList.map((item) => {
    return {
      value: item,
      label: item,
    };
  });
  return intenteds;
}

function getCustomerSources() {
  let { corpInfo } = memberStore();
  let customerSourceList = corpInfo.customerSourceList ? JSON.parse(JSON.stringify(corpInfo.customerSourceList)) : [];
  return updateValue(customerSourceList, "sourceName");
}

function updateValue(data, field) {
  if (Array.isArray(data)) {
    data = data.filter((item) => !item.disable);
    return data.map((item) => updateValue(item, field));
  } else if (data && typeof data === "object") {
    let newData = { ...data }; // 创建一个新的对象以避免直接修改原始对象
    if (newData[field] !== undefined) {
      newData.value = newData[field];
      newData.label = newData[field];
    }
    Object.keys(newData).forEach((key) => {
      newData[key] = updateValue(newData[key], field);
    });
    return newData;
  }
  return data;
}
export { getIntenteds, getCustomerSources };
