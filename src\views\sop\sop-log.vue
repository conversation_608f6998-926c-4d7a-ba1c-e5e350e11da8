<template>
  <my-layout bg-fff>
    <layout-item>
      <div class="p-15px">
        <div class="flex items-end">
          <div class="text-16px font-semibold flex">
            <div>
              <ww-user :openid="userId"></ww-user>
            </div>
            <div>的任务日志</div>
          </div>
          <div v-if="sopName" class="text-12px text-gray-500 ml-10px">【SOP任务：{{ sopName }} 】</div>
        </div>
        <div class="flex items-center mt-15px text-14px flex-wrap select-none">
          <el-select v-model="executeStatus" placeholder="任务状态">
            <el-option label="已处理" value="executed" />
            <el-option label="未处理" value="unexecuted" />
            <el-option label="处理失败" value="failed" />
          </el-select>
          <el-date-picker v-model="dates" class="flex-grow-0 ml-10px w-350px" type="daterange"  :unlink-panels="true"
 value-format="YYYY-MM-DD" start-placeholder="任务下发开始时间" end-placeholder="任务下发结束时间" />
          <el-button type="primary" class="ml-10px" @click="reset()">重置</el-button>
        </div>
      </div>
    </layout-item>
    <Layout-main :scroll="false">
      <el-table height="100%" :data="list">
        <el-table-column min-width="160" label="目标客户">
          <template #default="{ row: { customerName, name } }">
            <div class="truncate">{{ name || customerName }}</div>
          </template>
        </el-table-column>
        <el-table-column min-width="240" label="所属团队">
          <template #default="{ row: { executeTeamName } }">
            <div class="truncate">{{ executeTeamName }}</div>
          </template>
        </el-table-column>
        <el-table-column min-width="160" label="跟进方式">
          <template #default="{ row: { executeMethod } }">
            <div class="truncate">{{ executeMethod }}</div>
          </template>
        </el-table-column>
        <el-table-column min-width="120" label="任务状态">
          <template #default="{ row: { executeStatus } }">
            <div class="truncate">{{ executeStatus }}</div>
          </template>
        </el-table-column>
        <el-table-column min-width="160" label="任务下发时间">
          <template #default="{ row: { createTime } }">
            <div class="truncate">{{ createTime }}</div>
          </template>
        </el-table-column>
        <el-table-column min-width="160" label="任务完成时间">
          <template #default="{ row: { completeTime } }">
            <div class="truncate">{{ completeTime }}</div>
          </template>
        </el-table-column>
      </el-table>
    </Layout-main>
    <Layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize" @handle-current-change="changePage" />
    </Layout-item>
  </my-layout>
</template>
<script setup>
import { computed, ref, watch } from "vue";
import useElPagination from "@/hooks/useElPagination";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import { getCustomerSopTask } from "@/api/member";
import dayjs from "dayjs";
import { useRoute } from "vue-router";
import WwUser from "@/components/ww-user/index.vue";
const dates = ref([]);
const total = ref(0);
const route = useRoute();
const executeStatus = ref("");
const sopTaskId = route.query.sopTaskId;
const userId = route.query.userId;
const sopName = route.query.sopName;
const list = ref([{}]);
const executeStatusObj = {
  executed: "已处理",
  unexecuted: "未处理",
  failed: "处理失败",
};
const executeMethodObj = {
  tag: "自动新增/删减标签",
  todo: "待办事项",
  groupmsg: "群发任务",
};
const { page, pageSize, changePage, changeSize } = useElPagination(getList);
async function getList() {
  const { data, success } = await getCustomerSopTask({ page: page.value, pageSize: pageSize.value, sopTaskId, userId, dates: dates.value, executeStatus: executeStatus.value });
  if (!success) return;
  total.value = data.total;
  list.value = data.data.map((item) => {
    return {
      ...item,
      executeStatus: executeStatusObj[item.executeStatus],
      executeMethod: executeMethodObj[item.executeMethod],
      createTime: dayjs(item.createTime).format("YYYY-MM-DD"),
      completeTime: item.completeTime ? dayjs(item.completeTime).format("YYYY-MM-DD") : "",
    };
  });
}
getList();
watch([dates, executeStatus], () => {
  page.value = 1;
  getList();
});
function reset() {
  dates.value = [];
  executeStatus.value = "";
}
</script>
<style lang="scss" scoped>
.text-primary {
  color: var(--el-color-primary);
}
</style>
