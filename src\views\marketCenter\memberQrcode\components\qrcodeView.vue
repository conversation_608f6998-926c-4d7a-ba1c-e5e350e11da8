<template>
  <div class="qrcode">
    <el-dialog :model-value="qrcodeVisible" draggable :width="400" @close="close">
      <div style="margin-top: -20px">
        <div class="flex justify-center font-semibold text-18px py-20px">{{ qrCodeInfo.qrCodeName }}</div>
        <div>可用于面对面扫码，下载二维码图片放置在活动物料上使用，也可以复制链接配置到抖音、视频号等 场景使用。</div>
        <div class="py-10px">
          <el-image style="width: 100%; height: 200px" :src="qrCodeInfo.qrCode" fit="contain" preview-src-list="false"></el-image>
        </div>
      </div>
      <template #footer v-if="qrCodeInfo.qrCodeStatus === 'enable'">
        <div class="dialog-footer text-center">
          <el-button class="w-100px" @click="downloadImage">下载二维码</el-button>
          <el-button class="w-100px" type="primary" @click="copyUrl">复制链接</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import axios from "axios";
import { ElMessage } from "element-plus";
const emit = defineEmits(["close"]);
const props = defineProps({
  qrcodeVisible: { default: false },
  qrCodeInfo: { default: {} },
});
function close() {
  emit("close");
}
const downloadImage = async () => {
  const { qrCode, qrCodeName } = props.qrCodeInfo;
  try {
    const response = await axios.get(qrCode, { responseType: "blob" });
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", `${qrCodeName}.jpg`); // 设置下载的文件名
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error("There has been a problem with your fetch operation:", error);
  }
};

const copyUrl = () => {
  const { qrCode, qrCodeName } = props.qrCodeInfo;
  const input = document.createElement("input");
  document.body.appendChild(input);
  input.setAttribute("value", qrCode);
  input.select();
  document.execCommand("copy");
  document.body.removeChild(input);
  ElMessage.success("复制成功");
};
</script>
<style lang="scss" scoped>
</style>