<template>
  <my-layout bg-fff>
    <layout-main>
      <div class="px-30px py-15px" v-if="planForm.planId || isAddCustomerTask" :loading="taskLoading">
        <template v-if="!isAddCustomerTask && planForm.planExecutStaus !== 'untreated'">
          <div class="flex" align-center>
            <div>{{ planForm.planName }}</div>
            <div class="text-14px pl-10px" main-color>
              {{ managementPlanStatus[planForm.planExecutStaus] }}
            </div>
          </div>
          <div>
            <div class="flex my-10px">
              <div class="text-14px">
                <span grew-color class="pr-10px">开始时间:</span>
                <span>{{ planForm.planExecutionTime }}</span>
              </div>
              <div class="text-14px ml-30px">
                <span grew-color class="pr-10px">计划跟踪人:</span>
                <ww-user v-if="planForm.executorUserId" :openid="planForm.executorUserId"></ww-user>
              </div>
            </div>
            <div class="py-10px" border-top></div>
          </div>
        </template>
        <el-form inline label-suffix="：" :rules="rules" :model="planForm"
          v-if="planForm.planExecutStaus === 'untreated' || isAddCustomerTask">
          <el-form-item required label="计划名称" label-width="110px" class="mt-10px" prop="planName">
            <el-input flex-grow placeholder="请输入计划名称" class="w-180px" v-model="planForm.planName" />
          </el-form-item>
          <el-form-item required label="开始时间" label-width="110px" class="mt-10px" prop="planExecutionTime">
            <el-date-picker v-model="planForm.planExecutionTime" style="width: 180px" format="YYYY-MM-DD"
              placeholder="请选择开始时间" type="date" value-format="YYYY-MM-DD" :disabled-date="judgeDisabled" />
          </el-form-item>
          <el-form-item required label="计划跟踪人" label-width="110px" prop="executorUserId" class="mt-10px">
            <el-select v-model="planForm.executorUserId" class="el-select--hiddenValue w-180px" placeholder="请选择计划跟踪人"
              filterable>
              <template #prefix>
                <div class="h-30px" color-666>
                  <ww-user :openid="planForm.executorUserId"></ww-user>
                </div>
              </template>
              <el-option v-for="userId in currentTeam.memberList" :key="userId" :value="userId">
                <ww-user :openid="userId"></ww-user>
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <el-button v-if="planForm.planExecutStaus !== 'closed' || isAddCustomerTask" text class="item form-tag mb-10px"
          type="primary" :icon="Plus" plain @click="addTask">新增任务项</el-button>
        <task-timeline ref="taskTimelineRef" :taskList="taskList" :planExecutStaus="planForm.planExecutStaus"
          :planExecutionTime="planForm.planExecutionTime" @saveSuccess="saveTaskSuccess" :planForm="planForm"
          :customer="customer" @editTaskSuccess="onEditTaskSuccess"></task-timeline>
      </div>
      <div v-else>
        <empty-data :top="100" title="暂无任务" padding="0 0 15px" text-top="10px" :image-width="120"></empty-data>
        <div class="w-full flex" justify-center>
          <el-button class="item form-tag" type="primary" plain @click="addManagePlan"
            v-if="canAction">新增回访计划</el-button>
        </div>
      </div>
    </layout-main>
    <layout-item>
      <div text-center common-shadow--r py-10 v-if="planForm.planId || isAddCustomerTask">
        <template v-if="planForm && planForm.planExecutStaus === 'untreated'">
          <el-button class="w-100px" plain @click="closePlan()">取消</el-button>
          <el-button class="w-100px" type="primary" @click="executPlan()">执行计划</el-button>
        </template>
        <template v-else-if="planForm && planForm.planExecutStaus === 'executing'">
          <el-button class="w-100px" type="primary" @click="stopPlan()">终止任务</el-button>
        </template>
        <template v-else>
          <el-button class="w-100px" type="primary" @click="addManagePlan()">加入任务</el-button>
        </template>
      </div>
    </layout-item>
  </my-layout>
  <select-mamagement-plan :visible="visible" @close="visible = false" @success="onSuccess" :planId="planForm.planId"
    @addCustomerTask="addCustomerTask"></select-mamagement-plan>
</template>
<script setup>
import { ref, watch, computed, reactive, inject } from "vue";
import EmptyData from "@/components/empty-data.vue";
import selectMamagementPlan from "./select-management-plan";
import taskTimeline from "@/views/managementPlan/components/task-timeline";
import { Plus } from "@element-plus/icons-vue";
import WwUser from "@/components/ww-user/index.vue";
import dayjs from "dayjs";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import { managementPlanStatus } from "@/baseData";
import { teamStore } from "@/store/team";
import { storeToRefs } from "pinia";
import { memberMangePlanStore } from "@/store/managePlan/memberMangePlan";
const { currentTeam } = storeToRefs(teamStore());

const props = defineProps(["customer", "customerType", 'enableAction']);
const canAction = computed(() => Boolean(props.enableAction) || props.customerType !== 'corpCustomer')

const { _id } = props.customer;
const emits = defineEmits(["reload"]);
const { planForm, taskList, taskLoading, isAddCustomerTask, visible, customer, team } = storeToRefs(memberMangePlanStore());
const { initPlan, executPlan, stopPlan, getMemberManagementPlanFnc, onEditTaskSuccess, updateMemberMangePlanStatusFnc } = memberMangePlanStore();
let rules = reactive({
  planName: [{ required: true, message: "请选择计划名称", trigger: "blur" }],
  planExecutionTime: [{ required: true, message: "请选择开始时间", trigger: "blur" }],
  executorUserId: [{ required: true, message: "请选择计划跟踪人", trigger: "blur" }],
});
team.value = currentTeam.value;
watch(currentTeam, (newItem, oldItem) => {
  if (newItem && oldItem && newItem.teamId !== oldItem.teamId) {
    team.value = newItem;
    getMemberManagementPlanFnc();
  }
});
watch(
  () => props.customer,
  (n) => {
    customer.value = n;
    getMemberManagementPlanFnc();
  },
  { immediate: true }
);
function addManagePlan() {
  visible.value = true;
}
function onSuccess(item) {
  visible.value = false;
  const { planId, planName, taskList: list } = item;
  planForm.value = {
    planExecutionTime: "",
    planName,
    planId,
    planExecutStaus: "untreated",
  };
  taskList.value = list;
}
// 取消计划
function closePlan() {
  isAddCustomerTask.value = false;
  getMemberManagementPlanFnc();
}
// 编辑计划
function saveTaskSuccess(list) {
  // 回访计划状态为已变更
  planForm.value.changedStatus = "isChanged";
  updateMemberMangePlanStatusFnc();
  taskList.value = list;
}

function judgeDisabled(date) {
  return dayjs(date).endOf("day").isBefore(dayjs());
}

const taskTimelineRef = ref(null);
function addTask() {
  if (!planForm.value.planId && !isAddCustomerTask.value) return;
  taskTimelineRef.value.addTask(planForm.value.planId);
}

// 新增客户回访计划
function addCustomerTask() {
  visible.value = false;
  isAddCustomerTask.value = true;
  initPlan();
}
</script>

<style lang="scss">
.el-select.el-select--hiddenValue .el-input__inner {
  display: none;
}

.el-select.el-select--hiddenValue .el-input__prefix {
  flex-grow: 1;
}

:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-timeline-item__tail) {
  border-left-color: #006eff;
}
</style>
