<template>
  <my-layout bg-fff common-shadow v-loading="loading">
    <layout-main :scroll="false">
      <div class="flex h-full">
        <my-layout class="flex-grow w-2/3">
          <layout-main>
            <send-form ref="formRef" @success="success" @uploadFile="uploadFile" @closeLoading="closeLoading" />
          </layout-main>
        </my-layout>
        <my-layout class="w-1/3 flex-shrink-0">
          <layout-main class="border-l-1 border-l-gray-200">
            <phone-preview :fileList="fileList" :content="content" />
          </layout-main>
        </my-layout>
      </div>
    </layout-main>
    <layout-item>
      <div text-center p-15 common-shadow--r>
        <el-button class="w-100px" @click="cancel">取消</el-button>
        <el-button class="w-100px" type="primary" @click="save()">{{ sendTaskType === "MINE" ? "确认发送" : "确认" }}</el-button>
      </div>
    </layout-item>
  </my-layout>
</template>
<script setup>
import { ref } from "vue";
import sendForm from "./send-form.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import phonePreview from "../../components/phone-preview.vue";
import { ElMessageBox } from "element-plus";
import { useRoute } from "vue-router";
const route = useRoute();
const sendTaskType = ref(route.params.sendTaskType);
const formRef = ref();
const fileList = ref([]);
const content = ref("");
const loading = ref(false);
function save() {
  loading.value = true;
  formRef.value.submit();
}
function uploadFile(list, str) {
  fileList.value = list;
  content.value = str;
}
async function cancel() {
  await ElMessageBox.confirm("取消后将不会保存任何内容!", "提示", {
    confirmButtonText: "我知道了",
    cancelButtonText: "取消",
    type: "warning",
  });
  formRef.value.closeView();
  // 关闭当前页面
}
function closeLoading() {
  loading.value = false;
}
</script>
