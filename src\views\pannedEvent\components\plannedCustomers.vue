<template>
  <my-layout bg-fff height="100%">
    <layout-main class="y-auto h-320px">
      <div class="el-layout">
        <el-table stripe :data="customers">
          <el-table-column label="客户名称" property="customerName" />
          <el-table-column label="服务员工">
            <template #default="{ row }">
              <ww-user :openid="row.executorUserId"></ww-user>
            </template>
          </el-table-column>
          <el-table-column property="createTime" label="任务生成时间" />
          <el-table-column property="endTime" label="任务结束时间" />
          <el-table-column property="eventStatus" label="状态" />
        </el-table>
      </div>
    </layout-main>
    <layout-item>
      <pagination
        :totalRow="total"
        :pageSize="pageSize"
        :currentPage="currentPage"
        @handle-size-change="onHandleSizeChange"
        @handle-current-change="onHandleCurrentChange"
      />
    </layout-item>
  </my-layout>
</template>
<script setup>
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import WwUser from "@/components/ww-user/index.vue";
import { useRoute } from "vue-router";
import { getTodoEventsByPannedEventId } from "@/api/todo";
import { ToDoTaskStatus } from "@/baseData";
import { computed, ref } from "vue";
import pagination from "@/components/pagination/pagination.vue";
const route = useRoute();
import dayjs from "dayjs";
const list = ref([]);
const total = ref(0);
const pageSize = ref(10);
const currentPage = ref(1);
getTodoEventsByPannedEventIdAction();
async function getTodoEventsByPannedEventIdAction() {
  let res = await getTodoEventsByPannedEventId({
    pageSize: pageSize.value,
    page: currentPage.value,
    pannedEventId: route.params.id,
  });
  if (res.success) {
    list.value = res.data.data;
    total.value = res.data.total;
  }
}
const customers = computed(() => {
  return list.value.map((item) => {
    item["createTime"] = dayjs(item.createTime).format("YYYY-MM-DD");
    item["endTime"] = dayjs(item.createTime).format("YYYY-MM-DD");
    const obj = ToDoTaskStatus.reduce((acc, { value, label }) => {
      acc[value] = label;
      return acc;
    }, {});
    item["eventStatus"] = obj[item.eventStatus];
    return item;
  });
});

function onHandleSizeChange(e) {
  pageSize.value = e;
  getTodoEventsByPannedEventIdAction();
}
function onHandleCurrentChange(e) {
  currentPage.value = e;
  getTodoEventsByPannedEventIdAction();
}
</script>
<style scoped lang="scss">
.sub-title {
  font-size: 14px;
  color: #006eff;
  padding-top: 5px;
}
:deep(.el-table__inner-wrapper) {
  height: 100% !important;
}
</style>
