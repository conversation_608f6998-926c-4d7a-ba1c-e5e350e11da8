// import useCloudBase from "./index";
import { teamStore } from "@/store/team";
import { memberStore } from "@/store/member";

import { post } from "./axios";
async function useMember(data) {
  const res = await post("member", data);
  return res;
}

// async function useMember(data) {
//   const res = await useCloudBase("member", data);
//   return res;
// }

export async function createPointsTask(params = {}) {
  const res = await useMember({
    type: "createPointsTask",
    corpId: localStorage.getItem("corpId"),
    creator: localStorage.getItem("userId"),
    ...params,
  });
  return res;
}

export async function getPointsTaskList(params) {
  const res = await useMember({
    type: "getPointsTaskList",
    corpId: localStorage.getItem("corpId"),
    ...params,
  });
  return res;
}

export async function updatePointsTask(params) {
  const res = await useMember({
    type: "updatePointsTask",
    ...params,
  });
  return res;
}

export async function deletePointsTask(id) {
  const res = await useMember({
    type: "deletePointsTask",
    corpId: localStorage.getItem("corpId"),
    id,
  });
  return res;
}
