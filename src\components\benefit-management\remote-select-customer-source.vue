<template>
  <el-select
    v-model="selectedSource"
    :placeholder="placeholder"
    filterable
    clearable
    :loading="loading"
    style="width: 100%"
  >
    <el-option
      v-for="item in flattenSourceOptions"
      :key="item.id"
      :label="item.label"
      :value="item.id"
      :disabled="item.isCategory"
    >
      <div class="source-option-item">
        <span :class="{'text-gray-400': item.isCategory}">
          {{ getIndentation(item.level) }}
          <template v-if="item.isCategory && hasChildren(item.id)">
            <el-icon 
              class="category-toggle-icon" 
              @click.stop="toggleCategory(item.id, $event)"
            >
              <CaretBottom v-if="categoryExpandState[item.id]" />
              <CaretRight v-else />
            </el-icon>
          </template>
          <template v-else-if="item.level > 0">
            <span class="indent-placeholder"></span>
          </template>
          {{ item.label }}
        </span>
      </div>
    </el-option>
  </el-select>
</template>

<script setup>
import { computed, ref, onMounted } from "vue";
import { useVModels } from "@vueuse/core";
import { CaretRight, CaretBottom, Loading } from "@element-plus/icons-vue";
import { getSourceCateList, getSourceList } from "@/api/benefitManagement"; // 引入API

const emits = defineEmits(["update:modelValue"]);
const props = defineProps({
  placeholder: { type: String, default: "请选择客户来源" },
  modelValue: { type: Array, default: () => [] },
});

const { modelValue } = useVModels(props, emits);
const loading = ref(false);
const sourceCateList = ref([]); // 分类列表
const sourceDataList = ref([]); // 来源列表
const categoryExpandState = ref({}); // 分类展开状态
const selectedSource = computed({
  get() {
    // 如果有值，根据值查找对应的ID
    if (modelValue.value && modelValue.value.length > 0) {
      const sourceItem = flattenSourceOptions.value.find(item => item.label === modelValue.value[0] && !item.isCategory);
      return sourceItem ? sourceItem.id : '';
    }
    return '';
  },
  set(val) {
    if (!val) {
      modelValue.value = [];
      return;
    }
    
    // 找到选择的项，获取其标签值
    const selectedItem = flattenSourceOptions.value.find(item => item.id === val);
    if (selectedItem && !selectedItem.isCategory) {
      modelValue.value = [selectedItem.label];
    }
  }
});

// 获取分类和来源数据
async function fetchSourceData() {
  loading.value = true;
  try {
    // 获取分类数据
    const cateRes = await getSourceCateList({
      corpId: localStorage.getItem("corpId"),
      userId: localStorage.getItem("userId"),
    });
    if (cateRes.success) {
      sourceCateList.value = cateRes.data.list || [];
    }

    // 获取来源数据（不分页获取全部）
    const sourceRes = await getSourceList({
      page: 1,
      pageSize: 1000,
      corpId: localStorage.getItem("corpId"),
      userId: localStorage.getItem("userId"),
    });
    if (sourceRes.success) {
      sourceDataList.value = sourceRes.data.list || [];
    }
  } catch (error) {
    console.error("获取客户来源数据失败", error);
  } finally {
    loading.value = false;
  }
}

// 构建树形结构
const sourceTree = computed(() => {
  return convertData(sourceCateList.value, sourceDataList.value);
});

// 将树形结构扁平化为选择列表
const flattenSourceOptions = computed(() => {
  const result = [];
  
  function flattenTree(nodes, level = 0, parentId = null) {
    if (!nodes) return;
    
    nodes.forEach(node => {
      const nodeInfo = {
        id: node.id,
        label: node.label,
        isCategory: node.isCategory,
        level: level,
        parentId
      };
      
      result.push(nodeInfo);
      
      // 仅当分类是展开状态时，才添加其子项
      if (node.children && node.children.length && (!node.isCategory || categoryExpandState.value[node.id])) {
        flattenTree(node.children, level + 1, node.id);
      }
    });
  }
  
  flattenTree(sourceTree.value);
  return result;
});

// 检查分类是否有子项
function hasChildren(categoryId) {
  return sourceTree.value.some(node => {
    if (node.id === categoryId && node.children && node.children.length) {
      return true;
    }
    
    // 检查更深层级
    if (node.children) {
      return node.children.some(child => {
        if (child.id === categoryId && child.children && child.children.length) {
          return true;
        }
        
        // 检查最深层级
        if (child.children) {
          return child.children.some(grandChild => 
            grandChild.id === categoryId && grandChild.children && grandChild.children.length
          );
        }
        
        return false;
      });
    }
    
    return false;
  });
}

// 切换分类展开状态
function toggleCategory(categoryId, event) {
  // 阻止事件冒泡，避免触发选择操作
  event.stopPropagation();
  // 切换展开状态
  categoryExpandState.value[categoryId] = !categoryExpandState.value[categoryId];
}

// 生成缩进来表示层级关系
function getIndentation(level) {
  if (!level) return '';
  return '\u00A0\u00A0'.repeat(level);
}

// 将分类和来源数据转换为树形结构
function convertData(cateList, sourceList) {
  // 按level分组分类
  const level1 = cateList.filter(item => item.level === 1);
  const level2 = cateList.filter(item => item.level === 2);
  const level3 = cateList.filter(item => item.level === 3);
  
  // 构建一级分类节点
  const tree = level1.map(cate => ({
    id: cate._id,
    label: cate.label,
    level: cate.level,
    isCategory: true, // 标记为分类节点
    children: []
  }));
  
  // 添加二级分类节点
  level2.forEach(cate => {
    const parentNode = tree.find(node => node.id === cate.parentId);
    if (parentNode) {
      parentNode.children.push({
        id: cate._id,
        label: cate.label,
        level: cate.level,
        isCategory: true, // 标记为分类节点
        children: []
      });
    }
  });
  
  // 添加三级分类节点
  level3.forEach(cate => {
    for (const parentNode of tree) {
      if (!parentNode.children) continue;
      
      const parent2Node = parentNode.children.find(node => node.id === cate.parentId);
      if (parent2Node) {
        parent2Node.children.push({
          id: cate._id,
          label: cate.label,
          level: cate.level,
          isCategory: true, // 标记为分类节点
          children: []
        });
        break;
      }
    }
  });
  
  // 将来源添加到对应分类下
  sourceList.forEach(source => {
    // 只处理启用状态的来源
    if (source.sourceStatus === 'enable' && source.sourceCateIdGroup && source.sourceCateIdGroup.length > 0) {
      const cateId = source.sourceCateIdGroup[0]; // 取第一个分类ID
      
      // 查找对应的分类节点
      let targetNode = null;
      
      // 搜索一级分类
      for (const node1 of tree) {
        if (node1.id === cateId) {
          targetNode = node1;
          break;
        }
        
        // 搜索二级分类
        if (node1.children) {
          for (const node2 of node1.children) {
            if (node2.id === cateId) {
              targetNode = node2;
              break;
            }
            
            // 搜索三级分类
            if (node2.children) {
              for (const node3 of node2.children) {
                if (node3.id === cateId) {
                  targetNode = node3;
                  break;
                }
              }
              if (targetNode) break;
            }
          }
          if (targetNode) break;
        }
      }
      
      // 添加来源到找到的分类下
      if (targetNode) {
        targetNode.children.push({
          id: source._id,
          label: source.sourceName,
          isCategory: false // 标记为来源节点，非分类
        });
      }
    }
  });
  
  // 递归过滤掉没有子节点的分类
  function filterEmptyCategories(nodes) {
    if (!nodes) return [];
    return nodes.filter(node => {
      if (node.children && node.children.length) {
        node.children = filterEmptyCategories(node.children);
        return node.children.length > 0;
      }
      return true; // 保留所有叶子节点（来源）
    });
  }
  
  return filterEmptyCategories(tree);
}

// 组件挂载时获取数据
onMounted(() => {
  fetchSourceData().then(() => {
    // 默认展开所有一级分类
    sourceTree.value.forEach(node => {
      categoryExpandState.value[node.id] = true;
    });
  });
});
</script>

<style scoped>
.source-option-item {
  display: flex;
  align-items: center;
}
.category-toggle-icon {
  cursor: pointer;
  margin-right: 4px;
}
.indent-placeholder {
  display: inline-block;
  width: 14px;
}
</style>