<template>
  <div class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30">
    <div class="flex items-center">
      <img class="w-36px h-36px animate-spin mr-10px" style="animation-duration: 2000ms;"
        src="@/assets/icons/logo-plain.png">
      <div class="text-white">
        <div class="text-15px leading-22px">youcanService</div>
        <div class="text-13px leading-20px">{{text}} . . <span class="animate-ping"
            style="animation-duration: 1500ms;">.</span> </div>
      </div>
    </div>

  </div>
</template>
<script setup>
defineProps({
  maskBg: { type: Boolean, default: true },
  text: { type: String, default: "正在帮您归纳" }
})
</script>
<style scoped></style>