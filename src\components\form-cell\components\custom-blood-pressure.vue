<template>
  <div v-if="isRead" class="w-300px">
    <div v-if="bloodPressureDate" class="flex items-center ml-5px mb-10px pt-10px text-14px">
      <span class="text-gray-500">测量日期：</span>
      {{ bloodPressureDate }}
    </div>
    <div v-if="normal.systolicPressure" class="flex items-center mb-10px"
      :class="normal.systolicPressure==='正常'?'text-green-500':'text-red-500'">
      <div class="flex-grow">
        <el-progress :text-inside="true" :stroke-width="20" :percentage="percent.systolicPressure"
          :status="normal.systolicPressure==='正常'?'success':'exception'">
          <span>收缩压 {{ systolicPressure +'(mmHg)' }}</span>
        </el-progress>
      </div>
      <div class="flex-shrink-0 ml-8px text-12px">{{ normal.systolicPressure }}</div>
    </div>
    <div v-if="normal.diastolicPressure" class="flex items-center mb-10px"
      :class="normal.diastolicPressure==='正常'?'text-green-500':'text-red-500'">
      <div class="flex-grow">
        <el-progress :text-inside="true" :stroke-width="20" :percentage="percent.diastolicPressure"
          :status="normal.diastolicPressure==='正常'?'success':'exception'">
          <span>舒张压 {{ diastolicPressure +'(mmHg)' }}</span>
        </el-progress>
      </div>
      <div class="flex-shrink-0 ml-8px text-12px">{{ normal.diastolicPressure }}</div>
    </div>
  </div>
  <div v-else>
    <div class="max-w-300px">
      <div class="flex items-center justify-between mb-10px">
        <el-date-picker class="w-full" ref="dateRef" placeholder="请选择测量日期" :disabled-date="judgeDisabled"
          :model-value="bloodPressureDate" type="date" value-format="YYYY-MM-DD"
          @update:model-value="change($event, 'bloodPressureDate')" />
      </div>
      <div class="mb-10px">
        <div class="flex items-center justify-between text-12px">
          <div class="text-gray-500">收缩压</div>
          <div class="text-13px text-dark-500">{{ systolicPressure }} <span class=" text-gray-500"> (mmHg)</span></div>
        </div>
        <el-slider :model-value="systolicPressure" :max="250" :min="40" :style="systolicPressureStyle" size="small"
          @input="change($event, 'systolicPressure')" />
      </div>
      <div>
        <div class="flex items-center justify-between text-12px">
          <div class="text-gray-500">舒张压</div>
          <div class="text-13px text-dark-500">{{ diastolicPressure }} <span class=" text-gray-500"> (mmHg)</span></div>
        </div>
        <el-slider :model-value="diastolicPressure" :max="200" :min="30" :style="diastolicPressureStyle" size="small"
          @input="change($event, 'diastolicPressure')" />
      </div>
    </div>
  </div>
</template>
<script setup>
import { computed, ref, toRefs } from 'vue';
import dayjs from 'dayjs';
const $emit = defineEmits(["change"]);
const props = defineProps({
  form: {
    type: Object,
    default: {},
  },
  value: {
    type: String,
    default: "",
  },
  isRead: {
    type: Boolean,
    default: false,
  },
});
const { form } = toRefs(props)
function change(value, title) {
  $emit("change", { value, title });
}

const dateRef = ref()
function toggle() {
  dateRef.value?.handleOpen()
}

const bloodPressureDate = computed(() => {
  if (form.value && form.value['bloodPressureDate'] && dayjs(form.value['bloodPressureDate'])) {
    return dayjs(form.value['bloodPressureDate']).format('YYYY-MM-DD')
  }
  return ''
});
const systolicPressure = computed(() => {
  const data = form.value || {};
  const val = data['systolicPressure'] || '';
  if (typeof val === 'number') return val
  if (typeof val === 'string' && val.trim() && Number(val) == val) return Number(val)
  return ''
})
const diastolicPressure = computed(() => {
  const data = form.value || {}
  const val = data['diastolicPressure'] || '';
  if (typeof val === 'number') return val
  if (typeof val === 'string' && val.trim() && Number(val) == val) return Number(val)
  return ''
})

function judgeDisabled(date) {
  return dayjs(date).isAfter(dayjs().endOf('day'))
}

const normal = computed(() => {
  return {
    systolicPressure: getLevel(systolicPressure.value, 90, 139),
    diastolicPressure: getLevel(diastolicPressure.value, 60, 89)
  }
})

const percent = computed(() => {
  return {
    systolicPressure: normal.value.systolicPressure ? Math.ceil(systolicPressure.value / 250 * 100) : 0,
    diastolicPressure: normal.value.diastolicPressure ? Math.ceil(diastolicPressure.value / 200 * 100) : 0
  }
})

function getLevel(value, mix, max) {
  if (value < mix && value > 0) return '偏低'
  else if (value > mix && value < max) return '正常'
  else if (value > max) return '偏高'
  return ''
}

const baseStyle = {
  '--el-slider-button-wrapper-size': '20px',
  '--el-slider-button-wrapper-offset': '-8px',
  '--el-slider-button-size': '12px',
  '--el-slider-main-bg-color': '#F56C6C'
}
const systolicPressureStyle = computed(() => {
  if (normal.value.systolicPressure === '正常') {
    return {
      ...baseStyle,
      '--el-slider-main-bg-color': '#67C23A'
    }
  }
  return baseStyle
})

const diastolicPressureStyle = computed(() => {
  if (normal.value.diastolicPressure === '正常') {
    return {
      ...baseStyle,
      '--el-slider-main-bg-color': '#67C23A'
    }
  }
  return baseStyle
})

</script>

<style>
.hidden-date-picker.el-date-editor {
  height: 0px;
  opacity: 0;
}
</style>
<style scoped>
.blood-item::after {
  content: '';
  position: absolute;
  right: 0;
  top: calc(50% - 8px);
  width: 2px;
  height: 16px;
  background: #ccc;
}
</style>