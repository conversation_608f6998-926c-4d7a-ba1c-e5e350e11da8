<template>
  <el-dialog :model-value="visible" :width="width" title="导入客户档案" @close="close">
    <div class="flex justify-center importClass pointer border-dotted border-2 mx-20px" @click="importExcel" @dragover.prevent @drop="handleFileDrop($event)">
      <img src="@/assets/importFile.svg" alt="" class="w-100px h-100px" />
      <div class="mt-10px">点击上传文件或者将文件拖拽到这里上传</div>
      <div class="flex my-10px">
        <div>请点击下载</div>
        <div @click.stop="exportExcel" class="flex">
          <img src="@/assets/downFile.svg" alt="" class="w-20px h-20px ml-5px pointer" />
          <div main-color class="mr-5px pointer">导出模版</div>
        </div>
        <div>，根据模版格式字段上传</div>
      </div>
    </div>
    <div class="px-20px mt-15px">
      <div>导入须知：</div>
      <div>1、导入文件格式为 Excel文件格式，支持xlsx和xls，不允许合并单元格、不允许公式、不允许空行。</div>
      <div>2、导入文件表格第一行为‘表头字段’，从第二行开始为‘患者信息内容字段’，每次最多可导入500条‘患者信息‘数据。</div>
      <div>3、表头字段请参照医客通软件中‘配置中心-模板档案’功能下，各模板中配置启用的字段，命名与’展示字段名称‘严格保持一致，否则会导致导入数据的丢失或导入失败。</div>
      <div>4、为了保证客户唯一性，【身份证号】和【客户档案编号】必须至少有一个非空，模板中约定的必填字段也需要非空，否则相关患者数据会导入失败。</div>
      <div>5、更新客户档案情况下字段内容更新规则：</div>
      <div>5.1、基础信息、内部信息、健康指标内容部分，导入字段内容非空并校验通过的，则进行更新；否则不更新。</div>
      <div>5.2、门诊病历概要、住院病历概要部分，为客户创建一份门诊、住院病历概要记录信息。</div>
    </div>
    <template #footer>
      <div class="flex justify-center">
        <el-button class="w-100px" @click="close">取消</el-button>
        <el-button class="w-100px" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <input class="fixed -z-1 w-0 h-0 invisible" :value="fileInputValue" type="file" ref="uploadRef" accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" @change="handleFileUpload($event)" />
</template>
  <script setup>
import { ref, onMounted } from "vue";
import { saveAs } from "file-saver";
import { utils, write, read } from "xlsx";
import { getImportTable } from "../../api/importCustomer.js";
import dayjs from "dayjs";
const props = defineProps({
  visible: { type: Boolean, default: false },
  width: { type: [String, Number], default: 200 },
  importType: { type: String, default: "" },
});
const emits = defineEmits(["close", "confirm"]);
const uploadRef = ref();
const fileInputValue = ref("");
function handleFileDrop(event) {
  event.preventDefault();
  const file = event.dataTransfer.files[0];
  if (!file) return;
  let fileType = ".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel";
  if (!fileType.includes(file.type)) return;
  getImportFile(file);
}
const handleFileUpload = (event) => {
  const file = event.target.files[0];
  if (!file) return;
  getImportFile(file);
};
async function getImportFile(file) {
  const reader = new FileReader();
  reader.onload = (e) => {
    fileInputValue.value = "";
    const data = new Uint8Array(e.target.result);
    const workbook = read(data, { type: "array", cellDates: true });
    // 读取第一个工作表
    const worksheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[worksheetName];
    // 将工作表转换为 JSON 对象数组
    const arrayData = utils.sheet_to_json(worksheet, { header: 1 });
    // 提取表头
    const headers = arrayData[0];
    // 提取数据
    const dataRows = arrayData.slice(1).filter((row) => row.length !== 0);
    dataRows.forEach((row, index) => {
      if (row.length === 0) {
        dataRows.splice(index, 1);
      }
    });
    let positiveIndex = null;
    let positiveOpinionIndex = null;

    if (props.importType === "physicalExaminationTemplate") {
      // 获取  阳性发现 所在位置
      positiveIndex = headers.findIndex((item) => item === "阳性发现");
      //  获取 阳性发现处理意见 所在位置
      positiveOpinionIndex = headers.findIndex((item) => item === "阳性发现处理意见");
    }
    // 将表头和数据合并
    let jsonData = [],
      jsonIndex = 0;
    dataRows.forEach((row) => {
      let obj = {};
      if (row.every((item, index) => index === positiveIndex || index === positiveOpinionIndex || !item)) {
        let item = jsonData[jsonIndex - 1];
        if (!item[headers[positiveIndex]]) {
          item[headers[positiveIndex]] = [row[positiveIndex]];
        } else {
          item[headers[positiveIndex]].push(row[positiveIndex]);
        }
        if (!item[headers[positiveOpinionIndex]]) {
          item[headers[positiveOpinionIndex]] = [row[positiveOpinionIndex]];
        } else {
          item[headers[positiveOpinionIndex]].push(row[positiveOpinionIndex]);
        }
        jsonData[jsonIndex - 1] = item;
      } else {
        row.forEach((cell, index) => {
          if (cell instanceof Date) {
            // 判断 cell 时分秒是否为 00:00:00
            // 将日期转换为 "2023-12-10" 这样的格式
            if (dayjs(cell).format("HH:mm:ss") === "23:59:17") cell.setHours(24);
            obj[headers[index]] = dayjs(cell).format("YYYY-MM-DD");
          } else if (index === positiveIndex || index === positiveOpinionIndex) {
            obj[headers[index]] = [cell];
          } else {
            obj[headers[index]] = cell || "";
          }
        });
        jsonData.push(obj);
        jsonIndex++;
      }
    });
    jsonData.forEach((item) => {
      for (let key in item) {
        if (key === "undefined") {
          delete item[key];
        }
        if (key === "证件号" && item[key] && /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(item[key])) {
          let { birthday, gender } = getBirthdayAndGender(item[key]);
          item["出生日期"] = birthday;
          item["性别"] = gender;
          // 通过身份证号获取年龄
          item["年龄"] = new Date().getFullYear() - parseInt(item[key].substring(6, 10));
        }
      }
    });
    emits("confirm", jsonData);
  };
  reader.readAsArrayBuffer(file);
}

function getBirthdayAndGender(idCard) {
  let birthday = `${idCard.substring(6, 10)}-${idCard.substring(10, 12)}-${idCard.substring(12, 14)}`;
  let gender = parseInt(idCard.substring(16, 17)) % 2 === 0 ? "女" : "男";
  return { birthday, gender };
}
async function importExcel() {
  uploadRef.value.click();
}
function close() {
  emits("close");
}
async function exportExcel() {
  // 创建一个工作簿
  const workbook = utils.book_new();
  const { list } = await getImportTable(props.importType);
  const worksheetData = [list];
  const worksheet = utils.aoa_to_sheet(worksheetData);
  // 将工作表添加到工作簿
  utils.book_append_sheet(workbook, worksheet, "Sheet1");
  // 将工作簿转换为 ArrayBuffer 对象
  const workbookBinary = write(workbook, { bookType: "xlsx", type: "binary" });
  const workbookBuffer = new ArrayBuffer(workbookBinary.length);
  const workbookView = new Uint8Array(workbookBuffer);
  for (let i = 0; i < workbookBinary.length; i++) {
    workbookView[i] = workbookBinary.charCodeAt(i) & 0xff;
  }
  // 创建 Blob 对象
  const workbookBlob = new Blob([workbookBuffer], { type: "application/octet-stream" });
  // 使用 file-saver 库保存 Blob 对象为 Excel 文件
  let title = "客户档案.xlsx";
  if (props.importType == "inhospital") {
    title = `客户档案+住院病历概要.xlsx`;
  }
  if (props.importType == "outpatient") {
    title = `客户档案+门诊病历概要.xlsx`;
  }
  if (props.importType == "physicalExaminationTemplate") {
    title = `客户档案+体检档案.xlsx`;
  }
  saveAs(workbookBlob, title);
}
</script>
  <style lang="scss" scoped>
.importClass {
  flex-direction: column;
  align-items: center;
}
::-webkit-scrollbar {
  width: 6px;
  background-color: transparent;
}

::-webkit-scrollbar-track {
  border-radius: 4px;
  // background-color: white;
}

::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: #909399;
}
</style>
  