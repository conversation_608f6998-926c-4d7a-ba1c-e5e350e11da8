<template>
  <!-- <el-dialog append-to-body :model-value="props.visible" :width="width" title="确认授权团队" @close="close">
    <div p-15 text-center font-semibold color-normal>您当前入驻{{ props.teams.length }}个团队，客户建档需要确认授权团队，请选择。 </div>
    <el-scrollbar wrap-class="el-dialog-body-full-scroll">
      <div v-for="team in props.teams" :key="team.teamId" class="auth-team" flex items-center justify-between rounded-8
        px-15 py-10 @click="selectTeam(team)">
        <div flex-grow font-14 color-normal class="w-0 truncate mr-10px">{{ team.name }}</div>
        <el-icon>
          <ArrowRightBold />
        </el-icon>
      </div>
    </el-scrollbar>
  </el-dialog> -->
  <el-dialog append-to-body :model-value="props.visible" :width="width" :title="currentTeam.name"
    @close="close">
    <div p-15 text-center font-semibold color-normal>
      {{ currentTeam.name }}有{{ qrcodeList.length }}个二维码，请选择要发送的建档二维码
    </div>
    <el-scrollbar wrap-class="el-dialog-body-full-scroll">
      <div v-for="code in qrcodeList" :key="code.id" class="auth-team" flex items-center justify-between rounded-8 px-15
        py-10 @click="sendLink(code)">
        <div flex-grow font-14 color-normal class="w-0 truncate mr-10px">{{ code.name }}</div>
        <el-icon>
          <ArrowRightBold />
        </el-icon>
      </div>
    </el-scrollbar>
  </el-dialog>
</template>
<script setup>
import { computed, watch, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { teamStore as useTeamStore } from '@/store/team';
import { ElMessage } from 'element-plus';
import { configStore } from "@/store/config";

const props = defineProps({
  authType: { type: String },
  externalUserId: { type: String },
  teams: { type: Array, default: () => [] },
  visible: { type: Boolean, default: false },
  width: { type: Number }
})
const { currentTeam } = storeToRefs(useTeamStore());
const qrcodeList = computed(() => Array.isArray(currentTeam.value.qrcodes) ? currentTeam.value.qrcodes : [])

const emits = defineEmits(['close', 'select', 'send']);
function close() {
  emits('close')
}

const qrCodeVisible = ref(false);
// const currentTeam = ref({})
// function selectTeam(team) {
//   currentTeam.value = team;
//   if (props.authType === 'selectTeam') {
//     emits('select', team);
//     emits('close')
//     return
//   }
//   const qrcodes = Array.isArray(team.qrcodes) ? team.qrcodes : [];
//   if (qrcodes.length === 0) ElMessage.info(`${team.name}还未配置二维码, 暂不支持建档`);
//   else if (qrcodes.length === 1) sendLink(qrcodes[0]);
//   else qrCodeVisible.value = true;
// }

const config = configStore();
async function sendLink(item) {
  const card = item.card || {};
  const corpUserID = localStorage.getItem("userId");
  const corpId = localStorage.getItem("corpId");
  const url = `${import.meta.env.VITE_PATIENT_PAGE_BASE_URL}pages/external/memberManage/index?qrid=${item.id || ''}&teamId=${currentTeam.value.teamId}&externalUserId=${props.externalUserId}&corpUserID=${corpUserID}&corpId=${corpId}`;
  const desc = `点击链接，完成在线建档，让我们更精准的为您服务。`;
  qrCodeVisible.value = false;
  close();
  console.log(url)
  emits('send', { desc: card.desc || desc, title: card.title || '在线建档', url, imgUrl: card.imgUrl });
}

watch(() => props.visible, n => {
  if (!n) return;
  if (qrcodeList.value.length === 0) {
    ElMessage.info(`${currentTeam.value.name}还未配置二维码, 暂不支持建档`);
    close()
  } else if (qrcodeList.value.length === 1) {
    sendLink(qrcodeList.value[0])
    close()
  } else {
    qrCodeVisible.value = true;
  }

})


</script>
<style scoped>
.auth-team {
  border: 1px solid #eee;
}

.auth-team+.auth-team {
  margin-top: 15px;
}
</style>