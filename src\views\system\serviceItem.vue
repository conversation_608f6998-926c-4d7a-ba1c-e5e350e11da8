<template>
  <my-layout bg-fff common-shadow>
    <layout-item>
      <div flex items justify-between p-15>
        <div flex items-center>
          <el-input placeholder="输入项目名称搜索" v-model="searchName" @input="inputChange" />
          <el-button ml-10 type="primary" @click="search()">查询</el-button>
        </div>
        <el-button type="primary" plain @click="add()">新增</el-button>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <el-table stripe height="100%" :data="list" v-loading="loading" :header-cell-style="{ background: '#ecf4ff' }">
        <el-table-column property="index" label="序号" />
        <el-table-column property="name" label="项目名称"></el-table-column>
        <el-table-column fixed="right" class-name="el-table-fixed-column--shadow" label="操作" :width="80">
          <template #default="{ row }">
            <el-button text class="table-action-btn mx-auto" type="primary" size="small" @click="edit(row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </layout-main>
    <layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="currentPage" @handle-size-change="onSizeChange" @handle-current-change="onCurrentChange" />
    </layout-item>
  </my-layout>
  <el-dialog :title="type === 'edit' ? '编辑服务项目' : '新增服务项目'" v-model="visible" width="400">
    <div p-15>
      <el-input v-model="newProject" class="select" placeholder="请输入项目名称"></el-input>
    </div>

    <template #footer>
      <div text-center>
        <el-button v-if="type === 'edit'" class="w-100px" @click="remove()">删除</el-button>
        <el-button v-else class="w-100px" @click="onClose">取消</el-button>
        <el-button class="w-100px" type="primary" @click="saveDiagnosis">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref } from "vue";
import { ElMessage } from "element-plus";
import { memberStore } from "@/store/member";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import { ElMessageBox } from "element-plus";
import { updateCorpInfo } from "@/api/corp";
const { corpInfo } = memberStore();
const intentedProjectList = ref(corpInfo.intentedProjectList || []);
const searchName = ref("");
const pageSize = ref(10);
const currentPage = ref(1);
const visible = ref(false);
const newProject = ref("");
let type = ref("add");
let selectIndex = "";
const list = computed(() => {
  let arr = intentedProjectList.value.map((item, index) => {
    return {
      name: item,
      index: index + 1,
    };
  });
  return arr.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value);
});
const total = computed(() => {
  return intentedProjectList.value.length;
});
function onSizeChange(e) {
  currentPage.value = 1;
  pageSize.value = e;
}
function onCurrentChange(e) {
  currentPage.value = e;
}
function search() {
  if (!searchName.value) {
    return;
  }
  currentPage.value = 1;
  let pattern = new RegExp(searchName.value, "i");
  intentedProjectList.value = intentedProjectList.value.filter((item) => pattern.test(item));
}

function add() {
  type.value = "add";
  newProject.value = "";
  visible.value = true;
}
function inputChange(e) {
  if (!e) {
    intentedProjectList.value = corpInfo.intentedProjectList;
  }
}
let currentItem = ref({});
async function remove(item) {
  ElMessageBox.confirm("是否删除服务项目?", { type: "warning" }).then(() => {
    let index = intentedProjectList.value.indexOf(currentItem.value.name);
    if (index >= 0) {
      intentedProjectList.value.splice(index, 1);
      updateCorp();
      visible.value = false;
    }
  });
}

async function edit(item) {
  currentItem.value = item;
  newProject.value = item.name;
  selectIndex = item.index;
  type.value = "edit";
  visible.value = true;
}

// 更新corp表, 更新常用诊断
async function updateCorp() {
  await updateCorpInfo({ intentedProjectList: intentedProjectList.value });
}
function onClose() {
  visible.value = false;
  newProject.value = "";
}
async function saveDiagnosis() {
  if (newProject.value.trim() === "") {
    ElMessage.info("请输入项目名称!");
    return;
  }
  if (type === "edit") {
    intentedProjectList.value[selectIndex - 1] = newProject.value;
  } else {
    if (intentedProjectList.value.indexOf(newProject.value) !== -1) {
      ElMessage.error("该项目已添加!");
      return;
    }
    await intentedProjectList.value.unshift(newProject.value);
  }

  updateCorp();
  visible.value = false;
}
</script>

<style scoped>
.table-action-btn {
  padding-left: 0;
  padding-right: 0;
}
</style>