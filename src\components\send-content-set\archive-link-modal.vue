<template>
  <el-dialog :model-value="showDialog" title="请选择建档链接" :width="width" center @close="close">
    <div class="flex h-[50vh]" :class="teams.length ? '' : 'justify-center items-center'">
      <template v-if="teams.length">
        <el-scrollbar class="flex-shrink-0 border-r border-gray-200 h-full">
          <div v-for="team in teams" :key="team.teamId"
            class="p-10px border-b border-gray-200 cursor-pointer hover:text-blue-500"
            :class="currentTeam && currentTeam.teamId === team.teamId ? 'text-blue-500' : ''"
            @click="currentTeam = team">
            {{ team.name }}
          </div>
        </el-scrollbar>
        <el-scrollbar class="flex-grow flex-shrink-0 h-full">
          <div v-for="qrcode in qrcodes" :key="qrcode.id"
            class="p-10px flex items-center border-b border-gray-200 cursor-pointer hover:text-blue-500"
            :class="currentQrcode && currentQrcode.id === qrcode.id ? 'text-blue-500' : ''"
            @click="currentQrcode = qrcode">
            <div class="flex-grow"> {{ qrcode.name }}</div>
            <el-icon class="text-blue-500"
              :class="currentQrcode && currentQrcode.id === qrcode.id ? '' : 'opacity-0'"><Select /></el-icon>
          </div>
        </el-scrollbar>
      </template>
      <empty-data v-else :top="0" :title="!corpUserId || corpUserId.length === 0?'请选择使用员工':'暂无团队'" padding="0 0 15px"
        text-top="10px" :image-width="200"></empty-data>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close()">取消</el-button>
        <el-button type="primary" @click="confirm()"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref, watch } from 'vue';
import { storeToRefs } from 'pinia'
import { teamStore } from "@/store/team";
import EmptyData from "@/components/empty-data.vue";

const { allTeams } = storeToRefs(teamStore());

const emits = defineEmits(['close', 'change'])
const props = defineProps({
  corpUserId: { type: Array, default: () => [] },
  link: { default: '' },
  width: { type: Number },
  visible: { type: Boolean, default: false },
})
const { getArchiveLink } = teamStore();

const showDialog = ref(false);

const teams = computed(() => {
  return allTeams.value.filter(i => {
    const hasQrcodes = i && Array.isArray(i.qrcodes) && i.qrcodes.length;
    const memberList = i.memberList && Array.isArray(i.memberList) ? i.memberList : [];
    const corpUserId = Array.isArray(props.corpUserId) ? props.corpUserId : [];
    const hasCorpUserId = memberList.some(i => corpUserId.includes(i));
    return hasQrcodes && hasCorpUserId
  })
})
const currentTeam = ref(null);
const currentQrcode = ref(null);
const qrcodes = computed(() => currentTeam.value && Array.isArray(currentTeam.value.qrcodes) ? currentTeam.value.qrcodes.map(i => ({
  ...i,
  qrcodeArchiveLink: getArchiveLink(currentTeam.value.teamId, i.id),
  teamId: currentTeam.value.teamId
})) : [])
const close = () => {
  emits('close')
}

const default_picurl = 'https://796f-youcan-clouddev-1-8ewcqf31dbb2b5-1317294507.tcb.qcloud.la/other/link-card.jpg?sign=debcd363edae2efb6caed0488e2543ff&t=1718959730';
function confirm() {
  if (currentQrcode.value) {
    const card = currentQrcode.value.card;
    const link = {
      url: currentQrcode.value.qrcodeArchiveLink,
      title: card && card.title ? card.title : '在线建档',
      desc: card && card.desc ? card.desc : '点击链接，完成在线建档，让我们更精准的为您服务。',
      picurl: card && card.imgUrl ? card.imgUrl : default_picurl,
      id: currentQrcode.value.id,
      name: currentQrcode.value.name,
      teamId: currentQrcode.value.teamId,
    }
    emits('change', link)
  } else {
    emits('change', '')
  }
  close()
}

watch(() => props.visible, n => {
  if (n) {
    // 只有一个团队 并且团队只有只有一个二维码 默认选中
    if (teams.value.length === 1 && teams.value[0].qrcodes && teams.value[0].qrcodes.length === 1) {
      currentTeam.value = teams.value[0];
      currentQrcode.value = qrcodes.value[0];
      confirm()
    } else if (props.link && props.link.teamId) {
      currentTeam.value = teams.value.find(t => t.teamId === props.link.teamId);
      currentQrcode.value = qrcodes.value.find(q => q.id === props.link.id)
      showDialog.value = true
    } else {
      currentTeam.value = teams.value[0]
      currentQrcode.value = null
      showDialog.value = true
    }
  } else {
    showDialog.value = false
  }
})

</script>
<style scoped>
.tag__item {
  margin-right: 15px;
  margin-bottom: 15px;
  padding: 6px 10px;
  min-width: 80px;
}
</style>