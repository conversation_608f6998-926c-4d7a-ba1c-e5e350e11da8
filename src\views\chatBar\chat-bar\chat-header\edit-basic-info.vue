<template>
  <div v-if="visible" v-loading="loading || tempLoading" class="h-screen w-screen fixed inset-0 z-99 bg-white">
    <page-wrapper v-if="infoTempList.length" @back="close()">
      <template #header>
        <div font-16 font-semibold px-15 border-bottom class="py-12px">基础信息</div>
      </template>
      <form-template ref="formRef" :items="infoTempList" :rule="rule" :form="forms" @change="change($event)" />
      <template #footer>
        <div class="relative z-2" bg-fff flex items-center px-15 py-10 common-shadow--r>
          <el-button plain class="flex-grow mr-15px" @click="close()">取消</el-button>
          <el-button :loading="loading" class="flex-grow" type="primary" @click="confirm()">保存</el-button>
        </div>
      </template>
    </page-wrapper>
  </div>
  <customer-repeat :visible="repeatVisible" :customer="repeatCustomer" :width="300" @acceptCustomer="acceptCustomer" operateType="edit" @close="repeatVisible = false"></customer-repeat>
  <mobile-repeat :width="300" :visible="mobileRepeatVisible" :customers="mobileReatCustomers" @close="mobileRepeatVisible = false"></mobile-repeat>
</template>
<script setup>
import { ref, onMounted, toRefs, watch, computed, inject } from "vue";
import { storeToRefs } from "pinia";
import { ElMessage } from "element-plus";
import { updateMember } from "@/api/member";
import { templateStore } from "@/store/template";
import formTemplate from "../form-template/edit/index.vue";
import PageWrapper from "../../components/page-wrapper.vue";
import customerRepeat from "@/views/member/components/customer-repeat/index";
import repeatComponentApi from "@/views/member/components/customer-repeat/api";
import mobileRepeat from "@/components/mobile-repeat/index.vue";
import mobileRepeatApi from "@/components/mobile-repeat/api";
import { ElMessageBox } from "element-plus";
import { memberStore } from "@/store/member";
import validate from "@/utils/validate";
const emits = defineEmits(["close", "refresh"]);
const props = defineProps({
  visible: { type: Boolean, default: false },
  customer: { type: Object, default: () => ({}) },
  externalUserId: { type: String, default: "" },
});
const { repeatVisible, customer: repeatCustomer, getReatCustomer, idCardIsRepeat } = repeatComponentApi();
const { mobileReatCustomers, mobileRepeatVisible, getMobileReatCustomer } = mobileRepeatApi();
const { corpInfo } = storeToRefs(memberStore());
const { visible } = toRefs(props);
const store = templateStore();
const { corpTemplateList } = storeToRefs(store);
const infoTempList = computed(() => getTemplate("baseTemplate"));
const form = ref({});
const formRef = ref();
const forms = computed(() => ({ ...props.customer, ...form.value }));

function getTemplate(templateType) {
  const temp = Array.isArray(corpTemplateList.value) ? corpTemplateList.value.find((i) => i.templateType === templateType) : [];
  const tempList = temp && Array.isArray(temp.templateList) ? temp.templateList.filter((i) => i && i.fieldStatus !== "disable" && i.operateType !== "onlyRead") : [];
  return tempList;
}
onMounted(() => {});
const tempLoading = ref(false);
async function getLatestTemp() {
  tempLoading.value = true;
  await getTempateByType("baseTemplate");
  tempLoading.value = false;
}
watch(visible, (n) => {
  if (n) {
    form.value = {};
    getLatestTemp();
    repeatVisible.value = false;
    idCardIsRepeat.value = false;
  }
});

// 获取重复的档案数据
function acceptCustomer() {
  repeatVisible.value = false;
  if (repeatCustomer.value.externalUserId && repeatCustomer.value.externalUserId !== props.externalUserId) {
    ElMessageBox.alert("该档案已经绑定微信联系人，不允许绑定在当前微信联系人下。");
    form.value.idCard = "";
    form.value.cardType = "";
    form.value.sex = "";
    form.value.age = "";
    return;
  }
  form.value = Object.assign(form.value, repeatCustomer.value);
}

async function change({ title, value }) {
  form.value[title] = value;
  if (title === "idCard" && validate.isChinaId(value)[0]) handleIdCardChange(value, title);
  if (title === "mobile" && validate.isMobile(value)) getMobileReatCustomer(value);

  if (title === "customerNumber" && corpInfo.value.isConnectHis) handleIdCardChange(value, title);
}

async function handleIdCardChange(value, title) {
  await getReatCustomer(value, title);
}

function close() {
  emits("close");
}
const loading = ref(false);
async function confirm() {
  if (idCardIsRepeat.value) {
    ElMessageBox.alert("该档案已经存在，不允许重复添加");
    return;
  }
  if (Object.keys(form.value).length === 0) {
    emits("close");
    return;
  }
  if (loading.value || !props.customer._id || !formRef.value.verify()) return;
  loading.value = true;
  const { success, message = "更新客户信息失败" } = await updateMember(props.customer._id, { ...form.value });
  if (success) {
    ElMessage.success(message || "更新客户信息成功");
    emits("refresh");
    emits("close");
  } else {
    ElMessage.error(message);
  }
  loading.value = false;
}
</script>
<style>
.create-customer .el-form-item__label {
  height: auto;
  font-size: 14px;
  padding-top: 9px;
  padding-bottom: 9px;
  line-height: 18px;
}
</style>
