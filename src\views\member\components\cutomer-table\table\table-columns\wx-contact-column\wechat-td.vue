<template>
  <div class="inline-flex w-full items-center group" :class="canChat ? 'cursor-pointer text-blue-500' : 'text-gray-500'" @click="chat()">
    <div class="truncate underline-offset-2 group-hover:underline">
      {{ wechatName }}
    </div>
    <div v-if="wechatName" class="flex-shrink-0 ml-5px text-12px text-[#66CB70]">@微信</div>
    <div v-if="canUnbind" class="flex-shrink-0 ml-auto pl-10px whitespace-nowrap hidden cursor-pointer text-14px text-gray-400 group-hover:block" @click.stop="unbind()">解绑</div>
  </div>
</template>
<script setup>
import { computed } from "vue";
import { storeToRefs } from "pinia";
import { ElMessage, ElMessageBox } from "element-plus";
import { updateMember } from "@/api/member";
import wxContact from "@/store/wxContact";
import { openChatWindow } from "@/utils/common";

const emits = defineEmits(["onUnbind"]);
const props = defineProps({
  canUnbind: { type: Boolean, default: false },
  customer: { type: Object, default: () => ({}) },
});

const { contactFollowUsers, contactName } = storeToRefs(wxContact());
const externalUserId = computed(() => props.customer.externalUserId);
const wechatName = computed(() => contactName.value[externalUserId.value] || "");
const canChat = computed(() => {
  const userId = localStorage.getItem("userId");
  const users = contactFollowUsers.value[externalUserId.value] || [];
  return users.includes(userId);
});

function chat() {
  openChatWindow(props.customer._id, externalUserId.value);
}

function unbind() {
  ElMessageBox.confirm("确定解绑该客户微信吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    let { success, message } = await updateMember(props.customer._id, { externalUserId: "", unionid: "", unbindExternalUserId: externalUserId.value });
    if (success) {
      emits("onUnbind");
      ElMessage({
        message: "解绑成功",
        type: "success",
      });
    } else {
      ElMessage({
        message: message,
        type: "error",
      });
    }
  });
}
</script>
<style lang="scss" scoped></style>
