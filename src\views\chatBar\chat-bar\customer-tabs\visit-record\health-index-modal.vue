<template>
  <el-dialog append-to-body :model-value="visible" :width="width" :title="title" @close="close">
    <el-scrollbar wrap-class="el-dialog-body-full-scroll">
      <display-form :tempList="healthTempList" :format="format" :form="customer" />
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button class="w-100px" @click="close()">关闭</el-button>
        <el-button class="w-100px" type="primary" @click="edit()">编辑</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, onMounted } from "vue";
import { storeToRefs } from "pinia";
import displayForm from "../../form-template/display/index.vue";
import { templateStore } from "@/store/template";

const props = defineProps({
  visible: { type: Boolean, default: false },
  title: { type: String, default: "" },
  width: { type: Number },
  customer: { type: Object, default: () => ({}) },
});
const store = templateStore();
const { corpTemplateList } = storeToRefs(store);
const { getCorpTemplate } = store;
const healthTempList = computed(() => getTemplate("healthTemplate"));
onMounted(() => {
  if (!Array.isArray(corpTemplateList.value) || corpTemplateList.value.length === 0) getCorpTemplate();
});

const format = {
  surgicalHistory: (val) => (Array.isArray(val) ? val.filter((i) => i && i !== "有").join(" ") : ""),
};

function getTemplate(templateType) {
  const temp = Array.isArray(corpTemplateList.value) ? corpTemplateList.value.find((i) => i.templateType === templateType) : [];
  const tempList = temp && Array.isArray(temp.templateList) ? temp.templateList.filter((i) => i && i.fieldStatus !== "disable" && i.operateType !== "onlyRead") : [];
  return tempList;
}

const emits = defineEmits(["close", "edit"]);
function close() {
  emits("close");
}

function edit() {
  close();
  emits("edit");
}
</script>
<style lang="scss" scoped></style>
