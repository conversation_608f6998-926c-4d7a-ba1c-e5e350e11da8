<template>
  <div class="three-d-viewer" ref="container">
    <div class="rotate-instructions" v-if="showInstructions">
      拖动鼠标可全方位360°旋转模型
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from "vue";
import * as THREE from "three";
import { OBJLoader } from "three/examples/jsm/loaders/OBJLoader";
import { MTLLoader } from "three/examples/jsm/loaders/MTLLoader";
import { PLYLoader } from "three/examples/jsm/loaders/PLYLoader";
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls";
import { TrackballControls } from "three/examples/jsm/controls/TrackballControls";

const props = defineProps({
  objPath: {
    type: String,
    default: "",
  },
  mtlPath: {
    type: String,
    default: "",
  },
  plyPath: {
    type: String,
    default: "",
  },
  modelType: {
    type: String,
    default: "obj", // 'obj' 或 'ply'
    validator: (value) => ["obj", "ply"].includes(value),
  },
  width: {
    type: Number,
    default: 800,
  },
  height: {
    type: Number,
    default: 600,
  },
  textureUrl: {
    type: String,
    default: "",
  },
  flipX: {
    type: Boolean,
    default: false,
  },
  flipY: {
    type: Boolean,
    default: false,
  },
  flipZ: {
    type: Boolean,
    default: true, // 默认翻转Z轴，修正常见的"3D反了"问题
  },
  autoRotate: {
    type: Boolean,
    default: false,
  },
  allowVerticalFlip: {
    type: Boolean,
    default: true, // 是否允许垂直方向完全翻转
  },
  controlType: {
    type: String,
    default: "trackball", // 'orbit' 或 'trackball'
    validator: (value) => ["orbit", "trackball"].includes(value),
  },
});

const container = ref(null);
let scene, camera, renderer, controls, model;
let texture = null;
const showInstructions = ref(true);

const initThree = () => {
  // Create scene
  scene = new THREE.Scene();
  scene.background = new THREE.Color(0xffffff); // 更改背景为白色，增加对比度

  // Create camera
  camera = new THREE.PerspectiveCamera(75, props.width / props.height, 0.1, 1000);
  camera.position.z = 5;

  // Create renderer with improved settings
  renderer = new THREE.WebGLRenderer({
    antialias: true,
    precision: "highp", // 高精度渲染
    alpha: true, // 允许透明
  });
  renderer.setSize(props.width, props.height);
  renderer.setPixelRatio(window.devicePixelRatio);
  renderer.outputEncoding = THREE.sRGBEncoding; // 使用sRGB编码增强色彩
  renderer.physicallyCorrectLights = true; // 物理上正确的光照
  renderer.toneMappingExposure = 1.2; // 增加曝光度
  renderer.toneMapping = THREE.ACESFilmicToneMapping; // 电影级色调映射
  container.value.appendChild(renderer.domElement);

  // 增强光照系统
  const ambientLight = new THREE.AmbientLight(0xffffff, 0.7); // 增加环境光强度
  scene.add(ambientLight);

  // 添加主光源 - 更明亮
  const mainLight = new THREE.DirectionalLight(0xffffff, 1.0);
  mainLight.position.set(5, 5, 5);
  scene.add(mainLight);

  // 添加填充光 - 减少阴影过暗
  const fillLight = new THREE.DirectionalLight(0xffffff, 0.5);
  fillLight.position.set(-5, -2, 3);
  scene.add(fillLight);

  // 添加环境光球提升色彩还原度
  const pmremGenerator = new THREE.PMREMGenerator(renderer);
  pmremGenerator.compileEquirectangularShader();

  // 创建简单的环境贴图
  const envLight = new THREE.HemisphereLight(0xffffff, 0x444444, 0.6);
  scene.add(envLight);

  // 根据控制类型选择不同的控制器
  if (props.controlType === "trackball") {
    // 使用TrackballControls实现完全自由的旋转
    controls = new TrackballControls(camera, renderer.domElement);
    controls.rotateSpeed = 2.0;
    controls.zoomSpeed = 1.2;
    controls.panSpeed = 0.8;
    controls.noZoom = false;
    controls.noPan = false;
    controls.staticMoving = true;
    controls.dynamicDampingFactor = 0.2;

    // 如果需要启用自动旋转
    if (props.autoRotate) {
      const clock = new THREE.Clock();
      const originalUpdate = controls.update;
      controls.update = function () {
        // 添加自动旋转功能
        const delta = clock.getDelta();
        this.rotation.y += delta * 0.5; // 水平自动旋转
        return originalUpdate.apply(this, arguments);
      };
    }

    // 添加事件监听
    controls.addEventListener("start", () => {
      showInstructions.value = false;
    });
  } else {
    // 使用原来的OrbitControls
    controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.rotateSpeed = 1.0;
    controls.enableRotate = true;

    // 水平方向保持完全自由旋转
    controls.minAzimuthAngle = -Infinity;
    controls.maxAzimuthAngle = Infinity;

    // 尝试最大限度放开垂直旋转限制
    controls.minPolarAngle = -Infinity;
    controls.maxPolarAngle = Infinity;

    // 其他OrbitControls设置
    controls.enablePan = true;
    controls.screenSpacePanning = true;
    controls.autoRotate = props.autoRotate;
    controls.autoRotateSpeed = 2.0;

    controls.addEventListener("start", () => {
      showInstructions.value = false;
    });
  }
};

const loadTexture = () => {
  if (props.textureUrl) {
    const textureLoader = new THREE.TextureLoader();
    texture = textureLoader.load(props.textureUrl, (loadedTexture) => {
      loadedTexture.encoding = THREE.sRGBEncoding; // 使用sRGB编码增强纹理色彩
      loadedTexture.anisotropy = 16; // 增强斜视角下的纹理清晰度
      loadedTexture.needsUpdate = true;

      // 如果模型已经加载，则应用纹理
      if (model) {
        applyTextureToModel(model);
      }
    });
  }
};

const applyTextureToModel = (object) => {
  if (!texture) return;

  // 遍历所有材质并应用增强的纹理
  object.traverse((child) => {
    if (child.isMesh) {
      if (Array.isArray(child.material)) {
        child.material.forEach((mat) => {
          mat.map = texture;
          enhanceMaterial(mat);
        });
      } else if (child.material) {
        child.material.map = texture;
        enhanceMaterial(child.material);
      }
    }
  });
};

// 增强材质的视觉效果
const enhanceMaterial = (material) => {
  material.roughness = 0.5; // 降低粗糙度，增加光泽
  material.metalness = 0.2; // 轻微金属感
  material.envMapIntensity = 1.0; // 环境贴图强度
  material.needsUpdate = true;
  material.color.setScalar(1.05); // 稍微增亮颜色
};

const loadModel = () => {
  // 清除之前加载的模型
  if (model) {
    scene.remove(model);
    model = null;
  }

  if (props.modelType === "obj") {
    loadObjModel();
  } else if (props.modelType === "ply") {
    loadPlyModel();
  }
};

const loadObjModel = () => {
  // Load the MTL file
  const mtlLoader = new MTLLoader();
  mtlLoader.load(props.mtlPath, (materials) => {
    materials.preload();

    // 增强MTL材质
    for (const key in materials.materials) {
      if (materials.materials.hasOwnProperty(key)) {
        const mat = materials.materials[key];
        mat.side = THREE.DoubleSide; // 双面渲染
        // 增加材质亮度
        if (mat.color) {
          mat.color.multiplyScalar(1.2);
        }
      }
    }

    // Load the OBJ file
    const objLoader = new OBJLoader();
    objLoader.setMaterials(materials);
    objLoader.load(props.objPath, (object) => {
      processLoadedModel(object);

      // 增强所有材质效果
      object.traverse((child) => {
        if (child.isMesh) {
          if (Array.isArray(child.material)) {
            child.material.forEach((mat) => enhanceMaterial(mat));
          } else if (child.material) {
            enhanceMaterial(child.material);
          }
        }
      });

      // 应用纹理（如果有）
      if (texture) {
        applyTextureToModel(object);
      }
    });
  });
};

const loadPlyModel = () => {
  const plyLoader = new PLYLoader();
  plyLoader.load(props.plyPath, (geometry) => {
    // PLY文件加载后，创建增强的材质
    let material;

    if (texture) {
      // 如果有纹理，使用带纹理的PBR材质
      material = new THREE.MeshStandardMaterial({
        map: texture,
        flatShading: false, // 平滑着色
        side: THREE.DoubleSide,
        roughness: 0.5,
        metalness: 0.2,
        envMapIntensity: 1.0,
      });
    } else {
      // 使用默认增强材质
      material = new THREE.MeshStandardMaterial({
        color: 0xaaaaaa, // 浅灰色而不是深灰色
        flatShading: false,
        side: THREE.DoubleSide,
        roughness: 0.5,
        metalness: 0.2,
      });
    }

    // 计算法线以便正确显示光照
    geometry.computeVertexNormals();

    // 计算和应用UV坐标，以便正确显示纹理
    if (!geometry.attributes.uv && texture) {
      generateUVs(geometry);
    }

    const mesh = new THREE.Mesh(geometry, material);
    processLoadedModel(mesh);
  });
};

// 为没有UV坐标的几何体生成简单的UV映射
const generateUVs = (geometry) => {
  const positions = geometry.attributes.position.array;
  const count = positions.length / 3;
  const uvs = new Float32Array(count * 2);

  // 找出几何体的边界框
  let minX = Infinity,
    maxX = -Infinity;
  let minY = Infinity,
    maxY = -Infinity;
  let minZ = Infinity,
    maxZ = -Infinity;

  for (let i = 0; i < count; i++) {
    const x = positions[i * 3];
    const y = positions[i * 3 + 1];
    const z = positions[i * 3 + 2];

    minX = Math.min(minX, x);
    maxX = Math.max(maxX, x);
    minY = Math.min(minY, y);
    maxY = Math.max(maxY, y);
    minZ = Math.min(minZ, z);
    maxZ = Math.max(maxZ, z);
  }

  // 基于位置计算简单的UV坐标
  for (let i = 0; i < count; i++) {
    const x = positions[i * 3];
    const y = positions[i * 3 + 1];

    // 将x和y坐标归一化到0-1范围内作为UV坐标
    const u = (x - minX) / (maxX - minX);
    const v = (y - minY) / (maxY - minY);

    uvs[i * 2] = u;
    uvs[i * 2 + 1] = v;
  }

  geometry.setAttribute("uv", new THREE.BufferAttribute(uvs, 2));
};

const processLoadedModel = (object) => {
  // 居中模型
  const box = new THREE.Box3().setFromObject(object);
  const center = box.getCenter(new THREE.Vector3());
  object.position.sub(center);

  // 根据props应用翻转
  if (props.flipX) {
    object.scale.x *= -1;
  }
  if (props.flipY) {
    object.scale.y *= -1;
  }
  if (props.flipZ) {
    object.scale.z *= -1;
  }

  model = object;
  scene.add(object);

  // 调整相机以适应模型
  const size = box.getSize(new THREE.Vector3());
  const maxDim = Math.max(size.x, size.y, size.z);
  const fov = camera.fov * (Math.PI / 180);
  let cameraZ = Math.abs(maxDim / 2 / Math.tan(fov / 2));
  cameraZ *= 1.5; // 添加padding

  camera.position.z = cameraZ;
  camera.updateProjectionMatrix();

  // 更新控制器
  controls.update();
};

const animate = () => {
  requestAnimationFrame(animate);
  controls.update(); // 确保在每一帧更新控制器
  renderer.render(scene, camera);
};

const onWindowResize = () => {
  camera.aspect = window.innerWidth / window.innerHeight;
  camera.updateProjectionMatrix();
  renderer.setSize(window.innerWidth, window.innerHeight);
};

const dispose = () => {
  if (model) {
    scene.remove(model);
    model = null;
  }

  if (controls) {
    controls.dispose(); // 确保正确释放 controls
  }

  if (renderer) {
    renderer.dispose();
  }

  window.removeEventListener("resize", onWindowResize);
};

onMounted(() => {
  initThree();
  loadTexture(); // 先加载纹理
  loadModel(); // 再加载模型
  animate();
  window.addEventListener("resize", onWindowResize);
});

// 监听属性变化以重新加载模型
watch(
  () => [props.modelType, props.objPath, props.mtlPath, props.plyPath],
  () => {
    if (scene) loadModel();
  }
);

// 监听纹理URL变化
watch(
  () => props.textureUrl,
  () => {
    if (props.textureUrl) {
      loadTexture();
    }
  }
);

// 监听翻转属性变化
watch(
  () => [props.flipX, props.flipY, props.flipZ],
  () => {
    if (model) {
      // 重置缩放
      model.scale.set(1, 1, 1);

      // 应用新的翻转设置
      if (props.flipX) model.scale.x *= -1;
      if (props.flipY) model.scale.y *= -1;
      if (props.flipZ) model.scale.z *= -1;
    }
  }
);

// 监听 autoRotate 属性变化
watch(
  () => props.autoRotate,
  (newValue) => {
    if (controls) {
      controls.autoRotate = newValue;
    }
  }
);

// 监听垂直翻转设置变化
watch(
  () => props.allowVerticalFlip,
  (newValue) => {
    if (controls) {
      if (newValue) {
        controls.minPolarAngle = 0;
        controls.maxPolarAngle = Math.PI * 2;
      } else {
        controls.minPolarAngle = 0;
        controls.maxPolarAngle = Math.PI;
      }
    }
  }
);

// 监听控制器类型变化
watch(
  () => props.controlType,
  () => {
    if (controls) {
      controls.dispose();
    }

    if (scene && camera && renderer) {
      // 重新初始化控制器
      if (props.controlType === "trackball") {
        controls = new TrackballControls(camera, renderer.domElement);
        controls.rotateSpeed = 2.0;
        controls.zoomSpeed = 1.2;
        controls.panSpeed = 0.8;
        controls.noZoom = false;
        controls.noPan = false;
        controls.staticMoving = true;
        controls.dynamicDampingFactor = 0.2;
      } else {
        controls = new OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;
        controls.dampingFactor = 0.05;
        controls.rotateSpeed = 1.0;
        controls.minPolarAngle = -Infinity;
        controls.maxPolarAngle = Infinity;
        controls.minAzimuthAngle = -Infinity;
        controls.maxAzimuthAngle = Infinity;
        controls.enableRotate = true;
        controls.enablePan = true;
        controls.screenSpacePanning = true;
        controls.autoRotate = props.autoRotate;
      }
    }
  }
);

onBeforeUnmount(() => {
  dispose();
});
</script>

<style scoped>
.three-d-viewer {
  width: 100%;
  height: 100%;
  min-height: 400px;
  position: relative;
}

.rotate-instructions {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 14px;
  pointer-events: none;
  transition: opacity 0.5s;
  z-index: 10;
}
</style>
