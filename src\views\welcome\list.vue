<template>
  <my-layout bg-fff common-shadow>
    <layout-item>
      <div p-15 flex items-center>
        <el-button mr-10 type="primary" :icon="Plus" @click="add">新建欢迎语</el-button>
        <el-text>欢迎语由机构统一配置，配置后，客户将在添加员工为联系人后收到该欢迎语</el-text>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <el-table stripe height="100%" :data="list" v-loading="loading">
        <el-table-column property="content" label="消息内容" :min-width="200">
          <template #default="{ row: { content, files, hasImage, hasLink, hasVideo } }">
            <el-popover :key="_id" placement="top-start" :width="320" trigger="hover">
              <template #reference>
                <div flex>
                  <div overflow-hidden>
                    <el-text truncated>{{ content }}</el-text>
                  </div>
                  <div v-if="hasVideo" flex-shrink-0>【视频】</div>
                  <div v-if="hasImage" flex-shrink-0>【图片】</div>
                  <div v-if="hasLink" flex-shrink-0>【网页】</div>
                </div>
              </template>
              <div flex>
                <el-text>{{ content }}</el-text>
                <div v-if="hasVideo" flex-shrink-0>【视频】</div>
                <div v-if="hasImage" flex-shrink-0>【图片】</div>
                <div v-if="hasLink" flex-shrink-0>【网页】</div>
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column property="time" label="配置时间" :min-width="150" />
        <el-table-column property="members" label="使用员工" :min-width="200">
          <template #default="{ row: { memberList = [] } }">
            <el-popover :key="_id" placement="top-start" :width="320" trigger="hover">
              <template #reference>
                <div>
                  <span v-for="(userid, idx) in memberList" :key="userid + '_' + idx">
                    <ww-user :openid="userid"></ww-user>
                    <span v-if="idx !== memberList.length - 1">、</span>
                  </span>
                </div>
              </template>
              <span v-for="(userid, idx) in memberList" :key="userid">
                <ww-user :openid="userid"></ww-user>
                <span v-if="idx !== memberList.length - 1">、</span>
              </span>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column fixed="right" class-name="el-table-fixed-column--shadow" label="操作" width="100">
          <template #default="{ row }">
            <el-text type="primary" class="pointer" @click="showDetail(row)">编辑</el-text><span mr-5></span>
            <el-text type="danger" class="pointer" @click="remove(row)">删除</el-text>
          </template>
        </el-table-column>
      </el-table>
    </layout-main>
    <layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="currentPage" @handle-size-change="onSizeChange"
        @handle-current-change="onCurrentChange" />
    </layout-item>
  </my-layout>
</template>
<script setup>
import { ref, onActivated } from 'vue';
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus';
import { getWelcomeList, removeWelcome } from "@/api/knowledgeBase";
import dayjs from 'dayjs';
import MyLayout, { LayoutMain, LayoutItem } from '@/components/layout';
import pagination from "@/components/pagination/pagination.vue";
import WwUser from '@/components/ww-user/index.vue';
import { Plus } from '@element-plus/icons-vue';

onActivated(() => getList());
const list = ref([]);
const total = ref(0);
const pageSize = ref(10);
const currentPage = ref(1);
const loading = ref(false);
function onSizeChange(e) {
  pageSize.value = e;
  getList()
}
function onCurrentChange(e) {
  currentPage.value = e;
  getList()
}

async function getList() {
  loading.value = true;
  const { success, data = {}, message } = await getWelcomeList(localStorage.getItem('corpId'), currentPage.value, pageSize.value)
  const { list: words = [], total: count = 0 } = data;
  list.value = words.map(i => {
    const files = Array.isArray(i.files) ? i.files : [];
    return {
      ...i,
      hasLink: files.some(i => i.type === 'link'),
      hasVideo: files.some(i => i.type === 'video'),
      hasImage: files.some(i => i.type === 'image'),
      time: i.createTime ? dayjs(i.createTime).format('YYYY-MM-DD HH:mm') : ''
    }
  });
  total.value = count;
  loading.value = false;
  if (!success) ElMessage.error(message);
}
getList()

async function remove(data) {
  await ElMessageBox.confirm('确定删除该欢迎语吗？', '提示', { center: true });
  const { success, message } = await removeWelcome(data._id, localStorage.getItem("userId"));
  success ? ElMessage.success('删除成功') && getList() : ElMessage.error(message);
}
const router = useRouter();
function showDetail(row) {
  router.push({ name: "WELCOMEDETAIL", params: { id: row._id } })
}

function add() {
  router.push({ name: "WELCOMEDETAIL" })
}
</script>
<style scoped>
.hiddenContent {
  width: auto;
}
</style>

  