<template>
  <div>
    <!-- 批量回访弹框 -->
    <el-dialog v-model="batchModalVisible" title="批量回访" :width="batchModalWidth" :close-on-click-modal="false" :destroy-on-close="true" center>
      <div class="flex flex-row items-center gap-4 py-6">
        <el-button type="primary" size="large" class="w-200px" @click="handleNewTask">新增任务</el-button>
        <el-button type="success" size="large" class="w-200px" @click="handleUseTemplate">使用模版</el-button>
        <el-button type="warning" size="large" class="w-200px" @click="handleBatchPlan">批量回访计划</el-button>
      </div>
    </el-dialog>
    <team-plan-drawer :selectCustomers="selectCustomers" :type="taskDrawerType" :teamId="teamId" :visible="teamPlanDrawerVisible" @close="closeTeamPlanDrawer" @onSelect="selectPlan" />
    <task-drawer :selectCustomers="selectCustomers" :type="taskDrawerType" :teamId="teamId" :memberList="memberList" :customer="customer" :data="task" :visible="taskDrawerVisible" @close="closeTaskDrawer" @change="handleChangeTask" />
    <new-plan-drawer :selectCustomers="selectCustomers" :type="taskDrawerType" ref="newPlanDrawerRef" :customer="customer" :teamId="teamId" :memberList="memberList" :plan="currentPlan" :visible="newPlanDrawerVisible" @close="closeNewPlanDrawer" @onEdit="editTask" @change="handlePlanChange" />
    
    <!-- 新增批量回访计划弹窗 -->
    <el-dialog v-model="batchPlanVisible" title="批量回访计划" :width="1200" :close-on-click-modal="false" :destroy-on-close="true" fullscreen>
      <batch-followup-plan :visible="batchPlanVisible" :customerList="selectCustomers" :teamId="teamId" @close="closeBatchPlan" @success="handleBatchPlanSuccess" />
    </el-dialog>
  </div>
</template>
<script setup>
import { ref, computed } from "vue";
import useModal from "@/hooks/useModal";
import { getCustomTeamData } from "@/api/corp";
import { getCustomerInfoById } from "@/api/member";
import teamPlanDrawer from "@/components/manage-plan-drawer/team-plan-drawer.vue";
import taskDrawer from "@/components/manage-plan-drawer/task-drawer.vue";
import newPlanDrawer from "@/components/manage-plan-drawer/new-plan-drawer.vue";
import batchFollowupPlan from "@/views/e-store-report/batch-followup-plan.vue";

const props = defineProps({
  selectCustomers: {
    type: Array,
    default: () => [],
  },
  customer: {
    type: Object,
    default: () => ({}),
  },
  teamId: {
    type: [String, Number],
    required: true,
  },
});

const emits = defineEmits(["change"]);

const { close: closeBatchModal, show: showBatchModal, visible: batchModalVisible, width: batchModalWidth } = useModal(400);
const drawerLoading = ref(false);
const customerTeamIds = ref([]);
const memberList = ref([]);
const teamPlanDrawerVisible = ref(false);
const currentPlan = ref({});
const newPlanDrawerVisible = ref(false);
const taskDrawerVisible = ref(false);
const task = ref({});
const taskDrawerType = ref("");
const newPlanDrawerRef = ref(null);
const batchPlanVisible = ref(false);

const canSet = computed(() => customerTeamIds.value.includes(props.teamId));

// 获取客户团队信息
async function getCustomerTeams() {
  if (!props.customer || !props.customer._id) {
    customerTeamIds.value = [];
    return;
  }
  const { data } = await getCustomerInfoById(localStorage.getItem("userId"), { _id: props.customer._id });
  const customerData = data && Array.isArray(data.data) ? data.data[0] : null;
  if (customerData && Array.isArray(customerData.teamId)) {
    customerTeamIds.value = [...customerData.teamId];
  } else if (customerData && typeof customerData.teamId === "string" && customerData.teamId.trim()) {
    customerTeamIds.value = [customerData.teamId];
  } else {
    customerTeamIds.value = [];
  }
}

// 获取团队成员列表
async function getTeamMembers() {
  const params = { teamIds: [props.teamId], corpId: localStorage.getItem("corpId"), fields: ["memberList"] };
  const { data } = await getCustomTeamData(params);
  const team = data && Array.isArray(data.data) ? data.data[0] : null;
  memberList.value = team && Array.isArray(team.memberList) ? team.memberList : [];
}

// 显示批量回访弹框
function showEditModal() {
  showBatchModal();
}

// 新增任务
async function handleNewTask() {
  try {
    taskDrawerType.value = "addBatchTask";
    closeBatchModal();
    taskDrawerVisible.value = true;
  } catch (e) {
    taskDrawerVisible.value = false;
  }
}

// 使用模版
async function handleUseTemplate() {
  try {
    taskDrawerType.value = "addBatchTask";
    closeBatchModal();
    teamPlanDrawerVisible.value = true;
  } catch (e) {
    drawerLoading.value = false;
  }
}

// 批量回访计划
async function handleBatchPlan() {
  try {
    closeBatchModal();
    batchPlanVisible.value = true;
  } catch (e) {
    console.error("打开批量回访计划失败:", e);
  }
}

// 选择模板
function selectPlan(plan) {
  currentPlan.value = { ...plan };
  newPlanDrawerVisible.value = true;
}

// 关闭新建计划抽屉
function closeNewPlanDrawer() {
  currentPlan.value = {};
  newPlanDrawerVisible.value = false;
}

// 关闭团队计划抽屉
function closeTeamPlanDrawer() {
  teamPlanDrawerVisible.value = false;
}

// 关闭任务抽屉
function closeTaskDrawer() {
  taskDrawerVisible.value = false;
}

// 关闭批量回访计划弹窗
function closeBatchPlan() {
  batchPlanVisible.value = false;
}

// 批量回访计划成功处理
function handleBatchPlanSuccess() {
  batchPlanVisible.value = false;
  emits("change");
}

// 编辑任务
async function editTask(row = {}) {
  if (memberList.value.length === 0) {
    await getTeamMembers();
  }
  taskDrawerType.value = "editManagementPlanTask";
  task.value = row;
  taskDrawerVisible.value = true;
}

// 处理任务变更
function handleChangeTask(row) {
  if (taskDrawerType.value === "editManagementPlanTask") {
    newPlanDrawerRef.value && newPlanDrawerRef.value.changeTask(row);
  } else if (taskDrawerType.value === "editTask" || taskDrawerType.value === "addBatchTask") {
    emits("change");
  }
}

// 处理计划变更
function handlePlanChange() {
  emits("change");
}

// 对外暴露的方法
defineExpose({
  showEditModal,
});
</script>
