import dayjs from 'dayjs';

export default {
  bloodPressure(forms, required) {
    return {
      trigger: 'blur',
      required,
      validator(rule, value, callback) {
        const data = forms && forms.value ? forms.value : {};
        if (required && !data['bloodPressureDate']) callback(new Error('请选择测量日期'));
        if (required && !data['systolicPressure']) callback(new Error('请输入收缩压'));
        if (required && !data['diastolicPressure']) callback(new Error('请输入舒张压'));;
        if (data['bloodPressureDate'] && (!dayjs(data['bloodPressureDate']).isValid() || dayjs(data['bloodPressureDate'])).isAfter(dayjs())) callback('请选择正确的测量日期');
        if (data['systolicPressure']) {
          const res = checkInteger(data['systolicPressure'], 40, 300)
          if (!res) callback(new Error('收缩压请输入40-300之间的数字'));
        }
        if (data['diastolicPressure']) {
          const res = checkInteger(data['diastolicPressure'], 40, 300)
          if (!res) callback(new Error('舒张压请输入40-300之间的数字'));
        };
        return callback()
      }
    }
  },
  BMI() { return {} }
}

function checkInteger(value, min, max) {
  if ((typeof value === 'string' && value.trim() === '') || (value !== 0 && !value)) return true;
  const number = Number(value);
  if (parseInt(value) !== number) return false
  if (number < min || number > max) return false
  return true
}