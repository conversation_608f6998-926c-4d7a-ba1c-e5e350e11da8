<!-- -->
<template>
  <div v-bind="bgWhite ? { 'bg-fff': 'bg-fff' } : {}" class="pagination">
    <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="props.currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="props.pageSize" layout="total, prev, pager, next, sizes, jumper" :total="props.totalRow"></el-pagination>
  </div>
</template>
<script setup>
let props = defineProps({
  bgWhite: { type: Boolean, default: true },
  currentPage: { type: Number, default: 1 },
  totalRow: { type: Number, default: 0 },
  pageSize: { type: Number, default: 0 },
});
let $emit = defineEmits(["changePage", "handleSizeChange", "handleCurrentChange"]);
function handleSizeChange(e) {
  // props.pageSize = e;
  $emit("handleSizeChange", e);
}
function handleCurrentChange(e) {
  $emit("handleCurrentChange", e);
}
</script>
<style lang="scss" scoped>
.pagination {
  flex-grow: 0;
  // height: 120px;
  display: flex;
  justify-content: end;
  align-items: center;
  padding: 0 15px;
}

:deep(.el-pagination) {
  height: 60px;
}
</style>
