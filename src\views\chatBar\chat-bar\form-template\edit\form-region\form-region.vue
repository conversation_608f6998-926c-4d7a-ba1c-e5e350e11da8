<template>
  <region-data-picker :popup-title="name" :step-searh="true" @change="onchange">
    <div class="px-15px py-6px" border-bottom>
      <el-form-item :class="required ? 'is-required' : ''" :label="name">
        <div class="w-full" flex items-center>
          <div class="w-0 flex-grow truncate">{{ valueStr }}</div>
          <div flex-shrink-0 class="text-14px ml-10px" style="color: #a8abb2;">
            <el-icon>
              <ArrowDown />
            </el-icon>
          </div>
        </div>
      </el-form-item>
    </div>
  </region-data-picker>
</template>

<script setup>
import { computed } from 'vue';
import regionDataPicker from './region-data-picker.vue';

const emits = defineEmits(['change'])

const props = defineProps({
  form: { type: Object, default: () => ({}) },
  name: { default: '' },
  required: { type: <PERSON>ole<PERSON>, default: false },
  title: { default: '' }
})
const value = computed(() => Array.isArray(props.form[props.title]) ? props.form[props.title] : []);
const valueStr = computed(() => value.value.join('-'))

function onchange({ selected = [] }) {
  emits('change', { title: props.title, value: selected.map(i => i.name) })
}

</script>

<style lang="scss" scoped></style>
