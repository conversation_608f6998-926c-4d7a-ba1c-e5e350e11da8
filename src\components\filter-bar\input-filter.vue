<template>
  <custom-filter :label="label" :width="width">
    <el-input v-model="modelValue" :placeholder="placeholder" />
  </custom-filter>
</template>
<script setup>
import { ref } from 'vue';
import { useVModels } from '@vueuse/core';

import customFilter from './custom-filter-item.vue';

const props = defineProps({
  modelValue: { type: Array, default: '' },
  label: { type: String, default: '' },
  placeholder: { type: String, default: '' },
  width: { type: [Number, String], default: 280 },
})
const emits = defineEmits(['update:modelValue']);
const { modelValue } = useVModels(props, emits);

</script>

