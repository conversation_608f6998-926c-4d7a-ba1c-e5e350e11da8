<template>
  <div v-if="loading" v-loading="loading" class="h-screen"></div>
  <div v-else ref="containerRef" class="h-screen break-all" color-normal>
    <template v-if="customer && customerList.length">
      <div ref="headRef">
        <customer-head :customer="customer" :team="customerTeam" :teams="customerTeams" @toggle="show()"
          @toggleTeam="showTeam()" @add="send('selectTeam')" @send="send('link')"/>
        <time-bar class="mb-5px shadow-lg" :customer="customer" :teams="customerTeams" />
      </div>
      <el-lock-scrollbar :lock="lock" :height="scrollHeight" @scroll="handleScroll">
        <!-- <div class="h-40px bg-red-200" @click="send('selectTeam')">新增客户</div> -->
        <character-info :serviceTimes="serviceTimes" class="mb-5px bg-white shadow-xl" :customer="customer"
          @change="updateCustomer" @showService="showService" :team="customerTeam" />
        <action-bar :customer="customer" :unionId="unionId" :team="customerTeam" @send="sendMessage($event)" />
        <customer-tabs :head-height="actualHeadHeight" :customer="customer" :team="customerTeam" />
      </el-lock-scrollbar>
      <!-- 苹果手机底部安全区域 -->
      <div ref="safeBottomAreaRef" class="safe-bottom bg-white"></div>
    </template>
    <no-customer v-else @create="send($event)" />
    <select-team-modal :team="customerTeam" :teams="customerTeams" :visible="teamVisible" :width="width"
      @close="closeTeam" @select="selectTeam($event)" />
    <service-modal :serviceTimes="serviceTimes" :visible="serviceVisible" :width="width" @close="closeService" />
    <auth-modal :auth-type="authType" :externalUserId="externalUserId" :teams="teams" :visible="authVisible"
      :width="width" @close="closeAuthModal" @send="sendMessage($event)"
      @select="toCreateCustomer($event)"></auth-modal>
    <create-customer :externalUserId="externalUserId" :team="newCustomerTeam" :visible="newCustomerVisible"
      @close="newCustomerVisible = false" @created="refreshCustomerList" :unionId="unionId" />
    <select-modal :customer="customer" :customers="customerList" :visible="visible" :width="width" @close="close"
      @select="selectCustomer($event)" @send="send('link')" @add="send('selectTeam')" />
  </div>
</template>
<script setup>
import { computed, onMounted, provide, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { useElementBounding, useWindowSize } from '@vueuse/core';
import useDropFiles from '@/hooks/useDropFiles';
import useModal from '@/hooks/useModal';
import { teamStore as useTeamStore } from '@/store/team';
import { sendChatMessage, sendChatMessagePromise } from "@/utils/jssdk.js";
import useCustomer from './useCustomer';

import elLockScrollbar from '@/components/el-lock-scrollbar/el-lock-scrollbar';
import actionBar from './action-bar/action-bar';
import authModal from './auth-modal';
import characterInfo from './character-info/character-info';
import CreateCustomer from './create-customer/create-customer';
import customerHead from './customer-head';
import customerTabs from './customer-tabs/index'
import noCustomer from './no-customer';
import selectModal from './select-modal';
import selectTeamModal from './select-team-modal';
import serviceModal from './service-modal';
import timeBar from './time-bar';

const trigger = getEventTrigger()
provide('side-bar-event-trigger', trigger);

const teamStore = useTeamStore();
const { getTeams } = teamStore;
const { teams } = storeToRefs(teamStore);

const { customer, customerList, customerTeam, customerTeams, externalUserId, loading, refresh, refreshCustomerList, selectCustomer, selectTeam, sub, updateCustomer, unionId } = useCustomer(teams, trigger);
const { close, show, visible, width } = useModal(); //  选择客户弹窗
const { close: closeService, show: showService, visible: serviceVisible } = useModal();// 展示责任人弹窗
const { close: closeTeam, show: showTeam, visible: teamVisible } = useModal(); // 选择客户所属团队弹窗
const { close: closeAuthModal, show: showAuthModal, visible: authVisible } = useModal(); // 发送授权链接弹窗

sub(close, closeService, closeTeam, closeAuthModal);

provide('refresh-customer', refresh)

const authType = ref('')
async function send(type) {
  if (teams.value === 0) await getTeams();
  authType.value = type;
  showAuthModal()
}

function sendMessage({ desc, title, url, imgUrl }) {
  sendChatMessage({
    desc,
    title,
    url,
    type: "news",
    imgUrl: imgUrl || "https://796f-youcan-clouddev-1-8ewcqf31dbb2b5-1317294507.tcb.qcloud.la/other/%E6%A1%A3%E6%A1%88.png?sign=ad25044f43b599712bb290e486bd5038&t=1701328162",
  })
}

function getEventTrigger() {
  const trigger = {};
  const $on = (key, fn) => {
    if (typeof fn === 'function' && key && !trigger[key]) trigger[key] = fn;
  }
  const $off = key => trigger[key] = null;
  const $invoke = (key, ...rest) => {
    typeof trigger[key] === 'function' && trigger[key](...rest)
  }

  return { $off, $on, $invoke }
}

const headRef = ref();
const safeBottomAreaRef = ref()
const { height: winHeight } = useWindowSize();
const { height: headHeight } = useElementBounding(headRef);
const { height: safeHeight } = useElementBounding(safeBottomAreaRef);
const actualHeadHeight = computed(() => headHeight.value + 5) // 5像素 margin-bottom值
const scrollHeight = computed(() => winHeight.value - actualHeadHeight.value - safeHeight.value);

const newCustomerTeam = ref({});
const newCustomerVisible = ref(false);
function toCreateCustomer(team) {
  newCustomerTeam.value = team;
  newCustomerVisible.value = true
}

// 侧边栏文件拖入事件
let dropCallbaks = [];
const containerRef = ref();
useDropFiles(containerRef, onDrop);
function onDrop(files) {
  const cb = dropCallbaks[dropCallbaks.length - 1];
  if (typeof cb === 'function') cb(files);
}
trigger.$on('sub-sidebar-dropFiles', cb => {
  if (typeof cb === 'function') dropCallbaks.push(cb)
})
trigger.$on('unsub-sidebar-dropFiles', cb => {
  dropCallbaks = dropCallbaks.filter(i => i !== cb)
})


// 侧边栏滚动条 锁滚动
const lock = ref(false);
const mainScrollbarAtBottom = ref(false);
provide('main-scrollbar-at-bottom', mainScrollbarAtBottom);
function handleScroll(e) {
  mainScrollbarAtBottom.value = e.atBottom;
}
trigger.$on('lock-sidebar-scroll', () => lock.value = true);
trigger.$on('unlock-sidebar-scroll', () => lock.value = false);


// 处理 工作台应用跳转侧边栏的自定义事件  
trigger.$on('handle-work-bench-event', handleWorkBenchEvent);

async function handleWorkBenchEvent(payload) {
  const { name, time } = payload;
  // 超过10秒不触发
  const duration = new Date().getTime() - time;
  if (typeof time !== 'number' || !time > 0 || duration >= 1000 * 10 || duration < 0) return;
  // 【工作台 待办详情】点击“去处理”跳转到侧边栏,侧边栏底部tab切换到待跟进tab
  if (name === 'todo') trigger.$invoke('change-currentTab-2-followup');
  // 群发任务 给发送失败的客户再次发送 
  else if (name === 'sendGroupMsg' && Array.isArray(payload.msgList) && payload.msgList.length) {
    for (let item of payload.msgList) {
      try {
        await sendChatMessagePromise(item)
      } catch (e) {
        console.log('发送失败了： ', e)
      }
    }
  }

}
</script>
<style lang="scss" scoped>
// 禁止手机端ios的橡皮筋回弹效果
:global(html, body) {
  overflow: hidden;
  overscroll-behavior: none;
}

:global(.el-dialog__header) {
  padding-top: 10px;
}

:global(.el-dialog__headerbtn) {
  top: -4px;
}

:deep(.h-screen) {
  // height: calc(100vh - 40px);
  height: calc(100vh - env(safe-area-inset-bottom, 0px));
}

.safe-bottom {
  height: env(safe-area-inset-bottom, 0);
}
</style>
