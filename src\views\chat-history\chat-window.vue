<template>
  <div color-normal class="flex flex-col h-full text-14px bg-white rounded-t-r rounded-b-r">
    <div class="flex-shrink-0 flex justify-between px-15px py-12px">
      <div class="flex w-full">
        <div class="w-0 flex-grow truncate font-semibold text-15px flex" title="仅展示最多90天的聊天记录">
          “
          <ww-user :openid="selectedCustomer.memberUserId" v-if="selectedCustomer.memberUserId"></ww-user>
          ” 与客户 “{{ customerName }}” 的聊天记录
          <span class="font-normal text-12px text-gray-500">（仅展示最多90天的聊天记录）</span>
        </div>
        <div class="flex-shrink-0 flex items-center text-blue-500 cursor-pointer" @click="refreshRecord">
          <el-icon>
            <Refresh />
          </el-icon>
          <span>刷新</span>
        </div>
      </div>
    </div>
    <div ref="containRef" class="flex-shrink-0 px-15px pb-12px flex items-center justify-between border-b border-gray-200">
      <div ref="listRef" class="flex-shrink truncate">
        关联档案：
        <span v-for="(i, index) in customerList" :class="index > 0 ? 'ml-5px' : ''" class="inline-block px-10px py-5px border border-blue-500 rounded text-12px text-blue-500 cursor-pointer" @click="toCustomerDetail(i)">{{ i.name }}</span>
      </div>
      <div ref="arrowRef" class="flex-shrink-0 px-5px cursor-pointer" :class="showPopover ? '' : 'opacity-0'">
        <el-popover :disabled="!showPopover" placement="bottom-end" :width="width" trigger="click">
          <template #reference>
            <el-icon class="translate-y-1px">
              <ArrowDownBold />
            </el-icon>
          </template>
          <el-scrollbar wrap-style="max-height:40vh">
            <div color-normal class="text-14px font-semibold">关联档案：</div>
            <span v-for="i in customerList" class="inline-block px-10px mr-10px py-5px border mt-10px border-blue-500 rounded text-12px text-blue-500 cursor-pointer" @click="toCustomerDetail(i)">{{ i.name }}</span>
          </el-scrollbar>
        </el-popover>
      </div>
    </div>
    <div class="flex-grow relative bg-[#fefefe]">
      <div class="absolute inset-0 bg-[#fefefe]" v-loading="recordLoading">
        <div v-if="!isDev" ref="chatRecordRef" class="w-full h-full"></div>
        <DynamicScroller v-else class="h-full yc-scrollbar" :items="chatCustomerRecord" :min-item-size="54" key-field="_id">
          <template v-slot="{ item, index, active }">
            <DynamicScrollerItem :item="item" :min-item-size="100" :active="active" :data-index="index">
              <div v-if="item === 'isEmpty'" class="h-100px"></div>
              <div v-else-if="item.senderType === 2" class="dy-item max-w-4/5 py-10px px-15px">
                <div class="flex items-center">
                  <div class="flex-shrink text-14px font-semibold truncate">
                    {{ selectedCustomer.customerUserId }}
                  </div>
                  <div class="flex-shrink-0 mx-5px text-12px text-[#66CB70]">@微信</div>
                  <div class="flex-shrink-0 pl-5px text-12px">{{ item.sendTime }}</div>
                </div>
                <div class="bg-white mt-10px p-10px rounded-8px shadow-lg inline-block break-all leading-24px text-14px max-w-full">
                  {{ item.msgid }}
                </div>
              </div>
              <div v-else-if="item.senderType === 1" class="dy-item ml-auto max-w-4/5 text-right py-10px px-15px">
                <div class="flex items-center justify-end">
                  <div class="flex-shrink-0 pl-5px text-12px">{{ item.sendTime }}</div>
                  <div class="flex-shrink text-14px font-semibold truncate mx-5px">
                    <ww-user v-if="item.memberUserId" :openid="item.memberUserId"></ww-user>
                  </div>
                  <div class="flex-shrink-0 ml-5px text-12px text-orange-500">@员工</div>
                </div>
                <div class="bg-white mt-10px p-10px rounded-8px shadow-lg inline-block break-all leading-24px text-14px max-w-full">
                  {{ item.msgid }}
                </div>
              </div>
            </DynamicScrollerItem>
          </template>
        </DynamicScroller>
      </div>
    </div>
    <customer-detail v-if="customerId" :visible="customerDetailVisible" @close="customerDetailVisible = false" :customerId="customerId" title="客户详情" customerType="corpCustomer" />
  </div>
</template>
<script setup>
import { computed, ref, onMounted, nextTick } from "vue";
import { storeToRefs } from "pinia";
import * as ww from "@/libs/@wecom/jssdk/wecom.esm.js";
import { useElementBounding } from "@vueuse/core";
import { selectedCustomer } from "./api/chatCustomer.js";
import { chatCustomerRecord, refreshRecord, recordLoading, customerList, dataFrame, handleScroll } from "./api/chatRecord.js";
import { DynamicScroller, DynamicScrollerItem } from "vue-virtual-scroller";
import { useRouter } from "vue-router";
import { template, style } from "./template/chatRecord.js";
import "vue-virtual-scroller/dist/vue-virtual-scroller.css";
import WwUser from "@/components/ww-user/index.vue";
import customerDetail from "@/views/member/detail/index.vue";
import wxContact from "@/store/wxContact";

const isDev = import.meta.env.DEV;
const chatRecordRef = ref(null);
const router = useRouter();
const customerId = ref("");
const customerDetailVisible = ref(false);
const { pending, contactName } = storeToRefs(wxContact());

async function toCustomerDetail(customer) {
  customerId.value = customer._id;
  await nextTick();
  customerDetailVisible.value = true;
}
const containRef = ref(null);
const listRef = ref(null);
const arrowRef = ref(null);
const { width } = useElementBounding(containRef);
const { width: listWidth } = useElementBounding(listRef);
const { width: arrowWidth } = useElementBounding(arrowRef);
const showPopover = computed(() => width.value - listWidth.value - arrowWidth.value < 40);

const customerName = computed(() => {
  return selectedCustomer.value.customerUserId && contactName.value[selectedCustomer.value.customerUserId] ? contactName.value[selectedCustomer.value.customerUserId] : "";
});

onMounted(async () => {
  await nextTick();
  const factory = ww.createOpenDataFrameFactory();
  dataFrame.value = factory.createOpenDataFrame({
    el: chatRecordRef.value, // “容器”元素，用于挂载展示组件
    template,
    style,
    data: {
      migList: chatCustomerRecord.value,
    },
    methods: {
      bindscrolltolower() {
        handleScroll();
      },
    },
  });
});
</script>
<style>
iframe {
  width: 100%;
  height: 100%;
}
</style>
