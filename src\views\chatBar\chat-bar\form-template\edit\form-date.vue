<template>
  <el-form-item class="px-15px py-6px el-form-item--autoheight" :class="required ? 'is-required' : ''" :label="name"
    border-bottom>
    <el-date-picker style="width:100%" :model-value="value" :format="format" :placeholder="'请选择' + name" type="date"
      :value-format="format" @update:model-value="change($event)" />
  </el-form-item>
</template>
<script setup>
import { computed } from 'vue';

const emits = defineEmits(['change'])
const props = defineProps({
  form: { type: Object, default: () => ({}) },
  format: { type: Object, default: 'YYYY-MM-DD' },
  name: { type: String, default: '' },
  required: { type: Boolean, default: false },
  title: { type: String, default: '' },
})
const value = computed(() => props.form[props.title] || '');

function change(value) { emits('change', { title: props.title, value }) }

</script>
<style lang="scss" scoped></style>
