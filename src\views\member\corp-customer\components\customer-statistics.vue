<template>
  <div font-14 class="flex w-full text-center">
    <div bg-fff common-shadow px-15 rounded-4 class="mr-10px flex py-10px" style="width: 63%;">
      <div border-right flex flex-col class="px-20px items-center">
        <div>
          <span color-primary class="font-semibold text-16px">{{ corpMemberCount.customerTotalCount }}</span>
          <span> / </span>
          <span class="font-semibold">{{ corpPackage.customerCount + corpPackage.giveCustomerCount }}</span>
        </div>
        <div>已有档案数/上限数</div>
      </div>
      <div class="px-20px justify-center w-40%" flex>
        <div class="mr-20px">
          <div color-primary class="font-semibold text-16px">{{ corpMemberCount.friendCount }}</div>
          <div>已加好友</div>
        </div>
        <div>
          <div color-primary class="font-semibold text-16px">{{ corpMemberCount.noFriendCount }}</div>
          <div>未加好友</div>
        </div>
      </div>
    </div>
    <div bg-fff common-shadow px-15 rounded-4 class="flex py-10px" style="width: 37%;">
      <div border-right flex flex-col class="px-20px items-center">
        <div color-primary class="font-semibold text-16px">
          {{ corpMemberCount.todayAddCustomerCount }}
        </div>
        <div>今日新增</div>
      </div>
      <div class="flex px-20px justify-center">
        <div class="mr-20px">
          <div color-primary class="font-semibold text-16px">{{ corpMemberCount.todayAddFriendCount }}</div>
          <div>已加好友</div>
        </div>
        <div>
          <div color-primary class="font-semibold text-16px">{{ corpMemberCount.todayNoFriendCount }}</div>
          <div>未加好友</div>
        </div>
      </div>
    </div>
    <div
      class="ml-10px flex-shrink-0 flex items-center justify-center px-20px rounded-8px text-white text-14px leading-20px cursor-pointer bg-primary"
      @click="uploadCustomer">
      批量导入<br />客户数据
    </div>
  </div>
</template>
<script setup>
import { ref } from "vue";
import { getCustomerStatistics as getCustomerStatisticsApi } from "@/api/member";
import { memberStore } from "@/store/member";
const { corpInfo } = memberStore();
const corpPackage = ref(corpInfo.package);

const emits = defineEmits(['uploadCustomer', 'updateTotal']);
getCustomerStatistics();
const corpMemberCount = ref({});
async function getCustomerStatistics() {
  const { data } = await getCustomerStatisticsApi();
  corpMemberCount.value = data.data || {};
  emits('updateTotal', data.data.customerTotalCount)
}

function uploadCustomer() {
  emits('uploadCustomer')
}

defineExpose({
  reload: getCustomerStatistics
})

</script>
<style lang="scss" scoped>
.show-split-line::before {
  content: "";
  display: inline-block;
  margin: 2px 0;
  height: 1em;
  width: 1px;
  background: rgba(0, 0, 0, 0.9);
  margin: 0 10px;
}

.statis-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 50%;
  margin-left: 50px;
}

.bg-primary {
  background-color: var(--el-color-primary);

}
</style>