import { computed, onMounted, ref } from "vue";
import BigNumber from "bignumber.js";

export default function usePayment(colNum) {
  const list = ref([]);
  const calList = computed(() => groupList(list.value));
  function groupList(data) {
    const list = Array.isArray(data) ? data.map(i => ({ ...i })) : [];
    const m = new Map();
    const feeMap = new Map();
    for (let item of list) {
      if (!item.doctorAdvice) continue;
      // 根据类型分组
      if (!m.has(item.doctorAdvice)) {
        m.set(item.doctorAdvice, [item]);
      } else {
        m.get(item.doctorAdvice).push(item);
      }
      if (item.payment >= 0) {
        const fee = feeMap.get(item.doctorAdvice) || new BigNumber(0);
        const itemFee = new BigNumber(item.payment);
        item.itemFee = itemFee.toNumber();
        feeMap.set(item.doctorAdvice, fee.plus(itemFee));
      }
    }
    const keys = Array.from(m.keys()).sort((a, b) => (a === "处置治疗" ? -1 : 1)); // 按照处置治疗在前，其他在后的顺序排序
    const res = [];
    for (let key of keys) {
      res.push(...m.get(key), { projectType: key, rowType: "countRow", typeFee: feeMap.get(key) ? feeMap.get(key).toNumber() : 0 });
    }
    return res;
  }

  function mergeMethod({ row: { rowType }, columnIndex }) {
    if (rowType !== "countRow") return [1, 1];
    if (columnIndex === colNum - 1) return [1, 1];
    if (columnIndex === 0) return [1, colNum - 1];
    return [1, 0];
  }

  return { list, groupTableList: calList, mergeMethod };
}
