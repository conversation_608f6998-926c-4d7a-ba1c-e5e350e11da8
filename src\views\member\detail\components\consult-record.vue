<template>
  <my-layout bg-fff common-shadow>
    <layout-item>
      <div class="top-module">
        <div bg-fff class="query mt-15px aligin-center">
          <radio-filter v-model="tradeStatus" label="成交状态" :list="TradeStatus"></radio-filter>
          <date-range-filter v-model:dates="triageTimeDates" label="咨询日期" :text="getDatesText" />
          <div class="text-gray-500 text-14px flex items-center ml-10px">
            <div class="filter-label pr-5px">意向项目:</div>
            <project-intent-select v-model="projectIds" @change="handleProjectChange" placeholder="请选择意向项目" type="filter" style="width: 200px" multiple />
          </div>
          <filter-info-source v-model="infoSource" />
          <el-form-item class="query__item query__item--auto">
            <el-button type="primary" @click="handleChange">查询</el-button>
            <el-button type="primary" @click="reset">重置</el-button>
          </el-form-item>
        </div>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <div h-full px-15>
        <el-table stripe v-loading="loading" border height="100%" :data="list">
          <el-table-column property="triageTimeStr" label="咨询时间" width="150" />
          <el-table-column property="tradeStatus" label="成交状态" width="120">
            <template #default="{ row: { tradeAmount, dealStatus } }">
              <div v-if="dealStatus === '成交'" :style="{ color: dealStatus === '成交' ? 'green' : 'red' }">{{ dealStatus === "成交" ? "已成交" : "未成交" }}</div>
              <div v-else :style="{ color: tradeAmount > 0 ? 'green' : 'red' }">{{ tradeAmount > 0 ? "已成交" : "未成交" }}</div>
            </template>
          </el-table-column>
          <el-table-column property="source" label="信息来源" width="150">
            <template #default="{ row: { sourceNames } }">
              <el-tooltip :disabled="!sourceNames" placement="top" effect="light" :content="sourceNames" popper-class="max-w-480px">
                <span>{{ sourceNames }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="project" label="意向项目" :min-width="240">
            <template #default="{ row: { projectNames } }">
              <el-tooltip :disabled="!projectNames" placement="top" effect="light" :content="projectNames" popper-class="max-w-480px">
                <span>{{ projectNames }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="receptionPersonUserId" label="接诊人员" :min-width="120">
            <template #default="{ row: { receptionPersonUserId } }">
              <ww-user :openid="receptionPersonUserId" />
            </template>
          </el-table-column>
          <el-table-column property="customerComplaint" label="客户主诉" :min-width="200">
            <template #default="{ row: { customerComplaint } }">
              <el-popover placement="top" width="400" trigger="click" v-if="customerComplaint">
                <template #reference>
                  <div class="cursor-pointer whitespace-pre-wrap leading-5 max-h-10 overflow-hidden line-clamp-2 hover:text-blue-500 transition-colors" @click.stop>
                    {{ customerComplaint }}
                  </div>
                </template>
                <div class="whitespace-pre-wrap text-14px leading-6 max-h-300px overflow-y-auto">
                  {{ customerComplaint }}
                </div>
              </el-popover>
              <div v-else class="text-gray-400">暂无客户主诉</div>
            </template>
          </el-table-column>
          <el-table-column property="dealPlan" label="处理方案" :min-width="200">
            <template #default="{ row: { dealPlan } }">
              <el-popover placement="top" width="400" trigger="click" v-if="dealPlan">
                <template #reference>
                  <div class="cursor-pointer whitespace-pre-wrap leading-5 max-h-10 overflow-hidden line-clamp-2 hover:text-blue-500 transition-colors" @click.stop>
                    {{ dealPlan }}
                  </div>
                </template>
                <div class="whitespace-pre-wrap text-14px leading-6 max-h-300px overflow-y-auto">
                  {{ dealPlan }}
                </div>
              </el-popover>
              <div v-else class="text-gray-400">暂无处理方案</div>
            </template>
          </el-table-column>
          <el-table-column property="noDealReason" label="未成交原因" :min-width="200">
            <template #default="{ row: { noDealReason } }">
              <el-popover placement="top" width="400" trigger="click" v-if="noDealReason">
                <template #reference>
                  <div class="cursor-pointer whitespace-pre-wrap leading-5 max-h-10 overflow-hidden line-clamp-2 hover:text-blue-500 transition-colors" @click.stop>
                    {{ noDealReason }}
                  </div>
                </template>
                <div class="whitespace-pre-wrap text-14px leading-6 max-h-300px overflow-y-auto">
                  {{ noDealReason }}
                </div>
              </el-popover>
              <div v-else class="text-gray-400">暂无未成交原因</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </layout-main>
    <layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="currentPage" @handle-size-change="onSizeChange" @handle-current-change="onCurrentChange" />
      ·
    </layout-item>
  </my-layout>
</template>
<script setup>
import dayjs from "dayjs";
import { computed, ref } from "vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import WwUser from "@/components/ww-user/index.vue";
import useModal from "@/hooks/useModal";
import { ElMessage, ElMessageBox } from "element-plus";
import { baseFilterItem, DateRangeFilter, RadioFilter, filterInfoSource } from "@/components/filter-bar";
import projectIntentSelect from "@/components/benefit-management/project-intent-select.vue";
import { VisitType, ConsultStage, TradeStatus } from "@/baseData";
import { getConsultRecord } from "@/api/consult";
const { memberId, customer, memberName, customerType } = defineProps(["memberId", "customer", "memberName", "customerType"]);
const total = ref(0);
const pageSize = ref(10);
const currentPage = ref(1);
const loading = ref(false);
const infoSource = ref([]);
const projectIds = ref([]);
const page = ref(1);
const list = ref([]);
const tradeStatus = ref("");
const VisitStatus = [
  { label: "待接诊", value: "pending" },
  { label: "已接诊", value: "visited" },
];

const triageTimeDates = ref([]);

const getDatesText = computed(() => {
  return triageTimeDates.value && Array.isArray(triageTimeDates.value) ? triageTimeDates.value.join(" ~ ") : "";
});

// 处理项目变更
function handleProjectChange(projects) {
  // 可以根据需要处理项目变更
}

getList();
function onSizeChange(e) {
  pageSize.value = e;
  getList();
}
function onCurrentChange(e) {
  currentPage.value = e;
  getList();
}
// 获取服务记录
async function getList() {
  let query = {
    page: page.value,
    pageSize: pageSize.value,
    customerId: memberId,
    visitStatus: ["pending", "visited"],
  };
  if (projectIds.value && projectIds.value.length > 0) {
    query.projectIds = projectIds.value;
  }
  if (triageTimeDates.value && Array.isArray(triageTimeDates.value) && triageTimeDates.value.length) {
    query.triageTimeDates = triageTimeDates.value;
  }
  if (tradeStatus.value) {
    query.tradeStatus = tradeStatus.value;
  }
  if (infoSource.value.length) {
    query.source = [...infoSource.value];
  }
  loading.value = true;
  const { success, data } = await getConsultRecord(query);
  loading.value = false;
  if (success) {
    total.value = data.total;
    list.value = data.list.map((i) => {
      return {
        ...i,
        name: i.customerInfo.name,
        mobile: i.customerInfo.mobile,
        sourceNames: Array.isArray(i.source) ? i.source.join(" ") : "",
        projectNames: Array.isArray(i.projectNames) ? i.projectNames.join(" / ") : "",
        sexAge: i.customerInfo.sex && i.customerInfo.age ? `${i.customerInfo.sex} / ${i.customerInfo.age}` : `${i.customerInfo.sex || ""}${i.customerInfo.age || ""}`,
        triageTimeStr: i.triageTime ? dayjs(i.triageTime).format("YYYY-MM-DD HH:mm") : "",
        customerSourceStr: i.customerInfo.customerSource && Array.isArray(i.customerInfo.customerSource) ? i.customerInfo.customerSource.join(" / ") : "",
        visitStatusStr: VisitStatus.find((j) => j.value === i.visitStatus)?.label || "",
        visitTypeStr: VisitType.find((j) => j.value === i.visitType)?.label || "",
        consultStageStr: ConsultStage.find((j) => j.value === i.consultStage)?.label || "",
        receptionTimeStr: i.receptionTime ? dayjs(i.receptionTime).format("YYYY-MM-DD HH:mm") : "",
      };
    });
  }
}
async function handleChange() {
  getList();
}
async function reset() {
  tradeStatus.value = "";
  triageTimeDates.value = [];
  projectIds.value = [];
  infoSource.value = [];
  getList();
}
</script>
<style scoped lang="scss">
:deep(.el-table__inner-wrapper) {
  height: 100% !important;
}

.query {
  display: flex;
  flex-wrap: wrap;
  padding: 0 15px;
  align-items: center;
  margin-bottom: 10px;
  .query__item {
    width: 180px;
    margin-bottom: 15px;
    margin-right: 10px;

    @at-root &--large {
      width: 240px;
    }

    @at-root &--small {
      width: 140px;
    }

    @at-root &--small {
      width: 140px;
    }

    @at-root &--auto {
      width: auto;
    }

    @at-root &:last-of-type {
      margin-left: auto;
      margin-right: 0;
    }
  }
}
</style>