<template>
  <el-dialog :model-value="visible" :width="600" :show-close="teamList.length == 0" @close="close">
    <template #header="{ titleClass }">
      <div flex items-center justify-between>
        <span class="pb-5px" :class="titleClass">选择员工所属团队</span>
        <el-text v-if="teamList.length > 0" type="primary" pointer @click="nameVisible = true"><el-icon>
            <Plus />
          </el-icon> 新建团队</el-text>
      </div>
    </template>
    <el-input v-if="teamList.length" v-model="name" placeholder="请输入团队名称" class="mx-10px mb-15px"></el-input>
    <el-scrollbar wrap-style="max-height: calc(100vh - 360px)">
      <div v-for="item in teams" flex items-center px-15 pb-10 border-bottom>
        <div flex-grow>
          <div pt-10 flex items-center>
            <div mr-10>
              <img src="@/assets/check-active.png" class="w-18px h-18px pointer" v-if="isBelongTeamAction(item.teamId)"
                @click="removeTeam(item)" />
              <img @click="addTeamAction(item)" src="@/assets/check.png" class="w-18px h-18px pointer" v-else />
            </div>
            <div font-14 font-semibold>{{ item.name }}</div>
          </div>
          <div class="flex mt-10px" items-center>
            <div class="flex mr-20px" items-center>
              <div class="mr-10px" font-12>
                <div font-12>团队负责人</div>
              </div>
              <el-radio-group :model-value="showLeaderRadio(item)" @update:model-value="change($event, item.teamId)">

                <el-radio :label="false"><span font-12>否</span></el-radio>
                <el-radio :label="true"><span font-12>是</span></el-radio>
              </el-radio-group>
            </div>
          </div>
        </div>
      </div>
      <div v-if="teamList.length === 0" text-center class="pt-40px pb-30px">
        <div class="pb-15px" font-16>暂无团队信息，请先创建团队</div>
        <el-button type="primary" @click="nameVisible = true">创建团队</el-button>
      </div>
    </el-scrollbar>
    <template v-if="teamList.length > 0" #footer>
      <div text-center>
        <el-button @click="close()">取消</el-button>
        <el-button :loading="saveLoading" type="primary" @click="saveStaffTeam()"> 保存 </el-button>
      </div>
    </template>
  </el-dialog>
  <el-dialog v-model="nameVisible" title="创建团队" :width="480">
    <div p-15>
      <el-input v-model="teamName" placeholder="请输入团队名称" />
    </div>
    <template #footer>
      <div text-center>
        <el-button @click="nameVisible = false">取消</el-button>
        <el-button :loading="saveLoading" type="primary" @click="saveTeam()"> 保存 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref, watch } from 'vue';
import { getCorpTeams } from "@/api/corp.js"
import { getRandomStr } from "@/utils";
import { updateTeam } from "@/api/corp";
import { ElMessage } from "element-plus";
let props = defineProps({
  visible: { type: Boolean, default: false },
  belongTeamList: { type: Array, default: [] },
  userId: { type: String, default: '' }
})
const teamList = ref([]);
const selectTeams = ref([]);
const nameVisible = ref(false);
const teamName = ref('');
const saveLoading = ref(false);
const name = ref('')
const teams = computed(() => {
  if (name.value.trim()) return teamList.value.filter(item => item.name.includes(name.value.trim()))
  return teamList.value
})
function showLeaderRadio(item) {
  let flag = false;
  if (!isBelongTeamAction(item.teamId)) {
    flag = '';
  } else if (isBelongTeamLeader(item.teamId) && isBelongTeamAction(item.teamId)) {
    flag = true;
  }
  return flag;
}
watch(props, (newItem) => {
  selectTeams.value = JSON.parse(JSON.stringify(newItem.belongTeamList));
}, { immediate: true })

const emits = defineEmits(['close', 'change', 'addTeam']);
getList();
async function getList() {
  const {
    data = {},
  } = await getCorpTeams(1,
    100,
    localStorage.getItem("corpId"),
  );
  const { list: teams = [] } = data;
  teamList.value = teams;

}
function isBelongTeamLeader(teamId) {
  let team = selectTeams.value.filter(item => item.teamId === teamId)[0];
  return team && team.isTeamLeader
}
function isBelongTeamAction(teamId) {
  let teams = selectTeams.value.find(item => item.teamId === teamId);
  return teams ? teams.isBelongTeam : false
}
function close() {
  emits('close')
}
function saveStaffTeam(item) {
  emits('addTeam', selectTeams.value)
}
function addTeamAction(item) {
  const index = selectTeams.value.findIndex(i => i.teamId === item.teamId);
  if (index >= 0) {
    let team = selectTeams.value[index];
    team.isBelongTeam = true;
    team.memberList.push(props.userId);
    selectTeams.value[index] = team;
    return
  }
  const { teamId, name, memberList, memberLeaderList } = item;
  memberList.push(props.userId)
  let e = {
    teamId,
    name,
    memberList,
    memberLeaderList,
    isTeamLeader: false,
    isBelongTeam: true
  }
  selectTeams.value.push(e);
}
function removeTeam(item) {
  const index = selectTeams.value.findIndex(i => i.teamId === item.teamId);
  const team = selectTeams.value[index];
  team.isTeamLeader = false;
  team.isBelongTeam = false;
  team.memberList = team.memberList.filter(i => i !== props.userId);
  team.memberLeaderList = team.memberLeaderList.filter(i => i !== props.userId);
  selectTeams.value[index] = team;
}
function saveTeam() {
  if (!teamName.value) {
    ElMessage.error('请输入团队名称');
    return
  }
  createTeam();
  teamName.value = '';
  nameVisible.value = false;
}

function change(value, teamId) {
  if (value) isAddTeamLeader(teamId);
  else isRemoveTeamLeader(teamId)
}
function isAddTeamLeader(teamId) {
  if (!isBelongTeamAction(teamId)) {
    return
  }
  const index = selectTeams.value.findIndex(item => item.teamId === teamId);
  const team = selectTeams.value[index];
  team.isTeamLeader = true;
  team.memberLeaderList.push(props.userId);
  selectTeams.value[index] = team;
}
function isRemoveTeamLeader(teamId) {
  if (!isBelongTeamAction(teamId)) {
    return
  }
  const index = selectTeams.value.findIndex(item => item.teamId === teamId);
  const team = selectTeams.value[index];
  team.isTeamLeader = false;
  team.memberLeaderList = team.memberLeaderList.filter(i => i !== props.userId);
  selectTeams.value[index] = team;
}
async function createTeam() {
  let teamId = getRandomStr();
  let memberLeaderList = [];
  const memberList = [...memberLeaderList];
  let params = {
    corpId: localStorage.getItem("corpId"),
    teamId,
    memberList,
    memberLeaderList,
    isBelongTeam: true,
    name: teamName.value
  }
  const { success, message } = await updateTeam(params);
  if (success) {
    ElMessage.success(message);
  } else {
    ElMessage.error(message);
  }
  getList();

}
</script>