<template>
  <div class="max-w-2xl mx-auto p-6 bg-white shadow-lg rounded-lg">
    <!-- 头部 -->
    <div class="flex justify-between items-center mb-6">
      <div class="text-gray-500">6:00PM</div>
      <div class="text-green-500 font-bold">100%</div>
    </div>

    <!-- 添加药品按钮 -->
    <el-button type="primary" class="w-full mb-6">添加药品</el-button>

    <!-- 药品表单 -->
    <div class="space-y-6">
      <!-- 剂型选择 -->
      <div>
        <label class="block text-sm font-medium mb-2">剂型</label>
        <el-select class="w-full" v-model="form.dosageForm" placeholder="请选择">
          <el-option label="精制饮片" value="精制饮片" />
        </el-select>
      </div>

      <!-- 代煎选择 -->
      <div>
        <label class="block text-sm font-medium mb-2">代煎</label>
        <el-radio-group v-model="form.decoction">
          <el-radio label="代煎" />
          <el-radio label="不代煎" />
        </el-radio-group>
      </div>

      <!-- 药品列表 -->
      <div class="border rounded p-4">
        <div class="text-gray-400 mb-2">药品</div>
        <div class="text-center text-gray-400 py-4">暂无药品</div>
        <el-button type="text" class="w-full">编辑药品&gt;</el-button>
      </div>

      <!-- 用法选择 -->
      <div>
        <label class="block text-sm font-medium mb-2">用法</label>
        <el-radio-group v-model="form.usage">
          <el-radio label="内服" />
          <el-radio label="外敷" />
          <el-radio label="灌肠" />
          <el-radio label="鼻饲" />
          <el-radio label="冲服" />
          <el-radio label="煎汤外洗" />
        </el-radio-group>
      </div>

      <!-- 用量设置 -->
      <div class="grid grid-cols-3 gap-4">
        <div>
          <label class="block text-sm font-medium mb-2">一共</label>
          <el-input v-model="form.totalDose" placeholder="剂" />
        </div>
        <div>
          <label class="block text-sm font-medium mb-2">每天</label>
          <el-input v-model="form.dailyTimes" placeholder="次" />
        </div>
        <div>
          <label class="block text-sm font-medium mb-2">一共</label>
          <el-input v-model="form.totalBags" placeholder="袋" />
        </div>
      </div>

      <!-- 医嘱 -->
      <div>
        <label class="block text-sm font-medium mb-2">医嘱</label>
        <el-input type="textarea" :rows="2" placeholder="添加服药禁忌等告知患者" v-model="form.instructions" />
      </div>

      <!-- 备注 -->
      <div>
        <label class="block text-sm font-medium mb-2">备注</label>
        <el-input type="textarea" :rows="2" placeholder="输入制作要求等，该内容患者不可见" v-model="form.remark" />
      </div>

      <!-- 引用处方 -->
      <div>
        <el-button type="text">引用处方</el-button>
      </div>

      <!-- 确定按钮 -->
      <el-button type="primary" class="w-full mt-6">确定</el-button>
    </div>
  </div>
</template>
  
  <script setup>
import { reactive } from "vue";

const form = reactive({
  dosageForm: "精制饮片",
  decoction: "代煎",
  usage: "内服",
  totalDose: "",
  dailyTimes: "",
  totalBags: "",
  instructions: "",
  remark: "",
});
</script>
  
  <style>
</style>