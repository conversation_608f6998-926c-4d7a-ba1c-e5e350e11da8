<template>
  <div bg-fff flex class="h-full">
    <div h-full flex-shrink-0 class="w-3/10 min-w-240px max-w-320px border-r border-gray-200">
      <classify-list custom v-bind="componentProps" :level="2" :checked-cate="current" :data="cateList" @change="changeCurrent($event)" @search="getCateList()" @onHandle="handleCate($event)"></classify-list>
    </div>
    <div h-full flex-grow>
      <my-layout>
        <layout-item>
          <div p-15 class="flex items-center justify-between">
            <div class="flex items-center">
              <el-input :prefix-icon="Search" placeholder="请输入来源名称或拼音码搜索" v-model="searchKey"></el-input>
              <el-button class="w-100px ml-10px" type="primary" @click="getList">搜索</el-button>
            </div>
            <el-button w-100px :icon="Plus" type="primary" @click="addSource">新增来源</el-button>
          </div>
        </layout-item>
        <layout-main :scroll="false">
          <el-table stripe height="100%" :data="list" v-loading="loading">
            <el-table-column prop="code" label="编号" :min-width="80">
              <template #default="scope">
                {{ (page - 1) * pageSize + scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" :min-width="80">
              <template #default="{ row }">
                <span :class="row.sourceStatus === 'enable' ? 'text-success' : 'text-danger'">
                  {{ row.sourceStatus === "enable" ? "启用" : "停用" }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="sourceName" label="来源名称" :min-width="120" />
            <el-table-column prop="pinyin" label="拼音码" :min-width="100" />
            <el-table-column label="所属分类" :min-width="100">
              <template #default="{ row }">
                {{ formatCategoryName(row.sourceCateIdGroup) }}
              </template>
            </el-table-column>
            <el-table-column fixed="right" class-name="el-table-fixed-column--shadow" label="操作" width="120">
              <template #default="{ row }">
                <el-text class="cursor-pointer mr-5px" type="primary" @click="editSource(row)">编辑</el-text>
                <el-text class="cursor-pointer mr-5px" type="primary" v-if="row.sourceStatus === 'disable'" @click="changeStatus(row)">启用</el-text>
                <el-text class="cursor-pointer mr-5px" type="danger" v-if="row.sourceStatus === 'enable'" @click="changeStatus(row)">停用</el-text>
              </template>
            </el-table-column>
          </el-table>
        </layout-main>
        <layout-item>
          <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize" @handle-current-change="changePage" />
        </layout-item>
      </my-layout>
    </div>
  </div>
  <source-modal :visible="visible" :width="width" @close="close" @success="getList" :source="currentSource" :cateList="cateList" />
  <cate-modal :data="currentCate" :visible="visibleCateModal" :width="widthCateModal" @change="onCateChange()" @close="closeCateModal" />
</template>

<script setup>
import { ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getSourceList, updateSourceStatus, getSourceCateList, addSourceCate, deleteSourceCate, updateSourceCate, sortSourceCate } from "@/api/benefitManagement";
import useModal from "@/hooks/useModal";
import { Plus, Search } from "@element-plus/icons-vue";
import useElPagination from "@/hooks/useElPagination";
import useClassifyList from "@/components/classify-list/useClassifyList";

import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import sourceModal from "./source-model.vue";
import cateModal from "./cate-modal.vue";
import classifyList from "@/components/classify-list/classify-list.vue";

const { close, show, visible, width } = useModal(500);
const { close: closeCateModal, show: showCateModal, visible: visibleCateModal, width: widthCateModal } = useModal(640);

const list = ref([]);
const total = ref(0);
const loading = ref(false);
const searchKey = ref("");
const currentSource = ref({});
const currentCate = ref({});

// 分类管理相关
const options = {
  add: addSourceCate,
  remove: deleteSourceCate,
  update: updateSourceCate,
  sort: sortSourceCate,
  getList: getSourceCateList,
  callback: () => getList(),
  loading,
};

const { cateList, current, getCateList, changeCurrent, componentProps } = useClassifyList(options);

const { page, pageSize, changePage, changeSize } = useElPagination(getList);

async function getList() {
  loading.value = true;
  try {
    const params = {
      page: page.value,
      pageSize: pageSize.value,
      searchKey: searchKey.value,
      corpId: localStorage.getItem("corpId"),
      userId: localStorage.getItem("userId"),
    };

    // 如果选择了分类，则添加分类ID
    if (current.value._id) {
      const childrenIds = Array.isArray(current.value.childrenIds) ? current.value.childrenIds : [];
      params.cateIds = [current.value._id, ...childrenIds];
    }

    const { success, data, message } = await getSourceList(params);
    if (success) {
      const { list: sourceList, total: count = 0 } = data;
      list.value = sourceList;
      total.value = count;
    } else {
      ElMessage.error(message);
    }
  } catch (error) {
    ElMessage.error("获取来源列表失败");
  } finally {
    loading.value = false;
  }
}

function formatCategoryName(sourceCateIdGroup) {
  if (!Array.isArray(sourceCateIdGroup) || sourceCateIdGroup.length === 0) {
    return "-";
  }
  // 尝试从cateList中找到匹配的分类名称
  const categoryNames = [];
  sourceCateIdGroup.forEach((id) => {
    const findCategory = (list) => {
      for (const item of list) {
        if (item._id === id) {
          categoryNames.push(item.label);
          return true;
        }
        if (item.children && item.children.length > 0) {
          if (findCategory(item.children)) {
            return true;
          }
        }
      }
      return false;
    };
    findCategory(cateList.value);
  });

  return categoryNames.length > 0 ? categoryNames.join(", ") : "-";
}

function editSource(row) {
  currentSource.value = row;
  show();
}

function addSource() {
  currentSource.value = {
    cateId: current.value._id || "", // 如果当前选择了分类，将其设置为默认分类
  };
  show();
}

async function changeStatus(row) {
  try {
    await ElMessageBox.confirm(`是否${row.sourceStatus === "enable" ? "停用" : "启用"}该来源?`, "提示", { type: "warning" });
    const { success, message } = await updateSourceStatus({
      id: row._id,
      sourceStatus: row.sourceStatus === "enable" ? "disable" : "enable",
    });

    if (success) {
      ElMessage.success(message || "操作成功");
      getList();
    } else {
      ElMessage.error(message || "操作失败");
    }
  } catch (error) {
    // 用户取消操作
  }
}

function handleCate(data) {
  currentCate.value = { ...data };
  showCateModal();
}

async function onCateChange() {
  await getCateList();
  getList();
}
</script>

<style scoped>
.text-danger {
  color: #f56c6c;
}
</style>