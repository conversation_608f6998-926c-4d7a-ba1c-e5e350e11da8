<template>
  <div>
    <div border-bottom class="flex items-center py-8px px-10px text-14px" @click="showServiceModal()">
      <div font-semibold class="text-14px flex-shrink-0 leading-24px min-w-80px">责任人</div>
      <div class="flex-grow ml-10px">
        <ww-user v-if="personResponsible" :openid="personResponsible"></ww-user>
      </div>
      <div class="flex-shrink-0 ml-10px transform translate-y-1px" :class="serviceTimes.length === 0 ? 'opacity-0' : ''" color-primary>
        <el-icon>
          <ArrowRightBold />
        </el-icon>
      </div>
    </div>
    <div border-bottom class="flex py-8px px-10px text-14px">
      <div font-semibold class="text-14px leading-24px min-w-80px">联系方式</div>
      <div class="flex-grow ml-10px">
        <div v-if="mobiles.length === 0" class="flex justify-end leading-24px py-5px">
          <el-icon color="#006eff" class="pointer" size="16" @click="showMobile()">
            <EditPen />
          </el-icon>
        </div>
        <div v-for="(i, idx) in mobiles" :key="i.mobile + '_' + idx" class="flex items-center justify-between leading-24px pb-5px" :class="idx > 0 ? 'border-t border-gray-200 pt-5px' : ''">
          <div :class="isPhone ? 'text-[#006eff]' : ''" @click="callMobile(i.mobile)">
            <el-icon class="transform translate-y-1px" size="12">
              <Iphone />
            </el-icon>
            <span>{{ i.mobile }}</span>
            <span v-if="i.note" class="text-gray-500">（{{ i.note }}）</span>
          </div>
          <el-icon v-if="idx === 0" color="#006eff" class="pointer" size="16" @click="showMobile()">
            <EditPen />
          </el-icon>
        </div>
      </div>
      <!-- <div flex-shrink-0 color-primary @click="viewDetail()">查看更多</div> -->
    </div>
    <div border-bottom class="py-8px px-10px">
      <div class="flex items-center justify-between">
        <div font-semibold class="text-14px leading-24px min-w-80px">客户标签</div>
        <el-icon color="#006eff" class="pointer" size="16" @click="showModal()">
          <EditPen />
        </el-icon>
      </div>
      <div v-if="customer.tagIds && customer.tagIds.length" class="mt-5px flex flex-wrap">
        <customer-tag v-for="tagId in customer.tagIds" :key="tagId" :tag-id="tagId" />
      </div>
    </div>
    <div class="py-8px px-10px">
      <div flex items-center justify-between>
        <div font-semibold class="leading-24px text-14px min-w-80px">客户备注</div>
        <div class="flex-shrink-0">
          <el-icon color="#006eff" class="pointer" size="16" @click="showNote()">
            <EditPen />
          </el-icon>
        </div>
      </div>
      <div v-if="customer.notes" class="text-14px leading-24px break-all">
        {{ customer.notes }}
      </div>
    </div>
    <tag-modal :member-id="props.customer._id" :visible="visible" :value="customer.tagIds" :width="width" @change="change" @close="close" />
    <note-modal :member-id="props.customer._id" :visible="noteVisible" :value="customer.notes" :width="width" @change="change" @close="closeNote" />
    <mobile-modal :visible="mobileVisible" :customer="customer" :width="width" @change="change" @close="closeMobile" />
  </div>
</template>
<script setup>
import { computed, toRefs, watch, ref } from "vue";
import { memberStore } from "@/store/member";
import useModal from "@/hooks/useModal";
import validate from "@/utils/validate";
import customerTag from "@/components/customer-tag/customer-tag";
import mobileModal from "./mobile-modal";
import { isMobile } from "@/utils/common";

import noteModal from "./note-modal";
import tagModal from "./tag-modal";
const emits = defineEmits(["change"]);

const props = defineProps({
  customer: { type: Object, default: () => ({}) },
  team: { type: Object, default: () => ({}) },
  serviceTimes: { type: Array, default: () => [] },
});

const { reloadCorpInfo } = memberStore();
const { close, show, visible, width } = useModal();
const { close: closeNote, show: showNote, visible: noteVisible } = useModal();
const { close: closeMobile, show: showMobile, visible: mobileVisible } = useModal();
const isPhone = isMobile();

const { customer } = toRefs(props);
const mobiles = computed(() => {
  const list = [
    { mobile: customer.value.mobile, note: "本人" },
    { mobile: customer.value.phone1, note: customer.value.phone1Note },
    { mobile: customer.value.phone2, note: customer.value.phone2Note },
    { mobile: customer.value.phone3, note: customer.value.phone3Note },
  ];
  return list.filter((i) => validate.isMobile(i.mobile));
});
const personResponsible = computed(() => {
  let item = props.team && customer.value && Array.isArray(customer.value.personResponsibles) && customer.value.personResponsibles.find((i) => i.teamId === props.team.teamId);
  return item && item.corpUserId ? item.corpUserId : "";
});

function change(props = {}) {
  emits("change", props);
}

function showServiceModal() {
  if (props.serviceTimes.length > 0) emits("showService");
}

function callMobile(phoneNumber) {
  try {
    wx.makePhoneCall({
      phoneNumber, // 需要拨打的电话号码
      fail: (res) => {
        console.log(res);
      },
    });
  } catch (e) {
    window.open(`tel:${phoneNumber}`, "_target");
  }
}

async function showModal() {
  reloadCorpInfo(["tags"]);
  show();
}
</script>
<style lang="scss" scoped>
.border-top {
  border-top: 1px solid #eee;
}
</style>
