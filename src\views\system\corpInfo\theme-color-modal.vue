<template>
  <el-dialog :model-value="visible" title="修改主题色" :width="width" @close="close()">
    <el-form class="px-10px">
      <el-form-item class="is-required">
        <template #label> <span color-normal>主题色</span> </template>
        <el-color-picker v-model="color" :predefine="predefineColors" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div text-center>
        <el-button class="w-100px" @click="close">取消</el-button>
        <el-button class="w-100px" type="primary" :loading="loading" @click="confirm()"> 保存 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { ElMessage } from 'element-plus';
import { updateCorpInfo } from '@/api/corp.js';
import { memberStore } from "@/store/member";
import { validateColor } from "@/utils";


const emits = defineEmits(['close'])
const props = defineProps({
  visible: { type: Boolean, default: false },
  width: { type: Number }
})

const store = memberStore();
const { changeCorpInfo } = store;
const { themeColor } = storeToRefs(store);
const loading = ref(false);
const color = ref('');
const predefineColors = ref(['#FC8292', '#8F1D22', '#20B4B0', '#0D71DF', '#EE7352', '#004CA6'])
// '#ff4500',
// '#ff8c00',
// '#ffd700',
// '#90ee90',
// '#00ced1',
// '#1e90ff',
// '#c71585'


function close() {
  emits('close')
}

async function confirm() {
  if (validateColor(color.value)) {
    loading.value = true;
    const { success, message } = await updateCorpInfo({ themeColor: color.value })
    if (success) {
      ElMessage.success('保存成功')
      changeCorpInfo({ key: 'themeColor', value: color.value })
      close()
    } else {
      ElMessage.error(message || '保存失败')
    }
    loading.value = false;
  } else {
    ElMessage.error('请输入正确的颜色值')
  }
}

watch(() => props.visible, (val) => {
  if (val) color.value = themeColor.value
})


</script>
<style scoped>
.append-select {
  width: 80px;
}

.title-bar {
  position: relative;
  padding-left: 10px;
  margin-bottom: 15px;
  font-size: 14px;
  font-weight: bold;
  color: var(--yc-main-text-color);
}

.modal-footer {
  /* padding-top: 15px; */
  text-align: center;
}

.mr-20 {
  margin-right: 20px;
}
</style>