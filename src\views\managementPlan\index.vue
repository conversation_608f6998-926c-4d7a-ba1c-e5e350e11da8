<template>
  <my-layout v-loading="loading">
    <layout-item>
      <div flex items-center justify-between py-10 class="pb-15px">
        <div class="text-20px" font-semibold></div>
        <el-button type="primary" @click="addManagementPlan()">新建</el-button>
      </div>
    </layout-item>
    <layout-main v-if="list.length === 0" :scroll="false">
      <div flex flex-col justify-center h-full>
        <empty-data :top="0" title="暂无数据" />
      </div>
    </layout-main>
    <layout-main v-else>
      <div v-for="item in list" :key="item._id" bg-fff rounded-8 mb-10 px-15 common-shadow>
        <div flex items-center justify-between py-15 border-bottom>
          <div font-16 font-semibold mr-10 class="w-0 flex-grow mr-10px truncate">
            {{ item.planName }}
          </div>
          <div flex-shrink-0 font-14 flex items-center>
            <div mr-10>
              <span color-666>累计使用人次：</span>
              <span font-16 font-semibold class="inline-block min-w-40px max-w-100px truncate align-middle">
                {{ item.allCount || 0 }}
              </span>
            </div>
            <div mr-10>
              <span color-666>执行中人次：</span>
              <span font-16 font-semibold class="inline-block min-w-40px max-w-100px truncate align-middle">
                {{ item.executingCount || 0 }}
              </span>
            </div>

            <div mr-10>
              <span color-666>已结束人次：</span>
              <span font-16 font-semibold class="inline-block min-w-40px max-w-100px truncate align-middle">
                {{ item.closedCount || 0 }}
              </span>
            </div>
            <div>
              <span color-666>已变更人次：</span>
              <span font-16 font-semibold class="inline-block truncate align-middle">
                {{ item.changedCount || 0 }}
              </span>
            </div>
          </div>
        </div>

        <div flex items-center justify-between py-15>
          <div flex items-center font-14 color-666>
            <span>创建人：</span>
            <el-text class="mr-15px max-w-80px truncate">
              <ww-user v-if="item.createor" :openid="item.createor"></ww-user>
            </el-text>
            <span>创建时间：</span>
            <el-text class="mr-15px">{{ item.timeStr }}</el-text>
            <span>状态 ：</span>
            <el-text class="mr-15px" :type="item.planStatus ? 'primary' : ''">
              {{ item.planStatus ? "已启动" : "未启动" }}
            </el-text>
          </div>
          <div flex items-center>
            <el-text pointer class="mr-15px" type="primary" @click="showDetail(item)">详情</el-text>
            <el-text pointer class="mr-15px" type="primary" @click="editPlan(item)">
              {{ item.allCount > 0 ? "复制" : "编辑" }}
            </el-text>
            <el-text v-if="item.planStatus" type="danger" @click="stopPlan(item._id)">停用</el-text>
            <el-text v-else pointer type="primary" @click="enablePlan(item._id)">启用</el-text>
          </div>
        </div>
      </div>
    </layout-main>
    <layout-item>
      <pagination :bgWhite="false" :totalRow="total" :pageSize="pageSize" :currentPage="currentPage"
        @handle-size-change="onSizeChange" @handle-current-change="onCurrentChange" />
    </layout-item>
  </my-layout>
  <task-detial :visible="taskVisible" :managementPlan="managementPlan" @close="taskVisible = false"></task-detial>
</template>
<script setup>
import { ref, watch } from "vue";
import dayjs from "dayjs";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { storeToRefs } from "pinia";
import taskDetial from "./components/task-detial";
import WwUser from "@/components/ww-user/index.vue";
import EmptyData from "@/components/empty-data.vue";
import { teamStore } from "@/store/team";
import { managePlanStore } from "@/store/managePlan/managePlan.js";
const { getManagementPlan, stopPlan, enablePlan } = managePlanStore();
const { list, total, pageSize, loading, currentPage } = storeToRefs(managePlanStore());
const { currentTeam } = storeToRefs(teamStore());
const taskVisible = ref(false);
const managementPlan = ref({});
const router = useRouter();
watch(currentTeam, (newItem, oldItem) => {
  if (newItem && oldItem && newItem.teamId !== oldItem.teamId) {
    getManagementPlan();
  }
});
const source = {
  team: "团队计划",
  corp: "机构计划",
};
getManagementPlan();
function onSizeChange(e) {
  pageSize.value = e;
  getManagementPlan();
}
function onCurrentChange(e) {
  currentPage.value = e;
  getManagementPlan();
}
function showDetail(item) {
  taskVisible.value = true;
  managementPlan.value = item || {};
}
function addManagementPlan() {
  router.push({ name: "MANAGEMENTPLANDETAIL" });
}
function editPlan(item) {
  const { _id, allCount } = item;
  router.push({
    name: "MANAGEMENTPLANDETAIL",
    params: { id: _id, type: allCount > 0 ? "copy" : "edit" },
  });
}
</script>
<style scoped lang="scss"></style>