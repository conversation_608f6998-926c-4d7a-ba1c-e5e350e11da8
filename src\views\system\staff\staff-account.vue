<template>


  <my-layout>
      <!-- 账号规模组件 -->
  <account-size :corp-member-andcustomtor-count="corpMemberAndcustomtorCount" />
    <layout-item>
      <div class="py-5px flex bg-white">
        <div class="flex-grow">
          <div class="search-form">
            <div class="text-gray-500 text-14px flex items-center">
              <div class="filter-label pr-5px">账户名称:</div>
              <el-input
                v-model="searchForm.accountName"
                placeholder="请输入账户名称"
                clearable
                style="width: 200px"
              />
            </div>
            <div class="text-gray-500 text-14px flex items-center">
              <div class="filter-label pr-5px">账户状态:</div>
              <el-select
                v-model="searchForm.status"
                placeholder="请选择账户状态"
                clearable
                style="width: 150px"
              >
                <el-option label="启用" :value="'enabled'" />
                <el-option label="禁用" :value="'disabled'" />
              </el-select>
            </div>
          </div>
        </div>
        <div class="flex-shrink-0 flex items-center h-40px pr-15px">
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button type="primary" @click="handleAdd" class="ml-10px">账号开通</el-button>
        </div>
      </div>
    </layout-item>

    <layout-main :scroll="false">
      <el-table
        stripe
        border
        height="100%"
        :data="tableData"
        empty-text="暂无数据"
        v-loading="loading"
      >
        <el-table-column label="账号名称" align="center" min-width="140">
          <template #default="scope">
            <el-popover
              placement="top-start"
              :width="250"
              trigger="hover"
            >
              <template #reference>
                <div class="truncate cursor-pointer">
                  {{ scope.row.accountName }}
                </div>
              </template>
              <div v-html="getAccountPopoverContent(scope.row)"></div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="accountId" label="账号ID" align="center" min-width="120" />
        <el-table-column label="所属团队" align="center" min-width="150">
          <template #default="scope">
            <el-popover
              placement="top-start"
              :width="400"
              trigger="hover"
              :disabled="!getTeamPopoverContent(scope.row)"
            >
              <template #reference>
                <div class="truncate cursor-pointer">
                  {{ scope.row.team }}
                </div>
              </template>
              <div v-html="getTeamPopoverContent(scope.row)"></div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column label="平台角色" align="center" min-width="120">
          <template #default="scope">
            <el-popover
              placement="top-start"
              :width="400"
              trigger="hover"
              :disabled="!getRolePopoverContent(scope.row)"
            >
              <template #reference>
                <div class="truncate cursor-pointer">
                  {{ scope.row.role }}
                </div>
              </template>
              <div v-html="getRolePopoverContent(scope.row)"></div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="账号状态" align="center" min-width="100">
          <template #default="scope">
            <span :style="{ color: scope.row.status === 'enabled' ? '#000000' : '#ff0000' }">
              {{ scope.row.status === 'enabled' ? '已启用' : '已禁用' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="280">
          <template #default="scope">
            <el-button type="primary" link @click="handleEdit(scope.row)">修改</el-button>
            <el-button
              v-if="scope.row.status === 'enabled'"
              type="primary"
              link
              @click="handleDisable(scope.row)"
              
            >
              禁用
            </el-button>
            <el-button
              v-else
              type="primary"
              link
              @click="handleEnable(scope.row)"
              
            >
              启用
            </el-button>
            <el-button type="primary" link @click="handleTransfer(scope.row)">账户转让</el-button>
          </template>
        </el-table-column>
      </el-table>
    </layout-main>

    <layout-item>
      <pagination
        :totalRow="total"
        :pageSize="pageSize"
        :currentPage="currentPage"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handlePageChange"
      />
    </layout-item>
  </my-layout>

  <!-- 账号开通/编辑弹窗 -->
  <account-open-modal
    :visible="accountOpenVisible"
    :is-edit="isEditMode"
    :edit-data="editData"
    :corp-member-andcustomtor-count="corpMemberAndcustomtorCount"
    @close="handleModalClose"
    @success="handleAccountOpenSuccess"
  />


</template>

<script>
import { ElMessage, ElMessageBox } from 'element-plus';
import pagination from '@/components/pagination/pagination.vue';
import AccountSize from './components/account-size.vue';
import AccountOpenModal from './components/account-open-modal.vue';
import { getOpenedAccount, getRolesList, getCorpTeams, updateCorpMember } from '@/api/corp.js';
import { getDeptList } from '@/api/dept-manage';
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";

export default {
  name: 'StaffAccount',
  components: {
    pagination,
    AccountSize,
    AccountOpenModal,
    MyLayout,
    LayoutMain,
    LayoutItem
  },
  data() {
    return {
      searchForm: {
        accountName: '',
        status: ''
      },
      tableData: [],
      loading: false,
      total: 0,
      currentPage: 1,
      pageSize: 20,
      corpMemberAndcustomtorCount: {
        corpMemberCount: 0
      },
      roleList: [],
      departmentOptions: [],
      allTeams: [],
      accountOpenVisible: false,
      isEditMode: false,
      editData: {},
      originalAccountData: [] // 保存原始账号数据
    }
  },
  methods: {
    async loadAccountList() {
      this.loading = true;
      try {
        // 添加超时处理
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('账号数据加载超时')), 15000) // 15秒超时
        });

        const apiPromise = getOpenedAccount();

        // 获取已开通账号列表
        const response = await Promise.race([apiPromise, timeoutPromise]);
        console.log('API响应数据:', response);
        if (response.success && Array.isArray(response.data.data)) {
          let allAccounts = response.data.data;
          // 保存原始数据用于编辑
          this.originalAccountData = allAccounts;
          console.log('账号数据示例:', allAccounts.length > 0 ? allAccounts[0] : '无数据');

          // 根据搜索条件过滤
          if (this.searchForm.accountName) {
            allAccounts = allAccounts.filter(account =>
              account.name && account.name.includes(this.searchForm.accountName)
            );
          }
          if (this.searchForm.status) {
            allAccounts = allAccounts.filter(account => {
              const status = this.getAccountStatus(account);
              return status === this.searchForm.status;
            });
          }

          // 整个列表排序：已启用在前，已禁用在后
          allAccounts.sort((a, b) => {
            const aStatus = this.getAccountStatus(a);
            const bStatus = this.getAccountStatus(b);
            if (aStatus === 'enabled' && bStatus === 'disabled') return -1;
            if (aStatus === 'disabled' && bStatus === 'enabled') return 1;
            return 0;
          });

          // 分页处理
          this.total = allAccounts.length;
          const startIndex = (this.currentPage - 1) * this.pageSize;
          const endIndex = startIndex + this.pageSize;
          const pagedAccounts = allAccounts.slice(startIndex, endIndex);

          // 转换数据格式
          this.tableData = pagedAccounts.map(account => ({
            accountName: account.anotherName || account.name || '',
            accountId: account.userid || '',
            team: this.getBelongToTeamName(account.userid),
            role: this.getRoleText(account),
            status: this.getAccountStatus(account)
          }));
          // 更新账号规模数据
          this.corpMemberAndcustomtorCount.corpMemberCount = response.data.data.length;
        } else {
          this.tableData = [];
          this.total = 0;
          this.corpMemberAndcustomtorCount.corpMemberCount = 0;
        }
      } catch (error) {
        console.error('加载账号信息失败:', error);
        this.tableData = [];
        this.total = 0;
        this.corpMemberAndcustomtorCount.corpMemberCount = 0;

        // 如果是超时错误，给用户友好提示
        if (error.message && error.message.includes('超时')) {
          ElMessage.warning('账号数据加载超时，请稍后重试');
        } else {
          ElMessage.error('加载账号信息失败');
        }
      } finally {
        this.loading = false;
      }
    },
    getRoleText(account) {
      // 优先处理 roleIds 数组（多角色）- 与account-open-modal.vue保持一致
      if (account.roleIds && Array.isArray(account.roleIds) && account.roleIds.length > 0) {
        if (this.roleList && this.roleList.length > 0) {
          const roleNames = account.roleIds
            .map(roleId => {
              const role = this.roleList.find(r => r._id === roleId);
              return role ? role.roleName : null;
            })
            .filter(Boolean);

          if (roleNames.length > 0) {
            return roleNames.join('、');
          }
        }
        // 如果roleIds存在但找不到对应角色名称，返回"-"
        return '-';
      }

      // 处理单个 roleType（只有在没有roleIds时才使用）
      const roleType = account.roleType;
      if (roleType && this.roleList && this.roleList.length > 0) {
        const role = this.roleList.find(r => r.roleId === roleType);
        if (role) {
          return role.roleName;
        }
      }

      // 如果没有角色信息，显示"-"
      if (!roleType && (!account.roleIds || account.roleIds.length === 0)) {
        return '-';
      }

      // 回退到静态映射
      const roleMap = {
        'superAdmin': '超级管理员',
        'admin': '管理员',
        'member': '普通成员',
        'viewer': '查看者'
      };
      return roleMap[roleType] || '-';
    },

    // 获取账号状态
    getAccountStatus(account) {
      // 优先检查 accountState 字段
      if (account.accountState) {
        return account.accountState === 'disable' ? 'disabled' : 'enabled';
      }
      // 回退到 status 字段
      return account.status === 1 ? 'enabled' : 'disabled';
    },

    // 获取账号popover内容
    getAccountPopoverContent(row) {
      const originalData = this.findOriginalAccountData(row.accountId);
      if (!originalData) return '-';

      const accountInfo = {
        accountName: originalData.anotherName || originalData.name || '-',
        accountId: originalData.userid || '-',
        realName: originalData.name || '-',
        mobile: originalData.mobile || '-',
        workNo: originalData.workNo || originalData.workId || '-'
      };

      // 构建信息数组，在同一行显示
      const infoItems = [];
      infoItems.push(`账号名称：${accountInfo.accountName}`);
      infoItems.push(`账号ID：${accountInfo.accountId}`);

      if (accountInfo.realName !== accountInfo.accountName) {
        infoItems.push(`真实姓名：${accountInfo.realName}`);
      }
      if (accountInfo.mobile !== '-') {
        infoItems.push(`手机号：${accountInfo.mobile}`);
      }
      if (accountInfo.workNo !== '-') {
        infoItems.push(`工号：${accountInfo.workNo}`);
      }

      // 用"、"分隔在同一行显示
      const infoDisplay = infoItems.join('、');
      return `<div style="line-height: 1.6; word-wrap: break-word;">${infoDisplay}</div>`;
    },

    // 获取团队popover内容
    getTeamPopoverContent(row) {
      if (!row.team || row.team === '-' || row.team.length <= 20) {
        return null;
      }

      return `<div><strong>所属团队：</strong></div><div>${row.team}</div>`;
    },

    // 获取角色popover内容
    getRolePopoverContent(row) {
      const originalData = this.findOriginalAccountData(row.accountId);
      if (!originalData) return null;

      let fullRoleInfo = '';

      // 构建完整的角色信息
      if (originalData.roleIds && Array.isArray(originalData.roleIds) && originalData.roleIds.length > 0) {
        if (this.roleList && this.roleList.length > 0) {
          const roleNames = originalData.roleIds
            .map(roleId => {
              const role = this.roleList.find(r => r._id === roleId);
              return role ? role.roleName : null;
            })
            .filter(Boolean);

          if (roleNames.length > 0) {
            fullRoleInfo = roleNames.join('、');
          }
        }
      } else if (originalData.roleType) {
        if (this.roleList && this.roleList.length > 0) {
          const role = this.roleList.find(r => r.roleId === originalData.roleType);
          if (role) {
            fullRoleInfo = role.roleName;
          }
        }

        if (!fullRoleInfo) {
          const roleMap = {
            'superAdmin': '超级管理员',
            'admin': '管理员',
            'member': '普通成员',
            'viewer': '查看者'
          };
          fullRoleInfo = roleMap[originalData.roleType] || originalData.roleType;
        }
      }

      // 如果没有角色信息或信息过短，不显示popover
      if (!fullRoleInfo || fullRoleInfo === '-' || fullRoleInfo.length <= 10) {
        return null;
      }

      return `<div><strong>平台角色：</strong></div><div>${fullRoleInfo}</div>`;
    },
    formatDepartment(department) {
      try {
        if (!department) {
          return '-';
        }

        // 如果是数组，处理每个元素
        if (Array.isArray(department)) {
          const names = department.map(dept => this.getDepartmentName(dept)).filter(name => name && name !== '-');
          return names.length > 0 ? names.join(', ') : '-';
        }

        // 如果是单个部门
        return this.getDepartmentName(department);
      } catch (error) {
        console.error('格式化部门信息时出错:', error, department);
        return '-';
      }
    },

    getDepartmentName(deptId) {
      if (!deptId) return '-';

      // 如果是字符串，直接返回
      if (typeof deptId === 'string') {
        return this.findDepartmentName(deptId) || deptId;
      }

      // 如果是对象，尝试获取name属性
      if (typeof deptId === 'object') {
        if (deptId.name) {
          return deptId.name;
        }
        if (deptId.deptName) {
          return deptId.deptName;
        }
        if (deptId.label) {
          return deptId.label;
        }
        // 如果有ID，尝试查找名称
        const id = deptId._id || deptId.deptId || deptId.value;
        if (id) {
          return this.findDepartmentName(id) || String(id);
        }
      }

      return String(deptId);
    },

    findDepartmentName(targetId) {
      if (!this.departmentOptions || this.departmentOptions.length === 0) {
        return null;
      }

      function findName(options, id) {
        for (const option of options) {
          // 匹配 _id 或 deptId 或 value
          if (
            (option._id && String(option._id) === String(id)) ||
            (option.deptId && String(option.deptId) === String(id)) ||
            (option.value && String(option.value) === String(id))
          ) {
            return option.label || option.deptName || option.name;
          }

          if (option.children && option.children.length > 0) {
            const childName = findName(option.children, id);
            if (childName) return childName;
          }
        }
        return null;
      }

      return findName(this.departmentOptions, targetId);
    },

    getBelongToTeamName(userId) {
      if (!userId || !this.allTeams || this.allTeams.length === 0) {
        return '-';
      }

      // 根据团队列表获取该员工所属的团队
      const belongTeams = this.allTeams.filter((team) => {
        return Array.isArray(team.memberList) && team.memberList.indexOf(userId) !== -1;
      });

      const nameList = belongTeams.map((team) => team.name);
      return nameList.length > 0 ? nameList.join('、') : '-';
    },

    handleSearch() {
      this.currentPage = 1;
      this.loadAccountList();
    },
    handleEdit(row) {
      this.isEditMode = true;
      // 需要从原始数据中找到完整的账号信息
      const originalData = this.findOriginalAccountData(row.accountId);
      this.editData = {
        accountId: row.accountId,
        accountName: row.accountName,
        originalData: originalData, // 传递完整的原始数据
        teamNames: row.team, // 当前显示的团队名称
        roleNames: row.role   // 当前显示的角色名称
      };
      this.accountOpenVisible = true;
    },

    // 根据accountId查找原始账号数据
    findOriginalAccountData(accountId) {
      // 从API响应的原始数据中查找
      if (this.originalAccountData && this.originalAccountData.length > 0) {
        return this.originalAccountData.find(account => account.userid === accountId);
      }
      return null;
    },
    async handleDisable(row) {
      try {
        // 显示确认弹窗，与图片中的样式一致
        await ElMessageBox.confirm(
          '禁用后，该员工将无法登陆医客通平台。',
          '禁用提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        );

        // 查找原始数据
        const originalData = this.findOriginalAccountData(row.accountId);
        if (!originalData) {
          ElMessage.error('未找到账号信息');
          return;
        }

        // 调用禁用API
        const params = {
          accountState: 'disable'
        };

        const { success, message } = await updateCorpMember(originalData._id, params);

        if (success) {
          ElMessage.success('禁用成功!');
          // 刷新列表
          this.loadAccountList();
        } else {
          ElMessage.error(message || '禁用失败');
        }
      } catch (error) {
        // 用户取消操作时不显示错误
        if (error !== 'cancel') {
          console.error('禁用账号失败:', error);
          ElMessage.error('禁用账号失败');
        }
      }
    },

    async handleEnable(row) {
      try {
        // 显示确认弹窗
        await ElMessageBox.confirm(
          '是否启用该账号',
          '启用提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info'
          }
        );

        // 检查账号数量限制
        if (this.corpMemberAndcustomtorCount && this.corpMemberAndcustomtorCount.corpMemberCount) {
          // 这里可以添加账号数量限制检查逻辑
        }

        // 查找原始数据
        const originalData = this.findOriginalAccountData(row.accountId);
        if (!originalData) {
          ElMessage.error('未找到账号信息');
          return;
        }

        // 调用启用API
        const params = {
          accountState: 'enable'
        };

        const { success, message } = await updateCorpMember(originalData._id, params);

        if (success) {
          ElMessage.success('启用成功!');
          // 刷新列表
          this.loadAccountList();
        } else {
          ElMessage.error(message || '启用失败');
        }
      } catch (error) {
        // 用户取消操作时不显示错误
        if (error !== 'cancel') {
          console.error('启用账号失败:', error);
          ElMessage.error('启用账号失败');
        }
      }
    },

    async handleTransfer(row) {
      try {
        // 显示转让提示弹窗
        await ElMessageBox.confirm(
          `1、账号转让后，被转让员工无法登陆医客通平台。\n2、账户转让后，被转让员工目动清开平台员工列表及已自有团队。`,
          '账号转让提示',
          {
            confirmButtonText: '转让',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: false
          }
        );

        // 用户确认转让后，打开账号开通页面
        this.isEditMode = false;
        this.editData = {
          transferFrom: row, // 保存被转让的账号信息
          isTransfer: true   // 标记这是转让操作
        };
        this.accountOpenVisible = true;

      } catch (error) {
        // 用户取消操作时不显示错误
        if (error !== 'cancel') {
          console.error('账号转让失败:', error);
          ElMessage.error('账号转让失败');
        }
      }
    },
    handleAdd() {
      this.isEditMode = false;
      this.editData = {};
      this.accountOpenVisible = true;
    },

    handleModalClose() {
      this.accountOpenVisible = false;
      this.isEditMode = false;
      this.editData = {};
    },

    handleAccountOpenSuccess(userid, type) {
      // 账号开通/编辑/转让成功后刷新列表
      this.loadAccountList();
      // 可以在这里添加其他成功后的逻辑
      if (type === 'transfer') {
        console.log('账号转让成功:', userid);
      } else {

      }
    },



    // 获取账号popover内容
    getAccountPopoverContent(row) {
      const originalData = this.findOriginalAccountData(row.accountId);
      if (!originalData) {
        return null;
      }

      const accountInfo = {
        accountName: originalData.anotherName || originalData.name || '-',
        accountId: originalData.userid || '-',
        realName: originalData.name || '-',
        mobile: originalData.mobile || '-',
        workNo: originalData.workNo || originalData.workId || '-'
      };

      // 如果信息过短，不显示popover
      if (!accountInfo.accountName || accountInfo.accountName === '-' || accountInfo.accountName.length <= 6) {
        return null;
      }

      let content = `<div><strong>账号信息：</strong></div>`;
      content += `<div>账号名称：${accountInfo.accountName}</div>`;
      content += `<div>账号ID：${accountInfo.accountId}</div>`;
      if (accountInfo.realName !== accountInfo.accountName) {
        content += `<div>真实姓名：${accountInfo.realName}</div>`;
      }
      if (accountInfo.mobile !== '-') {
        content += `<div>手机号：${accountInfo.mobile}</div>`;
      }
      if (accountInfo.workNo !== '-') {
        content += `<div>工号：${accountInfo.workNo}</div>`;
      }

      return content;
    },

    // 获取团队popover内容
    getTeamPopoverContent(row) {
      // 获取完整的团队信息
      const originalData = this.findOriginalAccountData(row.accountId);
      if (!originalData) return '-';

      const fullTeamInfo = this.getBelongToTeamName(originalData.userid);

      // 如果团队信息为空，显示"-"
      if (!fullTeamInfo || fullTeamInfo === '-') {
        return '-';
      }

      // 将团队信息按"、"分割并在同一行显示
      const teams = fullTeamInfo.split('、').filter(team => team.trim());
      const teamDisplay = teams.join('、');

      return `<div style="line-height: 1.6; word-wrap: break-word;">${teamDisplay}</div>`;
    },

    // 获取角色popover内容
    getRolePopoverContent(row) {
      const originalData = this.findOriginalAccountData(row.accountId);
      if (!originalData) {
        return '-';
      }

      // 优先使用roleIds
      if (originalData.roleIds && Array.isArray(originalData.roleIds) && originalData.roleIds.length > 0) {
        // 只显示roleIds，不转换为角色名称
        const roleDisplay = originalData.roleIds.join('、');
        return `<div style="line-height: 1.6; word-wrap: break-word;">${roleDisplay}</div>`;
      }

      // 如果没有roleIds但有roleType
      if (originalData.roleType) {
        return `<div style="line-height: 1.6; word-wrap: break-word;">${originalData.roleType}</div>`;
      }

      // 如果没有角色信息，显示"-"
      return '-';
    },




    handlePageChange(page) {
      this.currentPage = page;
      this.loadAccountList();
    },
    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
      this.loadAccountList();
    },

    async loadRoleList() {
      try {
        // 添加超时处理
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('角色数据加载超时')), 10000) // 10秒超时
        });

        const apiPromise = getRolesList();

        const { data, success } = await Promise.race([apiPromise, timeoutPromise]);

        if (success && data && data.data) {
          this.roleList = data.data || [];
          console.log('角色列表加载成功:', this.roleList.length, '个角色');
        } else {
          console.warn('获取角色列表失败:', data);
          this.roleList = [];
        }
      } catch (error) {
        console.error('加载角色列表失败:', error);
        this.roleList = [];

        // 如果是超时错误，给用户友好提示
        if (error.message && error.message.includes('超时')) {
          ElMessage.warning('角色数据加载超时，角色信息可能显示不完整');
        }
      }
    },

    async loadDepartmentOptions() {
      try {
        const corpId = localStorage.getItem('corpId');
        if (!corpId) return;

        const { data, success } = await getDeptList({ corpId });
        if (success && data && data.list) {
          this.departmentOptions = this.buildDepartmentTree(data.list);
        }
      } catch (error) {
        console.error('加载部门选项失败:', error);
      }
    },

    async loadAllTeams() {
      try {
        const corpId = localStorage.getItem('corpId');
        if (!corpId) {
          console.warn('未找到corpId');
          this.allTeams = [];
          return;
        }

        // 添加超时处理
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('团队数据加载超时')), 15000) // 15秒超时
        });

        const apiPromise = getCorpTeams(1, 999, corpId);

        // 使用Promise.race实现超时控制
        const { data, success } = await Promise.race([apiPromise, timeoutPromise]);
        console.log('团队API响应:', { data, success });

        if (success && data && data.list) {
          // getCorpTeams返回的数据结构是 { list: [], total: number }
          const teams = data.list || [];
          this.allTeams = teams.map((team) => ({
            ...team,
            memberList: Array.isArray(team.memberList) ? team.memberList.filter(Boolean) : [],
            memberLeaderList: Array.isArray(team.memberLeaderList) ? team.memberLeaderList.filter(Boolean) : []
          }));
          console.log('团队列表加载成功:', this.allTeams.length, '个团队');
        } else {
          console.warn('获取团队列表失败:', data);
          this.allTeams = [];
        }
      } catch (error) {
        console.error('加载团队列表失败:', error);
        this.allTeams = [];

        // 如果是超时错误，给用户友好提示
        if (error.message && error.message.includes('超时')) {
          ElMessage.warning('团队数据加载超时，团队信息可能显示不完整');
        }
      }
    },

    buildDepartmentTree(deptList) {
      const deptMap = new Map();
      const rootDepts = [];

      // 创建部门节点映射
      deptList.forEach(dept => {
        const deptNode = {
          _id: dept._id,
          deptId: dept.deptId,
          value: dept._id,
          label: dept.deptName || dept.label,
          deptName: dept.deptName || dept.label,
          parentId: dept.parentId,
          children: []
        };
        deptMap.set(dept._id, deptNode);
        // 同时用deptId作为key，方便查找
        if (dept.deptId) {
          deptMap.set(dept.deptId, deptNode);
        }
      });

      // 构建树形结构
      deptList.forEach(dept => {
        const deptNode = deptMap.get(dept._id);
        if (dept.parentId && deptMap.has(dept.parentId)) {
          const parent = deptMap.get(dept.parentId);
          parent.children.push(deptNode);
        } else {
          rootDepts.push(deptNode);
        }
      });

      return rootDepts;
    }
  },
  async mounted() {
    try {
      // 并行加载不相互依赖的数据
      await Promise.all([
        this.loadRoleList(),
        this.loadDepartmentOptions(),
        this.loadAllTeams()
      ]);

      // 团队数据加载完成后再加载账号数据
      await this.loadAccountList();
    } catch (error) {
      console.error('初始化数据加载失败:', error);
      // 即使部分数据加载失败，也要尝试加载账号数据
      this.loadAccountList();
    }
  }
}
</script>

<style lang="scss" scoped>
.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 15px;
  padding: 10px 15px;
}

.filter-label {
  white-space: nowrap;
}

.role-cell,
.department-cell,
.team-cell {
  word-break: break-word;
  line-height: 1.4;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}



/* 团队单元格样式 */
.team-cell {
  transition: background-color 0.3s;
}

.team-cell:hover {
  background-color: #f5f7fa;
}

/* 表格单元格样式 */
.truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
