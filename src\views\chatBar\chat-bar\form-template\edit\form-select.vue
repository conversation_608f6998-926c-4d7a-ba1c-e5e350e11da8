<template>
  <el-form-item class="px-15px py-6px el-form-item--autoheight" :class="required ? 'is-required' : ''" :label="name"
    border-bottom>
    <el-select ref="selectRef" class="w-full" clearable :multiple="mult" :model-value="value" @change="change($event)">
      <el-option v-for="opt in options" :key="opt.value" :value="opt.value">
        {{ opt.label }}
      </el-option>
    </el-select>
  </el-form-item>
</template>
<script setup>
import { computed, nextTick, ref } from "vue";

const emits = defineEmits(["change"]);
const props = defineProps({
  form: { type: Object, default: () => ({}) },
  mult: { type: Boolean, default: false },
  name: { type: String, default: "" },
  realRange: {},
  range: { type: Array, default: () => [] },
  required: { type: Boolean, default: false },
  title: { type: String, default: "" },
  rangeKey: { type: String, default: "" },
});
const range = computed(() => {
  if(Array.isArray(props.realRange)) return props.realRange;
  return Array.isArray(props.range) ? props.range : [];
})
const options = computed(() => {
  if (props.rangeKey) return range.value.map((opt) => ({ label: opt.label, value: opt[props.rangeKey] }));
  return range.value.map((opt) => ({ label: opt, value: opt }));
});

const value = computed(() => props.form[props.title] || "");

const selectRef = ref();

async function change(value) {
  emits("change", { title: props.title, value });
  await nextTick();
  const clearIcon = selectRef.value.$el.querySelector(".el-input__clear");
  if (clearIcon) {
    clearIcon.style.display = "block";
  }
}
</script>
<style lang="scss" scoped></style>
