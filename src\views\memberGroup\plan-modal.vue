<template>
  <el-dialog :model-value="props.visible" title="加入计划" :width="400" @close="close">
    <div p-15>
      <el-form :label-width="100">
        <el-form-item class="is-required" label="开始时间 ">
          <el-date-picker v-model="planFrom.planExecutionTime" class="w-180px" placeholder="请填写回访计划开始时间"></el-date-picker>
        </el-form-item>

        <el-form-item class="is-required" label="计划跟踪人" prop="executorUserId">
          <el-select v-model="planFrom.executorUserId" class="el-select--hiddenValue w-220px" placeholder="请选择计划跟踪人" filterable>
            <template #prefix>
              <div class="h-30px" color-666>
                <ww-user :openid="planFrom.executorUserId"></ww-user>
              </div>
            </template>
            <el-option v-for="userId in currentTeam.memberList" :key="userId" :value="userId">
              <ww-user :openid="userId"></ww-user>
              <!-- <span>{{ userId }}</span> -->
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div text-center>
        <el-button class="w-100px" @click="close()">取消</el-button>
        <el-button class="w-100px" type="primary" @click="save()">立即执行</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { teamStore } from "@/store/team";
import { storeToRefs } from "pinia";
const { currentTeam } = storeToRefs(teamStore());
const props = defineProps({
  visible: { type: Boolean, default: false },
  customer: { type: Object, default: () => {} },
});
const planFrom = ref();
const emits = defineEmits(["close", "executePlan"]);
function close() {
  emits("close");
}
async function save() {
  const { planExecutionTime, executorUserId } = planFrom.value;
  if (!planExecutionTime) {
    ElMessage.warning("请选择回访计划开始时间!");
    return;
  }
  if (!executorUserId) {
    ElMessage.warning("请选择计划跟踪人!");
    return;
  }

  emits("executePlan", planFrom.value);
}

watch(
  () => props.visible,
  (n) => {
    if (n) {
      const executorUserId = Array.isArray(props.customer.personResponsibles) ? props.customer.personResponsibles.find((item) => item.teamId === currentTeam.value.teamId)?.corpUserId : "";
      planFrom.value = {
        planExecutionTime: "",
        executorUserId,
      };
    }
  }
);
</script>

<style lang="scss">
.el-select.el-select--hiddenValue .el-input__inner {
  display: none;
}

.el-select.el-select--hiddenValue .el-input__prefix {
  flex-grow: 1;
}
</style>
