<template>
  <div class="w-full h-full flex b-fff">
    <div class="w-200px border-r">
      <el-scrollbar wrap-class="h-full">
        <empty-data v-if="authMemberList.length == 0" :top="200" title="暂无员工" padding="0 0 15px" text-top="10px" :image-width="60"></empty-data>
        <div v-for="(i, idx) in authMemberList" class="truncate px-15px py-10px cursor-pointer hover:text-blue-500" :class="selectdUserId === i.userid ? 'text-blue-500' : ''" @click="getSelectdUser(i)">
          <span class="leading-24px">
            <ww-user :openid="i.userid"></ww-user>
          </span>
        </div>
      </el-scrollbar>
    </div>
    <custoner-chat-record :customerUserId="customerUserId" :memberUserId="memberUserId" class="w-full" />
  </div>
</template>

<script setup>
import custonerChatRecord from "@/views/chat-history/customer-chat-record.vue";
import { authMemberList, getUserAuthList, selectdUserId, getSelectdUser, memberLoading } from "@/views/chat-history/api/authMember.js";
import { onMounted } from "vue";
const props = defineProps({
  customerUserId: {
    type: String,
    default: "wmLgjyawAAQzaoYIGD668-Mg8jMWKpgw",
  },
  memberUserId: {
    type: String,
    default: "23090101",
  },
});
onMounted(() => {
  if (!authMemberList.value.length) getUserAuthList();
});
</script>