<template>
  <filter-item type="base" label="标签" :text="text" @onClick="show()">
  </filter-item>
  <tag-picker v-model="tagIds" :width="width" :visible="visible" @close="close" />
</template>
<script setup>
import { computed, ref } from 'vue';
import useModal from '@/hooks/useModal';
import FilterItem from './filter-item.vue';
import TagPicker from './tag-picker.vue';

const tagIds = ref([]);
const text = computed(() => tagIds.value && tagIds.value.length ? `已选${tagIds.value.length}项` : '');
const { width, visible, show, close } = useModal(640)

function getParam() {
  return tagIds.value.length ? { tagIds: tagIds.value } : {}
}

function reset() {
  tagIds.value = []
}

defineExpose({
  getParam,
  reset
})
</script>
<style lang="scss" scoped></style>
