<template>
  <el-dialog :model-value="visible" class="chat-commonwords-dialog" :close-on-click-modal="false" title="编辑分组"
    :width="width" @close="close">
    <template #header="{ titleClass }">
      <div :class="titleClass"><span font-14 font-semibold>编辑分组</span></div>
    </template>
    <el-scrollbar wrap-style="max-height: 50vh">
      <empty-data v-if="cateList.length === 0 && !newCate" :imageWidth="120" :top="0" title="暂无分组" />
      <div px-15 py-10 flex items-center border-dashed-bottom v-for="cate in cateList" :key="cate._id">
        <div flex-grow mr-15>
          <el-input v-if="newCate && newCate._id === cate._id" v-model="newCate.label" placeholder="输入分组名称"
            :maxlength="10"></el-input>
          <span v-else>{{ cate.label }}</span>
        </div>
        <div flex-shrink-0>
          <template v-if="newCate && newCate._id === cate._id">
            <el-button text size="small" type="primary" @click="saveCate()">保存</el-button>
            <el-button text size="small" @click="newCate = null">取消</el-button>
          </template>
          <template v-else>
            <el-button text size="small" type="primary" @click="editCate(cate)">编辑</el-button>
            <el-button text size="small" type="danger" @click="removeCate(cate)">删除</el-button>
          </template>
        </div>
      </div>
      <div v-if="newCate && !newCate._id" px-15 py-10 flex items-center>
        <div flex-grow mr-15>
          <el-input v-model="newCate.label" placeholder="输入分组名称" :maxlength="10"></el-input>
        </div>
        <div flex-shrink-0>
          <el-button text size="small" type="primary" @click="saveCate()">保存</el-button>
          <el-button text size="small" @click="newCate = null">取消</el-button>
        </div>
      </div>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button class="w-100px" :icon="Plus" size="small" type="primary" @click="editCate()"> 添加分组 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

import EmptyData from "@/components/empty-data.vue";
import { Plus } from '@element-plus/icons-vue';
import { addUserCommonWordCate, updateUserCommonWordCate, deleteUserCommonWordCate } from '@/api/knowledgeBase'

const emits = defineEmits(['close', 'change', 'remove', 'delete'])

const props = defineProps({
  visible: { type: Boolean, default: false },
  cates: { type: Array, default: () => [] },
  width: { type: Number, default: 300 }

})

const cateList = computed(() => props.cates.filter(i => i._id !== 'all'))

function close() { emits('close') };


const newCate = ref(null);
function editCate(cate = {}) {
  const editSameCate = newCate.value && newCate.value._id === cate._id;
  const editNewCate = !cate._id && newCate.value && !newCate.value._id;
  if (editSameCate || editNewCate) return
  else newCate.value = { ...cate }
}

async function saveCate() {
  if (newCate.value.label && newCate.value.label.trim() === '') {
    ElMessage.info('请输入分组名称')
    return
  }
  if (newCate.value._id) {
    const { success, message } = await updateUserCommonWordCate({
      corpId: localStorage.getItem("corpId"),
      userId: localStorage.getItem("userId"),
      id: newCate.value._id,
      label: newCate.value.label
    });
    if (success) {
      newCate.value = null
      ElMessage.success('保存成功')
      emits('change')
    } else {
      ElMessage.error(message)
    }
    return
  }
  const { success, message } = await addUserCommonWordCate({
    corpId: localStorage.getItem("corpId"),
    userId: localStorage.getItem("userId"),
    label: newCate.value.label
  });
  if (success) {
    newCate.value = null
    ElMessage.success('保存成功')
    emits('change')
  } else {
    ElMessage.error(message)
  }
}

async function removeCate(cate) {
  await ElMessageBox.confirm('确认删除该分类以及该分类下的常用语吗？', '提示', { type: 'warning' });
  const { success, message } = await deleteUserCommonWordCate({
    id: cate._id,
    userId: localStorage.getItem("userId"),
    corpId: localStorage.getItem("corpId"),
  });
  if (success) {
    ElMessage.success(message)
    emits('delete', cate._id)
  }
  else ElMessage.error(message);
}

// watch(currentCate, getList)

</script>
<style scoped lang="scss">
.cateList {
  position: relative;

  @at-root &__scroll {
    position: absolute;
    inset: 0;
  }
}

[border-dashed-bottom] {
  border-bottom: 1px dashed #eee;
}

:deep(.el-button.is-text.el-button--small) {
  padding-left: 0;
  padding-right: 0;
}
</style>