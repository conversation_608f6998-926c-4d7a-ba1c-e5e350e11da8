<template>
  <el-dialog :model-value="props.visible" title="任务单详情" :width="750" @close="onClose()">
    <!-- :rules="rules" -->
    <div class="action-bar" v-if="form.eventStatus === 'untreated'">
      <template v-if="form.executorUserId">
        <el-button type="primary" :loading="loading" class="close-btn" @click="confirm('取消', 'closed')">关闭</el-button>
        <el-button type="primary" plain :loading="loading" @click="confirm('完成', 'treated')">办结</el-button>
      </template>
      <el-button v-else type="primary" :loading="loading" @click="receive()">接受任务</el-button>
    </div>
    <el-form class="task-form" :model="form" ref="formRef" :label-width="105">
      <el-scrollbar :max-height="maxHeight">
        <el-row>
          <el-col :span="12">
            <el-form-item label="创建时间" prop="createTime">
              <el-input disabled v-model="form.createTime"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务分类" prop="taskType">
              <el-input disabled v-model="form.taskType"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务状态" prop="taskStatus">
              <el-input disabled v-model="form.taskStatus"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item v-if="form.eventStatus === 'treated' || form.eventStatus === 'closed'" label="结束时间" prop="endTime">
              <el-input disabled v-model="form.endTime"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="创建人" prop="creator">
              <span v-if="form.creatorUserId === 'system'">系统自动</span>
              <span v-else-if="form.creatorUserId">
                <span mr-10><ww-user :openid="form.creatorUserId"></ww-user></span>
                <el-button type="primary" size="small" plain @click="contact('', form.creatorUserId)">联系同事</el-button>
              </span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="接收对象" prop="executor">
              <ww-user v-if="form.executorUserId" :openid="form.executorUserId"></ww-user>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="服务对象" prop="customerName">
              <span mr-10>{{ form.customerName || props.wechatNames[form.externalUserId] || "" }}</span>
              <el-button v-if="form.customerId" mr-10 type="primary" size="small" plain @click="toCustomerDetail()">客户详情</el-button>
              <el-button v-if="form.externalUserId" type="primary" size="small" plain @click="contact(form.externalUserId)">聊天</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="任务内容" prop="taskContent">
              <el-input type="textarea" disabled v-model="form.taskContent" :autosize="{ minRows: 2, maxRows: 8 }"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="处理结果" prop="result">
              <el-input type="textarea" v-model="form.result" :disabled="!(form.eventStatus === 'untreated')" :autosize="{ minRows: 2, maxRows: 8 }"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-timeline v-if="false">
          <el-timeline-item timestamp="2018/4/12" :icon="Check" type="primary" placement="top">
            <el-card>
              <h4>任务单已发起</h4>
              <p>发起人：胡一</p>
            </el-card>
          </el-timeline-item>
          <el-timeline-item timestamp="2018/4/12" :icon="MoreFilled" type="primary" placement="top">
            <el-card>
              <h4>任务单处理中</h4>
              <p>处理人:张晓</p>
            </el-card>
          </el-timeline-item>
          <el-timeline-item timestamp="2018/4/12" placement="top">
            <el-card>
              <h4>任务单待完结</h4>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </el-scrollbar>
    </el-form>
  </el-dialog>
  <customer-detial :visible="customerDetialVisible" @close="customerDetialVisible = false" :customerId="form.customerId" title="客户详情" customerType="member" />
</template>
<script setup>
import { onMounted, ref, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import { updateTodo } from "@/api/todo";
import { openEnterpriseChat } from "@/utils/jssdk";
import { Check, MoreFilled } from "@element-plus/icons-vue";
import customerDetial from "@/views/member/detail/index.vue";
import WwUser from "@/components/ww-user/index.vue";

const props = defineProps({
  data: { type: Object, default: () => ({}) },
  visible: { type: Boolean, default: false },
  wechatNames: { type: Object, default: () => ({}) },
});
const maxHeight = ref("400px");
onMounted(() => {
  const height = window.innerHeight;
  maxHeight.value = height - height * 0.15 - 220 + "px";
});

const form = ref({});
const formRef = ref();
function initForm() {
  const data = { ...props.data };
  form.value = data;
}
watch(props, (n, o) => {
  if (!n.visible) return;
  initForm();
  const height = window.innerHeight;
  maxHeight.value = height - height * 0.15 - 220 + "px";
});

const emits = defineEmits(["close", "updated"]);
function onClose() {
  emits("close");
}

const loading = ref(false);
async function save(eventStatus, action) {
  loading.value = true;
  const { success, message } = await updateTodo(form.value._id, {
    result: form.value.result || `已${action}`,
    executorUserId: localStorage.getItem("userId"),
    eventStatus,
  });
  if (success) {
    loading.value = false;
    emits("updated");
  } else {
    ElMessage.error(message);
  }
}
async function receive() {
  await ElMessageBox.confirm(`确定接收该任务吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  });
  loading.value = true;
  const { success, message } = await updateTodo(form.value._id, {
    executorUserId: localStorage.getItem("userId"),
  });
  if (success) {
    loading.value = false;
    emits("updated");
  } else {
    ElMessage.error(message);
  }
  loading.value = false;
}

function confirm(action, eventStatus) {
  ElMessageBox.confirm(`确定${action}该待办事项吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    save(eventStatus, action);
  });
}

function contact(externalUserIds = "", userId = "") {
  openEnterpriseChat(externalUserIds, userId);
}

const router = useRouter();

const customerDetialVisible = ref(false);
function toCustomerDetail() {
  customerDetialVisible.value = true;
}
</script>
<style scoped>
.task-form {
  padding: 0 15px;
}

.action-bar {
  text-align: right;
  padding-bottom: 15px;
  padding-right: 15px;
}

.close-btn:active,
.close-btn:visited,
.close-btn:focus {
  color: #fff;
}
</style>