<template>
  <div v-if="isRead">{{ showText }}</div>
  <div class="flex flex-wrap" v-else>
    <div class="py-6px text-12px leading-12px">
      {{ showText }}
    </div>
    <el-button class="ml-10px" type="primary" size="small" :icon="Plus" plain @click="openDialog()">
      {{ openTitle }}
    </el-button>
    <el-drawer v-model="showView" :title="item.name">
      <div class="drawer">
        <el-tag v-for="i in selfRange" :key="i" size="large" class="pointer drawer"
          :type="selections.some((e) => e === i) ? 'primary' : 'info'"
          :effect="selections.some((e) => e === i) ? 'light' : 'plain'" @click="toggle(i)">
          {{ i }}
        </el-tag>
      </div>
      <diagnosis v-if="showOther" :value="diseases" :item="item" class="ml-10px" @change="onChange"
        @select="onGetOptions($event)">
      </diagnosis>
      <template #footer>
        <div common-shadow--r class="button_view">
          <el-button class="l w-100px" type="primary" @click="save">保存</el-button>
          <el-button class="r w-100px" @click="cancel">取消</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>
<script setup>
import { ElMessage } from "element-plus";
import { computed, ref } from "vue";
import diagnosis from "./diagnosis.vue";
import { memberStore } from "@/store/member";
let $emit = defineEmits(["change"]);
const props = defineProps({
  form: { type: Object, default: () => ({}) },
  item: {
    type: Object,
    default: {},
  },
  value: {
    type: String || Array,
    default: "" || [],
  },
  isRead: {
    type: Boolean,
    default: false,
  },
});
const selfRange = computed(() => {
  const { corpInfo } = memberStore();
  let arr = corpInfo.diseases || [];
  if (!arr.some((e) => e === "其他")) {
    arr.push("其他");
  }
  return arr;
});

const diseases = ref([]);

function onGetOptions(val) {

}

function onChange({ title, value }) {
  diseases.value = value;
}

const openTitle = computed(() => {
  // 判断value是否为空 或空数组
  if (!props.value || (Array.isArray(props.value) && props.value.length === 0)) {
    return "新增";
  } else {
    return "编辑";
  }
});
const arrayValue = computed(() => (Array.isArray(props.value) ? props.value.filter(Boolean) : [props.value].filter(Boolean)));
const showText = computed(() => {
  if (props.item.title === "surgicalHistory") return arrayValue.value.filter((i) => i !== "有" && i !== "其他").join("、");
  return arrayValue.value.filter((i) => i !== "其他").join("、");
});

let showView = ref(false);
const selections = ref([]);
const otherValue = ref("");
const showOther = ref(false);
const placeholder = computed(() => {
  let title = "请补充其它内容";
  if (props.item.title === "surgicalHistory") {
    title = "请补充手术名称";
  }
  return title;
});

function openDialog() {
  const range = selfRange.value;
  selections.value = [...arrayValue.value].filter((e) => range.some((i) => i === e));
  const list = [...arrayValue.value].filter((item) => !range.some((e) => e === item));
  if (list.length > 0) {
    if (props.item.title === "surgicalHistory" && !selections.value.some((e) => e === "有")) {
      selections.value.push("有");
    } else if (!selections.value.some((e) => e === "其他")) {
      selections.value.push("其他");
    }
    diseases.value = list;
    showOther.value = true;
  }
  showView.value = true;
}

function toggle(i) {
  if (i === "其他" || (props.item.title === "surgicalHistory" && i === "有")) {
    showOther.value = true;
  }
  if (selections.value.some((e) => e === i)) {
    if (i === "其他") {
      showOther.value = false;
      otherValue.value = "";
    }
    selections.value = selections.value.filter((e) => e !== i);
  } else {
    if (i === "无") {
      selections.value = [i];
      showOther.value = false;
      otherValue.value = "";
    } else {
      selections.value = selections.value.filter((e) => e !== "无");
      selections.value.push(i);
    }
  }
}

function save() {
  if (selections.value.some((e) => e == "其他") && diseases.value.length === 0) {
    ElMessage.warning(placeholder.value);
    return;
  }
  selections.value = selections.value.filter((item) => item !== "其他");
  diseases.value.length !== 0 && (selections.value = [...selections.value, ...diseases.value]);
  if (selections.value.length === 0) {
    ElMessage.warning("选择内容不能为空!");
    return;
  }
  showView.value = false;
  $emit("change", {
    title: props.item.title,
    value: selections.value,
  });
}

function cancel() {
  showView.value = false;
  selections.value = props.value || [];
}
</script>



<style lang="scss" scoped>
.drawer {
  margin: 5px;
  min-width: 60px;
}

.button_view {
  display: flex;
  padding: 15px 0;
  justify-content: center;
  border-top: 1px solid #eee;

  .l {
    margin-right: 20px;
  }
}

:deep(.el-drawer__footer) {
  --el-drawer-padding-primary: 0;
  padding-top: 0;
}
</style>