<template>
  <img v-if="props.avatar" :style="style" :src="props.avatar" />
  <img v-else-if="useDefault" :style="style" :src="defaultAvatar" />
  <img v-else :style="style" :src="src" />
</template>
<script setup>
import { computed } from 'vue';
import women from '@/assets/icons/avatar-women1.svg';
import man from '@/assets/icons/avatar-man.svg';
import comonAvatar from '@/assets/icons/avatar-common.svg';
import defaultAvatar from '@/assets/icons/default-avatar.png';

import avatar from '@/assets/images/avatar.png';

const props = defineProps({
  sex: { type: String, default: '' },
  avatar: { type: String, default: '' },
  size: { type: Number, default: 60 },
  useDefault: { type: Boolean, default: false }
})

const src = computed(() => {
  return comonAvatar
  if (props.sex === '女') return women;
  if (props.sex === '男') return man;
  return avatar;
})

const style = computed(() => ({
  width: props.size + 'px',
  height: props.size + 'px',
}))
</script>