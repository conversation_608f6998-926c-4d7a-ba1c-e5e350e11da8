<template>
  <el-scrollbar>
    <person-info @edit="showEdit()"></person-info>
    <health-info />
    <edit-modal :visible="visible" :customer="customer" @close="visible = false"
      @update="handleEdit($event)"></edit-modal>
  </el-scrollbar>
</template>
<script setup>
import { provide, ref, toRefs } from "vue"
import { ElMessage } from "element-plus";
import { updateMember } from '@/api/member';

import EditModal from "./edit-modal/index.vue";
import HealthInfo from "./health-info";
import PersonInfo from "./person-info";

const emits = defineEmits(['reload']);

provide('labelWidth', 110)
provide('formSize', 'large')

const props = defineProps({ customer: { type: Object, default: () => ({}) } })
const { customer } = toRefs(props);
provide('customer', customer)

const visible = ref(false)
function showEdit() {
  visible.value = true
}
async function handleEdit(data) {
  const success = await update(data)
  if (success) {
    visible.value = false
    emits('reload');
  }
}


async function update(data) {
  const { success, message } = await updateMember(customer.value._id, data)
  if (success) {
    ElMessage.success(message)
  } else {
    ElMessage.error(message)
  }
  return success
}

</script>