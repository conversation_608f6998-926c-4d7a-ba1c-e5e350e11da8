<template>
  <my-layout class="w-240px" bg-fff>
    <layout-item>
      <div flex items-center justify-between p-15>
        <div active-title-bar font-semibold class="text-20px">员工列表</div>
        <el-button class="is-hover pointer" size="small" :icon="Plus" plain @click="addCorpMember">添加员工</el-button>
      </div>
    </layout-item>
    <layout-main>
      <div class="role-group__name" v-if="rolesMemberList.length > 0">已开通账号</div>
      <div class="role-group__staff flex justify-between" flex items-center v-for="member in rolesMemberList" :key="member._id" :class="{ 'is-active': selectMember.userid == member.userid }">
        <div @click="selectMemberAction(member)">
          <el-icon class="mr-5px" flex-shrink-0>
            <Avatar />
          </el-icon>
          <ww-user :openid="member.userid"></ww-user>
          <span class="pl-10px text-12px" v-if="member.accountState === 'disable'">已禁用</span>
        </div>
        <div v-if="member.roleType === 'superAdmin'">超级管理员</div>
        <el-dropdown v-else>
          <el-icon>
            <MoreFilled />
          </el-icon>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="enableAccount(member)" v-if="member.accountState === 'disable'">启用</el-dropdown-item>
              <el-dropdown-item @click="stopAccount(member)" v-else>禁用</el-dropdown-item>
              <el-dropdown-item @click="transferAccount(member)">账户转让</el-dropdown-item>
              <el-dropdown-item @click="restPassword(member)" v-if="needLogin">重置密码</el-dropdown-item>
              <el-dropdown-item @click="removeAccount(member)" v-if="needLogin">删除</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <div class="role-group__name mt-50px" v-if="notOpenedAccount.length > 0">未开通账号</div>
      <div class="role-group__staff flex justify-between" flex items-center v-for="member in notOpenedAccount" :key="member._id" :class="{ 'is-active': selectMember.userid == member.userid }" @click="selectMemberAction(member)">
        <div>
          <el-icon class="mr-5px" flex-shrink-0>
            <Avatar />
          </el-icon>
          <ww-user :openid="member.userid"></ww-user>
        </div>
        <el-dropdown>
          <el-icon>
            <MoreFilled />
          </el-icon>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="removeAccount(member)">删除</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </layout-main>
  </my-layout>
</template>

<script setup>
import { ref } from "vue";
import { selectEnterpriseContact } from "@/utils/jssdk";
import WwUser from "@/components/ww-user/index.vue";
import { Plus } from "@element-plus/icons-vue";
import { memberStore } from "@/store/member";
const { corpInfo } = memberStore();
import { updateCorpMember, getCorpMember, judgeTransferTime, removeCorpMember } from "@/api/corp.js";
import { getCorpMemberInfoByUserId } from "@/api/wecom.js";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import { ElMessage, ElMessageBox } from "element-plus";
const needLogin = import.meta.env.VITE_NEED_LOGIN === "true"; // 是否需要登录
let $emit = defineEmits(["select-member", "update-success"]);
const props = defineProps({
  rolesMemberList: { type: Array, default: [] },
  notOpenedAccount: { type: Array, default: [] },
  selectMember: { type: Object, default: {} },
  corpMemberAndcustomtorCount: { type: Object, default: {} },
});
function selectMemberAction(item) {
  $emit("select-member", item);
}
async function addCorpMember() {
  $emit("select-member", {});
}

async function stopAccount(member) {
  await ElMessageBox.confirm("禁用后，该员工将无法登陆医客通平台。", "禁用提示");
  const params = {
    accountState: "disable",
  };
  const { success, message } = await updateCorpMember(member._id, params);
  if (success) {
    $emit("update-success", member.userid, "disable");
    ElMessage.success("禁用成功!");
  } else {
    ElMessage.success(message);
  }
}
async function transferAccount(member) {
  // 判断是否在转让时间内
  if (member.accountState == "disable" && corpInfo && corpInfo.package) {
    const corpPackage = corpInfo.package;
    const enableAccount = props.rolesMemberList.filter((item) => item.accountState !== "disable");
    let reatCount = corpPackage.accountCount + corpPackage.giveAccountCount - enableAccount.length;
    if (reatCount <= 0) {
      ElMessage.error("账号数量已达上限，请联系客服购买");
      return;
    }
  }
  const { data } = await judgeTransferTime(member.userid);
  const { isAfterToday, remainingTime } = data.data;
  if (!isAfterToday) {
    const str = `同一个账户，30天内仅支持转让一次. ${remainingTime}天后可再次转让`;
    ElMessage.error(str);
    return;
  }
  await ElMessageBox.confirm("<div> 1、账号转让后，被转让员工无法登陆医客通平台。</div><div>2、账户转让后，被转让员工自动离开平台员工列表及已有团队。</div>", "账号转让提示", {
    confirmButtonText: "转让",
    cancelButtonText: "取消",
    dangerouslyUseHTMLString: true,
  });
  const res = await selectEnterpriseContact();
  const { selectedUserList = [] } = res;
  if (selectedUserList.length > 1) {
    ElMessage.error("只能选择一名员工!");
    return;
  }
  if (selectedUserList.length === 0) {
    ElMessage.error("员工还未添加应用!");
    return;
  }
  const userid = selectedUserList[0].id;
  let user = { userid };
  const onOpenUser = Array.isArray(props.notOpenedAccount) && props.notOpenedAccount.find((item) => item.userid === userid);
  if (!onOpenUser) {
    let memberList = await getCorpMemberList();
    if (memberList.some((item) => item.userid === userid)) {
      ElMessage.error("该员工已存在!");
      return;
    }
  } else {
    user = onOpenUser;
  }
  $emit("select-member", user, "transfer", member.userid);
}
async function getCorpMemberList() {
  let params = {
    corpId: localStorage.getItem("corpId"),
  };
  let { data } = await getCorpMember(params, 999, 1);
  return data.data;
}

async function enableAccount(member) {
  await ElMessageBox.confirm("是否启用该账号", "启用提示");
  if (corpInfo && corpInfo.package) {
    const corpPackage = corpInfo.package;
    const enableAccount = props.rolesMemberList.filter((item) => item.accountState !== "disable");
    let reatCount = corpPackage.accountCount + corpPackage.giveAccountCount - enableAccount.length;
    if (reatCount <= 0) {
      ElMessage.error("账号数量已达上限，请联系客服购买");
      return;
    }
  }
  const params = {
    accountState: "enable",
  };
  const { success, message } = await updateCorpMember(member._id, params);
  if (success) {
    $emit("update-success", member.userid, "enable");
    ElMessage.success("启用成功!");
  } else {
    ElMessage.success(message);
  }
}

async function removeAccount(member) {
  await ElMessageBox.confirm("是否删除该账号", "删除提示");
  const { success, message } = await removeCorpMember(member._id);
  if (success) {
    $emit("update-success", "", "delete");
    ElMessage.success("删除成功!");
  } else {
    ElMessage.success(message);
  }
}

async function restPassword(member) {
  await ElMessageBox.confirm("是否重置密码", "重置密码提示");
  const params = {
    password: "",
  };
  const { success, message } = await updateCorpMember(member._id, params);
  if (success) {
    ElMessage.success("重置密码成功!");
  } else {
    ElMessage.success(message);
  }
}
</script>

<style lang="scss" scoped>
.role-group {
  @at-root &__name {
    font-size: 14px;
    line-height: 18px;
    padding: 0 15px 10px;
    color: #909399;
  }

  @at-root &__staff {
    font-size: 14px;
    padding: 0 15px 15px;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.9);
    cursor: pointer;

    @at-root &:hover {
      color: #006eff;
      // background-color: #006eff;
    }

    @at-root &.is-active {
      color: #006eff;
      // background-color: #006eff;
    }
  }
}
</style>
