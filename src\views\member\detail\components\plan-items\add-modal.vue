<template>
  <el-dialog :model-value="visible" title="新增待跟进事项" :width="600" @close="close">
    <el-form class="pt-20px pr-20px" :label-width="100">
      <el-form-item class="is-required" label="执行日期" prop="executionTime">
        <el-date-picker v-model="date" format="YYYY-MM-DD" placeholder="请选择执行日期" type="date" value-format="YYYY-MM-DD" :disabled-date="disabledPastDate" style="width: 100%" />
      </el-form-item>
      <el-form-item label="待办类型" class="is-required">
        <el-select v-model="form.eventType" filterable style="width: 100%" placeholder="请选择待办类型">
          <el-option v-for="item in eventTypeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item class="is-required" label="跟进人">
        <ww-user-select :list="memberList" placeholder="请选择跟进人" :value="form.executorUserId" @change="change($event)" />
      </el-form-item>
      <el-form-item class="is-required" label="跟进事项" prop="taskContent">
        <el-input v-model="form.taskContent" maxlength="200" resize="none" :rows="3" show-word-limit type="textarea" placeholder="请输入跟进事项" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="flex justify-center">
        <el-button class="w-100px" @click="close()">取消</el-button>
        <el-button class="w-100px" type="primary" @click="save()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, onMounted, ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { ElMessage } from "element-plus";
import dayjs from "dayjs";
import { createTodo } from "@/api/todo";
import { ToDoEventType } from "@/baseData";
import { memberStore } from "@/store/member";
import { teamStore as useTeamStore } from "@/store/team";
import WwUserSelect from "@/components/ww-user-select";

const emits = defineEmits(["close"]);
const props = defineProps({
  customer: { type: Object, default: () => ({}) },
  visible: { type: Boolean, default: false },
});
const eventTypeList = Object.keys(ToDoEventType).map((key) => ({ label: ToDoEventType[key], value: key }));

const { currentTeam } = storeToRefs(useTeamStore());

const store = memberStore();
const { memberInfo } = storeToRefs(store);
const memberList = computed(() => {
  return currentTeam.value && Array.isArray(currentTeam.value.memberList) ? currentTeam.value.memberList : [];
});
const date = ref("");
const form = ref({});

function change(value) {
  form.value.executorUserId = value;
}

function close() {
  emits("close");
}

function save() {
  if (date.value === "") {
    ElMessage.info("请选择执行日期");
  } else if (!form.value.eventType || !ToDoEventType[form.value.eventType]) {
    ElMessage.info("请选择待办类型");
  } else if (!form.value.executorUserId) {
    ElMessage.info("请选择跟进人");
  } else if (typeof form.value.taskContent !== "string" || form.value.taskContent.trim() === "") {
    ElMessage.info("请输入跟进事项");
  } else {
    add();
  }
}

async function add() {
  const time = dayjs(date.value).isAfter(dayjs().endOf("day")) ? dayjs(dayjs(date.value).format("YYYY-MM-DD 07:00:00")) : dayjs();
  const todo = {
    corpId: localStorage.getItem("corpId"),
    customerId: props.customer._id,
    customerName: props.customer.name,
    expireTime: dayjs(date.value).add(7, "day").valueOf(),
    plannedExecutionTime: time.valueOf(),
    externalUserId: props.customer.externalUserId,
    taskContent: form.value.taskContent.trim(),
    creatorUserId: localStorage.getItem("userId"),
    eventStatus: "untreated",
    eventType: form.value.eventType,
    executorUserId: form.value.executorUserId,
    executeTeamId: currentTeam.value.teamId,
  };
  const { success, message } = await createTodo(todo);
  if (success) {
    ElMessage.success("新增跟进事项成功");
    close();
    emits("change");
  } else {
    ElMessage.error(message);
  }
}
function disabledPastDate(e) {
  return dayjs().startOf("day").isAfter(dayjs(e));
}
onMounted(async () => {
  if (memberList.value.length === 1) {
    form.value.executorUserId = memberList.value[0];
  }
});

watch(
  () => props.visible,
  (n) => {
    if (n) {
      date.value = dayjs().format("YYYY-MM-DD");
      form.value = { executeTeamId: "", taskContent: "", executorUserId: memberInfo.value.userid };
    }
  }
);
</script>