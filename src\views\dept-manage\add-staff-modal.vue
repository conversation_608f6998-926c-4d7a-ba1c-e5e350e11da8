<template>
  <el-dialog :model-value="props.visible" :width="width" title="添加员工" @close="close()">
    <el-scrollbar wrap-class="el-dialog-body-full-scroll">
      <div class="p-15px">
        <div class="text-14px" color-normal>科室名称：{{dept.deptName}}</div>
        <div
          class="mt-10px p-10px flex items-center border border-gray-200 min-h-40px cursor-pointer rounded hover:border-gray-300"
          @click="select()">
          <div v-if="userIds.length == 0" class="flex-shrink-0 text-gray-500 text-13px">请选择员工</div>
          <div class="flex-grow flex flex-wrap -mb-5px">
            <div class="flex items-center rounded border border-gray-200 px-10px py-5px mr-5px mb-5px group bg-gray-50"
              v-for="(item, index) in userIds" :key="index" @click.stop="">
              <div> <ww-user :openid="item" /></div>
              <el-icon class="ml-5px text-gray-500 hover:text-red-500" :size="14" @click.stop="remove(item)">
                <CloseBold />
              </el-icon>
            </div>
          </div>
          <el-icon class="flex-shrink-0" :size="18">
            <CaretBottom />
          </el-icon>
        </div>
      </div>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button plain class="w-100px" type="primary" @click="close()">取消</el-button>
        <el-button class="w-100px" :loading="loading" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <select-member-vue ref="selectMemberRef" @getSelectItems="onSelected($event)"></select-member-vue>

</template>
<script setup>
import { ref, watch } from "vue";
import { ElMessage } from "element-plus";
import selectMemberVue from "@/components/select-member/index.vue";
import { addDeptStaff } from "@/api/dept-manage";

const emits = defineEmits(['close', 'change']);
const props = defineProps({
  dept: { type: Object, default: () => ({}) },
  visible: { type: Boolean, default: false },
  width: { type: [String, Number], default: '500px' },
})
const selectMemberRef = ref(null);
const userIds = ref([]);

function close() {
  emits('close')
}

const loading = ref(false);
function onSelected({ checkedMemberList }) {
  userIds.value = [...checkedMemberList]
}

function select() {
  selectMemberRef.value.openDialog([...userIds.value], [], false);
}

function remove(idx) {
  userIds.value.splice(idx, 1)
}

async function confirm() {
  if (userIds.value.length === 0) {
    ElMessage.warning('请选择员工')
  } else {
    const { success, message } = await addDeptStaff({
      corpId: localStorage.getItem('corpId'),
      deptId: props.dept._id,
      userIds: userIds.value
    })
    if (success) {
      ElMessage.success('添加成功')
      emits('change')
      close()
    } else {
      ElMessage.error(message)
    }
  }
}

watch(() => props.visible, (n) => {
  if (n) userIds.value = []
})

</script>