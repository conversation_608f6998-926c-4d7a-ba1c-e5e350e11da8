<template>
  <el-dialog :model-value="visible" title="修改标签" :width="width" center @close="close">
    <el-scrollbar :max-height="maxHeight">
      <el-form label-position="top">
        <el-form-item v-for="(group, idx) in tagList" :key="idx" :label="group.label">
          <el-tag v-for="item in group.options" :key="item.id" pointer size="large" class="tag__item"
            :type="selections[item.id] ? 'primary' : 'info'" :effect="selections[item.id] ? 'light' : 'plain'"
            @click="toggle(item, group.options)">
            {{ item.name }}
          </el-tag>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="saveTags()"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref, watch } from 'vue';
import { ElMessage } from "element-plus";
import { updateMember } from "@/api/member";
import { memberStore } from "@/store/member";

const emits = defineEmits(['close', 'change'])
const props = defineProps({
  memberId: { type: String, default: '' },
  tagIds: { type: Array, default: () => [] },
  visible: { type: Boolean, default: false },
})

const { corpInfo } = memberStore();
const tagList = ref(Array.isArray(corpInfo.tags) ? corpInfo.tags : []); //标签列表
const tags = computed(() => {
  return tagList.value.reduce((list, { options }) => {
    return [...list, ...(Array.isArray(options) ? options : [])]
  }, [])
})

const selections = ref({});
function setSelection() {
  selections.value = Array.isArray(props.tagIds) ? props.tagIds.reduce((val, id) => {
    const opt = tags.value.find(i => i.id === id);
    if (opt) val[id] = id;
    return val
  }, {}) : {}
}

function toggle(tag, opts) {
  opts.forEach(({ id }) => {
    selections.value[id] = id == tag.id ? (selections.value[id] ? false : tag.id) : false;
  });
}

async function saveTags() {
  const tagIds = Object.values(selections.value).filter(Boolean);
  const { success, message } = await updateMember(props.memberId, { tagIds });
  if (success) {
    ElMessage.success(message);
    emits('change', tagIds);
    close();
  } else {
    ElMessage.error(message);
  }
}

function close() {
  emits('close')
}

const maxHeight = ref(Math.floor((window.innerHeight - 220) * 0.8));
const width = ref(Math.floor(window.innerWidth * 0.75));
watch(() => props.visible, n => {
  if (n) {
    maxHeight.value = Math.floor((window.innerHeight - 220) * 0.8);
    width.value = Math.floor(window.innerWidth * 0.75);
    setSelection()
  }
})
</script>
<style scoped>
.tag__item {
  margin-right: 15px;
  margin-bottom: 15px;
  padding: 6px 10px;
  min-width: 80px;
}
</style>