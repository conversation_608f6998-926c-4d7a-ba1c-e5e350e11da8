<template>
  <view class="unavailable-container">
    <img src="@/assets/tishi.png" alt="提示图标" class="w-120px h-120px mt-100px" />
    <view class="unavailable-message mt-10px c-666">{{ errorMessage }}</view>
    <!-- <button v-if="showReturnButton" @click="handleReturn" class="return-button mt-20px">返回</button> -->
  </view>
</template>

<script setup>
import { computed, ref } from "vue";
import { memberStore } from "@/store/member";

// 错误信息常量
const ERROR_MESSAGES = {
  EXTERNAL_BROWSER: "应用不支持在外部浏览器打开, 请在企业微信内部打开",
  NO_ROLE: "应用管理员未给您配置角色,请联系应用管理员配置角色",
  EXPIRED: "账号已到期，请联系平台客服升级套餐",
  DISABLED: "您已被禁用,如有疑问请联系机构管理员",
  NO_TEAM: "应用管理员未给您配置团队,请联系应用管理员配置角色"
};

const showReturnButton = ref(true);

// 优化后的错误消息计算属性
const errorMessage = computed(() => {
  // 检查是否在企业微信内部打开
  if (!localStorage.getItem("NEWCODE") && import.meta.env.VITE_NEED_LOGIN !== "true") {
    return ERROR_MESSAGES.EXTERNAL_BROWSER;
  }
  
  const { roleIds, memberInfo, corpInfo } = memberStore();
  const { package: packAge } = corpInfo;
  
  // 检查角色配置
  if (roleIds.length === 0) {
    return ERROR_MESSAGES.NO_ROLE;
  }π
  
  // 检查套餐状态
  if (!packAge || (packAge && (packAge.packageStatus === "expire" || packAge.packageStatus === "closed"))) {
    return ERROR_MESSAGES.EXPIRED;
  }
  
  // 检查账号状态
  if (memberInfo.accountState === "disable") {
    return ERROR_MESSAGES.DISABLED;
  }
  
  // 检查团队配置
  if (!memberInfo.teamId || memberInfo.teamId.length === 0) {
    return ERROR_MESSAGES.NO_TEAM;
  }
  
  return "服务器异常，请稍后再试";
});

const handleReturn = () => {
  history.back();
};
</script>

<style scoped>
.unavailable-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 20px;
  text-align: center;
}

.unavailable-message {
  color: #666;
  max-width: 80%;
  line-height: 1.5;
}

.return-button {
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
}
</style>
