import { useRouter } from 'vue-router'
import { tagsStore } from "@/store/tags";
import { storeToRefs } from "pinia";
import { ElMessage } from 'element-plus';


export default function useNextMenu() {
  const { menus } = storeToRefs(tagsStore());
  const router = useRouter();

  return function (path, menuName) {
    if (menus.value.some(i => i.path === path)) {
      router.push({ name: path })
    } else {
      ElMessage.info(`您当前账户未开通”${menuName}“功能权限，请联系管理员开通`)
    }
  }
} 