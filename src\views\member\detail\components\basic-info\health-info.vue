<template>
  <info-card title="健康信息">
    <el-form :label-width="labelWidth" label-position="left">
      <el-row >
        <el-col :span="24" v-if="isShowCell('allergy')">
          <el-form-item label="过敏史：">
            <el-text size="default">{{ allergy }}</el-text></el-form-item
          >
        </el-col>
        <el-col :span="24" v-if="isShowCell('disease')">
          <el-form-item label="疾病史：">
            <el-text size="default">{{ disease }}</el-text></el-form-item
          >
        </el-col>
      </el-row>
    </el-form>
  </info-card>
</template>
<script setup>
import { computed, inject } from "vue";
import InfoCard from "../info-card";
import { templateStore } from "@/store/template";
const colAttr = { span: 24, md: 12, lg: 8 };
const labelWidth = inject("labelWidth");
const customer = inject("customer");
const { isShowCell } = templateStore();
const allergy = computed(() => {
  return customer.value.allergyHistory || "";
});
const disease = computed(() => {
  return Array.isArray(customer.value.pastMedicalHistory)
    ? customer.value.pastMedicalHistory.join("、")
    : "";
});


</script>
