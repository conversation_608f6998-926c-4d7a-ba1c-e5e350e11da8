<template>
  <my-layout>
    <layout-item>
      <div class="overflow-hidden mb-8px">
        <el-row :gutter="10">
          <el-col v-for="i in count" :key="i.key" :span="6">
            <div class="flex flex-col items-center bg-white rounded px-20px py-16px h-80px text-center select-none">
              <div class="w-full flex items-center justify-center pt-4px truncate text-18px font-semibold">
                <span>{{ i.enableHidden && i.hidden ? "--" : i.count }}</span>
                <svg-icon v-if="i.enableHidden && i.hidden" class="text-gray-500 ml-4px cursor-pointer" size="18" name="eye-close" @click="i.hidden = false"></svg-icon>
                <svg-icon v-else-if="i.enableHidden && !i.hidden" class="text-blue-500 ml-4px cursor-pointer" size="18" name="eye" @click="i.hidden = true"></svg-icon>
              </div>
              <div class="w-full mt-8px text-14px truncate text-gray-500">{{ i.label }}</div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="py-5px flex bg-white">
        <expand-filter-box v-model:can-expand="canExpand" :enable-expand="true" class="flex-grow" :expand="expand">
          <base-filter-item :clearable="selectDepts.length > 0" class="cursor-pointer" label="治疗科室" :text="deptText" @clear="clearDepts" @onClick="showDeptPicker()" />
          <date-range-filter v-model:dates="searchForm.reateTreatementTimeDates" label="分诊日期" :text="getDatesText(searchForm.reateTreatementTimeDates)" />
          <check-box-filter v-model="searchForm.treatmentDoctorUserIds" label="治疗人" :list="peopleList">
            <template #item="{ item }">
              <ww-user :openid="item.value"></ww-user>
            </template>
          </check-box-filter>
          <radio-filter v-model="searchForm.deductStatus" label="治疗状态" :list="DeductTreatmentStatus" />
          <date-range-filter v-model:dates="searchForm.treatmentTimeDates" label="治疗日期" :text="getDatesText(searchForm.treatmentTimeDates)"></date-range-filter>
          <base-filter-item :clearable="projectCategories.length > 0" class="cursor-pointer" label="项目分类" :text="categoryText" @clear="clearCategories" @onClick="showCategoryPicker()" />
          <base-filter-item :clearable="projects.length > 0" class="cursor-pointer" label="开单项目" :text="projectText" @clear="clearProjects" @onClick="showProjectModal()" />
          <base-filter-item :clearable="packageList.length > 0" class="cursor-pointer" label="套餐" :text="packageText" @clear="changePackage([])" @onClick="showPackageModal()" />
          <input-filter v-model="searchForm.name" label="客户姓名" placeholder="请输入" :width="200" />
          <input-filter v-model="searchForm.mobile" label="客户手机号" placeholder="请输入" :width="200" />
        </expand-filter-box>
        <div class="flex-shrink-0 flex items-center h-40px pr-15px">
          <el-button v-if="canExpand" text plain @click="expand = !expand">
            <span class="mr-5px">{{ expand ? "收起筛选" : "更多筛选" }}</span>
            <el-icon v-if="expand">
              <ArrowUpBold />
            </el-icon>
            <el-icon v-else>
              <ArrowDownBold />
            </el-icon>
          </el-button>
          <el-button type="primary" @click="search">查询</el-button>
          <el-button plain @click="reset">重置</el-button>
          <el-button plain type="primary" @click="bill()">新增治疗</el-button>
        </div>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <el-table border stripe height="100%" :data="list" v-loading="loading" @row-click="handleRowClick" :span-method="arraySpanMethod" :cell-class-name="getCellClassName">
        <el-common-column fixed prop="name" label="姓名" :min-width="100">
          <template #default="{ row: { name, customerId } }">
            <div @click.stop="toCustomerDetail(customerId)" main-color class="pointer">{{ name }}</div>
          </template>
        </el-common-column>
        <el-common-column fixed prop="sexAge" label="性别/年龄" :min-width="90" />
        <el-common-column fixed prop="mobile" label="联系方式" :min-width="120">
          <template #default="{ row: { mobile } }">
            <span>{{ maskPhone(mobile, 3, 4) }}</span>
          </template>
        </el-common-column>
        <el-common-column prop="wechat" label="微信联系人" :min-width="120" classnames="w-full">
          <template #default="{ row }">
            <wx-contact-td :customerId="row.customerInfo ? row.customerInfo._id : ''" :externalUserId="row.customerInfo ? row.customerInfo.externalUserId : ''" :row="row.customerInfo" @change="getList" />
          </template>
        </el-common-column>
        <el-common-column prop="visitStatusStr" label="就诊类型" :min-width="100">治疗</el-common-column>
        <el-common-column prop="projectName" label="开单项目" :min-width="160"></el-common-column>
        <el-common-column prop="packageName" label="所属套餐" :min-width="160">
          <template #default="{ row: { packageName } }">{{ packageName || "-" }}</template>
        </el-common-column>
        <el-common-column prop="restUsageCount" label="剩余/总数" :min-width="120">
          <template #default="{ row }">{{ row.restUsageCount }}/{{ row.usageCount }}</template>
        </el-common-column>
        <el-common-column prop="deducStatus" label="治疗状态" :min-width="160">
          <template #default="{ row }">
            <div>
              <div :style="'color:' + DeductTreatmentStatusColor[row.deductStatus]">{{ row.deductTreatmentStatus }}{{ DeductTreatmentStatusColor[row.deducStatus] }}</div>
              <div class="text-red-500" v-if="row.aenesthesiaEndTime && row.deductStatus === 'pending'">麻醉中(剩余{{ getAnesthesiaRestTme(row) }}分钟)</div>
            </div>
          </template>
        </el-common-column>
        <el-common-column prop="deductStatus" label="划扣状态" :min-width="100">
          <template #default="{ row: { deductStatus, deductStatusStr } }">
            <div :style="'color:' + DeductStatusColor[deductStatus]">{{ deductStatusStr }}</div>
          </template>
        </el-common-column>
        <el-common-column prop="deductUsageCount" label="划扣数" :min-width="100" />
        <el-common-column prop="deductedPriceStr" label="划扣金额" :min-width="100"></el-common-column>
        <el-common-column prop="treatmentDeptName" label="治疗科室" :min-width="140"></el-common-column>
        <el-common-column prop="treatmentDoctorUserId" label="治疗医生" :min-width="140">
          <template #default="{ row: { treatmentDoctorUserId } }">
            <ww-user :openid="treatmentDoctorUserId" />
          </template>
        </el-common-column>
        <el-common-column label="配台" :min-width="160" classnames="absolute w-full h-full">
          <template #default="{ row }">
            <el-tooltip :disabled="!(row.assistantDoctors && row.assistantDoctors.length > 0)" placement="top" effect="light">
              <div class="flex items-center justify-center w-full h-full">
                <div class="max-h-full max-w-full text-14px text-left leading-22px line-clamp-2">
                  <span v-for="(i, index) in row.assistantDoctors" :key="`${i}_${row._id}`">
                    <ww-user :openid="i" />
                    <span v-if="row.assistantDoctors.length - 1 !== index">、</span>
                  </span>
                </div>
              </div>
              <template #content>
                <span v-for="(i, index) in row.assistantDoctors" :key="`${i}__tooltip_${row._id}`">
                  <ww-user :openid="i" />
                  <span v-if="row.assistantDoctors.length - 1 !== index">、</span>
                </span>
              </template>
            </el-tooltip>
          </template>
        </el-common-column>
        <el-common-column prop="treatmentTimeStr" label="治疗时间" :min-width="160"></el-common-column>
        <el-common-column prop="createTimeStr" label="分诊时间" :min-width="160"></el-common-column>
        <el-common-column prop="creator" label="分诊人" :min-width="100">
          <template #default="{ row: { creator } }">
            <ww-user :openid="creator" />
          </template>
        </el-common-column>
        <el-table-column fixed="right" class-name="el-table-fixed-column--shadow" label="操作" width="185">
          <template #default="{ row }">
            <el-text class="cursor-pointer mr-5px" type="primary" @click.stop="registration(row)" v-if="!row.aenesthesiaStartTime && row.deductStatus === 'pending'">麻醉登记</el-text>
            <el-text class="cursor-pointer mr-5px" type="primary" @click.stop="deductProject(row)" v-if="row.deductStatus === 'pending'">划扣</el-text>
            <el-text class="cursor-pointer mr-5px" type="primary" @click.stop="toTreatMentRecord(row)" v-if="row.deductStatus === 'deducted'">治疗记录</el-text>
            <el-text class="cursor-pointer mr-5px" type="primary" @click.stop="toPlan(row)">回访计划</el-text>
            <el-text class="cursor-pointer mr-5px" type="danger" v-if="row.deductStatus === 'pending'" @click.stop="removeRecord(row)">取消</el-text>
          </template>
        </el-table-column>
      </el-table>
    </layout-main>
    <layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize" @handle-current-change="changePage" />
    </layout-item>
  </my-layout>
  <search-modal v-model="searchVisible" @close="searchClose()" @consult="openReception" @addCustomer="onAddCustomer" />
  <add-customer :customer="newCustomerInfo" :visible="addCustomerVisible" @close="addCustomerVisible = false" @update="addSuccess" title="新增患者" viewType="consult" />
  <customer-detail :enableAction="['managementPlan']" :visible="customerDetailVisible" @close="customerDetailVisible = false" :customerId="customerId" title="客户详情" @update="addSuccess" customerType="corpCustomer" />
  <project-list-picker :value="projects" :visible="projectModalVisible" :width="projectModalWidth" :current-category="projectCategories" @close="closeProjectModal()" @change="changeProjects" />
  <category-picker ref="categoryPickerRef" :value="projectCategories" :visible="categoryPickerVisible" :width="categoryPickerWidth" @close="closeCategoryPicker" @change="changeCategories" @categoryChange="handleCategoryChange" />
  <customer-bill-record :visible="billVisible" @close="closeBillVisible" :customer="customer" @success="getList" />
  <anesthesia-registration :visible="registrationVisible" @close="closeRegistrationVisible" :project="selectproject" @success="getList"></anesthesia-registration>
  <deduct-modal v-model="deductVisible" @close="closeDeductVisible" :project="selectproject" @success="success" @showCustomerDetail="toCustomerDetail"></deduct-modal>
  <deduct-record :visible="treatmentRecordVisible" @close="closeTreatmentRecordVisible" :project="selectproject" @success="success" />
  <dept-picker-modal :onlyStaffDepts="!(isManager || isAdmin)" ref="deptPickerRef" :value="deptIds" :visible="deptVisible" :width="deptPickerWidth" @change="deptChange" @close="closeDeptPicker" />
  <project-package-picker :data="packageList" :visible="packageModalVisible" :width="packageModalWidth" @close="closePackageModal()" @change="changePackage" />
  <component v-if="ManagePlanDrawer" :is="ManagePlanDrawer" :customer="customer" :visible="drawerVisible" @close="closeDrawer" />
</template>
<script setup>
import { ref, onMounted, computed, watch } from "vue";
import { storeToRefs } from "pinia";
import { ElMessageBox, ElMessage } from "element-plus";
import dayjs from "dayjs";
import { getCustomers } from "@/api/member";
import { getDeductRecord, updateDeductRecord, deductRecordStatistic, removeDeductRecord, updateTreatmentRecord } from "@/api/treatment-record";
import { getProjectListByCateIds } from "@/api/project-manage";
import { TreatmentStatus, TreatmentStatusColor, DeductStatus, DeductStatusColor, DeductTreatmentStatusColor, DeductTreatmentStatus } from "@/baseData";
import useManagePlanDrawer from "@/composables/useManagePlanDrawer";
import useElPagination from "@/hooks/useElPagination";
import useModal from "@/hooks/useModal";
import { memberStore } from "@/store/member";
import { staffStore } from "@/store/staff";
import { teamStore } from "@/store/team";
import { maskPhone } from "@/utils";
import addCustomer from "@/views/member/customer/add-customer.vue";
import anesthesiaRegistration from "./anesthesia-registration.vue";
import { CheckBoxFilter, baseFilterItem, DateRangeFilter, InputFilter, expandFilterBox, RadioFilter } from "@/components/filter-bar";
import customerBillRecord from "./customer-bill-record.vue";
import customerDetail from "@/views/member/detail/index.vue";
import deductModal from "./deduct-modal.vue";
import deptPickerModal from "@/components/dept-picker-modal/dept-picker-modal.vue";
import elCommonColumn from "@/components/el-common-column/el-common-column.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import projectListPicker from "@/components/project-picker/project-list-picker.vue";
import categoryPicker from "@/components/project-picker/category-picker.vue";
import projectPackagePicker from "@/components/project-package-picker/project-package-picker.vue";
import searchModal from "../consult-appoint/search-modal.vue";
import SvgIcon from "@/components/svg-icon";
import deductRecord from "./deduct-record.vue";
import wwUser from "@/components/ww-user/index.vue";
import wxContactTd from "@/components/wx-contact-td/wx-contact-td.vue";

const { currentTeamId, managerList } = storeToRefs(teamStore());
const { memberInfo } = storeToRefs(memberStore());
const customerDetailVisible = ref(false);
const loading = ref(false);
const customerId = ref("");
const selectproject = ref({});
const belongDeptIds = ref([]);
const canExpand = ref(false);
const expand = ref(false);
const packageList = ref([]);
const newCustomerInfo = ref({});
const { visible: drawerVisible, ManagePlanDrawer, openDrawer, closeDrawer } = useManagePlanDrawer();

function toCustomerDetail(id) {
  customerId.value = id;
  customerDetailVisible.value = true;
}
const searchForm = ref({});
const deptPickerRef = ref(null);
watch(
  () => currentTeamId.value,
  () => {
    getList();
    deductStatistic();
  }
);
const selectDepts = ref([]);
const projects = ref([]);
const projectCategories = ref([]);
const categoryPickerRef = ref(null);
const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());
const list = ref([]);
const { isAdmin } = storeToRefs(memberStore());
const { judgmentIsAdmin } = memberStore();
const isManager = computed(() => {
  return managerList.value.includes(10008);
});
const peopleList = computed(() => {
  const cureJobs = ["doctor", "assistant", "nurse", "expertDoctor", "therapist"];
  const jobPeople = staffList.value.filter((i) => Array.isArray(i.job) && i.job.some((job) => cureJobs.includes(job)));
  if (isManager.value || isAdmin.value) {
    return jobPeople.map((i) => ({ value: i.userid }));
  }
  const deptIds = Array.isArray(memberInfo.value.deptIds) ? memberInfo.value.deptIds : [];
  const userids = jobPeople.filter((i) => Array.isArray(i.deptIds) && i.deptIds.some((id) => deptIds.includes(id))).map((i) => ({ value: i.userid }));
  return userids;
});
const projectText = computed(() => {
  if (projects.value.length === 0) return "";
  if (projects.value.length == 1 && projects.value[0].projectName) return projects.value[0].projectName;
  return `已选择${projects.value.length}个项目`;
});
const categoryText = computed(() => {
  if (projectCategories.value.length === 0) return "";
  if (projectCategories.value.length > 1) return `已选择${projectCategories.value.length}个分类`;
  return projectCategories.value[0].name;
});
const packageText = computed(() => {
  if (packageList.value.length === 0) return "";
  if (packageList.value.length === 1) return packageList.value[0].name;
  return `已选择${packageList.value.length}个套餐`;
});

const deptIds = computed(() => selectDepts.value.map((i) => i._id));
const deptText = computed(() => {
  if (selectDepts.value.length === 0) return "";
  if (selectDepts.value.length > 1) return `已选择${selectDepts.value.length}个项目`;
  return selectDepts.value[0].deptName;
});
function clearProjects() {
  projects.value = [];
}
function clearCategories() {
  projectCategories.value = [];
  categoryPickerRef.value?.clear();
}
function getDatesText(dates) {
  return dates && Array.isArray(dates) ? dates.join(" ~ ") : "";
}
const date = ref();
const total = ref(0);
const { page, pageSize, changePage, changeSize } = useElPagination(getList);
const { close, show, visible, width } = useModal(640);
const { close: planClose, show: planShow, visible: planVisible, width: planWidth } = useModal(640);
const { show: receptionShow, visible: receptionVisible } = useModal();
const { show: addCustomerShow, visible: addCustomerVisible, close: addCustomerClose } = useModal();
const { close: searchClose, show: searchShow, visible: searchVisible, width: searchWidth } = useModal(1200);
const { close: closeProjectModal, show: showProjectModal, visible: projectModalVisible, width: projectModalWidth } = useModal(800);
const { close: closeCategoryPicker, show: showCategoryPicker, visible: categoryPickerVisible, width: categoryPickerWidth } = useModal(600);
const { close: closeBillVisible, show: showBillVisible, visible: billVisible } = useModal(800);
const { close: closeRegistrationVisible, show: showRegistrationVisible, visible: registrationVisible } = useModal(800);
const { close: closeDeductVisible, show: showDeductVisible, visible: deductVisible } = useModal(800);
const { close: closeTreatmentRecordVisible, show: showTreatmentRecordVisible, visible: treatmentRecordVisible } = useModal(800);
const { close: closeDeptPicker, show: showDeptPicker, visible: deptVisible, width: deptPickerWidth } = useModal(640);
const { close: closePackageModal, show: showPackageModal, visible: packageModalVisible, width: packageModalWidth } = useModal(800);

const customer = ref({});
const consultRecord = ref({});
const orderCount = ref({});
const arraySpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  const rows = list.value;
  const treatmentOrderId = row.treatmentOrderId;
  // 获取当前行的合并信息
  const currentRowSpan = rows.filter((item) => item.treatmentOrderId === treatmentOrderId).length;
  // 只在 item1 和 item2 列进行合并
  if (columnIndex === 0 || columnIndex === 1 || columnIndex === 2) {
    if (rowIndex === rows.findIndex((item) => item.treatmentOrderId === treatmentOrderId)) {
      return {
        rowspan: currentRowSpan,
        colspan: 1,
      };
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      };
    }
  }
};
const count = ref([
  { key: 1, label: "今日治疗人数", count: 0.0 },
  { key: 2, label: "今日治疗项目", count: 0.0 },
  { key: 3, label: "今日划扣业绩", count: 0.0, enableHidden: true, hidden: true },
]);

function deptChange(val) {
  selectDepts.value = val;
  closeDeptPicker();
}
function changeProjects(data) {
  projects.value = data.projects;
  closeProjectModal();
}
function changeCategories(data) {
  projectCategories.value = data.categories;
  // 当分类变更时，清空已选项目
  projects.value = [];
}
function handleCategoryChange(category) {
  // 当前分类变化时，清空已选项目
  projects.value = [];
}
function changePackage(data) {
  packageList.value = data;
}
function clearDepts() {
  selectDepts.value = [];
}
async function deductStatistic() {
  let params = {
    dates: [dayjs().format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")],
    treatmentDoctorUserIds: searchForm.value.treatmentDoctorUserIds,
  };
  if (!isAdmin.value) {
    if (!managerList.value.includes(10008)) params.belongTeamId = currentTeamId.value;
    params.creators = [localStorage.getItem("userId")];
  }
  if (!isAdmin.value && !selectDepts.value.length && !isManager.value) {
    params.treatmentDept_ids = belongDeptIds.value;
  }
  if (selectDepts.value.length) {
    params.treatmentDept_ids = selectDepts.value.map((i) => i._id);
  }

  // 添加项目筛选逻辑，与search函数保持一致
  if (projects.value.length > 0) {
    // 如果选择了具体项目，直接使用项目ID
    params.projectIds = projects.value.map((i) => i._id);
  } else if (projectCategories.value.length > 0) {
    // 如果只选择了项目分类而没有选择具体项目，根据分类获取项目ID
    try {
      const cateIds = projectCategories.value.map((category) => category._id);
      const { success, data } = await getProjectListByCateIds({ 
        corpId: localStorage.getItem("corpId"), 
        cateIds 
      });
      
      if (success && data && Array.isArray(data.data)) {
        const projectIds = data.data.map((project) => project._id);
        params.projectIds = projectIds;
      }
    } catch (error) {
      console.error('获取分类下的项目失败:', error);
    }
  }

  let { success, data } = await deductRecordStatistic({
    params,
  });
  if (success) {
    const { deductCount, deductProjectCount, totalDiscountedPrice } = data.data;
    count.value = [
      { key: 1, label: "今日治疗人数", count: deductCount },
      { key: 2, label: "今日治疗项目", count: deductProjectCount },
      { key: 3, label: "今日划扣业绩", count: totalDiscountedPrice?.toFixed(2), enableHidden: true, hidden: true },
    ];
  }
}
function openReception(item) {
  customer.value = item;
  searchClose();
  showBillVisible();
}

function success() {
  deductStatistic();
  getList();
}

function toPlan(row) {
  customer.value = row.customerInfo;
  openDrawer();
}

// 计算麻醉剩余时间 以分钟为单位
function getAnesthesiaRestTme(row) {
  const aenesthesiaEndTime = dayjs(row.aenesthesiaEndTime);
  const restTime = aenesthesiaEndTime.diff(dayjs(), "minute");
  return restTime > 0 ? restTime : 0;
}
function onAddCustomer() {
  newCustomerInfo.value = { relationship: "本人" };
  searchClose();
  addCustomerShow();
}
async function addSuccess(id) {
  // 根据id获取数据
  let { success, data } = await getCustomers({ _id: id });
  if (success && data && data.data.length > 0) {
    customer.value = data.data[0];
    receptionShow();
  }
}
async function toTreatMentRecord(row) {
  selectproject.value = row;
  showTreatmentRecordVisible();
}
async function getList() {
  let query = {
    page: page.value,
    pageSize: pageSize.value,
    ...searchForm.value,
  };
  if (searchForm.value.deductStatus) {
    query.deductStatus = [searchForm.value.deductStatus];
  }
  if (!isManager.value && !isAdmin) {
    query.teamId = currentTeamId.value;
  }
  if (!selectDepts.value.length && !isManager.value && !isAdmin.value) {
    query.treatmentDept_ids = belongDeptIds.value;
  }
  if (selectDepts.value.length) {
    query.treatmentDept_ids = selectDepts.value.map((i) => i._id);
  }
  loading.value = true;
  const { success, data } = await getDeductRecord(query);
  loading.value = false;
  if (success) {
    total.value = data.total;
    orderCount.value = data.list.reduce((acc, item) => {
      if (item.treatmentOrderId) acc[item.treatmentOrderId] = (acc[item.treatmentOrderId] || 0) + 1;
      return acc;
    }, {});
    list.value = data.list.map((i) => {
      return {
        ...i,
        name: i.customerInfo.name,
        mobile: i.customerInfo.mobile,
        projectNames: Array.isArray(i.projectNames) ? i.projectNames.join(" / ") : "",
        sexAge: i.customerInfo.sex && i.customerInfo.age ? `${i.customerInfo.sex} / ${i.customerInfo.age}` : `${i.customerInfo.sex || ""}${i.customerInfo.age || ""}`,
        createTimeStr: i.createTime ? dayjs(i.createTime).format("YYYY-MM-DD HH:mm") : "",
        deductStatusStr: i.deductStatus ? DeductStatus.find((j) => j.value === i.deductStatus).label : "",
        deductTreatmentStatus: i.deductStatus ? DeductTreatmentStatus.find((j) => j.value === i.deductStatus).label : "",
        treatmentTimeStr: i.treatmentTime ? dayjs(i.treatmentTime).format("YYYY-MM-DD") : "",
        deductedPriceStr: i.deductedPrice?.toFixed(2),
        deductCount: i.usageCount - i.restUsageCount,
      };
    });
  }
}
async function search() {
  page.value = 1;
  
  // 处理项目筛选逻辑
  if (projects.value.length > 0) {
    // 如果选择了具体项目，直接使用项目ID
    searchForm.value.projectIds = projects.value.map((i) => i._id);
  } else if (projectCategories.value.length > 0) {
    // 如果只选择了项目分类而没有选择具体项目，根据分类获取项目ID
    try {
      const cateIds = projectCategories.value.map((category) => category._id);
      const { success, data } = await getProjectListByCateIds({ 
        corpId: localStorage.getItem("corpId"), 
        cateIds 
      });
      
      if (success && data && Array.isArray(data.data)) {
        const projectIds = data.data.map((project) => project._id);
        searchForm.value.projectIds = projectIds;
      } else {
        delete searchForm.value.projectIds;
      }
    } catch (error) {
      console.error('获取分类下的项目失败:', error);
      delete searchForm.value.projectIds;
    }
  } else {
    delete searchForm.value.projectIds;
  }
  
  // 只有选择了套餐时才设置套餐ID
  if (packageList.value.length > 0) {
    searchForm.value.packageIds = packageList.value.map((i) => i._id);
  } else {
    delete searchForm.value.packageIds;
  }
  
  getList();
  deductStatistic();
}
function reset() {
  page.value = 1;
  searchForm.value = {};
  projects.value = [];
  projectCategories.value = [];
  packageList.value = [];
  selectDepts.value = [];
  search();
}
function recept(item) {
  consultRecord.value = item;
  show();
}
function read(item) {
  consultRecord.value = item;
  show();
}

onMounted(async () => {
  await getStaffList(true);
  getStaffBelongDept();
  await judgmentIsAdmin();
  await deductStatistic();
  getList();
});

// 开单
async function bill() {
  customer.value = {};
  searchShow();
}

function handleRowClick(row) {
  if (row.visitStatus === "visited") {
    read(row);
  } else if (row.visitStatus === "pending") {
    recept(row);
  }
}

function registration(row) {
  selectproject.value = row;
  showRegistrationVisible();
  console.log("麻醉登记", row);
}
function deductProject(row) {
  selectproject.value = row;
  showDeductVisible();
  console.log("划扣", row);
}
async function removeRecord(row) {
  await ElMessageBox.confirm("确定取消该分诊记录吗？");
  const { success, message } = await removeDeductRecord({ id: row._id });
  //更新记录
  await updateTreatmentRecordApi({ id: row.treatmentId });
  if (success) {
    getList();
    ElMessage.success("取消成功");
  } else {
    ElMessage.error(message);
  }
}
async function updateTreatmentRecordApi({ id }) {
  await updateTreatmentRecord({ id, params: { treatmentStatus: "init" } });
}
// 获取员工所在科室
function getStaffBelongDept() {
  belongDeptIds.value = memberStore().memberInfo?.deptIds || [];
}

function getCellClassName({ row, column }) {
  if (orderCount.value[row.treatmentOrderId] > 1 && column.property === "visitStatusStr") {
    return "border-l border-gray-200";
  }
  return "";
}
</script>
<style lang="scss" scoped></style>