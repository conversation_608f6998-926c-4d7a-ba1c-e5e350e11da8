import { watchDebounced } from "@vueuse/core";
import BigNumber from "bignumber.js";
import { ref } from "vue";

/**
 * 账单校验规则
 * @case 门诊账单 类型项目金额匹配，总金额匹配
 * @case 住院账单 总金额匹配即可
 */

export default function useBillMatch(bill, projects, packages) {
  const matchRes = ref({
    showTypeFee: false, // 是否显示类型金额匹配
    typeFeeMatched: true, // 账单项目是否匹配
    totalFeeMatched: false, // 是否有额外项目
    result: true,
    msg: ''
  })
  const typeStats = ref([])

  function compare() {
    // 获取账单统计信息
    const { types } = statsBill();

    // 统计所选项目以及套餐项目
    const packageProjects = Array.isArray(packages.value) ? packages.value.reduce((acc, item) => {
      // 如果套餐项目存在，则将套餐项目添加到acc数组中
      if (item.projects && Array.isArray(item.projects)) {
        return [...acc, ...item.projects]
      }
      return acc
    }, []) : [];
    const _projects = Array.isArray(projects.value) ? [...projects.value] : [];
    let allProjects = [...packageProjects, ..._projects];
    // 计算选择的套餐项目 + 项目 的各类型项目金额
    const projectTypeMap = new Map();
    let allFee = new BigNumber(0);
    allProjects.forEach(item => {
      if (!projectTypeMap.has(item.projectType)) {
        projectTypeMap.set(item.projectType, 0)
      }
      const fee = projectTypeMap.get(item.projectType);
      projectTypeMap.set(item.projectType, new BigNumber(fee).plus(Number(item.totalPrice) > 0 ? Number(item.totalPrice) : 0).toNumber());
      allFee = allFee.plus(Number(item.totalPrice) > 0 ? Number(item.totalPrice) : 0);
    })
    // 比较给类型项目的金额
    const finalTypes = new Map();
    for (let key of types.keys()) {
      const item = types.get(key);
      item.matched = item.fee === (projectTypeMap.get(key) || 0);
      finalTypes.set(key, item)
    }
    const unMatchType = [...finalTypes.values()].find(i => !i.matched);
    const typeFeeMatched = bill.value.billType === '门诊' ? !unMatchType : true; //  门诊账单校验 类型金额
    const totalFeeMatched = allFee.toNumber() == (bill.value && bill.value.totalFee ? bill.value.totalFee - 0 : 0);
    typeStats.value = [...finalTypes.values()];
    matchRes.value.showTypeFee = bill.value.billType === '门诊';
    matchRes.value.typeFeeMatched = typeFeeMatched;
    matchRes.value.totalFeeMatched = totalFeeMatched;
    matchRes.value.result = typeFeeMatched && totalFeeMatched;
    if (!typeFeeMatched && unMatchType) {
      matchRes.value.msg = `${unMatchType.name}类型金额不一致，请确认开单金额`
    } else if (!totalFeeMatched) {
      matchRes.value.msg = `总金额不匹配`
    } else {
      matchRes.value.msg = ''
    }
  }

  /**
   * his账单统计
   */
  function statsBill() {
    let projects = bill.value && Array.isArray(bill.value.itemList) ? bill.value.itemList : [];
    const types = new Map(); // 记录his账单共有多少类型的项目 以及每个类型下 项目的总额
    while (projects.length) {
      const item = projects[0];
      if (!types.has(item.doctorAdvice)) {
        types.set(item.doctorAdvice, { name: item.doctorAdvice, fee: 0 }) // 记录项目类型 
      }
      const sameProjects = projects.filter(i => i.doctorAdvice === item.doctorAdvice); // 找出相同的项目
      projects = projects.filter(i => i.doctorAdvice !== item.doctorAdvice); // 剩余的项目
      const { fee } = sameProjects.reduce((acc, cur) => { // 计算相同项目的总额 数量
        if (Number(cur.payment) > 0) {
          acc.fee = acc.fee.plus(Number(cur.payment))
        }
        return acc
      }, { fee: new BigNumber(0) })
      const stats = types.get(item.doctorAdvice);
      stats.fee = fee.toNumber();
      types.set(item.doctorAdvice, stats) // 记入项目类型的总额
    }
    return { types };
  }


  watchDebounced([bill, projects, packages], () => {
    compare()
  }, { immediate: true, debounce: 500, deep: true })

  return { matchRes, typeStats }
}