import { post } from "./axios";

async function useGroupmsg(data) {
  const res = await post("groupmsg", data);
  return res;
}

export async function createGroupmsgTaskUrl(params) {
  const res = await useGroupmsg({
    type: "createGroupmsgTask",
    params,
  });
  return res;
}

export async function addWecomMsgTemplateForTeamOrCorpUrl(params, id) {
  const res = await useGroupmsg({
    type: "addWecomMsgTemplateForTeamOrCorp",
    params,
    id,
  });
  return res;
}
export async function getWecomGroupmesgListUrl(corpId, permanentCode, page, pageSize, params) {
  const res = await useGroupmsg({
    type: "getWecomGroupmesgList",
    corpId,
    permanentCode,
    page,
    pageSize,
    params,
  });
  return res;
}

export async function addWecomMsgTemplateUrl(corpId, permanentCode, templateId) {
  const res = await useGroupmsg({
    type: "addWecomMsgTemplate",
    corpId,
    permanentCode,
    templateId,
  });
  return res;
}

export async function stopGroupmsgTaskUrl(id) {
  const res = await useGroupmsg({
    type: "stopGroupmsgTask",
    id,
  });
  return res;
}

export async function getGroupmesgByIdUrl(id) {
  const res = await useGroupmsg({
    type: "getGroupmesgById",
    id,
  });
  return res;
}

export async function getWecomGroupmsgSendResultUrl({ permanentCode, corpId, msgid, userid }) {
  const res = await useGroupmsg({
    type: "getWecomGroupmsgSendResult",
    permanentCode,
    corpId,
    msgid,
    userid,
  });
  return res;
}

export async function getCusomterGroupmsgSendResultUrl({ permanentCode, corpId, msgid, userid, customers }) {
  const res = await useGroupmsg({
    type: "getCusomterGroupmsgSendResult",
    permanentCode,
    corpId,
    msgid,
    userid,
    customers,
  });
  return res;
}

export async function getTeamMemberExecuteStateUrl({ permanentCode, corpId, teamTaskId, customers }) {
  const res = await useGroupmsg({
    type: "getTeamMemberExecuteState",
    permanentCode,
    corpId,
    teamTaskId,
    customers,
  });
  return res;
}

export async function remindGroupmsgSendUrl({ permanentCode, corpId, msgid }) {
  const res = await useGroupmsg({
    type: "remindGroupmsgSend",
    permanentCode,
    corpId,
    msgid,
  });
  return res;
}

export async function getGroupmsgMsgidsByTaskIds(data) {
  const res = await useGroupmsg({
    type: "getGroupmsgMsgidsByTaskIds",
    ...data,
  });
  return res;
}
