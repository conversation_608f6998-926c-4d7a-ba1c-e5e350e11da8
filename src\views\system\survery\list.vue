<template>
  <div v-loading="loading" class="flex flex-col h-full">
    <div flex items-center justify-between py-10 class="pb-15px flex-shrink-0 px-15px">
      <div class="text-20px" font-semibold>问卷列表</div>
      <el-form inline>
        <el-form-item>
          <el-input placeholder="输入问卷名称搜索" v-model="name" @keyup.enter="search" @input="changeName" />
        </el-form-item>
        <el-form-item label="状态：">
          <el-select v-model="enable" placeholder="请选择发布状态">
            <el-option label="全部" value="ALL" />
            <el-option v-for="opt in SurveryStatus" :label="opt.label" :value="opt.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search()">查询</el-button>
        </el-form-item>
        <el-form-item style="margin-right: 0;">
          <el-button type="primary" @click="toSurveryDetail()">新增问卷</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="flex-grow relative">
      <div class="absolute inset-0 flex">
        <div class="flex-shrink-0 w-3/10 min-w-240px max-w-320px mr-15px h-full bg-white rounded">
          <classify-list v-bind="componentProps" :checked-cate="current" :data="cateList"
            @change="changeCurrent($event)" @search="getCateList()">
            <template #prepend>
              <div class="mt-15px pl-10px text-14px text-gray-600">全部问卷（{{allCount}}）</div>
            </template>
          </classify-list>
        </div>
        <div v-if="list.length === 0" class="flex items-center h-full justify-center flex-grow">
          <empty-data :top="0" title="暂无问卷" />
        </div>
        <my-layout v-else class="flex-grow">
          <layout-main>
            <div v-for="item in list" :key="item._id" bg-fff rounded-8 mb-10 px-15 common-shadow>
              <div flex items-center justify-between py-15 border-bottom>
                <div flex items-center>
                  <div font-16 font-semibold mr-10>{{ item.name }}</div>
                </div>
                <div flex items-center>
                  <div font-14 mr-10>
                    <span color-666>发送数量：</span><span min-w-80 font-16 font-semibold pointer
                      @click="goSendRecord(item)">
                      {{item.sendCount || 0 }}
                    </span>
                  </div>
                  <div font-14>
                    <span color-666>回收数量：</span><span font-16 font-semibold pointer @click="goSendRecord(item)">
                      {{item.answerCount || 0}}
                    </span>
                  </div>
                </div>
              </div>
              <div flex items-center justify-between py-15 class="flex-wrap">
                <div flex items-center font-14 color-666 flex-shrink-0>
                  <span>创建人：</span><el-text class="mr-15" min-w-80>
                    <ww-user v-if="item.createBy" :openid="item.createBy"></ww-user>
                  </el-text>
                  <span>创建时间：</span><el-text class="mr-15">{{ item.time }}</el-text>
                  <span>发布状态 ：</span><el-text class="mr-15"
                    :type="item.status === SurveryStatusKey.enable ? 'primary' : ''">
                    {{ SurveryStatusMap[item.status] || '' }}
                  </el-text>
                  <div class="ml-15px flex items-center cursor-pointer" @click="setNewCate(item)">
                    <div>分类 ：</div>
                    <div class="min-w-40px max-w-80px truncate">{{cateMap[item.cateId]||''}}</div>
                    <el-icon class="ml-4px text-black -transform translate-y-2px">
                      <CaretBottom />
                    </el-icon>
                  </div>
                </div>
                <div v-if="item.status === SurveryStatusKey.init" flex items-center flex-shrink-0 class="ml-auto">
                  <el-text pointer class="mr-15" type="primary" @click="toggle(item._id, SurveryStatusKey.enable)"> 启用
                  </el-text>
                  <el-text pointer class="mr-15" type="primary"
                    @click="toSurveryDetail(item._id,item.cateId)">编辑</el-text>
                  <el-text pointer class="mr-15" type="danger" @click="remove(item._id, item.corpId)">删除</el-text>
                  <el-text pointer class="mr-15" type="primary" @click="copy(item)">复制</el-text>
                  <el-text pointer type="primary" @click="showDetail(item)">查看详情</el-text>
                </div>
                <div v-else-if="item.status === SurveryStatusKey.enable" flex items-center flex-shrink-0
                  class="ml-auto">
                  <el-text pointer class="mr-15" type="primary" @click="toggle(item._id, SurveryStatusKey.stop)"> 停用
                  </el-text>
                  <el-text pointer class="mr-15" type="primary" @click="copy(item)">复制</el-text>
                  <el-text pointer class="mr-15" type="primary" @click="showDetail(item)">查看详情</el-text>
                  <el-text pointer type="primary" @click="goSendRecord(item)">发送记录</el-text>
                </div>
                <div v-else-if="item.status === SurveryStatusKey.stop" flex items-center flex-shrink-0 class="ml-auto">
                  <el-text pointer class="mr-15" type="primary" @click="toggle(item._id, SurveryStatusKey.enable)"> 启用
                  </el-text>
                  <template v-if="item.sendCount === 0">
                    <el-text pointer class="mr-15" type="primary" @click="toSurveryDetail(item._id,item.cateId)"> 编辑
                    </el-text>
                    <el-text pointer class="mr-15" type="danger" @click="remove(item._id, item.corpId)">删除</el-text>
                  </template>
                  <el-text pointer class="mr-15" type="primary" @click="copy(item)">复制</el-text>
                  <el-text pointer class="mr-15" type="primary" @click="showDetail(item)">查看详情</el-text>
                  <el-text pointer type="primary" @click="goSendRecord(item)">发送记录</el-text>
                </div>
              </div>
            </div>
          </layout-main>
          <layout-item>
            <pagination :bgWhite="false" :totalRow="total" :pageSize="pageSize" :currentPage="currentPage"
              @handle-size-change="onSizeChange" @handle-current-change="onCurrentChange" />
          </layout-item>
        </my-layout>
      </div>
    </div>
  </div>
  <survery-dialog :data="currentSurvery" :visible="visible" @close="visible = false" />
  <classify-modal :id="currentSurvery.cateId" :cateTree="cateTree" :visible="cateVisible"
    :update-method="setSurveryNewCate" :width="cateModalWidth" @close="closeCateModal" />
  <copy-dialog :visible="copyVisible" :data="copySurvery" @close="copyVisible = false"
    @reload="getList()"></copy-dialog>
</template>
<script setup>
import { ref, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import dayjs from 'dayjs';
import { SurveryStatus, SurveryStatusMap, SurveryStatusKey } from '@/baseData';
import { getList as getSurveyList, setSurveryStatus, removeSurvery, addSurveryCate, updateSurveryCate, sortSurveryCate, deleteSurveryCate, getSurveryCateList, getSurveryCount, setSurveryCate } from '@/api/survery';
import useClassifyList from '@/components/classify-list/useClassifyList';
import useModal from "@/hooks/useModal";

import CopyDialog from './copy-dialog.vue';
import EmptyData from "@/components/empty-data.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import SurveryDialog from './dialog.vue'
import WwUser from '@/components/ww-user/index.vue';
import classifyList from '@/components/classify-list/classify-list.vue';
import classifyModal from '@/components/classify-list/classify-modal.vue';

const router = useRouter();
const name = ref("");
const enable = ref("ALL");
const list = ref([]);
const total = ref(0);
const pageSize = ref(10);
const currentPage = ref(1);
const loading = ref(false);

const options = {
  add: addSurveryCate,
  remove: deleteSurveryCate,
  update: updateSurveryCate,
  sort: sortSurveryCate,
  getList: getSurveryCateList,
  callback: getList,
  getTotal: getSurveryCount,
  loading: loading
}

const { cateList, cateTree, current, total: allCount, getCateList, changeCurrent, componentProps } = useClassifyList(options);
const cateMap = computed(() => cateList.value.reduce((acc, cur) => {
  acc[cur._id] = cur.label;
  return acc;
}, {}))

function changeName(e) {
  if (e === '') search();
}
function onSizeChange(e) {
  pageSize.value = e;
  getList();
}
function onCurrentChange(e) {
  currentPage.value = e;
  getList();
}

let surveryName = ''
let status = ''
function search() {
  surveryName = name.value.trim();
  status = enable.value === 'ALL' ? '' : enable.value;
  currentPage.value = 1;
  getList();
}

async function getList() {
  loading.value = true;
  if (current.value._id) {
    const childrenIds = Array.isArray(current.value.childrenIds) ? [current.value._id, ...(current.value.childrenIds || [])] : [current.value._id];
    const { success, message, data = {} } = await getSurveyList(currentPage.value, pageSize.value, surveryName, status, true, childrenIds)
    const { list: tableData = [], total: count = 0 } = data;
    list.value = tableData.map(i => ({ ...i, time: dayjs(i.createTime).format('YYYY-MM-DD HH:mm') }));
    total.value = count;
    loading.value = false;
    if (!success) ElMessage.error(message)
  } else {
    list.value = [];
    total.value = 0
  }
}
// getList();

const visible = ref(false);
const currentSurvery = ref({});
function showDetail(item) {
  currentSurvery.value = item;
  visible.value = true
}

function goSendRecord(item) {
  router.push({ name: 'SURVERYANSWER', params: { id: item._id } })
}

function toSurveryDetail(id = '', cateId) {
  cateId = cateId || (current.value && current.value._id ? current.value._id : '')
  router.push({ name: 'SURVERYDETAIL', params: { id }, state: { cateId } })
}

async function toggle(_id, status) {
  const { success, message } = await setSurveryStatus(_id, status);
  if (success) {
    ElMessage.success(message);
    getList()
  } else {
    ElMessage.error(message);
  }
}

async function remove(_id, corpId) {
  await ElMessageBox.confirm('确定删除该问卷吗?', '提示', { type: 'warning' });
  const { success, message } = await removeSurvery(_id, corpId);
  if (success) {
    ElMessage.success(message);
    getList()
  } else {
    ElMessage.error(message);
  }
}

const copySurvery = ref({});
const copyVisible = ref(false);
function copy(item) {
  copySurvery.value = item;
  copyVisible.value = true;
}

const { close: closeCateModal, show, visible: cateVisible, width: cateModalWidth } = useModal(560); // 选择客户弹窗

function setNewCate(item) {
  currentSurvery.value = item;
  show()
}
async function setSurveryNewCate(cateId) {
  const { success, message } = await setSurveryCate({
    corpId: localStorage.getItem("corpId"),
    _id: currentSurvery.value._id,
    cateId
  })
  if (success) getList()
  return { success, message }

}

</script>
<style scoped>
.w-200 {
  width: 200px;
}


[min-w-80] {
  display: inline-block;
  min-width: 40px;
}

.mr-15 {
  margin-right: 15px;
}

:deep(.el-form--inline .el-form-item) {
  margin-bottom: 0;
  margin-right: 10px;
}
</style>
