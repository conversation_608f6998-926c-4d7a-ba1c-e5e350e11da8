<template>
  <div class="h-full flex flex-col">
    <number-bar ref="numberBarRef"></number-bar>    <div class="flex-shrink-0 overflow-hidden mt-10px">
      <div class="h-40px rounded bg-white flex items-center">
        <!-- <div v-if="team"
          class="flex-shrink-0 items-baseline pl-10px pr-30px relative h-30px leading-30px cursor-pointer bg-blue-500 rounded-tl-15px rounded-br-15px truncate text-14px text-white max-w-200px">
          <span @click="teamVisible = true">{{ team.name }}</span>
          <el-dropdown class="absolute right-10px top-1/2 text-16px transform -translate-y-1/2"
            trigger="click">
            <el-icon class="text-white text-16px">
              <Switch />
            </el-icon>
            <template #dropdown>
              <el-dropdown-menu>
                <el-scrollbar wrap-style="max-height:60vh">
                  <el-dropdown-item v-for="team in showTeams" :key="team.teamId" @click="selectTeam(team)">
                    {{ team.name }}
                  </el-dropdown-item>
                </el-scrollbar>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div> -->
        
        <!-- 回访日期和提示 -->
        <div class="flex items-center ml-15px flex-grow">
          <date-range-filter v-model:dates="followUpDate" label="回访日期" :text="datesText" :width="240" />
          <div class="ml-15px text-red-500 text-14px">
            超7天未处理,待办自动失效。
          </div>
        </div>
        
        <div class="flex items-center ml-15px">
          <div class="flex bg-gray-100 rounded-full p-2px">
            <div 
              class="px-16px py-6px text-13px font-medium cursor-pointer transition-all duration-200 rounded-full"
              :class="activeName === 'todo' ? 'bg-blue-500 text-white shadow-sm' : 'text-gray-600 hover:text-blue-500 hover:bg-white'"
              @click="activeName = 'todo'"
            >
              待办
            </div>
            <div 
              class="px-16px py-6px text-13px font-medium cursor-pointer transition-all duration-200 rounded-full"
              :class="activeName === 'groupMsg' ? 'bg-blue-500 text-white shadow-sm' : 'text-gray-600 hover:text-blue-500 hover:bg-white'"
              @click="activeName = 'groupMsg'"
            >
              群发任务
            </div>
          </div>
        </div>
      </div>
    </div><div class="flex-grow relative">
      <div class="inset-0 absolute">
        <todo v-if="activeName === 'todo'" :team="team" :todo-status="todoStatus" v-model:follow-up-date="followUpDate" @change="handleTodoChange">
        </todo>
        <group-msg v-else-if="activeName === 'groupMsg'" />
      </div>
    </div>
  </div>
  <teams-modal :team-id="team ? team.teamId : ''" :teams="showTeams" :visible="teamVisible"
    @close="teamVisible = false"></teams-modal>

</template>
<script setup>
import { computed, onMounted, ref, watch } from 'vue';
import { storeToRefs } from "pinia";
import { teamStore } from "@/store/team";
import { DateRangeFilter } from "@/components/filter-bar";
import dayjs from 'dayjs';

import GroupMsg from './components/group-msg.vue';
import numberBar from './components/number-bar.vue';
import teamsModal from "./components/teams-modal.vue";
import todo from './components/todo.vue';

const activeName = ref('todo'); // 默认进入待办页面
const { teams, currentTeam, allTeams, isAdmin } = storeToRefs(teamStore());
const showTeams = computed(() => isAdmin.value ? allTeams.value : teams.value);
const team = ref();
const teamVisible = ref(false);

// 新增状态管理
const todoStatus = ref('pending'); // 默认"待处理"
const followUpDate = ref([
  dayjs().subtract(7, 'day').format('YYYY-MM-DD'), // 7天前
  dayjs().format('YYYY-MM-DD') // 今天
]); // 改为数组，存储日期范围

// 日期范围显示文本
const datesText = computed(() => followUpDate.value && followUpDate.value.length > 0 ? followUpDate.value.join(" ~ ") : "");

function selectTeam(t) {
  team.value = t
}

// 处理日期变化
function handleDateChange(dateRange) {
  // 处理日期范围变化逻辑
  console.log('回访日期范围变化:', dateRange);
}

// 处理状态变化
function handleStatusChange(status) {
  console.log('待办状态变化:', status);
}

onMounted(() => {
  const cacheTeamId = localStorage.getItem('work-space-team-id');
  let t = showTeams.value.find(team => team.teamId === cacheTeamId);
  if (t) team.value = t;
  else {
    t = showTeams.value.find(team => currentTeam.value && team.teamId === currentTeam.value.teamId);
    team.value = t || showTeams.value[0];
  }
})

const numberBarRef = ref()
function handleTodoChange() {
  numberBarRef.value.reloadTodo()
}

watch(team, val => localStorage.setItem('work-space-team-id', val && val.teamId ? val.teamId : ''))
</script>
