<template>
  <div class="h-screen w-screen fixed inset-0 z-100">
    <my-layout bg-fff>
      <layout-item>
        <div px-15 border-bottom class="py-6px">
          <el-input v-model="name" placeholder="请搜索诊断名称" />
        </div>
      </layout-item>
      <layout-main v-loading="loading" :scroll="false">
        <el-scrollbar height="100%" ref="scrollRef" @scroll="handleScroll($event)">
          <div v-for="i in list" :key="i.label" class="px-15px py-10px" font-14 flex items-center border-bottom
            @click="toggle(i.label)">
            <div flex-grow class="w-0 truncate">{{ i.label }}</div>
            <div flex-shrink-0 class="ml-10px text-18px text-blue-500"
              :class="selection.some(v => v === i.label) ? '' : 'opacity-0'">
              <el-icon><Select /></el-icon>
            </div>
          </div>
        </el-scrollbar>
      </layout-main>
      <layout-item>
        <div flex items-center px-15 py-10 common-shadow--r>
          <el-button plain class="flex-grow mr-15px" @click="close()">取消</el-button>
          <el-button class="flex-grow" type="primary" @click="confirm()">确定</el-button>
        </div>
      </layout-item>
    </my-layout>

  </div>
</template>
<script setup>
import { computed, onMounted, ref, watch } from 'vue';
import { watchDebounced } from "@vueuse/core";
import { storeToRefs } from 'pinia';
import { getPageDisease } from '@/api/knowledgeBase';
import useElScrollbar from '@/hooks/useElScrollbar';
import { memberStore } from "@/store/member";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import { ElMessage } from 'element-plus';

const emits = defineEmits(['close', 'confirm']);
const props = defineProps({ value: { default: () => [] } })

const store = memberStore();
const { corpInfo } = storeToRefs(store);
const corpDiseases = computed(() => Array.isArray(corpInfo.value.diseases) ? corpInfo.value.diseases.map(i => ({ label: i, value: i })) : []);

const name = ref('');
const page = ref(1);
const more = ref(false);
const loading = ref(false)
const list = ref([...corpDiseases.value]);
const selection = ref([]);
onMounted(() => selection.value = Array.isArray(props.value) ? props.value : []);

watchDebounced(name, () => (page.value = 1) && getList());

async function getList() {
  if (loading.value) return;
  loading.value = true;
  const keyword = name.value.trim();
  const res = await getPageDisease(keyword, page.value);
  const diseases = res.data && Array.isArray(res.data.list) ? res.data.list.map(i => ({ label: i.diseaseName, value: i.code })).filter(i => i.label !== keyword) : [];
  const pages = res.data && res.data.pages ? res.data.pages : 0;
  more.value = pages > page.value;
  if (page.value === 1) {
    const matchCorpDisease = keyword ? corpDiseases.value.filter(i => i.label !== keyword && i.label.includes(keyword)) : corpDiseases.value;
    list.value = [{ label: keyword }, ...(matchCorpDisease.filter(i => !diseases.some(b => b.label === i.label)))];
  }
  list.value.push(...diseases);
  loading.value = false;
}

const scrollRef = ref();
const { atBottom, handleScroll } = useElScrollbar(scrollRef, 0);
watch(atBottom, n => {
  if (n) loadmore();
})

function loadmore() {
  if (more.value && !loading.value) {
    page.value += 1;
    getList()
  }
}

function toggle(label) {
  if (selection.value.some(v => v === label)) {
    selection.value = selection.value.filter(i => i !== label)
  } else if (selection.value.length < 10) {
    selection.value.push(label)
  } else {
    ElMessage.info('最多选择十个')
  }
}


function close() {
  emits('close')
}
function confirm() {
  emits('confirm', [...selection.value]);
  close()
}
</script>
<style lang="scss" scoped></style>
