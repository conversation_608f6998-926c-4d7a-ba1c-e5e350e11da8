import { markRaw, ref, onMounted } from "vue";
import { watchDebounced, useWindowSize } from "@vueuse/core";
import echarts from "@/utils/echarts";

function getOptions(datas = []) {
  // let datas = [{ name: '市场活动', value: 4288 }, { name: '员工推荐', value: 1567 }, { name: '客户推荐', value: 2255 }, { name: '其他', value: 3676 }]

  let colors = ["#03D6EF", "#1276FF", "#7114FF", "#F83818", "#FFC513"];
  const option = {
    grid: {
      top: 0, // 图表距离容器顶部的距离
      bottom: 0, // 图表距离容器底部的距离
      left: 0,
      right: 0,
    },
    tooltip: {
      trigger: "item",
      triggerOn: "click",
      formatter: "{b}: {c}人",
    },
    legend: {
      orient: "vertical",
      bottom: "20%",
      right: 0,
      itemGap: 4,
      itemWidth: 4,
      itemHeight: 4,
      borderRadius: 4,
      // 使用回调函数
      formatter: function (name) {
        const text = typeof name === 'string' && name.length > 4 ? name.slice(0, 4) + '...' : name;
        var data = option.series[0].data;
        var tarValue;
        for (var i = 0, l = data.length; i < l; i++) {
          if (data[i].name == name) {
            tarValue = data[i].value;
          }
        }
        return "{a|" + text + "}" + " " + "{b|" + tarValue + "人 }";
      },
      textStyle: {
        fontSize: 14,
        lineHeight: 14,
        color: "#fff",
        rich: {
          a: {
            width: 70,
            fontSize: 14
          },
          b: {
            width: 90,
            fontSize: 14
          },
        },
      },
    },
    color: colors,
    series: [
      {
        type: "pie",
        // roseType: 'area',
        // radius: ['15', '60'],
        avoidLabelOverlap: false,
        top: "5%",
        left: "-58%",
        label: {
          show: false,
          // normal: {
          //   position: 'inner',
          //   formatter: '{d}%',

          //   textStyle: {
          //     color: '#fff',
          //     fontSize: 8
          //   }
          // }
        },

        labelLine: {
          show: false,
        },
        data: datas,
      },
    ],
  };
  return option;
}

export default function useRoseChart(chartRef, data) {
  const chart = ref(null);
  const { width, height } = useWindowSize();

  function setChart() {
    chart.value = markRaw(echarts.init(chartRef.value));
  }

  function paint(repaint = false) {
    if (chartRef.value && !chart.value) {
      setChart();
      repaint = false;
    }
    if (!chart.value) return;
    const option = getOptions(data);
    chart.value.setOption(option, repaint);
  }
  // onMounted(paint);
  watchDebounced(
    [width, height],
    () => {
      chart.value && chart.value.resize();
    },
    { debounce: 500, maxWait: 1500 }
  );
  return paint();
}
