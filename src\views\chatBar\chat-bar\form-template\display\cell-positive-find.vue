<template>
  <div v-for="(i,idx) in list" :key="idx" class="pl-15px" color-normal>
    <div class="leading-24px text-14px"> {{ idx+1 }}、{{ i.category }}</div>
    <div class="text-14px leading-24px whitespace-pre-wrap"> {{ i.opinion }}</div>
  </div>
</template>
<script setup>
import { computed } from 'vue';
const props = defineProps({
  form: { type: Object, default: () => ({}) },
  title: { type: String, default: '' },
})
const list = computed(() => props.form && props.title && Array.isArray(props.form[props.title])) ? props.form[props.title].filter(i => i.category || i.opinion) : []
</script>
