<template>
  <el-dialog v-model="props.visible" title="绑定档案" draggable :width="520" @close="cancel">
    <div v-if="list.length > 0" class="box-content" v-loading="loading">
      <div class="box-cell" v-for="item in list" :key="item.idCard">
        <div class="box-cell-name">
          <div class="flex algin-center">
            <div class="pr-10px t_1">{{ item.name }}</div>
            <div v-if="item.mobile">|</div>
            <div class="pl-10px">{{ item.mobile }}</div>
          </div>
          <div class="mt-10px">
            <span>身份证号:</span>
            <span class="pl-5px">{{ item.idCard }}</span>
          </div>
          <div class="mt-10px">
            <span>档案编号:</span>
            <span class="pl-5px">{{ item.customerNumber }}</span>
          </div>
        </div>
        <el-button type="primary" class="ml-10px" @click="bindCustomer(item)">绑定</el-button>
      </div>
    </div>
  </el-dialog>
</template>
    <script setup>
import { ref } from "vue";
import { updateMember } from "@/api/member";
import { ElMessage, ElMessageBox } from "element-plus";
const props = defineProps({
  visible: { type: Boolean, defautl: false },
  list: {
    type: Array,
    default: [],
  },
  customer: {
    type: Object,
    default: () => ({}),
  },
  memberId: {
    type: String,
    default: "",
  },
});
let loading = ref(false);
const emit = defineEmits(["cancel", "bindCustomerSuccess"]);
function cancel() {
  emit("cancel");
}

async function bindCustomer(item) {
  loading.value = true;
  item.isConnectHis = true;
  let { phone1, phone2, phone3 } = item;
  if (props.customer.mobile) {
    if (!phone1) {
      item.phone1 = props.customer.mobile;
    } else if (!phone2) {
      item.phone2 = props.customer.mobile;
    } else if (!phone3) {
      item.phone3 = props.customer.mobile;
    }
  }
  const { success, message } = await updateMember(props.memberId, item);
  loading.value = false;
  if (success) {
    ElMessage.success("档案绑定成功");
    emit("bindCustomerSuccess");
  } else {
    ElMessage.error(message);
  }
}
</script>
    <style scoped lang="scss">
.box-content {
  height: 300px;
  overflow-y: auto;
  position: relative;
  .box-cell {
    display: flex;
    justify-content: space-between;
    padding: 20px 20px 10px 20px;
    align-items: center;
    border-bottom: 1px solid #eee;
    .box-cell-name {
      .t_1 {
        color: #000;
        font-weight: 600;
        align-items: center;
      }
      .t_2 {
        font-size: 14px;
      }
    }
    .box-cell-radio {
      width: 18px;
      height: 18px;
      cursor: pointer;
    }
  }
}
</style>