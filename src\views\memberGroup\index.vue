<template>
  <div flex h-full>
    <div h-full class="flex-shrink-0 mr-15px w-1/2 max-w-320px">
      <corp-group-side :isAdmin="isAdmin" :current="current" ref="groupSideRef" class="flex-shrink-0" @change="changeGroup($event)" />
    </div>
    <div v-if="current && current.noGroup" class="h-full w-0 flex-grow">
      <member-table :group="current" :groupList="dropList" @showPlan="toPlan" />
    </div>
    <div v-else-if="current && current._id" v-loading="loading" class="h-full w-0 flex-grow flex flex-col">
      <div class="flex-shrink-0 mb-15px">
        <div color-normal class="p-15px text-14px bg-white truncate flex align-center">
          <span class="font-semibold text-15px">分组名称：</span>
          <span>{{ current.groupName }}</span>
          <el-popover v-if="current.description" placement="bottom" :width="200" :content="current.description" trigger="click" popper-class="max-w-320px">
            <template #reference>
              <img src="@/assets/gantanhaozhong.png" class="w-20px h-20px ml-5px pointer" />
            </template>
          </el-popover>
          <span class="ml-30px font-semibold text-15px">分组人数：</span>
          <span>{{ total }}</span>
          <span class="ml-30px font-semibold text-15px">绑定计划：</span>
          <el-tooltip v-if="current.managementPlan && current.managementPlan.planName" :content="current.managementPlan.planName" placement="bottom" effect="light">
            <div>
              <span>{{ current.managementPlan.planName }}</span>
              <span class="ml-5px inline-block rounded bg-blue-500 text-12px py-4px px-8px text-white">机构</span>
            </div>
          </el-tooltip>
        </div>
      </div>
      <div class="flex items-center px-15px py-10px bg-white">
        <div v-if="current.groupType === 'corp'" class="flex items-center mr-10px">
          <el-select v-model="serviceTeamId" placeholder="请选择团队" @change="search">
            <template #prefix>服务团队：</template>
            <el-option v-for="i in allTeams" :key="i.teamId" :value="i.teamId" :label="i.name" />
          </el-select>
        </div>
        <el-input v-model="keyword" :prefix-icon="Search" :placeholder="searchType === 'name' ? '请输入姓名' : '请输入手机号'" class="w-280px" @keyup.enter="search()">
          <template #prepend>
            <el-select v-model="searchType" class="w-100px" placeholder="请选择" @keyup.enter="search()">
              <el-option label="姓名" value="name"></el-option>
              <el-option label="手机号" value="mobile"></el-option>
            </el-select>
          </template>
        </el-input>
      </div>
      <div class="flex-grow relative">
        <div class="absolute inset-0">
          <el-table v-if="current.groupType === 'corp'" border stripe height="100%" :data="tableList">
            <el-common-column fixed prop="teamName" label="服务团队" :min-width="160">
              <template #default="{ row: { teamNames } }">
                <div>{{ teamNames }}</div>
              </template>
            </el-common-column>
            <el-common-column fixed prop="name" label="姓名" :min-width="100">
              <template #default="{ row: { name, _id } }">
                <div @click.stop="toCustomerDetail(_id)" main-color class="pointer">{{ name }}</div>
              </template>
            </el-common-column>
            <el-common-column prop="sexAge" label="性别/年龄" :min-width="100" />
            <el-common-column prop="mobile" label="联系方式" :min-width="120">
              <template #default="{ row: { mobile } }">
                <span>{{ maskPhone(mobile, 3, 4) }}</span>
              </template>
            </el-common-column>
            <el-common-column prop="externalUserId" label="微信联系人" :min-width="100">
              <template #default="{ row: { _id, externalUserId } }">
                <wx-contact-td :customerId="_id" :externalUserId="externalUserId" @change="getList" />
              </template>
            </el-common-column>
          </el-table>
          <el-table v-else border stripe height="100%" :data="tableList">
            <el-common-column fixed prop="name" label="姓名" :min-width="100">
              <template #default="{ row: { name, _id } }">
                <div @click.stop="toCustomerDetail(_id)" main-color class="pointer">{{ name }}</div>
              </template>
            </el-common-column>
            <el-common-column prop="sexAge" label="性别/年龄" :min-width="100" />
            <el-common-column prop="mobile" label="联系方式" :min-width="120">
              <template #default="{ row: { mobile } }">
                <span>{{ maskPhone(mobile, 3, 4) }}</span>
              </template>
            </el-common-column>
            <el-common-column prop="externalUserId" label="微信联系人" :min-width="100">
              <template #default="{ row: { _id, externalUserId } }">
                <wx-contact-td :customerId="_id" :externalUserId="externalUserId" @change="getList" />
              </template>
            </el-common-column>
            <el-table-column fixed="right" class-name="el-table-fixed-column--shadow" label="操作" width="200">
              <template #default="{ row }">
                <el-dropdown class="switch-el-dropdown" trigger="click">
                  <el-text pointer type="primary" class="mr-10px mt-4px">转组</el-text>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item v-for="item in dropListMap[row._id]" :key="item._id" :class="row.groupIds && row.groupIds[0] == item._id ? 'hidden' : ''" @click="transferGroup(row, item)">
                        <span class="leading-20px">{{ item.label }}</span>
                        <span v-if="item.showCorpTag" class="leading-20px bg-blue-500 text-12px text-white ml-5px rounded px-5px">机构</span>
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
                <el-text v-if="current && current.groupType === 'team' && current.teamId" class="cursor-pointer mr-5px" type="primary" @click.stop="toPlan(row)">回访计划</el-text>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div class="flex-shrink-0">
        <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize" @handle-current-change="changePage" />
      </div>
    </div>
    <div v-else class="h-full flex flex-grow flex-col items-center justify-center">
      <empty-data :top="0" title="请选择分组" />
    </div>
    <plan-modal :visible="visible" @close="visible = false" @executePlan="addPlan" :customer="customer" />
  </div>
  <component v-if="ManagePlanDrawer" :is="ManagePlanDrawer" :customer="customer" :teamId="current.teamId" :visible="drawerVisible" @close="closeDrawer" />
  <customer-detail v-if="customerDetailVisible" :visible="customerDetailVisible" @close="customerDetailVisible = false" :customerId="customerId" title="客户详情" @update="getList" customerType="corpCustomer" />
</template>
<script setup>
import { computed, ref } from "vue";
import { storeToRefs } from "pinia";
import { ElMessageBox, ElMessage } from "element-plus";
import dayjs from "dayjs";
import { addGroupIdForMember as addGroupIdForMemberUrl, getGroupsByCorpGroupId } from "@/api/group";
import { searchCorpCustomer } from "@/api/member";
import { executeManagementPlanTodo } from "@/api/todo";
import useManagePlanDrawer from "@/composables/useManagePlanDrawer";
import useElPagination from "@/hooks/useElPagination";
import { memberStore } from "@/store/member";
import { teamStore } from "@/store/team";
import { maskPhone, getRandomStr } from "@/utils";
import CorpGroupSide from "./corp-group-side.vue";
import customerDetail from "@/views/member/detail/index.vue";
import elCommonColumn from "@/components/el-common-column/el-common-column.vue";
import EmptyData from "@/components/empty-data.vue";
import MemberTable from "./member-table";
import pagination from "@/components/pagination/pagination.vue";
import planModal from "./plan-modal";
import wxContactTd from "@/components/wx-contact-td/wx-contact-td.vue";

const customer = ref({});
const customerId = ref("");
const current = ref({});
const customerDetailVisible = ref(false);
const keyword = ref("");
const loading = ref(false);
const searchType = ref("name");
const serviceTeamId = ref("");
const total = ref(0);
const tableData = ref([]);
const dropList = ref([]);
const corpGroupMap = ref({});
const { currentTeam, allTeams, isAdmin } = storeToRefs(teamStore());
const { memberInfo, teams } = storeToRefs(memberStore());
const { page, pageSize, changePage, changeSize } = useElPagination(getList);
const { visible: drawerVisible, ManagePlanDrawer, openDrawer, closeDrawer } = useManagePlanDrawer();
const params = ref({});
const currentGroupId = ref("");
const currentPlan = ref({});
const targetTeam = ref({});
const visible = ref(false);
const teamNameMap = computed(() => {
  return allTeams.value.reduce((acc, cur) => {
    acc[cur.teamId] = cur.name;
    return acc;
  }, {});
});
const tableList = computed(() => {
  return tableData.value.map((item) => {
    const row = { ...item };
    if (typeof item.teamId === "string") {
      row.teamNames = teamNameMap.value[item.teamId] || "";
    } else if (Array.isArray(item.teamId)) {
      row.teamNames = item.teamId
        .map((id) => teamNameMap.value[id])
        .filter(Boolean)
        .join(",");
    }
    return row;
  });
});
const dropListMap = computed(() => {
  return tableData.value.reduce((acc, cur) => {
    const groupIds = Array.isArray(cur.groupIds) ? cur.groupIds : [];
    acc[cur._id] = dropList.value.filter((i) => !groupIds.includes(i._id));
    return acc;
  }, {});
});

async function changeGroup({ group: data, options }) {
  current.value = data;
  dropList.value = options;
  const query = {};
  if (data.groupType === "corp") {
    const teamGroupIds = corpGroupMap[data._id] || (await getTeamGroupIds(data._id));
    if (!corpGroupMap[data._id] && teamGroupIds.length) {
      corpGroupMap[data._id] = teamGroupIds;
    }
    query.groupIds = teamGroupIds;
  } else {
    query.teamId = data.teamId;
    if (data.noGroup) {
      query.hasGroup = "NO";
    } else {
      query.groupIds = [data._id];
    }
  }
  params.value = query;
  changePage(1);
}

function search() {
  changePage(1);
}

async function getList() {
  if (loading.value) return;
  loading.value = true;
  const query = {
    corpId: memberInfo.value.corpId,
    userId: memberInfo.value.userid,
    page: page.value,
    pageSize: pageSize.value,
    showHasPlan: "YES",
    ...params.value,
  };
  if (keyword.value.trim()) {
    query[searchType.value] = keyword.value;
  }
  if (current.value.groupType === "corp" && serviceTeamId.value) {
    query.teamId = [serviceTeamId.value];
  }
  const { data } = await searchCorpCustomer(query);
  loading.value = false;
  total.value = data.total;
  tableData.value = Array.isArray(data.list)
    ? data.list.map((i) => ({
        ...i,
        sexAge: i.sex && i.age ? `${i.sex} / ${i.age}` : `${i.sex || ""}${i.age || ""}`,
        mobile: maskPhone(i.mobile),
      }))
    : [];
}

async function transferGroup(item, newGroup) {
  const { _id } = item;
  const newGroupId = newGroup.noGroup ? "" : newGroup._id;
  await ElMessageBox.confirm(`是否把该客户转入【${newGroup.label}】`, "提示", { type: "warning" });
  if (newGroup && newGroup.managementPlan && newGroup.managementPlan.planId) {
    try {
      await ElMessageBox.confirm(`该分组已关联回访计划 【${newGroup.managementPlan.planName}】，是否立即加入该回访计划？`, "提示", {
        type: "warning",
        confirmButtonText: "加入计划",
        cancelButtonText: "暂不加入",
      });
      await addGroupIdForMember(_id, newGroupId, current.value._id);
      executePlan(item, newGroup, current.value._id);
    } catch (error) {
      addGroupIdForMember(_id, newGroupId, current.value._id);
    }
  } else {
    addGroupIdForMember(_id, newGroupId, current.value._id);
  }
}

async function addGroupIdForMember(_id, newGroupId, groupId) {
  const { success, message } = await addGroupIdForMemberUrl(_id, newGroupId, groupId);
  if (success) {
    search();
  } else {
    ElMessage.error(message);
  }
}

function executePlan(item, newGroup, id) {
  currentGroupId.value = id;
  currentPlan.value = newGroup?.managementPlan;
  targetTeam.value = newGroup;
  customer.value = item;
  visible.value = true;
}

async function addPlan(item) {
  let { planId, planName, taskList = [] } = currentPlan.value;
  const { planExecutionTime, executorUserId } = item;
  const { _id: customerId, name: customerName, externalUserId } = customer.value;
  taskList = taskList.map((item) => {
    return {
      ...item,
      executorUserId,
      planExecutionTime: dayjs(planExecutionTime).add(item.taskTime, item.timeType).format("YYYY-MM-DD"),
    };
  });
  if (hasTemplateGroupTask(taskList)) {
    return ElMessage.warning("该档案还未绑定微信联系人，无法执行改回访计划!");
  }
  const executeTeamId = targetTeam.value.teamId;
  const { name: executeTeamName } = teams.value.find((item) => item.teamId === executeTeamId) || {};
  // 执行人为当天团队的责任人
  const params = {
    corpId: localStorage.getItem("corpId"),
    customerId: customerId,
    planId,
    planName,
    planExecutStaus: "executing", //  计划状态为执行中
    customerName: customerName,
    customerUserId: externalUserId,
    executeTeamName,
    executorUserId,
    userId: executorUserId,
    executeTeamId,
    memberPlanId: getRandomStr(),
    taskList
  };
  const { success, message } = await executeManagementPlanTodo(params);
  if (success) {
    visible.value = false;
    search();
  } else {
    ElMessage.error(message);
  }
}

function hasTemplateGroupTask(taskList) {
  return taskList.some((item) => item.executeMethod === "groupTask") && !customer.value.externalUserId;
}

async function getTeamGroupIds(corpGroupId) {
  const { data } = await getGroupsByCorpGroupId({
    id: corpGroupId,
    corpId: memberInfo.value.corpId,
  });
  return Array.isArray(data.data) ? data.data.map((i) => i._id).filter(Boolean) : [];
}

function toPlan(row) {
  customer.value = row;
  openDrawer();
}

function toCustomerDetail(id) {
  customerId.value = id;
  customerDetailVisible.value = true;
}
</script>