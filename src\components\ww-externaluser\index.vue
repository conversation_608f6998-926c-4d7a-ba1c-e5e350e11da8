<template>
  <div ref="nameRef" class="h-20px"></div>
</template>
  <script setup>
// import { jsSDKConfig } from "@/utils/jssdk";
import { nextTick, onMounted, ref } from "vue";
import { storeToRefs } from "pinia";
import { staffStore } from "@/store/staff";
import * as ww from "@/libs/@wecom/jssdk/wecom.esm.js";
const nameRef = ref(null);
const props = defineProps({
  openid: {
    type: String,
    default: "",
  },
});
onMounted(async () => {
  await nextTick();
  const factory = ww.createOpenDataFrameFactory();
  factory.createOpenDataFrame({
    el: nameRef.value, // “容器”元素，用于挂载展示组件
    template: `
    <view class="style">
       <ww-open-data  openid="{{data.openid}}" type="externalUserName"></ww-open-data>
    </view>
  `,
    data: {
      openid: props.openid,
    },
    style: `
    .style {
      height: 20px;
    }
  `,
    methods: {},
  });
});
</script>
  
  
  
  