import { post } from "./axios";
async function useSystem(data) {
  const res = await post("system", data, "CORP");
  return res;
}
export async function getMenus(corpId) {
  const res = await useSystem({
    type: "getMenus",
    corpId,
  });
  return res;
}

export async function getPlatFormTemplate() {
  const res = await useSystem({
    type: "getPlatFormTemplate",
  });
  return res;
}

export async function getMenuList(menuIdList) {
  const res = await useSystem({
    type: "getMenusByMenuIds",
    menuIdList,
  });
  return res;
}
export async function setCorpMenuConfig(data) {
  const res = await useSystem({
    type: "setCorpMenuConfig",
    ...data,
  });
  return res;
}
