<template>
  <div class="my-layout" :style="style">
    <slot></slot>
  </div>
</template>
<script setup>
import { computed } from 'vue';

const props = defineProps({
  height: { type: [String, Number], default: '100%' },
  radius: { type: Number, default: 8}
})
const style = computed(() => `height: ${typeof props.height === 'number' ? props.height + 'px' : props.height}`)
</script>
<style>
.my-layout {
  display: flex;
  flex-direction: column;
  border-radius: v-bind(props.radius);
  overflow: hidden;
  /* background-color: #f5f7f9; */
}

.my-layout__main {
  position: relative;
  flex-grow: 1;
}
.my-layout__wrapper {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

.my-layout__item {
  flex-shrink: 0;
}
</style>