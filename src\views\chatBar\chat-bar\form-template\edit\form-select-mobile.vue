<template>
  <el-form-item class="px-15px py-6px el-form-item--autoheight" :class="required ? 'is-required' : ''" :label="name"
    border-bottom>
    <el-input v-if="noteRight" :model-value="value" :placeholder="placeholder" flex-grow @update:model-value="change($event)">
      <template #append>
        <el-select class="flex-shrink-0 mr-15px" style="width: 90px;" placeholder="请选择" clearable
          :model-value="noteValue" @change="changeNote($event)">
          <el-option v-for="option in mobileRelation" :label="option.label" :value="option.label" />
        </el-select>
      </template>
    </el-input>
    <div v-else flex items-center class="w-full">
      <el-select class="flex-shrink-0 mr-15px" style="width: 90px;" placeholder="请选择" clearable :model-value="noteValue"
        @change="changeNote($event)">
        <el-option v-for="option in mobileRelation" :label="option.label" :value="option.label" />
      </el-select>
      <el-input :model-value="value" :placeholder="placeholder" flex-grow @update:model-value="change($event)" />
    </div>
  </el-form-item>
</template>
<script setup>
import { computed, inject, onMounted, toRefs } from 'vue';
import { mobileRelation } from "@/baseData";
import validate from "@/utils/validate";

const addRule = inject('addRule')
const emits = defineEmits(['change'])
const props = defineProps({
  form: { type: Object, default: () => ({}) },
  name: { type: String, default: '' },
  required: { type: Boolean, default: false },
  noteRight: { type: Boolean, default: false },
  title: { type: String, default: '' },
})
const { form } = toRefs(props)
const placeholder = computed(() => `请输入${props.name}`);
const value = computed(() => form.value[props.title] || '');
function change(value) {
  emits('change', { title: props.title, value })
}

const noteTitle = computed(() => `${props.title || ''}Note`);
const noteValue = computed(() => form.value[noteTitle.value] || '');
function changeNote(value) {
  emits('change', { title: noteTitle.value, value })
}

onMounted(() => addRule(props.title, check))

function check() {
  if (value.value && !validate.isMobile(value.value)) {
    return `${props.name}格式不正确`;
  }
  if (noteValue.value && !mobileRelation.some(i => i.label === noteValue.value)) {
    return `${props.name}：${item.value.note}无效，请重新选择`
  }
  if (props.required && !value.value) { return placeholder.value }
  if (props.required && !noteValue.value)`${props.name}：请选择关系`
  return true

}
</script>
