<template>
  <div v-if="isRead">
    {{ value && Array.isArray(value) ? item.value.join("") : value }}
  </div>
  <el-cascader v-else :model-value="value" :props="lazyProps" @update:model-value="change($event)" class="w-200px" />
</template>
<script setup>
import { watch, ref } from "vue";
import { storeToRefs } from "pinia";
import { dbStore } from "@/store/db.js";

const db = dbStore();
const { province, area } = storeToRefs(db);
const { getArea } = db;
const $emit = defineEmits(["change"]);
const props = defineProps({
  item: {
    type: Object,
    default: {},
  },
  value: {
    type: String,
    default: [],
  },
  isRead: {
    type: Boolean,
    default: false,
  },
});
function change(value) {
  $emit("change", {
    title: props.item.title,
    value,
  });
}
const lazyProps = {
  value: "name",
  label: "name",
  lazy: true,
  async lazyLoad(node, resolve) {
    const { level } = node;
    if (level === 0) {
      if (province.value.length === 0) await getArea();
      const list = province.value.map((i) => ({ ...i }));
      resolve(list);
    } else {
      const {
        data: { code },
      } = node;
      if (code) {
        if (area.value[code] === undefined) await getArea(code);
        if (Array.isArray(area.value[code])) {
          const list = area.value[code].map((i) => ({ ...i, leaf: level >= 2 }));
          resolve(list);
        } else {
          resolve([]);
        }
      }
    }
  },
};
</script>

<style></style>