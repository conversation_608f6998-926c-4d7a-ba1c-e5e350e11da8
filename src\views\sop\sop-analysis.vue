<template>
  <div v-if="!sop" v-loading="!sop" class="h-full flex flex-col items-center justify-center">
    <empty-data v-if="getFail" :top="0" title="获取sop内容失败" padding="0 0 15px" text-top="10px" :image-width="200"></empty-data>
  </div>
  <my-layout v-if="sop && !getFail" bg-fff>
    <layout-item>
      <div class="p-15px">
        <div class="flex items-center">
          <div class="text-16px font-semibold">{{ sop.sopName }}</div>
          <el-tag v-if="sop.statusName" class="ml-10px" :type="sop.status === 'executing' ? 'warning' : 'info'">{{ sop.statusName }}</el-tag>
          <!-- <div color-primary class="ml-10px text-14px cursor-pointer">
            任务详情
            <el-icon class="translate-y-2px transform">
              <DArrowRight />
            </el-icon>
          </div> -->
        </div>
        <div class="flex items-center mt-15px text-14px flex-wrap select-none">
          <el-date-picker v-model="dates" class="flex-grow-0 w-350px" type="daterange" value-format="YYYY-MM-DD" />
          <div v-for="item in dateTypes" :key="item.value" @click="toggle(item.value)" :class="active === item.value ? 'text-primary' : ''" class="ml-10px cursor-pointer">{{ item.label }}</div>
          <div class="flex-shrink-0 ml-auto">
            <span class="text-gray-500">数据更新于：{{ dayjs().format("YYYY-MM-DD") }}</span>
            <span class="text-primary ml-10px cursor-pointer" @click="refresh">刷新</span>
          </div>
        </div>
        <div class="mt-15px overflow-hidden">
          <el-row :gutter="10">
            <el-col v-for="i in count" :key="i.key" :span="6">
              <div class="flex flex-col justify-between rounded bg-gray-100 rounded px-20px py-24px h-100px">
                <div class="text-15px text-gray-500">
                  {{ i.label }}
                  <el-tooltip :content="i.des" placement="top-start" effect="light">
                    <el-icon class="cursor-pointer text-14px text-gray-400 translate-y-2px transform">
                      <QuestionFilled />
                    </el-icon>
                    <template #content>
                      <span class="text-gray-500">{{ i.des }}</span>
                    </template>
                  </el-tooltip>
                </div>
                <div class="text-primary font-semibold text-28px">{{ i.value }}</div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="mt-15px">
          <el-select v-model="teamName" placeholder="团队" class="w-350px" @change="changeTeeam" clearable>
            <el-option v-for="team in sop.executeTeams" :label="team.name" :value="team.name" />
          </el-select>
        </div>
      </div>
    </layout-item>
    <Layout-main :scroll="false">
      <el-table height="100%" :data="newList" @sort-change="handleSort($event)">
        <el-table-column width="160" label="员工姓名">
          <template #default="{ row: { userId } }">
            <ww-user :openid="userId"></ww-user>
          </template>
        </el-table-column>
        <el-table-column width="240" label="任务所属团队">
          <template #default="{ row: { teamNames } }">
            <div class="truncate">{{ teamNames }}</div>
          </template>
        </el-table-column>
        <el-table-column width="160" sortable="custom" prop="finishRate" label="任务完成率">
          <template #header>
            <el-tooltip placement="top-start" effect="light">
              <el-icon class="cursor-pointer text-14px font-normal text-gray-400 translate-y-2px transform">
                <QuestionFilled />
              </el-icon>
              <template #content><span class="text-gray-500">员工任务完成率 = 已完成 ÷ 派发任务数 * 100%</span></template>
            </el-tooltip>
            任务完成率
          </template>
          <template #default="{ row: { finishRate } }">
            <div class="truncate">{{ finishRate }}%</div>
          </template>
        </el-table-column>
        <el-table-column width="160" sortable="custom" prop="taskCount" label="任务触发数">
          <template #header>
            <el-tooltip placement="top-start" effect="light">
              <el-icon class="cursor-pointer text-14px font-normal text-gray-400 translate-y-2px transform">
                <QuestionFilled />
              </el-icon>
              <template #content><span class="text-gray-500">任务触发数</span></template>
            </el-tooltip>
            任务触发数
          </template>
          <template #default="{ row: { taskCount } }">
            <div class="truncate">{{ taskCount }}</div>
          </template>
        </el-table-column>
        <!-- <el-table-column width="160" sortable="custom" prop="taskCount" label="任务下发数">
          <template #header>
            <el-tooltip placement="top-start" effect="light">
              <el-icon class="cursor-pointer text-14px font-normal text-gray-400 translate-y-2px transform">
                <QuestionFilled />
              </el-icon>
              <template #content><span class="text-gray-500">派发给员工的任务数</span></template>
            </el-tooltip>
            任务下发数
          </template>
          <template #default="{ row: { finishCount, failedCount } }">
            <div class="truncate">{{ finishCount + failedCount }}</div>
          </template>
        </el-table-column> -->
        <el-table-column width="160" sortable="custom" prop="finishCount" label="已完成">
          <template #header>
            <el-tooltip placement="top-start" effect="light">
              <el-icon class="cursor-pointer text-14px font-normal text-gray-400 translate-y-2px transform">
                <QuestionFilled />
              </el-icon>
              <template #content><span class="text-gray-500">员工已处理的任务数</span></template>
            </el-tooltip>
            已完成
          </template>
          <template #default="{ row: { finishCount } }">
            <div class="truncate">{{ finishCount }}</div>
          </template>
        </el-table-column>
        <el-table-column width="160" sortable="custom" prop="unfinishCount" label="待执行">
          <template #header>
            <el-tooltip placement="top-start" effect="light">
              <el-icon class="cursor-pointer text-14px font-normal text-gray-400 translate-y-2px transform">
                <QuestionFilled />
              </el-icon>
              <template #content><span class="text-gray-500">员工还未处理的任务数</span></template>
            </el-tooltip>
            待执行
          </template>
          <template #default="{ row: { unexecutedCount } }">
            <div class="truncate">{{ unexecutedCount }}</div>
          </template>
        </el-table-column>
        <el-table-column width="160" sortable="custom" prop="fail" label="执行失败">
          <template #header>
            <el-tooltip v-if="failTip" placement="top-start" effect="light">
              <el-icon class="cursor-pointer text-14px font-normal text-gray-400 translate-y-2px transform">
                <QuestionFilled />
              </el-icon>
              <template #content>
                <span class="text-gray-500 whitespace-pre-wrap">{{ failTip }}</span>
              </template>
            </el-tooltip>
            执行失败
          </template>
          <template #default="{ row: { failedCount } }">
            <div class="truncate">{{ failedCount }}</div>
          </template>
        </el-table-column>
        <el-table-column></el-table-column>
        <el-table-column fixed="right" class-name="el-table-fixed-column--shadow" label="操作" width="100">
          <template #default="{ row: { userId } }">
            <el-text type="primary" class="cursor-pointer" @click="toLog(userId)">任务日志</el-text>
          </template>
        </el-table-column>
      </el-table>
    </Layout-main>
    <!-- <Layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize" @handle-current-change="changePage" />
    </Layout-item> -->
  </my-layout>
</template>
<script setup>
import { computed, onMounted, ref, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import { getSopTask } from "@/api/member";
import useDateRange from "@/hooks/useDateRange";
import useElPagination from "@/hooks/useElPagination";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import { statusMap } from "./sop-enum";
import { teamStore } from "@/store/team";
import WwUser from "@/components/ww-user/index.vue";
import { getSopTaskResultList, updateGroupMsgTaskResult } from "@/api/member";

const count = ref([
  { label: "累计触发人次", value: 0, key: "taskAllCount", des: "加入该SOP的总客户档案数" },
  // { label: "累计下发任务数", value: 0, key: "executedCount", des: "该SOP触发生成的总任务数" },
  { label: "累计完成任务数", value: 0, key: "finishCount", des: "员工已处理的总任务数" },
  { label: "任务完成率", value: 0, key: "finishRate", des: "任务完成率=累计完成任务数÷累计下发任务数*100%" },
]);
const tip = {
  todo: "生成待办单7日内未处理，状态自动变成“已过期”",
  groupSend: `群发任务执行失败的原因包括：
1、客户因已收到其他群发消息导致发送失败；
2、客户没有对应微信联系人导致发送失败；
3、因网络或者其他原因导致的发送失败。`,
};
const router = useRouter();
const route = useRoute();
const sop = ref(null);
const list = ref([]);
const getFail = ref(false);
const teamName = ref("");
const { page, pageSize, changePage, changeSize } = useElPagination(getList);
const { active, dates, list: dateTypes, toggle } = useDateRange(["all", "thisWeek", "lastWeek", "thisMonth", "lastMonth"]);
const failTip = computed(() => tip[sop.value.type] || tip["groupSend"]);
async function getList() {
  let params = {
    sopTaskId: route.params.id,
    dates: dates.value,
  };
  const { data, success } = await getSopTaskResultList(params);
  if (!success) return;
  list.value = data.data.map((i) => {
    const { executedCount = 0, unexecutedCount = 0, _id, failedCount = 0 } = i;
    i.taskCount = executedCount + unexecutedCount + failedCount;
    i.finishCount = i.executedCount || 0;
    i.userId = _id;
    i.finishRate = i.taskCount ? ((i.finishCount / i.taskCount) * 100).toFixed(2) : 0;
    i.teamNames = Array.isArray(i.executeTeamNames) ? i.executeTeamNames.join(", ") : "";
    return i;
  });
  await getResultStatistic(data.data);
}

const newList = computed(() => {
  if (teamName.value) {
    return list.value.filter((i) => i.executeTeamNames.includes(teamName.value));
  } else return list.value;
});

function handleDatesChange() {
  changePage(1);
}
async function getResultStatistic(List) {
  let item = {
    taskAllCount: 0,
    executedCount: 0,
    finishCount: 0,
    finishRate: 0,
  };
  let executedCount = 0,
    unexecutedCount = 0,
    failedCount = 0;
  List.forEach((i) => {
    executedCount += i.executedCount;
    unexecutedCount += i.unexecutedCount;
    failedCount += i.failedCount;
  });
  item.taskAllCount = executedCount + unexecutedCount + failedCount;
  item.executedCount = executedCount + failedCount;
  item.finishCount = executedCount;
  item.finishRate = item.taskAllCount ? ((item.finishCount / item.taskAllCount) * 100).toFixed(2) : 0;
  count.value = count.value.map((i) => {
    i.value = item[i.key] || 0;
    return i;
  });
}
function handleSort({ prop, order }) {
  list.value = list.value.sort((a, b) => {
    if (order === "ascending") {
      return b[prop] - a[prop];
    } else {
      return a[prop] - b[prop];
    }
  });
}
function toLog(userId) {
  router.push({ name: "SOPLOG", query: { sopTaskId: route.params.id, userId: userId, sopName: sop.value.sopName } });
}
async function getSop() {
  const { data, success, message } = await getSopTask(route.params.id);
  if (success && data.data) {
    sop.value = data.data;
    const disabled = sop.value.sopEnableStatus === "disable";
    if (data.timeStamp && dayjs(data.timeStamp).isValid()) {
      if (sop.value.executeStartTime > data.timeStamp) sop.value.status = "unexecuted";
      else if (sop.value.executeEndTime && sop.value.executeEndTime < data.timeStamp) sop.value.status = "closed";
      else if (sop.value.executeStartTime) sop.value.status = "executing";
    }
    sop.value.statusName = sop.value.status === "closed" ? statusMap[sop.value.status] : disabled ? "已停用" : statusMap[sop.value.status] || "";
  } else {
    getFail.value = true;
  }
}
onMounted(async () => {
  if (route.params.id) {
    getSop();
    if (route.params.executeMethod) await updateGroupMsgTaskResult({ sopTaskId: route.params.id });
    getList();
  } else {
    getFail.value = true;
  }
});
async function refresh() {
  if (route.params.executeMethod) await updateGroupMsgTaskResult({ sopTaskId: route.params.id });
  getList();
}
watch(dates, () => getList());
</script>
<style lang="scss" scoped>
.text-primary {
  color: var(--el-color-primary);
}
</style>
