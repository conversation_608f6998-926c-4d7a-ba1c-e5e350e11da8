import { ref } from "vue";

export default function useElPagination(cb, opt = { page: 1, pageSize: 10 }) {
  const list = ref([]);
  const more = ref(false);
  const page = ref(opt.page || 1);
  const pageSize = ref(opt.pageSize || 10);
  const callback = typeof cb === "function" ? cb : () => {};

  function changePage(val) {
    page.value = val;
    callback();
  }

  function changeSize(val) {
    pageSize.value = val;
    page.value = 1;
    callback();
  }

  function loadMore() {
    if (more.value) {
      page.value++;
      callback();
    }
  }

  return { list, more, page, pageSize, changePage, changeSize, loadMore };
}
