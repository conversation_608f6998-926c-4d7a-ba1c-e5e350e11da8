<template>
  <popover-filter :clearable="clearable" :label="label" :text="text" :width="width" @clear="clear">
    <el-radio-group v-model="modelValue">
      <div v-for="i in list" class="w-full">
        <el-radio :label="i.value" :value="i.value">{{ i.label }}</el-radio>
      </div>
    </el-radio-group>
  </popover-filter>
</template>
<script setup>
import { computed } from 'vue';
import { useVModels } from '@vueuse/core';

import PopoverFilter from './popover-filter-item.vue';

const emits = defineEmits(['update:modelValue']);
const props = defineProps({
  list: { type: Array, default: () => [] },
  label: { type: String, default: '' },
  text: { type: String, default: '' },
  width: { type: [Number, String], default: 280 },
  modelValue: { type: String, default: () => '' }
})
const { modelValue } = useVModels(props, emits);

const clear = () => modelValue.value = '';
const clearable = computed(() => Boolean(modelValue.value))
const text = computed(() => {
  if (modelValue.value) {
    const item = props.list.find(i => i.value === modelValue.value)
    return item ? item.label : ' '
  }
  return ''
})
</script>
<style lang="scss" scoped></style>
