<template>
  <Teleport to="body">
    <div v-if="visible" class="fixed inset-0 z-10">
      <my-layout bg-fff>
        <layout-main>
          <div p-15>
            <el-form label-position="top">
              <el-form-item class="is-required" label="执行日期">
                <el-date-picker v-model="workDate" format="YYYY-MM-DD" placeholder="请选择执行日期" type="date" value-format="YYYY-MM-DD" style="width: 100%" :disabled="true" />
              </el-form-item>
              <el-form-item class="is-required" label="执行时间">
                <el-time-picker v-model="workTime" format="HH:mm" placeholder="请选择执行时间" value-format="HH:mm" style="width: 100%" />
              </el-form-item>
              <el-form-item class="is-required" label="关联档案">
                <template #label>
                  <span>关联档案</span><span v-if="customerList.length === 0" class="text-red-500">(微信联系人未建档，请先建立档案)</span>
                </template>
                <el-select v-model="form.customerId" filterable style="width: 100%" placeholder="请选择关联档案">
                  <el-option v-for="item in customerList" :key="item._id" :label="item.name" :value="item._id" />
                </el-select>
              </el-form-item>
              <el-form-item class="is-required" label="所属团队">
                <el-select v-model="form.executeTeamId" filterable style="width: 100%" placeholder="请选择所属团队" @change="changeTeam">
                  <el-option v-for="item in teams" :key="item.teamId" :label="item.name" :value="item.teamId" />
                </el-select>
              </el-form-item>
              <el-form-item class="is-required" label="服务内容">
                <el-input v-model="form.taskContent" :maxlength="300" :autosize="{ minRows: 8, maxRows: 12 }" resize="none" show-word-limit type="textarea" :placeholder="placeholderName" />
              </el-form-item>
            </el-form>
          </div>
        </layout-main>
        <layout-item>
          <div class="py-12px px-15px text-center flex" common-shadow--r>
            <el-button class="w-100px flex-grow" plain @click="close()">取消</el-button>
            <el-button class="w-100px flex-grow" type="primary" :loading="loading" @click="confirm()">确定</el-button>
          </div>
        </layout-item>
      </my-layout>
      <wait-loading v-if="isActive" />
    </div>
  </Teleport>
</template>
<script setup>
import { computed, ref, watch, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import { addServiceRecord } from "@/api/todo";
import { memberStore } from "@/store/member";
import { teamStore } from "@/store/team";
import { getCustomerInSidebar } from "@/api/member";
import { getSummaryResult, getToDayAllChatRecord, batchStorageSession } from "@/api/sessionArchive";
import MyLayout, { LayoutMain } from "@/components/layout";
import waitLoading from "./wait-loading.vue";
import JSEncrypt from "jsencrypt";
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  externalUserId: {
    type: String,
    default: "",
  },
  publicKeys: {
    type: Array,
    default: () => [],
  },
});

const emits = defineEmits(["close"]);
const { memberInfo } = storeToRefs(memberStore());
const { teams } = storeToRefs(teamStore());
const isActive = ref(false);
const placeholderName = computed(() => {
  return isActive.value ? "正在智能生成服务摘要..." : "请输入服务内容";
});

const customerList = ref([]);
const form = ref({});
const loading = ref(false);
const workDate = ref(dayjs().format("YYYY-MM-DD"));
const workTime = ref("");
const customer = computed(() => {
  return customerList.value.find((i) => i._id === form.value.customerId);
});
const customerTeams = computed(() => {
  const teamIds = customer.value && Array.isArray(customer.value.teamIds) ? customer.value.teamIds : [];
  return teams.value.filter((i) => teamIds.includes(i.teamId));
});

function close() {
  emits("close");
}

async function getCustomerList() {
  const params = {
    corpId: localStorage.getItem("corpId"),
    externalUserId: props.externalUserId,
  };
  const { data } = await getCustomerInSidebar(params);
  customerList.value = Array.isArray(data.data) ? data.data : [];
}

function changeTeam(e) {
  const team = teams.value.find((i) => i.teamId === e);
  form.value.executeTeamName = team.name;
  form.value.executeTeamId = e;
}

async function confirm() {
  loading.value = true;
  if (workDate.value.trim() === "") {
    ElMessage.info("请选择执行日期");
  } else if (workTime.value.trim() === "") {
    ElMessage.info("请选择执行时间");
  } else if (!form.value.executeTeamId) {
    ElMessage.info("请选择所属团队");
  } else if (typeof form.value.taskContent !== "string" || form.value.taskContent.trim() === "") {
    ElMessage.info("请输入服务内容");
  } else {
    const params = {
      taskContent: form.value.taskContent,
      executionTime: dayjs(`${workDate.value}  ${workTime.value}`).valueOf(),
      creatorUserId: memberInfo.value.userid,
      executorUserId: memberInfo.value.userid,
      corpId: memberInfo.value.corpId,
      customerId: customer.value._id,
      customerName: customer.value.name,
      customerUserId: props.externalUserId,
      eventType: "serviceSummary",
      teamName: form.value.executeTeamName,
      executeTeamId: form.value.executeTeamId,
    };
    const { success, message } = await addServiceRecord(params);
    if (success) {
      emits("close");
      ElMessage.success(message || "新增成功");
    } else {
      ElMessage.error(message || "新增失败");
    }
  }
  loading.value = false;
}

watch(
  () => props.visible,
  async (n) => {
    if (n) {
      form.value = {
        customerId: "",
        executeTeamId: "",
        executeTeamName: "",
        taskContent: "",
      };
      getCustomerList();
      getChatUserRecord();
    }
  }
);

watch(customerList, (n) => {
  if (n && n.length === 1 && form.value.customerId !== n[0]._id) {
    form.value.customerId = n[0]._id;
  }
});

watch(customerTeams, (n) => {
  if (n && n.length === 1 && (form.value.executeTeamId !== n[0].teamId || !form.value.executeTeamId)) {
    form.value.executeTeamId = n[0].teamId;
  }
  if (form.value.executeTeamId && !n.some((i) => i.teamId === form.value.executeTeamId)) {
    form.value.executeTeamId = "";
  }
});

async function getSummary(magList) {
  let { data } = await getSummaryResult({
    magList,
  });
  if (data.response_data) {
    form.value.taskContent = data.response_data;
  }
}
async function getChatUserRecord() {
  // 先合并会话存档
  isActive.value = true;
  await batchStorageSession();
  const { success, data, message } = await getToDayAllChatRecord({ customerUserId: props.externalUserId, memberUserId: localStorage.getItem("userId") });
  if (success && data.data.length > 0) {
    const magList = data.data.map((item) => {
      return {
        msgid: item.msgid,
        encrypt_info: {
          secret_key: RSA_Decrypt(item),
        },
      };
    });
    await getSummary(magList);
  }
  isActive.value = false;
}
// 解密操作：先 Base64 解码，再用私钥解密
function RSA_Decrypt(item) {
  // Base64 解码
  let key = props.publicKeys.find((i) => i.publicKeyVersion === item.publicKeyVersion);
  const privateKey = key ? key.privateKey : "";
  // 使用 RSA 私钥解密
  let decrypt = new JSEncrypt();
  decrypt.setPrivateKey(privateKey);
  return decrypt.decrypt(item.encryptedSecretKey);
}
</script>
<style lang="scss" scoped></style>
