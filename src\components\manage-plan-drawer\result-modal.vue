<template>
  <el-dialog :model-value="visible" :width="width" :title="dialogTitle" @close="close">
    <div p-15>
      <el-input style="width: 100%;" v-model="result" maxlength="100" :autosize="{ minRows: 8, maxRows: 12 }"
        resize="none" show-word-limit type="textarea" placeholder="请填写处理结果" />
    </div>
    <template #footer>
      <div text-center>
        <el-button class="w-100px" @click="close()">取消</el-button>
        <el-button class="w-100px" :loading="loading" type="primary" @click="confirm()"> 保存 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref, watch, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { updateTaskTodoResult, setTodoStatus } from "@/api/todo";
import { storeToRefs } from "pinia";
import { memberStore } from "@/store/member";
const props = defineProps({
  data: { type: Object, default: () => ({}) },
  visible: { type: Boolean, default: false },
  width: { type: Number, default: 500 },
  isSetComplete: { type: Boolean, default: false } // 新增：标识是否来自"设为完成"按钮
})

const emits = defineEmits(['close', 'change']);
function close() {
  emits('close')
}

const { memberInfo } = storeToRefs(memberStore());

const dialogTitle = computed(() => {
  if (props.data.isCancelAction) {
    return '取消任务';
  }
  return props.isSetComplete ? '设为完成' : '编辑处理结果';
});

const result = ref('');
const loading = ref(false);
async function confirm() {
  if (result.value.trim() === '') {
    ElMessage.error('请填写处理结果');
    return;
  }
  
  loading.value = true;
    try {
    if (props.data.isCancelAction) {
      const { success, message } = await setTodoStatus(
        props.data._id, 
        'closed', 
        result.value, 
        memberInfo.value.userid
      );
      if (success) {
        ElMessage.success('任务已取消');
        emits('change');
        close();
      } else {
        ElMessage.error(message || '取消失败');
      }
    }
    // 如果是从"设为完成"按钮触发，需要同时更新状态为"treated"和结果
    else if (props.isSetComplete) {
      const { success, message } = await setTodoStatus(
        props.data._id, 
        'treated', 
        result.value, 
        memberInfo.value.userid
      );
      if (success) {
        ElMessage.success(message || '设置完成成功');
        emits('change');
        close();
      } else {
        ElMessage.error(message || '设置完成失败');
      }
    } else {
      // 如果是普通编辑结果，只更新结果内容
      const { success, message } = await updateTaskTodoResult({ 
        id: props.data._id, 
        result: result.value 
      });
      if (success) {
        ElMessage.success(message || '保存成功');
        emits('change');
        close();
      } else {
        ElMessage.error(message || '保存失败');
      }
    }
  } catch (error) {
    ElMessage.error('操作失败，请重试');
  } finally {
    loading.value = false;
  }
}

watch(() => props.visible, n => {
  if (n) {
    if (props.data.isCancelAction) {
      result.value = '已取消';
    } else {
      result.value = typeof props.data.result === 'string' ? props.data.result : '';
    }
  }
})

</script>
