import { ref } from "vue";
import { storeToRefs } from "pinia";
import { addMember, getCustomerInfoById, updateMember } from "@/api/member";
import { memberStore } from "@/store/member";

export default function useCustomerApi() {
  const loading = ref(false);
  const { memberInfo, currentTeam } = storeToRefs(memberStore());

  async function addCustomer(params) {
    if (loading.value) return;
    loading.value = true;

    const payLoad = {
      ...params,
      corpUserId: [memberInfo.value.userid],
      teamId: currentTeam.value ? [currentTeam.value.teamId] : [],
      creator: memberInfo.value.userid,
      personResponsible: [
        {
          corpUserId: memberInfo.value.userid,
          teamId: currentTeam.value ? currentTeam.value.teamId : "",
        },
      ],
    };
    const { success, data, message = "新增客户失败" } = await addMember(payLoad);
    loading.value = false;
    return { data, success, message };
  }

  async function getCustomer(id) {
    if (loading.value) return;
    loading.value = true;
    const { success, data, message = "获取客户详情失败" } = await getCustomerInfoById(memberInfo.value.userid, { _id: id });
    const customer = data && data.data[0] ? data.data[0] : null;
    loading.value = false;
    return { customer, success, message: message };
  }

  async function updateCustomer(id, params) {
    if (loading.value) return;
    loading.value = true;
    const { success, message = "更新客户信息失败" } = await updateMember(id, params);
    loading.value = false;
    return { success, message };
  }

  return { get: getCustomer, update: updateCustomer, loading };
}
