<template>
  <popover-filter-item :clearable="length > 0" label="类型" :text="text" :width="200" @clear="clear">
    <template #header>
      <div class="pb-10px"> <el-input v-model="name" placeholder="搜索条件" clearable /></div>
    </template>
    <el-checkbox v-model="allTypeChecked" label="提供患者服务" />
    <el-checkbox-group v-model="serviceTypes">
      <div v-for="item in showServiceList" :key="item.value" class="pl-15px">
        <el-checkbox :label="item.value" :value="item.value">
          <slot name="item" :item="item">{{ item.label }}</slot>
        </el-checkbox>
      </div>
    </el-checkbox-group>
    <el-checkbox v-model="allRateChecked" label="获得服务评价" />
    <el-checkbox-group v-model="serviceRates">
      <div v-for="item in showRateList" :key="item.value" class="pl-15px">
        <el-checkbox :label="item.value" :value="item.value">
          <slot name="item" :item="item">{{ item.label }}</slot>
        </el-checkbox>
      </div>
    </el-checkbox-group>
  </popover-filter-item>
</template>
<script setup>
import { computed, ref } from 'vue';
import { useVModels } from '@vueuse/core';
import { ServiceType } from '@/baseData';
import { PopoverFilterItem } from "@/components/filter-bar";

const emits = defineEmits(['update:serviceTypes', 'update:serviceRates']);
const props = defineProps({
  width: { type: [Number, String], default: 280 },
  serviceTypes: { type: Array, default: () => [] },
  serviceRates: { type: Array, default: () => [] }
})
const { serviceTypes, serviceRates } = useVModels(props, emits);
const name = ref('');

const serviceList = Object.keys(ServiceType).map(key => ({ label: ServiceType[key], value: key }));
const rateList = [{ label: '一星', value: 1 }, { label: '二星', value: 2 }, { label: '三星', value: 3 }, { label: '四星', value: 4 }, { label: '五星', value: 5 }]
const showServiceList = computed(() => {
  if (name.value.trim()) return serviceList.filter(item => item.label.includes(name.value))
  return serviceList
})
const showRateList = computed(() => {
  if (name.value.trim()) return rateList.filter(item => item.label.includes(name.value))
  return rateList
})

const allTypeChecked = computed({
  get() {
    for (let type of showServiceList.value) {
      if (!serviceTypes.value.includes(type.value)) return false
    }
    return showServiceList.value.length > 0
  },
  set(value) {
    if (value) {
      showServiceList.value.forEach(i => {
        if (!serviceTypes.value.includes(i.value)) serviceTypes.value.push(i.value)
      })
    } else {
      serviceTypes.value = serviceTypes.value.filter(val => !showServiceList.value.some(i => i.value == val))
    }
  }
})

const allRateChecked = computed({
  get() {
    for (let type of showRateList.value) {
      if (!serviceRates.value.includes(type.value)) return false
    }
    return showRateList.value.length > 0
  },
  set(value) {
    if (value) {
      showRateList.value.forEach(i => {
        if (!serviceRates.value.includes(i.value)) serviceRates.value.push(i.value)
      })
    } else {
      serviceRates.value = serviceRates.value.filter(val => !showRateList.value.some(i => i.value == val))
    }
  }
})

const length = computed(() => serviceTypes.value.length + serviceRates.value.length)
const text = computed(() => {
  if (length.value === 0) return '';
  if (length.value > 1) return `已选择${length.value}项`;
  if (serviceTypes.value.length == 1) return ServiceType[serviceTypes.value[0]];
  if (serviceRates.value.length == 1) return rateList.find(item => item.value == serviceRates.value[0]).label;
  return ''
})

function clear() {
  serviceTypes.value = []
  serviceRates.value = []
}

</script>
<style lang="scss" scoped></style>
