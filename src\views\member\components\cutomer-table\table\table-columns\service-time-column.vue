<template>
  <el-table-column label="最近服务时间" prop="serviceTime" :width="160">
    <template #default="{row}">
      <td-wrapper>{{ getTime(row) }}</td-wrapper>
    </template>
  </el-table-column>
</template>
<script setup>
import dayjs from 'dayjs';
import tdWrapper from './td-wrapper.vue';

function getTime(customer) {
  return customer.serviceTime ? dayjs(customer.serviceTime).format("YYYY-MM-DD HH:mm") : "";
}
</script>
