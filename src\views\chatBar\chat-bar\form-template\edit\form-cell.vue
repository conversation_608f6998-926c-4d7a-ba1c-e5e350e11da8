<template>
  <el-form-item class="px-15px py-6px el-form-item--autoheight" :class="required ? 'is-required' : ''" :label="name" border-bottom>
    {{ form[title] || "" }}
  </el-form-item>
</template>
<script setup>
import { computed } from "vue";

defineProps({
  form: { type: Object, default: () => ({}) },
  name: { type: String, default: "" },
  required: { type: Boolean, default: false },
  title: { type: String, default: "" },
});
</script>
<style lang="scss" scoped></style>
