import { onMounted, watch } from "vue";
import { storeToRefs } from "pinia";
import { teamStore } from "@/store/team";

export default function useTeamId(search, immediate = true) {
  const { currentTeamId } = storeToRefs(teamStore());
  watch(currentTeamId, n => {
    typeof search === 'function' && n && search()
  })
  if (immediate && typeof search === 'function') {
    onMounted(() => currentTeamId.value && search())
  }
  return currentTeamId
}