<template>
  <el-dialog :model-value="visible" title="标签" :width="width" @close="close">
    <el-scrollbar wrap-style="max-height:40vh">
      <el-form class="pt-10px pl-10px" label-suffix="：" label-position="top">
        <el-form-item v-for="(group, idx) in tags" :key="idx" class="el-form-item--autoheight mb-0 items-baseline">
          <template #label>
            <div class="w-max-90px">{{ group.groupName }}：</div>
          </template>
          <el-tag v-for="item in group.tag" :key="item.id" pointer size="large" class="tag__item" :type="selectMap[item.id] ? 'primary' : 'info'" :effect="selectMap[item.id] ? 'light' : 'plain'" @click="toggle(item, group.tag)">
            {{ item.name }}
          </el-tag>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="text-center">
        <el-button class="w-100px" @click="visible = false">取消</el-button>
        <el-button plain class="w-100px" type="primary" @click="reset()">重置</el-button>
        <el-button class="w-100px" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { configStore } from "@/store/config";
import { useVModels } from "@vueuse/core";
import { storeToRefs } from "pinia";
const { tags } = storeToRefs(configStore());

const emits = defineEmits(["close", "update:modelValue"]);
const props = defineProps({
  modelValue: { type: String, default: "" },
  width: { type: Number, default: 500 },
  visible: { type: Boolean, default: false },
});
const tagIds = computed(() => {
  return tags.value.reduce((list, { tag }) => {
    return [...list, ...(Array.isArray(tag) ? tag : [])];
  }, []);
});
const { modelValue } = useVModels(props, emits);
const selections = ref([]);
const selectMap = computed(() =>
  selections.value.reduce((acc, item) => {
    const tag = tagIds.value.find((t) => t.id === item);
    if (tag) {
      acc[tag.id] = true;
    }
    return acc;
  }, {})
);

function toggle(tag) {
  if (selectMap.value[tag.id]) {
    selections.value = selections.value.filter((id) => id !== tag.id);
  } else {
    selections.value.push(tag.id);
  }
}

function close() {
  emits("close");
}

function confirm() {
  modelValue.value = [...selections.value];
  close();
}

function reset() {
  selections.value = [];
}

watch(
  () => props.visible,
  (n) => {
    if (n) selections.value = [...modelValue.value];
  }
);
</script>
<style scoped>
.tag__item {
  margin-right: 15px;
  margin-bottom: 15px;
  padding: 6px 10px;
  min-width: 80px;
}
</style>