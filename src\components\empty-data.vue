<template>
  <div :style="topStyle"></div>
  <el-empty :image-size="props.imageWidth" :image="imgUrl" :description="props.title" />
</template>
<script setup>
import { computed } from 'vue'
import imgUrl from '../assets/images/empty.webp';
const props = defineProps({
  title: {
    type: String,
    default: '暂无信息'
  },
  imageWidth: {
    type: Number,
    default: 160
  },
  padding: {
    type: String,
    default: '40px 0'
  },
  textTop: {
    type: String,
    default: '20px'
  },
  top: {
    type: [Number, String],
    default: 166
  }
})
const topStyle = computed(() => {
  if (typeof props.top === 'number') {
    return `height: ${props.top}px`
  }
  if (typeof props.top === 'string') {
    return `height: ${props.top}`
  }
  return ''
})

</script>
<style>
.el-empty {
  --el-empty-padding: v-bind(props.padding);
  --el-empty-description-margin-top: v-bind(props.textTop);
}
</style>