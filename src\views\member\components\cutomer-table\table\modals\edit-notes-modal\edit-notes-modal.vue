<template>
  <el-dialog :model-value="visible" title="修改备注" :width="width" @close="close">
    <div p-15>
      <el-input v-model.trim="notes" :maxlength="200" type="textarea" placeholder="请填写备注信息"
        :autosize="{ minRows: 6 ,maxRows:10}" resize="none" show-word-limit />
    </div>
    <template #footer>
      <div text-center>
        <el-button type="primary" plain class="w-100px" @click="close()">取消</el-button>
        <el-button :loading="loading" class="w-100px" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref, watch } from 'vue';
import { ElMessage } from "element-plus";
import { updateMember } from "@/api/member";

const emits = defineEmits(['close', 'change'])
const props = defineProps({
  customer: { type: Object, default: () => ({}) },
  visible: { type: Boolean, default: false },
  width: { type: Number }
})
const notes = ref('');
const loading = ref(false);

async function confirm() {
  if (props.customer._id) {
    if (notes.value === props.customer.notes && notes.value) {
      close()
    } else {
      loading.value = true;
      let { success, message } = await updateMember(props.customer._id, { notes: notes.value });
      if (success) {
        ElMessage.success('修改成功');
        close()
        emits('change', notes.value)
      } else {
        ElMessage.error(message);
      }
      loading.value = false;
    }
  }
}

function close() {
  emits('close')
}

watch(() => props.visible, n => {
  if (n) {
    notes.value = props.customer && typeof props.customer.notes == 'string' ? props.customer.notes : '';
  }
})
</script>