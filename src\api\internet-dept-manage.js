import { post } from "./axios";

// 统一的API调用函数
async function useInternetDeptCorp(data) {
  const res = await post("corp", data);
  return res;
}

// 获取院区列表
export async function getHospitalAreaList(params = {}) {
  const result = await useInternetDeptCorp({
    type: 'getHospitalAreaList',
    params: {
      page: 1,
      pageSize: 100,
      status: 1,
      sortBy: 'createTime',
      sortOrder: -1,
      ...params
    }
  });
  return result;
}

// 获取科室列表
export async function getHlwDeptList(params = {}) {
  const result = await useInternetDeptCorp({
    type: 'getHlwDeptList',
    params: {
      page: 1,
      pageSize: 100,
      status: 1,
      sortBy: 'createTime',
      sortOrder: -1,
      ...params
    }
  });
  return result;
}

// 获取科室树形结构
export async function getHlwDeptTree(params = {}) {
  return await useInternetDeptCorp({
    type: 'getHlwDeptTree',
    params
  });
}

// 获取互联网科室列表（院区和科室合并）
export async function getInternetDeptList() {
  try {
    // 并行获取院区和科室数据
    const [hospitalResult, deptResult] = await Promise.all([
      getHospitalAreaList(),
      getHlwDeptList()
    ]);

    const list = [];
    
    // 添加院区数据
    if (hospitalResult.success && hospitalResult.data?.data?.list) {

      hospitalResult.data.data.list.forEach(area => {
        list.push({
          _id: area._id, // 保持原始MongoDB ID作为主键
          type: 'district',
          districtName: area.areaName,
          districtId: area.areaId, // 院区业务ID
          districtDesc: area.areaDescription,
          createTime: area.createTime,
          updateTime: area.updateTime,
          status: area.status,
          level: 0, // 院区是0级，科室从1级开始
          sort: area.sort || 0,
          label: area.areaName
        });
      });
    }

    // 添加科室数据
    if (deptResult.success && deptResult.data?.data?.list) {
      // 首先添加所有科室到列表中，先不设置level
      const deptList = [];
      const deptMap = new Map(); // 创建科室业务ID到MongoDB ID的映射

      // 第一遍遍历：建立业务ID到MongoDB ID的映射
      deptResult.data.data.list.forEach(dept => {
        deptMap.set(dept.hlwDeptId || dept.hlw_dept_id, dept._id);
      });



      deptResult.data.data.list.forEach(dept => {
        const areaInfo = dept.areaInfo?.[0];

        // 处理科室的父节点关系
        let parentId = null;
        let parentName = '';

        if (dept.parentId) {
          // 如果科室有parentId，说明它是子科室，父节点是另一个科室
          // 将业务ID转换为MongoDB ID
          parentId = deptMap.get(dept.parentId) || dept.parentId;



          // 从parentInfo中获取父科室名称
          if (dept.parentInfo && dept.parentInfo.length > 0) {
            parentName = dept.parentInfo[0].hlwDeptName || '';
          }
        } else {
          // 如果科室没有parentId，则父节点是院区
          // 优先使用院区的业务ID（districtId），兼容MongoDB _id
          const parentArea = hospitalResult.data.data.list.find(area => area.areaId === dept.areaId);
          if (parentArea) {
            // 优先使用院区的业务ID，但保存MongoDB _id用于兼容
            parentId = parentArea.areaId || parentArea._id; // 优先使用业务ID
            parentName = parentArea.areaName || '';
          } else {
            // 兼容性：如果通过areaId找不到，尝试使用原来的方式
            parentId = dept.areaId;
            parentName = areaInfo?.areaName || '';
          }
        }

        const deptItem = {
          _id: dept._id,
          type: 'dept',
          deptName: dept.hlwDeptName || dept.hlw_dept_name, // 兼容新旧字段
          deptId: dept.hlwDeptId || dept.hlw_dept_id, // 兼容新旧字段
          deptDesc: dept.description,
          areaId: dept.areaId, // 保存科室所属的院区ID
          parentId: parentId, // 父节点ID（可能是院区或父科室）
          parentName: parentName,
          createTime: dept.createTime,
          updateTime: dept.updateTime,
          status: dept.status,
          level: dept.level || (dept.parentId ? 3 : 2), // 优先使用后端返回的level，兼容旧数据
          sort: dept.sort || 0,
          label: dept.hlwDeptName || dept.hlw_dept_name // 兼容新旧字段
        };

        deptList.push(deptItem);
        list.push(deptItem);
      });

      // 使用后端返回的层级，不需要重新计算

      // 设置正确的父科室名称（如果之前没有从parentInfo获取到）
      deptList.forEach(dept => {
        if (dept.parentId && dept.level > 2 && !dept.parentName) { // 有父科室且是子科室且没有父科室名称
          const parentDept = deptList.find(d => d._id === dept.parentId);
          if (parentDept) {
            dept.parentName = parentDept.deptName;
          }
        }
      });
    }

    return {
      success: true,
      data: { list }
    };
  } catch (error) {
    return {
      success: false,
      message: '获取数据失败: ' + error.message
    };
  }
}

// 新增院区
export async function addDistrict(params) {
  return await useInternetDeptCorp({
    type: 'addHospitalArea',
    params: {
      areaName: params.districtName,
      areaId: params.districtId,
      areaDescription: params.districtDesc
    }
  });
}

// 新增科室
export async function addDept(params) {
  // 直接使用传入的参数，不再重新计算areaId
  const apiParams = {
    type: 'addHlwDept',
    params: {
      hlwDeptName: params.hlwDeptName,
      hlwDeptId: params.hlwDeptId,
      description: params.description,
      areaId: params.areaId, // 直接使用传入的areaId
      status: params.status || 1
    }
  };

  // 如果有父科室ID，设置parentId字段
  if (params.parentId) {
    apiParams.params.parentId = params.parentId;
  }

  const result = await useInternetDeptCorp(apiParams);

  // 如果返回空对象或失败，给出更明确的错误信息
  if (!result || Object.keys(result).length === 0) {
    return {
      success: false,
      message: '服务器响应异常，请检查后端API实现'
    };
  }

  if (!result.success && !result.message) {
    return {
      success: false,
      message: '添加科室失败，请检查参数或联系管理员'
    };
  }

  return result;
}

// 删除院区
export async function deleteDistrict(id) {
  return await useInternetDeptCorp({
    type: 'deleteHospitalArea',
    params: { _id: id }
  });
}

// 删除科室
export async function deleteDept(id) {
  return await useInternetDeptCorp({
    type: 'deleteHlwDept',
    params: { _id: id }
  });
}

// 更新院区
export async function updateDistrict(id, params) {
  return await useInternetDeptCorp({
    type: 'updateHospitalArea',
    params: {
      _id: id,
      areaName: params.districtName,
      areaId: params.districtId,
      areaDescription: params.districtDesc,
      status: 1
    }
  });
}

// 更新科室
export async function updateDept(id, params) {
  return await useInternetDeptCorp({
    type: 'updateHlwDept',
    params: {
      _id: id,
      hlwDeptName: params.deptName,
      hlwDeptId: params.deptId,
      description: params.deptDesc,
      status: 1
    }
  });
}

// 检查院区ID是否存在
export async function checkAreaIdExists(areaId, excludeId = null) {
  return await useInternetDeptCorp({
    type: 'checkAreaIdExists',
    params: {
      areaId,
      excludeId
    }
  });
}

// 检查科室ID是否存在
export async function checkHlwDeptIdExists(deptId, excludeId = null) {
  return await useInternetDeptCorp({
    type: 'checkHlwDeptIdExists',
    params: {
      hlwDeptId: deptId,
      excludeId
    }
  });
}

// 批量排序科室
export async function sortInternetDeptList(sortData) {
  // 只保留_id和sort字段
  const cleanSortData = sortData.map(item => ({
    _id: item._id,
    sort: item.sort
  }));
  
  return await useInternetDeptCorp({
    type: 'sortHlwDeptList',
    params: { sortData: cleanSortData }
  });
}

// 批量排序院区
export async function sortHospitalAreaList(sortData) {
  // 只保留_id和sort字段
  const cleanSortData = sortData.map(item => ({
    _id: item._id,
    sort: item.sort
  }));
  
  return await useInternetDeptCorp({
    type: 'sortHospitalAreaList',
    params: { sortData: cleanSortData }
  });
}

// 获取预约推荐院区和科室列表
export async function getAppointmentRecommendList() {
  try {
    // 并行获取院区和科室数据
    const [hospitalResult, deptResult] = await Promise.all([
      getHospitalAreaList({ sortBy: 'sort', sortOrder: 1 }),
      getHlwDeptList({ sortBy: 'sort', sortOrder: 1 })
    ]);
    if (!hospitalResult.success || !deptResult.success) {
      return {
        success: false,
        message: '获取预约推荐数据失败'
      };
    }
    const areas = hospitalResult.data?.data?.list || [];
    const departments = deptResult.data?.data?.list || [];

    // 构建预约推荐数据结构
    const appointmentData = {
      areas: areas.map(area => ({
        id: area._id,
        areaId: area.areaId,
        name: area.areaName,
        description: area.areaDescription,
        sort: area.sort || 0
      })),
      departments: buildDepartmentTree(departments, areas)
    };

    return {
      success: true,
      data: appointmentData
    };
  } catch (error) {
    return {
      success: false,
      message: '获取预约推荐数据失败: ' + error.message
    };
  }
}

// 构建科室树形结构用于预约推荐
function buildDepartmentTree(departments, areas) {
  const tree = [];
  const areaMap = new Map();
  // 创建院区映射
  areas.forEach(area => {
    areaMap.set(area.areaId, area);
  });
  // 按院区分组科室
  const departmentsByArea = new Map();

  departments.forEach(dept => {
    const areaId = dept.areaId;
    // 先尝试通过areaId找到院区
    let area = areaMap.get(areaId);
    if (!area) {
      // 兼容性：如果通过areaId找不到，尝试通过_id找
      area = Array.from(areaMap.values()).find(a => a._id === areaId);
    }

    if (area) {
      const areaKey = area.areaId; // 使用院区的_id作为key
      if (!departmentsByArea.has(areaKey)) {
        departmentsByArea.set(areaKey, []);
      }
      departmentsByArea.get(areaKey).push(dept);
    }
  });
  // 为每个院区构建科室树
  departmentsByArea.forEach((depts, areaKey) => {
    const area = areaMap.get(areaKey);
    if (!area) return;

    // 构建该院区的科室树
    const areaTree = {
      id: area._id,
      areaId: area.areaId,
      name: area.areaName,
      description: area.areaDescription,
      type: 'area',
      children: buildDeptHierarchy(depts)
    };

    tree.push(areaTree);
  });

  return tree;
}

// 构建科室层级结构
function buildDeptHierarchy(departments) {
  const deptMap = new Map();
  const rootDepts = [];
  // 创建科室映射
  departments.forEach(dept => {
    deptMap.set(dept.hlwDeptId, {
      id: dept._id,
      deptId: dept.hlwDeptId, // 业务ID，用于兼容
      hlwDeptId: dept.hlwDeptId, // 业务ID，标准字段
      name: dept.hlwDeptName,
      description: dept.description,
      parentId: dept.parentId,
      level: dept.level,
      sort: dept.sort || 0,
      type: 'department',
      children: [],
      expanded: false // 默认不展开
    });
  });
  // 构建层级关系
  departments.forEach(dept => {
    const deptNode = deptMap.get(dept.hlwDeptId);
    if (dept.parentId && deptMap.has(dept.parentId)) {
      // 有父级科室，添加到父级的children中
      const parent = deptMap.get(dept.parentId);
      parent.children.push(deptNode);
    } else {
      // 没有父级科室，作为根级科室
      rootDepts.push(deptNode);
    }
  });
  // 处理虚拟科室：只为一级科室创建虚拟二级科室，二级、三级科室不创建虚拟科室
  const processVirtualDepartments = (children, currentLevel = 1) => {
    children.forEach(child => {
      if (child.children && child.children.length > 0) {
        // 如果有子科室，递归处理下一级
        processVirtualDepartments(child.children, currentLevel + 1);
      } else {
        // 如果没有子科室，根据当前层级决定是否创建虚拟科室
        if (currentLevel === 1) {
          // 一级科室没有子科室时，创建虚拟二级科室
          child.children = [{
            id: `${child.id}_virtual`,
            deptId: child.deptId, // 使用父科室的deptId
            hlwDeptId: child.hlwDeptId || child.deptId,
            name: child.name,
            description: child.description,
            parentId: child.id, // 父科室的ID
            level: 2, // 虚拟二级科室
            sort: 0,
            type: 'department',
            children: [],
            expanded: false,
            isVirtual: true,
            // 添加用于API调用的真实科室ID
            realDeptId: child.deptId // 真实科室的业务ID
          }];
        }
        // 二级、三级科室没有子科室时，不创建虚拟科室，直接作为末级科室
      }
    });
  };

  processVirtualDepartments(rootDepts);

  // 按sort字段排序
  const sortChildren = (children) => {
    children.sort((a, b) => (a.sort || 0) - (b.sort || 0));
    children.forEach(child => {
      if (child.children && child.children.length > 0) {
        sortChildren(child.children);
      }
    });
  };

  sortChildren(rootDepts);
  return rootDepts;
}

// 获取院区科室选择器数据（用于各种选择场景）
export async function getHospitalAreaAndDeptSelector() {
  try {
    // 并行获取院区和科室数据
    const [hospitalResult, deptResult] = await Promise.all([
      getHospitalAreaList({ sortBy: 'sort', sortOrder: 1 }),
      getHlwDeptList({ sortBy: 'sort', sortOrder: 1 })
    ]);

    if (!hospitalResult.success || !deptResult.success) {
      return {
        success: false,
        message: '获取院区科室数据失败'
      };
    }

    const areas = hospitalResult.data?.data?.list || [];
    const departments = deptResult.data?.data?.list || [];

    // 构建简化的选择器数据结构
    const selectorData = {
      areas: areas.map(area => ({
        value: area._id,
        label: area.areaName,
        areaId: area.areaId,
        description: area.areaDescription
      })),
      departments: departments.map(dept => ({
        value: dept._id,
        label: dept.hlwDeptName,
        deptId: dept.hlwDeptId,
        description: dept.description,
        areaId: dept.areaId,
        parentId: dept.parentId,
        level: dept.level || 2
      }))
    };

    return {
      success: true,
      data: selectorData
    };
  } catch (error) {
    return {
      success: false,
      message: '获取院区科室数据失败: ' + error.message
    };
  }
}

// ====== 人员管理相关API ======

// 数据转换函数：将新API返回的corp-member数据转换为组件兼容格式
// 不再需要数据转换函数，直接使用corp-member原始数据

// 数据转换函数：将旧API格式的数据转换为新API格式
function transformHlwToCorpMemberFormat(hlwMember) {
  return {
    userid: hlwMember.relatedUserId || hlwMember._id,
    anotherName: hlwMember.name,
    name: hlwMember.name,
    gender: hlwMember.gender,
    mobile: hlwMember.phone,
    phone: hlwMember.phone,
    hlwDeptIds: hlwMember.deptId || [],
    job: hlwMember.job,
    workNo: hlwMember.workId,
    avatar: hlwMember.avatar,
    title: hlwMember.title,
    outpatientDept: hlwMember.outpatientDept,
    outpatientTime: hlwMember.outpatientTime,
    specialty: hlwMember.expertise || hlwMember.specialty || hlwMember.specialtyFields || '', // 兼容多种字段名
    memberTroduce: hlwMember.introduction,
    callNumber: hlwMember.publicPhone,
    convenienceService: hlwMember.convenienceService,
    recommended: hlwMember.recommend,
    hlwSortOrder: hlwMember.sortOrder || {}
  };
}

// 添加人员到互联网科室 - 使用addHlwMembers API（实质是更新操作）
export async function addHlwMembers(corpId, members) {
  return await useInternetDeptCorp({
    type: 'addHlwMembers',
    corpId: corpId,
    members: members
  });
}

// 根据互联网科室ID获取人员列表 - 使用getHlwDeptMembersWithSort API
// 支持传入 hlwDeptId (业务ID) 或 MongoDB _id，优先使用业务ID
export async function getHlwHospitalMembersByDept(corpId, deptId) {
  // 参数验证
  if (!corpId) {
    return {
      success: false,
      message: '企业ID不能为空'
    };
  }

  if (!deptId) {
    return {
      success: false,
      message: '科室ID不能为空'
    };
  }

  try {
    // 使用新的API接口 - getHlwDeptMembersWithSort 获取单个科室成员（带排序）
    // deptId 可以是 hlwDeptId (业务ID) 或 MongoDB _id，后端会自动识别
    const result = await useInternetDeptCorp({
      type: 'getHlwDeptMembersWithSort',
      corpId: corpId,
      hlwDeptId: Array.isArray(deptId) ? deptId[0] : deptId, // 单个科室ID，支持业务ID和MongoDB _id
      page: 1,
      pageSize: 1000 // 获取所有成员
    });

    // 直接返回corp-member原始数据，不进行转换
    if (result.success) {
      // 根据实际API响应结构处理数据
      let dataArray = [];

      if (result.data && result.data.data && Array.isArray(result.data.data)) {
        dataArray = result.data.data;
      } else if (Array.isArray(result.data)) {
        dataArray = result.data;
      }
      // 过滤掉无效数据，但不进行字段转换
      const validData = dataArray.filter(item => item && item.userid);

      return {
        success: result.success,
        message: result.message,
        data: validData,
        total: result.data?.total || validData.length
      };
    }

    return result;
  } catch (error) {
    return {
      success: false,
      message: `获取人员失败: ${error.message || error}`
    };
  }
}

// 更新corp-member信息 - 用于编辑功能
export async function updateCorpMember(id, corpId, params) {
  console.log('updateCorpMember called with:', { id, corpId, params });

  const result = await useInternetDeptCorp({
    type: 'updateCorpMember',
    id: id,
    corpId: corpId,
    params: {
      ...params
    }
  });

  console.log('updateCorpMember result:', result);
  return result;
}

// 从指定科室移除人员（只移除科室关联，不删除人员记录）
export async function removeHlwMemberFromDept(userid, corpId, hlwDeptId) {
  return await useInternetDeptCorp({
    type: 'removeHlwMemberFromDept',
    corpId: corpId,
    userid: userid,
    hlwDeptId: hlwDeptId
  });
}

// 删除重复的函数声明

// 删除不需要的函数，只保留核心业务需要的API

// 更新人员推荐状态
export async function updateMemberRecommendStatus(userid, corpId, recommended) {
  return await useInternetDeptCorp({
    type: 'updateCorpMember',
    id: userid,
    corpId: corpId,
    params: {
      recommended: recommended
    }
  });
}

// 批量更新人员排序 - 使用batchUpdateSortOrder API
export async function batchUpdateMembersSortOrder(corpId, deptId, members) {
  // 验证参数
  if (!corpId || !deptId || !Array.isArray(members)) {
    return {
      success: false,
      message: '参数不完整：corpId, deptId 和 members 都是必需的'
    };
  }

  try {
    // 转换为新API格式
    const updates = members.map(member => ({
      userid: member.userid || member._id,
      hlwDeptId: deptId,
      sortOrder: member.sortOrder || 1
    }));

    const result = await useInternetDeptCorp({
      type: 'batchUpdateSortOrder',
      corpId: corpId,
      updates: updates
    });

    return result;
  } catch (error) {
    return {
      success: false,
      message: `批量更新排序失败: ${error.message || error}`
    };
  }
}

// 搜索HLW来源的人员（支持模糊搜索）
export async function searchMembers(corpId, keyword, page = 1, pageSize = 50, hlwDeptId = null,searchAllRoles = false) {
  try {
    const params = {
      type: 'searchHlwMembers',
      corpId: corpId,
      keyword: keyword || '',
      page: page,
      pageSize: pageSize
    };

    // 如果指定了科室ID，添加科室过滤
    if (hlwDeptId) {
      params.hlwDeptId = hlwDeptId;
    }
    if(searchAllRoles) {
      params.searchAllRoles = searchAllRoles;
    }
    const result = await useInternetDeptCorp(params);

    return result;
  } catch (error) {
    return {
      success: false,
      message: `搜索成员失败: ${error.message || error}`
    };
  }
}

// ====== SortOrder 工具函数 ======

/**
 * 获取人员在指定科室的排序
 * @param {Object} member - 人员对象
 * @param {string} deptId - 科室ID
 * @returns {number} 排序编号
 */
export function getDeptSortOrder(member, deptId) {
  if (typeof member.sortOrder === 'object' && member.sortOrder !== null) {
    return member.sortOrder[deptId] || 0;
  }
  return 0;
}

/**
 * 检查排序格式是否为新的对象格式
 * @param {*} sortOrder - 排序数据
 * @returns {boolean} 是否为新格式
 */
export function isValidSortOrderFormat(sortOrder) {
  return typeof sortOrder === 'object' && sortOrder !== null;
}

/**
 * 获取全局排序编号（取最小值）
 * @param {Object} sortOrder - 排序对象
 * @returns {number} 全局排序编号
 */
export function getGlobalSortOrder(sortOrder) {
  if (typeof sortOrder === 'object' && sortOrder !== null) {
    const values = Object.values(sortOrder);
    return values.length > 0 ? Math.min(...values) : 0;
  }
  return 0;
}

/**
 * 创建新的排序对象
 * @param {string} deptId - 科室ID
 * @param {number} sortValue - 排序值
 * @returns {Object} 排序对象
 */
export function createSortOrder(deptId, sortValue) {
  return {
    [deptId]: sortValue
  };
}

/**
 * 更新排序对象中指定科室的排序值
 * @param {Object} sortOrder - 现有排序对象
 * @param {string} deptId - 科室ID
 * @param {number} sortValue - 新的排序值
 * @returns {Object} 更新后的排序对象
 */
export function updateSortOrder(sortOrder, deptId, sortValue) {
  const newSortOrder = typeof sortOrder === 'object' && sortOrder !== null 
    ? { ...sortOrder } 
    : {};
  
  newSortOrder[deptId] = sortValue;
  return newSortOrder;
}

