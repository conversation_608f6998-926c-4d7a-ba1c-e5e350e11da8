<template>
  <div bg-fff common-shadow rounded-8>
    <div flex items-center justify-between p-15 font-16>
      <div font-semibold>团队成员今日待办详情</div>
      <div flex items-center>
        <el-icon color="#F56C6C" class="mr-4px">
          <BellFilled />
        </el-icon>
        <div>
          公共待办剩余：<el-text type="danger"
            ><span font-16>{{ commonCount }}</span>
          </el-text>
          条
        </div>
      </div>
    </div>
    <div>
      <el-table stripe :data="list">
        <el-table-column prop="userId" label="成员">
          <template #default="{ row: { _id } }">
            <ww-user v-if="_id" :openid="_id"></ww-user>
          </template>
        </el-table-column>
        <el-table-column prop="count" label="待办总数">
          <template #default="{ row }">
            {{
              row.expireCount +
              row.closedCount +
              row.treatedCount +
              row.untreatedCount
            }}
          </template>
        </el-table-column>
        <el-table-column prop="treatedCount" label="已处理" />
        <el-table-column label="已关闭">
          <template #default="{ row }">
            {{ row.closedCount + row.expireCount }}
          </template>
        </el-table-column>
        <el-table-column prop="untreatedCount" label="待处理" />
        <el-table-column prop="importCount" label="重点待办未处理">
          <template #default="{ row }"> 0 </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script setup>
import { ref, watch, inject, computed } from "vue";
import WwUser from "@/components/ww-user/index.vue";
const props = defineProps({
  currentTeam: {
    type: Object,
    default: {},
  },
  list:{
    type: Array,
    default: [],
  },
  commonCount:{
    type: Number,
    default:0
  }
});

</script>
