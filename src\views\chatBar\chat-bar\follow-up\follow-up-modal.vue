<template>
  <el-dialog :model-value="visible" :width="width" :title="title" @close="close">
    <div p-15>
      <el-input v-model="result" maxlength="100" :autosize="{ minRows: 6, maxRows: 12 }" resize="none" show-word-limit
        type="textarea" placeholder="请填写处理结果" />
    </div>
    <template #footer>
      <div class="text-center">
        <el-button class="w-100px" plain @click="close()">取消</el-button>
        <el-button class="w-100px" :loading="loading" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref, toRefs, watch } from 'vue';
import { ElMessage, ElMessageBox } from "element-plus";
import { storeToRefs } from 'pinia';
import { memberStore } from "@/store/member";
import { setTodoStatus } from "@/api/todo";

const emits = defineEmits(['change', 'close']);
const props = defineProps({
  width: { type: Number },
  visible: { type: Boolean, default: false },
  status: { type: String, validator: val => ['closed', 'treated'].includes(val) },
  todo: { type: Object, default: () => ({}) }
})
const { width, visible } = toRefs(props)
const title = computed(() => props.status === 'closed' ? '取消任务' : '完成任务');
const result = ref('');
const loading = ref(false)

function close() {
  emits('close')
}

const { memberInfo } = storeToRefs(memberStore());

async function confirm() {
  if (loading.value || props.todo.executorUserId !== memberInfo.value.userid) return;
  const action = props.status === 'closed' ? '取消' : '完成'
  await ElMessageBox.confirm(`确定${action}该待办事项吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  });
  loading.value = true;
  const { success, message } = await setTodoStatus(
    props.todo._id,
    props.status,
    result.value || `已${action}`,
    memberInfo.value.userid
  );
  if (success) {
    ElMessage.success(message);
    close()
    emits('change')
  } else {
    ElMessage.error(message);
  }
  loading.value = false;
}
watch(() => props.visible, n => {
  if (n) result.value = ''
})
</script>
<style lang="scss" scoped>
.tag-item {
  border: 1px solid #eee;
  margin-bottom: 12px;
}

.tag-group {
  margin-bottom: -12px;
}

.tag-item+.tag-item {
  margin-left: 12px;
}

.tag-item.active {
  background-color: var(--yc-primary-color);
  border-color: var(--yc-primary-color);
  color: #fff;
}
</style>