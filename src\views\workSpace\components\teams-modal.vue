<template>
  <el-dialog :model-value="visible" class="teamDialog" title="团队详情" width="600" @close="close">
    <div px-15>
      <el-tabs v-model="currentTeamId" @tab-change="changeTeam($event)">
        <el-tab-pane v-for="team in teams" :key="team.teamId" :label="team.name" :name="team.teamId"></el-tab-pane>
      </el-tabs>
    </div>
    <el-scrollbar wrap-style="max-height:calc(100vh - 330px)">
      <el-form v-if="currentTeam" px-15 :label-width="120" label-position="left" labal-suffix="：">
        <el-form-item label="团队名称">
          <el-text size="default">{{ currentTeam.name }}</el-text>
        </el-form-item>
        <el-form-item label="团队负责人">
          <span v-for="userid in currentTeam.leaders" :key="userid" mr-5>
            <el-tag><ww-user :openid="userid"></ww-user></el-tag>
          </span>
        </el-form-item>
        <el-form-item label="团队成员">
          <span v-for="(userid, index) in currentTeam.members" :key="index" mr-5>
            <el-tag><ww-user :openid="userid"></ww-user></el-tag>
          </span>
        </el-form-item>
        <el-form-item v-if="currentTeam.friends.length" label="可加好友人员">
          <span v-for="userid in currentTeam.friends" :key="userid" mr-5 title="支持加好友">
            <el-tag><ww-user :openid="userid"></ww-user></el-tag>
          </span>
        </el-form-item>
        <el-form-item label="团队简介">
          <el-text size="default">{{ currentTeam.teamTroduce }}</el-text>
        </el-form-item>
        <el-form-item v-if="currentTeam.qrcodes && currentTeam.qrcodes.length" label="团队二维码">
          <div v-for="item in currentTeam.qrcodes" :key="item.id" text-center>
            <vue-qrcode class="mx-auto" :value="item.qrcode" :options="{ width: 120, margin: 2 }"></vue-qrcode>
            <div>
              <el-text size="default">{{ item.name }}</el-text>
            </div>
            <div>
              <el-button type="primary" text @click="downloadQrcode(item.qrcode, `${item.name}.png`)">下载二维码</el-button>
              <el-button type="primary" text @click="useCopy(item.qrcode)">
                复制链接
              </el-button>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button class="w-100px" type="primary" @click="close()">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, toRefs, watch } from 'vue';
import { downloadQrcode, useCopy } from '@/utils';
import VueQrcode from "@chenfengyuan/vue-qrcode";

const emits = defineEmits(['close']);
const props = defineProps({
  visible: { type: Boolean, default: false },
  teams: { type: Array, default: () => [] },
  teamId: { type: String, default: '' }
})
const { teams, teamId, visible } = toRefs(props);
const currentTeamId = ref('');
const currentTeam = computed(() => {
  const team = teams.value.find(i => i.teamId === currentTeamId.value);
  if (team) return { ...team, ...groupTeammate(team) }
  return team
});

function groupTeammate(team) {
  const memberList = Array.isArray(team.memberList) ? team.memberList : [];
  const memberLeaderList = Array.isArray(team.memberLeaderList) ? team.memberLeaderList : [];
  const friendlyMembers = Array.isArray(team.friendlyMembers) ? team.friendlyMembers : [];
  return memberList.reduce((val, item) => {
    if (memberLeaderList.includes(item)) {
      val.leaders.push(item)
    } else {
      val.members.push(item)
    }
    if (friendlyMembers.includes(item)) val.friends.push(item);
    return val
  }, { members: [], leaders: [], friends: [] })
}

function close() {
  emits('close')
}

watch(visible, n => {
  if (n) {
    const team = teams.value.find(i => i.teamId === teamId.value);
    currentTeamId.value = (team || teams.value[0] || {}).teamId
  }
})
</script>

<style scoped>
:global(.teamDialog.el-dialog .el-dialog__header) {
  display: none;
}
</style>