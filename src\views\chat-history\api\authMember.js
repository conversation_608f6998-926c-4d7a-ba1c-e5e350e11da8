import { getSessionArchiveUserList, getCorpSessionArchive, batchStorageSession } from "@/api/sessionArchive.js";
import { ref } from "vue";
const authMemberList = ref([]);
const selectdUserId = ref("");
const publicKeys = ref("");
const memberLoading = ref(false);
const enableChatRecord = ref(true);
async function getUserAuthList() {
  memberLoading.value = true;
  const { success, data } = await getSessionArchiveUserList();
  if (success) {
    enableChatRecord.value = true;
    await batchStorageSession();
    memberLoading.value = false;
    authMemberList.value = data.data;
    selectdUserId.value = data.data[0].userid || "";
  } else {
    enableChatRecord.value = false;
    memberLoading.value = false;
  }
}
function getSelectdUser(i) {
  selectdUserId.value = i ? i.userid : "";
}
async function getCorpSessionArchiveInfo() {
  const { success, data } = await getCorpSessionArchive();
  if (success) {
    publicKeys.value = data.data.publicKeys;
  }
}

export { authMemberList, getUserAuthList, getSelectdUser, selectdUserId, publicKeys, getCorpSessionArchiveInfo, memberLoading, enableChatRecord };
