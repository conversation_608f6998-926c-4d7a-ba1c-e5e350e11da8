<template>
  <my-layout bg-fff common-shadow>
    <layout-main font-14>
      <div px-15>
        <div class="pb-30px" border-bottom>
          <div flex items-center class="py-15px pb-6px">
            <div font-16 font-semibold active-title-bar>基本信息</div>
          </div>
          <el-form label-suffix="：" :rules="rules" :model="planForm" label-width="120px">
            <el-form-item label="名称" class="mt-20px" prop="planName">
              <el-input flex-grow placeholder="请输入名称" v-model="planForm.planName" />
            </el-form-item>
            <el-form-item v-if="planForm.planType === 'team' && planForm.teamId" label="应用团队" class="mt-20px w-full">
              <team-data :teamId="planForm.teamId" />
            </el-form-item>
            <el-form-item label="应用范围" class="mt-20px w-full">
              <el-input flex-grow type="textarea" :maxlength="100" rows="4" show-word-limit
                v-model="planForm.planDetail" placeholder="请输入应用范围"></el-input>
            </el-form-item>
            <el-form-item v-if="planForm.planType === 'corp'" label="使用团队" class="is-required mt-20px w-full">
              <el-select multiple v-model="planForm.teamIds" placeholder="请选择使用团队" class="w-full">
                <el-option label="全部团队" value="allTeams" @click="onSelected('allTeams')"></el-option>
                <el-option v-for="item in allTeams" :key="item.teamId" :label="item.name" :value="item.teamId"
                  @click="onSelected(item.teamId)" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <div pb-15>
          <div pb-10 class="pt-30px">
            <div active-title-bar font-16 font-semibold class="mb-20px">
              任务配置
              <el-button text class="item form-tag" type="primary" size="small" :icon="Plus" plain
                @click="addTask">新增任务项</el-button>
            </div>
            <task-timeline ref="taskTimelineRef" @saveSuccess="onSaveSuccess" :taskList="taskList"></task-timeline>
          </div>
        </div>
      </div>
    </layout-main>
    <layout-item>
      <div p-15 text-center common-shadow>
        <el-button class="w-100px" type="primary" plain @click="cancel">取消</el-button>
        <el-button class="w-100px" type="primary" @click="save">保存</el-button>
      </div>
    </layout-item>
  </my-layout>
  <el-dialog v-model="usePlanDialogVisible" title="提示" draggable :width="300">
    <div class="p-15px">是否立即启用该计划事项？</div>
    <template #footer>
      <div class="text-center">
        <el-button @click="savePlan(false)">稍后启用</el-button>
        <el-button type="primary" @click="savePlan(true)">立即启动</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { reactive, ref, onBeforeMount, nextTick } from "vue";
import { storeToRefs } from "pinia";
import { ElMessage } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import { tagsStore } from "@/store/tags";
import { teamStore } from "@/store/team";
import { managePlanStore } from "@/store/managePlan/managePlan.js";
import { getRandomStr } from "@/utils";

import { Plus } from "@element-plus/icons-vue";
import TeamData from '@/components/team-data/team-data.vue'
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import taskTimeline from "./components/task-timeline";


const { getManagementPlanById, createManagementPlan, updateManagementPlan } = managePlanStore();
const { allTeams } = storeToRefs(teamStore())
const router = useRouter();
const route = useRoute();
const { removeTag, renameTag } = tagsStore();

const planForm = ref({
  planName: "",
  planDetail: "",
});
let taskList = ref([]);
let managementPlan = [];
const taskTimelineRef = ref("");
const usePlanDialogVisible = ref(false);
let planId = route.params.id;
let cateId = '';
let type = route.params.type;
let rules = reactive({
  planName: [{ required: true, message: "请输入计划名称", trigger: "blur" }],
});
// 根据ID 获取计划详情
async function getManagementPlanByIdAction(planId) {
  const plan = await getManagementPlanById(planId);
  managementPlan = plan;
  const { planName, planDetail, taskList: tasks, planType } = plan;
  const data = {
    planName: type === "copy" ? "" : planName,
    planDetail,
    planType
  };
  taskList.value = tasks;
  // 复制团队计划里面的机构回访计划
  if (type === 'copy' && history.state.planType === 'team' && planType === 'corp') {
    data.planType = 'team';
    data.teamId = history.state.teamId;
    data.teamName = history.state.teamName;
  } else if (type === 'copy' && history.state.planType === 'team' && plan.planType === 'team') {
    data.teamId = plan.teamId;
    data.teamName = plan.teamName;
  } else if (plan.planType === 'corp' && plan.fitAllTeams) {
    data.teamIds = ['allTeams']
  } else if (plan.planType === 'corp' && Array.isArray(plan.teamIds)) {
    data.teamIds = [...plan.teamIds]
  }else if (plan.planType === 'team' && plan.teamId) {
    data.teamId = plan.teamId
  }
  cateId = plan.cateId;
  planForm.value = data;
  
}
async function cancel() {
  removeTag(route.fullPath);
}
function addTask() {
  taskTimelineRef.value.addTask(planId);
}
function onSaveSuccess(task = []) {
  taskList.value = task;
}
function save() {
  if (planId && type !== "copy") {
    updatePlan();
  } else {
    usePlanDialogVisible.value = true;
  }
}
async function savePlan(status) {
  const { planName, planDetail, planType, teamIds, teamName, teamId } = planForm.value;
  // usePlanDialogVisible.value = false;
  if (typeof planName !== 'string' || planName.trim() === '') {
    ElMessage.warning("请输入计划名称");
    return;
  }
  if (planType === 'corp' && Array.isArray(teamIds) && teamIds.length === 0) {
    ElMessage.warning("请选择团队");
    return;
  }
  if (taskList.value.length === 0) {
    ElMessage.warning("请新增任务");
    return;
  }
  const params = {
    planType,
    planId: getRandomStr(),
    planName,
    planSource: "team",
    planStatus: status,
    planDetail,
    createor: localStorage.getItem("userId"),
    taskList: taskList.value,
    cateId
  };
  if (planType === 'corp' && Array.isArray(teamIds) && teamIds.length === 1 && teamIds[0] == 'allTeams') {
    params.fitAllTeams = true;
    params.teamIds = [];
  } else if (planType === 'corp' && Array.isArray(teamIds) && teamIds.length > 0) {
    params.teamIds = teamIds;
    params.fitAllTeams = false;
  } else if (planType === 'team') {
    params.teamId = teamId;
    params.teamName = teamName;
  }
  const res = await createManagementPlan(params);
  if (res) {
    cancel();
  }
}
async function updatePlan() {
  const { planName, planDetail, planType, teamIds } = planForm.value;
  usePlanDialogVisible.value = false;
  if (typeof planName !== 'string' || planName.trim() === '') {
    ElMessage.warning("请输入计划名称");
    return;
  }
  if (taskList.value.length === 0) {
    ElMessage.warning("请新增任务");
    return;
  }
  const params = {
    planName,
    planDetail,
    planSource: managementPlan.planSource,
    taskList: taskList.value,
  };
  if (planType === 'corp' && Array.isArray(teamIds) && teamIds.length === 1 && teamIds[0] == 'allTeams') {
    params.fitAllTeams = true;
  } else if (planType === 'corp' && Array.isArray(teamIds) && teamIds.length > 0) {
    params.teamIds = teamIds;
    params.fitAllTeams = false;
  }
  const res = await updateManagementPlan(planId, params);
  if (res) {
    cancel()
  }
}

async function onSelected(value) {
  await nextTick()
  if (value === 'allTeams') {
    if (planForm.value.teamIds.includes('allTeams')) { // 说明选择了全部团队
      planForm.value.teamIds = ['allTeams']
    } else {
      planForm.value.teamIds = []
    }
  } else {
    if (planForm.value.teamIds.includes('allTeams')) {
      planForm.value.teamIds = planForm.value.teamIds.filter(item => item !== 'allTeams')
    }
  }
}

onBeforeMount(() => {
  if (route.params.id) {
    renameTag(route.path, '编辑计划')
    getManagementPlanByIdAction(route.params.id);
  } else {
    planForm.value.planType = history.state.planType === 'corp' ? 'corp' : 'team';
    cateId = history.state.cateId || '';
    if (planForm.value.planType === 'corp') {
      planForm.value.teamIds = ['allTeams']
    } else if (planForm.value.planType === 'team') {
      planForm.value.teamId = history.state.teamId;
      planForm.value.teamName = history.state.teamName;
    }
  }
})


</script>
<style scoped>
:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-timeline-item__tail) {
  border-left-color: #006eff;
}
</style>