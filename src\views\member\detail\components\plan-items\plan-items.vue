<template>
  <my-layout bg-fff common-shadow>
    <layout-item>
      <div px-15 class="pt-15px" v-if="customerType !== 'corpCustomer'">
        <el-button type="primary" @click="visible = true">新增待跟进事项</el-button>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <div h-full px-15 class="pt-15px">
        <el-table stripe v-loading="loading" height="100%" class="w-full" :data="list">
          <el-table-column property="taskType" label="待跟进事项类型" :width="140" />
          <el-table-column property="taskContent" label="待跟进事项内容" :width="300">
            <template #default="{ row }">
              <el-text truncated class="pointer" style="transform: translateY(4px)">
                {{ row.taskContent || "" }}
              </el-text>
            </template>
          </el-table-column>
          <el-table-column property="planTime" label="计划执行时间" :width="150" />
          <el-table-column property="expireTime" label="失效时间" :width="150">
            <template #default="{ row: { expireTime, willBeExpired } }">
              <el-text :type="willBeExpired ? 'danger' : ''">{{ expireTime }}</el-text>
            </template>
          </el-table-column>
          <el-table-column property="executorUserId" label="跟进人" :width="90">
            <template #default="{ row: { executorUserId } }">
              <span v-if="executorUserId === 'system'">系统自动</span>
              <ww-user v-else-if="executorUserId" :openid="executorUserId"></ww-user>
            </template>
          </el-table-column>
          <el-table-column fixed="right" class-name="el-table-fixed-column--shadow" label="操作" :width="60">
            <template #default="{ row }">
              <el-text v-if="memberInfo.userid === row.executorUserId" type="primary" pointer @click="toDetial(row)">去处理</el-text>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </layout-main>
    <layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="currentPage" @handle-size-change="onSizeChange" @handle-current-change="onCurrentChange" />
    </layout-item>
    <add-modal :customer="customer" :visible="visible" @change="onSizeChange(10)" @close="visible = false" />
  </my-layout>
</template>
<script setup>
import dayjs from "dayjs";
import { computed, ref, toRefs, watch } from "vue";
import { storeToRefs } from "pinia";
import { ToDoEventType, ToDoTaskStatus, ServiceType } from "@/baseData";
import pagination from "@/components/pagination/pagination.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import { getToDoList } from "@/api/todo";
import WwUser from "@/components/ww-user/index.vue";
import { useRouter } from "vue-router";
import addModal from "./add-modal.vue";
import { teamStore } from "@/store/team";
import { memberStore } from "@/store/member";
const { currentTeam } = storeToRefs(teamStore());
import { useRoute } from "vue-router";

watch(currentTeam, (newItem, oldItem) => {
  if (newItem && oldItem && newItem.teamId !== oldItem.teamId) {
    getList();
  }
});

const router = useRouter();
const corpId = localStorage.getItem("corpId");
const userId = localStorage.getItem("userId");
const props = defineProps(["memberId", "customer", "customerType"]);
const { memberId, customer } = toRefs(props);
const { memberInfo } = storeToRefs(memberStore());
const listData = ref([]);
const total = ref(0);
const pageSize = ref(10);
const currentPage = ref(1);
const loading = ref(false);
getList();
function onSizeChange(e) {
  currentPage.value = 1;
  pageSize.value = e;
  getList();
}
function onCurrentChange(e) {
  currentPage.value = e;
  getList();
}

const list = computed(() => {
  return listData.value.map((i) => {
    const status = ToDoTaskStatus.find((item) => item.value === i.eventStatus);
    const willBeExpired = i.expireTime ? dayjs().isAfter(dayjs(i.expireTime).subtract(2, "days")) && dayjs().isBefore(dayjs(i.expireTime)) : false;
    return {
      ...i,
      willBeExpired,
      taskStatus: status ? status.label : "",
      taskType: ServiceType[i.eventType] || "",
      createTime: i.createTime ? dayjs(i.createTime).format("YYYY-MM-DD HH:mm") : "",
      planTime: i.plannedExecutionTime ? dayjs(i.plannedExecutionTime).format("YYYY-MM-DD") : "",
      expireTime: i.expireTime ? dayjs(i.expireTime).format("YYYY-MM-DD") : "",
      endTime: i.endTime ? dayjs(i.endTime).format("YYYY-MM-DD HH:mm") : "",
    };
  });
});

async function getList() {
  loading.value = true;
  const params = {
    corpId,
    customerId: memberId.value,
    eventStatus: "untreated",
  };
  const { success, data, message } = await getToDoList(currentPage.value, pageSize.value, params, { orderBy: "plannedExecutionTime", orderType: "asc" });
  if (success) {
    const { data: tableData = [], total: count } = data;
    listData.value = tableData;
    total.value = count;
  }
  loading.value = false;
}

async function toDetial(item) {
  router.push({ name: "SCHEDULEDETAIL", params: { id: item._id } });
}

const visible = ref(false);
</script>
<style scoped lang="scss">
:deep(.el-table__inner-wrapper) {
  height: 100% !important;
}
</style>
