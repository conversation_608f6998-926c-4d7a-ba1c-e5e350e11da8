import { computed, ref, toRefs } from 'vue';
import { watchDebounced } from '@vueuse/core';
import { defineStore } from "pinia";
import { memberStore } from "./member";
import { getDisease } from '@/api/knowledgeBase'

const col = { lg: 8, md: 12, span: 24 }
const rowCol = { span: 24 }
export default defineStore('medicalRecord', () => {
  const { corpInfo } = memberStore();
  
  const intentedProjectList = ref(corpInfo.intentedProjectList || []);
  const serviceItems = computed(() => intentedProjectList.value.map(i => ({ label: i, value: i })));
  const corpDiseases = computed(() => Array.isArray(corpInfo.diseases) ? corpInfo.diseases.map(i => ({ label: i, value: i })) : []);
  const corpList = [{ label: '本院', value: 'current corp' }, { label: '其他', value: 'other' }];

  const diseases = ref([]);
  const diseaseLoading = ref(false);
  const diseaseName = ref('');

  const outpatientFormItems = ref([
    // { label: '创建人', prop: 'creator', required: false, type: 'wwuser', col },
    { label: '就诊机构', prop: 'corp', required: true, type: 'select', options: corpList, placeholder: '请选择就诊机构名称', col },
    { label: '就诊科室', prop: 'deptName', required: false, type: 'input', placeholder: '请输入就诊科室', showProps: 'corp', showValue: 'current corp', maxlength: 50, col },
    { label: '责任医生', prop: 'doctor', required: false, type: 'input', placeholder: '请输入责任医生姓名', showProps: 'corp', showValue: 'current corp', maxlength: 50, col },
    { label: '就诊日期', prop: 'visitTime', required: true, type: 'date', placeholder: '请输入就诊日期', col },
    { label: '门诊诊断', prop: 'diagnosisName', required: true, type: 'remoteSelect', remote: searchDisease, options: diseases, loading: diseaseLoading, placeholder: '请搜索门诊诊断名称', col },
    { label: '服务项目', prop: 'serviceItems', required: false, type: 'select', options: serviceItems, placeholder: '请选择服务项目', col },
    { label: '费用金额', prop: 'amount', required: false, type: 'inputAmount', appendText: '元', placeholder: '请输入门诊金额', col },
    { label: '处置计划', prop: 'disposalPlan', required: false, type: 'textarea', placeholder: '请输入处置计划', maxlength: 200, col: rowCol },
    { label: '治疗方案', prop: 'treatmentPlan', required: false, type: 'textarea', placeholder: '请输入诊后治疗方案', maxlength: 200, col: rowCol },
    { label: '康复措施', prop: 'recoveryGuide', required: false, type: 'textarea', placeholder: '请输入康复措施指导', maxlength: 200, col: rowCol }
  ])
  const inhospitalFormItems = ref([
    // { label: '创建人', prop: 'creator', required: false, type: 'wwuser', col },
    { label: '就诊机构', prop: 'corp', required: true, type: 'select', options: corpList, placeholder: '请选择就诊机构名称', maxlength: 50, col },
    { label: '入院科室', prop: 'deptName', required: false, type: 'input', placeholder: '请输入就诊科室', showProps: 'corp', showValue: 'current corp', maxlength: 50, col },
    { label: '责任医生', prop: 'doctor', required: false, type: 'input', placeholder: '请输入责任医生姓名', showProps: 'corp', showValue: 'current corp', maxlength: 50, col },
    { label: '病区床位', prop: 'bedNo', required: false, type: 'input', placeholder: '请输入病区床位', showProps: 'corp', showValue: 'current corp', maxlength: 50, col },
    { label: '入院时间', prop: 'inhosDate', required: true, type: 'date', placeholder: '请选择入院时间', col },
    { label: '入院诊断', prop: 'diagnosisName', required: true, type: 'remoteSelect', remote: searchDisease, options: diseases, loading: diseaseLoading, placeholder: '请搜索入院诊断名称', col },
    { label: '手术名称', prop: 'operation', required: true, type: 'input', placeholder: '请输入手术名称', col },
    { label: '手术日期', prop: 'operationDate', required: true, type: 'date', placeholder: '请选择手术日期', col },
    { label: '出院日期', prop: 'outhosDate', required: true, type: 'date', placeholder: '请选择出院日期', col },
    { label: '消费金额', prop: 'amount', required: false, type: 'inputAmount', appendText: '元', placeholder: '请输入门诊金额', col },
    { label: '治疗方案', prop: 'treatmentPlan', required: false, type: 'textarea', placeholder: '请输入院后治疗方案', maxlength: 200, col: rowCol },
    { label: '康复指导', prop: 'recoveryGuide', required: false, type: 'textarea', placeholder: '请输入康复措施指导', maxlength: 200, col: rowCol }
  ])


  function initDisease(label, value) {
    diseases.value = [{ label, value }, ...corpDiseases.value]
  }
  function searchDisease(e) {
    if (e.trim()) {
      diseaseLoading.value = true;
      diseaseName.value = e.trim();
    }
  }

  watchDebounced(diseaseName, async () => {
    const res = await getDisease(diseaseName.value);
    const list = res.data && Array.isArray(res.data.data) ? res.data.data.map(i => ({ label: i.diseaseName, value: i.code })) : [];
    const firstOption = list.find(i => i.label === diseaseName.value) || { label: diseaseName.value, value: diseaseName.value };
    const diseaseList = corpDiseases.value.filter(i => i.label !== diseaseName.value && !list.some(item => item.label == i.label));
    diseases.value = [firstOption, ...list.filter(i => i.label !== diseaseName.value), ...diseaseList];
    diseaseLoading.value = false;
  }, { debounce: 500, maxWait: 5 * 1000 })

  return { outpatientFormItems, inhospitalFormItems, initDisease }
})
