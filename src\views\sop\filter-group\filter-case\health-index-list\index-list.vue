<template>
  <div class="min-h-40px ml-160px mt-10px w-1/2 rounded bg-gray-100 p-10px">
    <div v-for="(item,idx) in data" :key="item.key" class="flex items-center" :class="idx > 0 ? 'mt-10px':''">
      <el-select v-model="item.condition" class="w-180px flex-shrink-0 mr-5px" placeholder="请选择"
        @change="change($event, idx)">
        <el-option v-for="i in options" :label="i.label" :value="i.title" />
      </el-select>
      <number-input v-if="optionMap[item.condition]&& optionMap[item.condition].type === 'range'" v-model="item.value"
        type="range" :unit="optionMap[item.condition].unit || ''" :integer="false" :precision="3" />
      <el-select multiple collapse-tags
        v-else-if="optionMap[item.condition]&& optionMap[item.condition].type === 'select'" v-model="item.value"
        class="w-240px flex-shrink-0" placeholder="请选择">
        <el-option v-for="i in item.options" :key="i.value" :label="i.label" :value="i.value" />
      </el-select>
      <el-button text type="danger" size="small" class="flex-shrink-0" style="margin-left: 5px;"
        @click="removeItem(idx)">
        <el-icon class="mr-3px">
          <Delete />
        </el-icon>
      </el-button>
    </div>
  </div>
</template>
<script setup>
import { computed } from 'vue';
import { useVModel } from '@vueuse/core';
import { storeToRefs } from 'pinia'
import { ElMessageBox } from 'element-plus';
import { dbStore } from "@/store/db.js";
import numberInput from '../number-input.vue';

const emit = defineEmits(['update:modelValue'])
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})
const data = useVModel(props, 'modelValue', emit);
const { healthIndicatorsTemplate } = storeToRefs(dbStore());
const options = computed(() => {
  return healthIndicatorsTemplate.value.map(i => {
    const item = { label: i.name, title: i.title };
    if (i.unit) item.unit = i.unit
    if (i.type === 'number') {
      item.type = 'range';
    } else if (Array.isArray(i.range)) {
      item.type = 'select';
      item.options = i.range.map(j => ({ label: j, value: j }));
    }
    return item
  })
})
const optionMap = computed(() => {
  return options.value.reduce((m, i) => {
    m[i.title] = i;
    return m
  }, {})
})

function change(val, idx) {
  const opt = options.value.find(i => i.title === val);
  if (opt) {
    data.value[idx].type = opt.type;
    data.value[idx].value = opt.type === 'range' ? {} : []
    if (opt.options) data.value[idx].options = opt.options
  }
}

async function removeItem(idx) {
  await ElMessageBox.confirm('确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
  data.value = data.value.filter((i, index) => idx !== index)
}

</script>
