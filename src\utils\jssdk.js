import { envjudgwxWork } from "@/utils/common";
import { getSignstrOfjsapi } from "@/api/wecom";
import { memberStore } from "@/store/member";
import * as ww from "@/libs/@wecom/jssdk/wecom.esm.js";
let factory = null;
let initRes = null;
function jsSDKConfig() {
  initRes = initRes || initJsSdk();
  return initRes;
}
async function initJsSdk() {
  let res = await getSignature();
  await wwComponent(res);
  await wxComponent(res);
  return true;
}
async function wwComponent(res) {
  return new Promise(async (reslove, reject) => {
    let corpInfo = memberStore().corpInfo;
    ww.register({
      corpId: localStorage.getItem("corpId"),
      agentId: corpInfo.auth_info.agent[0].agentid,
      jsApiList: ["selectEnterpriseContact", "getCurCorpGroupContact", "openEnterpriseChat", "getCurExternalContact", "selectExternalContact", "sendChatMessage", "openThirdAppServiceChat", "getContext", "wwapp.invokeJsApiByCallInfo"], // 必填，需要使用的JS接口列表，凡是要调用的接口都需要传进来
      async getAgentConfigSignature(e) {
        return res.appSign;
      },
      async onAgentConfigSuccess(e) {
        console.log("onConfigSuccess", e);
        await ww.initOpenData();
        reslove(true);
      },
      onAgentConfigFail(e) {
        console.log("onConfigFail", e);
        reject(false);
      },
    });
  });
}

async function wxComponent(res) {
  return new Promise(async (reslove, reject) => {
    const { timestamp, nonceStr, signature } = res.corpSign;
    let corpInfo = memberStore().corpInfo;
    let corpid = localStorage.getItem("corpId");
    // 通过config接口注入权限验证配置
    wx.config({
      beta: true, // 必须这么写，否则wx.invoke调用形式的jsapi会有问题
      debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
      appId: corpid, // 必填，企业微信的corpID，必须是本企业的corpID，不允许跨企业使用
      timestamp: timestamp, // 必填，生成签名的时间戳
      nonceStr: nonceStr, // 必填，生成签名的随机串
      signature: signature, // 必填，签名，见 附录-JS-SDK使用权限签名算法
      jsApiList: ["selectEnterpriseContact", "previewImage", "getCurCorpGroupContact", "openEnterpriseChat", "getCurExternalContact", "shareToExternalContact", "selectExternalContact", "sendChatMessage", "openThirdAppServiceChat", "getContext"], // 必填，需要使用的JS接口列表，凡是要调用的接口都需要传进来
    });
    wx.ready(async function () {
      const { timestamp, nonceStr, signature } = res.appSign;
      wx.agentConfig({
        corpid: corpid, // 必填，企业微信的corpid，必须与当前登录的企业一致
        agentid: corpInfo.auth_info.agent[0].agentid, // 必填，企业微信的应用id （e.g. 1000247）
        timestamp: timestamp, // 必填，生成签名的时间戳
        nonceStr: nonceStr, // 必填，生成签名的随机串
        signature: signature, // 必填，签名，见附录-JS-SDK使用权限签名算法
        jsApiList: ["selectEnterpriseContact", "previewImage", "getCurCorpGroupContact", "openEnterpriseChat", "getCurExternalContact", "shareToExternalContact", "sendChatMessage", "selectExternalContact", "openThirdAppServiceChat", "getContext"], // 必填，需要使用的JS接口列表，凡是要调用的接口都需要传进来
        success: function () {
          console.log("config调用成功");
          // 回调
          reslove(true);
        },
        fail: function (error) {
          console.log("configFalse", error);
          reject(false);
        },
      });
      // config信息验证后会执行ready方法，所有接口调用都必须在config接口获得结果之后，config是一个客户端的异步操作，所以如果需要在页面加载时就调用相关接口，则须把相关接口放在ready函数中调用来确保正确执行。对于用户触发时才调用的接口，则可以直接调用，不需要放在ready函数中。
    });
    wx.error(function (error) {
      console.log("configFalse", error);
      reject(false);
    });
  });
}

// 获取 signature
async function getSignature() {
  let url = decodeURIComponent(window.location.href.split("#")[0]);
  let corpInfo = memberStore().corpInfo;
  const { data } = await getSignstrOfjsapi({
    permanentCode: corpInfo.permanent_code,
    corpId: localStorage.getItem("corpId"),
    url: url,
  });
  return data;
}

async function getCurExternalContact() {
  return new Promise((reslove, reject) => {
    ww.getCurExternalContact({
      success: function (res) {
        console.log(res);
        reslove(res.userId);
      },
      complete: function (res) {
        console.log(res);
        reslove(res.userId);
      },
      fail: function (e) {
        console.log("回调失败", e);
        reject("");
      },
    });
  });
}
async function selectExternalContact() {
  return new Promise((reslove, reject) => {
    ww.selectExternalContact({
      filterType: 0, //0表示展示全部外部联系人列表，1表示仅展示未曾选择过的外部联系人。默认值为0；除了0与1，其他值非法。在企业微信2.4.22及以后版本支持该参数
      success: function (res) {
        console.log("selectExternalContact success: ", res);
        reslove(res);
      },
      fail: function (res) {
        console.log("selectExternalContact fail: ", res);
        reject(res);
        if (res.errMsg.indexOf("function not exist") > -1) {
          alert("版本过低请升级");
        }
      },
    });
  });
}
async function openEnterpriseChat(externalUserIds = "", userIds = "", callback) {
  ww.openEnterpriseChat({
    // 注意：userIds和externalUserIds至少选填一个。内部群最多2000人；外部群最多500人；如果有微信联系人，最多40人
    userIds, //参与会话的企业成员列表，格式为userid1;userid2;...，用分号隔开。
    externalUserIds, // 参与会话的外部联系人列表，格式为userId1;userId2;…，用分号隔开。
    groupName: "", // 会话名称。单聊时该参数传入空字符串""即可。
    chatId: "", // 若要打开已有会话，需指定此参数。如果是新建会话，chatId必须为空串
    success: function (res) {
      console.log("openEnterpriseChat success: ", res);
      typeof callback === "function" && callback("success");
      var chatId = res.chatId; //返回当前群聊ID，仅当使用agentConfig注入该接口权限时才返回chatId
      // 回调
    },
    fail: function (res) {
      console.log("openEnterpriseChat fail: ", res);
      typeof callback === "function" && callback("fail");
      if (res.errMsg.indexOf("function not exist") > -1) {
        alert("版本过低请升级");
      }
    },
  });
}
function selectEnterpriseContact(type = "multi") {
  if (!envjudgwxWork()) return;
  return new Promise((reslove) => {
    ww.selectEnterpriseContact({
      fromDepartmentId: -1, // 必填，表示打开的通讯录从指定的部门开始展示，-1表示自己所在部门开始, 0表示从最上层开始
      mode: type, // 必填，选择模式，single表示单选，multi表示多选
      type: ["department", "user"], // 必填，选择限制类型，指定department、user中的一个或者多个
      success: function (res) {
        if (typeof res.result == "string") {
          res.result = JSON.parse(res.result); //由于目前各个终端尚未完全兼容，需要开发者额外判断result类型以保证在各个终端的兼容性
        }
        var selectedDepartmentList = res.result.departmentList || []; // 已选的部门列表
        var selectedUserList = res.result.userList || []; // 已选的成员列表
        return reslove({ selectedUserList, selectedDepartmentList });
      },
      fail: function (res) {
        return reslove({ selectedUserList: [], selectedDepartmentList: [] });
      },
    });
  });
}
function sendChatMessage(item) {
  const { type, url, title, desc, imgUrl, video, file } = item;
  ww.sendChatMessage({
    msgtype: type, //消息类型，必填
    news: {
      link: url, //H5消息页面url 必填
      title, //H5消息标题
      desc, //H5消息摘要
      imgUrl, //H5消息封面图片URL
    },
    text: {
      content: desc, //文本内容
    },
    image: {
      mediaid: imgUrl, //图片的素材id
    },
    video: {
      mediaid: video, //视频的素材id
    },
    file: {
      mediaid: file, //文件的素材id
    },
  });
}
function sendChatMessagePromise(item) {
  console.log("sendChatMessage Promise", item);
  return new Promise((resolve, reject) => {
    ww.sendChatMessage({
      ...item,
      success: function (res) {
        console.log("sendChatMessage: ", res);
        resolve();
      },
      fail: function (res) {
        console.log("sendChatMessage fail: ", res);
        reject();
      },
    });
  });
}

function createOpenDataFrame(containerElement, template, data, style = "", methods) {
  if (!factory) {
    factory = ww.createOpenDataFrameFactory();
  }
  factory.createOpenDataFrame({
    el: containerElement, // “容器”元素，用于挂载展示组件
    template,
    style,
    data,
    methods,
  });
}
export { jsSDKConfig, selectEnterpriseContact, getCurExternalContact, selectExternalContact, openEnterpriseChat, sendChatMessage, sendChatMessagePromise, createOpenDataFrame };
