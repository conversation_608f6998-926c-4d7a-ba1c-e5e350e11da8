<template>
  <el-dialog :model-value="visible" :width="width" title="选择发送范围" @close="close">
    <el-form class="px-10px" label-width="130px" label-position="left" label-suffix="：">
      <!-- <el-form-item class="mb-10px" label="客户所属团队">
        <el-scrollbar class="flex-grow" wrap-style="max-height: 30vh">
          <el-checkbox-group v-model="selfTeamIds">
            <el-checkbox :label="item.teamId" v-for="item in teams" :key="item._id">
              {{ item.name }}
            </el-checkbox>
          </el-checkbox-group>
        </el-scrollbar>
      </el-form-item> -->
      <el-form-item class="mb-10px" label="接收客户范围">
        <el-radio-group v-model="form.range">
          <el-radio label="ALL">全部客户</el-radio>
          <el-radio label="SOME">按条件筛选我的客户</el-radio>
        </el-radio-group>
      </el-form-item>
      <template v-if="form.range === 'SOME'">
        <el-form-item class="pt-10px border-t border-gray-100" label="执行团队">
          <div class="flex-grow flex justify-end items-center px-12px py-4px border cursor-pointer border-gray-200 hover:border-gray-300 rounded-8px" @click="selectTeam()">
            <div class="flex-grow w-0 truncate h-2em text-right">{{ teamStr }}</div>
            <el-icon size="18" class="ml-10px text-gray-400 hover:text-blue-500">
              <ArrowRight />
            </el-icon>
          </div>
        </el-form-item>
        <el-form-item class="pt-10px border-t border-gray-100" label="发送给">
          <div class="flex-grow flex justify-end items-center px-12px py-4px border cursor-pointer border-gray-200 hover:border-gray-300 rounded-8px" @click="selectCustomers('include')" v-loading="loading">
            <div class="flex-grow w-0 truncate h-2em text-right">{{ includeStr }}</div>
            <el-icon size="18" class="ml-10px text-gray-400 hover:text-blue-500">
              <ArrowRight />
            </el-icon>
          </div>
        </el-form-item>
        <el-form-item label="不发送给">
          <div class="flex-grow flex justify-end items-center px-12px py-4px border cursor-pointer border-gray-200 hover:border-gray-300 rounded-8px" @click="selectCustomers('exclude')">
            <div class="flex-grow w-0 truncate h-2em text-right">{{ excludeStr }}</div>
            <el-icon size="18" class="ml-10px text-gray-400 hover:text-blue-500">
              <ArrowRight />
            </el-icon>
          </div>
        </el-form-item>
      </template>
    </el-form>
    <template #footer>
      <div text-center>
        <el-button type="primary" plain class="w-100px" @click="close()">取消</el-button>
        <el-button class="w-100px" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <team-filter :visible="teamVisible" @close="teamVisible = false" :selectTeams="selfTeamIds" @change="changeTeam"></team-filter>
  <customer-filter :visible="filterVisible" :width="filterWidth" @close="closeFilter" @change="changeCustomer" :teamIds="selfTeamIds" :selectCustomers="filterCustomers" />
</template>
<script setup>
import { computed, ref, toRefs, watch } from "vue";
import useModal from "@/hooks/useModal";
import { teamStore } from "@/store/team";
import { memberStore } from "@/store/member";
import CustomerFilter from "./customer-filter.vue";
import { getCorpCustomer } from "@/api/member";
import { ElMessage } from "element-plus";
import teamFilter from "./team-filter.vue";
const { corpInfo } = memberStore();
let selfTeamIds = ref([]);
const { teams } = teamStore();
const props = defineProps({
  visible: { type: Boolean, default: false },
  width: { type: String, default: "30%" },
  teamIds: { type: Array, default: () => [] },
});
watch(
  () => props.visible,
  (val) => {
    if (!val) return;
    // 默认选择第一个团队
    selfTeamIds.value = props.teamIds.length > 0 ? props.teamIds : teams.map((item) => item.teamId);
  }
);
watch(
  () => selfTeamIds.value,
  (val) => {
    if (val.length === 0) return;
    getCustomers();
  },
  { immediate: true }
);
const teamVisible = ref(false);
const form = ref({ team: "", range: "ALL" });
const excludeCustomers = ref([]);
const includeCustomer = ref([]);
const { close: closeFilter, show: showFilter, visible: filterVisible, width: filterWidth } = useModal(640); //  选择客户弹窗
const customerType = ref("");
const excludeStr = computed(() => getCustomerStr(excludeCustomers.value));
const includeStr = computed(() => getCustomerStr(includeCustomer.value));
const teamStr = computed(() => {
  return selfTeamIds.value.length ? `${selfTeamIds.value.length}个团队` : "";
});
const filterCustomers = computed(() => {
  return customerType.value === "include" ? includeCustomer.value : excludeCustomers.value;
});
function getCustomerStr(customers) {
  return customers.length ? `${customers.length}位客户` : "";
}

function selectCustomers(type) {
  customerType.value = type;
  if (selfTeamIds.value && selfTeamIds.value.length === 0) return ElMessage.info("请选择团队");
  showFilter();
}
// 选择团队
function changeTeam(teamIds) {
  // 判断数组内容是否相同
  teamVisible.value = false;
  if (teamIds.length === selfTeamIds.value.length && teamIds.every((value, index) => value === selfTeamIds.value[index])) return;
  selfTeamIds.value = teamIds;
}
function selectTeam(type) {
  teamVisible.value = true;
}
function changeCustomer(customers) {
  if (customerType.value === "exclude") {
    excludeCustomers.value = customers;
  } else {
    includeCustomer.value = customers;
  }
  closeFilter();
}
const emits = defineEmits(["close"]);
function close() {
  emits("close");
}
let more = false,
  loading = ref(false);
async function getCustomers() {
  more = false;
  loading.value = true;
  await getAllCustomers(1);
  loading.value = false;
}
async function confirm() {
  if (selfTeamIds.value.length === 0) return ElMessage.info("请选择团队");
  let customers = includeCustomer.value.filter((customer) => !excludeCustomers.value.some((item) => item._id === customer._id));
  if (customers.length === 0) return ElMessage.info("请选择客户");
  // 传出团队和客户
  emits("confirm", { customers, teamIds: selfTeamIds.value });
}

// 获取所有客户
async function getAllCustomers(page) {
  let params = {
    page,
    pageSize: 100,
    showExternalCustomer: true,
    teamId: selfTeamIds.value,
    userIds: [localStorage.getItem("userId")],
    corpId: localStorage.getItem("corpId"),
    permanent_code: corpInfo.permanent_code,
  };
  const { data, success } = await getCorpCustomer(params);
  if (!success) return;
  includeCustomer.value = page === 1 ? data.data : [...includeCustomer.value, ...data.data];
  more = data.total > includeCustomer.value.length;
  // 如果还有数据，继续获取
  if (more) {
    page += 1;
    await getAllCustomers(page);
  }
}
</script>
