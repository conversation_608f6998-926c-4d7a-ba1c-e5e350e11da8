<template>
  <div class="wrapper">
    <v-header></v-header>
    <div class="content-box">
      <nav-tags></nav-tags>
      <div class="content">
        <router-view v-slot="{ Component }">
          <keep-alive>
            <component :is="Component" :key="route.name" v-if="route.meta.keepAlive" />
          </keep-alive>
          <component :is="Component" :key="route.name" v-if="!route.meta.keepAlive" />
        </router-view>
      </div>
    </div>
    <v-sidebar></v-sidebar>
  </div>
</template>
<script setup>
import vHeader from "./header.vue";
import vSidebar from "./sidebar.vue";
import navTags from "./navTags";
import { useRoute } from "vue-router";
const route = useRoute();
</script>

<style scoped>
.wrapper {
  width: 100%;
  height: 100%;
  width: 100vw;
  height: 100vh;
  /* min-width: 1349px; */
  overflow: auto;
}

.content-box {
  transition: padding 0.3s ease-in-out;
  position: absolute;
  left: 150px;
  top: var(--app-header-height);
  bottom: 0;
  right: 0;
  background: #f3f4f7;
}

.content-collapse {
  padding-left: 64px;
}

.content {
  /* width: 100%; */
  height: calc(100vh - var(--app-header-height) - var(--app-nav-height) - 5px);
  overflow: auto;
  padding: 10px;
  min-width: 750px;
  margin-top: 35px;
}

.move-enter-active,
.move-leave-active {
  transition: opacity 0.5s;
}

.move-enter,
.move-leave {
  opacity: 0;
}
</style>
