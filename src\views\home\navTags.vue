<template>

  <div class="pb-5px bg-white nav-tabs absolute z-10 w-full">
    <el-tabs :model-value="selectTag" @tab-change="handleClick">
      <el-tab-pane v-for="item in tags" :key="item.path" :name="item.path">
        <template #label>
          <tag-tab :active="selectTag === item.path" :closable="item.type !== 'fixed' && tags.length > 1"
            :name="item.showName || item.name" @close="handleClose(item)" />
          <!-- <div class="h-40px bg-blue-500"> {{ item.name }}</div> -->
          <!-- <el-tag class="tag" size="large" :type="selectTag === item.path ? '' : 'info'"
            :closable="(item.type !== 'fixed' && tags.length > 1)" @close="handleClose(item)" @click="change(item)">
            {{ item.name }}
          </el-tag> -->
        </template>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script setup>
import { watch } from "vue";
import { storeToRefs } from 'pinia';
import { tagsStore } from "@/store/tags";
import { useRouter } from "vue-router";
import tagTab from "./tag-tab.vue";

const router = useRouter();
const { addTag, changeTag, closeTag } = tagsStore();
const { selectTag, tags } = storeToRefs(tagsStore());

function handleClose(item) {
  closeTag(item)
}

function handleClick(path) {
  if (path !== selectTag.value) {
    const item = tags.value.find(i => i.path === path);
    changeTag(item);
    router.push({ path: item.path })
  }
}

watch(
  () => router.currentRoute.value,
  (newValue, oldValue) => {
    addTag({
      name: newValue.meta.title,
      routeName: newValue.name,
      path: newValue.fullPath,
      multTag: Boolean(newValue.meta.multTag),
      prePath: oldValue && oldValue.fullPath ? oldValue.fullPath : ''
    });
  },
  { immediate: false }
);
</script>
<style>
.nav-tabs .el-tabs__item {
  --el-tabs-header-height: 34px
}

.nav-tabs .el-tabs__nav-next,
.el-tabs__nav-prev {
  line-height: 34px;
}
</style>
<style scoped lang="scss">
.tags {
  margin: 10px 15px 0;

  .tag {
    margin-right: 10px;
    cursor: pointer;
  }
}

.icon__close {
  margin-left: 8px;
  transform: translateY(2px);
}

:deep(.el-tag.el-tag--info) {
  background-color: white;
}

:deep(.el-tag__content) {
  position: relative;
}

:deep(.el-tag__content::before) {
  position: absolute;
  content: "";
  width: 2px;
  // height: 1em;
  left: -10px;
  top: 1px;
  bottom: 0;
  background: currentColor;
}

:deep(.el-tabs__header) {
  margin-bottom: 0;
}

:deep(.el-tabs__nav-wrap::after),
:deep(.el-tabs__active-bar.is-top) {
  display: none;
}

:deep(.el-tabs__item) {
  padding: 0 !important;
}
</style>
