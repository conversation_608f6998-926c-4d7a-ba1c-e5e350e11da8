<template>
  <div ref="todayOverviewRef" class="flex-shrink-0 mt-[1rem] rect-border-box">
    <title-bar title="今日概览" />
    <!-- <div class="h-15vh" ref="todayPieRef"></div> -->
    <div class="flex mt-[1rem] text-white text-center">
      <div class="w-1/5 flex flex-col justify-center bg-[#37D2D4] rounded">
        <count-to-bar class="text-30px h-60px leading-60px" :end-val="todayCustomerData.todayCustomerCount || 0" :duration="1500" />
        <div class="text-16px font-semibold leading-40px">新增客户数</div>
      </div>
      <div class="w-1/5 ml-[1rem] flex flex-col justify-center bg-[#19CA88] rounded">
        <count-to-bar class="text-30px h-60px leading-60px" :end-val="todayCustomerData.todayServiceCount || 0" :duration="1500" />
        <div class="text-16px font-semibold leading-40px">服务记录</div>
      </div>
      <div class="w-1/5 ml-[1rem] flex flex-col justify-center bg-[#FD9133] rounded">
        <count-to-bar class="text-30px h-60px leading-60px" :end-val="todayChatData.todayChatCount || 0" :duration="1500" />
        <div class="text-16px font-semibold leading-40px">会话消息数</div>
      </div>
      <div class="w-1/5 ml-[1rem] flex flex-col justify-center">
        <el-progress type="dashboard" :percentage="todoCount" color="#2E8CFF">
          <template #default="{ percentage }">
            <div class="text-24px font-semibold leading-42px text-white">
              <count-to-bar :end-val="todoCount" :duration="1500" />
            </div>
            <div class="text-14px text-white text-center">待办总数</div>
          </template>
        </el-progress>
      </div>
      <div class="w-1/5 ml-[1rem] flex flex-col justify-center">
        <el-progress type="dashboard" :percentage="completionRate" color="#F83818">
          <template #default="{ percentage }">
            <div class="text-24px font-semibold leading-42px text-white">
              <count-to-bar :end-val="completionRate" :duration="1500" suffix="%" />
            </div>
            <div class="text-14px text-white text-center">完成率</div>
          </template>
        </el-progress>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted } from "vue";
import { useElementBounding } from "@vueuse/core";
import titleBar from "./title-bar.vue";
import countToBar from "../count-to-bar.vue";
import { getStatisticsEventsApi, getCorpToDolistApi } from "../api";
defineProps({
  todayCustomerData: {
    type: Object,
    default: {},
  },
  todayChatData: {
    type: Object,
    default: {},
  },
});
let todoCount = ref(0);
let completionRate = ref(0);
const todayOverviewRef = ref(null);
const { width, height } = useElementBounding(todayOverviewRef);
onMounted(async () => {
  let data = await getStatisticsEventsApi();
  const { untreatedCount, treatedCountToday } = data;
  todoCount.value = untreatedCount;
  const denominator = untreatedCount + treatedCountToday;
  completionRate.value = denominator ? (treatedCountToday / denominator) * 100 : 0;
});
</script>
<style lang="scss" scoped>
:deep(.el-progress-circle) {
  margin: 0 auto;
}

.percentage-value {
  font-size: 2rem;
  line-height: 1;
}

.percentage-label {
  font-size: 1rem;
}
</style>
