import { post } from "./axios";
async function useknow(data) {
  const res = await post("knowledgeBase", data);
  return res;
}
export async function getWelcomeList(corpId, page = 1, pageSize = 10) {
  const res = await useknow({ type: "getWelcomeList", corpId, page, pageSize });
  return res;
}

export async function removeWelcome(id, operator) {
  const res = await useknow({ type: "removeWelcome", id, operator });
  return res;
}

export async function updateWelcome(data) {
  const res = await useknow({ type: "updateWelcome", ...data });
  return res;
}

export async function getWords(id, corpId) {
  const res = await useknow({ type: "getWelcome", id, corpId });
  return res;
}

export async function getCommonWordsCates(corpId, userId, cateType) {
  const res = await useknow({
    type: "getCommonWordsCates",
    corpId,
    userId,
    cateType,
  });
  return res;
}

export async function removeCommonWordsCate(id, corpId, cateType) {
  const res = await useknow({ type: "removeCommonWordsCate", id, corpId, cateType });
  return res;
}

export async function setCommonWordsCate(data) {
  const res = await useknow({ type: "setCommonWordsCate", ...data });
  return res;
}

export async function getCommonWordsList(data) {
  const res = await useknow({ type: "getCommonWordsList", ...data });
  return res;
}

export async function removeCommonWords(data) {
  const res = await useknow({ type: "removeCommonWords", ...data });
  return res;
}

export async function setCommonWords(data) {
  const res = await useknow({ type: "setCommonWords", ...data });
  return res;
}

export async function setArticleCate(data) {
  const res = await useknow({ type: "setArticleCate", ...data });
  return res;
}

export async function getArticleCates(corpId) {
  const res = await useknow({ type: "getArticleCates", corpId });
  return res;
}

export async function removeArticleCate(id, corpId) {
  const res = await useknow({ type: "removeArticleCate", id, corpId });
  return res;
}

export async function getDisease(diseaseName = "") {
  const res = await useknow({ type: "getDisease", diseaseName });
  return res;
}

export async function getPageDisease(diseaseName = "", page, pageSize = 20) {
  const res = await useknow({ type: "getPageDisease", diseaseName, page, pageSize });
  return res;
}

export async function setArticle(data) {
  const res = await useknow({ type: "setArticle", ...data });
  return res;
}
export async function getArticle(data) {
  const res = await useknow({ type: "getArticle", ...data });
  return res;
}
export async function getArticleStats(data) {
  const res = await useknow({ type: "getArticleStats", ...data });
  return res;
}

export async function getArticleTrend(data) {
  const res = await useknow({ type: "getArticleTrend", ...data });
  return res;
}

export async function getArticleSendDetail(data) {
  const res = await useknow({ type: "getSendDetail", ...data });
  return res;
}
export async function getReadDetail(data) {
  const res = await useknow({ type: "getReadDetail", ...data });
  return res;
}

export async function getArticleList(data) {
  const res = await useknow({ type: "getArticleList", ...data });
  return res;
}

export async function getArticleStatus(data) {
  const res = await useknow({ type: "getArticleStatus", ...data });
  return res;
}

export async function removeArticle(data) {
  const res = await useknow({ type: "removeArticle", ...data });
  return res;
}

export async function toggleArticleStatus(data) {
  const res = await useknow({ type: "toggleArticleStatus", ...data });
  return res;
}

export async function getRegion(code) {
  const res = await useknow({ type: "getRegion", code });
  return res;
}

export async function addArticleSendRecord(data) {
  const res = await useknow({ type: "addArticleSendRecord", ...data });
  return res;
}

// 营销中心获取二维码

export async function getStaffQrcode(page, pageSize, params) {
  const res = await useknow({ type: "getStaffQrcode", page, pageSize, params });
  return res;
}

export async function getPersonalQrcodeList(params) {
  const res = await useknow({ type: "getPersonalQrcodeList", params });
  return res;
}

export async function getPersonalQrCode(corpUserId, corpId, creator) {
  const res = await useknow({ type: "getPersonalQrCode", corpUserId, corpId, creator });
  return res;
}

export async function getStaffQrCodeDetail(id) {
  const res = await useknow({ type: "getStaffQrCodeDetail", id });
  return res;
}

export async function updateStaffQrcode(id, params) {
  const res = await useknow({ type: "updateStaffQrcode", id, params });
  return res;
}
// 删除
export async function removeStaffQrcode(id, params) {
  const res = await useknow({ type: "removeStaffQrcode", id, params });
  return res;
}
// 新增
export async function addStaffQrcode(params) {
  const res = await useknow({ type: "addStaffQrcode", params });
  return res;
}

export async function getQrcodeCustomerCount(corpId, configId) {
  const res = await useknow({ type: "getQrcodeCustomerCount", corpId, configId });
  return res;
}

export async function getQrcodeCustomer(configId, dates) {
  const res = await useknow({ type: "getQrcodeCustomer", corpId: localStorage.getItem("corpId"), configId, dates });
  return res;
}

export async function getCorpRateConfig() {
  const res = await useknow({ type: "getCorpRateConfig", corpId: localStorage.getItem("corpId") });
  return res;
}

export async function addRateTag(text, rateStar) {
  const res = await useknow({ type: "addRateTag", corpId: localStorage.getItem("corpId"), text, rateStar });
  return res;
}

export async function deleteRateTag(id) {
  const res = await useknow({ type: "deleteRateTag", corpId: localStorage.getItem("corpId"), id });
  return res;
}

export async function updateRateTagText(id, text) {
  const res = await useknow({ type: "updateRateTagText", corpId: localStorage.getItem("corpId"), id, text });
  return res;
}

export async function addRateRecord(data) {
  const res = await useknow({ type: "addRateRecord", ...data });
  return res;
}

export async function getCorpRateRecord(data) {
  const res = await useknow({ type: "getCorpRateRecord", ...data });
  return res;
}

export async function getCorpRateStats(data) {
  const res = await useknow({ type: "getCorpRateStats", ...data });
  return res;
}
export async function getMemberRateRecord(data) {
  const res = await useknow({ type: "getMemberRateRecord", ...data });
  return res;
}

export async function getQrcodeStaticsticsSort() {
  const res = await useknow({ type: "getQrcodeStaticsticsSort", corpId: localStorage.getItem("corpId") });
  return res;
}

export async function addCorpCommonWordCate(data) {
  const res = await useknow({ type: "addCorpCommonWordCate", ...data });
  return res;
}
export async function addUserCommonWordCate(data) {
  const res = await useknow({ type: "addUserCommonWordCate", ...data });
  return res;
}

export async function deleteCorpCommonWordCate(data) {
  const res = await useknow({ type: "deleteCorpCommonWordCate", ...data });
  return res;
}
export async function deleteUserCommonWordCate(data) {
  const res = await useknow({ type: "deleteUserCommonWordCate", ...data });
  return res;
}

export async function getCorpCommonWordCate(data) {
  const res = await useknow({ type: "getCorpCommonWordCate", ...data });
  return res;
}

export async function getUserCommonWordCate(data) {
  const res = await useknow({ type: "getUserCommonWordCate", ...data });
  return res;
}

export async function updateCorpCommonWordCate(data) {
  const res = await useknow({ type: "updateCorpCommonWordCate", ...data });
  return res;
}

export async function updateUserCommonWordCate(data) {
  const res = await useknow({ type: "updateUserCommonWordCate", ...data });
  return res;
}

export async function sortCorpCommonWordCate(data) {
  const res = await useknow({ type: "sortCorpCommonWordCate", ...data });
  return res;
}

export async function addArticleCate(data) {
  const res = await useknow({ type: "addArticleCate", ...data });
  return res;
}

export async function updateArticleCate(data) {
  const res = await useknow({ type: "updateArticleCate", ...data });
  return res;
}

export async function deleteArticleCate(data) {
  const res = await useknow({ type: "deleteArticleCate", ...data });
  return res;
}

export async function getArticleCateList(data) {
  const res = await useknow({ type: "getArticleCateList", ...data });
  return res;
}

export async function sortArticleCate(data) {
  const res = await useknow({ type: "sortArticleCate", ...data });
  return res;
}

export async function addStaffQrcodeCate(data) {
  const res = await useknow({ type: "addStaffQrcodeCate", ...data });
  return res;
}

export async function updateStaffQrcodeCate(data) {
  const res = await useknow({ type: "updateStaffQrcodeCate", ...data });
  return res;
}

export async function deleteStaffQrcodeCate(data) {
  const res = await useknow({ type: "deleteStaffQrcodeCate", ...data });
  return res;
}

export async function getStaffQrcodeCateList(data) {
  const res = await useknow({ type: "getStaffQrcodeCateList", ...data });
  return res;
}

export async function sortStaffQrcodeCate(data) {
  const res = await useknow({ type: "sortStaffQrcodeCate", ...data });
  return res;
}

export async function addContactWay(data) {
  const res = await useknow({ type: "addContactWay", ...data });
  return res;
}

export async function getArticleCount(data) {
  const res = await useknow({ type: "getArticleCount", ...data });
  return res;
}
export async function getCommonWordsCount(data) {
  const res = await useknow({ type: "getCommonWordsCount", ...data });
  return res;
}

export async function getStaffQrcodeCount(data) {
  const res = await useknow({ type: "getStaffQrcodeCount", ...data });
  return res;
}

export async function getArticleListReadStats() {
  const res = await useknow({ type: "getArticleListReadStats" });
  return res;
}

export async function getQrcodeCustomerStatsByCateId(data) {
  const res = await useknow({ type: "getQrcodeCustomerStatsByCateId", ...data });
  return res;
}
