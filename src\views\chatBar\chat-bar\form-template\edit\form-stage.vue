<template>
  <form-select v-bind="$attrs" rangeKey="value" :form="form"  :title="title" :real-range="range" @change="change" />
</template>
<script setup>
import { computed, onMounted } from "vue";
import { storeToRefs } from "pinia";
import { dbStore } from "@/store/db.js";
import formSelect from "./form-select.vue";

const emits = defineEmits(["change"]);
const store = dbStore();
const { stageList } = storeToRefs(store);
const { getStageList } = store;
const range = computed(() => stageList.value);
onMounted(() => stageList.value.length === 0 && getStageList());

const props = defineProps({
  form: { type: Object, default: () => ({}) },
  title: { type: String, default: "" },
});
const form = computed(() => {
  const stage = stageList.value.find((i) => i.value === props.form[props.title]);
  if (props.title && stage) {
    return { ...props.form, [props.title]: stage ? stage.label : props.form[props.title] };
  }
  return props.form;
});

function change(data) {
  emits("change", data);
}
</script>
<style lang="scss" scoped></style>
