<template>
  <my-layout bg-fff>
    <layout-item mb-10>
      <span ref="calcRef" class="calcEle"></span>
      <div ref="titleRef" flex items-center p-15>
        <span mr-15 font-18 font-semibold>{{ survery.name }}</span>
        <el-button type="primary" size="small" plain @click="visible = true">问卷详情</el-button>
      </div>
      <div px-15 pb-10>
        <div class="query-tabs__item pointer" :class="type === 'answered' ? 'query-tabs__item--active' : ''"
          @click="changeType('answered')">
          已回收 <span v-if="tabCount.answerCount > 0">（{{ tabCount.answerCount }}）</span>
        </div>
        <div class="query-tabs__item pointer" :class="type === 'unanswer' ? 'query-tabs__item--active' : ''"
          @click="changeType('unanswer')">
          未回收<span v-if="tabCount.unAnswerCount > 0">（{{ tabCount.unAnswerCount }}）</span>
        </div>
      </div>
      <div flex items-center px-15 pb-10>
        <div class="flex items-center mr-15px">
          <div class="text-14px">客户姓名：</div>
          <el-input v-model="name" flex-shrink-0 class="w-140px" placeholder="输入客户姓名搜索" @keyup.enter="search" />
        </div>
        <div v-if="survery.enableScore" class="flex items-center mr-15px">
          <div class="text-14px">分值：</div>
          <el-input flex-shrink-0 class="w-80px" v-model="minScore" placeholder="最小分值" @keyup.enter="search" />
          <div class="px-5px"> - </div>
          <el-input flex-shrink-0 class="w-80px" v-model="maxScore" placeholder="最大分值" @keyup.enter="search" />
        </div>

        <el-button type="primary" @click="search()">查询</el-button>
      </div>
    </layout-item>

    <layout-main v-loading="loading" :scroll="false">
      <el-table stripe v-show="!loading" class="answer-table" style="height: 100%;" :data="list" border>
        <el-table-column fixed type="index" label="序号" :width="60" align="center" />
        <el-table-column fixed prop="customer" label="客户" :width="columnWidth['customer']" />
        <el-table-column fixed prop="sender" label="发送人" :width="100">
          <template #default="{ row: { sender } }">
            <ww-user v-if="sender" :openid="sender" type="userName"></ww-user>
          </template>
        </el-table-column>
        <el-table-column fixed prop="sendTime" label="发送时间" :width="columnWidth['sendTime']" />
        <!-- <el-table-column fixed v-for="col in leftColumns" :key="col.prop" :prop="col.prop" :label="col.label"
            :width="columnWidth[col.prop]" /> -->
        <el-table-column v-for="col in rightColumns" :key="col.prop" :prop="col.prop" :label="col.label"
          :width="columnWidth[col.prop]" />
        <el-table-column v-if="fillColWidth > 0" prop="blank" label=" " :width="fillColWidth" />
      </el-table>
    </layout-main>
    <layout-item>
      <pagination :bgWhite="false" :totalRow="total" :pageSize="pageSize" :currentPage="currentPage"
        @handle-size-change="onSizeChange" @handle-current-change="onCurrentChange" />
    </layout-item>
  </my-layout>
  <survery-dialog :data="survery" :visible="visible" @close="visible = false" />
</template>
<script setup>
import { computed, nextTick, ref, onActivated } from 'vue';
import { onBeforeRouteUpdate, useRoute, useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { getDetail, getAnswerList, getAnswerCount } from '@/api/survery'

import EmptyData from "@/components/empty-data.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import SurveryDialog from './dialog.vue';
import { ElMessage } from 'element-plus';
import WwUser from '@/components/ww-user/index.vue';

const route = useRoute();
const router = useRouter();
const survery = ref({});
const visible = ref(false);
const type = ref('answered')
const name = ref("");
const minScore = ref('');
const maxScore = ref('');
const total = ref(0);
const list = ref([]);
const pageSize = ref(10);
const currentPage = ref(1);
const loading = ref(true);
const tabCount = ref({});
const leftColumns = ref([
  { prop: 'customer', label: '客户' },
  { prop: 'send', label: '发送人' },
  { prop: 'sendTime', label: '发送时间' },
])
const fillColWidth = ref(0);
const rightColumns = computed(() => {
  const questions = Array.isArray(survery.value.list) ? survery.value.list : [];
  const cols = type.value === 'answered' ?
    [
      { prop: 'submitTime', label: '提交时间' },
      survery.value.enableScore ? { prop: 'score', label: '问卷总分' } : null,
      ...questions.map(i => ({ prop: i.id, label: i.title, type: i.type, options: Array.isArray(i.options) ? i.options : [] }))
    ]
    : []
  return cols.filter(Boolean);
})
const columns = computed(() => [...leftColumns.value, ...rightColumns.value]);

function changeType(val) {
  if (type.value !== val) {
    type.value = val;
    search()
  }
}

function onSizeChange(e) {
  pageSize.value = e;
  getList();
}
function onCurrentChange(e) {
  currentPage.value = e;
  getList();
}

let customerName = '';
let scoreMin = ''
let scoreMax = ''
function search() {
  customerName = name.value.trim();
  currentPage.value = 1;
  scoreMin = minScore.value.trim() !== '' && Number(minScore.value.trim()) >= 0 ? Number(minScore.value.trim()) : '';
  scoreMax = maxScore.value.trim() !== '' && Number(maxScore.value.trim()) >= 0 ? Number(maxScore.value.trim()) : '';
  getList();
}


async function getList() {
  loading.value = true;
  const query = { surveryId: id, customer: customerName, page: currentPage.value, pageSize: pageSize.value, answered: type.value === 'answered' }
  if (survery.value.enableScore) {
    if (scoreMin >= 0) query.minScore = scoreMin;
    if (scoreMax >= 0) query.maxScore = scoreMax;
  }
  const { data } = await getAnswerList(query)
  const { list: arr = [], total: count = 0, } = data;
  total.value = count;
  const tableData = arr.map(i => {
    const row = {
      ...i,
      sendTime: i.createTime ? dayjs(i.createTime).format('YYYY-MM-DD HH:mm') : '',
      submitTime: i.submitTime ? dayjs(i.submitTime).format('YYYY-MM-DD HH:mm') : '',
    }
    const rightData = getRightData(Array.isArray(row.list) ? row.list : []);
    return { ...row, ...rightData }
  })
  if (type.value === 'answered') {
    tabCount.value.answerCount = count
  } else {
    tabCount.value.unAnswerCount = count
  }
  await startCalc(tableData);
  list.value = tableData;
  loading.value = false;
}
function getRightData(list) {
  if (list.length) {
    const row = {};
    rightColumns.value.forEach(col => {
      if (col.type === 'input') {
        const question = list.find(i => i.id === col.prop);
        row[col.prop] = question && question.value ? question.value : '';
      } else if (col.type === 'radio') {
        const question = list.find(i => i.id === col.prop);
        const val = question && question.value ? question.value : '';
        const opt = col.options.find(i => val && i.value === val);
        row[col.prop] = opt && opt.label ? opt.label : '';
      }
    })
    return row
  }
  return {}
}

const columnWidth = ref({})
const calcRef = ref();
const titleRef = ref();
async function calcWidth(text = '') {
  if (calcRef.value && typeof text === 'string' && text.trim()) {
    calcRef.value.innerText = text;
    await nextTick();
    return calcRef.value.offsetWidth + 3
  }
  return 24
}
async function startCalc(arr) {
  loading.value = true;
  const props = columns.value.map(i => i.prop)
  const m = {};
  for (const item of columns.value) {
    const { label, prop } = item;
    const width = await calcWidth(label);
    m[prop] = width
  }
  for (let prop of props) {
    for (let item of arr) {
      const width = await calcWidth(item[prop]);
      m[prop] = Math.max(m[prop], width)
    }
  }
  const totalWidth = Object.values(m).reduce((val, width) => val + width);
  fillColWidth.value = titleRef.value.offsetWidth - totalWidth;
  columnWidth.value = m;
  loading.value = false
}

let id = '';
onActivated(() => {
  handleIdChange(route)
})
onBeforeRouteUpdate((to) => {
  if (to.name == 'SURVERYANSWER') handleIdChange(to);
})

async function getSurvery() {
  const corpId = localStorage.getItem("corpId");
  const { success, data, message } = await getDetail(corpId, id);
  survery.value = data && data.data ? data.data : {};
  if (success) {
    getCount();
    getList()
  } else {
    ElMessage.error(message);
  }
  // if (!success) ElMessage.error(message);
}
async function getCount() {
  const { data } = await getAnswerCount(id);
  const { answerCount = 0, unAnswerCount = 0 } = data;
  tabCount.value = { answerCount, unAnswerCount };
}

function handleIdChange(route) {
  if (route.params.id && route.params.id != id) {
    id = route.params.id;
    getSurvery()
  } else if (id && !route.params.id) {
    id = '';
    survery.value = {};
  }
}

</script>
<style scoped>
.query-tabs__item {
  display: inline-block;
  padding: 6px 16px;
  margin-right: 12px;
  border-radius: 16px;
  background-color: #f5f7fb;
  color: #333;
  border: 1px solid #ddd;
  font-size: 14px;
}

.query-tabs__item--active {
  background-color: #1261ff;
  border-color: #1261ff;
  color: #fff;
  font-weight: 600;
}

:deep(.el-badge__content.is-fixed) {
  right: calc(1px + var(--el-badge-size)/ 2 + 12px);
}

.w-200 {
  width: 200px;
}

.calcEle {
  position: fixed;
  top: -9999px;
  left: -9999px;
  z-index: -1;
  opacity: 0;
  visibility: hidden;
  padding: 0 12px;
}
</style>
