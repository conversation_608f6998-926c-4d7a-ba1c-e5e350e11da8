<template>
  <my-layout bg-fff common-shadow>
    <layout-item>
      <div class="query-bar">
        <el-form-item label="平台角色：">
          <el-select v-model="role" clearable @change="selectRole">
            <el-option :key="0" label="全部" :value="0" />
            <el-option v-for="label in roles" :key="label._id" :label="label.roleName" :value="label._id" />
          </el-select>
        </el-form-item>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <el-table stripe height="100%" :data="list" v-loading="loading" :header-cell-style="{ background: '#ecf4ff' }">
        <el-table-column property="name" label="姓名" width="120">
          <template #default="scope">
            <ww-user :openid="scope.row.name"></ww-user>
          </template>
        </el-table-column>
        <el-table-column property="userid" label="ID" width="120" />
        <el-table-column property="department" label="所属部门">
          <template #default="scope">
            <div v-if="scope.row.department && scope.row.department.length > 0">
              <span v-for="(item, index) in scope.row.department">
                <ww-user :openid="item" type="departmentName"></ww-user>
                <span v-if="index !== scope.row.department.length - 1">、</span>
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column property="teamId" label="所属团队">
          <template #default="scope">
            {{ getBelongToTeamName(scope.row.userid) }}
          </template>
        </el-table-column>
        <el-table-column property="roleIds" label="平台角色">
          <template #default="scope">
            {{ getRolesName(scope.row.roleIds) }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" class-name="el-table-fixed-column--shadow" label="操作" width="120">
          <template #default="scope">
            <el-button text class="table-action-btn" type="primary" size="small" @click="edit(scope.row)">详情</el-button>
            <el-button text class="no-ml table-action-btn" type="primary" size="small" @click="edit(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </layout-main>
    <layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="currentPage" @handle-size-change="onSizeChange" @handle-current-change="onCurrentChange" />
    </layout-item>
  </my-layout>
</template>

<script setup>
import { onMounted, ref, inject } from "vue";
import { ElMessage } from "element-plus";
import WwUser from "@/components/ww-user/index.vue";
import { useRouter } from "vue-router";
import { memberStore } from "@/store/member";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import EmptyData from "@/components/empty-data.vue";
import { getCorpMember, getAllTeamByCorp, getRolesList } from "@/api/corp";
const { memberInfo } = memberStore();
const router = useRouter();
const list = ref([]);
const total = ref(0);
const pageSize = ref(10);
const currentPage = ref(1);
const loading = ref(true);
const role = ref(0);
const roles = ref([]);
const teams = ref([]);
onMounted(async () => {
  await getRoleList();
  await getTeams();
  getList();
});
function onSizeChange(e) {
  pageSize.value = e;
  getList();
}
function onCurrentChange(e) {
  currentPage.value = e;
  getList();
}
const visible = ref(false);
const currentRow = ref({});
function showDetail(row) {
  currentRow.value = row;
  visible.value = true;
}
let params = {};
function change(val) {
  params = val;
  getList();
}
async function getList() {
  let params = {
    corpId: localStorage.getItem("corpId"),
  };
  if (role.value) {
    params["roleIds"] = role.value;
  }
  let { success, data } = await getCorpMember(params, pageSize.value, currentPage.value);
  loading.value = false;
  if (success) {
    list.value = data.data;
  }
}
// 获取角色列表
async function getRoleList() {
  let { success, data } = await getRolesList();
  if (success) roles.value = data.data;
}
// 获取团队列表
async function getTeams() {
  const { success, data } = await getAllTeamByCorp();
  if (success) {
    teams.value = data.data;
  }
}
// 更加团队列表 获取改员工在那个团队
function getBelongToTeamName(userId) {
  let belongTeams = teams.value.filter((item) => {
    return item.memberList.indexOf(userId) !== -1;
  });
  let nameList = belongTeams.map((item) => item.name);
  return nameList.length > 0 ? nameList.join("、 ") : "";
}
function getRolesName(list) {
  if (!list) return;
  let roleList = roles.value.filter((item) => {
    return list.indexOf(item._id) !== -1;
  });
  let nameList = roleList.map((item) => item.roleName);
  return nameList.length > 0 ? nameList.join("、 ") : "";
}

function selectRole() {
  getList();
}

function edit(member) {
  let item = {
    member,
    teams: teams.value,
    roles: roles.value,
  };
  localStorage.setItem("MEMBERDETIAL", JSON.stringify(item));
  router.push({ name: "MEMBERMANAGEDETIAL" });
}
</script>

<style scoped>
.p-15 {
  padding: 15px;
}

.query-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
}

.table-action-btn {
  padding-left: 0;
}

.no-ml {
  margin-left: 0;
}
</style>
