<template>
  <div class="px-15px py-6px" :class="idx > 0 ? 'mt-15px' : ''" border-bottom>
    <el-form-item :class="required ? 'is-required' : ''" :label="name" label-width="auto">
    </el-form-item>
    <div class="py-6px flex flex-wrap break-all" :class="hasOther ? 'border-bottom pb-12px' : ''">
      <div v-for="i in list" :key="i.text"
        class="border-1 p-6px flex items-center justify-center rounded-4px range-item text-14px"
        :class="i.selected ? 'bg-blue-100 border-blue-100 text-blue-500' : ''" @click="toggle(i.text, i.selected)">
        {{ i.text }}
      </div>
    </div>
    <div v-if="hasOther" class="pb-6px">
      <el-input v-model="other" type="text" class="w-full" placeholder="请补充其它内容" :maxlength="50"
        @input="changeOther($event)" />
    </div>
  </div>
</template>
<script setup>
import { computed, inject, onMounted, ref } from 'vue';

const NoList = ['无'];
const addRule = inject('addRule')

const props = defineProps({
  form: { type: Object, default: () => ({}) },
  name: { type: String, default: '' },
  range: { type: Array, default: () => [] },
  required: { type: Boolean, default: false },
  title: { type: String, default: '' },
  otherList: { type: Array, default: () => ['其他'] },
  idx: { type: Number, default: 0 }
})

const emits = defineEmits(['change'])
function change(value) { emits('change', { title: props.title, value }) }

const value = computed(() => Array.isArray(props.form[props.title]) ? props.form[props.title] : []);
const hasOther = computed(() => value.value.some(i => inArray(i, props.otherList)));
const hasNo = computed(() => value.value.some(i => inArray(i, NoList)));
const list = computed(() => props.range.map(i => ({
  text: i,
  selected: value.value.some(a => a === i)
})))

const other = ref('');
onMounted(() => {
  const [text = ''] = value.value.filter(i => !props.range.some(a => a === i));
  other.value = text;
  addRule(props.title, check)
})

function toggle(text, selected) {
  const clearText = inArray(text, NoList);
  if (clearText && !selected) { // 选择无 清除其他选项
    other.value = '';
    change([text]);
    return
  }
  let list = [...value.value];
  if (selected) { list = list.filter(i => i !== text) } // 取消选中
  else {
    list.push(text);
    if (hasNo.value) list = list.filter(i => !inArray(i, NoList)); // 已选择 “无” 选项 清除选中的 “无” 选项
  }; // 选择非无选项
  const val = props.range.filter(i => list.some(a => a === i)).map(i => i); // 修改后的值 必须是 range的子项；
  if (list.some(i => inArray(i, props.otherList)) && other.value.trim() !== '') { // 选中 “其他” 并且 补充的有内容 补上
    val.push(other.value.trim())
  }
  change(val)
}

function changeOther(e) {
  const string = e;
  let list = [...value.value];
  const val = props.range.filter(i => list.some(a => a === i)).map(i => i); // 修改后的值 必须是 range的子项；
  if (!val.some(i => inArray(i, props.otherList))) return; // 没有 “其他” 选项 不做处理
  if (string.trim()) change([...val, string.trim()]);
  else change(val)
}

function inArray(text, list) {
  return list.some(i => i === text);
}

function check() {
  if (value.value.length === 0) {
    if (props.required) return `请选择${props.name}`;
    return true;
  } else {
    const list = value.value.filter(i => !inArray(i, props.range) && i !== other.value);
    if (list.length === 0) return true;
    return `${props.name} 【${list[0]}】无效`
  }
}

</script>

<style lang="scss" scoped>
.range-item {
  width: 23%;
}

.range-item:nth-of-type(n + 5) {
  margin-top: 12px;
}

.range-item:nth-of-type(4n + 1),
.range-item:nth-of-type(4n + 2),
.range-item:nth-of-type(4n + 3) {
  margin-right: 2.5%;
}
</style>
