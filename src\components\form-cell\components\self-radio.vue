<template>
  <div v-if="isRead">
    {{ value && Array.isArray(value) ? item.value.join("、") : value }}
  </div>
  <el-radio-group v-else :model-value="value" @update:model-value="change($event)">
    <el-radio :label="label" v-for="label in item.range">{{ label }}</el-radio>
  </el-radio-group>
</template>
<script setup>
import { watch, ref } from "vue";
const $emit = defineEmits(["change"]);
const props = defineProps({
  item: {
    type: Object,
    default: {},
  },
  value: {
    type: String,
    default: "",
  },
  isRead: {
    type: Boolean,
    default: false,
  },
});
function change(value) {
  $emit("change", {
    title: props.item.title,
    value,
  });
}
</script>

<style lang="scss"></style>