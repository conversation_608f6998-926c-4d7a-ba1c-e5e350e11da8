<template>
  <el-table-column label="建档时间" prop="createTime" :width="160">
    <template #default="{row}">
      <td-wrapper>{{ getTime(row) }}</td-wrapper>
    </template>
  </el-table-column>
</template>
<script setup>
import dayjs from 'dayjs';
import tdWrapper from './td-wrapper.vue';

function getTime(customer) {
  return customer.createTime ? dayjs(customer.createTime).format("YYYY-MM-DD HH:mm") : "";
}
</script>
