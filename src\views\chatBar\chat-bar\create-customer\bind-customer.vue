<template>
  <div v-if="visible" class="h-screen w-screen fixed inset-0 z-99">
    <page-wrapper @back="close()">
      <template #header>
        <div class="flex-shrink-0 mx-15px transition-all duration-300" :class="text.length || list.length ? 'pt-15px' : 'pt-2/5'">
          <div class="border rounded-4px border-gray-200 py-10px px-15px bg-white shadow-lg">
            <input v-model="text" class="text-14px w-full border-none outline-none" placeholder="请输入客户身份证号、手机号、档案编号查询" maxlength="20" />
          </div>
          <div class="flex mt-15px">
            <img class="w-36px h-36px" src="@/assets/icons/icon-inspiration.svg" />
            <div class="text-14px px-6px text-gray-500 leading-18px pt-6px">请在上方搜索客户，客户如果在院内已有建档信息，可直接绑定院内档案。</div>
          </div>
        </div>
      </template>
      <div v-loading="loading" class="px-15px pt-20px pb-40px">
        <div v-for="(item, i) in list" class="flex items-center rounded px-15px shadow-lg bg-white" :class="i > 0 ? 'mt-12px' : ''">
          <div class="w-0 flex-grow mr-10px">
            <div class="flex items-center py-12px">
              <img v-if="getGenderAction(item.idCard)" class="flex-shrink-0 w-20px h-20px" src="@/assets/icons/icon-male.svg" />
              <img v-else class="flex-shrink-0 w-20px h-20px" src="@/assets/icons/icon-female.svg" />
              <div class="text-16px font-semibold mx-6px break-all">{{ item.name }}</div>
              <div class="text-12px">
                <span class="mr-6px">|</span>
                <span>{{ item.mobile }}</span>
              </div>
            </div>
            <div class="flex items-center text-14px pb-12px">
              <div class="flex-shrik-0 text-gray-500">身份证号：</div>
              <div class="break-all">{{ item.idCard }}</div>
            </div>
            <div class="flex items-center text-14px pb-12px">
              <div class="flex-shrik-0 text-gray-500">档案编号：</div>
              <div class="break-all">{{ item.customerNumber }}</div>
            </div>
          </div>
          <div class="bg-blue-500 text-white text-12px px-14px py-6px rounded" @click="bindCustomer(item)">绑定</div>
        </div>
        <div v-if="text && !loading" class="pt-24px flex items-center justify-center">
          <div class="text-14px text-gray-500">未找到客户可以：</div>
          <div class="border border-blue-500 px-14px pt-7px pb-6px text-blue-500 rounded text-12px" @click="addCustomer()">新建客户</div>
        </div>
      </div>
    </page-wrapper>
  </div>
  <customer-repeat :visible="repeatVisible" :customer="customer" :width="300" @acceptCustomer="acceptCustomer" :repeatType="repeatType" @close="repeatVisible = false"></customer-repeat>
  <edit-basic-info :visible="editVisible" :customer="customer" @refresh="refresh" @close="closeEidtModal" :externalUserId="externalUserId" />
</template>
<script setup>
import { nextTick, ref, watch } from "vue";
import validate from "@/utils/validate";
import { useDebounceFn } from "@vueuse/core";
import pageWrapper from "../../components/page-wrapper.vue";
import { getHisCustomerArchive } from "@/api/customerHisSync";
import { addMember } from "@/api/member";
import { teamStore } from "@/store/team";
import customerRepeat from "@/views/member/components/customer-repeat/index";
import repeatComponentApi from "@/views/member/components/customer-repeat/api";
import editBasicInfo from "../chat-header/edit-basic-info.vue";
import useModal from "@/hooks/useModal";
import { ElMessage } from "element-plus";
const emits = defineEmits(["close", "addCustomer", "success"]);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  externalUserId: {
    type: String,
    default: "",
  },
});
const { repeatVisible, customer, getCustomer } = repeatComponentApi();
const text = ref("");
const list = ref([]);
const loading = ref(false);
const debouncedFn = useDebounceFn(handleChange, 750);
const { close: closeEidtModal, show: showEditModal, visible: editVisible } = useModal(560); // 选择客户弹窗
function getGenderAction(idCard) {
  return idCard && validate.getGender(idCard) === "男" ? true : false;
}
function refresh() {
  emits("success");
}
async function handleChange() {
  let searchName = text.value.trim();
  let query = {
    idNo: searchName,
  };
  if (validate.isMobile(searchName)) {
    query = {
      mobile: searchName,
    };
  } else if (validate.isChinaId(searchName)[0]) {
    query = {
      idCard: searchName,
    };
  }
  let { success, data } = await getHisCustomerArchive(query);
  if (success) {
    list.value = data.list;
  } else {
    list.value = [];
  }
  loading.value = false;
}

async function addCustomer() {
  close();
  await nextTick();
  emits("addCustomer");
}

async function acceptCustomer() {
  if (customer.value.externalUserId && customer.value.externalUserId !== props.externalUserId) {
    ElMessage.error("该档案已绑定其他用户");
    return;
  }
  const teamId = Array.isArray(customer.value.teamId) ? customer.value.teamId : customer.value.teamId ? [customer.value.teamId] : [];
  if (!teamId.includes(teamStore().currentTeamId)) customer.value.teamId = [...teamId, teamStore().currentTeamId];
  const personResponsibles = customer.value.personResponsibles || [];
  let contains = personResponsibles.some((item) => item.corpUserId === localStorage.getItem("userId") && item.teamId === teamStore().currentTeamId);
  if (!contains) {
    personResponsibles.push({
      corpUserId: localStorage.getItem("userId"),
      teamId: teamStore().currentTeamId,
    });
  }
  customer.value.personResponsibles = personResponsibles;
  if (!customer.value.externalUserId) customer.value.externalUserId = props.externalUserId;
  if (!customer.value.relationship) customer.value.relationship = "本人";
  customer.value.isConnectHis = true;
  repeatVisible.value = false;
  emits("close");
  showEditModal();
}

async function bindCustomer(params) {
  if (loading.value) return;
  loading.value = true;
  const payLoad = {
    ...params,
    corpId: localStorage.getItem("corpId"),
    teamId: teamStore().currentTeamId ? [teamStore().currentTeamId] : [],
    creator: localStorage.getItem("userId"),
    externalUserId: props.externalUserId,
    isConnectHis: true,
    relationship: "本人",
    personResponsibles: [
      {
        corpUserId: localStorage.getItem("userId"),
        teamId: teamStore().currentTeamId,
      },
    ],
  };
  const { success } = await addMember(payLoad);
  loading.value = false;
  if (success) {
    emits("close");
    emits("success");
  } else {
    let item = await getCustomer({ value: params.idCard, title: "idCard" });
    customer.value = Object.assign(params, item);
    if (customer.value["idCard"]) repeatVisible.value = true;
  }
}
watch(text, () => {
  if (!text.value) {
    list.value = [];
    return;
  }
  if (!loading.value) loading.value = true;
  debouncedFn();
});
watch(
  () => props.visible,
  (val) => {
    if (val) {
      text.value = "";
      list.value = [];
      customer.value = {};
    }
  }
);

function close() {
  emits("close");
}
</script>
