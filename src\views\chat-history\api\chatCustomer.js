import { selectdUserId } from "./authMember.js";
import { getCorpMemberChatList } from "@/api/sessionArchive.js";
import { ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { watchDebounced } from "@vueuse/core";
const chatCustomerList = ref({});
const pageSize = ref(30);
const currentPage = ref(1);
const selectedCustomer = ref({});
const total = ref(0);
const chatLoading = ref(false);
async function getChatUserList() {
  const { success, data, message } = await getCorpMemberChatList({ pageSize: pageSize.value, page: currentPage.value, memberUserId: selectdUserId.value });
  chatLoading.value = false;
  if (success) {
    total.value = data.total;
    if (currentPage.value === 1) chatCustomerList.value = data.data;
    else chatCustomerList.value = chatCustomerList.value.concat(data.data);
  } else {
    total.value = 0;
    chatCustomerList.value = [];
    selectedCustomer.value = {};
    ElMessage.error(message);
  }
}
watchDebounced(
  () => selectdUserId.value,
  async (val) => {
    chatCustomerList.value = [];
    if (val) {
      currentPage.value = 1;
      chatLoading.value = true;
      await getChatUserList();
      getSelectdCustomer(chatCustomerList.value[0]);
    }
  },
  { immediate: true, debounce: 500, maxWait: 5 * 1000 }
);
async function handleScroll(event) {
  const { target } = event;
  if (target.scrollHeight - target.scrollTop === target.clientHeight) {
    if (chatCustomerList.value.length >= total.value) return;
    currentPage.value++;
    chatLoading.value = true;
    await getChatUserList();
  }
}

function getSelectdCustomer(i) {
  selectedCustomer.value = i || {};
}

export { chatCustomerList, pageSize, currentPage, handleScroll, getSelectdCustomer, selectedCustomer, chatLoading };
