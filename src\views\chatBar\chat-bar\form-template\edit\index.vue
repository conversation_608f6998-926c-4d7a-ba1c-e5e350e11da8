<template>
  <el-form require-asterisk-position="right" :label-width="80" label-position="left">
    <template v-for="(item, idx) in items" :key="item.title">
      <component v-if="FormTitleComponent[item.title] || FormComponent[item.type]" :is="FormTitleComponent[item.title] || FormComponent[item.type]" v-bind="item" :idx="idx" class="bg-white" :form="form" @change="change" />
      <form-cell class="bg-white" v-else v-bind="item" :form="form" />
      <!-- <div class="px-15px py-12px" v-else>{{ item.name }} -- {{ item.title }} -- {{ item.type }}</div> -->
    </template>
  </el-form>
</template>
<script setup>
import { defineAsyncComponent, ref, provide } from "vue";
import verify from "./verify";
const formCell = defineAsyncComponent(() => import("./form-cell.vue"));

provide("addRule", addRule);

const props = defineProps({
  form: { type: Object, default: () => ({}) },
  items: { type: Array, default: () => [] },
  rule: { type: Object, default: () => ({}) },
});
const emit = defineEmits(["change"]);
const FormTitleComponent = {
  surgicalHistory: defineAsyncComponent(() => import("./form-surgical-history.vue")),
};
const FormComponent = {
  BMI: defineAsyncComponent(() => import("./form-bmi.vue")),
  corpProject: defineAsyncComponent(() => import("./form-corp-project.vue")),
  customerSource: defineAsyncComponent(() => import("./form-customerSource.vue")),
  customerStage: defineAsyncComponent(() => import("./form-stage.vue")),
  date: defineAsyncComponent(() => import("./form-date.vue")),
  diagnosis: defineAsyncComponent(() => import("./form-diagnosis.vue")),
  files: defineAsyncComponent(() => import("./form-upload.vue")),
  input: defineAsyncComponent(() => import("./form-input.vue")),
  multiSelectAndOther: defineAsyncComponent(() => import("./form-mult.vue")),
  radio: defineAsyncComponent(() => import("./form-radio.vue")),
  reference: defineAsyncComponent(() => import("./form-reference.vue")),
  region: defineAsyncComponent(() => import("./form-region/form-region.vue")),
  select: defineAsyncComponent(() => import("./form-select.vue")),
  tag: defineAsyncComponent(() => import("./form-tags.vue")),
  textarea: defineAsyncComponent(() => import("./form-textarea.vue")),
  wwUser: defineAsyncComponent(() => import("./form-ww-user.vue")),
  selectMobile: defineAsyncComponent(() => import("./form-select-mobile.vue")),
  selectWwuser: defineAsyncComponent(() => import("./form-select-wwuser.vue")),
  selfMultipleDiseases: defineAsyncComponent(() => import("./self-multiple-diseases.vue")),
  bloodPressure: defineAsyncComponent(() => import("./form-blood-pressure.vue")),
  positiveFind: defineAsyncComponent(() => import("./form-positive-find/form-positive-find.vue")),
  runTime: defineAsyncComponent(() => import("./form-run-time.vue")),
  selectAndOther: defineAsyncComponent(() => import("./form-select-other.vue")),
  selectAndImage: defineAsyncComponent(() => import("./form-select-image.vue")),
  // selectMobile: defineAsyncComponent(() => import('@/components/form-select-mobile.vue')),
};

const componentRule = {};
function addRule(title, fn) {
  if (typeof title === "string" && title && typeof fn === "function") {
    componentRule[title] = fn;
  } else {
    console.warn("addRule 参数错误, title必须是字符串 fn必须是函数");
  }
}

const beforeSave = {}; // 表单保存前 组件内部的保存前需要执行的方法 （目前用于 文件上传组件， 在保存前将文件上传服务器 ）
provide("addBeforeSave", addBeforeSave); // 提供给组件 方便组件注册保存前处理函数 （目前用于 文件上传组件 ）
function addBeforeSave(title, fn) {
  if (title && props.items.some((i) => i.title === title) && typeof fn === "function") {
    beforeSave[title] = fn;
  }
}

function change(data) {
  emit("change", data);
}

const loading = ref(false);
async function handleBeforeSave() {
  if (loading.value) return;
  loading.value = true;
  for (let item of props.items.filter((i) => !i.hidden)) {
    if (beforeSave[item.title] && typeof beforeSave[item.title] === "function") {
      const res = await beforeSave[item.title]();
      if (res === false) {
        loading.value = false;
        return false;
      }
    }
  }
  loading.value = false;
  return true;
}

defineExpose({
  beforeSave: handleBeforeSave,
  verify: () =>
    verify(
      props.items.filter((i) => !i.hidden),
      { ...props.rule, ...componentRule },
      props.form
    ),
});
</script>
<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-bottom: 0;
}
</style>
