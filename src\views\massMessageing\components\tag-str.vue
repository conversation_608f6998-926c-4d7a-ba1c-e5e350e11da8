<template>
  <div :title="names">
    <span class="inline-block max-w-1/2 truncate text-12px">{{ names }}</span>
    <span v-if="names" class="inline-block text-12px">的客户</span>
  </div>
</template>
<script setup>
import { computed, ref, watch } from 'vue';
const props = defineProps({
  tags: { type: Array, default: () => [] }
})
const names = computed(() => {
  const list = Array.isArray(props.tags) ? props.tags : [];
  return list.filter(i => i && i.name).map(i => ` "${i.name}" `).join('且');
});


</script>
<style lang="scss" scoped></style>
