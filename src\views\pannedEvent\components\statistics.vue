<template>
  <div class="mx-20px mt-20px">
    <div class="event-top-cell" border-bottom>
      <div class="event-top-cell_left">
        <div class="flex">
          <div title-bar>{{ detial.pannedEventName }}</div>
          <el-text class="ml-10px" type="primary" v-if="detial.executionSatus">
            启用
          </el-text>
          <el-text class="ml-10px" type="danger" v-else> 停用 </el-text>
        </div>
        <div class="text-14px mb-10px mt-15px">
          {{ dayjs(detial.createTime).format("YYYY-MM-DD HH:mm") }} 由{{
            detial.teamName
          }}<ww-user :openid="detial.pannedCreateMember"></ww-user>创建
        </div>
      </div>
      <div class="ml-50px">
        <div class="flex ">
          <div
            class="flex al-center mr-20px"
            v-if="detial.tagsName && detial.tagsName.length > 0"
          >
            <div class="c-666 text-14px h-10px">标签筛选:</div>
            <div class="ml-10px">
              <el-tag
                v-for="(item, idx) in detial.tagsName"
                :key="idx"
                class="mr-2 h-20px"
                type="primary"
                effect="dark"
              >
                {{ item }}
              </el-tag>
            </div>
          </div>
          <div
            class="flex al-center"
            v-if="detial.excludeTagsName && detial.excludeTagsName.length > 0"
          >
            <div class="c-666 text-14px h-10px">标签删除:</div>
            <div class="ml-10px">
              <el-tag
                v-for="(item, idx) in detial.excludeTagsName"
                :key="idx"
                class="mr-2 h-20px"
                type="primary"
                effect="dark"
              >
                {{ item }}
              </el-tag>
            </div>
          </div>
        </div>

        <div class="flex mt-15px">
          <div class="c-666 text-14px">客户阶段:</div>
          <div class="ml-10px c-666 text-14px">
            {{ customerType }}
          </div>
        </div>
      </div>
      <div main-color class="text-14px stc-title">截止昨日统计</div>
    </div>
    <div class="flex felx-between mt-20px">
      <!-- <el-popover placement="right" :width="400">
        <template #reference>
          <div class="flex cursor-pointer" style="align-items: center;">
            <span class="text-14px">数据统计说明</span>
            <img src="@/assets/gantanhaozhong.png" class="w-20px h-20px" />
          </div>
        </template>
        <div>
          <div>
            <li>当前在计划客户数：已加入计划还未到执行时间的客户数</li>
            <li>已下发任务数：已经开始执行的任务数</li>
            <li>已执行任务数：已成功处理的任务数</li>
            <li>已反馈任务数：客户已阅读或已提交数据的任务数</li>
            <li>任务执行完成率：已执行任务数/已下发任务数*100%</li>
            <li>任务信息反馈率：已反馈任务数/已执行任务数*100%</li>
          </div>
        </div>
      </el-popover> -->
    </div>
    <div class="flex felx-between">
      <div class="flex statisticsItem px-15px">
        <div class="item">
          <div class="num">
            {{ pannedEventStatistics.distributedTaskCount || 0 }}
          </div>
          <div class="title">已下发任务数</div>
        </div>
        <div class="item">
          <div class="num">
            {{ pannedEventStatistics.executedTaskCount || 0 }}
          </div>
          <div class="title">已执行任务数</div>
        </div>
        <div class="item">
          <div class="num">
            {{ pannedEventStatistics.feedbackTaskCount || 0 }}
          </div>
          <div class="title">已反馈任务数</div>
        </div>
        <div class="item">
          <div class="num">{{ executedTaskCompletionRate }}%</div>
          <div class="title">任务执行完成率</div>
        </div>
        <div class="item">
          <div class="num">{{ feedbackTaskCompletionRate }}%</div>
          <div class="title">任务信息反馈率</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { computed, reactive, ref, watch } from "vue";
import { getCustomerType } from "@/api/member";
import WwUser from "@/components/ww-user/index.vue";
import dayjs from "dayjs";
const customerList = ref([]);
const props = defineProps({
  detial: { type: Object, default: {} },
  pannedEventResult: { type: Object, default: {} },
});
const detial = computed(() => {
  return props.detial;
});
const pannedEventStatistics = computed(() => {
  return props.pannedEventResult;
});
const executedTaskCompletionRate = computed(() => {
  const { distributedTaskCount, executedTaskCount } =
    pannedEventStatistics.value;
  if (!distributedTaskCount || !executedTaskCount) {
    return "0.0";
  }
  return (executedTaskCount / distributedTaskCount).toFixed(1) * 100;
});

const feedbackTaskCompletionRate = computed(() => {
  const { distributedTaskCount, feedbackTaskCount } =
    pannedEventStatistics.value;
  if (!distributedTaskCount || !feedbackTaskCount) {
    return "0.0";
  }
  return (feedbackTaskCount / distributedTaskCount).toFixed(1) * 100;
});

customerTypeAction();

const customerType = computed(() => {
  const item = customerList.value.filter(
    (item) => item.type === detial.value.customerType
  )[0];
  return item ? item["name"] : "";
});

async function customerTypeAction() {
  let { data, success } = await getCustomerType();
  if (success) {
    customerList.value = data.data;
  }
}
</script>
<style scoped lang="scss">
.event-top-cell {
  display: flex;
  position: relative;
  &_left {
    position: relative;
  }
  &_left::after {
    content: "";
    position: absolute;
    top: 5px;
    right: -30px;
    height: 50px;
    border-left: 1px dashed #bbb;
  }
}
.c-666 {
  color: #666;
}

.felx-between {
  justify-content: space-between;
}

.statisticsItem {
  justify-content: space-between;
  flex: 1;
  padding-top: 10px;
  .item {
    display: flex;
    flex-direction: column;
    align-items: center;

    .title {
      font-size: 12px;
    }

    .num {
      font-size: 20px;
      font-weight: 600;
      color: #006eff;
    }
  }
}

.al-center {
  align-items: center;
}

.stc-title {
  position: absolute;
  right: 0;
  bottom: 10px;
  font-size: 14px;
}
</style>
