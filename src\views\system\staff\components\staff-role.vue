<template>
  <el-dialog :model-value="visible" title="选择平台角色" :width="600" @close="close">
    <template #header="{ titleClass }">
      <span class="pb-5px block" :class="titleClass">选择平台角色</span>
    </template>
    <div style="height: 50vh" flex items-center>
      <my-layout class="w-1/2" border-right>
        <layout-item>
          <div flex justify-between items-center border-bottom class="pr-15px">
            <div font-14 font-semibold py-10 px-15 color-normal>角色</div>
            <el-tooltip class="box-item" effect="dark" content="如需修改角色权限，请前往角色与权限设置功能处理" placement="bottom">
              <el-icon color="#909399">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </div>
        </layout-item>
        <layout-main>
          <div v-for="item in showRoleList" px-15>
            <div class="flex mt-10px pointer py-10px px-5px" items-center
              :class="selectRole._id === item._id ? 'check-active' : ''" @click="selectRoleAction(item)">
              <img src="@/assets/check-active.png" class="w-18px h-18px pointer mr-10px"
                v-if="myRoles.some((i) => i === item._id)" @click="removeRole(item._id)" />
              <img src="@/assets/check.png" class="w-18px h-18px pointer mr-10px" v-else @click="addRole(item._id)" />
              <div>
                {{ item.roleName }}
              </div>
            </div>
          </div>
        </layout-main>
        <layout-item>
          <div text-center pt-10 common-shadow--r>
            <el-button type="primary" @click="toAddNewRose">新增角色</el-button>
          </div>
        </layout-item>
      </my-layout>
      <my-layout class="w-1/2" border-right>
        <layout-item>
          <div font-14 font-semibold py-10 px-15 border-bottom color-normal>功能权限</div>
        </layout-item>
        <layout-main>
          <el-tree :data="menus" node-key="id" :show-checkbox="false" :expand-on-click-node="false"
            :default-expand-all="true" :default-checked-keys="menusProps" :props="defaultProps" />
        </layout-main>
      </my-layout>
    </div>
    <template #footer>
      <div text-center>
        <el-button @click="close()">取消</el-button>
        <el-button :loading="saveLoading" type="primary" @click="saveRole()">保存</el-button>
      </div>
    </template>
  </el-dialog>
  <authority-detial ref="authorityDetialRef" @on-add-role="onAddRole"></authority-detial>
</template>
<script setup>
import { computed, onMounted, ref, watch } from "vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import { getMenus } from "@/api/system";
import { getRolesList } from "@/api/corp";
import authorityDetial from "../../components/authorityDetial";
const authorityDetialRef = ref();
const roleList = ref([]);
const commonMenus = ref([]);
const selectRole = ref({});
const myRoles = ref([]);
const emit = defineEmits(["close", "addRole"]);
const props = defineProps({
  roleIds: { type: Array, default: () => [] },
  visible: { type: Boolean, default: false },
  member: { type: Object, default: () => ({}) },
});
const defaultProps = {
  children: "children",
  label: "label",
};
let menusProps = ref([]);
getList();
getMenus();
getMenusData();
watch(
  () => props.visible,
  (n) => {
    if (n) {
      myRoles.value = Array.isArray(props.roleIds) ? [...props.roleIds] : [];
    }
  },
  { immediate: true }
);

const menus = computed(() => {
  const arr = findByIdWithChildren(commonMenus.value, menusProps.value);
  return buildTree(arr, 0);
});
function selectRoleAction(item) {
  selectRole.value = item;
  menusProps.value = item.menuList;
}
async function getList() {
  let { data, success } = await getRolesList();
  if (success) {
    roleList.value = data.data;
    selectRole.value = data.data[0];
    menusProps.value = roleList.value[0].menuList;
  }
}

const showRoleList = computed(() => {
  // let list = roleList.value.filter((i) => i.roleId !== "memberLeader" && i.roleId !== "member");
  let list = roleList.value;
  if (props.member.roleType === "superAdmin") {
    list = list.filter((i) => i.roleId !== "admin");
  }
  return list;
});
async function getMenusData() {
  let { data, success } = await getMenus(localStorage.getItem("corpId"));
  if (success) {
    commonMenus.value = data.data;
  }
}
function onAddRole() {
  getList();
}
function buildTree(rawData, parentId) {
  const result = [];
  rawData.forEach((item) => {
    if (item.parentId === parentId) {
      const children = buildTree(rawData, item.menuId);
      result.push({
        id: item.menuId,
        label: item.menuName,
        children: children.length ? children : [],
      });
    }
  });
  return result;
}
function findByIdWithChildren(data, ids) {
  const menus = data.filter(i => ids.includes(i.menuId));
  const menuIds = menus.reduce((arr, menu) => {
    if (menu.parentId) arr.push(menu.parentId);
    arr.push(menu.menuId);
    return arr;
  }, []);
  const uniqueMenuIds = [...new Set(menuIds)];
  return data.filter(i => uniqueMenuIds.includes(i.menuId));
}
function saveRole() {
  emit("addRole", myRoles.value, roleList.value);
}
function addRole(id) {
  myRoles.value.push(id);
}
function removeRole(id) {
  myRoles.value = myRoles.value.filter((i) => i !== id);
}
function toAddNewRose() {
  authorityDetialRef.value.openDialog("add", []);
}
function close() {
  emit("close");
}
</script>
<style scoped>
.check-active {
  background-color: #d6e4f6;
  border-radius: 4px;
}
</style>