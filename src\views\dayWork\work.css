.work-cell {
  padding: 10px;
  border-top: 1px solid #eee;
  border-right: 1px solid #eee;
  overflow: hidden;
  position: relative;
}

.work-cell-th {
  background-color: #ecf4ff;
  /* color: #fff; */
  font-weight: bold;
}

.work-cell-today {
  text-align: center;

  width: 20px;
  height: 20px;
  line-height: 18px;
  background-color: #fca104;
  font-size: 12px;
  color: #fff;
  border-radius: 50%;
}

.work-cell-item {
  position: relative;
  z-index: 2;
  padding: 0 10px;
  border-radius: 4px;
  background: #ecf4ff;
}

.work-cell-bg {
  position: absolute;
  inset: 0;
  z-index: 1;
  cursor: pointer;
}

.work-cell:nth-of-type(1) {
  border-left: 1px solid #eee;
}

.work-tr:last-of-type .work-cell {
  border-bottom: 1px solid #eee;
}

.work-cell--active {
  color: #006eff;
}