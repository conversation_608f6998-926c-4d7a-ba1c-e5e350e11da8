<template>
  <div class="table-block bg-white">
    <div class="h-40px flex items-center px-15px">
      <div class="w-0 flex-grow truncate text-16px font-semibold">团队服务概况</div>
      <div class="flex flex-shrink-0 text-14px">
        <div class="ml-10px text-blue-500 cursor-pointer">本月</div>
        <div class="ml-10px cursor-pointer">上月</div>
        <div class="ml-10px cursor-pointer">全部</div>
      </div>
    </div>
    <div style="height: calc(100% - 40px);">
      <el-table stripe :data="teamData" height="100%">
        <el-table-column property="rank" label="排名" width="60px" />
        <el-table-column property="name" label="团队" />
        <el-table-column property="customerCount" label="新增客户数" width="100px" />
        <el-table-column property="serviceCount" label="服务记录" width="80px" />
      </el-table>
    </div>

  </div>
  <div class="table-block mt-10px bg-white">
    <div class="h-40px flex items-center px-15px">
      <div class="w-0 flex-grow truncate text-16px font-semibold">员工服务概况</div>
      <div class="flex flex-shrink-0 text-14px">
        <div class="ml-10px text-blue-500 cursor-pointer">本月</div>
        <div class="ml-10px cursor-pointer">上月</div>
        <div class="ml-10px cursor-pointer">全部</div>
      </div>
    </div>
    <div style="height: calc(100% - 40px);">
      <el-table stripe :data="employeeData" height="100%">
        <el-table-column property="rank" label="排名" width="60px" />
        <el-table-column property="name" label="员工" />
        <el-table-column property="customerCount" label="客户数" width="100px" />
        <el-table-column property="serviceCount" label="服务记录" width="80px" />
      </el-table>
    </div>
  </div>

</template>
<script setup>
import { ref } from 'vue';
const teamData = ref([{ name: '团队1', rank: 1 }])
const employeeData = ref([{ name: '员工1', rank: 1 }])
</script>
<style lang="scss" scoped>
.table-block {
  height: calc(50% - 5px);
}

:deep(.el-table__empty-block) {
  display: none;
}
</style>
