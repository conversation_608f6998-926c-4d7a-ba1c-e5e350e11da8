<template>
  <div color-normal class="flex flex-col h-full text-14px bg-white rounded w-300px">
    <div class="flex-shrink-0 flex justify-between px-15px py-12px border-b border-gray-200">
      <div class="font-semibold text-15px" @click="scrollTo()">
        <ww-user :openid="selectdUserId" v-if="selectdUserId"></ww-user>
        <span>的客户</span>
      </div>
    </div>
    <div class="flex-grow relative">
      <div class="absolute inset-0" v-loading="chatLoading">
        <empty-data v-if="chatCustomerList.length == 0" :top="200" title="暂无客户" padding="0 0 15px" text-top="10px" :image-width="120"></empty-data>
        <RecycleScroller v-else ref="scrollerRef" class="h-full yc-scrollbar" :items="chatCustomerList" :item-size="40" key-field="customerUserId" v-slot="{ item }" @scroll="handleScroll">
          <div class="flex px-15px py-10px cursor-pointer rounded" :class="[item.customerUserId === selectedCustomer.customerUserId ? 'bg-blue-500 text-white' : 'hover:text-blue-500']" @click="getSelectdCustomer(item)">
            <!-- <img class="flex-shrink-0 w-48px h-48px rounded mr-10px" :src="'https://picsum.photos/200/200?random=@@' + item" /> -->
            <div class="flex-grow w-0">
              <div class="flex items-center h-20px">
                <div class="flex-shrink text-14px font-semibold truncate">
                  <!-- {{ item.externalUserName }} -->
                  <wechat-nick-name :wechat-id="item.customerUserId" />
                </div>
                <div class="flex-shrink-0 ml-5px text-12px text-[#66CB70]">@微信</div>
                <div class="flex-shrink-0 ml-auto pl-5px text-12px">{{ dayjs(item.sendTime * 1000).format("YYYY-MM-DD HH:mm") }}</div>
              </div>
              <!-- <div class="truncate mt-4px" v-if="item.memberList">
                <div v-for="i in item.memberList" class="inline-block text-12px h-20px px-5px rounded leading-18px" :class="[i > 1 ? 'ml-5px' : '', item === 1 ? 'bg-white text-blue-500' : 'border border-blue-500 text-blue-500']">{{ i.name }}</div>
              </div> -->
            </div>
          </div>
        </RecycleScroller>
      </div>
    </div>
  </div>
</template>
<script setup>
import { computed, ref, watch } from "vue";
import dayjs from "dayjs";
import EmptyData from "@/components/empty-data.vue";
import { RecycleScroller } from "vue-virtual-scroller";
import "vue-virtual-scroller/dist/vue-virtual-scroller.css";
import { chatCustomerList, handleScroll, chatLoading, selectedCustomer, getSelectdCustomer } from "./api/chatCustomer";
import { selectdUserId } from "./api/authMember.js";
import WwUser from "@/components/ww-user/index.vue";
// import { getExternalcontact } from "@/api/wecom";
import { memberStore } from "@/store/member";

import wechatNickName from "@/components/wechat-nick-name/wechat-nick-name.vue";

const { corpInfo } = memberStore();
const props = defineProps({
  customer: { default: () => ({}) },
  member: { default: () => ({}) },
});
const wechatCustomerList = ref([]);
watch(
  () => selectdUserId.value,
  async (n) => {
    if (n) {
      // await getCustomerWeChatInfo([selectdUserId.value]);
      // if (!selectedCustomer.value.externalUserName) {
      //   const item = wechatCustomerList.value.find((j) => j.external_userid === selectedCustomer.value.customerUserId);
      //   selectedCustomer.value.externalUserName = item?.name;
      // }
    }
  }
);
// async function getCustomerWeChatInfo(userIds) {
//   const { permanent_code } = corpInfo;
//   let { success, data } = await getExternalcontact(permanent_code, userIds);
//   if (success) {
//     wechatCustomerList.value = data.data;
//   }
// }

// const listData = computed(() => {
//   return chatCustomerList.value && Array.isArray(chatCustomerList.value)
//     ? chatCustomerList.value
//         .map((i) => {
//           const item = wechatCustomerList.value.find((j) => j.external_userid === i.customerUserId);
//           return {
//             ...i,
//             externalUserName: item?.name,
//           };
//         })
//         .filter((i) => i.externalUserName)
//     : [];
// });

const scrollerRef = ref();
// 滚动到指定下标
function scrollTo(index) {
  scrollerRef.value && scrollerRef.value.scrollToItem(index);
}
function change(customer) {
  emits("change", customer);
}
</script>
<style lang="scss" scoped></style>
