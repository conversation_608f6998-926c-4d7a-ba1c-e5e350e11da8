<template>
  <div px-15 pb-10 border-bottom>
    <div font-semibold font-16 class="mt-17px pt-10px" @click="changeData()">新增客户趋势</div>
    <div v-loading="loading" class="h-300px" ref="lineChartRef"></div>
  </div>
</template>
<script setup>
import { ref, watch, inject, computed } from "vue";
import useLineChart from "@/hooks/useLineChart";
import { dates } from "./useDateTag";
const props = defineProps({
  dates: { type: Array, default: () => [] },
});
const loading = ref(false);
const lineChartRef = ref();
const { xAxisData, seriesData } = useLineChart(lineChartRef);
async function getList() {}
async function getData(xData, sData) {
  loading.value = true;
  setTimeout(function mock() {
    loading.value = false;
    const [start, end] = props.dates;
    xAxisData.value = xData;
    seriesData.value = sData;
  }, 2000);
}

watch(
  () => props.dates,
  () => getData()
);
</script>
