<template>
  <my-layout bg-fff common-shadow>
    <template v-if="form._id">
      <layout-main font-14>
        <div px-15>
          <div pb-15 border-bottom>
            <div flex items-center class="pt-15px pb-6px">
              <div font-16 font-semibold active-title-bar mr-10>{{ todoType }}</div>
              <el-text :type="todoStatus.type">{{ todoStatus.label }}</el-text>
            </div>
            <el-form inline label-suffix="：">
              <el-row>
                <el-col :span="24" :md="18">
                  <el-form-item label="计划执行时间">
                    <span>{{ form.plannedExecutionTime }}</span>
                  </el-form-item>
                  <el-form-item label="失效时间"
                    v-if="form.eventStatus !== 'treated' && form.eventStatus !== 'closed' && form.eventStatus !== 'expire'">
                    <span>{{ form.expireTime }}</span>
                    <span v-if="remainDays !== ''" class="ml-15px">
                      剩余
                      <span color-danger>{{ remainDays }}</span>
                      天失效
                    </span>
                  </el-form-item>
                </el-col>
                <el-col :span="24" :md="6" class="hidden-md-and-up">
                  <el-form-item label="创建人">
                    <span v-if="form.creatorUserId === 'system'">系统自动</span>
                    <ww-user v-else :openid="form.creatorUserId"></ww-user>
                  </el-form-item>
                </el-col>
                <el-col :span="24" :md="6" class="hidden-sm-and-down">
                  <div flex class="justify-end">
                    <el-form-item label="创建人">
                      <span v-if="form.creatorUserId === 'system'">系统自动</span>
                      <ww-user v-else :openid="form.creatorUserId"></ww-user>
                    </el-form-item>
                  </div>
                </el-col>
              </el-row>
            </el-form>
          </div>
          <div pb-15 border-bottom>
            <div pb-10 class="pt-15px">
              <div active-title-bar font-16 font-semibold>任务内容：</div>
            </div>
            <el-form-item>
              <span>{{ form.taskContent }}</span>
            </el-form-item>
            <template v-if="form.sendContent || (form.files && form.files.length)">
              <div font-16 font-semibold pb-10 class="pt-10px">发送给客户内容：</div>
              <el-form-item v-if="form.sendContent">
                <span>{{ form.sendContent }}</span>
              </el-form-item>
              <el-form-item v-if="form.files  && form.files.length">
                <div class="flex-grow">
                  <el-text v-for="i in form.files" class="block" pointer type="primary" @click="readFile(i)">
                    <span font-14 class="hover:underline">附件：{{ i.showName }}</span>
                  </el-text>
                </div>

              </el-form-item>
              <el-button v-if="hasWechatFriend" class="mt-10px" type="primary" @click="send()">去处理</el-button>
            </template>
          </div>
          <div class="py-15px" border-bottom>
            <span color-666>客户：</span>
            <span v-if="form.eventType != 'remindFiling'" mr-10>{{ form.customerName || "客户暂未建档" }}</span>
            <el-button v-if="form.customerId" type="primary" size="small" plain
              @click="toCustomerDetail()">客户详情</el-button>
            <el-button v-if="hasWechatFriend" type="primary" size="small" plain @click="contact()">聊天</el-button>
          </div>
          <div class="py-15px">
            <el-form-item label="处理人：">
              <ww-user v-if="form.executorUserId" :openid="form.executorUserId"></ww-user>
            </el-form-item>
            <template v-if="form.eventStatus === 'treated' || form.eventStatus === 'closed'">
              <el-form-item v-if="form.endTime" label="处理时间：">{{ form.endTime }}</el-form-item>
              <el-form-item v-if="form.result" label="处理结果：">{{ form.result }}</el-form-item>
            </template>
          </div>
        </div>
      </layout-main>
      <layout-item v-if="form.eventStatus === 'untreated'">
        <div p-15 text-center common-shadow>
          <template v-if="form.executorUserId && memberInfo.userid === form.executorUserId">
            <el-button type="info" :loading="loading" plain @click="showResultModal('closed')">取消任务</el-button>
            <el-button type="primary" plain :loading="loading" @click="showResultModal('treated')">完成任务</el-button>
          </template>
          <el-button v-else-if="!form.executorUserId" type="primary" :loading="loading"
            @click="receive()">接受任务</el-button>
        </div>
      </layout-item>
    </template>
    <layout-main v-else :scroll="false">
      <div h-full flex flex-col items-center justify-center>
        <empty-data :top="0" title="未查询到待办信息" />
      </div>
    </layout-main>
  </my-layout>
  <survery-dialog :data="currentSurvery" :visible="visible" @close="visible = false" />
  <result-dialog ref="resultRef" :visible="resultVisible" :type="resultStatus" @close="resultVisible = false"
    @save="confirm" />
  <customer-detial :visible="customerDetialVisible" @close="customerDetialVisible = false" :customerId="form.customerId"
    title="客户详情" customerType="member" />
  <el-image-viewer v-if="showViewer" :url-list="[previewUrl]" @close="() => { showViewer = false }" />
</template>
<script setup>
import { computed, onActivated, onDeactivated, onMounted, ref } from "vue";
import { storeToRefs } from "pinia";
import { getAnswer, getDetail } from "@/api/survery";
import { useRoute, useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { getTodoById, setTodoExecutor, setTodoStatus } from "@/api/todo";
import { ToDoEventType, ToDoTaskStatus } from "@/baseData";
import { openChatWindow, getNameByexternalUserId } from "@/utils/common";
import { tagsStore } from "@/store/tags";
import { memberStore } from "@/store/member";
import wxContact from "@/store/wxContact";

import dayjs from "dayjs";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import WwUser from "@/components/ww-user/index.vue";
import EmptyData from "@/components/empty-data.vue";
import "element-plus/theme-chalk/display.css";
import resultDialog from "./components/result-dialog.vue";
import SurveryDialog from "@/views/system/survery/dialog.vue";
import customerDetial from "@/views/member/detail/index.vue";
import { formatTodoFiles } from '@/utils/todo';

const pic = new URL("@/assets/group-msg.png", import.meta.url).href; // 默认logo


const visible = ref(false);
const currentSurvery = ref({});
const router = useRouter();
const route = useRoute();
const wechatName = ref("");
const { renameTag } = tagsStore();
const store = memberStore();
const { memberInfo } = storeToRefs(store);
const { getContacts } = wxContact();
const { contactName } = storeToRefs(wxContact());
const previewUrl = ref('')

let id = "";
let hasLeft = false;
onActivated(() => {
  if (hasLeft) {
    hasLeft = false;
  }
});
onDeactivated(() => (hasLeft = true));
onMounted(async () => {
  id = route.params.id;
  if (id) getDetailItem();
  else form.value = {};
});

const form = ref({});
const formRef = ref();
const showViewer = ref(false)


const customerDetialVisible = ref(false);
function toCustomerDetail() {
  customerDetialVisible.value = true;
}

const todoType = computed(() => {
  return ToDoEventType[form.value.eventType] || "";
});
const todoStatus = computed(() => {
  const status = ToDoTaskStatus.find((item) => item.value === form.value.eventStatus);
  const type =
    {
      untreated: "danger",
      treated: "primary",
    }[form.value.eventStatus] || "info";
  return { label: status ? status.label : "", type };
});
const hasWechatFriend = computed(() => {
  const { customerUserId, externalUserId } = form.value;
  return contactName.value[customerUserId] || contactName.value[externalUserId];
});
const remainDays = computed(() => {
  if (form.value.eventStatus !== "untreated" || !form.value.expireTime) return "";
  const expireTime = dayjs(form.value.expireTime).valueOf();
  const dayStamp = expireTime - new Date().getTime();
  const day = dayStamp / (1000 * 60 * 60 * 24);
  return Math.max(Math.floor(day), 0);
});

async function getDetailItem() {
  const { success, data, message } = await getTodoById(id, localStorage.getItem("corpId"));
  if (success) {
    form.value = {
      ...data.data,
      plannedExecutionTime: data.data.plannedExecutionTime ? dayjs(data.data.plannedExecutionTime).format("YYYY-MM-DD HH:mm") : "",
      createTime: data.data.createTime ? dayjs(data.data.createTime).format("YYYY-MM-DD HH:mm") : "",
      expireTime: data.data.expireTime ? dayjs(data.data.expireTime).format("YYYY-MM-DD") : "",
      endTime: data.data.endTime ? dayjs(data.data.endTime).format("YYYY-MM-DD") : "",
    };
    form.value = { ...form.value, ...formatTodoFiles(form.value) };
    const maybeExternalUserIds = [form.value.customerUserId, form.value.externalUserId].filter(Boolean);
    setRouteTag();
    if (maybeExternalUserIds.length) {
      getContacts(maybeExternalUserIds);
    }
  } else {
    form.value = {};
    ElMessage.error(message);
  }
  loading.value = false;
}

async function setRouteTag() {
  if (form.value.customerName) {
    const name = form.value.customerName.length > 5 ? `【${form.value.customerName.slice(0, 5)}...】待办详情` : `【${form.value.customerName}】待办详情`;
    renameTag(route.fullPath, name);
  } else {
    const res = await getNameByexternalUserId(form.value.externalUserId);
    if (res && typeof res.name === "string") {
      wechatName.value = res.name;
      const name = res.name.length > 5 ? `【${res.name.slice(0, 5)}...】待办详情` : `【${res.name}】待办详情`;
      renameTag(route.fullPath, name);
    } else {
      wechatName.value = "";
    }
  }
}

const loading = ref(false);
async function receive() {
  await ElMessageBox.confirm(`确定接受该任务吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  });
  loading.value = true;
  const { success, message } = await setTodoExecutor(form.value._id, localStorage.getItem("userId"), localStorage.getItem("corpId"));
  if (success) {
    loading.value = false;
    ElMessage.success(message);
    getDetailItem();
    // emits('updated')
  } else {
    ElMessage.error(message);
  }
  loading.value = false;
}

const resultStatus = ref("");
const resultVisible = ref(false);
const resultRef = ref();
function showResultModal(type) {
  resultStatus.value = type;
  resultVisible.value = true;
}
async function confirm(result) {
  const action = resultStatus.value === "closed" ? "取消" : "完成";
  await ElMessageBox.confirm(`确定${action}该待办事项吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  });
  loading.value = true;
  const { success, message } = await setTodoStatus(form.value._id, resultStatus.value, result || `已${action}`, localStorage.getItem("userId"));
  if (success) {
    resultVisible.value = false;
    ElMessage.success(message);
    getDetailItem();
  } else {
    ElMessage.error(message);
  }
  loading.value = false;
}

function contact() {
  const { customerUserId, externalUserId, customerId } = form.value;
  if (contactName.value[customerUserId]) {
    openChatWindow(customerId, customerUserId, "", { name: "todo", time: new Date().getTime() });
  } else if (contactName.value[externalUserId]) {
    openChatWindow(customerId, externalUserId, "", { name: "todo", time: new Date().getTime() });
  }
}
// async function getSurvery(item) {
//   const { surveryId, answerId } = item;
//   const { data } = await getAnswer(surveryId, form.value.customerId, answerId);
//   currentSurvery.value = data && data.data ? data.data : {};
// }
async function getSurvery(item) {
  const { surveryId, answerId } = item;
  if (answerId && surveryId) {
    const { data } = await getAnswer(surveryId, form.value.customerId, answerId);
    if (data && data.record && data.record.submitTime) {
      currentSurvery.value = data && data.record ? data.record : {};
      visible.value = true;
      return;
    }
  }
  surveryId && getSurveryDetail(surveryId);
}
async function getSurveryDetail(id) {
  const corpId = localStorage.getItem("corpId");
  const { data } = await getDetail(corpId, id);
  currentSurvery.value = data && data.data ? data.data : {};
  visible.value = true;
}

function readFile(file) {
  if (file._type === 'questionnaire') {
    getSurvery(file.file || {})
  }
  if (file._type === 'article') {
    file.file && file.file.url && window.open(file.file.url);
  }
  if (file._type === 'image' && file.URL) {
    previewUrl.value = file.URL
    showViewer.value = true
  }
}

async function send() {
  if (form.value.executeMethod === 'groupTask') {
    previewUrl.value = pic
    showViewer.value = true
  } else {
    contact()
  }
}
</script>
<style scoped>
:deep(.el-form-item) {
  margin-bottom: 0;
}

[pt-30] {
  padding-top: 30px;
}
</style>
