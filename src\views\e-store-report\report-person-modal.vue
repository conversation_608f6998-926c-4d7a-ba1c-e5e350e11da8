<template>
  <el-dialog :model-value="visible" :width="width" title="更新报备人" @close="close()">
    <div class="p-15px">
      <el-form label-position="top">
        <el-form-item label="报备人">
          <el-select v-model="person" ref="selectRef" placeholder="请选择报备人" class="w-full report-people-modal-select" clearable @change="change">
            <template #prefix>
              <div class="truncate h-30px leading-30px text-left" :style="{ width: prefixWidth, color: 'rgb(96, 98, 102)' }">
                <ww-user v-if="form.reportUserId" :openid="form.reportUserId" />
                <span class="ml-10px">{{ teamName }}</span>
              </div>
            </template>
            <el-option-group v-for="group in options" :key="group.teamId" :label="group.name">
              <el-option v-for="item in group.list" :key="item.value" :value="item.value">
                <div class="truncate">
                  <ww-user :openid="item.userId"></ww-user>
                  <span class="ml-10px">{{ item.name }}</span>
                </div>
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item label="报备时间">
          <el-date-picker v-model="form.reportTimeStamp" type="datetime" format="YYYY-MM-DD HH:mm" value-format="x" placeholder="请选择报备时间" class="w-full" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div text-center>
        <el-button plain class="w-100px" type="primary" :icon="Plus" @click="close()">取消</el-button>
        <el-button class="w-100px" :loading="loading" type="primary" :icon="Plus" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { useElementBounding } from "@vueuse/core";
import { storeToRefs } from "pinia";
import { updateCustomerReportPeople } from "@/api/member";
import { memberStore } from "@/store/member";
import { teamStore } from "@/store/team";
import { staffStore } from "@/store/staff";
import { ElMessage } from "element-plus";
const emits = defineEmits(["close", "change"]);
const props = defineProps({
  customer: { type: Object, default: () => ({}) },
  visible: { type: Boolean, default: false },
  width: { type: [Number, String], default: 500 },
});
const person = ref("");
const form = ref({});
const { allTeams } = storeToRefs(teamStore());
const { memberInfo } = storeToRefs(memberStore());
const { staffList } = storeToRefs(staffStore());
const staffMap = computed(() =>
  staffList.value.reduce((acc, cur) => {
    acc[cur.userid] = cur;
    return acc;
  }, {})
);
const loading = ref(false);

const options = computed(() => {
  return allTeams.value.reduce((acc, cur) => {
    const memberList = Array.isArray(cur.memberList) ? cur.memberList : [];
    const group = { teamId: cur.teamId, name: cur.name, list: [] };
    group.list = memberList
      .map((i) => ({
        value: `${cur.teamId}||${i}`,
        userId: i,
        teamId: cur.teamId,
        name: cur.name,
        staff: staffMap.value[i],
      }))
      .filter((i) => i.staff && Array.isArray(i.staff.job) && i.staff.job.includes("onlineCustomerService"));
    if (group.list.length) return [...acc, group];
    return acc;
  }, []);
});
const selectRef = ref(null);

const teamName = computed(() => {
  const team = allTeams.value.find((i) => i.teamId === form.value.teamId && i.teamId);
  return team ? team.name : "";
});
const { width: selectWidth } = useElementBounding(selectRef);
const prefixWidth = computed(() => `${selectWidth.value - 40}px`);

function change(val) {
  const [teamId = "", userId = ""] = val.split("||");
  form.value.reportUserId = userId;
  form.value.teamId = teamId;
}

function close() {
  emits("close");
}

async function confirm() {
  loading.value = true;
  let { teamId: customerTeamIds = [], reportTeamId, personResponsibles = [], reportUserId } = props.customer;
  if (reportTeamId !== form.value.teamId) {
    customerTeamIds = customerTeamIds.filter((i) => i !== reportTeamId);
    customerTeamIds.push(form.value.teamId);
  }
  personResponsibles = personResponsibles.filter((i) => !(i.corpUserId == reportUserId && i.teamId == reportTeamId));
  personResponsibles.push({
    corpUserId: form.value.reportUserId,
    teamId: form.value.teamId,
  });
  const { success, message } = await updateCustomerReportPeople({
    ...form.value,
    teamId: customerTeamIds,
    personResponsibles,
    reportTeamId: form.value.teamId,
  });
  loading.value = false;
  if (success) {
    ElMessage.success(message || "操作成功");
    close();
    emits("change");
  } else {
    ElMessage.error(message || "操作失败");
  }
}
watch(
  () => props.visible,
  (n) => {
    if (n) {
      form.value = {
        updateUserId: memberInfo.value.userid,
        customerId: props.customer._id,
        reportTimeStamp: new Date().getTime(),
      };
      person.value = "";
    }
  }
);
</script>
<style scoped>
:deep(.report-people-modal-select > .select-trigger > .el-input > .el-input__wrapper > .el-input__inner) {
  display: none;
}

:deep(.report-people-modal-select > .select-trigger > .el-input > .el-input__wrapper > .el-input__prefix) {
  flex-grow: 1;
}
</style>