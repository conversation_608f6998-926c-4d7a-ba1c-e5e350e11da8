import { ElLoading, ElMessage } from "element-plus";
import { updateMember, getUnionidByExternalUserId } from "@/api/member";
import { selectExternalContact } from "@/utils/jssdk";

export async function bindWechat({ _id }) {
  let res = await selectExternalContact();
  const { err_msg, userIds } = res;
  if (err_msg === "selectExternalContact:cancel") return;
  if (err_msg !== "selectExternalContact:ok") {
    ElMessage({
      message: err_msg,
      type: "error",
    });
    return;
  }
  if (userIds.length > 1) {
    ElMessage({
      message: "只能选择一个用户!",
      type: "warning",
    });
    return;
  }
  let params = {
    externalUserId: userIds[0],
  };
  const unionId = await getUnionid(userIds[0]);
  if (unionId) params.unionid = unionId;
  let { success } = await updateMember(_id, params);
  if (success) {
    ElMessage({
      message: "更新成功",
      type: "success",
    });
    // 页面刷新
    return success;
  }
}

async function getUnionid(externalUserId) {
  const res = await getUnionidByExternalUserId(externalUserId);
  if (res.data && res.data.data && res.data.data.unionid) {
    return res.data.data.unionid;
  } else {
    return "";
  }
}
