<template>
  <el-dialog v-model="addFileDialogVisible" :title="title" draggable :width="width">
    <div v-loading="loading">
      <div class="file-content">
        <template v-if="type === 'questionnaire'">
          <div class="flex atricle">
            <el-scrollbar class="atricle__side">
              <classify-list :checked-cate="currentSurveryCate" :data="surveryCateList"
                @change="changeCurrentSurveryCate($event)" />
            </el-scrollbar>
            <div class="w-280px flex-grow">
              <el-scrollbar v-if="surveryList.length">
                <div class="file-cell" v-for="item in surveryList" :key="item._id" @click="selectFile(item)">
                  <div class="file-cell-name">
                    <div class="t_1 flex" @click="readFile(item)">
                      <div>{{ item.name }}</div>
                      <el-icon color="#006eff">
                        <Tickets />
                      </el-icon>
                    </div>
                    <div class="t_2">创建时间: {{ dayjs(item.createTime).format("YYYY-MM-DD") }}</div>
                  </div>
                  <img src="@/assets/check.png" class="file-cell-radio" v-if="item._id !== file._id" />
                  <img src="@/assets/check-active.png" class="file-cell-radio" v-else />
                </div>
              </el-scrollbar>
              <empty-data v-else :top="20" title="暂无数据" padding="0 60px 15px" text-top="10px"
                :image-width="150"></empty-data>
            </div>
          </div>
        </template>
        <template v-else>
          <div class="flex atricle">
            <el-scrollbar class="atricle__side">
              <classify-list :checked-cate="current" :data="cateList" @change="changeCurrent($event)" />
            </el-scrollbar>
            <div class="w-280px flex-grow">
              <el-scrollbar v-if="articleList.length">
                <div class="file-cell" v-for="item in articleList" :key="item._id" @click="selectFile(item)">
                  <div class="file-cell-name flex-grow">
                    <div class="t_1 flex" @click="readFile(item)">
                      <el-icon class="flex-shrink-0 mr-5px" color="#006eff">
                        <Tickets />
                      </el-icon>
                      <div class="w-0 flex-grow truncate">{{ item.title }}</div>
                    </div>
                    <div class="t_2">创建时间: {{ dayjs(item.createTime).format("YYYY-MM-DD") }}</div>
                  </div>
                  <img src="@/assets/check.png" class="file-cell-radio" v-if="item._id !== file._id" />
                  <img src="@/assets/check-active.png" class="file-cell-radio" v-else />
                </div>
              </el-scrollbar>
              <empty-data v-else :top="20" title="暂无数据" padding="0 60px 15px" text-top="10px"
                :image-width="150"></empty-data>
            </div>
          </div>
        </template>
      </div>
    </div>
    <template #footer>
      <span class="block text-center">
        <el-button class="w-100px" @click="close()">取消</el-button>
        <el-button class="w-100px" type="primary" @click="dialogSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
  <survery-dialog :data="currentSurvery" :visible="visible" @close="visible = false" />
</template>
<script setup>
import { computed, ref } from "vue";
import { ElMessage } from "element-plus";
import dayjs from "dayjs";
import { getArticleList, getArticleCateList } from "@/api/knowledgeBase";
import { getList as getSurveyList, getSurveryCateList } from "@/api/survery";
import useModal from "@/hooks/useModal";
import useClassifyList from '@/components/classify-list/useClassifyList';
import { getRandomStr } from "@/utils";

import classifyList from '@/components/classify-list/classify-list-side.vue';
import EmptyData from "@/components/empty-data.vue";
import SurveryDialog from "@/views/system/survery/dialog.vue";

const { close, show, visible: addFileDialogVisible, width } = useModal(600);
const loading = ref(false);
const options = {
  getList: getArticleCateList,
  callback: getArticles,
  loading,
}
const { cateList, current, cateIds, changeCurrent, getCateList } = useClassifyList(options, false);

const surveryOptions = {
  getList: getSurveryCateList,
  callback: getSurverys,
  loading,
}

const { cateList: surveryCateList, current: currentSurveryCate, changeCurrent: changeCurrentSurveryCate, cateIds: surveryCateIds, getCateList: getSurveryCates } = useClassifyList(surveryOptions, false);


const articleList = ref([]);
const surveryList = ref([]);
const file = ref({});
const type = ref("");
const list = ref([]);
const emit = defineEmits(["onGetSelectFile"]);
const visible = ref(false);
const currentSurvery = ref({});
const title = computed(() => type.value === "article" ? `宣教文章列表` : `调查问卷列表(${list.value.length})`);

async function open(item, f) {
  file.value = f;
  type.value = item;
  if (type.value === 'article' && cateList.value.length === 0) {
    await getCateList();
  } else if (type.value === 'questionnaire' && surveryCateList.value.length === 0) {
    await getSurveryCates();
  }
  // getList();
  show()
}

async function getArticles() {
  loading.value = true;
  const { data, message, success } = await getArticleList({ corpId: localStorage.getItem("corpId"), page: 1, pageSize: 99, enable: true, cateIds: cateIds.value });
  articleList.value = Array.isArray(data.list) ? data.list.map(i => ({
    ...i,
    time: i.createTime ? dayjs(i.createTime).format("YYYY-MM-DD HH:mm") : ''
  })) : []
  loading.value = false;
  if (!success) ElMessage.error(message);
}

function readFile(item) {
  if (type.value === "questionnaire") {
    currentSurvery.value = item;
    visible.value = true;
  } else {
    window.open(item.link);
  }
}
function dialogSubmit() {
  if (!file.value._id) {
    ElMessage.warning("请选择!");
    return;
  }
  let obj = {
    type: type.value,
  };
  if (type.value === "questionnaire") {
    obj["surveryId"] = file.value._id;
    obj["name"] = file.value.name;
    obj["url"] = `${import.meta.env.VITE_PATIENT_PAGE_BASE_URL}pages/survery/fill?corpId=${localStorage.getItem("corpId")}&surveryId=${file.value._id}`;
  } else {
    const { title, summary, _id } = file.value;
    obj.articleId = _id;
    obj["url"] = `${import.meta.env.VITE_PATIENT_PAGE_BASE_URL}pages/article/index?id=${_id}&corpId=${localStorage.getItem("corpId")}`;
    obj["name"] = title;
    obj["summary"] = summary;
  }
  emit("onGetSelectFile", obj);
  addFileDialogVisible.value = false;
}

async function selectFile(item) {
  file.value = file.value._id === item._id ? {} : item;
}

async function getSurverys() {
  loading.value = true;
  if (type.value !== "article") {
    const { success, data = {} } = await await getSurveyList(1, 99, "", "enable", false, surveryCateIds.value);
    if (success) {
      const { list: tableData = [], total: count = 0 } = data;
      surveryList.value = tableData;
    } else {
      surveryList.value = []
    }
  }
  loading.value = false;
}

defineExpose({
  open,
});
</script>
<style lang="scss" scoped>
.file-content {
  height: 300px;
  overflow-y: hidden;

  .file-cell {
    display: flex;
    justify-content: space-between;
    padding: 20px 20px 10px 20px;
    align-items: center;
    border-bottom: 1px solid #eee;

    .file-cell-name {
      .t_1 {
        color: #000;
        font-weight: 600;
        align-items: center;
        padding-right: 10px;
        cursor: pointer;
      }

      .t_2 {
        font-size: 12px;
      }
    }

    .file-cell-radio {
      width: 18px;
      height: 18px;
      cursor: pointer;
    }
  }
}

.atricle {
  height: 100%;

  @at-root &__side {
    width: 200px;
    border-right: 1px solid #eee;
    height: 100%;
  }

  @at-root &__cate {
    border-bottom: 1px solid #eee;

    @at-root &--active {
      color: #fff;
      background-color: var(--el-color-primary);
    }
  }
}

.el-dialog__body {
  padding: 0px !important;
}
</style>
