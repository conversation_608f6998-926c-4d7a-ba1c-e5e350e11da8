<template>
  <el-form-item class="px-15px py-6px el-form-item--autoheight" :class="required ? 'is-required' : ''" :label="name" border-bottom>
    <img :src="image" alt="" v-for="image in props.range" :class="image === value ? 'h-40px w-60px pointer mr-10px  active' : 'h-40px w-60px  pointer mr-10px'" @click="change(image)" />
  </el-form-item>
</template>
  <script setup>
import { computed, nextTick, ref } from "vue";

const emits = defineEmits(["change"]);
const props = defineProps({
  form: { type: Object, default: () => ({}) },
  mult: { type: Boolean, default: false },
  name: { type: String, default: "" },
  range: { type: Array, default: () => [] },
  required: { type: Boolean, default: false },
  title: { type: String, default: "" },
  rangeKey: { type: String, default: "" },
});
const options = computed(() => {
  if (props.rangeKey) return props.range.map((opt) => ({ label: opt.label, value: opt[props.rangeKey] }));
  return props.range.map((opt) => ({ label: opt, value: opt }));
});

const value = computed(() => props.form[props.title] || "");

const selectRef = ref();

async function change(value) {
  emits("change", { title: props.title, value });
}
</script>
  <style lang="scss" scoped>
.active {
  border: 1px solid #409eff;
}
</style>
  