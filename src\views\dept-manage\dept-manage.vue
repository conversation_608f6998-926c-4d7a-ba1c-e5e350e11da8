<template>
  <div bg-fff flex class="h-full">
    <div h-full flex-shrink-0 class="w-3/10 min-w-240px max-w-320px border-r border-gray-200">
      <classify-list v-bind="componentProps" custom :checked-cate="current" :data="cateList" placeholder="输入科室名称搜索"
        label="科室" :label-option="{edit:'编辑'}" @change="changeCurrent($event)" @search="getCateList()"
        @onHandle="handle">
        <template #header>
          <div color-normal class="text-16px font-semibold mb-15px">科室列表</div>
        </template>
      </classify-list>
    </div>
    <div h-full flex-grow>
      <my-layout v-if="current">
        <layout-item>
          <div class="text-16px font-semibold px-15px pt-15px pb-5px">{{current.label}}</div>
          <el-tabs v-model="activeName">
            <el-tab-pane name="staff">
              <template #label>
                <span class="px-15px">科室人员</span>
              </template>
            </el-tab-pane>
            <el-tab-pane name="detail">
              <template #label>
                <span class="px-15px">科室详情</span>
              </template>
            </el-tab-pane>
          </el-tabs>
          <div v-show="activeName === 'staff'" p-15 class="flex items-center justify-between">
            <!-- <div class="flex items-center">
              <el-input :prefix-icon="Search" placeholder="输入姓名搜索"></el-input>
              <el-button class="w-100px ml-10px" type="primary" @click="search">搜索</el-button>
            </div> -->
            <el-button :icon="Plus" type="primary" @click="staffShow()">添加员工</el-button>
          </div>
        </layout-item>
        <layout-main v-show="activeName === 'staff'" :scroll="false">
          <el-table stripe height="100%" :data="list" v-loading="loading">
            <el-table-column prop="name" label="员工姓名" :min-width="100">
              <template #default="{ row: { anotherName,userId } }">
                <el-popover placement="top-start" :width="320" trigger="hover">
                  <template #reference>
                    <div class="truncate"><ww-user :openid="userId" /></div>
                  </template>
                  <el-text><ww-user :openid="userId" /></el-text>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column prop="userId" label="员工ID" :min-width="100"></el-table-column>
            <el-table-column prop="job" label="岗位" :min-width="100">
              <template #default="{row:{jobName}}">
                <el-tooltip :disabled="!jobName" placement="top" effect="light" :content="jobName"
                  popper-class="max-w-480px">
                  <div class="truncate">{{ jobName }}</div>
                  <template #content>
                    <div class="whitespace-pre-wrap">{{ jobName }}</div>
                  </template>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="depts" label="科室" :min-width="100">
              <template #default="{row:{deptNames}}">
                <div class="truncate">{{ deptNames && deptNames.length ? deptNames.join('，') : '-'}}</div>
              </template>
            </el-table-column>

            <el-table-column fixed="right" class-name="el-table-fixed-column--shadow" label="操作" width="160">
              <template #default="{ row }">
                <el-text class="cursor-pointer mr-5px" type="primary" @click="changeDept(row)">修改科室</el-text>
                <el-text class="cursor-pointer mr-5px" type="primary" @click="remove(row.userId)">移除</el-text>
              </template>
            </el-table-column>
          </el-table>
        </layout-main>
        <layout-main v-if="currentDept" v-show="activeName === 'detail'">
          <div class="flex px-15px">
            <el-form class="flex-grow" label-suffix="：" label-width="90">
              <el-form-item label="科室名称">{{ currentDept.deptName }}</el-form-item>
              <el-form-item label="科室ID">{{ currentDept.deptId }}</el-form-item>
              <el-form-item label="科室介绍">{{ currentDept.deptDesc }}</el-form-item>
            </el-form>
            <el-button class="flex-shrink-0" text type="primary" @click="handle(currentDept)">编辑</el-button>
          </div>
        </layout-main>
        <!-- <layout-item>
          <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize"
            @handle-current-change="changePage" />
        </layout-item> -->
      </my-layout>
    </div>
  </div>
  <dept-modal :visible="visible" :width="width" :dept="dept" @close="close" @change="getCateList()" />
  <add-staff-modal :dept="currentDept" :visible="staffVisible" :width="staffWidth" @close="staffClose"
    @change="getList()" />
  <change-dept-modal :cateList="cateList" :staff="currentStaff" :visible="changeDeptVisible" :width="changeDeptWidth"
    @close="changeDeptClose" @change="getList()" />
</template>
<script setup>
import { computed, ref, onMounted } from "vue";
import { storeToRefs } from 'pinia';
import { ElMessage, ElMessageBox } from "element-plus";
import { getDeptList, addDept, deleteDept, updateDept, sortDeptList, getDeptStaff, removeDeptStaff } from "@/api/dept-manage";
import useModal from "@/hooks/useModal";
import { dbStore } from "@/store/db.js";

import { Plus, Search } from "@element-plus/icons-vue";
import useClassifyList from "@/components/classify-list/useClassifyList";
import useElPagination from "@/hooks/useElPagination";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import classifyList from "@/components/classify-list/classify-list.vue";
import addStaffModal from "./add-staff-modal.vue";
import changeDeptModal from "./change-dept-modal.vue";
import deptModal from "./dept-modal.vue";

const { close, show, visible, width } = useModal(640);
const { close: staffClose, show: staffShow, visible: staffVisible, width: staffWidth } = useModal(640);
const { close: changeDeptClose, show: changeDeptShow, visible: changeDeptVisible, width: changeDeptWidth } = useModal(640);

const db = dbStore();
const { getJobList } = db;
const { jobMap } = storeToRefs(db);

const activeName = ref("staff");
const list = ref([]);
const currentStaff = ref({})
const loading = ref(false);
const options = {
  add: addDept,
  remove: deleteDept,
  update: updateDept,
  sort: sortDeptList,
  getList: getDeptList,
  callback: getList,
  format: list => list.map(i => ({ ...i, label: i.deptName, value: i._id }))
  // loading: cateLoading
};

const { cateList, current, getCateList, changeCurrent, componentProps } = useClassifyList(options);
const currentDept = computed(() => cateList.value.find(i => current.value && current.value._id && i._id === current.value._id))
const dept = ref({});
const { page, pageSize, changePage, changeSize } = useElPagination(getList);

function handle(data = {}) {
  dept.value = data
  show();
}

async function getList() {
  if (current.value._id) {
    const { data } = await getDeptStaff({ corpId: localStorage.getItem("corpId"), deptId: current.value._id });
    list.value = Array.isArray(data.list) ? data.list.map(i => {
      return {
        ...i,
        jobName: Array.isArray(i.job) ? i.job.map(j => jobMap.value[j] || j).join('、') : ''
      }
    }) : [];
  } else {
    list.value = [];
  }
}

async function remove(userId) {
  await ElMessageBox.confirm("确认是否把该员工移除？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
  const { success, message } = await removeDeptStaff({ corpId: localStorage.getItem('corpId'), deptId: current.value._id, userId });
  if (success) {
    ElMessage.success(message);
    getList();
  } else {
    ElMessage.error(message);
  }
}

function changeDept(staff) {
  currentStaff.value = staff;
  changeDeptShow();
}
onMounted(() => {
  getJobList();

})

</script>
