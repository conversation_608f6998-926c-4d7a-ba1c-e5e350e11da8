<template>
  <div class="flex flex-col" :style="{ height: maxHeight + 'px' }">
    <div class="flex-grow relative">
      <div class="absolute inset-0">
        <el-scrollbar height="100%" class="text-14px">
          <div v-if="planForm && planForm.planName" class="pb-40px">
            <div class="px-10px shadow-md bg-white">
              <div class="flex items-center py-12px border-bottom">
                <div class="max-w-1/2 text-14px font-semibold truncate"> {{ planForm.planName }} </div>
                <div class="ml-10px flex-shrink-0 text-14px text-blue-500">
                  [{{ managementPlanStatus[planForm.planExecutStaus] }}]
                </div>
              </div>
              <div class="flex items-center pb-12px text-12px">
                <div class="flex-shrink-0 flex items-center">
                  <div class="text-gray-500">开始时间：</div>
                  <div class="truncate">{{ planForm.planExecutionTime }}</div>
                </div>
                <div class="flex-grow flex items-center ml-15px">
                  <div class="text-gray-500">跟进人：</div>
                  <div class="truncate">
                    <ww-user :openid="planForm.executorUserId"></ww-user>
                  </div>
                </div>
              </div>
            </div>
            <plan-node-list :showAdd="false" class="my-40rpx" @addTask="addTask" @edit="editTask" />

            <!-- <div class="p-15px flex bg-white footer-item z-3" common-shadow--r>
              <el-button v-if="showAdd" class="flex-grow mr-15px" type="primary" @click="addTask">新增任务</el-button>
              <el-button v-if="planForm && planForm.planExecutStaus === 'executing'" class="flex-grow mr-15px"
                type="primary" @click="stopPlan()">终止计划</el-button>
              <el-button class="flex-grow" type="primary" @click="addPlan" v-else>加入任务</el-button>
            </div> -->
          </div>
          <div v-else class="pb-40px">
            <empty-data class="mt-20px" :top="0" title="暂无回访计划"></empty-data>
            <div
              class="flex items-center justify-center mt-15px w-160px py-6px px-12px mx-auto text-blue-500 border-1 border-blue-500 cursor-pointer rounded-full"
              @click="visible = true">
              <el-icon size="16">
                <Plus />
              </el-icon>
              <span class="text-14px ml-5px">新增回访计划</span>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>
    <div v-if="planForm && planForm.planName" class="p-15px flex bg-white flex-shrink-0 z-3" common-shadow--r>
      <el-button v-if="showAdd" class="flex-grow mr-15px" type="primary" @click="addTask">新增任务</el-button>
      <el-button v-if="planForm && planForm.planExecutStaus === 'executing'" class="flex-grow mr-15px" type="primary"
        @click="stopPlan()">终止计划</el-button>
      <el-button class="flex-grow" type="primary" @click="addPlan" v-else>加入任务</el-button>
    </div>
  </div>
  <plan-page v-if="visible" @close="close" ref="planPageRefs" />
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { memberStore } from "@/store/member";
import EmptyData from "@/components/empty-data.vue";
import PlanPage from "./page.vue";
import { managementPlanStatus } from "@/baseData";
import planNodeList from "./components/plan-node-list.vue";
import { memberMangePlanStore } from "@/store/managePlan/memberMangePlan";
const { planForm, taskList, customer, team } = storeToRefs(memberMangePlanStore());
const { stopPlan, getMemberManagementPlanFnc, initPlan } = memberMangePlanStore();
const { memberInfo } = storeToRefs(memberStore());
const props = defineProps({
  maxHeight: { type: Number },
  customer: { type: Object, default: () => ({}) },
  team: { type: Object, default: () => ({}) },
});

const visible = ref(false);
watch(
  () => [props.customer, props.team],
  ([newCustomer, newTeam]) => {
    if (newCustomer && newTeam) {
      customer.value = newCustomer;
      team.value = newTeam;
      taskList.value = [];
      getMemberManagementPlanFnc();
    }
  },
  { immediate: true }
);
const planPageRefs = ref(null);
function addTask() {
  visible.value = true;
  setTimeout(() => {
    planPageRefs.value.addTask("add");
  }, 0);
}

function addPlan() {
  visible.value = true;
  setTimeout(() => {
    planPageRefs.value.addPlan();
  }, 0);
}

function close() {
  visible.value = false;
  initPlan();
  getMemberManagementPlanFnc();
}

function editTask() {
  visible.value = true;
  setTimeout(() => {
    planPageRefs.value.addTask("edit");
  }, 0);
}

const showAdd = computed(() => planForm.value && planForm.value.planExecutStaus !== 'closed')
</script>

