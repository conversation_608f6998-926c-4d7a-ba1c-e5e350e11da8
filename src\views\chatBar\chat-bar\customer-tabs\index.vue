<template>
  <div class="overflow-hidden">
    <el-scrollbar class="bg-white">
      <div ref="tabsRef" border-bottom class="flex text-center text-14px">
        <div v-for="tab in tabs" :key="tab.key" :class="currentTab === tab.key ? 'color-primary tab-active' : ''"
          class="flex-shrink-0 px-10px py-12px tab" @click="toggleTab(tab.key)">
          <el-badge :hidden="!(badgeMap[tab.key] && badgeMap[tab.key] > 0)" :value="badgeMap[tab.key]" :max="99">
            {{ tab.title }}
          </el-badge>
        </div>
      </div>
    </el-scrollbar>
    <Component :is="currentComponent" :ready="ready" :unionId="unionId" :max-height="maxWrapperHeight"
      :customer="customer" :team="team" @change="handleChange($event)" />
  </div>
</template>
<script setup>
import { computed, inject, onMounted, ref, toRefs, watch } from 'vue';
import { useElementBounding, useWindowSize, useMouse } from '@vueuse/core'
import { storeToRefs } from 'pinia';
import dayjs from 'dayjs';
import { getEventsCount } from '@/api/todo';
import { memberStore } from "@/store/member";

// import followUp from './follow-up';
import feeInfo from './fee-info/fee-info.vue';
import internalInfo from './internal-info/internal-info';
import serviceRecord from './service-record';
import visitRecord from './visit-record/visit-record';
// import managePlan from './manage-plan/manage-plan';


const props = defineProps({
  headHeight: { type: Number },
  customer: { type: Object, default: () => ({}) },
  team: { type: Object, default: () => ({}) },
  unionId: { type: String, default: '' }
})
const { customer, headHeight, team } = toRefs(props)
const trigger = inject('side-bar-event-trigger');

const tabsRef = ref(null);
const { height } = useElementBounding(tabsRef)
const { height: winHeight } = useWindowSize();
const maxWrapperHeight = computed(() => winHeight.value - headHeight.value - height.value);
const tabs = ref([
  { key: 'serviceRecord', title: '服务记录', component: serviceRecord },
  { key: 'innerInfo', title: '内部信息', component: internalInfo },
  { key: 'visitiRecord', title: '健康档案', component: visitRecord },
  // { key: 'followUp', title: '待跟进事项', component: followUp },
  // { key: 'managePlan', title: '回访计划', component: managePlan },
  { key: 'feeInfo', title: '费用信息', component: feeInfo }


])
onMounted(() => {
  trigger.$on('change-currentTab-2-followup', () => { currentTab.value = 'followUp' })
});

const currentTab = ref('serviceRecord');
function toggleTab(key) {
  currentTab.value = key;
}
const currentComponent = computed(() => {
  const tab = tabs.value.find(i => i.key === currentTab.value);
  return tab && tab.component ? tab.component : null
})

const { memberInfo } = storeToRefs(memberStore());

const badgeMap = ref({});
const lastTime = ref(new Date().getTime());
const loading = ref(false);
async function getCount() {
  if (loading.value) return;
  loading.value = true;
  if (props.customer && props.customer._id && props.team && props.team.teamId) {
    const params = { corpId: memberInfo.value.corpId, executorUserId: memberInfo.value.userid, customerId: props.customer._id, eventStatus: "untreated", plannedExecutionEndTime: dayjs().format('YYYY-MM-DD') };
    const { data } = await getEventsCount(params, props.team.teamId);
    badgeMap.value['followUp'] = data.total > 0 ? data.total : 0;
    lastTime.value = new Date().getTime();
  } else {
    badgeMap.value['followUp'] = 0
  }
  loading.value = false;
}

/**
 * 定时刷新 待跟进数量角标
 */
const payload = computed(() => ({ id: props.customer ? props.customer._id : '', teamId: props.team ? props.team.teamId : '' }))
watch(payload, getCount, { immediate: true });

const { x } = useMouse(); // 用鼠标移动来检测是否在操作当前页面
watch(x, () => {
  if (new Date().getTime() - lastTime.value > 1000 * 60 * 30) getCount();
})

function handleChange({ name }) {
  if (name === 'follow-up-change') getCount()
}


</script>
<style lang="scss" scoped>
.color-primary {
  color: #006eff;
}

.tab-active {
  position: relative;

  @at-root &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: calc(50% - 30px);
    width: 60px;
    height: 2px;
    background: #006eff;
  }
}
</style>
