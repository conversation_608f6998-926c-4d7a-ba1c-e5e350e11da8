<template>
  <el-table-column label="计划开始日期" prop="managePlanDate" :width="160">
    <template #default="{row}">
      <td-wrapper classnames="w-full">
        <plan-wrapper :plans="row.plans">
          <template #default="{plan}">
            {{ plan && plan.planExecutionTime ? plan.planExecutionTime:'' }}
          </template>
        </plan-wrapper>
      </td-wrapper>
    </template>
  </el-table-column>
</template>
<script setup>
import tdWrapper from '../td-wrapper.vue';
import planWrapper from './plan-wrapper.vue';

</script>
<style lang="scss" scoped></style>
