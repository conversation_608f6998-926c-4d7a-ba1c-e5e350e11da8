<template>
  <el-dialog :model-value="props.visible" :width="520" @close="close">
    <template #header>
      <div>
        <el-text type="primary">
          <span flex items-center font-18 font-semibold>
            <el-icon class="mr-5px">
              <Management />
            </el-icon>
            <span>{{ work.title }}</span>
          </span>
        </el-text>
        <div class="pt-4px">
          {{ time }}
        </div>
      </div>
    </template>
    <el-scrollbar wrap-style="max-height: calc(100vh - 360px)">
      <div px-15 pb-10 border-bottom>
        <div flex pt-10>
          <div class="w-100px pr-10px" color-666 flex-shrink-0>{{ props.work.workType === "visit-reg" ? "就诊人" : "服务对象" }}</div>
          <div class="w-0px" flex-grow>
            {{ props.work.customerName }}
            <el-button v-if="props.work.memberId" class="ml-10px" size="small" type="primary" plain @click="viewCustomer(props.work.memberId)">详情</el-button>
          </div>
        </div>
        <div v-if="props.work.workType === 'visit-reg'" flex pt-10>
          <div class="w-100px pr-10px" color-666 flex-shrink-0>
            就诊医生
            <!-- {{ props.work.workType === 'visit-reg' ? '就诊医生' : '执行人' }} -->
          </div>
          <div v-if="props.work.workType === 'visit-reg'" class="w-0px" flex-grow>
            <ww-user v-if="props.work.doctorUserId" :openid="props.work.doctorUserId"></ww-user>
          </div>
          <!-- <div v-else class="w-0px" flex-grow>
            <ww-user v-if="props.work.userId" :openid="props.work.userId"></ww-user>
          </div> -->
        </div>
        <div flex pt-10>
          <div class="w-100px pr-10px" color-666 flex-shrink-0>日程内容</div>
          <div class="w-0px" flex-grow>{{ props.work.workContent }}</div>
        </div>
      </div>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button class="w-100px" :loading="loading" type="danger" plain @click="remove()">删除</el-button>
        <el-button v-if="canEdit" class="w-100px" type="primary" plain @click="toEdit()">编辑</el-button>
      </div>
    </template>
  </el-dialog>
  <customer-detail :visible="customerDetailVisible" @close="customerDetailVisible = false" :customerId="customerId" title="客户详情" customerType="member" />
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { WeekDay } from "@/baseData";
import { removeWork, closeWorkTodo } from "@/api/corp";
import { getTodoById } from "@/api/todo";
import { memberStore } from "@/store/member";
import { openEnterpriseChat } from "@/utils/jssdk";
import dayjs from "dayjs";
import WwUser from "@/components/ww-user/index.vue";
import customerDetail from "@/views/member/detail/index.vue";
const props = defineProps({
  visible: { type: Boolean, default: false },
  work: { type: Object, default: () => ({}) },
  timestamp: { type: Number, default: 0 },
});
const emits = defineEmits(["change", "close", "edit", "remove", "show-survery"]);
const time = computed(() => {
  if (props.work.workTimeStamp) {
    const weekdayIndex = dayjs(props.work.workTimeStamp).day();
    return dayjs(props.work.workTimeStamp).format(`YYYY年MM月DD日 ${WeekDay[weekdayIndex]} HH:mm`);
  }
  return "";
});

const canEdit = computed(() => props.work.workTimeStamp > props.timestamp);
const customerDetailVisible = ref(false);
const customerId = ref("");
const loading = ref(false);
async function remove() {
  await ElMessageBox.confirm(props.work.isTodo === "YES" && props.work.workTimeStamp > props.timestamp ? "删除此项日程后，关联的任务将被终止，是否确认删除？" : "确定删除此项日程？", "提示", {
    confirmButtonText: "确认删除",
    cancelButtonText: "取消",
    type: "warning",
  });
  loading.value = true;
  const { success, message } = await removeWork({
    id: props.work._id,
    todoId: props.work.todoId,
    corpId: localStorage.getItem("corpId"),
    userId: localStorage.getItem("userId"),
  });
  if (success) {
    ElMessage.success(message);
    emits("remove");
  } else {
    ElMessage.error(message);
  }
  loading.value = false;
}

const router = useRouter();
function viewCustomer(id) {
  customerDetailVisible.value = true;
  customerId.value = id;
}

function close() {
  emits("close");
}

function toEdit() {
  emits("close");
  emits("edit", { ...props.work });
}

const todo = ref({});
const canDeal = computed(() => {
  return todo.value.createTime ? props.timestamp > dayjs(todo.value.createTime).startOf("day").valueOf() : false;
});
async function getTodo() {
  if (props.work.isTodo === "YES" && props.work.todoId) {
    const { success, data, message } = await getTodoById(props.work.todoId, localStorage.getItem("corpId"));
    todo.value = data.data || {};
  } else {
    todo.value = {};
  }
}

const closeLoading = ref(false);
async function closeTodo() {
  if (closeLoading.value) return;
  closeLoading.value = true;
  await ElMessageBox.confirm("是否确定关闭待办单提醒？", "提示");
  const { success, data, message } = await closeWorkTodo({
    id: props.work._id,
    corpId: localStorage.getItem("corpId"),
    userId: localStorage.getItem("userId"),
  });
  if (success) {
    ElMessage.success(message);
    emits("change");
  } else {
    ElMessage.error(message);
  }
  closeLoading.value = false;
}

watch(
  () => props.visible,
  (n) => {
    if (n) {
      getTodo();
    }
  }
);
</script>
