<template>
  <my-layout bg-fff common-shadow>
    <layout-main :scroll="false">
      <div class="source">
        <div class="item" border-right>
          <div class="header">来源一级</div>
          <source-item type="oneClass" :list="oneClassList" :selectSource="oneSelectSource" :selecet-index="selectOneClassIndex" @select-item="onSelectItem" @update="updateCorp"></source-item>
        </div>
        <div class="item" border-right>
          <div class="header">来源二级</div>
          <source-item type="twoClass" :list="twoClassList" :disabled="oneSelectSource && oneSelectSource.type === 'fixed'" :selectSource="twoSelectSource" :selecet-index="selectTwoClassIndex" @select-item="onSelectItem" @update="updateCorp"></source-item>
        </div>
        <div class="item">
          <div class="header">来源三级</div>
          <source-item type="treeClass" :list="threeClassList" :disabled="oneSelectSource && oneSelectSource.type === 'fixed'" :selectSource="treeSelectSource" @select-item="onSelectItem" @update="updateCorp"></source-item>
        </div>
      </div>
    </layout-main>
  </my-layout>
</template>
<script setup>
import { computed, ref, onMounted } from "vue";
import { storeToRefs } from "pinia";
import MyLayout, { LayoutMain } from "@/components/layout";
import { memberStore } from "@/store/member";
import sourceItem from "./components/sourceItem.vue";
import { updateCorpInfo } from "@/api/corp";
const { corpInfo } = storeToRefs(memberStore());
// let corpInfo = memberStore().corpInfo;
// let customerSourceList = ref(corpInfo["customerSourceList"] || []);
const customerSourceList = ref([]);
let selectOneClassIndex = ref(0);
let selectTwoClassIndex = ref(0);
let selectTreeClassIndex = ref(0);
const fixedSource = [
  { sourceName: "客户推荐", sourceId: "customer-recommend", type: "fixed", disable: false, children: [] },
  { sourceName: "同事推荐", sourceId: "co-worker-recommend", type: "fixed", disable: false, children: [] },
]; // 固定来源

onMounted(() => getSourceList());

function getSourceList() {
  const list = fixedSource.map((i) => ({ ...i }));
  const sourceList = Array.isArray(corpInfo.value.customerSourceList) ? corpInfo.value.customerSourceList : [];
  sourceList.forEach((i) => {
    const index = fixedSource.findIndex((item) => item.sourceId === i.sourceId);
    if (index >= 0) {
      if ("disable" in i) list[index]["disable"] = i.disable;
    } else {
      const { type, ...source } = i;
      list.unshift(source);
    }
  });
  list.reverse();
  customerSourceList.value = list;
}

const oneClassList = computed(() => customerSourceList.value);
const twoClassList = computed(() => {
  return customerSourceList.value[selectOneClassIndex.value] ? customerSourceList.value[selectOneClassIndex.value].children : [];
});
const threeClassList = computed(() => {
  return customerSourceList.value[selectOneClassIndex.value] && customerSourceList.value[selectOneClassIndex.value].children && customerSourceList.value[selectOneClassIndex.value].children[selectTwoClassIndex.value] ? customerSourceList.value[selectOneClassIndex.value].children[selectTwoClassIndex.value].children : [];
});

let oneSelectSource = computed(() => {
  return oneClassList.value[selectOneClassIndex.value];
});
let twoSelectSource = computed(() => {
  return twoClassList.value[selectTwoClassIndex.value];
});
let treeSelectSource = computed(() => {
  return threeClassList.value[selectTreeClassIndex.value];
});
function onSelectItem(obj) {
  let { index, type } = obj;
  if (type === "oneClass") {
    selectOneClassIndex.value = index;
  } else if (type === "twoClass") {
    selectTwoClassIndex.value = index;
  } else if (type === "treeClass") {
    selectTreeClassIndex.value = index;
  }
}

async function updateCorp() {
  await updateCorpInfo({ customerSourceList: customerSourceList.value });
  memberStore().getCorpInfo();
}
</script>

<style lang="scss" scoped>
.source {
  display: flex;
  width: 100%;
  height: 100%;

  .item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;

    .header {
      text-align: center;
      height: 50px;
      line-height: 50px;
      background: rgb(236, 244, 255);
      width: 100%;
    }

    .addButton {
      margin-top: 30px;
    }

    .context-box {
      height: 50px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      flex-wrap: wrap;

      .left {
        font-size: 14px;
        padding-left: 15px;
      }

      .right {
        display: flex;
        color: #006eff;
        font-size: 14px;
        width: 130px;
      }
    }
  }
}
</style>
