<template>
  <el-dialog class="staff-picker-dialog" :model-value="visible" :width="560" :show-close="false" @close="cancel">
    <div class="flex h-450px">
      <div class="w-250px bac-color overflow-y-auto r_10">
        <div font-16 py-10 px-15 class="member_title pl-20px">从成员列表中选择</div>
        <!-- 搜索框 -->
        <div class="px-15px pb-10px">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索成员姓名"
            size="small"
            clearable
            @input="handleSearch"
            @clear="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        <el-scrollbar class="h-360px" v-loading="loading">
          <div v-if="memberList.length === 0 && !loading" class="text-center py-30px text-gray-500">
            <div class="mb-10px">
              <el-icon size="40" class="text-gray-300">
                <Search />
              </el-icon>
            </div>
            <div>{{ searchKeyword ? '未找到匹配的成员' : '请输入关键词搜索成员' }}</div>
            <div v-if="searchKeyword" class="text-sm mt-5px">
              尝试使用姓名关键词搜索成员
            </div>
            <div v-if="!searchKeyword" class="mt-10px">
              <el-button size="small" @click="loadAllMembers">查看所有成员</el-button>
            </div>
          </div>
          <div v-for="item in memberList" class="mx-10px pl-20px py-6px mb-4px pointer contact" @click="checkedMember(item)" :class="selectItem._id === item._id ? 'check-active' : ''">
            <div class="flex align-center">
              <img src="@/assets/check-active.png" class="cell-radio" v-if="showChecked(item, checkedMemberList)" />
              <img src="@/assets/check.png" class="cell-radio" v-else />
              <img :src="getAvatarUrl(item.avatar)" class="w-25px h-25px mx-10px rounded-full" />
              <ww-user :openid="item.userid"></ww-user>
            </div>
          </div>
          <!-- 加载更多按钮 -->
          <div v-if="memberList.length > 0 && !allLoaded" class="text-center py-10px">
            <el-button size="small" @click="loadMoreMembers" :loading="loadingMore">
              {{ loadingMore ? '加载中...' : '加载更多' }}
            </el-button>
          </div>
          <!-- 已加载全部提示 -->
          <div v-if="memberList.length > 0 && allLoaded" class="text-center py-10px text-gray-500 text-sm">
            已加载全部成员
          </div>
        </el-scrollbar>
      </div>
      <div class="w-310px">
        <div px-15 py-10 class="member_num_title">已选择{{ checkedMemberList.length }}位成员</div>
        <el-scrollbar class="h-360px">
          <div class="px-20px">
            <div v-for="(item, idx) in checkedMemberList" :key="'selected_' + item.userid" class="py-6px mb-4px" flex items-center justify-between pb-10>
              <div flex items-center flex-grow class="mr-5px">
                <img :src="getAvatarUrl(item.avatar)" class="mr-5px w-25px h-25px rounded-full" />
                <ww-user :openid="item.userid"></ww-user>
              </div>
              <el-checkbox :model-value="showChecked(item, selectLeaders)" label="团队负责人" v-if="noShowLeader" @update:model-value="changeLeader($event, item)"></el-checkbox>
              <el-icon class="ml-10px" pointer @click="remove(item)">
                <Close />
              </el-icon>
            </div>
          </div>
        </el-scrollbar>
        <div flex items-center justify-around>
          <div class="b_r pointer" @click="submit">确定</div>
          <div class="b_l pointer" @click="cancel">取消</div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import WwUser from "@/components/ww-user/index.vue";
import { getCorpMember } from "@/api/corp.js";
import { searchMembers as searchHlwMembers } from "@/api/internet-dept-manage.js";
import { ref, watch } from "vue";
import { Search } from "@element-plus/icons-vue";
import { debounce } from "lodash-es";

const checkedMemberList = ref([]);
const memberList = ref([]);
const selectItem = ref({});
const visible = ref(false);
const selectMemberType = ref(2);
const loading = ref(false);
const loadingMore = ref(false);
const searchKeyword = ref('');
const allLoaded = ref(false);
const currentPage = ref(1);
const pageSize = ref(50);

const emit = defineEmits("getSelectItems", "close");

// 搜索成员数据
async function searchMembers(keyword = '', isLoadMore = false) {
  if (!isLoadMore) {
    loading.value = true;
    currentPage.value = 1;
    memberList.value = [];
    allLoaded.value = false;
  } else {
    loadingMore.value = true;
  }

  try {
    const corpId = localStorage.getItem("corpId");

    // 如果有搜索关键词，使用 searchHlwMembers 进行搜索
    if (keyword.trim()) {
      const result = await searchHlwMembers(corpId, keyword.trim(), currentPage.value, pageSize.value, null, true);
      if (result.success) {
        const newData = result.data?.data || result.data || [];
        if (isLoadMore) {
          memberList.value = [...memberList.value, ...newData];
        } else {
          memberList.value = newData;
        }

        // 检查是否还有更多数据
        if (newData.length < pageSize.value) {
          allLoaded.value = true;
        }
      } else {
        if (!isLoadMore) {
          memberList.value = [];
        }
      }
    } else {
      // 无搜索关键词时使用原来的 getCorpMember 加载数据
      const { success, data } = await getCorpMember({ corpId }, pageSize.value, currentPage.value);
      if (success) {
        const newData = data.data || [];
        if (isLoadMore) {
          memberList.value = [...memberList.value, ...newData];
        } else {
          memberList.value = newData;
        }

        // 检查是否还有更多数据
        if (newData.length < pageSize.value) {
          allLoaded.value = true;
        }
      } else {
        if (!isLoadMore) {
          memberList.value = [];
        }
      }
    }
  } catch (error) {
    console.error('搜索成员失败:', error);
    if (!isLoadMore) {
      memberList.value = [];
    }
  } finally {
    loading.value = false;
    loadingMore.value = false;
  }
}

// 防抖搜索
const handleSearch = debounce(() => {
  searchMembers(searchKeyword.value);
}, 300);

// 加载更多成员
async function loadMoreMembers() {
  if (allLoaded.value || loadingMore.value) return;

  currentPage.value += 1;
  await searchMembers(searchKeyword.value, true);
}

// 获取头像URL，如果没有头像则使用默认头像
function getAvatarUrl(avatar) {
  if (avatar && avatar.trim()) {
    return avatar;
  }
  // 使用默认头像
  return 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png';
}
function checkedMember(item) {
  if (selectMemberType.value === 1 && checkedMemberList.value.length === 1) {
    return;
  }
  const index = checkedMemberList.value.findIndex((i) => i._id === item._id);
  if (index !== -1) {
    checkedMemberList.value.splice(index, 1);
  } else {
    selectItem.value = item;
    checkedMemberList.value.push(item);
  }
}

function remove(item) {
  const index = checkedMemberList.value.findIndex((i) => i._id === item._id);
  if (index !== -1) {
    checkedMemberList.value.splice(index, 1);
  }
}

function showChecked(item, list) {
  let arr = list.filter((i) => i && i._id === item._id);
  return arr.length > 0;
}

function cancel() {
  visible.value = false;
}
function submit() {
  visible.value = false;
  let checkedMembers = checkedMemberList.value.map((i) => i.userid);
  let isleaders = [...new Set(selectLeaders.value.map((i) => i.userid))];
  let leaders = [...new Set(checkedMembers.filter((i) => isleaders.includes(i)))];
  emit("getSelectItems", { leaders, checkedMemberList: checkedMembers });
}

const selectLeaders = ref([]);
function changeLeader(val, user) {
  if (val) {
    selectLeaders.value.push(user);
  } else {
    selectLeaders.value = selectLeaders.value.filter((i) => i.userid !== user.userid);
  }
}

let draggingIndex = ref(-1); // 记录拖拽的标签索引

const dragStart = (index) => {
  draggingIndex.value = index;
};

const dragEnd = () => {
  draggingIndex.value = -1;
};
const drop = (targetIndex) => {
  if (!draggingIndex.value || !targetIndex) return;
  let dragItem = checkedMemberList.value[draggingIndex.value];
  let targetItem = checkedMemberList.value[targetIndex];
  checkedMemberList.value[draggingIndex.value] = targetItem;
  checkedMemberList.value[targetIndex] = dragItem;
};
const noShowLeader = ref(true);
async function openDialog(arr, leaders = [], flag = true, type = 2) {
  noShowLeader.value = flag;
  selectMemberType.value = type;
  selectItem.value = {};

  // 重置搜索状态
  searchKeyword.value = '';
  allLoaded.value = false;

  // 打开弹窗
  visible.value = true;

  // 加载初始数据（少量）
  await searchMembers();

  // 设置已选中的成员
  if (Array.isArray(arr)) {
    checkedMemberList.value = arr
      .map((i) => {
        return memberList.value.find((item) => item.userid === i);
      })
      .filter((i) => i);
    selectLeaders.value = memberList.value.filter((item) => leaders.findIndex((i) => i === item.userid) != -1);
  } else {
    selectLeaders.value = [];
  }
}
defineExpose({
  openDialog,
});
</script>
<style scoped>
.staff-picker-dialog.el-dialog .el-dialog__header {
  display: none;
}

.staff-picker-dialog.el-dialog .el-dialog__body {
  padding: 0 !important;
}
</style>
<style scoped lang="scss">
.member_title {
  // background-color: rgb(24, 143, 243);
  color: #000;
  font-weight: 600;
  line-height: 25px;
}

.member_num_title {
  font-weight: 600;
  color: #000;
  font-size: 14px;
  line-height: 25px;
}

.contact {
  border-radius: 4px;
}

.contact:hover {
  background-color: #eaeaec;
}

.check-active {
  background-color: #d6e4f6;
  border-radius: 4px;
  // color: #fff;
}

.bac-color {
  background-color: #f6f6f7;
}

.r_10 {
  border-radius: 10px;
}

.b_l {
  background-color: #f6f6f7;
  width: 100px;
  height: 30px;
  text-align: center;
  line-height: 30px;
  margin-right: 20px;
  border-radius: 3px;
}

.b_r {
  width: 100px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  color: #fff;
  background: #006eff;
  border-radius: 3px;
}

.cell-radio {
  width: 16px;
  height: 16px;
  cursor: pointer;
}
</style>
