<template>
  <div class="pt-6px mt-10px bg-white" border-bottom>
    <el-form-item class="px-15px" :class="required ? 'is-required' : ''" label-width="auto" :label="name">
      <div class="flex-grow text-blue-500 text-right">
        <el-icon class="cursor-pointer text-16px transform translate-y-2px" @click="add()">
          <Plus />
        </el-icon>
      </div>
    </el-form-item>
    <div v-if="value.length" class="px-15px mt-6px border-t border-gray-200">
      <div v-for="(i,idx) in value" :key="idx" class="py-10px">
        <div class="flex">
          <div class="flex-grow leading-24px text-14px"> {{ idx+1 }}、{{ i.category }}</div>
          <el-icon class="flex-shrink-0 ml-10px text-blue-500 cursor-pointer text-16px" @click="edit(i, idx)">
            <EditPen />
          </el-icon>
        </div>
        <div class="text-14px leading-24px text-gray-500 whitespace-pre-wrap"> {{ i.opinion }}</div>
      </div>
    </div>
  </div>
  <editModal :name="name" :width="width" :visible="visible" :type="type" :category="current.category" :opinion="current.opinion"
    @change="handleChange($event)" @remove="remove()" @close="close()" />
</template>
<script setup>
import { ref, computed } from 'vue';
import useModal from "@/hooks/useModal";
import editModal from './edit-modal.vue';

const emits = defineEmits(['change'])
const props = defineProps({
  form: { type: Object, default: () => ({}) },
  name: { type: String, default: '' },
  required: { type: Boolean, default: false },
  title: { type: String, default: '' },
})
const value = computed(() => Array.isArray(props.form[props.title]) ? props.form[props.title].filter(i => i.category || i.opinion) : []);
const { close, show, visible, width } = useModal(560);
const type = ref('add');
const current = ref({ category: '', opinion: '' });

function change(value) { emits('change', { title: props.title, value }) }

function add() {
  type.value = 'add';
  current.value = { category: '', opinion: '' }
  show()
}

function edit(item, idx) {
  type.value = 'edit';
  current.value = {
    category: typeof item.category === 'string' ? item.category : '',
    opinion: typeof item.opinion === 'string' ? item.opinion : '',
    idx
  }
  show()
}

function handleChange(data) {
  const list = value.value.map(i => ({ category: i.category, opinion: i.opinion }))
  if (type.value === 'add') {
    list.push({ category: data.category, opinion: data.opinion })
  } else if (current.value.idx >= 0) {
    list[current.value.idx] = { category: data.category, opinion: data.opinion }
  }
  change(list)
}

function remove() {
  if (type.value === 'edit' && current.value.idx >= 0) {
    const list = value.value.filter((i, idx) => idx !== current.value.idx)
    change(list)
  }
}

</script>
