import { computed, ref } from "vue";
import { defineStore } from "pinia";
import { getAllCorpMember } from "@/api/corp.js";

export const staffStore = defineStore("staffStore", () => {
  const list = ref([]);
  const enableList = computed(() => list.value.filter((item) => item.accountState != "disable"));
  const request = ref(null);

  async function getList(reload) {
    if (list.value.length > 0 && !reload) return;
    if (!request.value) {
      request.value = getAllCorpMember();
    }
    const { data } = await request.value;
    list.value = data && Array.isArray(data.data) ? data.data : [];
    list.value.sort((a, b) => {
      if (a.superAdminName && !b.superAdminName) return -1;
      if (!a.superAdminName && b.superAdminName) return 1;
      return 0;
    });
    request.value = null;
  }

  return {
    getStaffList: getList,
    staffList: enableList,
  };
});
