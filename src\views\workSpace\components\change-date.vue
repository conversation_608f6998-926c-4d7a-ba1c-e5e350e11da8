<template>
  <el-dialog :model-value="props.visible" :width="width" title="修改计划执行时间" @close="close()">
    <div class="p-15px">
      <el-date-picker v-model="value" class="w-full" value-format="YYYY-MM-DD" :disabled-date="disabledDate" />
    </div>
    <template #footer>
      <div text-center>
        <el-button plain class="w-100px" type="primary" :icon="Plus" @click="close()">取消</el-button>
        <el-button class="w-100px" :loading="loading" type="primary" :icon="Plus" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref, watch } from "vue";
import { ElMessage } from "element-plus";
import dayjs from "dayjs";
import { updatePlannedExecutionTime } from "@/api/todo";

const emits = defineEmits(['close', 'change']);
const props = defineProps({
  todo: { type: String, default: '' },
  visible: { type: Boolean, default: false },
  width: { type: String, default: '500px' },
})

const value = ref('');
function close() {
  emits('close')
}
function disabledDate(time) {
  return dayjs(time).isBefore(dayjs().startOf('day'))
}

const loading = ref(false);
async function confirm() {
  if (!props.todo) return;
  if (value.value && dayjs(value.value).isValid() && (dayjs(value.value).isAfter(dayjs().startOf('day')) || dayjs(value.value).isSame(dayjs().startOf('day')))) {
    loading.value = true;
    const { success, message } = await updatePlannedExecutionTime({ id: props.todo._id, date: value.value, corpId: props.todo.corpId })
    if (success) {
      ElMessage.success('修改成功')
      emits('change', value.value)
      close()
    } else {
      ElMessage.error(message)
    }
    loading.value = false
  } else {
    ElMessage.error('请选择正确的日期')
  }

}

watch(() => props.visible, (n) => {
  if (n) {
    value.value = props.todo && props.todo.plannedExecutionTime && dayjs(props.todo.plannedExecutionTime).isValid() ? dayjs(props.todo.plannedExecutionTime).format('YYYY-MM-DD') : ''
  }
})

</script>