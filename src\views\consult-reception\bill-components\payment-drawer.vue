<template>
  <el-drawer :model-value="visible" title="请选择关联缴费单" size="70%" style="--el-drawer-padding-primary: 0" @close="close()">
    <my-layout v-loading="loading">
      <layout-item>
        <div class="px-15px">
          <div ref="noticeRef" class="text-14px leading-21px text-gray-500 py-10px">
            <el-icon class="text-red-500 transform translate-y-1px mr-5px">
              <InfoFilled />
            </el-icon>
            <span>须知：</span>
            <br />
            <span>1、一次开单只允许关联一个缴费单。</span>
            <br />
            <span>2、“绿色”为门诊缴费单，“橙色”为住院缴费单，“灰色”为已经被绑定过缴费单，不允许再次绑定。</span>
          </div>
          <div class="flex items-center" v-if="billGroup.length > 0">
            <el-icon class="mr-5px -mt-20px cursor-pointer text-24px" :class="initialIndex === 0 ? 'text-gray-300' : ''" @click="toggle('prev')">
              <ArrowLeftBold />
            </el-icon>
            <div class="w-0 flex-grow">
              <el-carousel :autoplay="false" :initial-index="initialIndex" :loop="false" arrow="never" indicator-position="none" ref="carouselRef" trigger="click" height="120px" @change="onCarouselChange">
                <el-carousel-item v-for="(item, idx) in billGroup" :key="idx" :name="idx">
                  <div class="flex text-white select-none">
                    <div v-for="(card, index) in item" :key="card.id" class="cursor-pointer relative rounded px-10px pt-5px pb-10px bg-green-500" :class="[index > 0 ? 'ml-10px' : '', billedDate[card.settlementTime] ? 'bg-gray-400' : card.classnames]" :style="{ width: `${cardWidth}px` }" @click="viewBillId = card.id">
                      <div class="mx-auto">
                        <div class="h-30px flex justify-between items-center">
                          <div class="font-18px font-semibold">{{ card.billType }}</div>
                          <div v-if="!billedDate[card.settlementTime]" class="h-22px w-22px flex items-center justify-center rounded" :class="selectMap[card.id] ? 'bg-blue-500' : 'bg-white'" @click.stop="select(card)">
                            <el-icon v-if="selectMap[card.id]" class="text-white text-18px"><Select /></el-icon>
                          </div>
                          <!-- <div v-if="!billedDate[card.settlementTime]" class="h-30px flex items-center pr-10px"
                            @click.stop="select(card)">
                            <el-checkbox class="cursor-pointer pointer-events-none" :model-value="selectMap[card.id]" />
                          </div> -->
                        </div>
                        <div class="h-25px leading-25px text-14px whitespace-nowrap">结算时间：{{ card.settlementTime }}</div>
                        <div class="h-25px leading-25px text-14px truncate">结算金额：￥{{ card.totalFeeStr }}</div>
                      </div>
                      <div v-if="viewBillId === card.id" class="triangle-up absolute left-1/2 -bottom-24px transform -translate-x-1/2" :class="billedDate[card.settlementTime] ? 'text-gray-400' : card.triangleColor"></div>
                    </div>
                  </div>
                </el-carousel-item>
              </el-carousel>
            </div>
            <el-icon class="ml-5px -mt-20px cursor-pointer text-24px" :class="initialIndex === billGroup.length - 1 ? 'text-gray-300' : ''" @click="toggle('next')">
              <ArrowRightBold />
            </el-icon>
          </div>
        </div>
      </layout-item>
      <layout-main :scroll="false">
        <el-table stripe border class="h-full" :data="groupTableList" :span-method="mergeMethod">
          <el-table-column property="type" :min-width="120" label="类型">
            <template #default="{ row: { doctorAdvice, rowType } }">
              <span v-if="rowType === 'countRow'"></span>
              <span v-else>{{ doctorAdvice }}</span>
            </template>
          </el-table-column>
          <el-table-column property="orderName" :min-width="160" label="项目名称" />
          <el-table-column property="billDept" :min-width="120" label="开单科室" />
          <el-table-column property="billDoctor" :min-width="120" label="开单医生" />
          <el-table-column property="projectPrice" :min-width="120" label="单价" />
          <el-table-column property="projectCount" :min-width="120" label="数量" />
          <el-table-column property="itemFee" :min-width="120" label="总金额">
            <template #default="{ row: { itemFee, rowType, typeFee } }">
              <span v-if="rowType === 'countRow'" class="font-semibold">小计： ￥{{ typeFee.toFixed(2) }}</span>
              <span>{{ itemFee >= 0 ? `￥${itemFee.toFixed(2)}` : "" }}</span>
            </template>
          </el-table-column>
        </el-table>
      </layout-main>
      <layout-item>
        <div class="text-center mt-10px py-10px bg-white" common-shadow--r>
          <el-button plain class="w-100px" @click="close()">取消</el-button>
          <el-button class="w-100px" type="primary" @click="confirm()">确定</el-button>
        </div>
      </layout-item>
    </my-layout>
  </el-drawer>
</template>
<script setup>
import { computed, ref, onMounted, watch } from "vue";
import { useElementBounding } from "@vueuse/core";
import { dayjs, ElMessage } from "element-plus";
import usePayment from "./usePayment";
import { getHisFeeRecord } from "@/api/customerHisSync";
import { getProjectCreateTreatmentOrder } from "@/api/project-manage";
import { getFeeRecord } from "@/api/member";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import BigNumber from "bignumber.js";
const emits = defineEmits(["close", "change"]);
const props = defineProps({
  visible: { type: Boolean, default: false },
  customer: { type: String, default: false },
});
const billList = ref([]);
const viewBillId = ref("");
const initialIndex = ref(0);
const loading = ref(false);
const carouselRef = ref();
const noticeRef = ref();
const selections = ref([]);
const selectMap = computed(() => {
  return selections.value.reduce((acc, item) => {
    acc[item.id] = true;
    return acc;
  }, {});
});
const billedFeeList = ref([]);
const billedDate = computed(() =>
  billedFeeList.value.reduce((acc, item) => {
    if (item.settlementTime) acc[item.settlementTime] = true;
    return acc;
  }, {})
);
const { width } = useElementBounding(noticeRef);
const tableData = computed(() => {
  const item = billList.value.find((item) => item.id === viewBillId.value);
  return item ? item.itemList : [];
});
const { list, groupTableList, mergeMethod } = usePayment(7);
const groupNum = computed(() => {
  const num = Math.floor((width.value - 60) / 240);
  return num > 1 ? num : 1;
});
const cardWidth = computed(() => {
  const restWidth = width.value - 60 - groupNum.value * 10 - 10;
  return Math.floor(restWidth / groupNum.value);
});
const billGroup = computed(() => {
  const result = [];
  for (let i = 0; i < billList.value.length; i += groupNum.value) {
    result.push(billList.value.slice(i, i + groupNum.value));
  }
  return result;
});
function close() {
  emits("close");
}
async function confirm() {
  if (loading.value) return;
  if (selections.value.length) {
    const bill = selections.value.reduce(
      (acc, item) => {
        acc["settlementTime"] = acc["settlementTime"] || dayjs(item.settlementTime).format("YYYY-MM-DD");
        acc["itemList"] = [...(acc["itemList"] || []), ...item.itemList];
        acc["totalFee"] = acc["totalFee"].plus(item.totalFee);
        acc["billType"] = acc["billType"] || item.billType;
        return acc;
      },
      { totalFee: new BigNumber(0) }
    );
    bill.totalFee = bill.totalFee.toNumber().toFixed(2);
    bill.itemList = await getBillList(bill.itemList);
    console.log(bill);
    emits("change", bill);
    close();
  } else {
    ElMessage.info("请选择关联账单");
  }
}

async function getBillList(list) {
  loading.value = true;
  const projectNos = list.map((i) => i.projectId);
  const { success, data } = await getProjectCreateTreatmentOrder({ projectNos });
  const projects = Array.isArray(data.data) ? data.data : [];
  const m = projects.reduce((acc, item) => {
    acc.set(item.projectNo, Boolean(item.createTreatmentOrder));
    return acc;
  }, new Map());
  return list.map((i) => ({
    ...i,
    createTreatmentOrder: m.get(i.projectId),
  }));
  loading.value = false;
}

function onCarouselChange(i) {
  initialIndex.value = i;
}
function select(card) {
  selections.value = [card];
  return;
  if (selectMap.value[card.id]) {
    selections.value = selections.value.filter((item) => item.id !== card.id);
    return;
  }
  const item = selections.value.find((item) => {
    const isSameDay = dayjs(item.settlementTime).isSame(dayjs(card.settlementTime), "day");
    const isSameType = item.billType === card.billType;
    return !(isSameDay && isSameType);
  });
  if (item) {
    ElMessage.info("请选择同一天同一类型的账单");
    return;
  }
  selections.value.push(card);
  // billId.value = id === billId.value ? "" : id;
}
function toggle(type) {
  if (type === "prev") {
    carouselRef.value.prev();
  } else {
    carouselRef.value.next();
  }
}
async function getbilledFeeList() {
  const params = {
    memberId: props.customer._id,
    billStatus: "billed",
    page: 1,
    pageSize: 1000,
  };
  const { success, data } = await getFeeRecord(params);
  if (success) {
    billedFeeList.value = data.list;
  }
}
async function getList() {
  if (!props.customer.customerNumber) return;
  const params = {
    idNo: props.customer.customerNumber,
    startTime: dayjs().subtract(180, "day").format("YYYY-MM-DD"),
    endTime: dayjs().format("YYYY-MM-DD"),
  };
  const { success, data, message } = await getHisFeeRecord(params);
  const list = Array.isArray(data.list) ? data.list : [];
  // const success = true;
  // const list = [...hisBill];

  const m = new Map();
  const res = [];
  for (let item of list) {
    const billType = item.billType === "住院" ? "inhospital" : item.billType === "门诊" ? "outpatient" : "";
    const key = item.settlementTime && billType ? `${billType}__${item.settlementTime}` : "";
    if (key && !m.has(key)) {
      const itemList = list.filter((i) => i.settlementTime === item.settlementTime && i.billType === item.billType);
      const payment = itemList.reduce((acc, cur) => {
        if (Number(cur.payment) > 0) {
          return acc.plus(cur.payment);
        }
        return acc;
      }, new BigNumber(0));
      res.push({
        id: key,
        doctorAdvice: item.doctorAdvice,
        classnames: item.billType === "门诊" ? "bg-green-500" : "bg-orange-500",
        triangleColor: item.billType === "门诊" ? "text-green-500" : "text-orange-500",
        billType: item.billType,
        settlementTime: item.settlementTime,
        itemList,
        totalFee: payment.toNumber(),
        totalFeeStr: payment.toNumber().toFixed(2),
      });
      m.set(key, true);
    }
  }
  billList.value = res.sort((a, b) => (dayjs(a.settlementTime).isAfter(b.settlementTime) ? -1 : 1));
  const item = res.find((i) => i.id === viewBillId.value) || res[0];
  viewBillId.value = item ? item.id : "";
  if (!success) ElMessage.error(message || "获取关联账单失败");
}
watch(
  () => props.visible,
  async (n) => {
    if (n) {
      loading.value = true;
      billList.value = [];
      selections.value = [];
      viewBillId.value = "";
      await Promise.all([getList(), getbilledFeeList()]);
      loading.value = false;
    }
  }
);
watch(tableData, (n) => (list.value = n || []));
</script>
<style scoped>
.triangle-up {
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-bottom: 20px solid currentColor;
}
</style>
