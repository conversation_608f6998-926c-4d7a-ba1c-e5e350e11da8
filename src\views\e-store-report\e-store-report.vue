<template>
  <my-layout>
    <layout-item>
      <div class="overflow-hidden mb-8px">
        <el-row :gutter="10">
          <el-col v-for="i in count" :key="i.key" :span="6">
            <div class="flex flex-col items-center bg-white rounded px-20px py-16px h-80px text-center">
              <div class="w-full pt-4px truncate text-18px font-semibold">{{ i.value }}</div>
              <div class="w-full mt-8px text-14px truncate text-gray-500">{{ i.label }}</div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="py-5px flex bg-white">
        <expand-filter-box v-model:can-expand="canExpand" :enable-expand="true" class="flex-grow" :expand="expand">
          <radio-filter
            v-model="arrivalStatus"
            label="到院状态"
            :list="[
              { label: '未到院', value: 'notInHospital' },
              { label: '已到院', value: 'inHospital' },
            ]"
          ></radio-filter>
          <date-range-filter v-model:dates="createrDates" label="报备日期" :text="createrDatesText" :width="320" />
          <filter-info-source v-model="infoSource" />
          <check-box-filter v-model="counselors" label="所属咨询" :list="counselorList">
            <template #item="{ item }">
              <ww-user :openid="item.value"></ww-user>
            </template>
          </check-box-filter>
          <check-box-filter v-model="introducers" label="所属开发" :list="peopleList" @clear="clearIntroducers" v-if="isManager">
            <template #item="{ item }">
              <ww-user :openid="item.value"></ww-user>
            </template>
          </check-box-filter>
          <filter-customer-source v-model="customerSource" label="开发渠道" />
          <div class="text-gray-500 text-14px flex items-center ml-10px">
            <div class="filter-label pr-5px">咨询科室:</div>
            <dept-select v-model="deptId" placeholder="请选择科室" @change="handleDeptChange" />
          </div>
          <div class="text-gray-500 text-14px flex items-center ml-10px">
            <div class="filter-label pr-5px">意向项目:</div>
            <project-intent-select v-model="projectIds" @change="handleProjectChange" placeholder="请选择意向项目" type="filter" style="width: 200px" :deptId="deptId" multiple />
          </div>
          <date-range-filter v-model:dates="inHospitalDates" label="到院时间" :width="320" :text="inHospitalDatesText" />
          <date-range-filter v-model:dates="appointmentDates" label="预约时间" :width="320" :text="appointmentDatesText" />
          <input-filter v-model="name" label="客户姓名" placeholder="请输入" :width="200" />
          <input-filter v-model="mobile" label="客户手机号" placeholder="请输入" :width="200" />
        </expand-filter-box>
        <div class="flex-shrink-0 flex items-center h-40px pr-15px">
          <el-button v-if="canExpand" text plain @click="expand = !expand">
            <span class="mr-5px">{{ expand ? "收起筛选" : "更多筛选" }}</span>
            <el-icon v-if="expand">
              <ArrowUpBold />
            </el-icon>
            <el-icon v-else>
              <ArrowDownBold />
            </el-icon>
          </el-button>
          <el-button type="primary" @click="search">查询</el-button>
          <el-button type="success" @click="exportToExcel">导出Excel</el-button>
          <el-button plain type="primary" @click="addReport">新增报备</el-button>
        </div>
      </div>
      <div class="flex flex-wrap justify-between items-center px-15px pb-10px b-fff mt-5px">
        <div class="flex items-center pt-10px">
          <div class="mr-15px">
            已选择：
            <span class="inline-block min-w-30px text-center text-red-500">
              {{ selectCustomers.length }}
            </span>
            位
          </div>
          <el-button plain type="primary" @click="toBatchPlan">批量回访</el-button>
        </div>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <el-table stripe border height="100%" :data="tableData" v-loading="loading" @row-click="handleRowClick" @selection-change="handleSelectionChange" ref="tableRef">
        <el-table-column align="center" type="selection" width="55" />
        <el-common-column prop="" label="到院状态" :min-width="100">
          <template #default="{ row: { inHospitalTimes } }">
            <div :style="{ color: isInHospital(inHospitalTimes) ? 'green' : 'red' }">
              {{ isInHospital(inHospitalTimes) ? "已到院" : "未到院" }}
            </div>
          </template>
        </el-common-column>
        <el-common-column prop="createTime" label="报备时间" :min-width="160">
          <template #default="{ row: { createTime, reportTimeStamp } }">
            <td-wrapper>{{ reportTimeStamp || createTime ? dayjs(reportTimeStamp || createTime).format("YYYY-MM-DD HH:mm") : "" }}</td-wrapper>
          </template>
        </el-common-column>
        <el-common-column prop="name" label="姓名" :min-width="120">
          <template #default="{ row: { _id, name } }">
            <span @click.stop="toCustomerDetail(_id)" main-color class="pointer hover:underline underline-offset-2">
              {{ name }}
            </span>
          </template>
        </el-common-column>
        <el-common-column prop="mobile" label="联系方式 " :min-width="120">
          <template #default="{ row: { mobile } }">
            <span>{{ maskPhone(mobile, 3, 4) }}</span>
          </template>
        </el-common-column>
        <el-common-column label="微信联系人" prop="wechatContact" :width="120" classnames="w-full">
          <template #default="{ row }">
            <wx-contact-td :customerId="row._id" :externalUserId="row.externalUserId" :row="row" @change="getList" />
          </template>
        </el-common-column>
        <el-common-column prop="project" label="意向项目" :min-width="240">
          <template #default="{ row: { projectNames } }">
            <el-tooltip :disabled="!projectNames" placement="top" effect="light" :content="projectNames" popper-class="max-w-480px">
              <span>{{ projectNames }}</span>
            </el-tooltip>
          </template>
        </el-common-column>
        <el-common-column prop="introducer" label="所属开发" :min-width="120">
          <template #default="{ row: { introducer } }">
            <ww-user v-if="introducer" :openid="introducer" />
          </template>
        </el-common-column>
        <el-common-column prop="infoSource" label="信息来源" :min-width="180">
          <template #default="{ row }">
            <td-wrapper>{{ getInfoSource(row) }}</td-wrapper>
          </template>
        </el-common-column>
        <el-common-column prop="department" label="咨询科室" :min-width="120">
          <template #default="{ row: { projectDeptIds } }">
            <dept-name-transformer :dept-id="projectDeptIds" />
          </template>
        </el-common-column>
        <el-common-column prop="customerSource" label="开发渠道" :min-width="240">
          <template #default="{ row }">
            <td-wrapper>{{ getSource(row) }}</td-wrapper>
          </template>
        </el-common-column>
        <el-common-column prop="plan" label="跟进计划" :width="100">
          <template #default="{ row: { hasPlan } }">
            <td-wrapper :class="hasPlan ? 'w-full' : 'w-full text-gray-400'">{{ hasPlan ? "有" : "无" }}计划</td-wrapper>
          </template>
        </el-common-column>
        <el-common-column prop="counselor" label="所属咨询" :min-width="120">
          <template #default="{ row: { counselor } }">
            <ww-user v-if="counselor" :openid="counselor" />
          </template>
        </el-common-column>
        <el-common-column prop="consume" label="消费情况" :min-width="90">
          <template #default="{ row: { consumeRecord } }">
            {{ getConsumeStatus(consumeRecord) }}
          </template>
        </el-common-column>
        <el-common-column prop="recentPayTime" label="最近消费时间" :min-width="160">
          <template #default="{ row: { consumeRecord } }">
            <div v-if="consumeRecord && consumeRecord.length">
              {{ dayjs(consumeRecord[consumeRecord.length - 1].createTime).format("YYYY-MM-DD HH:mm") }}
            </div>
          </template>
        </el-common-column>
        <el-common-column prop="firstArriveTime" label="首次到院时间" :min-width="160">
          <template #default="{ row: { inHospitalTimes } }">
            <td-wrapper>{{ inHospitalTimes && Array.isArray(inHospitalTimes) && inHospitalTimes[0] ? dayjs(inHospitalTimes[0]).format("YYYY-MM-DD HH:mm") : "" }}</td-wrapper>
          </template>
        </el-common-column>
        <el-common-column prop="lastAppointmentTime" label="预约时间" :min-width="160">
          <template #default="{ row: { lastAppointmentTime } }">
            <td-wrapper>{{ lastAppointmentTime ? dayjs(lastAppointmentTime).format("YYYY-MM-DD HH:mm") : "" }}</td-wrapper>
          </template>
        </el-common-column>
        <!-- <el-common-column prop="age" label="性别/年龄" :min-width="100">
          <template #default="{ row: { sex, age } }">
            <div>
              {{ sex }}
              <span v-if="sex && age">/</span>
              {{ age }}
            </div>
          </template>
        </el-common-column> -->
        <el-common-column prop="reportDesc" label="报备说明" :min-width="300">
          <template #default="{ row: { reportDesc } }">
            <el-popover placement="top" width="400" trigger="click" v-if="reportDesc">
              <template #reference>
                <div class="cursor-pointer whitespace-pre-wrap leading-5 max-h-10 overflow-hidden line-clamp-2 hover:text-blue-500 transition-colors" @click.stop>
                  {{ reportDesc }}
                </div>
              </template>
              <div class="whitespace-pre-wrap text-14px leading-6 max-h-300px overflow-y-auto">
                {{ reportDesc }}
              </div>
            </el-popover>
            <div v-else class="text-gray-400">暂无说明</div>
          </template>
        </el-common-column>
        <el-table-column prop="action" fixed="right" class-name="el-table-fixed-column--shadow" label="操作" width="220">
          <template #default="{ row }">
            <el-text class="cursor-pointer mr-5px" type="primary" @click="toDetail(row)">报备详情</el-text>
            <el-text class="cursor-pointer mr-5px" type="primary" @click="toPlan(row)">回访计划</el-text>
            <el-text class="cursor-pointer mr-5px" type="primary" @click="toAppointment(row)">预约</el-text>
          </template>
        </el-table-column>
      </el-table>
    </layout-main>
    <layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize" @handle-current-change="changePage" />
    </layout-item>
  </my-layout>
  <management-plan-modal :visible="planVisible" :width="planWidth" @close="planClose" :data="customer" @success="changeSuccess" :executorUserId="executorUserId" />
  <report-table-drawer :customer="existCustomer" :visible="drawerVisible" @close="drawerClose" @showCustomerDetail="toCustomerDetail" @success="changeSuccess" />
  <report-modal :peopleList="peopleList" :visible="visible" :width="width" @close="close" @success="changeSuccess" :customer="customer" @showExistCustomer="showExistCustomer" />
  <customer-detail :enableAction="['managementPlan']" :visible="customerDetailVisible" @close="customerDetailVisible = false" :customerId="customerId" title="客户详情" @update="changeSuccess" customerType="corpCustomer" />
  <project-picker :value="projects" :visible="projectModalVisible" :width="projectModalWidth" @close="closeProjectModal()" @change="changeProjects" />
  <report-person-modal :customer="reportCustomer" :visible="personVisible" :width="personWidth" @close="closePersonModal" @change="getList" />
  <component v-if="ManagePlanDrawer" :is="ManagePlanDrawer" :customer="customer" :visible="managePlanDrawerVisible" @close="closeDrawer" />
  <batch-followup ref="batchFollowupRef" :selectCustomers="selectCustomers" :customer="customer" :teamId="teamId" @change="getList" />
  <appointment-modal :customer="appointmentCustomer" :visible="appointmentVisible" :width="appointmentWidth" @close="closeAppointment" @success="onAppointmentSuccess" />
  <batch-followup-plan :visible="batchPlanVisible" :customerList="batchPlanCustomers" :teamId="teamId" @close="closeBatchPlan" @success="onBatchPlanSuccess" />
</template>
<script setup>
import { computed, onMounted, ref, watch } from "vue";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import { searchCorpCustomer, getCustomersCount, getInHospitalCustomersCount } from "@/api/member";
import useElPagination from "@/hooks/useElPagination";
import useManagePlanDrawer from "@/composables/useManagePlanDrawer";
import useModal from "@/hooks/useModal";
import { memberStore } from "@/store/member";
import { staffStore } from "@/store/staff";
import { teamStore } from "@/store/team";
import { maskPhone } from "@/utils";
import { ElMessage, ElLoading } from "element-plus";
import { useDeptStore } from "@/components/dept-components/dept-store";

import { CheckBoxFilter, DateRangeFilter, InputFilter, filterCustomerSource, filterInfoSource, expandFilterBox, RadioFilter } from "@/components/filter-bar";
import customerDetail from "@/views/member/detail/index.vue";
import elCommonColumn from "@/components/el-common-column/el-common-column.vue";
import managementPlanModal from "@/components/management-plan-modal/index.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import projectPicker from "@/components/project-picker/project-picker.vue";
import projectIntentSelect from "@/components/benefit-management/project-intent-select.vue";
import reportTableDrawer from "./report-table-drawer.vue";
import reportModal from "./report-modal.vue";
import reportPersonModal from "./report-person-modal.vue";
import WwUser from "@/components/ww-user/index.vue";
import wxContactTd from "@/components/wx-contact-td/wx-contact-td.vue";
import batchFollowup from "@/components/batch-followup/index.vue";
import appointmentModal from "@/components/appointment-modal/index.vue";
import batchFollowupPlan from "./batch-followup-plan.vue";
import DeptNameTransformer from "@/components/dept-components/dept-name-transformer.vue";
import DeptSelect from "@/components/dept-components/dept-select.vue";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";

const dates = ref([]);
const customerId = ref("");
const selectCustomers = ref([]);
const inHospitalDates = ref([]);
const createrDates = ref([dayjs().startOf("month").format("YYYY-MM-DD"), dayjs().endOf("month").format("YYYY-MM-DD")]);
const appointmentDates = ref([]);
const inHospitalDatesText = computed(() => inHospitalDates.value.join(" ~ "));
const createrDatesText = computed(() => createrDates.value.join(" ~ "));
const appointmentDatesText = computed(() => appointmentDates.value.join(" ~ "));

const stages = ref([]);
const customerSource = ref([]);
const infoSource = ref([]);
const arrivalStatus = ref("");
const list = ref([]);
const total = ref(0);
const name = ref("");
const mobile = ref("");
const projectIds = ref([]);
const selectedProjects = ref([]);
const canExpand = ref(false);
const expand = ref(false);
const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());
const { isAdmin } = storeToRefs(memberStore());
const { currentTeamId, managerList } = storeToRefs(teamStore());
const { judgmentIsAdmin } = memberStore();
const reportCustomer = ref({});
const existCustomer = ref({});
const counselors = ref([]);
const customerDetailVisible = ref(false);
const peopleList = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("onlineCustomerService")).map((i) => ({ value: i.userid, label: i.name }));
});
const counselorList = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("consultant")).map((i) => ({ value: i.userid }));
});
const isManager = computed(() => {
  return managerList.value.includes(10005) || isAdmin.value;
});

const selectedIntroducers = ref([]);
const hasExplicitlyClearedIntroducers = ref(false);
const introducers = computed({
  get() {
    if (selectedIntroducers.value.length > 0) {
      return selectedIntroducers.value;
    }
    if (hasExplicitlyClearedIntroducers.value) {
      return [];
    }
    return managerList.value.includes(10005) || isAdmin.value ? [localStorage.getItem("userId")] : [];
  },
  set(newVal) {
    selectedIntroducers.value = newVal;
    hasExplicitlyClearedIntroducers.value = false;
  },
});

const deptId = ref("");
const selectedDept = ref({});

const deptStore = useDeptStore();

function toCustomerDetail(id) {
  customerId.value = id;
  customerDetailVisible.value = true;
}

const loading = ref(false);
const customer = ref({});
const tableRef = ref(null);
const executorUserId = ref("");
const { page, pageSize, changePage, changeSize } = useElPagination(getList);
const { close, show, visible, width } = useModal(640);
const { close: drawerClose, show: drawerShow, visible: drawerVisible } = useModal();
const { close: planClose, show: planShow, visible: planVisible, width: planWidth } = useModal(640);
const { close: closePersonModal, show: showPersonModal, visible: personVisible, width: personWidth } = useModal(640);
const { visible: managePlanDrawerVisible, ManagePlanDrawer, openDrawer, closeDrawer } = useManagePlanDrawer();
const { close: closeAppointment, show: showAppointment, visible: appointmentVisible, width: appointmentWidth } = useModal(500);
const appointmentCustomer = ref({});
const batchPlanCustomers = ref([]);
const batchFollowupRef = ref(null);
const teamId = computed(() => currentTeamId.value);

const appointmentRecordVisible = ref(false);

// 批量回访计划相关
const batchPlanVisible = ref(false);

function getConsumeStatus(arr = []) {
  if (arr.length === 0) {
    return "未消费";
  } else if (arr.length === 1) {
    return "已消费";
  } else {
    return "多次消费";
  }
}

function isInHospital(inHospitalTimes) {
  return inHospitalTimes && Array.isArray(inHospitalTimes) && inHospitalTimes.length > 0;
}

function updateReportUser(row) {
  reportCustomer.value = row;
  showPersonModal();
}

function handleProjectChange(projects) {
  console.log("Selected projects:", projects);
  selectedProjects.value = projects;
}

function handleDeptChange(dept) {
  selectedDept.value = dept;
}

function clearIntroducers() {
  selectedIntroducers.value = [];
  hasExplicitlyClearedIntroducers.value = true;
}

function getPersonResponsibles(items) {
  return items
    .map((i) => {
      return i.corpUserId && i.teamId === currentTeamId.value ? i.corpUserId : "";
    })
    .filter((i) => i)
    .flat();
}

function getSource(customer) {
  return customer && Array.isArray(customer.customerSource) && customer.customerSource.length ? customer.customerSource.join(" / ") : "";
}

function getInfoSource(customer) {
  return customer && Array.isArray(customer.infoSource) && customer.infoSource.length ? customer.infoSource.join(" 、 ") : "";
}

watch(
  () => currentTeamId.value,
  () => {
    getList();
    getStatics();
  }
);
async function getList() {
  if (loading.value) return;
  loading.value = true;
  let params = {
    corpId: localStorage.getItem("corpId"),
    page: page.value,
    pageSize: pageSize.value,
    showHasPlan: "YES",
    showProjectName: "YES",
    addMethod: "eStoreReport",
    showConsumeAmount: "YES",
    showAppointmentTime: "YES",
    arrivalStatus: arrivalStatus.value,
  };
  if (name.value) params.name = name.value;
  if (mobile.value) params.mobile = mobile.value;
  if (dates.value.length) params.createTime = dates.value;
  if (stages.value.length) params.stages = stages.value;
  if (customerSource.value.length) params.customerSource = customerSource.value;
  if (infoSource.value.length) params.infoSource = infoSource.value;
  if (inHospitalDates.value.length) params.inHospitalDates = inHospitalDates.value;
  if (counselors.value.length) params.counselors = counselors.value;
  if (deptId.value) {
    params.deptId = deptId.value;
    params.projectIds = (await deptStore.fetchProjectIdsByDeptId(deptId.value)) || [];
  }

  if (createrDates.value.length) {
    params.startCreateTime = createrDates.value[0];
    params.endCreateTime = createrDates.value[1];
  }

  if (appointmentDates.value.length) {
    params.startAppointmentTime = appointmentDates.value[0];
    params.endAppointmentTime = appointmentDates.value[1];
  }

  if (projectIds.value && projectIds.value.length > 0) params.projectIds = projectIds.value;
  if (isManager.value) {
    const userIds = peopleList.value.map((i) => i.value);
    params.introducers = !introducers.value.length ? userIds : introducers.value;
  } else {
    params.introducers = [localStorage.getItem("userId")];
  }
  const { data } = await searchCorpCustomer(params);
  loading.value = false;
  clearSelectCustomers();
  list.value = Array.isArray(data.list)
    ? data.list.map((i) => {
        return {
          ...i,
          counselor: i.counselorRecord && Array.isArray(i.counselorRecord) ? i.counselorRecord[i.counselorRecord.length - 1]?.counselor : "",
          introducer: i.introducerRecord && Array.isArray(i.introducerRecord) ? i.introducerRecord[i.introducerRecord.length - 1]?.introducer : "",
        };
      })
    : [];
  total.value = data.total;
}

const tableData = computed(() => {
  return list.value.map((i) => {
    const personResponsibles = Array.isArray(i.personResponsibles) ? i.personResponsibles : [];
    const teamResponsibles = personResponsibles.reduce((list, i) => {
      if (i.teamId && i.teamId === currentTeamId.value && !list.some((item) => item.corpUserId === i.corpUserId)) {
        list.push({ ...i });
      }
      return list;
    }, []);
    const projectNames = Array.isArray(i.projectNames) ? i.projectNames.join(" / ") : "";
    return { ...i, teamResponsibles, projectNames };
  });
});

const count = ref([
  { key: 1, label: "本年合计报备人数", value: 0 },
  { key: 2, label: "本年合计到院人数", value: 0 },
  { key: 3, label: "今日报备人数", value: 0 },
  { key: 4, label: "今日到院人数", value: 0 },
]);

async function getStatics() {
  const todayDates = [dayjs().format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")];
  const yearDates = [dayjs().startOf("year").format("YYYY-MM-DD"), dayjs().endOf("year").format("YYYY-MM-DD")];
  let query = {
    corpId: localStorage.getItem("corpId"),
  };
  if (!isManager.value) query.customerType = "myCustomer";
  if (isManager.value) {
    const userIds = peopleList.value.map((i) => i.value);
    query.introducers = !introducers.value.length ? userIds : introducers.value;
  } else {
    query.reportTeamId = currentTeamId.value;
    query.introducers = [localStorage.getItem("userId")];
  }
  const allReportCountTask = getCustomersCount({
    ...query,
    dates: yearDates,
    params: {
      addMethod: "eStoreReport",
    },
  });
  const todayReportCountTask = getCustomersCount({
    ...query,
    dates: todayDates,
    params: {
      addMethod: "eStoreReport",
    },
  });
  const allInHospitalCountTask = getInHospitalCustomersCount({
    ...query,
    dates: yearDates,
  });
  const todayInHospitalCountTask = getInHospitalCustomersCount({
    ...query,
    dates: todayDates,
  });

  const [allReportCountRes, todayReportCountDataRes, allInHospitalCountRes, todayInHospitalCountRes] = await Promise.all([allReportCountTask, todayReportCountTask, allInHospitalCountTask, todayInHospitalCountTask]);
  count.value = [
    { key: 1, label: "本年合计报备人数", value: allReportCountRes.data.count },
    { key: 2, label: "本年合计到院人数", value: allInHospitalCountRes.data.count },
    { key: 3, label: "今日报备人数", value: todayReportCountDataRes.data.count },
    { key: 4, label: "今日到院人数", value: todayInHospitalCountRes.data.count },
  ];
}

function toDetail(row) {
  existCustomer.value = row;
  drawerShow();
}

function showExistCustomer(customer) {
  existCustomer.value = { ...customer };
  drawerShow();
}

function toPlan(row) {
  customer.value = row;
  openDrawer();
}

function toAppointment(row) {
  appointmentCustomer.value = row;
  showAppointment();
}

function onAppointmentSuccess() {
  getList();
  closeAppointment();
}

// 批量回访计划相关处理函数
function closeBatchPlan() {
  batchPlanVisible.value = false;
  batchPlanCustomers.value = [];
}

function onBatchPlanSuccess() {
  getList();
  getStatics();
  closeBatchPlan();
  ElMessage.success("批量回访计划生成成功");
}

function search() {
  page.value = 1;
  getList();
  getStatics();
}

onMounted(async () => {
  await getStaffList(true);
  await judgmentIsAdmin();
  await deptStore.fetchDeptList();
  getList();
  getStatics();
});

function addReport() {
  customer.value = {};
  show();
}
async function changeSuccess() {
  close();
  getList();
  getStatics();
}

function selectable(row) {
  return true;
}

function batchAction() {
  if (selectCustomers.value.length === 0) {
    ElMessage.warning("请先选择客户");
    return;
  }
  batchFollowupRef.value && batchFollowupRef.value.showEditModal();
}

function clearSelectCustomers() {
  selectCustomers.value = [];
  tableRef.value && tableRef.value.clearSelection();
}

function handleSelectionChange(selection) {
  selectCustomers.value = selection;
}

function handleRowClick(row, { property }) {
  if ("action" !== property) {
    toDetail(row);
  }
}

// 导出Excel
async function exportToExcel() {
  if (loading.value) {
    ElMessage.warning("数据正在加载中，请稍后再试");
    return;
  }

  // 显示加载状态
  const loadingInstance = ElLoading.service({
    lock: true,
    text: "正在获取数据，请稍候...",
    background: "rgba(0, 0, 0, 0.7)",
  });

  try {
    // 先获取总数据量
    let totalParams = {
      corpId: localStorage.getItem("corpId"),
      page: 1,
      pageSize: 1, // 只获取总数，不需要实际数据
      showHasPlan: "YES",
      showProjectName: "YES",
      addMethod: "eStoreReport",
      showConsumeAmount: "YES",
      showAppointmentTime: "YES",
      arrivalStatus: arrivalStatus.value,
    };

    // 添加筛选条件
    if (name.value) totalParams.name = name.value;
    if (mobile.value) totalParams.mobile = mobile.value;
    if (dates.value.length) totalParams.createTime = dates.value;
    if (stages.value.length) totalParams.stages = stages.value;
    if (customerSource.value.length) totalParams.customerSource = customerSource.value;
    if (infoSource.value.length) totalParams.infoSource = infoSource.value;
    if (inHospitalDates.value.length) totalParams.inHospitalDates = inHospitalDates.value;
    if (counselors.value.length) totalParams.counselors = counselors.value;
    if (deptId.value) {
      totalParams.deptId = deptId.value;
      totalParams.projectIds = (await deptStore.fetchProjectIdsByDeptId(deptId.value)) || [];
    }

    if (createrDates.value.length) {
      totalParams.startCreateTime = createrDates.value[0];
      totalParams.endCreateTime = createrDates.value[1];
    }

    if (appointmentDates.value.length) {
      totalParams.startAppointmentTime = appointmentDates.value[0];
      totalParams.endAppointmentTime = appointmentDates.value[1];
    }

    if (projectIds.value && projectIds.value.length > 0) totalParams.projectIds = projectIds.value;
    if (isManager.value) {
      const userIds = peopleList.value.map((i) => i.value);
      totalParams.introducers = !introducers.value.length ? userIds : introducers.value;
    } else {
      totalParams.introducers = [localStorage.getItem("userId")];
    }

    // 获取总数据量
    const totalResult = await searchCorpCustomer(totalParams);
    const totalCount = totalResult.data.total;

    if (totalCount === 0) {
      loadingInstance.close();
      ElMessage.warning("暂无数据可导出");
      return;
    }

    // 计算需要分多少批
    const pageSize = 1000; // 每批1000条
    const totalPages = Math.ceil(totalCount / pageSize);

    loadingInstance.setText(`正在下载数据，共${totalCount}条记录，请稍候...`);

    // 分批获取所有数据
    const allData = [];
    for (let currentPage = 1; currentPage <= totalPages; currentPage++) {
      loadingInstance.setText(`正在下载数据 ${currentPage}/${totalPages} 批，共${totalCount}条记录...`);

      const batchParams = {
        ...totalParams,
        page: currentPage,
        pageSize: pageSize,
      };

      const batchResult = await searchCorpCustomer(batchParams);
      const batchData = Array.isArray(batchResult.data.list)
        ? batchResult.data.list.map((i) => {
            return {
              ...i,
              counselor: i.counselorRecord && Array.isArray(i.counselorRecord) ? i.counselorRecord[i.counselorRecord.length - 1]?.counselor : "",
              introducer: i.introducerRecord && Array.isArray(i.introducerRecord) ? i.introducerRecord[i.introducerRecord.length - 1]?.introducer : "",
            };
          })
        : [];

      // 处理数据，添加项目名称等信息
      const processedBatchData = batchData.map((i) => {
        const personResponsibles = Array.isArray(i.personResponsibles) ? i.personResponsibles : [];
        const teamResponsibles = personResponsibles.reduce((list, i) => {
          if (i.teamId && i.teamId === currentTeamId.value && !list.some((item) => item.corpUserId === i.corpUserId)) {
            list.push({ ...i });
          }
          return list;
        }, []);
        const projectNames = Array.isArray(i.projectNames) ? i.projectNames.join(" / ") : "";
        return { ...i, teamResponsibles, projectNames };
      });

      allData.push(...processedBatchData);

      // 添加小延迟，避免请求过于频繁
      if (currentPage < totalPages) {
        await new Promise((resolve) => setTimeout(resolve, 100));
      }
    }

    loadingInstance.setText("正在生成Excel文件...");

    // 准备导出数据
    const exportData = [];

    // 添加标题
    exportData.push(["网电报备数据表"]);

    // 添加筛选条件信息
    if (createrDates.value && createrDates.value.length === 2) {
      exportData.push(["报备日期范围", `${createrDates.value[0]} 至 ${createrDates.value[1]}`]);
    }

    if (arrivalStatus.value) {
      const statusText = arrivalStatus.value === "inHospital" ? "已到院" : "未到院";
      exportData.push(["到院状态", statusText]);
    }

    if (name.value) {
      exportData.push(["客户姓名", name.value]);
    }

    if (mobile.value) {
      exportData.push(["客户手机号", mobile.value]);
    }

    if (infoSource.value.length > 0) {
      exportData.push(["信息来源", infoSource.value.join(", ")]);
    }

    exportData.push([`总计导出记录数: ${allData.length} 条`]);
    exportData.push([]); // 空行

    // 添加表头
    exportData.push(["到院状态", "报备时间", "姓名", "联系方式", "意向项目", "所属开发", "信息来源", "咨询科室", "开发渠道", "跟进计划", "所属咨询", "消费情况", "最近消费时间", "首次到院时间", "预约时间", "报备说明"]);

    // 添加数据行
    allData.forEach((row) => {
      const arrivalStatusText = isInHospital(row.inHospitalTimes) ? "已到院" : "未到院";
      const reportTime = row.reportTimeStamp || row.createTime ? dayjs(row.reportTimeStamp || row.createTime).format("YYYY-MM-DD HH:mm") : "";
      const contactInfo = row.mobile;
      const latestConsumeTime = row.consumeRecord && row.consumeRecord.length ? dayjs(row.consumeRecord[row.consumeRecord.length - 1].createTime).format("YYYY-MM-DD HH:mm") : "";
      const firstArriveTime = row.inHospitalTimes && Array.isArray(row.inHospitalTimes) && row.inHospitalTimes[0] ? dayjs(row.inHospitalTimes[0]).format("YYYY-MM-DD HH:mm") : "";
      const appointmentTime = row.lastAppointmentTime ? dayjs(row.lastAppointmentTime).format("YYYY-MM-DD HH:mm") : "";
      const hasPlanText = row.hasPlan ? "有计划" : "无计划";
      const consumeStatus = getConsumeStatus(row.consumeRecord);
      const infoSourceText = getInfoSource(row);
      const customerSourceText = getSource(row);

      exportData.push([
        arrivalStatusText,
        reportTime,
        row.name || "",
        contactInfo || "",
        row.projectNames || "",
        "", // 所属开发，需要通过ww-user组件获取名字，这里暂时留空
        infoSourceText,
        "", // 咨询科室，需要通过dept-name-transformer组件获取，这里暂时留空
        customerSourceText,
        hasPlanText,
        "", // 所属咨询，需要通过ww-user组件获取名字，这里暂时留空
        consumeStatus,
        latestConsumeTime,
        firstArriveTime,
        appointmentTime,
        row.reportDesc || "",
      ]);
    });

    // 创建工作簿和工作表
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(exportData);

    // 设置列宽
    const colWidths = [
      { wch: 10 }, // 到院状态
      { wch: 18 }, // 报备时间
      { wch: 12 }, // 姓名
      { wch: 15 }, // 联系方式
      { wch: 30 }, // 意向项目
      { wch: 12 }, // 所属开发
      { wch: 20 }, // 信息来源
      { wch: 15 }, // 咨询科室
      { wch: 25 }, // 开发渠道
      { wch: 10 }, // 跟进计划
      { wch: 12 }, // 所属咨询
      { wch: 12 }, // 消费情况
      { wch: 18 }, // 最近消费时间
      { wch: 18 }, // 首次到院时间
      { wch: 18 }, // 预约时间
      { wch: 30 }, // 报备说明
    ];

    ws["!cols"] = colWidths;

    // 合并标题单元格
    ws["!merges"] = [
      { s: { r: 0, c: 0 }, e: { r: 0, c: 15 } }, // 标题行
    ];

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, "网电报备数据");

    // 生成Excel文件并下载
    const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
    const blob = new Blob([excelBuffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });

    // 使用当前日期作为文件名的一部分
    const now = new Date();
    const fileName = `网电报备数据_${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}.xlsx`;

    saveAs(blob, fileName);

    loadingInstance.close();
    ElMessage.success(`导出Excel成功，共导出 ${allData.length} 条记录`);
  } catch (error) {
    loadingInstance.close();
    console.error("导出Excel失败:", error);
    ElMessage.error("导出Excel失败，请稍后重试");
  }
}
</script>
<style lang="scss" scoped></style>
