<template>
  <div v-if="isRead">
    {{ value }}
    <span v-if="item.appendText && value">{{ item.appendText }}</span>
  </div>
  <el-input v-else :model-value="value" placeholder="请输入" :maxlength="item.wordLimit" @update:model-value="change($event)" class="w-200px" :type="item.inputType">
    <template v-if="item.appendText" #append>{{ item.appendText }}</template>
  </el-input>
</template>
<script setup>
import { watch, ref } from "vue";

const $emit = defineEmits(["change"]);
const props = defineProps({
  item: {
    type: Object,
    default: {},
  },
  value: {
    type: String || Number,
    default: "",
  },
  isRead: {
    type: Boolean,
    default: false,
  },
});

function change(value) {
  $emit("change", {
    title: props.item.title,
    value,
  });
}
</script>

<style></style>