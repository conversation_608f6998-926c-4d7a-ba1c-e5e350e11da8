<template>
    <popover-filter :clearable="modelValue && modelValue.length" :label="label" :text="text" :width="width" :scroll="false" @clear="clear">
      <el-input v-model.trim="filterText" :prefix-icon="Search" placeholder="输入搜索内容"></el-input>
      <el-scrollbar class="mt-10px" wrap-style="max-height: 40vh">
        <div v-if="loading" class="py-20px flex justify-center items-center">
          <el-icon class="is-loading mr-5px"><Loading /></el-icon>加载中...
        </div>
        <el-tree 
          v-else 
          ref="sourceTreeRef" 
          :data="sourceTree" 
          show-checkbox 
          node-key="id" 
          :props="defaultProps" 
          :expand-on-click-node="false" 
          :filter-node-method="filterNode" 
          @check-change="changeSource">
          <template #default="{ node, data }">
            <div class="w-0 flex-grow flex items-center" :class="{'cursor-not-allowed': data.isCategory && !hasSourceData(data)}" @click="expand(data)">
              <div class="w-0 flex-grow truncate" :class="{'text-gray-400': data.isCategory && !hasSourceData(data)}">
                {{ node.label }}
              </div>
              <div class></div>
              <a v-if="data.children && data.children.length" class="flex-shrink-0 ml-5px">
                <el-icon v-if="treeExpandMap[data.id]">
                  <CaretTop />
                </el-icon>
                <el-icon v-else>
                  <CaretBottom />
                </el-icon>
              </a>
            </div>
          </template>
        </el-tree>
      </el-scrollbar>
    </popover-filter>
  </template>
  <script setup>
  import { computed, ref, watch, onMounted } from "vue";
  import { useVModels } from "@vueuse/core";
  import { CaretBottom, CaretTop, Search, Loading } from "@element-plus/icons-vue";
  import PopoverFilter from "../popover-filter-item.vue";
  import { getSourceCateList, getSourceList } from "@/api/benefitManagement"; // 引入API
  
  const emits = defineEmits(["update:modelValue"]);
  const props = defineProps({
    label: { type: String, default: "信息来源" },
    width: { type: Number, default: 300 },
    modelValue: { type: Array, default: () => [] },
  });
  
  const { modelValue } = useVModels(props, emits);
  const filterText = ref("");
  const sourceTreeRef = ref();
  const treeExpandMap = ref({});
  const loading = ref(false);
  const sourceCateList = ref([]); // 分类列表
  const sourceDataList = ref([]); // 来源列表
  const text = computed(() => (modelValue.value && modelValue.value.length ? `已选${modelValue.value.length}项` : "全部"));
  
  const defaultProps = {
    class: 'customer-source-filter-tree',
    disabled: (data) => {
      // 如果是分类，且没有子节点或只有空的分类子节点，则禁用
      if (data.isCategory) {
        return !hasSourceData(data);
      }
      return false;
    }
  };
  
  // 获取分类和来源数据
  async function fetchSourceData() {
    loading.value = true;
    try {
      // 获取分类数据
      const cateRes = await getSourceCateList({
        corpId: localStorage.getItem("corpId"),
        userId: localStorage.getItem("userId"),
      });
      if (cateRes.success) {
        sourceCateList.value = cateRes.data.list || [];
      }
  
      // 获取来源数据（不分页获取全部）
      const sourceRes = await getSourceList({
        page: 1,
        pageSize: 1000,
        corpId: localStorage.getItem("corpId"),
        userId: localStorage.getItem("userId"),
      });
      if (sourceRes.success) {
        sourceDataList.value = sourceRes.data.list || [];
      }
    } catch (error) {
      console.error("获取客户来源数据失败", error);
    } finally {
      loading.value = false;
    }
  }
  
  // 构建树形结构
  const sourceTree = computed(() => {
    return convertData(sourceCateList.value, sourceDataList.value);
  });
  
  function clear() {
    sourceTreeRef.value.setCheckedKeys([]);
    modelValue.value = [];
  }
  
  function changeSource() {
    if (!sourceTreeRef.value) return;
    const nodes = sourceTreeRef.value.getCheckedNodes();
    // 只获取非分类节点的标签（即来源节点）
    modelValue.value = nodes.filter(i => !i.isCategory).map(i => i.label);
  }
  
  function expand(data) {
    const nodesMap = sourceTreeRef.value.store.nodesMap;
    if (!nodesMap[data.id]) return;
    
    if (data.children && data.children.length) {
      nodesMap[data.id].expanded = !nodesMap[data.id].expanded;
      treeExpandMap.value = Object.keys(nodesMap).reduce((acc, key) => {
        const node = nodesMap[key];
        if (node.expanded) {
          acc[key] = node.expanded;
        }
        return acc;
      }, {});
    } else if (!data.isCategory) {
      // 只有非分类节点（来源）才能被选中
      nodesMap[data.id].checked = !nodesMap[data.id].checked;
    }
  }
  
  function filterNode(value, data) {
    if (!value) return true;
    
    // 如果是分类节点，检查其子节点是否有匹配的来源
    if (data.children && data.children.length) {
      return data.label.includes(value) || data.children.some(child => child.label.includes(value));
    }
    
    // 来源节点直接匹配名称
    return data.label.includes(value);
  }
  
  // 检查分类节点是否包含来源数据
  function hasSourceData(node) {
    if (!node.children || node.children.length === 0) {
      return false;
    }
    
    // 检查是否有非分类节点（来源节点）
    const hasSource = node.children.some(child => !child.isCategory);
    if (hasSource) return true;
    
    // 递归检查子分类
    return node.children.some(child => child.isCategory && hasSourceData(child));
  }
  
  // 将分类和来源数据转换为树形结构
  function convertData(cateList, sourceList) {
    // 按level分组分类
    const level1 = cateList.filter(item => item.level === 1);
    const level2 = cateList.filter(item => item.level === 2);
    const level3 = cateList.filter(item => item.level === 3);
    
    // 构建一级分类节点
    const tree = level1.map(cate => ({
      id: cate._id,
      label: cate.label,
      level: cate.level,
      isCategory: true, // 标记为分类节点
      children: []
    }));
    
    // 添加二级分类节点
    level2.forEach(cate => {
      const parentNode = tree.find(node => node.id === cate.parentId);
      if (parentNode) {
        parentNode.children.push({
          id: cate._id,
          label: cate.label,
          level: cate.level,
          isCategory: true, // 标记为分类节点
          children: []
        });
      }
    });
    
    // 添加三级分类节点
    level3.forEach(cate => {
      for (const parentNode of tree) {
        if (!parentNode.children) continue;
        
        const parent2Node = parentNode.children.find(node => node.id === cate.parentId);
        if (parent2Node) {
          parent2Node.children.push({
            id: cate._id,
            label: cate.label,
            level: cate.level,
            isCategory: true, // 标记为分类节点
            children: []
          });
          break;
        }
      }
    });
    
    // 将来源添加到对应分类下
    sourceList.forEach(source => {
      // 只处理启用状态的来源
      if (source.sourceStatus === 'enable' && source.sourceCateIdGroup && source.sourceCateIdGroup.length > 0) {
        const cateId = source.sourceCateIdGroup[0]; // 取第一个分类ID
        
        // 查找对应的分类节点
        let targetNode = null;
        
        // 搜索一级分类
        for (const node1 of tree) {
          if (node1.id === cateId) {
            targetNode = node1;
            break;
          }
          
          // 搜索二级分类
          if (node1.children) {
            for (const node2 of node1.children) {
              if (node2.id === cateId) {
                targetNode = node2;
                break;
              }
              
              // 搜索三级分类
              if (node2.children) {
                for (const node3 of node2.children) {
                  if (node3.id === cateId) {
                    targetNode = node3;
                    break;
                  }
                }
                if (targetNode) break;
              }
            }
            if (targetNode) break;
          }
        }
        
        // 添加来源到找到的分类下
        if (targetNode) {
          targetNode.children.push({
            id: source._id,
            label: source.sourceName,
            isCategory: false // 标记为来源节点，非分类
          });
        }
      }
    });
    
    // 递归过滤掉没有子节点的分类
    function filterEmptyCategories(nodes) {
      if (!nodes) return [];
      return nodes.filter(node => {
        if (node.children && node.children.length) {
          node.children = filterEmptyCategories(node.children);
          return node.children.length > 0;
        }
        return true; // 保留所有叶子节点（来源）
      });
    }
    
    return filterEmptyCategories(tree);
  }
  
  // 组件挂载时获取数据
  onMounted(() => {
    fetchSourceData();
  });
  
  watch(filterText, (val) => sourceTreeRef.value && sourceTreeRef.value.filter(val));
  </script>
  <style lang="scss" scoped>
  :deep(.customer-source-filter-tree > .el-tree-node__content > .el-tree-node__expand-icon) {
    display: none;
  }
  </style>