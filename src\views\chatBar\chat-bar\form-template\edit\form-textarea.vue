<template>
  <div class="px-15px pt-6px pb-15px mt-10px" border-bottom>
    <el-form-item :class="required ? 'is-required' : ''" :label="name">
    </el-form-item>
    <el-input show-word-limit :maxlength="wordLimit" :model-value="value" type="textarea"
      :autosize="{ minRows: 5, maxRows: 10 }" resize="none" @update:model-value="change($event)"></el-input>
  </div>
</template>
<script setup>
import { computed } from 'vue';

const emits = defineEmits(['change'])
const props = defineProps({
  form: { type: Object, default: () => ({}) },
  name: { type: String, default: '' },
  required: { type: Boolean, default: false },
  title: { type: String, default: '' },
  wordLimit: { type: Number, default: 20 },
})
const value = computed(() => props.form[props.title] || '');

function change(value) { emits('change', { title: props.title, value }) }


</script>
