<template>
  <filter-item label="回访计划" :text="text" :width="100">
    <radio-group v-model="hasPlan" v-model:text="text" :list="list" />
  </filter-item>
</template>
<script setup>
import { ref } from 'vue';
import FilterItem from './filter-item.vue';
import radioGroup from './radio-group.vue';

const list = [{ label: '有计划', value: 'YES' }, { label: '无计划', value: 'NO' }];
const hasPlan = ref('');
const text = ref('');

function getParam() {
  return hasPlan.value ? { hasPlan: hasPlan.value } : {}
}

function reset() {
  hasPlan.value = ''
}

defineExpose({
  getParam,
  reset
})
</script>
<style lang="scss" scoped></style>
