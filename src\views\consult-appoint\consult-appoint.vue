<template>
  <my-layout>
    <layout-item>
      <div class="overflow-hidden mb-8px">
        <el-row :gutter="10">
          <el-col v-for="i in count" :key="i.key" :span="6">
            <div class="flex flex-col items-center rounded bg-white px-20px py-16px h-100px text-center">
              <div class="w-full pt-4px truncate text-18px font-semibold">{{ i.count }}</div>
              <div class="w-full mt-8px text-14px truncate text-gray-500">{{ i.label }}</div>
              <div v-if="i.tradeCount !== undefined" class="w-full mt-4px text-12px truncate text-green-500">成交{{ i.tradeCount }}人 ({{ i.dealRate }}%)</div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="py-5px flex bg-white">
        <expand-filter-box v-model:can-expand="canExpand" :enable-expand="true" class="flex-grow" :expand="expand">
          <date-range-filter v-model:dates="triageTimeDates" label="咨询日期" :text="datesText" />
          <check-box-filter v-model="consultStages" label="接诊类型" :list="ConsultStage" />
          <check-box-filter v-model="counselors" label="所属咨询" :list="receptUsers">
            <template #item="{ item }">
              <ww-user :openid="item.value"></ww-user>
            </template>
          </check-box-filter>
          <check-box-filter v-model="introducers" label="所属开发" :list="introducerList">
            <template #item="{ item }">
              <ww-user :openid="item.value"></ww-user>
            </template>
          </check-box-filter>
          <filter-info-source v-model="source" label="信息来源" />
          <filter-customer-source v-model="customerSource" label="开发渠道" />
          <div class="text-gray-500 text-14px flex items-center">
            <div class="filter-label pr-5px">咨询科室:</div>
            <dept-select v-model="deptId" placeholder="请选择科室" @change="handleDeptChange" />
          </div>
          <div class="text-gray-500 text-14px flex items-center ml-10px">
            <div class="filter-label pr-5px">咨询项目:</div>
            <project-intent-select v-model="projectIds" :deptId="deptId" @change="handleProjectChange" placeholder="请选择咨询项目" type="filter" style="width: 200px" multiple />
          </div>
          <check-box-filter v-model="triagePersonUserIds" label="分诊人员" :list="peopleList">
            <template #item="{ item }">
              <ww-user :openid="item.value"></ww-user>
            </template>
          </check-box-filter>
          <check-box-filter v-model="receptionPersonUserIds" label="接诊人员" :list="receptUsers">
            <template #item="{ item }">
              <ww-user :openid="item.value"></ww-user>
            </template>
          </check-box-filter>
          <input-filter v-model="name" label="客户姓名" placeholder="请输入" :width="200" />
          <input-filter v-model="mobile" label="客户手机号" placeholder="请输入" :width="200" />
        </expand-filter-box>
        <div class="flex-shrink-0 flex items-center h-40px pr-15px">
          <el-button v-if="canExpand" text plain @click="expand = !expand">
            <span class="mr-5px">{{ expand ? "收起筛选" : "更多筛选" }}</span>
            <el-icon v-if="expand">
              <ArrowUpBold />
            </el-icon>
            <el-icon v-else>
              <ArrowDownBold />
            </el-icon>
          </el-button>
          <el-button type="primary" @click="search">查询</el-button>
          <el-button plain type="primary" @click="addNewConsult()">新增咨询</el-button>
          <el-button plain type="primary" @click="openAppointmentRecords">预约客户</el-button>
        </div>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <el-table border stripe height="100%" :data="list" v-loading="loading">
        <el-common-column prop="consultStageStr" label="接诊类型" :min-width="100"></el-common-column>
        <el-common-column prop="dealStatus" label="成交状态" :min-width="100">
          <template #default="{ row: { tradeAmount, dealStatus } }">
            <div v-if="dealStatus === '成交'" :style="{ color: dealStatus === '成交' ? 'green' : 'red' }">{{ dealStatus === "成交" ? "已成交" : "未成交" }}</div>
            <div v-else :style="{ color: tradeAmount > 0 ? 'green' : 'red' }">{{ tradeAmount > 0 ? "已成交" : "未成交" }}</div>
          </template>
        </el-common-column>
        <el-common-column prop="name" label="姓名" :min-width="100">
          <template #default="{ row: { name, customerId } }">
            <span @click.stop="toCustomerDetail(customerId)" main-color class="pointer hover:underline">
              {{ name }}
            </span>
          </template>
        </el-common-column>
        <el-common-column prop="mobile" label="联系方式" :min-width="120">
          <template #default="{ row: { mobile } }">
            <span>{{ maskPhone(mobile, 3, 4) }}</span>
          </template>
        </el-common-column>
        <el-common-column prop="counselor" label="所属咨询" :min-width="120">
          <template #default="{ row: { counselorUserId } }">
            <ww-user v-if="counselorUserId" :openid="counselorUserId" />
          </template>
        </el-common-column>
        <el-common-column prop="introducer" label="所属开发" :min-width="120">
          <template #default="{ row: { introducerUserId } }">
            <ww-user v-if="introducerUserId" :openid="introducerUserId" />
          </template>
        </el-common-column>
        <el-common-column prop="triageTimeStr" label="咨询时间" :min-width="150"></el-common-column>
        <el-common-column prop="projectNames" label="咨询项目" :min-width="150">
          <template #default="{ row: { projectNames } }">
            {{ Array.isArray(projectNames) ? projectNames.join(", ") : projectNames || "" }}
          </template>
        </el-common-column>
        <el-common-column prop="department" label="咨询科室" :min-width="120">
          <template #default="{ row: { projectDeptIds } }">
            <dept-name-transformer :dept-id="projectDeptIds" />
          </template>
        </el-common-column>
        <el-common-column prop="source" label="信息来源" :min-width="100">
          <template #default="{ row: { source } }">
            {{ Array.isArray(source) ? source.join("-") : "" }}
          </template>
        </el-common-column>
        <!-- <el-common-column prop="channel" label="开发渠道" :min-width="120">
          <template #default="{ row: { channel } }">
            {{ channel || "" }}
          </template>
        </el-common-column> -->
        <el-common-column prop="visitTypeStr" label="就诊类型" :min-width="100"></el-common-column>
        <el-common-column prop="triagePersonUserId" label="分诊人员" :min-width="100">
          <template #default="{ row: { triagePersonUserId } }">
            <ww-user :openid="triagePersonUserId" />
          </template>
        </el-common-column>
        <el-common-column prop="receptionPersonUserId" label="接诊人员" :min-width="100">
          <template #default="{ row: { receptionPersonUserId } }">
            <ww-user v-if="receptionPersonUserId" :openid="receptionPersonUserId" />
          </template>
        </el-common-column>
        <el-common-column prop="receptionTimeStr" label="接诊时间" :min-width="150"></el-common-column>
        <el-common-column prop="visitStatusStr" label="就诊状态" :min-width="100">
          <template #default="{ row: { visitStatusStr, visitStatus } }">
            <div :style="'color:' + VisitStatusColor[visitStatus]">
              {{ visitStatusStr }}
            </div>
          </template>
        </el-common-column>
        <el-common-column prop="customerSourceStr" label="开发渠道" :min-width="240"></el-common-column>
        <el-table-column fixed="right" class-name="el-table-fixed-column--shadow" label="操作" width="120">
          <template #default="{ row }">
            <el-text class="cursor-pointer mr-5px" type="primary" v-if="row.visitStatus === 'pending' && dayjs(row.triageTime).isSame(dayjs(), 'day')" @click="edit(row)">修改</el-text>
            <el-text class="cursor-pointer mr-5px" type="primary" v-if="row.visitStatus === 'visited'" @click="showRecordShow(row)">咨询记录</el-text>
          </template>
        </el-table-column>
      </el-table>
    </layout-main>
    <layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize" @handle-current-change="changePage" />
    </layout-item>
  </my-layout>
  <reg-list-drawer v-model="visible" />
  <search-modal v-model="searchVisible" @close="searchClose()" @consult="openReception" @addCustomer="onAddCustomer" />
  <reception-drawer v-model="receptionVisible" :customer="customer" @success="success" :consult="consult" @showCustomerDetail="toCustomerDetail" @changeName="changeName" />
  <add-customer :customer="newCustomerInfo" :visible="addCustomerVisible" @close="addCustomerVisible = false" @update="addSuccess" title="新增患者" viewType="consult" />
  <customer-detail :visible="customerDetailVisible" @close="customerDetailVisible = false" :customerId="customerId" title="客户详情" @update="getList" customerType="corpCustomer" />
  <customer-record-model v-model="recordVisible" :recordId="recordId" @close="recordVisible = false" :consultRecord="consult" />
  <appointment-record-modal :visible="appointmentRecordVisible" @close="closeAppointmentRecords" @triage="handleAppointmentTriage" />
</template>
<script setup>
import { onMounted, ref, computed } from "vue";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import { getCustomers } from "@/api/member";
import { getConsultRecord, getConsultStageCount } from "@/api/consult";
import { VisitStatusObj, VisitType, ConsultStage, VisitStatusColor } from "@/baseData";
import useElPagination from "@/hooks/useElPagination";
import useModal from "@/hooks/useModal";
import { staffStore } from "@/store/staff";
import { maskPhone } from "@/utils";
import { useDeptStore } from "@/components/dept-components/dept-store";
import addCustomer from "@/views/member/customer/add-customer.vue";
import { CheckBoxFilter, DateRangeFilter, InputFilter, filterCustomerSource, expandFilterBox, filterInfoSource } from "@/components/filter-bar";
import customerDetail from "@/views/member/detail/index.vue";
import customerRecordModel from "@/views/consult-appoint/customer-record-model.vue";
import elCommonColumn from "@/components/el-common-column/el-common-column.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import regListDrawer from "./reg-list-drawer.vue";
import receptionDrawer from "./reception-drawer.vue";
import searchModal from "./search-modal.vue";
import WwUser from "@/components/ww-user/index.vue";
import { memberStore } from "@/store/member";
import projectIntentSelect from "@/components/benefit-management/project-intent-select.vue";
import DeptNameTransformer from "@/components/dept-components/dept-name-transformer.vue";
import DeptSelect from "@/components/dept-components/dept-select.vue";
import appointmentRecordModal from "../e-store-report/appointment-record-modal.vue";

const loading = ref(false);
const customerDetailVisible = ref(false);
const customerId = ref("");
const recordId = ref("");
const newCustomerInfo = ref({});
const canExpand = ref(false);
const expand = ref(false);
function toCustomerDetail(id) {
  customerId.value = id;
  customerDetailVisible.value = true;
}

const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());
const peopleList = computed(() => {
  return staffList.value.map((i) => ({ value: i.userid }));
});
const introducerList = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("onlineCustomerService")).map((i) => ({ value: i.userid }));
});

const receptUsers = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("consultant")).map((i) => ({ value: i.userid }));
});
const { corpInfo } = storeToRefs(memberStore());
const customerSources = computed(() => (corpInfo.value && Array.isArray(corpInfo.value.customerSourceList) ? corpInfo.value.customerSourceList : []));

const triageTimeDates = ref([dayjs().format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")]);
const triagePersonUserIds = ref([]);
const receptionPersonUserIds = ref([]);
const consultStages = ref([]);
const source = ref([]);
const counselors = ref([]);
const introducers = ref([]);
const projectIds = ref([]);
const deptId = ref(""); // 新增科室ID筛选变量
const customerSource = ref([]); // 添加开发渠道筛选变量
const appointmentRecordVisible = ref(false);

const datesText = computed(() => triageTimeDates.value.join(" ~ "));
const mobile = ref("");
const name = ref("");
const customer = ref({});
const consult = ref({});
const list = ref([]);
const stages = ref([]);
const total = ref(0);
const { page, pageSize, changePage, changeSize } = useElPagination(getList);
const { close, show, visible } = useModal();
const { show: receptionShow, visible: receptionVisible } = useModal();
const { show: addCustomerShow, visible: addCustomerVisible, close: addCustomerClose } = useModal();
const { close: searchClose, show: searchShow, visible: searchVisible, width: searchWidth } = useModal(1200);

const { show: recordShow, visible: recordVisible, close: recordClose } = useModal();

const deptStore = useDeptStore(); // 初始化deptStore

function addNewConsult() {
  consult.value = {};
  customer.value = {};
  searchShow();
}

function openReception(item) {
  customer.value = item;
  consult.value = {};
  if (Array.isArray(item.projects)) {
    consult.value.projects = item.projects.map((i) => ({ _id: i._id, projectCateId: i.projectCateId }));
  }
  searchClose();
  receptionShow();
}
function onAddCustomer(data = {}) {
  newCustomerInfo.value = { relationship: "本人", ...data };
  searchClose();
  addCustomerShow();
}
async function addSuccess(id) {
  // 根据id获取数据
  let { success, data } = await getCustomers({ _id: id });
  if (success && data && data.data.length > 0) {
    customer.value = data.data[0];
    if (customer.value && Array.isArray(customer.value.customerSource)) {
      consult.value.source = [...customer.value.customerSource];
    }
    receptionShow();
  }
}
async function getList() {
  let query = {
    page: page.value,
    pageSize: pageSize.value,
    mobile: mobile.value,
    name: name.value,
    stages: stages.value,
    triageTimeDates: triageTimeDates.value,
    triagePersonUserIds: triagePersonUserIds.value,
    receptionPersonUserIds: receptionPersonUserIds.value,
    consultStages: consultStages.value,
    source: source.value,
  };
  if (counselors.value.length) {
    query.counselors = counselors.value;
  }
  if (introducers.value.length) {
    query.introducers = introducers.value;
  }
  if (deptId.value) {
    query.deptId = deptId.value;
    query.projectIds = (await deptStore.fetchProjectIdsByDeptId(deptId.value)) || [];
  }
  if (projectIds.value && projectIds.value.length > 0) {
    query.projectIds = projectIds.value;
  }
  if (customerSource.value.length) {
    query.customerSource = customerSource.value; // 添加开发渠道筛选条件
  }
  loading.value = true;
  const { success, data } = await getConsultRecord(query);
  loading.value = false;
  if (success) {
    total.value = data.total;
    list.value = data.list.map((i) => {
      return {
        ...i,
        name: i.customerInfo.name,
        mobile: i.customerInfo.mobile,
        customerSource: i.customerInfo.customerSource,
        triageTimeStr: i.triageTime ? dayjs(i.triageTime).format("YYYY-MM-DD HH:mm") : "",
        customerSourceStr: i.customerInfo.customerSource && Array.isArray(i.customerInfo.customerSource) ? i.customerInfo.customerSource.join(" / ") : "",
        visitStatusStr: VisitStatusObj[i.visitStatus],
        visitTypeStr: VisitType.find((j) => j.value === i.visitType)?.label || "",
        consultStageStr: ConsultStage.find((j) => j.value === i.consultStage)?.label || "",
        receptionTimeStr: i.receptionTime ? dayjs(i.receptionTime).format("YYYY-MM-DD HH:mm") : "",
      };
    });
  }
}
async function search() {
  await getList();
  await getStatics(); // 同时更新统计数据
}
const count = ref([
  { key: "totals", label: "咨询人次", count: 0 },
  { key: "firstVisit", label: "初诊人次", count: 0, tradeCount: 0, dealRate: 0 },
  { key: "returnVisit", label: "复诊人次", count: 0, tradeCount: 0, dealRate: 0 },
  { key: "moreConsumed", label: "再消费人次", count: 0, tradeCount: 0, dealRate: 0 },
]);

function openAppointmentRecords() {
  customer.value = {};
  consult.value = {};
  appointmentRecordVisible.value = true;
}

function closeAppointmentRecords() {
  appointmentRecordVisible.value = false;
}

async function getStatics() {
  const query = {
    corpId: localStorage.getItem("corpId"),
    createDates: triageTimeDates.value, // 使用筛选条件中的日期范围
    // 添加其他筛选条件
    mobile: mobile.value,
    name: name.value,
    stages: stages.value,
    triagePersonUserIds: triagePersonUserIds.value,
    receptionPersonUserIds: receptionPersonUserIds.value,
    consultStages: consultStages.value,
    source: source.value,
  };

  // 添加咨询师筛选
  if (counselors.value.length) {
    query.counselors = counselors.value;
  }

  // 添加开发人员筛选
  if (introducers.value.length) {
    query.introducers = introducers.value;
  }

  // 添加科室和项目筛选
  if (deptId.value) {
    query.deptId = deptId.value;
    query.projectIds = (await deptStore.fetchProjectIdsByDeptId(deptId.value)) || [];
  }
  if (projectIds.value && projectIds.value.length > 0) {
    query.projectIds = projectIds.value;
  }

  // 添加开发渠道筛选
  if (customerSource.value.length) {
    query.customerSource = customerSource.value;
  }

  const { data, success } = await getConsultStageCount(query);
  if (success) {
    const visitObj = data.data;
    count.value = count.value.map((item) => {
      const i = visitObj[item.key];
      if (i) {
        item.count = i.count || 0;
        // 如果有成交数据，计算成交人次和成交率
        if (i.tradeCount !== undefined) {
          item.tradeCount = i.tradeCount || 0;
          item.dealRate = item.count > 0 ? Math.round((item.tradeCount / item.count) * 100) : 0;
        }
      } else {
        item.count = 0;
        if (item.tradeCount !== undefined) {
          item.tradeCount = 0;
          item.dealRate = 0;
        }
      }
      return item;
    });
  }
}

function showRecordShow(row) {
  consult.value = row;
  recordShow();
}
async function edit(row) {
  consult.value = row;
  customer.value = row.customerInfo;
  receptionShow();
}
async function success() {
  getList();
  getStatics();
}

function changeName(name) {
  customer.value.name = name;
  list.value.forEach((i) => {
    if (i.customerId === customer.value._id) {
      i.name = name;
    }
  });
}

function handleAppointmentTriage(data) {
  customer.value = data.customer;
  consult.value = data.consult;
  appointmentRecordVisible.value = false;
  receptionShow();
}

onMounted(async () => {
  await getStaffList(true);
  await deptStore.fetchDeptList(); // 确保科室列表已加载
  getList();
  getStatics();
});
</script>
<style lang="scss" scoped></style>
