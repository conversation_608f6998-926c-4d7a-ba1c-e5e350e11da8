<template>
  <div class="chat">
    <div class="navbar">微信名</div>
    <div id="chat">
      <div class="message flex" v-if="content">
        <img src="@/assets/images/a-ziyuan.png" alt="" class="avatar" />
        <div class="content">
          <div class="template-card">
            {{ content }}
          </div>
        </div>
      </div>
      <div class="message flex" v-for="(item, index) in fileList" :key="index">
        <img src="@/assets/images/a-ziyuan.png" alt="" class="avatar" />
        <div class="content">
          <img v-if="item.file && item.file.type && item.file.type.includes('image')" :src="item.URL" />
          <video v-else-if="item.file && item.file.type && item.file.type.includes('video')" :src="item.URL" controls></video>
          <div v-else-if="item.file && item.type === 'link'">
            <div class="template-card flex" justify-between items-center>
              <div>
                <div class="title">{{ item.file.name }}</div>
                <div class="description">{{ item.file.subtitle }}</div>
              </div>
              <img :src="item.URL" alt="" class="w-40px h-40px" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
defineProps({
  fileList: {
    type: Array,
    default: () => [],
  },
  content: {
    type: String,
    default: "",
  },
});
</script>

<style lang="scss">
.chat {
  width: 320px;
  height: 640px;
  box-sizing: border-box;
  background: #eee;
  overflow: auto;
  margin: 10px auto 0 auto;
  border-radius: 5px;
}

.navbar {
  height: 50px;
  background: #000;
  color: white;
  line-height: 50px;
  text-align: center;
  font-size: 18px;
  position: relative;
}

.message {
  margin-bottom: 10px;
  clear: both;
  padding: 10px 0 0 10px;
}

.message .avatar {
  float: left;
  width: 30px;
  height: 30px;
  margin-right: 3px;
  background: white;
  border-radius: 5px;
}

.message .content {
  max-width: 200px;
  padding: 10px;
  border-radius: 10px;
}

.message img,
.message video {
  max-width: 100%;
}

.message .template-card {
  width: 200px;
  //   border: 1px solid #ccc;
  border-radius: 4px;
  padding: 5px 10px;
  box-sizing: border-box;
  background: white;
  font-size: 14px;
}

.message .template-card .title {
  //   font-weight: bold;
  font-size: 14px;
  margin-bottom: 10px;
  padding-top: 5px;
}

.message .template-card .description {
  margin-bottom: 10px;
  font-size: 12px;
  color: #999;
}

.message .template-card .footer {
  text-align: right;
  font-size: 12px;
  color: #999;
}
</style>