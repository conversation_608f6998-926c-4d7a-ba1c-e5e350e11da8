<template>
  <my-layout>
    <layout-main>
      <form-template ref="formRef" :items="formItems" :form="planForm" :rule="rule" @change="change" />
      <plan-node-list class="my-40rpx" @addTask="addTask" @edit="editTask" viewType="newPlan" />
      <div class="flex items-center justify-center pb-5px text-blue-500 mt-20px" @click="addTask({})">
        <el-icon>
          <Plus />
        </el-icon>
        <text class="text-14px ml-5px">新增任务项</text>
      </div>
    </layout-main>
    <layout-item>
      <div class="p-15px flex bg-white relative z-2" common-shadow--r>
        <el-button class="flex-grow" plain @click="cancel()">取消</el-button>
        <el-button class="flex-grow ml-15px" type="primary" @click="executePlan()">执行计划</el-button>
      </div>
    </layout-item>
  </my-layout>
</template>
<script setup>
import { computed, ref, toRefs, watch } from "vue";
import { ElMessage } from "element-plus";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import { memberStore } from "@/store/member";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import formTemplate from "../../form-template/edit/index.vue";
import planNodeList from "./components/plan-node-list.vue";
import { memberMangePlanStore } from "@/store/managePlan/memberMangePlan";
import { taskStore } from "@/store/managePlan/task";
import { getRandomStr } from "@/utils";
const { planForm, taskList, team } = storeToRefs(memberMangePlanStore());
const { initPlan, executPlan, stopPlan, getMemberManagementPlanFnc, onEditTaskSuccess, updateMemberMangePlanStatusFnc } = memberMangePlanStore();
const { memberInfo } = storeToRefs(memberStore());
const { taskForm } = storeToRefs(taskStore());
const emits = defineEmits(["cancel", "complete", "editPlanNote"]);
const formItems = computed(() => [
  { type: "input", name: "计划名称", operateType: "formCell", title: "planName", required: true },
  { type: "date", name: "开始时间", operateType: "formCell", title: "planExecutionTime", required: true },
  { type: "selectWwuser", name: "计划跟踪人", operateType: "custom", title: "executorUserId", required: true, memberList: Array.isArray(team.value.memberList) ? team.value.memberList : [] },
]);
const rule = {
  planExecutionTime: (e) => {
    if (!e) return "请选择开始时间";
    if (!dayjs(e).isValid()) return "开始时间无效，请重新选择开始时间";
    if (dayjs(e).isBefore(dayjs().startOf("day"))) return "开始时间不得小于当天，请重新选择开始时间";
    return true;
  },
};
const form = ref({});
function change({ title, value }) {
  planForm.value[title] = value;
}
function editTask() {
  emits("editPlanNote", "edit");
}

function addTask() {
  taskForm.value = {};
  emits("editPlanNote", "add");
}
function cancel() {
  emits("cancel");
}
const formRef = ref(null);
async function executePlan() {
  if (formRef.value.verify()) {
    if (!planForm.value.executorUserId) {
      ElMessage.warning("请选择计划跟踪人");
      return;
    }
    if (taskList.value.length === 0) {
      ElMessage.warning("请添加任务项");
      return;
    }
    planForm.value["planId"] = getRandomStr();
    await executPlan();
    emits("complete");
  }
}
</script>
<style lang="scss" scoped></style>
