<template>
    <div v-if="isRead">
        {{ form.referenceType }}
        <span v-if="form.referenceType === '同事'">
            <ww-user v-if="form.referenceUserId" :openid="form.referenceUserId"></ww-user>
        </span>
        <span v-if="form.referenceType === '客户'">
            {{ form.reference }}
        </span>
    </div>
    <div v-else flex items-center>
        <!-- <el-select class="flex-shrink-0 mr-15px" placeholder="请选择" :model-value="form.referenceType"
            @update:model-value="changeType($event)">
            <el-option v-for="option in referenceTypeList" :label="option.label" :value="option.value" />
        </el-select> -->
        <el-input v-if="form.referenceType === '同事'" :placeholder="form.referenceUserId ? '' : '请选择同事'" readonly
            flex-grow class="w-250px">
            <template #prefix>
                <span v-if="form.referenceUserId" class="text-black mr-10px">
                    <ww-user :openid="form.referenceUserId"></ww-user>
                </span>
            </template>
            <template #append>
                <el-text pointer @click="chooseCoWorker()">选择</el-text>
            </template>
        </el-input>
        <remote-select-customer v-else-if="form.referenceType === '客户'" :optionWidth="320"  class="w-200px"
            :value="form.referenceCustomerId" placeholder="请搜索客户姓名" @change="changeOrigin($event, '客户')" />
    </div>
</template>
<script setup>
import RemoteSelectCustomer from '@/components/remote-select-customer/remote-select-customer';
import { selectEnterpriseContact } from '@/utils/jssdk';
import { watch } from 'vue';
import WwUser from "@/components/ww-user/index.vue";
const $emit = defineEmits(["change"]);
const props = defineProps({
    form: {
        type: String,
        default: {}
    },
    isRead: {
        type: Boolean,
        default: false
    }
})
watch(() => props.form.customerSource, (newVal) => {
    const source = Array.isArray(newVal) ? newVal : [];
    const referenceType = props.form.referenceType;
    // 此处另类写法 source.join() === '同事推荐' 等同 source.length === 1 && source[0] === '同事推荐'，下面同理
    if (source.join() === '同事推荐' && referenceType == '同事') return;
    if (source.join() === '客户推荐' && referenceType == '客户') return;
    const value = { referenceType: '', reference: '', referenceCustomerId: '', referenceUserId: '' };
    if (source.join() === '客户推荐' && props.form.referenceType !== '客户') {
        value.referenceType = '客户';
    } else if (source.join() === '同事推荐' && props.form.referenceType !== '同事') {
        value.referenceType = '同事';
    }
    $emit("change", { title: "reference", value })
}, { immediate: true })

async function chooseCoWorker() {
    const res = await selectEnterpriseContact('single');
    const { selectedUserList = [] } = res;
    if (selectedUserList && selectedUserList[0]) changeOrigin(selectedUserList[0].id, '同事')
}
function changeType(type) {
    let newProps = {};
    newProps.referenceType = type;
    if (type === '同事') {
        newProps.reference = '';
        newProps.referenceCustomerId = '';
    } else if (type === '客户') {
        newProps.referenceUserId = '';
    }
    $emit("change", {
        title: "reference",
        value: newProps
    })
}
function changeOrigin(data, type) {
    let newProps = {};
    if (type === '同事') {
        const userid = data;
        newProps.referenceUserId = userid;
        newProps.reference = ''
        newProps.referenceCustomerId = '';
    } else if (type === '客户') {
        const customer = data;
        newProps.referenceUserId = '';
        newProps.reference = customer.name
        newProps.referenceCustomerId = customer._id;
    }
    $emit("change", {
        title: "reference",
        value: newProps
    })
}

</script>

<style lang="scss"></style>