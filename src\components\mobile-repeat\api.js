import { ref } from "vue";
import { getCustomers } from "@/api/member";
export default function mobileRepeatComponentApi() {
  const mobileReatCustomers = ref([]);
  const mobileRepeatVisible = ref(false);
  async function getMobileReatCustomer(mobile, _id) {
    const { data, success } = await getCustomers({ mobile });
    const customers = data && Array.isArray(data.data) ? data.data.filter(i => i._id !== _id ) : [];
    if (success && customers.length > 0) {
      mobileRepeatVisible.value = true;
      mobileReatCustomers.value = customers;
    } else {
      mobileReatCustomers.value = [];
    }
  }

  return { mobileReatCustomers, mobileRepeatVisible, getMobileReatCustomer };
}
