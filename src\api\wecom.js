import { post } from "./axios";
import { memberStore } from "@/store/member";
async function usewecom(data) {
  const res = await post("weCom", data, "CORP");
  return res;
}

export async function getCorpMemberInfoByUserId(params) {
  const res = await usewecom({
    type: "getCorpMemberInfoByUserId",
    params,
  });
  return res;
}

export async function getAccessUser(code) {
  const res = await usewecom({
    type: "getAccessUser",
    code,
  });
  return res;
}

export async function getNameByexternalUserId(obj) {
  const res = await usewecom({
    type: "getNameByexternalUserId",
    ...obj,
  });
  return res;
}

export async function getAccessToken(corpid, permanentCode) {
  const res = await usewecom({
    type: "getAccessToken",
    corpid,
    permanentCode,
  });
  return res;
}

export async function transferExternalcontact({ externalUserIds = [], handoverUserId, takeoverUserId, permanentCode }) {
  const res = await usewecom({
    type: "transferExternalcontact",
    corpId: localStorage.getItem("corpId"),
    externalUserIds,
    handoverUserId,
    takeoverUserId,
    permanentCode,
  });
  return res;
}

export async function transferLicense({ handoverUserId, takeoverUserId }) {
  const res = await usewecom({
    type: "transferLicense",
    corpId: localStorage.getItem("corpId"),
    handoverUserId,
    takeoverUserId,
  });
  return res;
}

export async function getSignstrOfjsapi({ permanentCode, url }) {
  const res = await usewecom({
    type: "getSignstrOfjsapi",
    corpId: localStorage.getItem("corpId"),
    permanentCode,
    url: url,
  });
  return res;
}

export async function addContactWay(data) {
  const res = await usewecom({ type: "addContactWay", ...data });
  return res;
}

export async function getCorpAllStaffUseBehaviorDataToday(data) {
  const res = await usewecom({ type: "getCorpAllStaffUseBehaviorDataToday", ...data });
  return res;
}

export async function getPageTitleAndContent(url) {
  const res = await usewecom({ type: "getPageTitleAndContent", url });
  return res;
}

/**
 * 生成小程序链接（使用URL Scheme）
 * @param {Object} params 参数
 * @param {string} params.path 小程序页面路径，例如：pages/tab_bar/hospHome/hospHome?actionType=89&promotionCode=21&hisDrId=123
 * @param {string} params.hisDrId 医生工号
 * @param {boolean} params.is_expire 是否设置过期时间，默认false
 * @param {number} params.expire_time 过期时间戳（秒），is_expire为true时必填
 * @returns {Object} 生成结果
 */
export async function generateMiniProgramLink(params) {
  const res = await usewecom({
    type: "generateMiniProgramLink",
    ...params
  });
  return res;
}

export async function uploadTempMedia({ sendType, name, fileID }) {
  const { corpId, permanent_code } = memberStore().corpInfo;
  const res = await usewecom({
    type: "uploadTempMedia",
    sendType,
    corpid: corpId,
    permanentCode: permanent_code,
    name,
    fileID,
  });
  return res;
}

export async function uploadTempImage({ name, fileID }) {
  const { corpId, permanent_code } = memberStore().corpInfo;
  const res = await usewecom({
    type: "uploadTempImage",
    corpid: corpId,
    permanentCode: permanent_code,
    name,
    fileID,
  });
  return res;
}
