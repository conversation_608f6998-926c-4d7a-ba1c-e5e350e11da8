<template>
  <div class="flex">
    <div class="title w-150px mb-10px">院内来源:</div>
    <div class="flex">
      <div v-if="record._id">
        <div class="flex align-center mb-10px">
          <div class="title w-80px">转入日期:</div>
          <div class="text-14px">{{ record.createTime }}</div>
        </div>
        <div class="flex align-center mb-10px">
          <div class="title w-80px">转入类型:</div>
          <div class="text-14px">{{ record.eventTypeName }}</div>
        </div>
        <div class="flex mb-10px" v-if="record.eventType === 'share'">
          <div class="title w-80px">共享团队:</div>
          <div class="text-14px leading-normal">{{ record.executeTeamName }}</div>
        </div>
        <div class="flex mb-10px" v-if="record.eventType === 'transferToOtherTeam' || record.eventType === 'transferToCustomerPool'">
          <div class="title w-80px">转出团队:</div>
          <div class="text-14px leading-normal">{{ record.executeTeamName }}</div>
        </div>
        <div class="flex align-center mb-10px">
          <div class="title w-80px">操作人:</div>
          <div class="text-14px">
            <ww-user :openid="record.creatorUserId"></ww-user>
          </div>
        </div>
      </div>
      <div v-else class="text-14px leading-normal">--</div>
      <div main-color class="text-14px pl-10px pointer leading-normal" @click="visible = true" v-if="list.length > 0">院内流转记录</div>
    </div>
  </div>
  <el-dialog v-model="visible" :title="title" @close="visible = false" width="500px">
    <div class="transfer-box">
      <el-timeline v-if="list.length > 0" class="mx-15px">
        <el-timeline-item v-for="(item, index) in list" :key="index" color="#006eff" hollow placement="top" :hide-timestamp="true">
          <div class="flex-grow mr-10px font-semibold text-14px truncate pt-2px">{{ item.createTime }}</div>
          <div class="pt-20px">
            <el-card>
              <div class="flex mb-10px">
                <div class="title w-80px">{{ item.eventType === "adminRemoveTeams" ? "解除团队:" : "转入团队:" }}</div>
                <div class="text-14px leading-normal">{{ item.teamName || item.executeTeamName }}</div>
              </div>
              <div class="flex mb-10px">
                <div class="title w-80px">转入方式:</div>
                <div class="text-14px leading-normal">{{ item.eventTypeName }}</div>
              </div>
              <div class="flex mb-10px" v-if="item.eventType === 'share'">
                <div class="title w-80px">共享团队:</div>
                <div class="text-14px leading-normal">{{ item.executeTeamName }}</div>
              </div>
              <div class="flex mb-10px" v-if="item.eventType === 'transferToOtherTeam' || item.eventType === 'transferToCustomerPool'">
                <div class="title w-80px">转出团队:</div>
                <div class="text-14px leading-normal">{{ item.executeTeamName }}</div>
              </div>
              <div class="flex">
                <div class="title w-80px">操作人:</div>
                <div class="text-14px leading-normal">
                  <ww-user :openid="item.creatorUserId"></ww-user>
                </div>
              </div>
            </el-card>
          </div>
        </el-timeline-item>
        <el-timeline-item placement="top" color="#006eff" hollow v-if="list.length > 0" timestamp="结束"></el-timeline-item>
      </el-timeline>
    </div>
  </el-dialog>
</template>

<script setup>
import { customerTransferRecord } from "@/api/todo.js";
import { computed, ref, watch } from "vue";
import dayjs from "dayjs";
import wwUser from "@/components/ww-user/index.vue";
import { ServiceType } from "@/baseData";
import { teamStore } from "@/store/team";
import { storeToRefs } from "pinia";

const { allTeams } = teamStore();
const { currentTeam } = storeToRefs(teamStore());
watch(currentTeam, (newItem, oldItem) => {
  if (newItem && oldItem && newItem.teamId !== oldItem.teamId) {
    getRecord();
  }
});

const visible = ref(false);
const props = defineProps({
  customerId: {
    type: String,
    default: "",
  },
  customerName: {
    type: String,
    default: "",
  },
});
const record = ref({});
const list = ref([]);
const title = computed(() => `院内流转记录 - ${props.customerName}`);
async function getCustomerTransferRecord() {
  const { data, success } = await customerTransferRecord({ customerId: props.customerId });
  if (success && data.list.length > 0) {
    list.value = data.list.map((item) => {
      item.createTime = dayjs(item.createTime).format("YYYY-MM-DD HH:mm:ss");
      item.executeTeamName = allTeams.find((team) => team.teamId === item.executeTeamId)?.name;
      item.eventTypeName = ServiceType[item.eventType];
      if (item.transferToTeamIds && Array.isArray(item.transferToTeamIds)) item.teamName = item.transferToTeamIds.map((teamId) => allTeams.find((team) => team.teamId === teamId)?.name).join("、");
      if (item.removeTeamIds && Array.isArray(item.removeTeamIds)) item.teamName = item.removeTeamIds.map((teamId) => allTeams.find((team) => team.teamId === teamId)?.name).join("、");
      if (item.eventType === "remindFiling") item.eventTypeName = "自主开拓";
      return item;
    });
    getRecord();
  }
}
function getRecord() {
  if (list.value.length > 0) {
    for (let i = 0; i < list.value.length; i++) {
      if (list.value[i].eventType !== "remindFiling" && list.value[i].eventType !== "adminRemoveTeams") {
        const transferToTeamIds = list.value[i].transferToTeamIds;
        if (transferToTeamIds && transferToTeamIds.includes(currentTeam.value.teamId)) {
          record.value = list.value[i];
          return;
        }
      }
    }
  }
}

watch(() => props.customerId, getCustomerTransferRecord());
</script>

<style lang="scss" scoped>
.title {
  color: #666 !important;
  font-size: 14px;
  text-align: right;
  padding-right: 10px;
}

.transfer-box {
  overflow-y: auto;
  max-height: 500px;
}
</style>