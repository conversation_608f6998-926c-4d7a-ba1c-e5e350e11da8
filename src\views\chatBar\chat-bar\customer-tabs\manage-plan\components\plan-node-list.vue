<template>
  <div>
    <node-list v-for="(i, index) in taskShowList" :key="index" :index="index + 1" :last="index === taskShowList.length - 1">
      <node-item @add="edit({})" @remove="remove" @edit="edit($event)" :task="i"
        :planExecutionTime="planForm.planExecutionTime" />
    </node-list>
    <yc-fab :iconSize="20" :icon="imgSrc" :size="50" bottom="100" @click="addTask()" v-if="showAdd && viewType !== 'newPlan' && planForm && planForm.planExecutStaus !== 'closed'"></yc-fab>
  </div>
</template>
<script setup>
import { ref } from "vue";
import nodeList from "./node-list.vue";
import nodeItem from "./node-item.vue";
import { storeToRefs } from "pinia";
import ycFab from "@/components/yc-fab/yc-fab";
import { memberMangePlanStore } from "@/store/managePlan/memberMangePlan";
import { taskStore } from "@/store/managePlan/task";
const { taskShowList, planForm } = storeToRefs(memberMangePlanStore());
const { taskForm } = storeToRefs(taskStore());
const emits = defineEmits(["edit", "remove", "addTask"]);
// const imgSrc = ref("/src/assets/icon-plus.svg");
const imgSrc = new URL('@/assets/icon-plus.svg', import.meta.url).href;

const props = defineProps({
  viewType: { type: String, default: "" },
  showAdd: { type: Boolean, default: true }
});
function edit() {
  emits("edit");
}
function addTask() {
  taskForm.value = {};
  emits("addTask");
}
function remove() {
  emits("remove");
}

</script>
<style lang="scss" scoped>
.floating-button {
  position: absolute;
  bottom: 140px;
  right: 20px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #007bff;
  color: white;
  font-size: 32px;
  border: none;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  line-height: 40px;
  box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.25);
  padding-bottom: 5px;
}
</style>
