
import { post } from "./axios";
async function useCorp(data) {
  const res = await post("corp", data);
  return res;
}
export async function getDeptList(data) {
  const res = await useCorp({ type: "getDeptList", ...data });
  return res;
}
export async function addDept(data) {
  const res = await useCorp({ type: "addDept", ...data });
  return res;
}
export async function updateDept(data) {
  const res = await useCorp({ type: "updateDept", ...data });
  return res;
}
export async function deleteDept(data) {
  const res = await useCorp({ type: "deleteDept", ...data });
  return res;
}
export async function sortDeptList(data) {
  const res = await useCorp({ type: "sortDeptList", ...data });
  return res;
}
export async function addDeptUserId(data) {
  const res = await useCorp({ type: "addDeptUserId", ...data });
  return res;
}
export async function removeDeptStaff(data) {
  const res = await useCorp({ type: "removeDeptStaff", ...data });
  return res;
}

export async function getDeptStaff(data) {
  const res = await useCorp({ type: "getDeptStaff", ...data });
  return res;
}

export async function addDeptStaff(data) {
  const res = await useCorp({ type: "addDeptStaff", ...data });
  return res;
}
export async function updateStaffDept(data) {
  const res = await useCorp({ type: "updateStaffDept", ...data });
  return res;
}
