<template>
  <el-dialog :model-value="visible" :title="data._id ? `编辑分类` : `新增分类`" :width="width" @close="close">
    <el-scrollbar ref="scrollbarRef" wrap-style="max-height: calc(85vh - 200px)">
      <el-form p-15 label-position="top">
        <el-form-item>
          <template #label>
            <span v-if="data._id">分类名称</span>
            <span v-else-if="data.parentId" font-14>在【{{ data.parentName }}】下方新增分类</span>
            <span v-else font-14>添加一级分类</span>
          </template>
          <el-input v-model="label" :maxlength="10" :placeholder="data.label || `请输入分类名称`" />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button @click="close()">取消</el-button>
        <el-button type="primary" @click="save()">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue';
import { ElMessage } from "element-plus";
import { addSourceCate, updateSourceCate } from "@/api/benefitManagement";

const emits = defineEmits(['close', 'change'])
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  },
  width: {
    type: Number,
    default: 500
  }
})
const label = ref('');

function close() {
  emits('close')
}

async function save() {
  if (typeof label.value !== "string" || label.value.trim() === "") {
    ElMessage.info("分类名称不能为空");
    return;
  }
  const fn = ['add', 'addChild'].includes(props.data._type) ? addSourceCate : updateSourceCate;
  const params = { label: label.value.trim() };
  if (props.data._type === 'addChild') {
    params.parentId = props.data.parentId;
  } else if (props.data._type === 'edit') {
    params.id = props.data._id;
  }
  
  const { success, message } = await fn(params);
  if (success) {
    ElMessage.success(message);
    close();
    emits('change')
  } else {
    ElMessage.error(message);
  }
}

watch(() => props.visible, n => {
  if (!n) return;
  label.value = props.data.label || '';
})
</script>
