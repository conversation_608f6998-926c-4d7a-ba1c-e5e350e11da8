<template>
  <div v-if="isRead">
    {{ value && Array.isArray(value) ? value.join("、") : value }}
  </div>
  <!-- <el-select v-else :model-value="value" placeholder="请选择" clearable @update:model-value="change($event)" class="w-200px">
    <el-option v-for="label in item.range" :label="label" :value="label" />
  </el-select> -->
  <div v-else>
    <el-tag v-for="i in item.range" :key="i" size="large" class="pointer mr-10px mt-5px" :type="i === value ? 'primary' : 'info'" :effect="i === value ? 'light' : 'plain'" @click="change(i)">
      {{ i }}
    </el-tag>
  </div>
</template>
<script setup>
const $emit = defineEmits(["change"]);
const props = defineProps({
  item: {
    type: Object,
    default: {},
  },
  value: {
    type: String,
    default: "",
  },
  isRead: {
    type: Boolean,
    default: false,
  },
});
function change(value) {
  if (value === props.value) {
    value = "";
  }
  $emit("change", {
    title: props.item.title,
    value,
  });
}
</script>

<style></style>