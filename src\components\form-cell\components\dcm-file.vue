<template>
  <div class="dcm-uploader">
    <div class="buttons-container">
      <!-- 上传按钮组 -->
      <input type="file" ref="fileInputSingle" @change="handleFileUpload(false)" accept=".dcm" class="file-input" />
      <input type="file" ref="fileInputMultiple" @change="handleFileUpload(true)" multiple accept=".dcm" class="file-input" />

      <div class="upload-buttons" v-if="!isRead">
        <el-button type="primary" @click="triggerFileInput(true)">上传</el-button>
      </div>

      <el-button v-if="dcmFiles.length > 0" type="primary" @click="openPreview()">查看影像</el-button>
      <!-- 删除按钮 -->
      <el-button v-if="dcmFiles.length > 0 && !isRead" type="danger" @click="deleteFiles()">
        <el-icon><Delete /></el-icon>
        全部删除
      </el-button>
    </div>

    <div v-if="errorMessage" class="error-message">{{ errorMessage }}</div>

    <!-- 文件上传指示器 -->
    <div v-if="loading" class="upload-progress">
      <el-progress :percentage="uploadProgress" :text-inside="true" :stroke-width="18"></el-progress>
      <div class="upload-status">正在处理文件 ({{ processedCount }}/{{ totalFiles }})</div>
    </div>

    <div v-if="dcmFiles.length > 0" class="file-info">
      <p>已上传 {{ dcmFiles.length }} 个DICOM文件</p>
      <!-- 文件列表 -->
      <div class="file-list">
        <div v-for="(file, index) in dcmFiles" :key="file.key" class="file-item">
          <div class="file-item-info">
            <span class="file-number">{{ index + 1 }}</span>
            <span class="file-name">{{ file.name }}</span>
            <span class="file-size">{{ formatFileSize(file.size) }}</span>
          </div>
          <div class="file-actions ml-10px" v-if="!isRead">
            <el-icon @click.stop="deleteSingleFile(index)" class="delete-icon"><Delete /></el-icon>
          </div>
        </div>
      </div>
    </div>
    <!-- 模态框预览 -->
    <div v-if="showModal" class="modal-overlay" @click="closePreview">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>DICOM影像预览 (共{{ dcmFiles.length }}张影像)</h3>
          <button class="close-button" @click="closePreview">&times;</button>
        </div>
        <div class="dicom-viewer-container">
          <!-- <DicomViewer v-if="dcmFiles.length > 0" :files="dcmFiles" :width="1000" :height="1000" /> -->
        </div>
      </div>
    </div>
  </div>
</template>
  
<script setup>
import { ref, watch, computed } from "vue";
import { Delete } from "@element-plus/icons-vue";
import { getRandomStr, file2Base64 } from "@/utils";
// 假设有一个DICOM查看器组件
// import DicomViewer from "./dicom-viewer.vue";

const props = defineProps({
  preview: {
    type: Boolean,
    default: true,
  },
  item: {
    type: Object,
    default: () => ({}),
  },
  isRead: {
    type: Boolean,
    default: false,
  },
  value: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["change", "upload-remove"]);

const fileInputSingle = ref(null);
const fileInputMultiple = ref(null);
const loading = ref(false);
const errorMessage = ref("");
const showModal = ref(false);

// 上传进度相关
const totalFiles = ref(0);
const processedCount = ref(0);
const uploadProgress = computed(() => {
  if (totalFiles.value === 0) return 0;
  return Math.floor((processedCount.value / totalFiles.value) * 100);
});

// 存储DICOM文件列表
const dcmFiles = ref([]);

// 初始化文件数据
const initFileData = (files) => {
  if (!files || files.length === 0) {
    dcmFiles.value = [];
    return;
  }
  dcmFiles.value = files;
};

// 监听 value 变化
watch(
  () => props.value,
  (newValue) => {
    initFileData(newValue);
  },
  { immediate: true }
);

const triggerFileInput = (multiple) => {
  if (multiple) {
    fileInputMultiple.value.click();
  } else {
    fileInputSingle.value.click();
  }
};

const handleFileUpload = (multiple) => {
  loading.value = true;
  errorMessage.value = "";
  const fileInput = multiple ? fileInputMultiple.value : fileInputSingle.value;
  const files = fileInput.files;
  if (!files || files.length === 0) {
    errorMessage.value = "没有选择任何文件";
    loading.value = false;
    return;
  }

  processFiles(files);
};

// 打开预览模态框
const openPreview = () => {
  showModal.value = true;
};

// 关闭预览模态框
const closePreview = () => {
  showModal.value = false;
};

// 删除已上传文件
const deleteFiles = () => {
  // 释放URL资源
  dcmFiles.value.forEach((file) => {
    if (file.url) {
      URL.revokeObjectURL(file.url);
    }
  });

  // 重置状态
  dcmFiles.value = [];

  // 通知父组件文件已删除
  emit("upload-remove");

  // 通过 change 事件发送空文件列表
  emit("change", {
    title: props.item.title,
    value: dcmFiles.value,
  });

  // 清空文件输入框，允许重新上传相同的文件
  if (fileInputSingle.value) fileInputSingle.value.value = "";
  if (fileInputMultiple.value) fileInputMultiple.value.value = "";
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// 删除单个文件
const deleteSingleFile = (index) => {
  // 释放被删除文件的URL资源
  if (dcmFiles.value[index].url) {
    URL.revokeObjectURL(dcmFiles.value[index].url);
  }

  // 从数组中移除该文件
  const updatedFiles = [...dcmFiles.value];
  updatedFiles.splice(index, 1);
  dcmFiles.value = updatedFiles;

  // 通知父组件文件变更
  emit("change", {
    title: props.item.title,
    value: dcmFiles.value,
  });

  // 如果删除了所有文件，也通知父组件
  if (dcmFiles.value.length === 0) {
    emit("upload-remove");
  }
};

// 更新处理文件的函数
const processFiles = async (files) => {
  // 验证是否是DCM文件
  const validFiles = Array.from(files).filter((file) => file.name.toLowerCase().endsWith(".dcm") || file.type === "application/dicom");
  if (validFiles.length === 0) {
    errorMessage.value = "请上传有效的DICOM文件(.dcm)";
    loading.value = false;
    return;
  }

  // 重置进度计数
  totalFiles.value = validFiles.length;
  processedCount.value = 0;

  // 处理新文件
  const processedFiles = [...dcmFiles.value]; // 保留之前上传的文件

  for (const file of validFiles) {
    try {
      const fileUrl = URL.createObjectURL(file);
      const fileBase64 = await file2Base64(file);

      processedFiles.push({
        base64: fileBase64,
        name: file.name,
        key: getRandomStr(),
        file: file,
        size: file.size,
        type: file.type || "application/dicom",
        loading: false,
        url: fileUrl,
      });

      processedCount.value++;
    } catch (error) {
      console.error("处理文件失败:", error);
    }
  }

  // 更新文件列表
  dcmFiles.value = processedFiles;

  // 通过 change 事件发送文件列表
  emit("change", {
    title: props.item.title,
    value: dcmFiles.value,
  });

  // 清空文件输入框，允许重新上传相同的文件
  if (fileInputSingle.value) fileInputSingle.value.value = "";
  if (fileInputMultiple.value) fileInputMultiple.value.value = "";

  loading.value = false;
};
</script>
  
<style scoped>
.dcm-uploader {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  margin: 0 auto;
}

.file-input {
  display: none;
}

.buttons-container {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  width: 100%;
  flex-wrap: wrap;
}

.upload-buttons {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.upload-progress {
  width: 100%;
  margin-bottom: 15px;
}

.upload-status {
  text-align: center;
  margin-top: 5px;
  color: #606266;
  font-size: 14px;
}

.error-message {
  color: #f56c6c;
  margin-bottom: 15px;
}

.file-info {
  width: 100%;
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-bottom: 15px;
}

.file-list {
  margin-top: 10px;
  max-height: 300px;
  overflow-y: auto;
  border-top: 1px solid #eee;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 5px;
  border-bottom: 1px solid #eee;
}

.file-item:hover {
  background-color: #f9f9f9;
}

.file-item-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.file-number {
  margin-right: 10px;
  color: #909399;
  font-size: 0.9em;
  min-width: 20px;
}

.file-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  color: #909399;
  font-size: 0.85em;
  margin-left: 10px;
}

.file-actions {
  display: flex;
  gap: 5px;
}

.dicom-viewer-container {
  width: 100%;
  height: 600px;
  border: 1px solid #eee;
  border-radius: 4px;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 5px;
  width: 850px;
  max-width: 95%;
  max-height: 90vh;
  overflow: auto;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.33);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.delete-icon {
  color: #f56c6c;
  cursor: pointer;
}
</style>
