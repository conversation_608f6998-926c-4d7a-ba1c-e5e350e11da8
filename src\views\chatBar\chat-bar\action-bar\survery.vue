<template>
  <my-layout bg-fff v-loading="loading">
    <layout-item>
      <div class="px-15px pt-20px pb-10px font-semibold" font-16 border-bottom>问卷列表</div>
      <div class="py-12px px-15px" border-bottom>
        <el-input v-model="title" placeholder="输入内容名称搜索" :suffix-icon="Search" />
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <div class="h-full flex">
        <my-layout class="min-w-120px w-1/3 max-w-180px" flex-shrink-0 no-radius border-right>
          <layout-main>
            <classify-list appendAll :checked-cate="current" :data="cateList" @change="changeCurrent($event)" />
          </layout-main>
        </my-layout>

        <my-layout class="flex-grow">
          <layout-main v-if="list.length" :scroll="false">
            <el-scrollbar h-full ref="scrollRef" @scroll="handleScroll($event)">
              <div ref="listRef">
                <div v-for="item in list" :key="item._id" flex items-center px-15 border-bottom>
                  <div flex-grow w-0 @click="preview(item)">
                    <div font-14 font-semibold class="pt-10px pb-6px">{{ item.name }}</div>
                    <div pb-10 font-14 text-ellipsis>
                      <el-text type="info">{{ item.description || "暂无问卷说明" }}</el-text>
                    </div>
                  </div>
                  <div flex-shrink-0>
                    <!-- <el-text pointer class="mr-8" type="success" @click="showSurvery(item)">预览</el-text> -->
                    <el-text pointer type="primary" @click.stop="send(item)">发送</el-text>
                  </div>
                </div>
              </div>
            </el-scrollbar>

          </layout-main>
          <layout-main v-else-if="list.length === 0 && !loading" :scroll="false">
            <div flex flex-col h-full justify-center items-center>
              <empty-data :top="0" :title="emptyText" />
            </div>
          </layout-main>
        </my-layout>
      </div>
    </layout-main>

    <layout-item>
      <div class="py-12px px-15px" common-shadow--r>
        <el-button class="block w-full" @click="cancel()">取消</el-button>
      </div>
    </layout-item>
  </my-layout>

  <survery-dialog isMobile :data="survery" :visible="visible" :y-padding="300" @close="close()" />
</template>
<script setup>
import { computed, onMounted, ref, watch } from "vue";
import { useDebounceFn, watchDebounced } from "@vueuse/core";
import { ElMessage } from "element-plus";
import { getList as getSurveyList, createRecord, getSurveryCateList } from "@/api/survery";
import { configStore } from "@/store/config";
import useModal from "@/hooks/useModal";
import { memberStore } from "@/store/member";
import { createServiceRecord } from "@/utils/service";
import SurveryDialog from "@/views/system/survery/dialog.vue";
import EmptyData from "@/components/empty-data.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import classifyList from '@/components/classify-list/classify-list-side.vue';
import useClassifyList from '@/components/classify-list/useClassifyList';

import * as ww from "@/libs/@wecom/jssdk/wecom.esm.js";
const props = defineProps({
  customer: { type: Object, default: () => ({}) },
  unionId: { type: String, default: "" },
  teams: { type: Array, default: () => [] },
});
const emits = defineEmits(["close"]);
const { close, show, visible } = useModal(); //  选择客户弹窗
const team = computed(() => props.teams[0] || {});
const options = {
  getList: getSurveryCateList,
  callback: getList
}

const { cateList, current, changeCurrent } = useClassifyList(options);

function cancel() {
  emits("close");
}


const title = ref('');
const list = ref({});
const page = ref(1);
const total = ref(0)
const loading = ref(false);
const emptyText = ref("");
async function getList() {
  loading.value = true;
  const childrenIds = Array.isArray(current.value.childrenIds) ? current.value.childrenIds : [];
  const cateIds = current.value._id ? [current.value._id, ...childrenIds] : [];
  const { success, message, data = {} } = await getSurveyList(page.value, 30, title.value.trim(), "enable", false, cateIds);
  const { list: tableData = [], total: count } = data;
  emptyText.value = success ? "暂无问卷信息" : message;
  list.value = page.value === 1 ? tableData : [...list.value, ...tableData];
  total.value = count;
  if (!success) ElMessage.error(message);
  loading.value = false;
}

watchDebounced(title, () => {
  page.value = 1;
  getList()
}, { debounce: 1000 })


const scrollRef = ref();
const listRef = ref();
const handleScroll = useDebounceFn(function scroll({ scrollTop }) {
  if (loading.value) return;
  const isBottom = scrollRef.value.wrapRef.scrollHeight - scrollRef.value.wrapRef.clientHeight - scrollTop <= 20;
  if (isBottom && !loading.value && list.value.length < total.value) {
    page.value += 1;
    getList();
  }
});

const survery = ref({});
function preview(item) {
  survery.value = item;
  show();
}

async function send(item) {
  if (loading.value) return;
  loading.value = true;
  const { success, data, message } = await createRecord(item._id, props.customer._id, props.customer.name);
  if (success) sendLink(data.id, props.customer.name, item);
  else ElMessage.error(message);
  loading.value = false;
}

const config = configStore();
async function sendLink(id, name, survery) {
  //发送问卷触发服务记录
  createServiceRecordAction(survery, id);
  const corpId = localStorage.getItem("corpId");
  const url = `${import.meta.env.VITE_PATIENT_PAGE_BASE_URL}pages/survery/fill?corpId=${corpId}&surveryId=${survery._id}&memberId=${props.customer._id}&unionid=${props.customer.unionid || props.unionId || ""}&answerId=${id}&name=${encodeURIComponent(name || "")}`;
  const desc = `请填写问卷`;
  ww.sendChatMessage({
    msgtype: "news", //消息类型，必填
    news: {
      link: url, //H5消息页面url 必填
      title: survery.name || "填写问卷", //H5消息标题
      desc, //H5消息摘要
      imgUrl: "https://796f-youcan-clouddev-1-8ewcqf31dbb2b5-**********.tcb.qcloud.la/other/19-%E9%97%AE%E5%8D%B7.png?sign=55a4cd77c418b2c548b65792a2cf6bce&t=**********", //H5消息封面图片URL
    },
    success: function (res) {
      ElMessage.success("发送成功");
      cancel();
    },
    fail: function (res) {
      ElMessage.success("发送失败");
    },
  });
}

async function createServiceRecordAction(survery, answerId) {
  const { memberInfo } = memberStore();
  const { anotherName } = memberInfo;
  let item = {
    taskContent: `${anotherName}发送问卷"${survery.name}"给客户${props.customer.name}`,
    executionTime: new Date().getTime(),
    customerId: props.customer._id,
    executeTeamId: team.value.teamId || "",
    teamName: team.value.name || "",
    eventType: "questionnaire",
    customerName: props.customer.name,
    pannedEventSendFile: {
      name: survery.name,
      surveryId: survery._id,
      type: "questionnaire",
      answerId,
    },
  };
  createServiceRecord(item);
}
</script>
<style scoped>
.transparent {
  opacity: 0;
}

[border-bottom-dashed] {
  border-bottom: 1px dashed #eee;
}

.mr-8 {
  margin-right: 8px;
}
</style>