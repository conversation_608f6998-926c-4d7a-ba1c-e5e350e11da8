<template>
  <el-dialog :model-value="props.visible" title="复制问卷" :width="520" @close="close()">
    <el-form px-15 pt-10 :label-width="100" label-position="right" label-suffix="：">
      <el-form-item class="is-required" label="问卷标题" >
        <el-input v-model="form.name" placeholder="请输入问卷标题">
          <template #append>
            <el-text pointer type="primary" text @click="useName()">使用原问卷名称</el-text>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="问卷描述">
        <el-input type="textarea" placeholder="请输入问卷说明" :autosize="{ minRows: 4, maxRows: 8 }"
          v-model="form.description" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div text-center>
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="save()"> 保存 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref, watch } from 'vue';
import { ElMessage } from "element-plus";
import { SurveryStatusKey } from '@/baseData';
import { setSurvery } from "@/api/survery";

const props = defineProps({
  data: { type: Object, default: () => ({}) },
  visible: { type: Boolean, default: false },
})
const form = ref({ name: '', description: '' });

const emits = defineEmits(['close', 'reload']);
function close() {
  emits('close')
}

function useName() {
  form.value.name = props.data.name
}

async function save() {
  if (form.value.name.trim() === '') {
    ElMessage.info('请输入问卷标题')
  } else {
    const { list } = props.data;
    const params = {
      list,
      cateId: props.data.cateId,
      corpId: localStorage.getItem('corpId'),
      userId: localStorage.getItem('userId'),
      name: form.value.name,
      description: form.value.description || '',
      status: SurveryStatusKey.init
    }
    const { success, message } = await setSurvery(params);
    if (success) {
      ElMessage.success(message);
      close();
      emits('reload')
    } else {
      ElMessage.error(message)
    }
  }
}

watch(() => props.visible, n => {
  if (n) form.value = { name: '', description: '' }
})
</script>