<template>
  <div class="px-15px py-10px">
    <div active-title-bar class="font-semibold">{{ title }}</div>
    <div v-if="tabs.length" class="flex flex-wrap">
      <div v-for="tab in tabs" :key="tab.key"
        :class="current === tab.key ? 'text-white bg-blue-700 border-blue-700' : ''"
        class="flex-shrink-0 mt-10px mr-15px px-20px text-14px leading-24px h-26px border border-gray-300 rounded-14px cursor-pointer hover:text-white hover:bg-blue-700 hover:border-blue-700"
        @click="changeTab(tab.key)">
        {{ tab.title }}
      </div>
    </div>
  </div>
  <div class="flex items-center flex-wrap text-14px px-15px py-10px"
    :class="tabs.length ? 'border-t border-gray-300 ' : ''">
    <div color-normal>统计日期：</div>
    <div v-for="item in dateTypes" :key="item.value" @click="toggle(item.value)"
      :class="active === item.value ? 'text-blue-700 font-semibold' : ''" class="ml-10px cursor-pointer">
      {{ item.label }}
    </div>
    <div class="ml-10px">
      <el-date-picker v-model="beforeDates" type="daterange"  :unlink-panels="true"
 value-format="YYYY-MM-DD" :disabled-date="judgeDisabled" />
    </div>
    <slot name="append">
      <div class="w-240px ml-10px">
        <ww-user-select :list="peopleList" placeholder="请选择" :value="userId" @change="change($event)" />
      </div>
    </slot>
  </div>
</template>
<script setup>
import { onMounted, ref, computed, watch } from "vue";
import dayjs from "dayjs";
import useDateRange from "./useDateRange";
import WwUserSelect from "@/components/ww-user-select";

const emits = defineEmits(["change"]);
const props = defineProps({
  title: {
    type: String,
    default: "数据趋势",
  },
  tabs: {
    type: Array,
    default: () => [],
  },
  staffList: {
    type: Array,
    default: () => [],
  },
  placeholder: {
    type: String,
    default: "请选择",
  },
  dateItems: {
    type: Array,
    default: () => []
  }
});
// const items = [{ label: "近7天", value: -7 }, { value: "lastWeek" }, { value: "thisMonth" }, { value: "lastMonth" }];
const { current: active, dates, list: dateTypes, change: toggle } = useDateRange({ items: props.dateItems, untilToday: true, callback: onChange });
const beforeDates = computed({
  set(val) {
    dates.value = [...val]
  },
  get() {
    if (dates.value.length === 2) {
      let [startDate, endDate] = dates.value;
      if (dayjs().isSame(dayjs(endDate), 'day') || dayjs().isBefore(dayjs(endDate), 'day')) {
        return [startDate, dayjs().subtract(1, 'day').format("YYYY-MM-DD")]
      }
    }
    return [...dates.value]
  }
})
const peopleList = computed(() => props.staffList.map((i) => i.userid));
const userId = ref("");
const current = ref("");
function change(val) {
  userId.value = val;
  onChange("selectStaff");
}
function changeTab(key) {
  if (key !== current.value) {
    current.value = key;
  }
}
function judgeDisabled(date) {
  return !(dayjs().startOf('day').isAfter(dayjs(date)))
}

function onChange(type) {
  emits("change", {
    type,
    userId: userId.value,
    dates: beforeDates.value,
    tab: current.value,
  });
}

onMounted(() => {
  current.value = props.tabs[0] ? props.tabs[0].key : "";
});
watch([dates, current], (n) => {
  onChange();
});
</script>
<style lang="scss" scoped></style>
