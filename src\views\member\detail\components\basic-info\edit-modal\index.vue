<template>
  <el-dialog :model-value="props.visible" title="客户基础信息编辑" :close-on-click-modal="false" :width="750" @close="onClose()">
    <!-- :rules="rules" -->
    <el-scrollbar :max-height="maxHeight">
      <el-form class="mr-20px" :model="form" ref="formRef" :label-width="labelWidth" label-suffix="：">
        <el-row>
          <el-col :span="24">
            <div class="title-bar pt-15px">基本信息</div>
          </el-col>
          <el-col :span="12" v-if="isShowCell('name')">
            <el-form-item label="客户姓名" prop="name">
              <el-input placeholder="请输入客户姓名" v-model="form.name"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="isShowCell('relationship')">
            <el-form-item class="el-form-item--autoHeight pl-4px" label="微信联系人关系" prop="relationship">
              <!-- <span>{{ form.relationship }}</span> -->
              <el-select v-model="form.relationship" placeholder="" clearable>
                <el-option v-for="item in mobileRelation" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="isShowCell('cardType')">
            <el-form-item label="证件类型" prop="name">
              <span>{{ form.cardType }}</span>
            </el-form-item>
          </el-col>

          <el-col :span="12" v-if="isShowCell('idCard')">
            <el-form-item label="证件号码" prop="idCard">
              <el-input placeholder="请输入证件号码" v-model="form.idCard" @change="changeIdCard($event)"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="isShowCell('birthday')">
            <el-form-item label="出生日期" prop="birthday">
              <el-date-picker v-model="form.birthday" style="width: 100%;" type="date" placeholder="请选择出生时间"
                format="YYYY-MM-DD" @change="changeBirthday($event)" />
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="isShowCell('sex')">
            <el-form-item label="性别" prop="sex">
              <el-radio-group v-model="form.sex">
                <el-radio label="男">男</el-radio>
                <el-radio label="女">女</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="isShowCell('age')">
            <el-form-item label="年龄" prop="age">
              <el-input placeholder="请输入年龄" v-model="form.age"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="isShowCell('mobile')">
            <el-form-item label="预留手机号" prop="mobile">
              <span>{{ form.mobile }}</span>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="联系方式①" prop="phone1">
              <el-input placeholder="请输入联系方式①" v-model="form.phone1">
                <template #append>
                  <el-select v-model="form.phone1Note" placeholder="备注" class="append-select" clearable>
                    <el-option v-for="item in mobileRelation" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系方式②" prop="phone2">
              <el-input placeholder="请输入联系方式②" v-model="form.phone2">
                <template #append>
                  <el-select v-model="form.phone2Note" placeholder="备注" class="append-select" clearable>
                    <el-option v-for="item in mobileRelation" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系方式③" prop="phone3">
              <el-input placeholder="请输入联系方式③" v-model="form.phone3">
                <template #append>
                  <el-select v-model="form.phone3Note" placeholder="备注" class="append-select" clearable>
                    <el-option v-for="item in mobileRelation" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="Array.isArray(healthInfo) && healthInfo.length > 0">
            <div class="title-bar">健康信息</div>
          </el-col>
          <el-col :span="24" v-if="isShowCell('allergy')">
            <el-form-item label="过敏史" prop="allergyHistory">
              <el-input placeholder="请输入过敏史" type="textarea" rows="3" v-model="form.allergyHistory"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="isShowCell('disease')">
            <el-form-item label="疾病史" prop="pastMedicalHistory">
              <el-tag v-for="tag in form.pastMedicalHistory" :key="tag" class="mr-20 mb-5" closable type="info"
                @close="handleTagClose(tag)">
                {{ tag }}
              </el-tag>
              <el-button type="primary" size="small" :icon="Plus" plain @click="addDisease">添加</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>

    <template #footer>
      <div text-center>
        <el-button class="w-100px" @click="onClose()">取消</el-button>
        <el-button class="w-100px" type="primary" @click="save()"> 保存 </el-button>
      </div>
    </template>
  </el-dialog>
  <disease-drawer ref="diseaseRef" @get-select-disease="onGetSelectDisease"></disease-drawer>
</template>
<script setup>
import { inject, onMounted, ref, watch } from 'vue';
import dayjs from "dayjs";
import { mobileRelation } from '@/baseData';
import { getBirthFromIdNo } from '@/utils';
import validate from '@/utils/validate';
import diseaseDrawer from "@/views/member/components/diseaseDrawer/index.vue";
import { memberStore } from "@/store/member";
import { templateStore } from "@/store/template";
const { isShowCell } = templateStore();

const { corpInfo } = memberStore();
const {
  corpFileds: { healthInfo }
} = corpInfo;
const labelWidth = inject('labelWidth');
const props = defineProps({
  visible: { type: Boolean, default: false },
  customer: { type: Object, default: () => ({}) }
})
const maxHeight = ref('400px')
onMounted(() => {
  const height = window.innerHeight;
  maxHeight.value = height - height * 0.15 - 220 + 'px'
})

const form = ref({});
const formRef = ref();
function initForm() {
  const data = { ...props.customer };
  const phones = Array.isArray(data.assistantByMobile) ? data.assistantByMobile : [];
  const [phone1, phone2, phone3] = phones;
  [phone1, phone2, phone3].forEach((item, idx) => {
    if (item) {
      data[`phone${idx + 1}`] = item.mobile;
      data[`phone${idx + 1}Note`] = item.note || ''
    }
  })
  data.pastMedicalHistory = Array.isArray(data.pastMedicalHistory) ? data.pastMedicalHistory : [];
  form.value = data
}
watch(props, (n, o) => {
  if (!n.visible) return;
  initForm()
  const height = window.innerHeight;
  maxHeight.value = height - height * 0.15 - 220 + 'px'
})

const diseaseRef = ref();
function handleTagClose(tag) {
  form.value.pastMedicalHistory = form.value.pastMedicalHistory.filter(i => i !== tag)
}
function addDisease() {
  diseaseRef.value.openDialog(form.value.pastMedicalHistory);
}
function onGetSelectDisease(e) {
  form.value.pastMedicalHistory = e
}

const emits = defineEmits(['close', 'update'])
function onClose() {
  emits('close')
}
function save() {
  emits('update', getFormData())
}

function changeBirthday(e) {
  if (e) {
    const birthNum = dayjs(e).format('YYYY.MMDD');
    const todayNum = dayjs().format('YYYY.MMDD');
    form.value.age = Math.max(Math.floor(todayNum - birthNum), 1);
  }
}
function changeIdCard(idno) {
  const isIdNo = validate.isChinaId(idno);
  if (isIdNo) {
    const date = getBirthFromIdNo(idno);
    form.value.birthday = date;
    const sexNum = idno.slice(-2, -1);
    form.value.sex = sexNum % 2 === 0 ? '女' : '男';
    changeBirthday(date)
  }
}

function getFormData() {
  const params = {}
  if (isShowCell('name')) params.name = typeof form.value.name === 'string' ? form.value.name.trim() : '';
  if (isShowCell('idCard')) params.idCard = typeof form.value.idCard === 'string' ? form.value.idCard.trim() : '';
  if (isShowCell('sex')) params.sex = form.value.sex || '';
  if (isShowCell('age')) params.age = form.value.age || '';
  if (isShowCell('birthday')) params.birthday = form.value.birthday || '';
  if (isShowCell('allergy')) params.allergyHistory = form.value.allergyHistory || '';
  if (isShowCell('disease')) params.pastMedicalHistory = form.value.pastMedicalHistory || '';
  if (isShowCell('relationship')) params.relationship = form.value.relationship || '';

  params.assistantByMobile = [1, 2, 3].map(i => {
    if (form.value[`phone${i}`]) return { mobile: form.value[`phone${i}`], note: form.value[`phone${i}Note`] || '' }
    return null
  }).filter(Boolean);
  return params
}

</script>
<style scoped>
:deep(.el-form-item.el-form-item--autoHeight .el-form-item__label ) {
 line-height: 1.2em;
}
.mb-5 {
  margin-bottom: 5px;
}

.append-select {
  width: 80px;
}

.title-bar {
  position: relative;
  padding-left: 10px;
  margin-bottom: 15px;
  font-size: 14px;
  font-weight: bold;
  color: var(--yc-main-text-color);
}

.mr-20 {
  margin-right: 20px;
}
</style>