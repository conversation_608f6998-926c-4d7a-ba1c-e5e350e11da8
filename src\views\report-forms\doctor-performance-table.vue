<template>
  <div class="report-container">
    <div class="report-header">
      <div class="report-actions">
        <div class="filter-section">
          <div class="filter-row">
            <div class="filter-item">
              <span class="filter-label">日期：</span>
              <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :shortcuts="dateShortcuts" :disabled-date="disabledDate" @change="handleDateChange" class="date-range" value-format="YYYY-MM-DD" />
            </div>
            <div class="filter-item">
              <check-box-filter v-model="receptionDoctorUserId" label="面诊医生" :list="doctorList">
                <template #item="{ item }">
                  <ww-user :openid="item.value"></ww-user>
                </template>
              </check-box-filter>
            </div>
            <div class="filter-item">
              <span class="filter-label">咨询项目：</span>
              <project-intent-select v-model="projectIntentIds" placeholder="请选择或搜索咨询项目" @change="handleProjectChange" />
            </div>
            <div class="filter-actions">
              <el-button type="primary" @click="handleSearch" :loading="loading" :disabled="loading">查询</el-button>
              <el-button type="success" @click="exportToExcel" :disabled="loading">导出Excel</el-button>
              <el-button @click="resetFilters" :disabled="loading">重置</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="table-container">
      <table class="performance-table">       
         <colgroup>
          <col style="width: 120px" />
          <!-- 医生 -->
          <col style="width: 100px" />
          <!-- 面诊人次(初诊) -->
          <col style="width: 100px" />
          <!-- 成交人次(初诊) -->
          <col style="width: 100px" />
          <!-- 成交率(初诊) -->
          <col style="width: 120px" />
          <!-- 开单业绩(初诊) -->
          <col style="width: 100px" />
          <!-- 客单价(初诊) -->
          <col style="width: 100px" />
          <!-- 面诊人次(复诊) -->
          <col style="width: 100px" />
          <!-- 成交人次(复诊) -->
          <col style="width: 100px" />
          <!-- 成交率(复诊) -->
          <col style="width: 120px" />
          <!-- 开单业绩(复诊) -->
          <col style="width: 100px" />
          <!-- 客单价(复诊) -->
          <col style="width: 100px" />
          <!-- 面诊人次(老客) -->
          <col style="width: 100px" />
          <!-- 成交人次(老客) -->
          <col style="width: 120px" />
          <!-- 成交金额(老客) -->
          <col style="width: 100px" />
          <!-- 客单价(老客) -->
          <col style="width: 100px" />
          <!-- 面诊人次(合计) -->
          <col style="width: 100px" />
          <!-- 成交人次(合计) -->
          <col style="width: 120px" />
          <!-- 成交金额(合计) -->
          <col style="width: 100px" />
          <!-- 客单价(合计) -->
        </colgroup>
        <thead>
          <tr>
            <th rowspan="2">医生</th>
            <th colspan="5">初诊</th>
            <th colspan="5">复诊</th>
            <th colspan="4">老客情况（再消费）</th>
            <th colspan="4">合计</th>
          </tr>
          <tr>
            <th>面诊人次</th>
            <th>成交人次</th>
            <th>成交率</th>
            <th>开单业绩</th>
            <th>客单价</th>
            <th>面诊人次</th>
            <th>成交人次</th>
            <th>成交率</th>
            <th>开单业绩</th>
            <th>客单价</th>
            <th>面诊人次</th>
            <th>成交人次</th>
            <th>成交金额</th>
            <th>客单价</th>
            <th>面诊人次</th>
            <th>成交人次</th>
            <th>成交金额</th>
            <th>客单价</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in tableData" :key="index">
            <td>{{ item.doctorName }}</td>
            <!-- 初诊 -->
            <td>{{ item.firstVisitCount }}</td>
            <td>{{ item.firstVisitSuccessCount }}</td>
            <td>{{ formatPercentage(item.firstVisitSuccessRate) }}</td>
            <td class="amount">{{ formatCurrency(item.firstVisitAmount) }}</td>
            <td class="amount">{{ formatCurrency(item.firstVisitAvgOrderValue) }}</td>
            <!-- 复诊 -->
            <td>{{ item.returnVisitCount }}</td>
            <td>{{ item.returnVisitSuccessCount }}</td>
            <td>{{ formatPercentage(item.returnVisitSuccessRate) }}</td>
            <td class="amount">{{ formatCurrency(item.returnVisitAmount) }}</td>
            <td class="amount">{{ formatCurrency(item.returnVisitAvgOrderValue) }}</td>
            <!-- 老客情况（再消费） -->
            <td>{{ item.moreConsumedCount }}</td>
            <td>{{ item.moreConsumedSuccessCount }}</td>
            <td class="amount">{{ formatCurrency(item.moreConsumedSuccessAmount) }}</td>
            <td class="amount">{{ formatCurrency(item.moreConsumedAvgOrderValue) }}</td>
            <!-- 合计 -->
            <td>{{ item.consultCount }}</td>
            <td>{{ item.successCount }}</td>
            <td class="amount">{{ formatCurrency(item.successAmount) }}</td>
            <td class="amount">{{ formatCurrency(item.avgOrderValue) }}</td>
          </tr>
          <tr class="total-row">
            <td class="total-label">合计：</td>
            <!-- 初诊 -->
            <td>{{ totalFirstVisitCount }}</td>
            <td>{{ totalFirstVisitSuccessCount }}</td>
            <td>{{ formatPercentage(totalFirstVisitSuccessRate) }}</td>
            <td class="amount">{{ formatCurrency(totalFirstVisitAmount) }}</td>
            <td class="amount">{{ formatCurrency(totalFirstVisitAvgOrderValue) }}</td>
            <!-- 复诊 -->
            <td>{{ totalReturnVisitCount }}</td>
            <td>{{ totalReturnVisitSuccessCount }}</td>
            <td>{{ formatPercentage(totalReturnVisitSuccessRate) }}</td>
            <td class="amount">{{ formatCurrency(totalReturnVisitAmount) }}</td>
            <td class="amount">{{ formatCurrency(totalReturnVisitAvgOrderValue) }}</td>
            <!-- 老客情况（再消费） -->
            <td>{{ totalMoreConsumedCount }}</td>
            <td>{{ totalMoreConsumedSuccessCount }}</td>
            <td class="amount">{{ formatCurrency(totalMoreConsumedSuccessAmount) }}</td>
            <td class="amount">{{ formatCurrency(totalMoreConsumedAvgOrderValue) }}</td>
            <!-- 合计 -->
            <td>{{ totalConsultCount }}</td>
            <td>{{ totalSuccessCount }}</td>
            <td class="amount">{{ formatCurrency(totalSuccessAmount) }}</td>
            <td class="amount">{{ formatCurrency(totalAvgOrderValue) }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="footer-info">
      <span>共 {{ tableData.length }} 条记录</span>
      <span class="date-limit-info">* 查询限制为一个月内的数据</span>
    </div>
  </div>
  <dept-picker-modal ref="deptPickerRef" :value="deptIds" :visible="deptVisible" :width="deptPickerWidth" @change="deptChange" @close="closeDeptPicker" />
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { ElButton, ElMessage, ElDatePicker } from "element-plus";
import { getDoctorPerformance } from "@/api/bill-record";
import useModal from "@/hooks/useModal";
import { CheckBoxFilter, baseFilterItem } from "@/components/filter-bar";
import { storeToRefs } from "pinia";
import deptPickerModal from "@/components/dept-picker-modal/dept-picker-modal.vue";
import { staffStore } from "@/store/staff";
import ProjectIntentSelect from "@/components/benefit-management/project-intent-select.vue";

const receptionDoctorUserId = ref([]);
const projectIntentIds = ref([]);
const selectedProjects = ref([]);

const doctorList = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("doctor")).map((i) => ({ value: i.userid }));
});

const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());

// 项目选择变更处理
const handleProjectChange = (projects) => {
  selectedProjects.value = projects;
  // 自动触发查询
  handleDetDoctorPerformance();
};

// 新增筛选选项
/* ===============  科室筛选  start ================= */
const selectDepts = ref([]);
const deptIds = computed(() => selectDepts.value.map((i) => i._id));
const deptText = computed(() => {
  if (selectDepts.value.length === 0) return "";
  if (selectDepts.value.length > 1) return `已选择${selectDepts.value.length}个项目`;
  return selectDepts.value[0].deptName;
});
function deptChange(val) {
  selectDepts.value = val;
  closeDeptPicker();
}
function clearDepts() {
  selectDepts.value = [];
}
/* ===============  科室筛选  end ================= */

// 日期筛选
const dateRange = ref([]);

// 日期快捷选项
const dateShortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近半个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
];

// 禁用超过一个月范围的日期
const disabledDate = (time) => {
  if (!dateRange.value || dateRange.value.length === 0) {
    return false;
  }

  const startDate = new Date(dateRange.value[0]);
  const endDate = new Date(dateRange.value[1]);
  const currentDate = new Date(time);

  // 如果已选择了开始日期，限制结束日期不能超过开始日期后的31天
  if (dateRange.value.length === 1) {
    const oneMonthLater = new Date(startDate);
    oneMonthLater.setDate(startDate.getDate() + 31);
    return currentDate > oneMonthLater;
  }

  return false;
};

// 监听日期变化，确保不超过一个月
watch(dateRange, (newValue) => {
  if (newValue && newValue.length === 2) {
    const start = new Date(newValue[0]);
    const end = new Date(newValue[1]);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays > 31) {
      // 如果超过31天，将结束日期设置为开始日期后的31天
      const maxEnd = new Date(start);
      maxEnd.setDate(start.getDate() + 31);
      const formattedMaxEnd = formatDate(maxEnd);

      dateRange.value = [newValue[0], formattedMaxEnd];
      ElMessage.warning("查询范围已限制为一个月内");
    }
    // 日期变化后自动触发查询
    handleDetDoctorPerformance();
  }
});

// 添加对医生筛选条件的监听
watch(receptionDoctorUserId, () => {
  handleDetDoctorPerformance();
});

// 添加对项目筛选条件的监听
watch(projectIntentIds, () => {
  handleDetDoctorPerformance();
});

// 格式化显示的日期范围
const formatDateRange = computed(() => {
  if (dateRange.value && dateRange.value.length === 2) {
    return `${dateRange.value[0]} 至 ${dateRange.value[1]}`;
  }
  return "全部日期";
});

// 格式化日期为YYYY-MM-DD
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  return `${year}-${month}-${day}`;
};

// 表格数据
const tableData = ref([]);
// 根据接口数据填充表格
async function handleDetDoctorPerformance() {
  try {
    const params = {
      startDate: dateRange.value ? dateRange.value[0] : undefined,
      endDate: dateRange.value ? dateRange.value[1] : undefined,
      projectIds: projectIntentIds.value?.length > 0 ? projectIntentIds.value : undefined,
      receptionDoctorUserId: receptionDoctorUserId.value?.length > 0 ? receptionDoctorUserId.value : undefined,
    };
    const { data, success, message } = await getDoctorPerformance(params);
    if (success) {
      tableData.value = data.data.map((i) => {
        const doctorName = staffList.value.find((staff) => staff.userid === i.receptionDoctorUserId)?.anotherName || "未知医生";

        // 初诊成交率
        const firstVisitSuccessRate = i.firstVisitCount > 0 ? i.firstVisitSuccessCount / i.firstVisitCount : 0;

        // 初诊客单价
        const firstVisitAvgOrderValue = i.firstVisitSuccessCount > 0 ? i.firstVisitAmount / i.firstVisitSuccessCount : 0;

        // 复诊成交率
        const returnVisitSuccessRate = i.returnVisitCount > 0 ? i.returnVisitSuccessCount / i.returnVisitCount : 0;

        // 复诊客单价
        const returnVisitAvgOrderValue = i.returnVisitSuccessCount > 0 ? i.returnVisitAmount / i.returnVisitSuccessCount : 0;

        // 新客成交人次
        const newCustomerSuccessCount = i.firstVisitSuccessCount + i.returnVisitSuccessCount || 0;

        // 新客成交率
        const newCustomerSuccessRate = i.firstVisitCount + i.returnVisitCount > 0 ? newCustomerSuccessCount / (i.firstVisitCount + i.returnVisitCount) : 0;

        // 新客成交金额
        const newCustomerSuccessAmount = i.firstVisitAmount + i.returnVisitAmount || 0;

        // 新客客单价
        const newCustomerAvgOrderValue = newCustomerSuccessCount > 0 ? newCustomerSuccessAmount / newCustomerSuccessCount : 0;

        // 老客成交人次
        const moreConsumedSuccessCount = i.moreConsumedSuccessCount || 0;

        // 老客成交金额
        const moreConsumedSuccessAmount = i.moreConsumedAmount || 0;

        // 老客客单价
        const moreConsumedAvgOrderValue = moreConsumedSuccessCount > 0 ? moreConsumedSuccessAmount / moreConsumedSuccessCount : 0;

        // 总客单价
        const avgOrderValue = i.successCount > 0 ? i.successAmount / i.successCount : 0;

        return {
          ...i,
          doctorName,
          firstVisitSuccessRate,
          firstVisitAvgOrderValue,
          returnVisitSuccessRate,
          returnVisitAvgOrderValue,
          newCustomerSuccessCount,
          newCustomerSuccessRate,
          newCustomerSuccessAmount,
          newCustomerAvgOrderValue,
          moreConsumedSuccessCount,
          moreConsumedSuccessAmount,
          moreConsumedAvgOrderValue,
          avgOrderValue,
        };
      });
    } else {
      ElMessage.error(message || "获取医生业绩数据失败");
    }
  } catch (error) {
    ElMessage.error("获取医生业绩数据出错：" + error.message);
  }
}

// 计算合计 - 更新为接口返回的字段
const totalConsultCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (item.consultCount || 0), 0);
});

const totalSuccessCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (item.successCount || 0), 0);
});

const totalSuccessAmount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (item.successAmount || 0), 0);
});

const totalConsumeCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (item.consumeCount || 0), 0);
});

const totalNetAmount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (item.netAmount || 0), 0);
});

const totalFirstVisitCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (item.firstVisitCount || 0), 0);
});

const totalReturnVisitCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (item.returnVisitCount || 0), 0);
});

const totalMoreConsumedCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (item.moreConsumedCount || 0), 0);
});

// 添加初诊相关的计算属性
const totalFirstVisitSuccessCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (item.firstVisitSuccessCount || 0), 0);
});

const totalFirstVisitSuccessRate = computed(() => {
  return totalFirstVisitCount.value > 0 ? totalFirstVisitSuccessCount.value / totalFirstVisitCount.value : 0;
});

const totalFirstVisitAmount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (item.firstVisitAmount || 0), 0);
});

const totalFirstVisitAvgOrderValue = computed(() => {
  return totalFirstVisitSuccessCount.value > 0 ? totalFirstVisitAmount.value / totalFirstVisitSuccessCount.value : 0;
});

// 添加复诊相关的计算属性
const totalReturnVisitSuccessCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (item.returnVisitSuccessCount || 0), 0);
});

const totalReturnVisitSuccessRate = computed(() => {
  return totalReturnVisitCount.value > 0 ? totalReturnVisitSuccessCount.value / totalReturnVisitCount.value : 0;
});

const totalReturnVisitAmount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (item.returnVisitAmount || 0), 0);
});

const totalReturnVisitAvgOrderValue = computed(() => {
  return totalReturnVisitSuccessCount.value > 0 ? totalReturnVisitAmount.value / totalReturnVisitSuccessCount.value : 0;
});

// 添加新的统计指标
const totalNewCustomerSuccessCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (item.newCustomerSuccessCount || 0), 0);
});

const totalNewCustomerSuccessRate = computed(() => {
  const totalNewCustomerConsult = totalFirstVisitCount.value + totalReturnVisitCount.value;
  return totalNewCustomerConsult > 0 ? totalNewCustomerSuccessCount.value / totalNewCustomerConsult : 0;
});

const totalNewCustomerSuccessAmount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (item.newCustomerSuccessAmount || 0), 0);
});

const totalNewCustomerAvgOrderValue = computed(() => {
  return totalNewCustomerSuccessCount.value > 0 ? totalNewCustomerSuccessAmount.value / totalNewCustomerSuccessCount.value : 0;
});

const totalMoreConsumedSuccessCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (item.moreConsumedSuccessCount || 0), 0);
});

const totalMoreConsumedSuccessAmount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (item.moreConsumedSuccessAmount || 0), 0);
});

const totalMoreConsumedAvgOrderValue = computed(() => {
  return totalMoreConsumedSuccessCount.value > 0 ? totalMoreConsumedSuccessAmount.value / totalMoreConsumedSuccessCount.value : 0;
});

const totalAvgOrderValue = computed(() => {
  return totalSuccessCount.value > 0 ? totalSuccessAmount.value / totalSuccessCount.value : 0;
});

// 格式化货币
const formatCurrency = (value) => {
  return new Intl.NumberFormat("zh-CN", { style: "currency", currency: "CNY" }).format(value);
};

// 格式化百分比
const formatPercentage = (value) => {
  if (value === undefined || value === null || isNaN(value)) {
    return "0.00%";
  }
  return `${(value * 100).toFixed(2)}%`;
};

// 重置筛选条件
const resetFilters = () => {
  // 设置默认日期范围为当月
  const today = new Date();
  const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
  const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

  dateRange.value = [formatDate(firstDayOfMonth), formatDate(lastDayOfMonth)];
  selectDepts.value = [];
  projectIntentIds.value = [];
  selectedProjects.value = [];
  receptionDoctorUserId.value = [];

  // 重置后自动查询
  handleDetDoctorPerformance();
};

// 处理查询 - 修改为调用接口
const handleSearch = () => {
  if (dateRange.value && dateRange.value.length === 2) {
    const start = new Date(dateRange.value[0]);
    const end = new Date(dateRange.value[1]);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays > 31) {
      ElMessage.warning("查询范围已限制为一个月内");
      return;
    }

    console.log("查询条件：", {
      dateRange: dateRange.value,
      departments: selectDepts.value || "全部",
      projectFilter: projectIntentIds.value || "全部",
      doctorIds: receptionDoctorUserId.value || "全部",
    });

    // 调用接口获取数据
    handleDetDoctorPerformance();
  } else {
    ElMessage.warning("请选择日期范围");
  }
};

// 日期变更处理函数
const handleDateChange = () => {
  // 不需要在这里触发查询，因为已经在 watch 中处理了
  console.log("日期已变更:", dateRange.value);
};

// 导出Excel - 更新为新的字段名称
const exportToExcel = () => {
  // 准备导出数据
  const exportData = [];

  // 添加筛选条件信息
  exportData.push(["医生业绩统计表"]);
  exportData.push(["日期范围", dateRange.value ? `${dateRange.value[0]} 至 ${dateRange.value[1]}` : "全部日期"]);

  const projectsText = selectedProjects.value.length > 0 ? selectedProjects.value.map((proj) => proj.name).join(", ") : "全部";
  exportData.push(["咨询项目", projectsText]);

  const doctorsText = receptionDoctorUserId.value.length > 0 ? `已选择${receptionDoctorUserId.value.length}位医生` : "全部";
  exportData.push(["医生", doctorsText]);

  exportData.push([]); // 空行

  // 添加表头 - 更新为新的字段名称
  exportData.push(["医生", "初诊", "", "", "", "", "复诊", "", "", "", "", "老客情况（再消费）", "", "", "", "合计", "", "", ""]);

  exportData.push(["", "面诊人次", "成交人次", "成交率", "开单业绩", "客单价", "面诊人次", "成交人次", "成交率", "开单业绩", "客单价", "面诊人次", "成交人次", "成交金额", "客单价", "面诊人次", "成交人次", "成交金额", "客单价"]);

  // 格式化数字
  const formatNumber = (num) => {
    return Number((num || 0).toFixed(2));
  };

  // 添加数据行
  tableData.value.forEach((item) => {
    exportData.push([
      item.doctorName,
      // 初诊
      item.firstVisitCount,
      item.firstVisitSuccessCount,
      `${(item.firstVisitSuccessRate * 100).toFixed(2)}%`,
      formatNumber(item.firstVisitAmount),
      formatNumber(item.firstVisitAvgOrderValue),
      // 复诊
      item.returnVisitCount,
      item.returnVisitSuccessCount,
      `${(item.returnVisitSuccessRate * 100).toFixed(2)}%`,
      formatNumber(item.returnVisitAmount),
      formatNumber(item.returnVisitAvgOrderValue),
      // 老客情况
      item.moreConsumedCount,
      item.moreConsumedSuccessCount,
      formatNumber(item.moreConsumedSuccessAmount),
      formatNumber(item.moreConsumedAvgOrderValue),
      // 合计
      item.consultCount,
      item.successCount,
      formatNumber(item.successAmount),
      formatNumber(item.avgOrderValue),
    ]);
  });

  // 添加总计行
  exportData.push([
    "合计",
    // 初诊合计
    totalFirstVisitCount.value,
    totalFirstVisitSuccessCount.value,
    `${(totalFirstVisitSuccessRate.value * 100).toFixed(2)}%`,
    formatNumber(totalFirstVisitAmount.value),
    formatNumber(totalFirstVisitAvgOrderValue.value),
    // 复诊合计
    totalReturnVisitCount.value,
    totalReturnVisitSuccessCount.value,
    `${(totalReturnVisitSuccessRate.value * 100).toFixed(2)}%`,
    formatNumber(totalReturnVisitAmount.value),
    formatNumber(totalReturnVisitAvgOrderValue.value),
    // 老客情况合计
    totalMoreConsumedCount.value,
    totalMoreConsumedSuccessCount.value,
    formatNumber(totalMoreConsumedSuccessAmount.value),
    formatNumber(totalMoreConsumedAvgOrderValue.value),
    // 总合计
    totalConsultCount.value,
    totalSuccessCount.value,
    formatNumber(totalSuccessAmount.value),
    formatNumber(totalAvgOrderValue.value),
  ]);

  // 创建工作簿和工作表
  const wb = XLSX.utils.book_new();
  const ws = XLSX.utils.aoa_to_sheet(exportData);

  // 设置列宽
  const colWidths = [
    { wch: 12 }, // 医生
    { wch: 10 }, // 面诊人次(初诊)
    { wch: 10 }, // 成交人次(初诊)
    { wch: 10 }, // 成交率(初诊)
    { wch: 12 }, // 开单业绩(初诊)
    { wch: 10 }, // 客单价(初诊)
    { wch: 10 }, // 面诊人次(复诊)
    { wch: 10 }, // 成交人次(复诊)
    { wch: 10 }, // 成交率(复诊)
    { wch: 12 }, // 开单业绩(复诊)
    { wch: 10 }, // 客单价(复诊)
    { wch: 10 }, // 面诊人次(老客)
    { wch: 10 }, // 成交人次(老客)
    { wch: 12 }, // 成交金额(老客)
    { wch: 10 }, // 客单价(老客)
    { wch: 10 }, // 面诊人次(合计)
    { wch: 10 }, // 成交人次(合计)
    { wch: 12 }, // 成交金额(合计)
    { wch: 10 }, // 客单价(合计)
  ];

  ws["!cols"] = colWidths;

  // 合并标题单元格和表头单元格
  ws["!merges"] = [
    { s: { r: 0, c: 0 }, e: { r: 0, c: 18 } }, // 标题行
    { s: { r: 1, c: 0 }, e: { r: 1, c: 18 } }, // 日期行
    { s: { r: 2, c: 0 }, e: { r: 2, c: 18 } }, // 项目行
    { s: { r: 3, c: 0 }, e: { r: 3, c: 18 } }, // 医生行
    { s: { r: 5, c: 0 }, e: { r: 6, c: 0 } }, // 医生列
    { s: { r: 5, c: 1 }, e: { r: 5, c: 5 } }, // 初诊列
    { s: { r: 5, c: 6 }, e: { r: 5, c: 10 } }, // 复诊列
    { s: { r: 5, c: 11 }, e: { r: 5, c: 14 } }, // 老客情况列
    { s: { r: 5, c: 15 }, e: { r: 5, c: 18 } }, // 合计列
  ];

  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(wb, ws, "医生面诊表");

  // 生成Excel文件并下载
  const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
  const blob = new Blob([excelBuffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });

  // 使用当前日期作为文件名的一部分
  const now = new Date();
  const fileName = `医生面诊表_${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}.xlsx`;

  saveAs(blob, fileName);

  ElMessage.success("导出Excel成功");
};

onMounted(async () => {
  // 设置默认日期范围为当月
  if (staffList.value.length === 0) await getStaffList();
  const end = new Date();
  const start = new Date(end.getFullYear(), end.getMonth(), 1);
  dateRange.value = [formatDate(start), formatDate(end)];
  // 初始加载数据
  handleDetDoctorPerformance();
});
</script>

<style scoped>
.report-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
  background-color: #f5f7fa;
  overflow: hidden;
  min-width: 1200px;
}

.report-header {
  background-color: white;
  padding: 15px 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
}

.report-title {
  text-align: center;
  font-weight: bold;
  font-size: 20px;
  margin-bottom: 5px;
  color: #303133;
}

.report-date {
  text-align: center;
  font-size: 14px;
  margin-bottom: 15px;
  color: #606266;
}

.filter-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-label {
  white-space: nowrap;
  margin-right: 5px;
  font-size: 14px;
  color: #606266;
}

.filter-actions {
  margin-left: auto;
  display: flex;
  gap: 10px;
}

.date-range {
  min-width: 320px;
}

:deep(.el-date-editor.el-input__wrapper),
:deep(.el-select) {
  width: 100%;
  flex: 1;
}

.buttons-row {
  justify-content: flex-end;
}

.filter-controls {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.table-container {
  flex: 1;
  margin: 15px 0 0 0;
  background-color: white;
  overflow: auto;
  position: relative;
  border-top: 1px solid #ebeef5;
  border-bottom: 1px solid #ebeef5;
  max-height: 70vh;
}

.performance-table {
  width: 100%;
  min-width: 2180px; 
  border-collapse: collapse;
  font-size: 14px;
  table-layout: fixed;
}

.performance-table th,
.performance-table td {
  border: 1px solid #ebeef5;
  padding: 12px 8px;
  text-align: center;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.performance-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 10;
}

.performance-table .amount {
  text-align: right;
  padding-right: 10px;
  min-width: 100px; /* 确保最小宽度足够显示7位数+小数点两位 */
  max-width: 120px;
  white-space: nowrap;
  overflow: visible; /* 更改为visible，避免截断 */
}

.total-row {
  background-color: #ebeef5;
  font-weight: bold;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.total-label {
  text-align: right;
  padding-right: 10px;
}

.footer-info {
  background-color: white;
  padding: 10px 20px;
  margin: 0;
  display: flex;
  justify-content: space-between;
  color: #606266;
  font-size: 14px;
  flex-shrink: 0;
  border-top: 1px solid #ebeef5;
}

.date-limit-info {
  color: #e6a23c;
  font-style: italic;
}

@media print {
  .report-container {
    background-color: white;
    height: auto;
  }

  .report-header {
    box-shadow: none;
  }

  .report-actions,
  .footer-info {
    display: none;
  }

  .table-container {
    margin: 0;
    border: none;
    overflow: visible;
    height: auto;
  }

  .performance-table th,
  .performance-table td {
    font-size: 12px;
    padding: 8px 4px;
  }
}

@media (max-width: 768px) {
  .filter-item {
    max-width: 100%;
  }

  .report-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .filters-row {
    flex-direction: column;
  }

  .filter-item {
    width: 100%;
  }

  .filter-controls {
    margin-top: 10px;
    width: 100%;
    justify-content: space-between;
  }
}

.performance-table th[rowspan="2"] {
  vertical-align: middle;
}

.performance-table th {
  border: 1px solid #ebeef5;
  padding: 12px 8px;
  text-align: center;
  height: 40px;
  background-color: #f5f7fa;
  color: #606266;
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 10;
}

/* 添加一个媒体查询，在大屏幕上提供更多空间 */
@media (min-width: 1400px) {
  .performance-table .amount {
    min-width: 120px;
    max-width: 150px;
  }
}
</style>