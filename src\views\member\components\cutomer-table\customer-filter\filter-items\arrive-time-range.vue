<template>
  <filter-item type="base" label="最近到院日期" :text="text" :width="320" @onClick="show()">
    <date-range v-model="arriveTime" v-model:text="text" v-model:visible="visible" />
  </filter-item>
</template>
<script setup>
import { computed, ref } from 'vue';
import dayjs from 'dayjs'
import dateRange from './date-range.vue'
import FilterItem from './filter-item.vue';

const arriveTime = ref([]);
const visible = ref(false)
const text = ref('');

function show() {
  visible.value = true
}

function reset() { arriveTime.value = [] }

function getParam() {
  const params = {};
  if (arriveTime.value[0] && dayjs(arriveTime.value[0]).isValid()) {
    params.startArriveTime = arriveTime.value[0];
  }
  if (arriveTime.value[1] && dayjs(arriveTime.value[1]).isValid()) {
    params.endArriveTime = arriveTime.value[1];
  }
  return params
}

defineExpose({
  getParam,
  reset
})
</script>
<style lang="scss" scoped></style>
