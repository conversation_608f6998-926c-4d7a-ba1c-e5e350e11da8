<template>
  <el-form-item class="px-15px py-6px el-form-item--autoheight" :class="required ? 'is-required' : ''" :label="name"
    border-bottom>
    <cell-wwuser :form="form" :title="title"/>
  </el-form-item>
</template>
<script setup>
import { computed } from 'vue';
import CellWwuser from '../display/cell-wwuser.vue'

const emits = defineEmits(['change'])
const props = defineProps({
  form: { type: Object, default: () => ({}) },
  name: { type: String, default: '' },
  required: { type: Boolean, default: false },
  title: { type: String, default: '' }
})

</script>
<style lang="scss" scoped></style>
