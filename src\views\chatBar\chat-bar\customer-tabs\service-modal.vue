<template>
  <el-dialog :model-value="visible" :width="width" :title="props.record._id ? '编辑服务记录' : '新增服务记录'" @close="close">
    <el-scrollbar wrap-style="max-height:50vh">
      <el-form label-position="top">
        <el-form-item class="is-required" label="执行日期">
          <el-date-picker v-model="workDate" format="YYYY-MM-DD" placeholder="请选择执行日期" type="date" value-format="YYYY-MM-DD" :disabled-date="judgeDisabled" style="width: 100%" :disabled="true" />
        </el-form-item>
        <el-form-item class="is-required" label="执行时间">
          <el-time-picker v-model="workTime" format="HH:mm" placeholder="请选择执行时间" value-format="HH:mm" style="width: 100%" :disabled="true" />
        </el-form-item>
        <el-form-item class="is-required" label="服务内容">
          <el-input v-model="taskContent" maxlength="100" :autosize="{ minRows: 6, maxRows: 12 }" resize="none" show-word-limit type="textarea" placeholder="请输入服务内容" />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="text-center">
        <el-button class="w-100px" :loading="loading" plain @click="close()">取消</el-button>
        <el-button class="w-100px" :loading="loading" type="primary" @click="save()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref, toRefs, watch } from "vue";
import { ElMessage } from "element-plus";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import { updateServiceRecord } from "@/api/todo";
import { memberStore } from "@/store/member";

const props = defineProps({
  width: { type: Number },
  visible: { type: Boolean, default: false },
  record: { type: Object, default: () => ({}) },
  customer: { type: Object, default: () => ({}) },
});
const { width, visible } = toRefs(props);
const emits = defineEmits(["change", "close"]);
const workDate = ref("");
const workTime = ref("");
const taskContent = ref("");
const loading = ref(false);
function close() {
  emits("close");
}

const { memberInfo } = storeToRefs(memberStore());
async function save() {
  if (loading.value) return;
  loading.value = true;
  if (workDate.value.trim() === "") {
    ElMessage.info("请选择执行日期");
  } else if (workTime.value.trim() === "") {
    ElMessage.info("请选择执行时间");
  } else if (taskContent.value.trim() === "") {
    ElMessage.info("请输入服务内容");
  } else {
    const params = {
      taskContent: taskContent.value,
      // executionTime: dayjs(`${workDate.value}  ${workTime.value}`).valueOf(),
    };
    const { success, message } = await updateServiceRecord(props.record._id, params);
    if (success) {
      emits("change");
      emits("close");
      ElMessage.success(message || (props.record._id ? "修改成功" : "新增成功"));
    } else {
      ElMessage.error(message || (props.record._id ? "修改失败" : "新增失败"));
    }
  }
  loading.value = false;
}

watch(
  () => props.visible,
  (n) => {
    if (n) {
      workDate.value = props.record.executionTime ? dayjs(props.record.executionTime).format("YYYY-MM-DD") : "";
      workTime.value = props.record.executionTime ? dayjs(props.record.executionTime).format("HH:mm") : "";
      taskContent.value = `${props.record.taskContent}(${props.record.result ? "处理结果" + props.record.result : ""})`;
    }
  }
);
</script>
<style lang="scss" scoped>
.tag-item {
  border: 1px solid #eee;
  margin-bottom: 12px;
}

.tag-group {
  margin-bottom: -12px;
}

.tag-item + .tag-item {
  margin-left: 12px;
}

.tag-item.active {
  background-color: var(--yc-primary-color);
  border-color: var(--yc-primary-color);
  color: #fff;
}
</style>