<template>
  <my-layout bg-fff>
    <layout-main>
      <div p-15>
        <el-form label-position="top">
          <el-form-item class="is-required" label="执行日期">
            <el-date-picker v-model="workDate" format="YYYY-MM-DD" placeholder="请选择执行日期" type="date" value-format="YYYY-MM-DD" style="width: 100%" :disabled="true" />
          </el-form-item>
          <el-form-item class="is-required" label="执行时间">
            <el-time-picker v-model="workTime" format="HH:mm" placeholder="请选择执行时间" value-format="HH:mm" style="width: 100%" />
          </el-form-item>
          <el-form-item class="is-required" label="服务类型">
            <el-select v-model="form.eventType" filterable placeholder="请选择服务类型" class="w-400px">
              <el-option v-for="item in serviceTypeList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item class="is-required" label="所属团队">
            <el-select v-model="form.executeTeamId" filterable style="width: 100%" placeholder="请选择所属团队" @change="changeTeam">
              <el-option v-for="item in teams" :key="item.teamId" :label="item.name" :value="item.teamId" />
            </el-select>
          </el-form-item>
          <el-form-item class="is-required" label="服务内容">
            <el-input v-model="form.taskContent" maxlength="100" :autosize="{ minRows: 8, maxRows: 12 }" resize="none" show-word-limit type="textarea" placeholder="请输入服务内容" />
          </el-form-item>
        </el-form>
      </div>
    </layout-main>
    <layout-item>
      <div class="py-12px px-15px text-center flex" common-shadow--r>
        <el-button class="w-100px flex-grow" plain @click="close()">取消</el-button>
        <el-button class="w-100px flex-grow" type="primary" :loading="loading" @click="confirm()">确定</el-button>
      </div>
    </layout-item>
  </my-layout>
</template>
<script setup>
import { computed, inject, onMounted, ref, toRefs } from "vue";
import { ElMessage } from "element-plus";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import { addServiceRecord } from "@/api/todo";
import { memberStore } from "@/store/member";
import { ToDoEventType } from "@/baseData";

import MyLayout, { LayoutMain } from "@/components/layout";
const serviceTypeList = Object.keys(ToDoEventType).map((key) => ({ label: ToDoEventType[key], value: key }));
const props = defineProps({
  customer: { type: Object, default: () => ({}) },
  teams: { type: Array, default: () => [] },
});
const emits = defineEmits(["close"]);

const trigger = inject("side-bar-event-trigger");

const { customer, teams } = toRefs(props);
const { memberInfo } = storeToRefs(memberStore());

const form = ref({});
const workDate = ref(dayjs().format("YYYY-MM-DD"));
const workTime = ref("");
const team = computed(() => {
  return teams.value.find((i) => i.teamId === form.value.executeTeamId);
});

function close() {
  emits("close");
}

const loading = ref(false);
async function confirm() {
  if (loading.value) return;
  loading.value = true;
  if (workDate.value.trim() === "") {
    ElMessage.info("请选择执行日期");
  } else if (workTime.value.trim() === "") {
    ElMessage.info("请选择执行时间");
  } else if (!team.value) {
    ElMessage.info("请选择所属团队");
  } else if (typeof form.value.taskContent !== "string" || form.value.taskContent.trim() === "") {
    ElMessage.info("请输入服务内容");
  } else {
    const params = {
      taskContent: form.value.taskContent,
      executionTime: dayjs(`${workDate.value}  ${workTime.value}`).valueOf(),
      creatorUserId: memberInfo.value.userid,
      executorUserId: memberInfo.value.userid,
      corpId: memberInfo.value.corpId,
      customerId: customer.value._id,
      customerName: customer.value.name,
      customerUserId: customer.value.externalUserId,
      eventType: form.value.eventType,
      teamName: team.value.name,
      executeTeamId: team.value.teamId,
    };
    const { success, message } = await addServiceRecord(params);
    if (success) {
      trigger.$invoke("update-customer-latest-service-time", { _id: params.customerId, time: params.executionTime });
      setTimeout(() => trigger.$invoke("update-service-record"), 1000);
      emits("close");
      ElMessage.success(message || "新增成功");
    } else {
      ElMessage.error(message || "新增失败");
    }
  }
  loading.value = false;
}
onMounted(() => {
  if (teams.value.length === 1) {
    form.value.executeTeamId = teams.value[0].teamId;
  }
});
</script>
<style lang="scss" scoped></style>
