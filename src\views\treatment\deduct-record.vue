<template>
  <el-drawer :model-value="props.visible" :width="width" size="50%" title="治疗记录" style="--el-drawer-padding-primary: 0" @close="close()">
    <el-scrollbar>
      <el-form class="p-15px">
        <el-row>
          <el-col :span="12">
            <el-form-item class="mb-15px" label="治疗项目:" label-width="120">
              {{ form.projectName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item class="mb-15px" label="治疗数:" label-width="120">
              {{ form.deductUsageCount }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item class="mb-15px" label="治疗科室:" label-width="120">
              {{ form.treatmentDeptName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item class="mb-15px" label="治疗医生:" label-width="120">
              <ww-user :openid="form.treatmentDoctorUserId" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item class="mb-15px" label="划扣方式:" label-width="120">
              {{ form.deductType }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item class="mb-15px" label="划扣人:" label-width="120">
              <ww-user :openid="form.deductOperator" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item class="mb-15px" label="治疗时间:" label-width="120">
              <div>
                {{ form.treatmentTime ? dayjs(form.treatmentTime).format("YYYY-MM-DD") : "" }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item class="mb-15px" label="下次治疗时间:" label-width="120">
              <el-date-picker v-model="form.nextTreatmentTime" placeholder="请选择下次治疗时间" @change="selectNextTreatmentTime" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item class="mb-15px" label="配台:" label-width="120">
          <div v-if="operateType === 'read'" class="flex">
            <div v-for="(userId, index) in form.assistantDoctors" :key="userId" :label="userId" :value="userId">
              <ww-user :openid="userId"></ww-user>
              <span v-if="form.assistantDoctors.length - 1 !== index">、</span>
            </div>
          </div>
          <el-select v-else v-model="form.assistantDoctors" multiple class="w-full el-select--hiddenValue" placeholder="请选择配台" @change="editDoctors">
            <template #prefix>
              <div class="h-30px flex" color-666>
                <div v-for="(i, index) in form.assistantDoctors">
                  <ww-user :openid="i"></ww-user>
                  <span v-if="form.assistantDoctors.length - 1 !== index">、</span>
                </div>
              </div>
            </template>
            <el-option-group label="">
              <el-option v-for="userId in deptDoctors" :key="userId" :label="userId" :value="userId">
                <ww-user :openid="userId"></ww-user>
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item class="mb-15px" label="治疗备注:" label-width="120">
          <div v-if="operateType === 'read'">{{ form.treatmentRemark }}</div>
          <el-input v-else type="textarea" v-model="form.treatmentRemark" :autosize="{ minRows: 5, maxRows: 8 }" resize="none" :maxlength="2000" show-word-limit placeholder="请输入治疗备注内容" @input="editTreatmentRemark" class="custom-remark-input" />
        </el-form-item>
        <el-form-item class="mb-15px" label="知情同意书:" label-width="120">
          <div class="image-upload-group">
            <div class="button-row">
              <el-upload multiple action="#" accept="image/*" :show-file-list="false" list-type="picture" :auto-upload="false" @change="changeConsentFile" v-if="operateType !== 'read'">
                <template #trigger>
                  <el-button size="small" type="primary">本地上传</el-button>
                </template>
              </el-upload>
              <el-button size="small" class="ml-10px" v-if="operateType !== 'read'" @click="openThirdPartySelector('consent')">三方导入</el-button>
            </div>
            <div class="image-list-wrapper">
              <template v-if="consentImageList.length === 0">
                <div class="empty-image">
                  <el-icon class="empty-icon"><Picture /></el-icon>
                  <div class="empty-text">无图片</div>
                </div>
              </template>
              <template v-else>
                <div v-for="(image, idx) in consentImageList" :key="image.key" v-loading="uploadLoading[image.key]" class="image-card" element-loading-text="正在上传">
                  <img class="image-preview" :src="image.href || image.url" :alt="image.name" @click="previewImage(image.href || image.url)" />
                  <div class="image-name" :class="image.error ? 'text-gray-500 line-through' : ''" :title="image.name">{{ image.name }}</div>
                  <div v-if="image.error" class="image-error">上传失败，请重试</div>
                  <el-icon class="image-remove" @click="removeConsentImage(idx)" v-if="operateType !== 'read'">
                    <CircleCloseFilled />
                  </el-icon>
                </div>
              </template>
            </div>
          </div>
        </el-form-item>
        <el-form-item class="mb-15px" label="治疗前:" label-width="120">
          <div class="image-upload-group">
            <div class="button-row">
              <el-upload multiple action="#" accept="image/*" :show-file-list="false" list-type="picture" :auto-upload="false" @change="changeBeforeFile" v-if="operateType !== 'read'">
                <template #trigger>
                  <el-button size="small" type="primary">本地上传</el-button>
                </template>
              </el-upload>
              <el-button size="small" class="ml-10px" v-if="operateType !== 'read'" @click="openThirdPartySelector('before')">三方导入</el-button>
            </div>
            <div class="image-list-wrapper">
              <template v-if="beforeImageList.length === 0">
                <div class="empty-image">
                  <el-icon class="empty-icon"><Picture /></el-icon>
                  <div class="empty-text">无图片</div>
                </div>
              </template>
              <template v-else>
                <div v-for="(image, idx) in beforeImageList" :key="image.key" v-loading="uploadLoading[image.key]" class="image-card" element-loading-text="正在上传">
                  <img class="image-preview" :src="image.href || image.url" :alt="image.name" @click="previewImage(image.href || image.url)" />
                  <div class="image-name" :class="image.error ? 'text-gray-500 line-through' : ''" :title="image.name">{{ image.name }}</div>
                  <div v-if="image.error" class="image-error">上传失败，请重试</div>
                  <el-icon class="image-remove" @click="removeBeforeImage(idx)" v-if="operateType !== 'read'">
                    <CircleCloseFilled />
                  </el-icon>
                </div>
              </template>
            </div>
          </div>
        </el-form-item>
        <el-form-item class="mb-15px" label="治疗中:" label-width="120">
          <div class="image-upload-group">
            <div class="button-row">
              <el-upload multiple action="#" accept="image/*" :show-file-list="false" list-type="picture" :auto-upload="false" @change="changeDuringFile" v-if="operateType !== 'read'">
                <template #trigger>
                  <el-button size="small" type="primary">本地上传</el-button>
                </template>
              </el-upload>
              <el-button size="small" class="ml-10px" v-if="operateType !== 'read'" @click="openThirdPartySelector('during')">三方导入</el-button>
            </div>
            <div class="image-list-wrapper">
              <template v-if="duringImageList.length === 0">
                <div class="empty-image">
                  <el-icon class="empty-icon"><Picture /></el-icon>
                  <div class="empty-text">无图片</div>
                </div>
              </template>
              <template v-else>
                <div v-for="(image, idx) in duringImageList" :key="image.key" v-loading="uploadLoading[image.key]" class="image-card" element-loading-text="正在上传">
                  <img class="image-preview" :src="image.href || image.url" :alt="image.name" @click="previewImage(image.href || image.url)" />
                  <div class="image-name" :class="image.error ? 'text-gray-500 line-through' : ''" :title="image.name">{{ image.name }}</div>
                  <div v-if="image.error" class="image-error">上传失败，请重试</div>
                  <el-icon class="image-remove" @click="removeDuringImage(idx)" v-if="operateType !== 'read'">
                    <CircleCloseFilled />
                  </el-icon>
                </div>
              </template>
            </div>
          </div>
        </el-form-item>
        <el-form-item class="mb-15px" label="治疗后:" label-width="120">
          <div class="image-upload-group">
            <div class="button-row">
              <el-upload multiple action="#" accept="image/*" :show-file-list="false" list-type="picture" :auto-upload="false" @change="changeAfterFile" v-if="operateType !== 'read'">
                <template #trigger>
                  <el-button size="small" type="primary">本地上传</el-button>
                </template>
              </el-upload>
              <el-button size="small" class="ml-10px" v-if="operateType !== 'read'" @click="openThirdPartySelector('after')">三方导入</el-button>
            </div>
            <div class="image-list-wrapper">
              <template v-if="afterImageList.length === 0">
                <div class="empty-image">
                  <el-icon class="empty-icon"><Picture /></el-icon>
                  <div class="empty-text">无图片</div>
                </div>
              </template>
              <template v-else>
                <div v-for="(image, idx) in afterImageList" :key="image.key" v-loading="uploadLoading[image.key]" class="image-card" element-loading-text="正在上传">
                  <img class="image-preview" :src="image.href || image.url" :alt="image.name" @click="previewImage(image.href || image.url)" />
                  <div class="image-name" :class="image.error ? 'text-gray-500 line-through' : ''" :title="image.name">{{ image.name }}</div>
                  <div v-if="image.error" class="image-error">上传失败，请重试</div>
                  <el-icon class="image-remove" @click="removeAfterImage(idx)" v-if="operateType !== 'read'">
                    <CircleCloseFilled />
                  </el-icon>
                </div>
              </template>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div text-center common-shadow--r class="z-1 relative py-10px" v-if="operateType !== 'read'">
        <el-button plain class="w-100px" type="primary" @click="close()">取消</el-button>
        <el-button class="w-100px" :loading="loading" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-drawer>
  <el-image-viewer v-if="showViewer" :url-list="previewList" @close="cancelPreview" />
  <ThirdPartyPhotoSelector v-model="thirdPartyVisible" :customer-name="form?.customerName" :customerId="form?.customerId"
    @confirm="handleThirdPartyConfirm" />
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import { ElMessage } from "element-plus";
import { updateDeductRecord } from "@/api/treatment-record";
import { updateFile } from "@/api/uploadFIle.js";
import { staffStore } from "@/store/staff";
import wwUser from "@/components/ww-user/index.vue";
import ThirdPartyPhotoSelector from "@/components/third-party-photo-selector.vue";

const emits = defineEmits(["close", "success"]);
const props = defineProps({
  project: { type: Object, default: () => ({}) },
  visible: { type: Boolean, default: false },
  width: { type: Number, default: 600 },
  operateType: { type: String, default: "" },
});
const { staffList } = storeToRefs(staffStore());
const form = ref({});
const editForm = ref({});
const previewList = ref([]);
const consentImageList = ref([]); // 知情同意书照片
const beforeImageList = ref([]); // 治疗前照片
const duringImageList = ref([]); // 治疗中照片
const afterImageList = ref([]); // 治疗后照片
const uploadLoading = ref({});
const showViewer = ref(false);
const thirdPartyVisible = ref(false);
const currentPhotoType = ref('');
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const TARGET_FILE_SIZE = 1 * 1024 * 1024; // 1MB

function changeConsentFile({ raw: file, name, uid: key, url }) {
  if (file.size > MAX_FILE_SIZE) {
    ElMessage.error(`图片大小不能超过10MB，当前大小: ${(file.size / (1024 * 1024)).toFixed(2)}MB`);
    return;
  }
  consentImageList.value.push({ name, key, url, file });
}

function changeBeforeFile({ raw: file, name, uid: key, url }) {
  if (file.size > MAX_FILE_SIZE) {
    ElMessage.error(`图片大小不能超过10MB，当前大小: ${(file.size / (1024 * 1024)).toFixed(2)}MB`);
    return;
  }
  beforeImageList.value.push({ name, key, url, file });
}

function changeDuringFile({ raw: file, name, uid: key, url }) {
  if (file.size > MAX_FILE_SIZE) {
    ElMessage.error(`图片大小不能超过10MB，当前大小: ${(file.size / (1024 * 1024)).toFixed(2)}MB`);
    return;
  }
  duringImageList.value.push({ name, key, url, file });
}

function changeAfterFile({ raw: file, name, uid: key, url }) {
  if (file.size > MAX_FILE_SIZE) {
    ElMessage.error(`图片大小不能超过10MB，当前大小: ${(file.size / (1024 * 1024)).toFixed(2)}MB`);
    return;
  }
  afterImageList.value.push({ name, key, url, file });
}

function removeConsentImage(idx) {
  consentImageList.value.splice(idx, 1);
}

function removeBeforeImage(idx) {
  beforeImageList.value.splice(idx, 1);
}

function removeDuringImage(idx) {
  duringImageList.value.splice(idx, 1);
}

function removeAfterImage(idx) {
  afterImageList.value.splice(idx, 1);
}

function changeFile({ raw: file, name, uid: key, url }) {
  if (file.size > MAX_FILE_SIZE) {
    ElMessage.error(`图片大小不能超过10MB，当前大小: ${(file.size / (1024 * 1024)).toFixed(2)}MB`);
    return;
  }
  imageList.value.push({ name, key, url, file });
}

function compressImage(file, maxSize = TARGET_FILE_SIZE) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = (event) => {
      const img = new Image();
      img.src = event.target.result;
      img.onload = () => {
        let quality = 0.8;
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0, img.width, img.height);
        function compressWithQuality(q) {
          const dataUrl = canvas.toDataURL('image/jpeg', q);
          const binaryData = atob(dataUrl.split(',')[1]);
          const arrayBuffer = new ArrayBuffer(binaryData.length);
          const uint8Array = new Uint8Array(arrayBuffer);
          for (let i = 0; i < binaryData.length; i++) {
            uint8Array[i] = binaryData.charCodeAt(i);
          }
          const blob = new Blob([arrayBuffer], { type: 'image/jpeg' });
          if (blob.size > maxSize && q > 0.1) {
            return compressWithQuality(q - 0.1);
          } else {
            return new File([blob], file.name, { type: 'image/jpeg' });
          }
        }
        const result = compressWithQuality(quality);
        resolve(result);
      };
      img.onerror = (error) => reject(error);
    };
    reader.onerror = (error) => reject(error);
  });
}

async function uploadImages() {
  await Promise.all([
    uploadImagesByType(consentImageList.value, 'consent'),
    uploadImagesByType(beforeImageList.value, 'before'),
    uploadImagesByType(duringImageList.value, 'during'),
    uploadImagesByType(afterImageList.value, 'after')
  ]);
}

async function uploadImagesByType(imageList, type) {
  const m = new Map();
  for (let item of imageList) {
    if (item.file) {
      uploadLoading.value[item.key] = true;
      try {
        const compressedFile = await compressImage(item.file);
        const res = await updateFile(
          `${type}_${+new Date()}_${Math.ceil(Math.random() * 100000)}_${item.name}`, 
          compressedFile, 
          localStorage.getItem("corpId")
        );
        if (res.download_url) {
          m.set(item.key, { name: item.name, href: res.download_url, key: item.key });
        } else {
          m.set(item.key, { ...item, error: true });
        }
      } catch (e) {
        console.error('上传或压缩图片出错:', e);
        m.set(item.key, { ...item, error: true });
      }
      uploadLoading.value[item.key] = false;
    } else {
      m.set(item.key, { name: item.name, href: item.href, key: item.key });
    }
  }
  for (let i = 0; i < imageList.length; i++) {
    const item = imageList[i];
    imageList[i] = m.get(item.key);
  }
}

function removeImage(idx) {
  imageList.value.splice(idx, 1);
}

function previewImage(url) {
  showViewer.value = true;
  previewList.value = [url];
}
function cancelPreview() {
  showViewer.value = false;
}

function close() {
  emits("close");
}
const loading = ref(false);
async function confirm() {
  loading.value = true;
  await uploadImages();
  if ([...consentImageList.value, ...beforeImageList.value, ...duringImageList.value, ...afterImageList.value]
      .some((item) => item.error || item.file)) {
    loading.value = false;
    return;
  }
  const query = {
    id: props.project._id,
    params: {
      ...editForm.value,
      consentPics: consentImageList.value.map((i) => ({ name: i.name, href: i.href })),
      pics: beforeImageList.value.map((i) => ({ name: i.name, href: i.href })),
      duringPics: duringImageList.value.map((i) => ({ name: i.name, href: i.href })),
      afterPics: afterImageList.value.map((i) => ({ name: i.name, href: i.href })),
    },
  };
  let { success } = await updateDeductRecord(query);
  if (success) {
    ElMessage.success("更新成功");
    emits("success");
    emits("close");
  }
  loading.value = false;
}
const deptDoctors = computed(() => {
  return getDoctors(props.project);
});

function getDoctors(project) {
  if (!props.project.treatmentDept_id) return [];
  let userIds =
    staffList.value && Array.isArray(staffList.value)
      ? staffList.value
          .filter((item) => {
            if (item.deptIds && Array.isArray(item.deptIds)) {
              return item.deptIds.includes(project.treatmentDept_id);
            } else {
              return false;
            }
          })
          .map((item) => {
            return item.userid;
          })
      : [];
  return userIds;
}

function editDoctors(value) {
  editForm.value.assistantDoctors = value;
  console.log(editForm.value);
}

function selectNextTreatmentTime(value) {
  editForm.value.nextTreatmentTime = value;
}

function editTreatmentRemark(value) {
  editForm.value.treatmentRemark = value;
  console.log(editForm.value);
}

function editTreatementTime(value) {
  editForm.value.treatmentTime = value;
}

function openThirdPartySelector(type) {
  currentPhotoType.value = type;
  thirdPartyVisible.value = true;
}

// 处理三方照片选择确认
function handleThirdPartyConfirm(selectedPhotos) {
  if (selectedPhotos && selectedPhotos.length > 0) {
    selectedPhotos.forEach((photo, idx) => {
      const imageData = {
        name: photo.name,
        key: `${Date.now()}_${Math.floor(Math.random() * 10000)}_${idx}`,
        href: photo.url,
        url: photo.url
      };
      switch (currentPhotoType.value) {
        case 'consent':
          consentImageList.value.push(imageData);
          break;
        case 'before':
          beforeImageList.value.push(imageData);
          break;
        case 'during':
          duringImageList.value.push(imageData);
          break;
        case 'after':
          afterImageList.value.push(imageData);
          break;
      }
    });
  }
}


watch(
  () => props.visible,
  (n) => {
    if (n) {
      form.value = { ...props.project };
      console.log(form.value);
      editForm.value = {};
      consentImageList.value = Array.isArray(props.project.consentPics)
        ? props.project.consentPics.map((i, idx) => ({
            name: i.name,
            href: i.href,
            key: `consent_${Date.now()}_${idx}`,
          }))
        : [];
      beforeImageList.value = Array.isArray(props.project.pics)
        ? props.project.pics.map((i, idx) => ({
            name: i.name,
            href: i.href,
            key: `before_${Date.now()}_${idx}`,
          }))
        : [];
      duringImageList.value = Array.isArray(props.project.duringPics)
        ? props.project.duringPics.map((i, idx) => ({
            name: i.name,
            href: i.href,
            key: `during_${Date.now()}_${idx}`,
          }))
        : [];
      afterImageList.value = Array.isArray(props.project.afterPics)
        ? props.project.afterPics.map((i, idx) => ({
            name: i.name,
            href: i.href,
            key: `after_${Date.now()}_${idx}`,
          }))
        : [];
    }
  }
);
</script>
<style lang="scss">
.el-select.el-select--hiddenValue .el-select__tags > span {
  display: none;
}

.el-select.el-select--hiddenValue .el-input__prefix {
  flex-grow: 1;
}

.custom-remark-input textarea {
  background: transparent;
  border-radius: 8px;
  min-height: 120px;
  font-size: 15px;
  color: #333;
}
:deep(.el-input__count) {
  right: 12px !important;
  bottom: 8px !important;
  color: #b0b0b0 !important;
  font-size: 13px !important;
}
.image-upload-group {
  background: #fff;
  border: 1px solid #e5e6eb;
  border-radius: 8px;
  margin: 10px 0 0 0;
  padding: 16px 20px 10px 20px;
  width: 100%;
}
.button-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.image-list-wrapper {
  min-height: 120px;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  background: #fafbfc;
  border-radius: 8px;
  padding: 16px 0 6px 0;
  border: 1px dashed #e5e6eb;
  margin-bottom: 0;
  min-width: 100px;
}
.empty-image {
  width: 100%;
  min-height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #b0b0b0;
  font-size: 15px;
}
.empty-icon {
  font-size: 36px;
  margin-bottom: 6px;
}
.empty-text {
  font-size: 14px;
  color: #b0b0b0;
}
.image-card {
  position: relative;
  width: 160px;
  margin-right: 10px;
  margin-bottom: 10px;
  text-align: center;
  border-radius: 8px;
  box-shadow: 0 2px 8px 0 #e5e6eb;
  background: #fff;
  overflow: hidden;
  transition: box-shadow 0.2s, transform 0.2s;
}
.image-card:hover {
  box-shadow: 0 4px 16px 0 #d0d1d6;
  transform: translateY(-2px) scale(1.02);
}
.image-preview {
  width: 100%;
  height: 100px;
  object-fit: cover;
  border-radius: 8px 8px 0 0;
  cursor: pointer;
}
.image-name {
  font-size: 14px;
  margin-top: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.image-error {
  font-size: 12px;
  color: #f56c6c;
  margin-top: 10px;
}
.image-remove {
  position: absolute;
  right: 6px;
  top: 6px;
  background: #fff;
  border-radius: 50%;
  color: #f56c6c;
  cursor: pointer;
  font-size: 18px;
  box-shadow: 0 2px 8px 0 #e5e6eb;
  z-index: 2;
}
</style>