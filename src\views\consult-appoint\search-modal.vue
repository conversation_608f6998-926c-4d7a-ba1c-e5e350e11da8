<template>
  <el-dialog :model-value="visible" title="搜索客户" draggable :width="width" @close="cancel">
    <div v-loading="loading">
      <div class="box-content">
        <div p-15 border-bottom flex>
          <el-input v-model="filterName" class="mr-10px" flex-grow placeholder="请输入客户姓名、身份证号、手机号查询、his登记号" @blur="searchBlur" @keyup.enter="search" clearable></el-input>
          <el-button flex-shrink-0 type="primary" @click="search">查询</el-button>
        </div>
        <div v-if="list.length > 0">
          <div class="box-cell" v-for="item in list" :key="item.idCard">
            <div class="box-cell-name">
              <div class="flex algin-center">
                <div class="pr-10px t_1">{{ item.name }}</div>
                <div class="t_2">
                  {{ item.age }}
                  <span v-if="item.age && item.sex">/</span>
                  {{ item.sex }}
                </div>
              </div>
              <div class="mt-5px flex">
                <div class="w-80px">身份证号:</div>
                <div class="pl-5px">{{ item.idCard }}</div>
              </div>
              <div class="mt-5px flex">
                <div class="w-80px">手机号:</div>
                <div class="pl-5px">{{ item.mobile }}</div>
              </div>
              <div class="mt-5px flex" v-if="item.phone1">
                <div class="w-80px">联系电话1:</div>
                <div class="pl-5px">{{ item.phone1 }}</div>
              </div>
              <div class="mt-5px flex" v-if="item.phone2">
                <div class="w-80px">联系电话2:</div>
                <div class="pl-5px">{{ item.phone2 }}</div>
              </div>
              <div class="mt-5px flex" v-if="item.phone3">
                <div class="w-80px">联系电话3:</div>
                <div class="pl-5px">{{ item.phone3 }}</div>
              </div>
              <div class="mt-5px flex" v-if="item.customerNumber">
                <div class="w-80px">门诊ID号:</div>
                <div class="pl-5px">{{ item.customerNumber }}</div>
              </div>
              <div class="mt-5px" v-if="item && item.counselorRecord && Array.isArray(item.counselorRecord) && item.counselorRecord.length > 0">
                <span class="inline-block w-80px text-gray-500">所属咨询:</span>
                <ww-user :openid="item.counselorRecord[item.counselorRecord.length - 1]?.counselor"></ww-user>
              </div>
              <div class="mt-5px" v-if="item && item.introducerRecord && Array.isArray(item.introducerRecord) && item.introducerRecord?.length > 0">
                <span class="inline-block w-80px text-gray-500">所属开发:</span>
                <ww-user :openid="item.introducerRecord[item.introducerRecord.length - 1]?.introducer"></ww-user>
              </div>
            </div>
            <el-button type="primary" class="ml-10px" @click="keepCustomer(item)">获取</el-button>
          </div>
        </div>
        <empty-data v-else :top="20" :title="noDataTitle" padding="0 0 15px" text-top="10px" :image-width="120"></empty-data>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <div>
          未找到患者可以:
          <el-button type="primary" class="ml-10px" @click="addCustomer">新增患者</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref } from "vue";
import { getCustomers } from "@/api/member";
import EmptyData from "@/components/empty-data.vue";
import validate from "@/utils/validate";
import WwUser from "@/components/ww-user/index.vue";
const props = defineProps({
  visible: { type: Boolean, defautl: false },
  externalUserId: { type: String, default: "" },
  width: { type: Number, default: 520 },
});
const filterName = ref("");
const searchName = ref("");
const list = ref([]);
const loading = ref(false);
const emit = defineEmits(["close", "addCustomer", "success", "consult"]);
function defineData() {
  filterName.value = "";
  searchName.value = "";
  list.value = [];
}
function cancel() {
  defineData();
  emit("close");
}
async function search() {
  searchName.value = filterName.value.trim();
  if (!searchName.value) return;
  let query = {};
  if (validate.isMobile(searchName.value)) {
    query = {
      mobile: filterName.value,
    };
  } else if (validate.isChinaId(searchName.value)[0]) {
    query = {
      idCard: searchName.value,
    };
  } else if (isNumeric(searchName.value) && searchName.value.length === 10) {
    query = {
      customerNumber: searchName.value,
    };
  } else {
    query = {
      name: searchName.value,
    };
  }
  // 判断query 是否为空
  if (Object.keys(query).length === 0) return;
  loading.value = true;
  let { success, data } = await getCustomers(query);
  loading.value = false;
  list.value = success
    ? data.data.map((i) => {
        return {
          ...i,
          counselor: i.counselorRecord && Array.isArray(i.counselorRecord) ? i.counselorRecord[i.counselorRecord.length - 1]?.counselor : "",
        };
      })
    : [];
}
function isNumeric(str) {
  return /\d+$/.test(str);
}
function searchBlur() {
  if (!filterName.value) {
    searchName.value = "";
    list.value = [];
  }
}
const noDataTitle = computed(() => {
  return searchName.value ? `没有找到“${searchName.value}”在的建档信息` : "";
});
function addCustomer() {
  const mobile = searchName.value.trim();
  if (mobile && validate.isMobile(mobile)) {
    emit("addCustomer", { mobile });
  } else {
    emit("addCustomer");
  }
}
async function keepCustomer(item) {
  emit("consult", item);
}
</script>
<style scoped lang="scss">
.box-content {
  height: 300px;
  overflow-y: auto;
  position: relative;

  .box-cell {
    display: flex;
    justify-content: space-between;
    padding: 10px 0px 10px 20px;
    align-items: center;
    border-bottom: 1px solid #eee;

    .box-cell-name {
      .t_1 {
        color: #000;
        font-weight: 600;
        align-items: center;
      }

      .t_2 {
        font-size: 14px;
      }
    }

    .box-cell-radio {
      width: 18px;
      height: 18px;
      cursor: pointer;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}
</style>