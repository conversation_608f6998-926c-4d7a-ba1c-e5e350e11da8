<template>
  <tip-card class="mb-10px" :tips="tips" />
  <div v-if="templates.length > 1" class="mb-10px flex items-center">
    <el-radio-group v-model="templateType">
      <el-radio-button v-for="item in selfTemplates" :key="item.templateType" :value="item.templateType" :disabled="item.templateStatus === 'disable'" :label="item.templateType">{{ item.name }}</el-radio-button>
    </el-radio-group>
    <el-button class="w-100px ml-20px" type="primary" @click="edit()">修改</el-button>
  </div>
  <!-- <el-button class="w-100px" type="primary" @click="addTemplate()">新增模板</el-button> -->
  <template-table :name="selectTemplate.name" :key="selectTemplate.templateType" :templateType="selectTemplate.templateType" :list="selectTemplate.templateList" @onEidt="eidtField($event)" @onRemove="handleRemove()" />
  <modal-template :width="width" :visible="visible" @close="cancel()" @confirm="change()" />
  <health-template-edit :visible="editVisible" @close="cancel" :templates="selfTemplates" @onChangeTemplate="changeTemplate"></health-template-edit>
</template>
<script setup>
import { computed, inject, ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { templateStore } from "@/store/template";

import tipCard from "./tip-card.vue";
import healthTemplateEdit from "./health-template-edit.vue";
import modalTemplate from "./modal-template.vue";
import templateTable from "./template-table.vue";

const props = defineProps({
  name: {
    type: String,
    default: "",
  },
  templates: {
    type: Array,
    default: [],
  },
});

const store = templateStore();
const { getCorpTemplate } = store;
const { corpTemplate } = storeToRefs(store);
const tips = inject("tips");
const templateType = ref("");
const editVisible = ref(false);

const selfTemplates = computed(() =>
  props.templates.map((i) => {
    const template = corpTemplate.value.find((e) => e.templateType === i.templateType);
    return { ...i, templateStatus: template ? template.templateStatus : "enable" };
  })
);

const selectTemplate = computed(() => props.templates.find((e) => e.templateType === templateType.value) || {});

function edit() {
  editVisible.value = true;
}
function cancel() {
  editVisible.value = false;
}

async function changeTemplate() {
  editVisible.value = false;
  await getCorpTemplate();
  const temp = selfTemplates.value.find((i) => i.templateType === templateType.value) || selfTemplates.value[0];
  templateType.value = temp.templateType || "";
}

async function addTemplate() {
  visible.value = true;
}
//数组变化时，更新模板类型
watch(
  () => props.templates,
  (newItem) => {
    const templates = Array.isArray(newItem) ? newItem : [];
    const temp = templates.find((i) => i.templateType === templateType.value) || templates[0];
    templateType.value = temp ? temp.templateType : "";
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped></style>
