import { computed, ref } from 'vue';
import { useDebounceFn } from '@vueuse/core';

/**
 * 封装element-plus scrollbar 滚动事件
 * @param {Ref} scrollRef 引用的 el-scrollbar
 * @param {Number} gap 判定滚动到底部的距离
 * @returns 
 */
export default function useElScrollbar(scrollRef, gap = 20, duration = 300) {
  const atBottom = ref(false);
  const scrollTop = ref(0);
  const atTop = computed(() => scrollTop.value === 0);

  const handleScroll = useDebounceFn(function ({ scrollTop: top }) {
    scrollTop.value = top;
    if (!scrollRef || !scrollRef.value || !scrollRef.value.wrapRef) return;
    atBottom.value = scrollRef.value.wrapRef.scrollHeight - scrollRef.value.wrapRef.clientHeight - top <= gap;
  }, duration);


  return { atBottom, atTop, handleScroll, scrollTop }
}