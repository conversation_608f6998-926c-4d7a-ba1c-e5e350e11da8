<template>
  <el-dialog :model-value="visible" :width="400" @close="close">
    <template #header>授权/解除团队服务</template>
    <el-scrollbar wrap-style="max-height: calc(100vh - 360px)">
      <div v-for="item in allTeams" flex items-center px-15 pb-10 border-bottom>
        <div flex-grow>
          <div pt-10 flex items-center>
            <div mr-10>
              <img src="@/assets/check-active.png" class="w-18px h-18px pointer" v-if="isBelongTeam(item.teamId)" @click="removeTeam(item.teamId)" />
              <img @click="addTeamAction(item.teamId)" src="@/assets/check.png" class="w-18px h-18px pointer" v-else />
            </div>
            <div font-14 font-semibold>{{ item.name }}</div>
            <div color-primary v-if="isBelongTeam(item.teamId)">【授权该团队】</div>
          </div>
        </div>
      </div>
      <div v-if="allTeams.length === 0" text-center class="pt-40px pb-30px">
        <div class="pb-15px" font-16>暂无团队信息，请先创建团队</div>
      </div>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button @click="close()">取消</el-button>
        <el-button :loading="saveLoading" type="primary" @click="saveTeam()">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref, watch } from "vue";
import { updateMember } from "@/api/member";
import { getMemberIsTodoAndPlan as getMemberIsTodoAndPlanUrl } from "@/api/todo";
import { storeToRefs } from "pinia";
import { teamStore } from "@/store/team";
import { ElMessage, ElMessageBox } from "element-plus";
const store = teamStore();
const { allTeams } = storeToRefs(store);
const emit = defineEmits(["onSaveEditTeam", "onCloseEditTeam"]);
let props = defineProps({
  visible: { type: Boolean, default: false },
  teamIds: { type: [Array, String], default: "" },
  memberId: { type: String, default: "" },
});
const list = ref([]);
watch(
  () => props.teamIds,
  (newValue) => {
    if (newValue) {
      list.value = props.teamIds ? (typeof props.teamIds === "string" ? [props.teamIds] : props.teamIds) : [];
    }
  }
);
function isBelongTeam(teamId) {
  return list.value.some((i) => i === teamId);
}
function addTeamAction(teamId) {
  list.value.push(teamId);
}
async function removeTeam(teamId) {
  // 如果 teamIds 是字符串
  let flag = await getMemberIsTodoAndPlanAction(teamId);
  if (!flag) return;
  list.value = list.value.filter((team) => team !== teamId);
}

async function getMemberIsTodoAndPlanAction(teamId) {
  const { data, success, message } = await getMemberIsTodoAndPlanUrl(props.memberId, teamId);
  if (success) {
    const { toDoTotal, taskTotal } = data.data;
    if (toDoTotal > 0 || taskTotal > 0) {
      ElMessageBox.alert("该客户还有进行中的团队回访计划或待跟进事项，请处理完毕再解除客户与本团队的服务关系！", "提示", {
        type: "error",
      });
      return false;
    } else {
      return true;
    }
  } else {
    ElMessage.error(message);
    return false;
  }
}
async function saveTeam() {
  let { success, message } = await updateMember(props.memberId, {
    teamId: list.value,
  });
  if (success) {
    emit("onSaveEditTeam");
  } else {
    ElMessage.error(message);
  }
}
function close() {
  list.value = props.teamIds ? (typeof props.teamIds === "string" ? [props.teamIds] : props.teamIds) : [];
  emit("onCloseEditTeam");
}
</script>