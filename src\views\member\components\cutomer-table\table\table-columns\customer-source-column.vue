<template>
  <el-table-column label="来源" prop="source" :width="200">
    <template #default="{row}">
      <td-wrapper>{{ getSource(row) }}</td-wrapper>
    </template>
  </el-table-column>
</template>
<script setup>
import tdWrapper from './td-wrapper.vue';

function getSource(customer) {
  return customer && Array.isArray(customer.customerSource) && customer.customerSource.length ? customer.customerSource.join(' / ') : "";
}
</script>
