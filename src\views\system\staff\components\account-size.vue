<template>
  <div bg-fff common-shadow p-15 rounded-4>
    <div flex items-center justify-between mb-15>
      <div font-18 font-semibold class="leading-none">当前账户规模</div>
      <div flex items-center font-16>
        <div flex items-center pointer @click="chat">
          <el-icon color="#409EFF" class="mr-8px">
            <Service />
          </el-icon>
          <span>咨询</span>
        </div>
      </div>
    </div>
    <div flex items-center class="leading-none">
      <div flex class="items-center mr-30px">
        <div font-14>总账户数：</div>
        <div color-primary font-18 v-if="corpPackage">
          {{ corpPackage.accountCount + corpPackage.giveAccountCount }}
        </div>
      </div>
      <div flex class="items-center mr-30px">
        <div font-14>已使用账户数：</div>
        <div color-primary font-18>{{ corpMemberAndcustomtorCount.corpMemberCount }}</div>
      </div>
      <div flex class="items-center mr-30px">
        <div font-14>剩余可用账户数：</div>
        <div color-primary font-18 v-if="corpPackage">{{ reatCount || 0 }}</div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, computed } from "vue";
import { memberStore } from "@/store/member";
const { corpInfo } = memberStore();
const props = defineProps({
  corpMemberAndcustomtorCount: { type: Object, default: {} },
});
const corpPackage = ref(corpInfo.package);
const reatCount = computed(() => {
  return corpPackage.value.accountCount + corpPackage.value.giveAccountCount - props.corpMemberAndcustomtorCount.corpMemberCount;
});

function chat() {
  wx.invoke("openThirdAppServiceChat");
}
</script>
<style scoped>
.show-split-line::before {
  content: "";
  display: inline-block;
  margin: 2px 0;
  height: 1em;
  width: 1px;
  background: rgba(0, 0, 0, 0.9);
  margin: 0 10px;
}
</style>
