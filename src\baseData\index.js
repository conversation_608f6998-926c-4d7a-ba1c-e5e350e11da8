// 联系人
export const mobileRelation = [
  {
    value: "本人",
    label: "本人",
  },
  {
    value: "父母",
    label: "父母",
  },
  {
    value: "配偶",
    label: "配偶",
  },
  {
    value: "子女",
    label: "子女",
  },
  {
    value: "其他",
    label: "其他",
  },
];

// 推荐人
export const referenceType = [
  {
    value: "客户",
    label: "客户",
  },
  {
    value: "同事",
    label: "同事",
  },
];

export const ToDoEventType = {
  followUpNoShow: "未到院回访",
  followUpNoDeal: "未成交回访",
  followUp: "诊后回访",
  followUpPostSurgery: "术后回访",
  followUpPostTreatment: "治疗后回访",
  appointmentReminder: "就诊提醒",
  followUpReminder: "复诊提醒",
  medicationReminder: "用药提醒",
  serviceSummary: "咨询服务",
  eventNotification: "活动通知",
  ContentReminder: "宣教发送",
  questionnaire: "问卷调查",
  followUpComplaint: "投诉回访",
  followUpActivity: "活动回访",
  other: "其他",
};

//
export const ServiceType = {
  remindFiling: "新建档案",
  addVisitRecord: "预约登记",
  ContentReminder: "宣教发送",
  questionnaire: "问卷调查",
  customerUpdate: "更新客户档案",
  followUp: "就诊回访",
  serviceSummary: "咨询服务",
  visitRegistration: "就诊登记",
  followUpNoShow: "未到院回访",
  followUpNoDeal: "未成交回访",
  followUp: "诊后回访",
  followUpPostSurgery: "术后回访",
  followUpPostTreatment: "治疗后回访",
  appointmentReminder: "就诊提醒",
  followUpReminder: "复诊提醒",
  medicationReminder: "用药提醒",
  eventNotification: "活动通知",
  transferToSameTeam: "客户转移（团队内转）",
  transferToOtherTeam: "客户转移（转别的团队）",
  transferToCustomerPool: "客户转移（转公共客户池）",
  adminAllocateTeams: "管理员分配团队",
  adminRemoveTeams: "管理员解除团队",
  share: "客户共享",
  followUpComplaint: "投诉回访",
  followUpActivity: "活动回访",
  other: "其他",
};

// 待办状态
export const ToDoTaskStatus = [
  { value: "untreated", label: "待处理" },
  { value: "closed", label: "已取消" },
  { value: "treated", label: "已完成" },
  { value: "expire", label: "已过期" },
];

export const ToDoTaskStatusObj = {
  untreated: "待处理",
  closed: "已取消",
  treated: "已完成",
  expire: "已过期",
};

export const PlanTaskStatus = [
  { value: "untreated", label: "待处理" },
  { value: "closed", label: "已取消" },
  { value: "treated", label: "已完成" },
];

export const PlanTaskStatusObj = {
  untreated: "待处理",
  closed: "已取消",
  treated: "已完成",
};

export const ToDoTaskType = {
  pendding: "pendding",
  end: "end",
  common: "common",
  teamEnd: "teamEnd",
};
export const managementPlanStatusOption = [
  { value: "stop", label: "已结束" },
  { value: "executing", label: "执行中" },
  // { value: "complete", label: "完成" },
];

export const managementPlanStatus = {
  closed: "已结束",
  executing: "执行中",
  complete: "完成",
};

// 日程类型
export const WorkType = [
  { label: "新增日程", value: "schedule" },
  { label: "就诊登记", value: "visit-reg" },
];

export const WorkTypeEnum = WorkType.reduce((val, { label, value }) => {
  val[value] = label;
  val[label] = value;
  return val;
}, {});

export const WeekDay = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];

// 问卷状态
export const SurveryStatus = [
  { value: "enable", label: "已启用" },
  { value: "init", label: "未启用" },
  { value: "stop", label: "已停用" },
];

/**
 * 项目类型
 * 1、治疗 -- treatment
 * 2、手术 -- surgery
 * 3、药品 -- drug
 * 4、材料 -- material
 * 5、挂号 -- registration
 * 6、化验 -- assay
 */
// export const ProjectType = [
//   { value: "treatment", label: "治疗" },
//   { value: "surgery", label: "手术" },
//   { value: "drug", label: "药品" },
//   { value: "material", label: "材料" },
//   { value: "registration", label: "挂号" },
//   { value: "assay", label: "化验" },
//   { value: "other", label: "其他" },
// ];

export const ProjectType = [
  { label: "处置治疗", value: "处置治疗" },
  { label: "西药", value: "西药" },
  { label: "中成药", value: "中成药" },
  { label: "中草药", value: "中草药" },
  { label: "检查", value: "检查" },
  { label: "检验", value: "检验" },
  { label: "嘱托", value: "嘱托" },
  { label: "手术麻醉", value: "手术麻醉" },
  { label: "护理", value: "护理" },
  { label: "材料", value: "材料" },
  { label: "医嘱套", value: "医嘱套" },
  { label: "自备药", value: "自备药" },
  { label: "其他", value: "其他" },
];

export const ProjectTypeMap = ProjectType.reduce((val, item) => ((val[item.value] = item.label), val), {});

export const unitList = [
  { name: "次", value: "次" },
  { name: "针", value: "针" },
  { name: "瓶", value: "瓶" },
  { name: "疗程", value: "疗程" },
  { name: "部位", value: "部位" },
  { name: "支", value: "支" },
  { name: "例", value: "例" },
  { name: "套", value: "套" },
  { name: "cm", value: "cm" },
  { name: "cm/次", value: "cm/次" },
  { name: "cm2", value: "cm2" },
  { name: "mm/次", value: "mm/次" },
  { name: "侧", value: "侧" },
  { name: "次/cm2", value: "次/cm2" },
  { name: "次/部位", value: "次/部位" },
  { name: "点", value: "点" },
  { name: "点位", value: "点位" },
  { name: "盒", value: "盒" },
  { name: "季卡", value: "季卡" },
  { name: "颗", value: "颗" },
  { name: "厘米", value: "厘米" },
  { name: "年", value: "年" },
  { name: "年卡", value: "年卡" },
  { name: "片", value: "片" },
  { name: "套", value: "套" },
  { name: "月卡", value: "月卡" },
  { name: "组", value: "组" },
  { name: "次/单侧", value: "次/单侧" },
  { name: "单侧", value: "单侧" },
  { name: "个", value: "个" },
  { name: "根", value: "根" },
  { name: "块", value: "块" },
  { name: "粒", value: "粒" },
  { name: "裂口", value: "裂口" },
  { name: "每部位", value: "每部位" },
  { name: "每侧", value: "每侧" },
  { name: "每厘米", value: "每厘米" },
  { name: "每例", value: "每例" },
  { name: "每毛囊", value: "每毛囊" },
  { name: "每人次", value: "每人次" },
  { name: "人次", value: "人次" },
  { name: "双侧", value: "双侧" },
  { name: "天", value: "天" },
  { name: "一侧", value: "一侧" },
  { name: "3次", value: "3次" },
  { name: "100cm2", value: "100cm2" },
  { name: "包", value: "包" },
  { name: "平方厘米", value: "平方厘米" },
  { name: "全面部", value: "全面部" },
  { name: "条", value: "条" },
];

export const VisitStatus = [
  { value: "pending", label: "待就诊" },
  { value: "visited", label: "已就诊" },
];

export const VisitStatusObj = {
  pending: "待就诊",
  visited: "已就诊",
  canceled: "已取消",
  treated: "已治疗",
};

export const VisitStatusColor = {
  pending: "red",
  visited: "#67C23A",
  canceled: "#909399",
  treated: "#409EFF",
};

// 消费情况 未消费 pending 已消费 consumed 多次消费 canceled
export const ConsumeStatus = [
  { value: "notConsumed", label: "未消费" },
  { value: "consumed", label: "已消费" },
  { value: "moreConsumed", label: "多次消费" },
];
// 客户阶段 再消费 moreConsumed 复诊 returnVisit 初诊 firstVisit
export const ConsultStage = [
  { value: "firstVisit", label: "初诊" },
  { value: "returnVisit", label: "复诊" },
  { value: "moreConsumed", label: "再消费" },
];

// 待治疗 pending 已治疗 treated
export const TreatmentStatus = [
  { value: "init", label: "待治疗" },
  { value: "pending", label: "治疗中" },
  { value: "treated", label: "已治疗" },
];

export const TreatmentStatusColor = {
  pending: "#E6A23C",
  treated: "#67C23A",
};

export const DeductTreatmentStatus = [
  { value: "pending", label: "待治疗" },
  { value: "deducted", label: "已治疗" },
];

export const DeductTreatmentStatusColor = {
  pending: "#E6A23C",
  deducted: "#67C23A",
};

export const DeductStatus = [
  { value: "pending", label: "待划扣" },
  { value: "deducted", label: "已划扣" },
];
export const DeductStatusColor = {
  pending: "#909399",
  treated: "#409EFF",
};

export const TradeStatus = [
  { value: "untraded", label: "未成交" },
  { value: "traded", label: "已成交" },
];

export const ConsultStageMap = ConsultStage.reduce((val, item) => ((val[item.value] = item.label), val), {});

export const VisitType = [{ value: "consultation", label: "咨询" }];
export const VisitTypeMap = VisitType.reduce((val, item) => ((val[item.value] = item.label), val), {});

export const SurveryStatusMap = SurveryStatus.reduce((val, item) => ((val[item.value] = item.label), val), {});
export const SurveryStatusKey = SurveryStatus.reduce((val, item) => ((val[item.value] = item.value), val), {});

// 项目套餐组合类型
export const ProjectPackageType = [
  { label: "固定组合", value: "fixed" },
  { label: "自由组合", value: "free" },
];
export const ProjectPackageTypeMap = ProjectPackageType.reduce((val, item) => ((val[item.value] = item.label), val), {});

export const ReportRemarkTemp = `1、顾客主诉：

2、项目报价：

3、咨询情况：

4、推荐专家：

5、治疗顾虑：

6、治疗意向：

7、地理位置：

8、特殊来源：

9、其他备注：`;

export const CustomerComplaintTemp = `1、咨询项目：

2、咨询情况：

3、推荐医生：

4、开发项目：

5、其他：`;
