<template>
  <div class="addMember">
    <el-dialog v-model="addMemberVisible" title="新增客户" :width="720">
      <el-form :rules="rules" :model="ruleForm">
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="group-title">基础信息</div>
          </el-col>
          <el-col :push="1" :span="11" v-if="isShowCell('name')">
            <el-form-item label="客户姓名" label-width="100px" prop="name">
              <el-input placeholder="请输入客户姓名" v-model="ruleForm.name" />
            </el-form-item>
          </el-col>
          <el-col :push="1" :span="11" v-if="isShowCell('idCard')">
            <el-form-item label="身份证号" label-width="100px" prop="idCard">
              <el-input placeholder="请输入身份证号" v-model="ruleForm.idCard" @input="changeIdCard" />
            </el-form-item>
          </el-col>
          <el-col :push="1" :span="11" v-if="isShowCell('sex')">
            <el-form-item label="性别" label-width="100px" prop="sex">
              <el-radio-group v-model="ruleForm.sex">
                <el-radio label="男">男</el-radio>
                <el-radio label="女">女</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :push="1" :span="11" v-if="isShowCell('age')">
            <el-form-item label="年龄" label-width="100px" prop="age">
              <el-input placeholder="请输入客户年纪" v-model="ruleForm.age" />
            </el-form-item>
          </el-col>
          <el-col :push="1" :span="23" v-if="isShowCell('relationship')">
            <el-form-item class="el-form-item--autoHeight" label="微信联系人关系" label-width="100px" prop="relationship">
              <el-select class="w-full" v-model="ruleForm.relationship" placeholder="" clearable>
                <el-option v-for="item in mobileRelation" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :push="1" :span="23" v-if="isShowCell('mobile')">
            <el-form-item class="width-auto" label="联系电话" label-width="100px" prop="mobile">
              <el-row>
                <el-col :span="24">
                  <div class="mobileView" v-for="(item, index) in assistantByMobile">
                    <el-input class="item w-240" placeholder="请输入手机号" v-model="item.mobile" />
                    <el-select v-model="item.note" class="item" placeholder="备注" @change="handleChange" clearable style="width: 150px">
                      <el-option v-for="item in mobileRelation" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                    <el-button v-if="index === 0" class="item" type="primary" size="small" :icon="Plus" plain @click="addMobile">添加</el-button>
                    <el-button v-else type="danger" class="item" size="small" :icon="Delete" plain @click="deteleMobile(index)">删除</el-button>
                  </div>
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <div class="group-title">辅助信息</div>
          </el-col>
          <el-col :push="1" :span="23">
            <el-form-item class="w-650" label="备注" label-width="100px" prop="notes">
              <el-input type="textarea" v-model="ruleForm.notes" />
            </el-form-item>
          </el-col>
          <el-col :push="1" :span="23" v-if="isShowCell('tagIds')">
            <el-form-item class="width-auto" label="标签" label-width="100px" prop="tagList">
              <!-- <el-tag class="form-tag" v-for="tag in tagList" :key="tag" closable type="info"
                @close="handleTagClose(tag, 'tag')">
                {{ tag }}
              </el-tag> -->
              <el-tag v-for="(item, idx) in id2Tags" :key="idx" class="mr-6 linear-tag" type="primary" effect="dark">
                {{ item.options && item.options[0] ? item.options[0].name : "" }}
              </el-tag>
              <el-button class="item form-tag" type="primary" size="small" :icon="id2Tags.length ? EditPen : Plus" plain @click="addTags">{{ id2Tags.length ? "调整" : "添加" }}</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <div class="group-title">来源信息</div>
          </el-col>
          <el-col :push="1" :span="11" v-if="isShowCell('customerSource')">
            <el-form-item label="客户来源" label-width="100px" prop="customerSource">
              <el-cascader style="width: 100%" v-model="ruleForm.customerSource" :options="customerSources" @change="handleChange" :show-all-levels="false" placeholder="请选择客户来源" clearable />
            </el-form-item>
          </el-col>
          <el-col :push="1" :span="11" v-if="isShowCell('myRecommend')">
            <el-form-item label="推荐人" label-width="100px">
              <el-input placeholder="请输入推荐人" class="input-with-select" v-model="ruleForm.reference">
                <template #prepend>
                  <el-select placeholder="选择" style="width: 115px" v-model="ruleForm.referenceType">
                    <el-option v-for="option in referenceType" :label="option.label" :value="option.value" />
                  </el-select>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :push="1" :span="11" v-if="isShowCell('intentedProject')">
            <el-form-item label="意向项目" label-width="100px" prop="age">
              <el-select v-model="ruleForm.intentedProject" placeholder="请选择意向项目" @change="handleChange" clearable style="width: 400px">
                <el-option v-for="item in intenteds" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-col :span="24" v-if="Array.isArray(healthInfo) && healthInfo.length > 0">
          <div class="group-title">健康信息</div>
        </el-col>
        <el-col :push="1" :span="23" v-if="isShowCell('disease')">
          <el-form-item class="width-auto" label="疾病史" label-width="100px" prop="diseases">
            <div class="tag">
              <el-tag style="margin-right: 20px" v-for="tag in diseases" :key="tag" closable type="info" @close="handleTagClose(tag, 'disease')">
                {{ tag }}
              </el-tag>
              <el-button class="item" type="primary" size="small" :icon="Plus" plain @click="addDisease">添加</el-button>
            </div>
          </el-form-item>
        </el-col>
        <el-col :push="1" :span="23" v-if="isShowCell('allergy')">
          <el-form-item class="width-auto" label="过敏史" label-width="100px" prop="allergyHistory">
            <el-input placeholder="请输入过敏史" type="textarea" rows="3" v-model="ruleForm.allergyHistory"></el-input>
          </el-form-item>
        </el-col>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button class="w-100px cancel-btn" @click="cancel">取消</el-button>
          <el-button class="w-100px" type="primary" @click="save">保存</el-button>
        </span>
      </template>
    </el-dialog>
    <disease-drawer ref="diseaseRef" @get-select-disease="onGetSelectDisease"></disease-drawer>
    <tag-drawer ref="tagRef" @get-select-tag="onGetSelectTag"></tag-drawer>
  </div>
</template>
<script setup>
import { ref, computed, reactive, watch, toRaw } from "vue";
import { dayjs, ElMessage } from "element-plus";
import { Plus, Delete, EditPen } from "@element-plus/icons-vue";
import { getCustomerSources, getIntenteds, isTemplateShowCellAction, getTemplatefiledsList } from "../../corpInfo";
import diseaseDrawer from "../diseaseDrawer/index.vue";
import tagDrawer from "@/components/tag-drawer/index.vue";
import { memberStore } from "@/store/member";
import validate from "@/utils/validate";
import { getCorpTags } from "@/utils/common";
import { mobileRelation, referenceType } from "@/baseData";
import { createServiceRecord } from "@/utils/service";
import { addMember } from "@/api/member";
import { teamStore } from "@/store/team";
import { templateStore } from "@/store/template";
const { corpInfo, memberInfo, teams } = memberStore();
const {
  corpFileds: { baseInfo, healthInfo, viceInfo },
} = corpInfo;
const teamList = ref([]);
const { isShowCell } = templateStore();
let intenteds = getIntenteds();
let customerSources = getCustomerSources();
let $emit = defineEmits(["save"]);
const initForm = {
  mobile: "",
  name: "",
  customerSource: "",
  reference: "",
  referenceType: "",
  intentedProject: "",
  notes: "",
  age: "",
  sex: "",
  idCard: "",
};
const ruleForm = ref({ ...initForm });

let addMemberVisible = ref(false);
let diseaseRef = ref();
let diseases = ref([]);
let tagRef = ref();
let tagIds = ref([]);
let assistantByMobile = ref([
  {
    mobile: "",
    note: "",
  },
]);
function openDialog(e) {
  teamList.value = teams;
  ruleForm.value = { ...initForm };
  diseases.value = [];
  tagIds.value = [];
  assistantByMobile.value = [
    {
      mobile: "",
      note: "",
    },
  ];
  addMemberVisible.value = true;
}
function handleChange() {}
watch(
  () => assistantByMobile.value,
  (newValue) => {
    let array = newValue.filter((item) => item.mobile);
    ruleForm.value.mobile = array.length > 0 ? array[0].mobile : "";
  },
  { immediate: false, deep: true }
);

// 新增联系电话
function addMobile() {
  if (assistantByMobile.value.length < 3) {
    assistantByMobile.value.push({
      mobile: "",
      note: "",
    });
  }
}

// 删除手机号
function deteleMobile(index) {
  assistantByMobile.value.splice(index, 1);
}
// 添加成员
async function save() {
  let mobileList = assistantByMobile.value.filter((item) => item.mobile);
  // 判断必选项
  if (!ruleForm.value.name) {
    return;
  }
  const { currentTeam } = teamStore();
  const { teamId, name: teamName } = currentTeam;
  const userId = localStorage.getItem("userId");
  let params = {
    teamId: teamId ? [teamId] : [],
    teamName,
    sistantByMobile: mobileList, // 助手手机的手机号
    tagIds: tagIds.value, // 标签列表
    pastMedicalHistory: diseases.value || [],
    creator: userId,
    corpId: localStorage.getItem("corpId"),
    personResponsibles: [
      {
        corpUserId: userId,
        teamId,
      },
    ],
  };
  Object.assign(params, ruleForm.value);
  const { data, success, message } = await addMember(params);
  if (success) {
    $emit("save");
    // createServiceRecordAction(data.data);
    addMemberVisible.value = false;
    ElMessage({
      message: "添加成功",
      type: "success",
    });
  } else {
    ElMessage.error(message);
  }
}

// async function createServiceRecordAction(e) {
//   const { currentTeam } = teamStore();
//   const { teamId, name: teamName } = currentTeam;

//   const { anotherName } = memberInfo;
//   let time = dayjs().add(1, "day").format("YYYY-MM-DD HH:mm");
//   let item = {
//     taskContent: `${anotherName}为客户${ruleForm.value.name}新增一份档案，建档时间为${time}`,
//     executionTime: new Date().getTime(),
//     customerId: e.id,
//     executeTeamId: teamId,
//     teamName,
//     eventType: "addCustomerRrofile",
//     customerName: ruleForm.value.name,
//   };
//   await createServiceRecord(item);
// }

function addDisease() {
  diseaseRef.value.openDialog(diseases.value);
}
function cancel() {
  addMemberVisible.value = false;
}

// 获取选择的疾病列表
function onGetSelectDisease(e) {
  diseases.value = e;
}

function onGetSelectTag(ids) {
  tagIds.value = ids;
}

function addTags() {
  tagRef.value.openDialog(tagIds.value);
}
const handleTagClose = (tag, type) => {
  if (type === "disease") {
    diseases.value = diseases.value.filter((i) => i !== tag);
  } else if (type === "tag") {
    tagIds.value = tagIds.value.filter((i) => i !== tag);
  }
};
let rulesObj = {};
let list = getTemplatefiledsList() || [];
list.forEach((item) => {
  if (item.required) {
    rulesObj[item.title] = [{ required: true, message: `请输入${item.name}`, trigger: "blur" }];
  }
});

let rules = reactive(rulesObj);
function changeIdCard(e) {
  if (e.length === 18) {
    ruleForm.value.age = validate.getAge(e);
    ruleForm.value.sex = validate.getGender(e);
  } else {
    ruleForm.value.age = "";
    ruleForm.value.sex = "";
  }
}
const id2Tags = computed(() => {
  const { list } = getCorpTags(tagIds.value || []);
  return list;
});
defineExpose({
  openDialog,
});
</script>

<style lang="scss" scoped>
:deep(.el-form-item.el-form-item--autoHeight .el-form-item__label) {
  line-height: 1.2em;
}
.dease-drawer {
  .header {
    display: flex;
    align-items: center;

    .left {
      margin-right: 20px;
    }

    .right {
      width: 250px;
    }
  }
}

.form-tag {
  // margin-top: 5px;
  margin-right: 15px;
}

.addMember {
  display: flex;
  justify-content: center;
}

.member_from {
  width: 900px;
}

.mobileView {
  display: flex;
  align-items: center;

  .item {
    margin-right: 20px;
  }
}

.mobileView:not(:last-child) {
  margin-bottom: 20px;
}

.pointer {
  cursor: pointer;
}

.mt-10 {
  margin-top: 20px;
}

.group-title {
  padding: 0 12px 16px;
  color: var(--yc-main-text-color);
  font-weight: 600;
  font-size: 14px;
}

:deep(.el-form-item) {
  width: 320px;
}

:deep(.el-dialog__header) {
  padding-bottom: 15px;
}

:deep(.el-dialog__footer) {
  border-top: 1px solid #eee;
}

.width-auto {
  width: auto !important;
}

.w-240 {
  width: 240px;
}

.w-650 {
  width: 650px;
}

.cancel-btn {
  border-color: #bbb;
}

.cancel-btn:focus {
  color: #fff;
}

.linear-tag.el-tag--dark.el-tag--primary {
  // margin-bottom: 5px;
  background-image: linear-gradient(180deg, #4391f5 60%, #70c4e8);
  border-color: transparent !important;
}

.mr-6 {
  margin-right: 6px;
}
</style>
