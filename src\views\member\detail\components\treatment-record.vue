<template>
  <my-layout bg-fff common-shadow>
    <layout-item>
      <div class="p-15px pb-10px">
        <el-radio-group v-model="treatmentStatus" @change="search">
          <el-radio-button label="treated" value="treated">已治疗</el-radio-button>
          <el-radio-button label="pending" value="pending">治疗中</el-radio-button>
          <el-radio-button label="init" value="init">未治疗</el-radio-button>
        </el-radio-group>
        <div class="flex mt-10px items-center">
          <div class="flex-grow flex flex-wrap">
            <date-range-filter v-if="treatmentStatus === 'treated' || treatmentStatus === 'pending'" v-model:dates="treatmentDates" label="治疗日期" :text="treatmentDatesText" :width="320" />
            <date-range-filter v-if="treatmentStatus === 'init'" v-model:dates="billDates" label="开单日期" :text="billDatesText" :width="320" />
            <base-filter-item :clearable="projects.length > 0" class="cursor-pointer" label="项目" :text="projectText" @clear="changeProjects([])" @onClick="showProjectModal()" />
          </div>
          <div class="flex-shrink-0">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button plain type="primary" @click="reset()">重置</el-button>
          </div>
        </div>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <div v-if="treatmentStatus === 'treated'" class="h-full">
        <el-table border stripe v-loading="loading" class="mx-15px" height="100%" :data="list">
          <el-table-column property="projectName" label="项目名称" min-width="160" />
          <el-table-column property="treatmentDeptName" label="治疗科室" min-width="160" />
          <el-table-column property="doctorName" label="治疗医生" min-width="160">
            <template #default="{ row: { treatmentDoctorUserId } }">
              <ww-user :openid="treatmentDoctorUserId" />
            </template>
          </el-table-column>
          <el-table-column property="assistantName" label="配台" min-width="160">
            <template #default="{ row }">
              <el-tooltip :disabled="!(row.assistantDoctors && row.assistantDoctors.length > 0)" placement="top" effect="light">
                <div class="flex items-center justify-center w-full h-full">
                  <div class="max-h-full max-w-full text-14px text-left leading-22px line-clamp-2">
                    <span v-for="(i, index) in row.assistantDoctors" :key="`${i}_${row._id}`">
                      <ww-user :openid="i" />
                      <span v-if="row.assistantDoctors.length - 1 !== index">、</span>
                    </span>
                  </div>
                </div>
                <template #content>
                  <span v-for="(i, index) in row.assistantDoctors" class="text-14px" :key="`${i}__tooltip_${row._id}`">
                    <ww-user :openid="i" />
                    <span v-if="row.assistantDoctors.length - 1 !== index">、</span>
                  </span>
                </template>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column property="billTimeStr" label="治疗日期" width="120" />
          <el-table-column property="treatmentArea" label="治疗部位" width="120" />
          <el-table-column property="deductUsageCount" label="治疗数" width="160" />
          <el-table-column property="treatmentRemark" label="治疗备注" min-width="180">
            <template #default="{ row: { treatmentRemark } }">
              <el-tooltip :disabled="!treatmentRemark" placement="top" effect="light" :content="treatmentRemark" popper-class="max-w-480px">
                <div class="leading-22px line-clamp-2 w-full overflow-hidden flex">
                  {{ treatmentRemark }}
                </div>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column property="deductType" label="划扣方式" width="120" />
          <el-table-column property="deductOperator" label="划扣人" width="120">
            <template #default="{ row: { deductOperator } }">
              <ww-user :openid="deductOperator" />
            </template>
          </el-table-column>
          <el-table-column fixed="right" property="action" label="操作" width="80">
            <template #default="{ row }">
              <el-text type="primary" class="cursor-pointer" @click="toDetial(row)">详情</el-text>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-else class="h-full">
        <el-table border stripe v-loading="loading" class="mx-15px" height="100%" :data="list">
          <el-table-column property="projectName" label="项目名称" min-width="160" />
          <el-table-column property="packageName" label="所属套餐" min-width="160" />
          <el-table-column property="restUsageCount" label="剩余数" min-width="100" />
          <el-table-column property="usageCount" label="总数" min-width="100" />
          <el-table-column property="billdCreateTimeStr" label="开单时间" min-width="120" />
          <el-table-column property="validTimeStr" label="有效期至" min-width="160">
            <template #default="{ row: { validTime } }">
              <div v-if="!validTime">无限期</div>
              <div style="color: red" v-else-if="validTime && validTime < dayjs().endOf('day').valueOf()">{{ dayjs(validTime).format("YYYY-MM-DD") }}(已过期)</div>
              <div v-else>
                {{ dayjs(validTime).format("YYYY-MM-DD") }}
              </div>
            </template>
          </el-table-column>
          <el-table-column property="treatmentDeptName" label="计划治疗科室" min-width="160" />
          <el-table-column property="doctorName" label="计划治疗医生" min-width="160">
            <template #default="{ row: { treatmentDoctorUserId } }">
              <ww-user :openid="treatmentDoctorUserId" />
            </template>
          </el-table-column>
        </el-table>
      </div>
    </layout-main>
    <layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize" @handle-current-change="changePage" />
    </layout-item>
  </my-layout>
  <deduct-record :visible="treatmentRecordVisible" @close="closeTreatmentRecordVisible" :project="selectproject" operateType="read" />
  <project-picker :value="projects" :visible="projectModalVisible" :width="projectModalWidth" @close="closeProjectModal()" @change="changeProjects" />
  <deduct-modal v-model="deductVisible" @close="closeDeductVisible" :project="selectproject" @success="updateSuccess" @showCustomerDetail="toCustomerDetail"></deduct-modal>
  <customer-bill-record :visible="billVisible" @close="closeBillVisible" :customer="customer" :unTreatedRecord="unTreatedRecord" @success="getList" />
</template>
<script setup>
import { computed, onMounted, ref } from "vue";
import { storeToRefs } from "pinia";
import useElPagination from "@/hooks/useElPagination";
import useModal from "@/hooks/useModal";
import { getTreatmentRecord, getDeductRecord } from "@/api/treatment-record";
import pagination from "@/components/pagination/pagination.vue";
import projectPicker from "@/components/project-picker/project-picker.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import { DateRangeFilter, baseFilterItem } from "@/components/filter-bar";
import wwUser from "@/components/ww-user/index.vue";
import deductRecord from "@/views/treatment/deduct-record.vue";
import deductModal from "@/views/treatment/deduct-modal.vue";
import { teamStore } from "@/store/team";
import customerBillRecord from "@/views/treatment/customer-bill-record.vue";
import dayjs from "dayjs";
const { currentTeamId } = storeToRefs(teamStore());
const treatmentStatus = ref("treated");
const selectproject = ref({});
const unTreatedRecord = ref([]);
const treatmentDates = ref([]);
const billDates = ref([]);
const projects = ref([]);
const total = ref(0);
const loading = ref(false);
const { memberId } = defineProps(["memberId"]);
const emits = defineEmits(["update-customer"]);
const treatmentDatesText = computed(() => treatmentDates.value.join(" ~ "));
const billDatesText = computed(() => billDates.value.join(" ~ "));
const projectText = computed(() => {
  if (projects.value.length === 0) return "";
  if (projects.value.length > 1) return `已选择${projects.value.length}个项目`;
  return projects.value[0].projectName;
});
const { list, page, pageSize, changePage, changeSize } = useElPagination(getList);
const { close: closeProjectModal, show: showProjectModal, visible: projectModalVisible, width: projectModalWidth } = useModal(800);
const { close: closeTreatmentRecordVisible, show: showTreatmentRecordVisible, visible: treatmentRecordVisible } = useModal(800);
const { close: closeDeductVisible, show: showDeductVisible, visible: deductVisible } = useModal(800);
const { close: closeBillVisible, show: showBillVisible, visible: billVisible } = useModal(800);
function changeProjects(data) {
  projects.value = data;
}
function reset() {
  projects.value = [];
  treatmentDates.value = [];
  billDates.value = [];
  page.value = 1;
  pageSize.valu = 10;
  list.value = [];
  search();
}
function search() {
  list.value = [];
  changePage(1);
}
function toDetial(row) {
  showTreatmentRecordVisible();
  selectproject.value = row;
}
function updateSuccess() {
  getList();
  emits("update-customer");
}
async function getList() {
  if (treatmentStatus.value === "treated") {
    getTreatedRecord();
  } else {
    getOtherRecord();
  }
}

async function getOtherRecord() {
  let query = {
    page: page.value,
    pageSize: pageSize.value,
    customerId: memberId,
    treatmentStatus: [treatmentStatus.value],
    treatmentTimeDates: treatmentDates.value,
  };
  if (projects.value.length) query.projectIds = projects.value.map((i) => i._id);
  loading.value = true;
  const { success, data } = await getTreatmentRecord(query);
  loading.value = false;
  if (success) {
    total.value = data.total;
    list.value = data.list.map((i) => {
      return {
        ...i,
        createTreatementTimeStr: i.createTreatementTime ? dayjs(i.createTreatementTime).format("YYYY-MM-DD") : "",
        treatmentTimeStr: i.treatmentTime ? dayjs(i.treatmentTime).format("YYYY-MM-DD") : "",
        billdCreateTimeStr: i.billdCreateTime ? dayjs(i.billdCreateTime).format("YYYY-MM-DD") : "",
      };
    });
  }
}

async function getTreatedRecord() {
  let query = {
    page: page.value,
    pageSize: pageSize.value,
    customerId: memberId,
    deductStatus: ["deducted"],
    billTimeDates: treatmentDates.value,
  };
  if (projects.value.length) query.projectIds = projects.value.map((i) => i._id);
  loading.value = true;
  const { success, data } = await getDeductRecord(query);
  loading.value = false;
  if (success) {
    total.value = data.total;
    list.value = data.list.map((i) => {
      return {
        ...i,
        createTreatementTimeStr: i.createTreatementTime ? dayjs(i.createTreatementTime).format("YYYY-MM-DD") : "",
        billTimeStr: i.billTime ? dayjs(i.billTime).format("YYYY-MM-DD") : "",
        billdCreateTimeStr: i.billdCreateTime ? dayjs(i.billdCreateTime).format("YYYY-MM-DD") : "",
      };
    });
  }
}

// getDeductRecord
onMounted(() => {
  list.value = [{}];
  getList();
});
</script>
<style lang="scss" scoped></style>
