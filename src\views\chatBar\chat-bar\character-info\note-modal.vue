<template>
  <el-dialog :model-value="visible" :width="width" title="修改备注" @close="close">
    <el-scrollbar style="max-height: 50vh;">
      <div class="px-15px">
        <el-input show-word-limit type="textarea" v-model="notes" :autosize="{ minRows: 8, maxRows: 12 }" resize="none"
          class="w-full text-14px" placeholder="请输入备注" :maxlength="500" />
      </div>
    </el-scrollbar>
    <template #footer>
      <div class="text-center">
        <el-button plain class="w-100px" @click="close()">取消</el-button>
        <el-button type="primary" class="w-100px" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref, watch } from 'vue';
import { ElMessage } from "element-plus";
import useCustomerApi from '../../chat-bar/useCustomerApi';

const props = defineProps({
  memberId: { type: String, default: '' },
  visible: { type: Boolean, default: false },
  value: { type: String, default: '' }
})
const emits = defineEmits(['change', 'close']);
const { update } = useCustomerApi();
const notes = ref('');
async function confirm() {
  const { success, message } = await update(props.memberId, { notes: notes.value });
  if (success) {
    emits('change', { notes: notes.value });
    close()
  } else ElMessage.error(message)
}
function close() {
  emits('close')
}

watch(() => props.visible, n => {
  if (n) {
    notes.value = typeof props.value === 'string' ? props.value : '';
  }
})
</script>
<style lang="scss" scoped>
.tag-item {
  border: 1px solid #eee;
  margin-bottom: 12px;
}

.tag-group {
  margin-bottom: -12px;
}

.tag-item+.tag-item {
  margin-left: 12px;
}

.tag-item.active {
  background-color: var(--yc-primary-color);
  border-color: var(--yc-primary-color);
  color: #fff;
}
</style>