import { computed, ref } from "vue";
import { defineStore } from "pinia";
import { getProjectList, addProjectCate, deleteProjectCate, getProjectCateList, updateProjectCate, sortProjectCate, updateProject, getProjectAllCount } from "@/api/project-manage";
import { getCateTreeData } from '@/utils'


export default defineStore("projectStore", () => {
  const loading = ref(false);
  const cateList = ref([]);
  const cateTree = computed(() => getCateTreeData(cateList.value))

  async function getCates() {

    if (loading.value) return;
    loading.value = true
    const { data } = await getProjectCateList({ corpId: localStorage.getItem('corpId') });
    cateList.value = data && Array.isArray(data.list) ? data.list : [];
    loading.value = false
  }
  return { cateTree, getCates }
});
