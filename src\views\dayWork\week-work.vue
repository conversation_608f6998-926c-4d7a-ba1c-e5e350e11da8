<template>
  <div v-if="props.width > 0">
    <div flex class="work-tr" font-14>
      <div flex-shrink-0 text-center class="work-cell work-cell-th w-60px"> <span class="opacity-0">午别</span></div>
      <div v-for="item in weekTh" :key="item.date" flex-shrink-0 text-center class="work-cell work-cell-th"
        :class="item.isToday ? 'work-cell--active' : ''" :style="cellStyle">
        <div font-14>{{ item.weekStr }}</div>
        <div v-if="item" class="py-6px" flex items-center justify-center font-18 font-semibold>
          <span>{{ item.crossMonth ? item.monthDay : item.day }}</span>
          <span v-if="item.isToday" class="work-cell-today inline-block ml-2px">今</span>
        </div>
        <div font-12>{{ item.dayCn }}</div>
      </div>
    </div>
    <div flex class="work-tr" font-14>
      <div flex-shrink-0 text-center class="work-cell noon w-60px">上 午</div>
      <div v-for="({ date }) in props.days" :key="date + '_AMNOON'" flex-shrink-0 class="work-cell"
        :style="noonCellStyle">
        <template v-if="noonWorks[date] && noonWorks[date].AMWorks.length">
          <div v-for="work in noonWorks[date].AMWorks" :key="work._id" flex items-center pointer
            class="mb-10px work-cell-item" @click.stop="handleClick($event, work)">
            <div flex-shrink-0 class="dot" :class="work.workTimeStamp > props.timestamp ? '' : 'dot--gray'"></div>
            <div flex-shrink-0 class="mr-6px">{{ work.noonTime }}</div>
            <div flex-grow class="w-0px truncate">{{ work.workContent }}</div>
            <div flex-shrink-0>{{ work.customerName }}</div>
          </div>
        </template>
        <div class="work-cell-bg" @click="addWork(date)"></div>
      </div>
    </div>
    <div flex class="work-tr" font-14>
      <div flex-shrink-0 text-center class="work-cell noon w-60px">下 午</div>
      <div v-for="({ date }) in props.days" :key="date + '_PMNOON'" flex-shrink-0 class="work-cell"
        :style="noonCellStyle">
        <template v-if="noonWorks[date] && noonWorks[date].PMWorks.length">
          <div v-for="work in noonWorks[date].PMWorks" :key="work._id" flex items-center pointer
            class="mb-10px work-cell-item" @click="handleClick($event, work)">
            <div flex-shrink-0 class="dot" :class="work.workTimeStamp > props.timestamp ? '' : 'dot--gray'"></div>
            <div flex-shrink-0 class="mr-6px">{{ work.noonTime }}</div>
            <div flex-grow class="w-0px truncate">{{ work.title }}</div>
            <!-- <div flex-shrink-0>{{ work.customerName }}</div> -->
          </div>
        </template>
        <div class="work-cell-bg" @click="addWork(date)"></div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { computed } from 'vue';
import { WeekDay } from '@/baseData';
import useClick from '@/hooks/useClick';
import dayjs from 'dayjs';
const props = defineProps({
  days: { type: Array, default: () => [] },
  timestamp: { type: Number, default: 0 },
  width: { type: Number, default: 0 },
  works: { type: Object, default: () => ({}) }
})
const cellWidth = computed(() => {
  return Math.max(150, (props.width - 9 - 60) / 7)
})
const cellStyle = computed(() => {
  return { width: cellWidth.value + 'px' }
})
const noonCellStyle = computed(() => {
  return { ...cellStyle.value, minHeight: 'calc(50vh - 170px)' }
})
const noonWorks = computed(() => {
  return props.days.reduce((val, day) => {
    const works = props.works[day.date] || [];
    const AMWorks = works.filter(i => dayjs(i.workTimeStamp).hour() < 12);
    const PMWorks = works.filter(i => dayjs(i.workTimeStamp).hour() >= 12);
    val[day.date] = { AMWorks, PMWorks }
    return val
  }, {})
})
const weekTh = computed(() => {
  return props.days.slice(0, 7).map(i => {
    return {
      date: i.date,
      monthDay: i.monthDay,
      dayCn: i.dayCn,
      crossMonth: i.crossMonth,
      isToday: dayjs(props.timestamp).format('YYYY-MM-DD') === i.date,
      day: dayjs(i.date).format('D'),
      weekStr: WeekDay[dayjs(i.date).day()]
    }
  })
})

const emits = defineEmits(['showDetail', 'toEdit', 'addWork']);
const handleClick = useClick({ delay: 400, click: showDetail, dbclick: toEdit })
function showDetail(detail) {
  emits('showDetail', detail)
}

function toEdit(work) {
  emits('edit', work)
}

function addWork(date) {
  emits('addWork', date)
}

</script>
<style>
@import "./work.css";

.dot {
  margin-right: 6px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #006eff;
}

.dot--gray {
  background-color: #666
}

.noon {
  writing-mode: vertical-lr;
}
</style>