<template>
  <div class="flex justify-between">
    <div v-for="item in packagerows" :class="item.key === 'operation' ? ' operationClass' : 'item'" border-bottom>
      <div class="p-10px h-50px text-ellipsis text-14px line-height-30px flex align-center"
        :style="'width:' + item.width + 'px'">
        <div v-if="item.key === 'operation'">
          <el-icon class="pointer" @click="removePackage(packageItem)" color="red">
            <Delete />
          </el-icon>
        </div>
        <div v-if="item.key === 'number'">1</div>
        <div v-else-if="item.key === 'discount'">
          <div class="flex align-center">
            <el-input type="number" :disabled="!packageItem.enableDiscount" v-model="packageItem.discount"
              class="w-60px" @blur="inputPackageDiscount(packageItem)" />
          </div>
        </div>
        <div v-else-if="item.key === 'packageTotalPrice'">
          <el-input type="number" v-model="packageItem.packageTotalPrice" class="w-90px"
            @blur="inputPackageTotal(packageItem)" />
        </div>
        <div v-else>{{ packageItem[item.key] }}</div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ElMessageBox } from "element-plus";
import BigNumber from "bignumber.js";
const props = defineProps({
  packageItem: { type: Array, default: () => [] },
  staffList: { type: Array, default: () => [] },
  operationType: { type: String, default: "" },
});

const packagerows = [
  {
    key: "projectType",
    value: "类型",
    width: "140",
  },
  {
    key: "name",
    value: "项目名称",
    width: "170",
  },
  {
    key: "packagePrice",
    value: "单价",
    width: "100",
  },
  {
    key: "number",
    value: "数量",
    width: "70",
  },
  {
    key: "discount",
    value: "折扣",
    width: "150",
  },
  {
    key: "packageTotalPrice",
    value: "总价",
    width: "110",
  },
  // {
  //   key: "billDept_id",
  //   value: "开单科室",
  //   width: "160",
  // },
  {
    key: "treatmentDept_id",
    value: "计划治疗科室",
    width: "160",
  },
  { key: "treatmentDoctorUserId", value: "计划治疗人", width: "160" },
  {
    key: "operation",
    value: "操作",
    width: "50",
  },
];
const emits = defineEmits(["removePackage", "inputPackageDiscount"]);
function getTotal(packageItem) {
  let total = packageItem.packagePrice;
  if (packageItem.discount) {
    total = (total * packageItem.discount) / 10;
  }
  return total.toFixed(2);
}

async function inputPackageDiscount(packageItem) {
  if (Number(packageItem.discount) < Number(packageItem.minDiscount)) {
    try {
      await ElMessageBox.alert("折扣不能小于最低折扣", "提示", {
        confirmButtonText: "确定",
        type: "warning",
      });
      packageItem.discount = "";
      packageItem.packageTotalPrice = packageItem.packagePrice;
    } catch (error) {
      packageItem.discount = "";
      packageItem.packageTotalPrice = packageItem.packagePrice;
      return;
    }
  } else {
    packageItem.discount = Number(packageItem.discount);
    packageItem.packageTotalPrice = getTotal(packageItem);
    emits("inputPackageDiscount", packageItem);
  }
}
async function inputPackageTotal(packageItem) {
  packageItem.discount = new BigNumber(packageItem.packageTotalPrice).dividedBy(new BigNumber(packageItem.packagePrice)).times(new BigNumber(10)).toFixed(2);
  if (Number(packageItem.discount) < Number(packageItem.minDiscount)) {
    try {
      let title = packageItem.minDiscount == 10 ? "该套餐不支持折扣" : "折扣不能小于最低折扣";
      await ElMessageBox.alert(title, "提示", {
        confirmButtonText: "确定",
        type: "warning",
      });
      packageItem.discount = "";
      packageItem.packageTotalPrice = packageItem.packagePrice;
    } catch (error) {
      packageItem.discount = "";
      packageItem.packageTotalPrice = packageItem.packagePrice;
      return;
    }
  } else {
    emits("inputPackageDiscount", packageItem);
  }
}
function removePackage(packageItem) {
  emits("removePackage", packageItem);
}
</script>
<style lang="scss" scoped>
.operationClass {
  position: sticky;
  right: 0;
  box-shadow: 0 0 4px 0 rgba(54, 58, 80, 0.2);
  background-color: #fff;
}

.item {
  flex: 1;
}
</style>