<template>
  <div class="tagDropdown" :class="{
    'tagDropdown--border': props.visible,
    'animated': props.animate
  }" :style="style">
    <el-scrollbar :max-height="200">
      <el-form :label-width="120" label-position="left">
        <el-form-item v-for="(item, idx) in tagList" :key="idx" :label="item.label + '：'">
          <el-checkbox-group v-model="values[idx]">
            <el-checkbox v-for="tag in item.options" class="tag__item" :key="tag.id" :label="tag.id"
              @change="onChange($event, tag.id, idx)">
              {{ tag.name }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
    </el-scrollbar>
  </div>
</template>
<script setup>
import { computed, ref } from "vue";
import { memberStore } from "@/store/member";
const props = defineProps({ visible: { type: Boolean, default: false }, animate: { type: Boolean, default: true }, mult: { default: false } })
const style = computed(() => props.visible ? 'max-height:200px' : 'max-height: 0')
const { corpInfo } = memberStore();
const corpTags = Array.isArray(corpInfo.tags) ? corpInfo.tags : []
const tagList = ref(corpTags); //标签列表
const values = ref(corpTags.map(i => []))
const emits = defineEmits(['onChange'])
function onChange(e, id, idx) {
  if (e && !props.mult) {
    values.value[idx] = [id]
  }
  emits('onChange', values.value.reduce((val, item) => [...val, ...item], []))

}
// watch(values, n => {
//   emits('onChange', n.reduce((val, item) => [...val, ...item], []))
// })

function reset() {
  values.value = values.value.map(i => [])
}
defineExpose({
  reset,
});
</script>
<style scoped>
.tagDropdown {
  /* margin: 0 10px; */
  padding: 0 20px;
  border-radius: 8px;
  overflow: hidden;
  background-color: #fefefe;
}

.tagDropdown.animated {
  /* transition: max-height .5s; */
}

.tagDropdown--border {
  border: 1px solid #eee;
}

:deep(.el-form-item) {
  margin-bottom: 5px;
}

:deep(.el-checkbox__label) {
  color: var(--yc-main-text-color);
}

:deep(.el-form-item__label) {
  color: #666;
}

.tag__item {
  min-width: 120px;
}
</style>