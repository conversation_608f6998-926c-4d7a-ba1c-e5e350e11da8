<template>
  <div px-15 pb-10 border-bottom>
    <div font-semibold font-16 class="mt-17px pt-10px" @click="changeData()">{{ title }}</div>
    <div v-loading="loading" class="h-300px" ref="lineChartRef"></div>
  </div>
</template>
<script setup>
import { ref, watch, inject, computed } from "vue";
import useLineChart from "@/hooks/useLineChart";
import { dates } from "./useDateTag";
import { getMemberTrendByTeamId, getServiceTrendByTeamId } from "@/api/member.js";
const props = defineProps({
  dates: { type: Array, default: () => [] },
  currentTeam: {
    type: Object,
    default: {},
  },
  cardType: {
    type: String,
    default: "",
  },
});
const title = computed(() => {
  switch (props.cardType) {
    case "increaseCount":
      return "新增客户趋势";
    case "peopleCount":
      return "累计服务人次趋势";
    case "visitCount":
      return "累计报到人次趋势";
    default:
      return "新增客户趋势";
  }
});
const loading = ref(false);
const lineChartRef = ref();
const { xAxisData, seriesData } = useLineChart(lineChartRef, { grid: { left: 30, right: 40 } });
watch(() => [props.dates, props.cardType, props.currentTeam], () => {
  getList();
}, { immediate: true });
async function getList() {
  let { data, success } = await getMemberTrendByTeamId(dates.value, "", props.cardType);
  if (success) {
    let xData = data.data.map((item) => {
      return item._id;
    });
    let sData = data.data.map((item) => {
      return item.count;
    });

    getData(xData, sData);
  }
}
async function getData(xData, sData) {
  loading.value = true;
  setTimeout(function mock() {
    loading.value = false;
    const [start, end] = props.dates;
    xAxisData.value = xData;
    seriesData.value = sData;
  }, 2000);
}

watch(
  () => props.dates,
  () => getData()
);
</script>
