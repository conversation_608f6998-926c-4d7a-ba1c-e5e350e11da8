<template>
  <div class="flex-shrink-0 relative flex items-center text-14px h-40px pr-30px cursor-pointer"
    :class="clearable ? 'group':''" :style="{width}" @click="onClick()">
    <div class="flex-shrink-0 pl-10px text-gray-500"> {{ label ? `${label}：`:''}}</div>
    <div color-normal class="text-right mx-5px truncate">{{ valueStr}}</div>
    <el-icon class="flex-shrink-0 group-hover:hidden" :size="18">
      <CaretBottom />
    </el-icon>
    <el-icon class="flex-shrink-0 text-gray-500 hidden group-hover:block" :size="18" @click.stop="clear()">
      <Close />
    </el-icon>
    <slot></slot>
  </div>
</template>
<script setup>
import { computed } from 'vue';

const emits = defineEmits(['onClick', 'clear'])
const props = defineProps({
  clearable: { type: <PERSON>olean, default: false },
  label: { type: String, default: '' },
  text: { type: String, default: '' },
  width: { type: [Number, String], default: 280 },
})

const valueStr = computed(() => props.text || '全部');

function onClick() {
  emits('onClick')
}

function clear() {
  emits('clear')
}
</script>
<style lang="scss" scoped></style>
