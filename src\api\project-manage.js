import { post } from "./axios";
async function useCorp(data) {
  const res = await post("corp", data);
  return res;
}

export async function addProject(data) {
  const res = await useCorp({ type: "addProject", ...data });
  return res;
}

export async function deleteProject(data) {
  const res = await useCorp({ type: "deleteProject", ...data });
  return res;
}

export async function updateProject(data) {
  const res = await useCorp({ type: "updateProject", ...data });
  return res;
}

export async function getProjectList(data) {
  const res = await useCorp({ type: "getProjectList", ...data });
  return res;
}

export async function getProjectListWithCates(data) {
  const res = await useCorp({ type: "getProjectListWithCates", ...data });
  return res;
}

export async function getProjectListByCateIds(data) {
  const res = await useCorp({ type: "getProjectListByCateIds", ...data });
  return res;
}

export async function addProjectCate(data) {
  const res = await useCorp({ type: "addProjectCate", ...data });
  return res;
}

export async function updateProjectCate(data) {
  const res = await useCorp({ type: "updateProjectCate", ...data });
  return res;
}

export async function deleteProjectCate(data) {
  const res = await useCorp({ type: "deleteProjectCate", ...data });
  return res;
}

export async function getProjectCateList(data) {
  const res = await useCorp({ type: "getProjectCateList", ...data });
  return res;
}

export async function sortProjectCate(data) {
  const res = await useCorp({ type: "sortProjectCate", ...data });
  return res;
}

export async function ifProjectCateExist(data) {
  const res = await useCorp({ type: "ifProjectCateExist", ...data });
  return res;
}

export async function getProjectAllCount(data) {
  const res = await useCorp({ type: "getProjectAllCount", ...data });
  return res;
}

export async function getProjectCreateTreatmentOrder(data) {
  const res = await useCorp({ type: "getProjectCreateTreatmentOrder", ...data });
  return res;
}

export async function getProjectDiscount(data) {
  const res = await useCorp({ type: "getProjectDiscount", ...data });
  return res;
}

export async function getDeptProjectCate(data) {
  const res = await useCorp({ type: "getDeptProjectCate", ...data });
  return res;
}

export async function getProjectCateGroup(data) {
  const res = await useCorp({ type: "getProjectCateGroup", ...data });
  return res;
}

export async function getProjectAllCateGroup(data) {
  const res = await useCorp({ type: "getProjectAllCateGroup", ...data });
  return res;
}

