<template>
  <div v-if="getFail" class="h-full flex flex-col items-center justify-center">
    <empty-data :top="0" title="获取sop内容失败" padding="0 0 15px" text-top="10px" :image-width="200"></empty-data>
  </div>
  <my-layout v-else bg-fff>
    <Layout-main>
      <el-form class="px-15px pt-15px pt-30px overflow-hidden" :label-width="140" label-suffix="：" label-position="right">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item class="is-required" label="名称">
              <el-input v-model="sop.sopName" placeholder="请输入名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item class="is-required" label="分类">
              <el-cascader v-model="classId" class="w-full" :show-all-levels="false" :options="cateTree" :props="{ checkStrictly: true, value: '_id' }" clearable />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="内容说明">
              <el-input v-model="sop.sopContent" type="textarea" :rows="4" resize="none" placeholder="请输入内容说明" />
            </el-form-item>
          </el-col>
          <el-col :span="24" :lg="12">
            <el-form-item class="is-required" label="下达执行范围">
              <div class="flex-grow flex">
                <el-select multiple collapse-tags v-model="teamIds" class="w-1/2" placeholder="请选择团队">
                  <el-option-group label="">
                    <div class="el-select-dropdown__item" :class="allTeamSelected ? 'selected' : ''" @click="allTeamSelected = !allTeamSelected">
                      <span>全部团队</span>
                      <el-icon v-show="allTeamSelected" class="absolute right-20px text-12px top-11px">
                        <Check />
                      </el-icon>
                    </div>
                    <el-option v-for="i in teams" :key="i.teamId" :label="i.name" :value="i.teamId" />
                  </el-option-group>
                </el-select>
                <el-select multiple collapse-tags v-model="sop.executeJobs" class="w-1/2 ml-10px" placeholder="请选择岗位" @change="selectExecuteJobs">
                  <el-option-group label="">
                    <el-option key="all" label="全部员工" value="all" />
                    <el-option v-for="i in jobList" :key="i.value" :label="i.name" :value="i.value" />
                  </el-option-group>
                </el-select>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24" :lg="12">
            <el-form-item class="is-required" label="生效日期">
              <div class="flex-grow flex items-center">
                <el-date-picker v-model="sop.executeStartTime" class="flex-shrink flex-grow" format="YYYY-MM-DD" placeholder="立即执行" type="date" value-format="x" style="--el-date-editor-width: auto" :disabled-date="disableBeforeDay" @change="changeEffectDate('start')" />
                <span class="mx-10px flex-shrink-0 text-20px">~</span>
                <el-date-picker v-model="sop.executeEndTime" class="flex-shrink flex-grow" format="YYYY-MM-DD" placeholder="不限" type="date" value-format="x" style="--el-date-editor-width: auto" :disabled-date="disableBeforeDay" @change="changeEffectDate('end')" />
              </div>
            </el-form-item>
          </el-col>
          <div class="py-15px pr-15px">
            <div class="text-16px font-semibold pr-10px pb-10px pl-40px">
              目标客户筛选
              <el-tooltip v-if="filterDesc" placement="top-start" effect="light">
                <el-icon class="cursor-pointer text-14px font-normal text-gray-400 translate-y-2px transform">
                  <QuestionFilled />
                </el-icon>
                <template #content>
                  <span class="text-gray-500 text-14px leading-24px whitespace-pre-wrap">{{ filterDesc }}</span>
                </template>
              </el-tooltip>
            </div>
            <filter-group v-model="sop" v-model:list="sop.list" :conditions="conditions" />
          </div>

          <el-col :span="24">
            <el-col :span="12" :lg="12" v-if="sop.executeType === 'manual'">
              <el-form-item class="is-required" label="任务类型">
                <el-select v-model="sop.eventType" class="flex-grow" placeholder="请选择类型">
                  <el-option v-for="item in eventList" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-form-item class="is-required mb-10px" label="处理类型">
              <el-radio-group :model-value="sop.executeType">
                <el-radio label="manual" value="manual" @click="changeExecuteType('manual')">
                  <span>员工任务</span>
                  <el-tooltip placement="top-start" effect="light" content="需员工手工点击处理的任务">
                    <el-icon class="ml-5px cursor-pointer text-14px font-normal text-gray-400 translate-y-2px transform">
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </el-radio>
                <el-radio label="auto" value="auto" @click="changeExecuteType('auto')">
                  <span>自动化任务</span>
                  <el-tooltip placement="top-start" effect="light" content="系统自动处理的任务，无需员工手工处理">
                    <el-icon class="ml-5px cursor-pointer text-14px font-normal text-gray-400 translate-y-2px transform">
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item class="is-required mb-10px" label="处理方式">
              <el-radio-group :model-value="sop.executeMethod">
                <template v-if="sop.executeType === 'manual'">
                  <el-radio label="groupmsg" value="groupmsg" @click="change('groupmsg')">群发任务(人工批量发送)</el-radio>
                  <el-radio label="todo" value="todo" @click="change('todo')">待办事项 (人工一对一跟进)</el-radio>
                </template>
                <template v-else-if="sop.executeType === 'auto'">
                  <el-radio label="tag" value="tag" @click="change('tag')">自动打标签</el-radio>
                  <el-radio label="enterGroup" value="enterGroup" @click="change('enterGroup')">自动入组</el-radio>
                  <el-radio label="sms" value="sms" @click="change('sms')">短信发送</el-radio>
                </template>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <!-- <div class="border-t border-dashed border-gray-200"> -->
      <!-- <div class="pt-15px pr-15px pl-50px text-16px font-semibold">内容</div> -->
      <el-form class="px-15px overflow-hidden" :label-width="140" label-suffix="：" label-position="right">
        <el-form-item v-if="sop.executeMethod === 'todo'" class="is-required mb-10px" label="向客户发送">
          <el-switch v-model="sop.enableSendContent" size="large" inline-prompt active-text="是" inactive-text="否" />
        </el-form-item>
        <el-form-item v-if="sop.enableSendContent" class="is-required mb-10px" label="发送内容">
          <div class="flex-grow">
            <send-content-set v-model="sop" />
          </div>
        </el-form-item>
        <template v-if="sop.executeMethod === 'tag'">
          <el-form-item class="is-required" label="选择新增标签">
            <div class="flex-grow flex flex-wrap">
              <customer-tag v-for="tagId in sop.newTagIds || []" :key="tagId" :tag-id="tagId" class="flex-shrink-0 mr-5px mt-5px" />
              <el-button text type="primary" class="mt-5px" @click="selectTag(sop.newTagIds, 'newTagIds')" size="small">
                <svg-icon size="16" name="pencil"></svg-icon>
              </el-button>
            </div>
          </el-form-item>
          <el-form-item class="is-required" label="选择删除标签">
            <div class="flex-grow flex flex-wrap">
              <customer-tag v-for="tagId in sop.removeTagIds || []" :key="tagId" :tag-id="tagId" class="flex-shrink-0 mr-5px mt-5px" />
              <el-button text type="primary" class="mt-5px" @click="selectTag(sop.removeTagIds, 'removeTagIds')" size="small">
                <svg-icon size="16" name="pencil"></svg-icon>
              </el-button>
            </div>
          </el-form-item>
        </template>
        <template v-if="sop.executeMethod === 'enterGroup'">
          <el-form-item v-if="sop.groups && sop.groups[0]" class="is-required" label="分组名称">
            <div class="flex-grow">
              <div class="flex items-center">
                <el-select v-model="sop.groups[0].groupId" class="w-160px" placeholder="请选择分组">
                  <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
                <span class="ml-10px text-14px text-gray-500">加入人数占比：{{ sop.groups[0].percent }}%</span>
              </div>
              <el-button v-if="sop.groups.length === 1" text type="primary" class="mt-10px" @click="addNewGroup()">
                <el-icon>
                  <Plus />
                </el-icon>
                添加分组
              </el-button>
            </div>
          </el-form-item>
          <el-form-item v-if="sop.groups && sop.groups[1]" class="is-required" label="分组名称">
            <el-select v-model="sop.groups[1].groupId" class="w-160px" placeholder="请选择分组">
              <el-option v-for="item in groupList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <span class="ml-10px text-14px text-gray-500">加入人数占比：</span>
            <el-input v-model="sop.groups[1].percent" class="w-100px" placeholder="请输入" @blur="checkPercent()" />
            <span class="ml-10px text-14px text-gray-500">%</span>
            <el-button text type="danger" @click="removeGroup()">
              <el-icon>
                <Delete />
              </el-icon>
            </el-button>
          </el-form-item>
        </template>
      </el-form>
      <!-- </div> -->
    </Layout-main>
    <Layout-item>
      <div common-shadow--r class="py-15px text-center">
        <el-button plain class="w-100px" @click="closePage()">取消</el-button>
        <el-button class="w-100px" type="primary" @click="save">保存</el-button>
      </div>
    </Layout-item>
  </my-layout>
  <tag-drawer ref="tagDrawerRef" :mult="true" @get-select-tag="onTagChanged($event)"></tag-drawer>
</template>
<script setup>
import { computed, onMounted, ref, watch } from "vue";
import { getGroups } from "@/api/group";
import { storeToRefs } from "pinia";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import dayjs from "dayjs";
import BigNumber from "bignumber.js";
import { getCorpMemberJob } from "@/api/corp.js";
import { createSopTask, getSopCateList, getSopFilterRule, getSopTask, updateSopTask } from "@/api/member";
import { getCorpMemberByTeamsAndJobs } from "@/api/corp";
import { ToDoEventType } from "@/baseData";
import { dbStore } from "@/store/db.js";
import { memberStore } from "@/store/member";
import { tagsStore } from "@/store/tags";
import { teamStore } from "@/store/team";
import { getCateTreeData, disableBeforeDay } from "@/utils/index";
import customerTag from "@/components/customer-tag/customer-tag.vue";
import filterGroup from "./filter-group/index.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import sendContentSet from "@/components/send-content-set/send-content-set.vue";
import SvgIcon from "@/components/svg-icon";
import TagDrawer from "@/components/tag-drawer/index.vue";
import EmptyData from "@/components/empty-data.vue";

const filterDesc = `目标客户筛选时，具有时间属性的筛选条件只能选择1个。
具备时间属性的筛选条件包括：
【生日祝福主题】
【未到院主题】中的“建档当天/建档后”。
【门诊主题】中的“门诊就诊当天/门诊就诊后”。
【住院主题】中的“入院当天/入院后”、“出院当天/出院后”。
【手术主题】中的“手术当天/手术后”。
【体检主题】中的“体检当天/体检后”。
【健康指标主题】中的“指标更新当天/指标更新后”。`;

const eventList = Object.keys(ToDoEventType).map((value) => ({ value, label: ToDoEventType[value] }));
const route = useRoute();
const db = dbStore();
const { removeTag } = tagsStore();
const { allTeams: teams } = storeToRefs(teamStore());
const { healthIndicatorsTemplate } = storeToRefs(db);
const { memberInfo } = storeToRefs(memberStore());
const { getHealthIndicatorTemplate } = db;
const teamMap = computed(() =>
  teams.value.reduce((acc, cur) => {
    acc[cur.teamId] = cur.name;
    return acc;
  }, {})
);
const cateList = ref([]);
const cateTree = computed(() => getCateTreeData(cateList.value));
const classId = ref("");
const conditions = ref([]);
const getFail = ref(false);
const jobList = ref([]);
const tagIdsType = ref();
const tagDrawerRef = ref();
const teamIds = ref();
const groupList = ref([]);
const sop = ref({
  sopName: "",
  eventType: "",
  sopContent: "",
  classId: "",
  executeTeams: [],
  executeJobs: ["all"],
  executeStartTime: "",
  executeEndTime: "",
  intervalDays: "",
  newTagIds: [],
  executeType: "manual",
  enableSendContent: true,
  executeMethod: "groupmsg",
});
const sopTeamMap = ref({});
function selectExecuteJobs(val) {
  if (val[val.length - 1] === "all") {
    sop.value.executeJobs = ["all"];
  } else {
    sop.value.executeJobs = val.filter((i) => i !== "all");
  }
}

const cateIdStr = computed(() => {
  if (typeof classId.value === "string") return classId.value;
  return Array.isArray(classId.value) ? classId.value[classId.value.length - 1] || "" : "";
});

const allTeamSelected = computed({
  get() {
    return teams.value.every((item) => teamIds.value.includes(item.teamId));
  },
  set(val) {
    if (val) teamIds.value = teams.value.map((item) => item.teamId);
    else teamIds.value = [];
  },
});

// 使用 watch 监听 teamIds 变化 获取分组列表
watch(() => teamIds.value, getGroupList);

function change(type) {
  if (sop.value.executeMethod === type) return;
  if (type === "sms") {
    ElMessage.info("机构还未开通短信通道！");
    return;
  }
  sop.value.executeMethod = type;
  if (type === "enterGroup") {
    getGroupList();
    sop.value.groups = [{ percent: 100, groupId: "" }];
  } else {
    sop.value.groups = [];
  }
  if (type === "groupmsg") {
    sop.value.enableSendContent = true;
  } else {
    sop.value.enableSendContent = false;
  }
}
function changeEffectDate(type) {
  if (type === "end" && sop.value.executeEndTime) {
    sop.value.executeEndTime = dayjs(sop.value.executeEndTime).endOf("day").valueOf();
  }
  if (sop.value.executeStartTime && sop.value.executeEndTime) {
    sop.value.executeStartTime = Math.min(sop.value.executeStartTime, sop.value.executeEndTime);
    sop.value.executeEndTime = Math.max(sop.value.executeStartTime, sop.value.executeEndTime);
  }
}

function selectTag(tagIds, type) {
  tagIdsType.value = type;
  tagDrawerRef.value.openDialog(Array.isArray(tagIds) ? [...tagIds] : []);
}

function onTagChanged(tagIds) {
  if (tagIdsType.value === "newTagIds") {
    sop.value.newTagIds = tagIds;
  } else {
    sop.value.removeTagIds = tagIds;
  }
}

function closePage() {
  removeTag(route.fullPath);
}

function addNewGroup() {
  if (sop.value.groups.length === 1) {
    sop.value.groups.push({ percent: "", groupId: "" });
  }
}
function removeGroup() {
  sop.value.groups = [sop.value.groups[0]];
  checkPercent();
}

function checkPercent() {
  const secondGroup = sop.value.groups[1] || {};
  const percent = Number(secondGroup.percent || 0);
  if (percent >= 0 && percent <= 100) {
    sop.value.groups[0].percent = new BigNumber(100).minus(percent).toNumber();
    if (sop.value.groups[1]) sop.value.groups[1].percent = percent;
  } else {
    secondGroup.percent = "";
    sop.value.groups[1].percent = 100;
    ElMessage.error("请输入0-100的数字");
  }
}

async function getCateList() {
  const { success, data, message } = await getSopCateList({ corpId: localStorage.getItem("corpId") });
  if (success) {
    cateList.value = data && Array.isArray(data.list) ? data.list : [];
  } else {
    ElMessage.error(message);
    cateList.value = [];
  }
  if (!route.params.id && history.state.cateId && cateList.value.some((i) => i._id === history.state.cateId)) {
    classId.value = history.state.cateId;
  }
}

async function getJobs() {
  const { data, success } = await getCorpMemberJob();
  if (success) jobList.value = Array.isArray(data.data) ? data.data : [];
}

async function getExecuteUserIds(teamIds, jobs) {
  const { data, success } = await getCorpMemberByTeamsAndJobs({
    teamIds,
    jobs,
  });
  return success ? data.data : [];
}

onMounted(async () => {
  if (healthIndicatorsTemplate.value.length === 0) getHealthIndicatorTemplate();
  getJobs();
  getSopFilterRule().then((res) => {
    conditions.value = res.data && res.data.data && Array.isArray(res.data.data.conditions) ? res.data.data.conditions : [];
  });
  await getCateList();
  if (route.params.id) {
    await getSop();
  }
});

async function getSop() {
  const { data, success, message } = await getSopTask(route.params.id);
  if (success && data.data) {
    sop.value = data.data;
    const executeTeams = Array.isArray(sop.value.executeTeams) ? sop.value.executeTeams : [];
    teamIds.value = executeTeams.map((i) => i.teamId).filter(Boolean);
    sopTeamMap.value = executeTeams.reduce((prev, cur) => {
      if (cur.teamId && cur.name) prev[cur.teamId] = cur.name;
      return prev;
    }, {});
    classId.value = sop.value.classId;
    if (sop.value.executeMethod === "groupmsg") {
      sop.value.enableSendContent = true;
    } else if (sop.value.executeMethod === "todo") {
      sop.value.enableSendContent = Boolean(sop.value.sendContent);
    }
    setFilterConditionsKey();
  } else {
    ElMessage.error(message);
    getFail.value = true;
  }
}

function verify() {
  const newTagIds = Array.isArray(sop.value.newTagIds) ? sop.value.newTagIds : [];
  const removeTagIds = Array.isArray(sop.value.removeTagIds) ? sop.value.removeTagIds : [];
  if (typeof sop.value.sopName !== "string" || sop.value.sopName.trim() === "") {
    ElMessage.info("请输入名称");
  } else if (!eventList.some((i) => i.value === sop.value.eventType) && sop.value.executeType === "manual") {
    ElMessage.info("请选择类型");
  } else if (!cateList.value.some((i) => i._id === cateIdStr.value)) {
    ElMessage.info("请选择分类");
  } else if (teamIds.value.length === 0) {
    ElMessage.info("请选择团队");
  } else if (!Array.isArray(sop.value.executeJobs) || sop.value.executeJobs.length === 0) {
    ElMessage.info("请选择岗位");
  } else if (!Array.isArray(sop.value.filterConditions) || sop.value.filterConditions.length === 0) {
    ElMessage.info("请添加筛选项");
  } else if (!sop.value.executeMethod) {
    ElMessage.info("请选择跟进方式");
  } else if (sop.value.enableSendContent && !sop.value.sendContent) {
    ElMessage.info("请输入发送内容");
  } else if (sop.value.executeMethod === "tag" && newTagIds.length === 0 && removeTagIds.length === 0) {
    ElMessage.info("请选择标签");
  } else {
    return true;
  }
}

async function save() {
  if (!verify()) return;
  const filterConditions = sop.value.filterConditions.map((i) => {
    const item = {
      theme: i.theme,
      condition: i.condition,
      value: i.value,
    };
    if (i.theme === "healthIndexTheme" && i.condition === "healthIndexAbnormal" && Array.isArray(item.value)) {
      item.value = item.value.map((i) => {
        const { condition, value } = i;
        return { condition, value };
      });
    }
    return item;
  });
  const cate = cateList.value.find((i) => i._id === cateIdStr.value);
  const { _id, ...form } = { ...sop.value };
  form.filterConditions = filterConditions;
  form.classId = cateIdStr.value;
  form.className = cate && cate.label ? cate.label : sop.value.className;
  form.executeTeams = teamIds.value.map((teamId) => ({ teamId, name: teamMap.value[teamId] || sopTeamMap.value[teamId] }));
  if (!_id) {
    form.createUserId = memberInfo.value.userid;
    form.localStorage = memberInfo.value.corpId;
  }
  if (sop.value._id) form.sopTaskId = sop.value._id;
  // 获取执行人executeUserIds
  // const ids = teamIds.value.map((i) => i.teamId);
  form.executeUserIds = await getExecuteUserIds(teamIds.value, form.executeJobs);
  const fn = sop.value._id ? updateSopTask : createSopTask;
  const res = await fn(form);
  if (res.success) {
    ElMessage.success("保存成功");
    closePage();
  } else {
    ElMessage.error(res.message);
  }
}

function setFilterConditionsKey() {
  if (Array.isArray(sop.value.filterConditions)) {
    sop.value.filterConditions.forEach((i) => {
      i.key = `${Date.now()}_${Math.ceil(Math.random() * 10000)}`;
      if (i.theme === "healthIndexTheme" && i.condition === "healthIndexAbnormal" && Array.isArray(i.value)) {
        i.value.forEach((j) => {
          j.key = `${Date.now()}_${Math.ceil(Math.random() * 10000)}`;
        });
      }
    });
  }
}

function changeExecuteType(type) {
  sop.value.executeType = type;
  if (type === "manual" && !["groupmsg", "todo"].includes(sop.value.executeMethod)) {
    sop.value.enableSendContent = true;
    sop.value.executeMethod = "groupmsg";
  }
  if (type === "auto" && !["tag", "enterGroup", "sms"].includes(sop.value.executeMethod)) {
    sop.value.executeMethod = "tag";
  }
  if (type === "auto") sop.value.enableSendContent = false;
  console.log(type, sop.value.executeMethod);
}

// 获取分组列表
async function getGroupList() {
  if (sop.value.executeMethod !== "enterGroup" || teamIds.value.length === 0) return;
  let query = {
    page: 1,
    pageSize: 1000,
  };
  if (teamIds.value.length === 1) {
    query["teamId"] = teamIds.value[0];
  } else {
    query["groupType"] = "corp";
  }
  const { success, data } = await getGroups(query);
  groupList.value = success
    ? data.data.map((i) => {
        return {
          value: i._id,
          label: i.groupType === "corp" || i.parentGroupId ? `${i.groupName}(机构)` : i.groupName,
        };
      })
    : [];
}
</script>
<style lang="scss" scoped>
.text-primary {
  color: var(--el-color-primary);
}
</style>
