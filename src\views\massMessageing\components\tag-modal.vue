<template>
  <el-dialog :model-value="visible" :width="width" title="选择标签" @close="close">
    <el-scrollbar wrap-style="max-height: 50vh;">
      <div v-for="group in tags" :key="group.id" class="px-15px">
        <div class="text-14px py-12px font-semibold">{{ group.groupName }}</div>
        <div class="flex flex-wrap tag-group">
          <div v-for="item in group.tag" :key="item.id" :class="selectMap[item.id] ? 'active' : ''" class="px-12px py-6px text-12px tag-item text-center rounded-full" @click="toggle(item, group.tag)">
            {{ item.name }}
          </div>
        </div>
      </div>
    </el-scrollbar>
    <template #footer>
      <div class="flex items-center justify-between">
        <div class="w-0 flex-grow text-left mr-30px text-gray-500" :title="names">
          <tag-str :tags="tagList" />
        </div>
        <el-button type="primary" class="w-100px flex-shrink-0" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref, watch, onMounted } from "vue";
import { storeToRefs } from "pinia";
import { configStore } from "@/store/config";
const { getGroupTag } = configStore();
const { tags } = storeToRefs(configStore());
import tagStr from "./tag-str.vue";
onMounted(async () => {
  if (!tags.value.length) await getGroupTag();
});
const props = defineProps({
  visible: { type: Boolean, default: false },
  value: { type: Array, default: () => [] },
});
const selectMap = ref({});
const tagList = computed(() => Object.values(selectMap.value));
function toggle(item) {
  selectMap.value[item.id] = selectMap.value[item.id] ? null : item;
}
const emits = defineEmits(["change", "close"]);
async function confirm() {
  const tagIds = Object.values(selectMap.value).filter(Boolean);
  emits("change", tagIds);
  close();
}
function close() {
  emits("close");
}

watch(
  () => props.visible,
  (n) => {
    if (n) {
      const data = Array.isArray(props.value) ? props.value : [];
      selectMap.value = data.reduce((val, i) => {
        val[i.id] = i;
        return val;
      }, {});
    }
  }
);
</script>
<style lang="scss" scoped>
.tag-item {
  border: 1px solid #eee;
  margin-bottom: 12px;
}

.tag-group {
  margin-bottom: -12px;
}

.tag-item + .tag-item {
  margin-left: 12px;
}

.tag-item.active {
  background-color: var(--yc-primary-color);
  border-color: var(--yc-primary-color);
  color: #fff;
}
</style>