<template>
  <div class="report-container">
    <div class="report-header">
      <div class="report-actions">
        <div class="filter-section">
          <div class="filter-row">
            <div class="filter-item">
              <span class="filter-label">咨询日期：</span>
              <el-date-picker v-model="consultDateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :shortcuts="dateShortcuts" :disabled-date="disabledDate" @change="handleDateChange" class="date-range" value-format="YYYY-MM-DD" />
            </div>
            <div class="filter-item" v-if="isManager">
              <check-box-filter v-model="developerUserIds" label="开发人员" :list="developerList" @clear="clearDevelopers">
                <template #item="{ item }">
                  <ww-user :openid="item.value"></ww-user>
                </template>
              </check-box-filter>
            </div>
            <div class="filter-item">
              <filter-info-source v-model="selectedSources" />
            </div>
            <div class="filter-item">
              <span class="filter-label">咨询科室：</span>
              <dept-select v-model="deptId" placeholder="请选择科室" @change="handleDeptChange" class="dept-select" />
            </div>
            <div class="filter-item">
              <span class="filter-label">咨询项目：</span>
              <project-intent-select v-model="projectIds" :deptId="deptId" @change="handleProjectChange" placeholder="请选择咨询项目" type="filter" style="width: 200px" multiple class="project-select" />
            </div>
            <div class="filter-actions">
              <el-button type="primary" @click="handleSearch" :loading="loading" :disabled="loading">查询</el-button>
              <el-button type="success" @click="exportToExcel" :disabled="loading">导出Excel</el-button>
              <el-button @click="resetFilters" :disabled="loading">重置</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="table-container">
      <table class="performance-table">        <colgroup>
          <col style="width: 200px" />
          <!-- 项目 -->
          <col style="width: 120px" />
          <!-- 咨询人次 -->
          <col style="width: 100px" />
          <!-- 占比% -->
          <col style="width: 120px" />
          <!-- 到诊人次 -->
          <col style="width: 100px" />
          <!-- 占比% -->
          <col style="width: 120px" />
          <!-- 成交人次 -->
          <col style="width: 100px" />
          <!-- 占比% -->
        </colgroup>
        <thead>
          <tr>
            <th>咨询项目</th>
            <th>咨询人次</th>
            <th>占比%</th>
            <th>到诊人次</th>
            <th>占比%</th>
            <th>成交人次</th>
            <th>占比%</th>
          </tr>
        </thead>
        <tbody>
          <template v-for="(projectGroup, groupIndex) in projectGroupedData" :key="groupIndex">
            <!-- 项目行 -->
            <tr v-for="(item, sIndex) in projectGroup.projects" :key="`${item.projectKey}-${sIndex}`">
              <td>{{ item.projectName }}</td>
              <td>{{ item.consultCount }}</td>
              <td>{{ formatPercentage(item.consultCount / totalConsultCount) }}</td>
              <td>{{ item.visitCount }}</td>
              <td>{{ formatPercentage(item.visitRate) }}</td>
              <td>{{ item.dealCount }}</td>
              <td>{{ formatPercentage(item.dealRate) }}</td>
            </tr>
          </template>
          <tr class="total-row">
            <td class="total-label">总计：</td>
            <td>{{ totalConsultCount }}</td>
            <td></td>
            <td>{{ totalVisitCount }}</td>
            <td></td>
            <td>{{ totalDealCount }}</td>
            <td></td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="footer-info">
      <span class="date-limit-info">* 查询限制为一个月内的数据</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { ElButton, ElMessage, ElDatePicker } from "element-plus";
import { CheckBoxFilter, filterInfoSource } from "@/components/filter-bar";
import { storeToRefs } from "pinia";
import { staffStore } from "@/store/staff";
import { memberStore } from "@/store/member";
import { teamStore } from "@/store/team";
import { getEConsultProjectStatistics } from "@/api/bill-record";
import { getProjectIntentNames } from "@/api/benefitManagement";
import DeptSelect from "@/components/dept-components/dept-select.vue";
import projectIntentSelect from "@/components/benefit-management/project-intent-select.vue";
import { useDeptStore } from "@/components/dept-components/dept-store";

// 用户筛选
const selectedSources = ref([]);
const consultDateRange = ref([]);

// 新增项目和科室筛选变量
const projectIds = ref([]);
const selectedProjects = ref([]);
const deptId = ref("");
const selectedDept = ref({});
const deptStore = useDeptStore();

// 从store获取员工列表和管理员状态
const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());
const { isAdmin } = storeToRefs(memberStore());
const { managerList } = storeToRefs(teamStore());
const { judgmentIsAdmin } = memberStore();
// 添加管理员判断逻辑
const isManager = computed(() => {
  return managerList.value.includes(90007) || isAdmin.value;
});

// 添加开发人员列表计算属性，只显示开发人员角色
const developerList = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("onlineCustomerService")).map((i) => ({ value: i.userid }));
});

const selectedDevelopers = ref([]);
const hasExplicitlyClearedDevelopers = ref(false);

const developerUserIds = computed({
  get() {
    if (selectedDevelopers.value.length > 0) {
      return selectedDevelopers.value;
    }
    if (hasExplicitlyClearedDevelopers.value) {
      return [];
    }
    const currentUserId = localStorage.getItem("userId");
    if (!isManager.value) {
      // 不是主管，只能看到自己
      return [currentUserId];
    } else {
      // 主管
      const inList = developerList.value.some((i) => i.value === currentUserId);
      if (inList) {
        return [currentUserId];
      } else {
        // 展示全部
        return [];
      }
    }
  },
  set(newVal) {
    selectedDevelopers.value = newVal;
    hasExplicitlyClearedDevelopers.value = false;
  },
});

function clearDevelopers() {
  selectedDevelopers.value = [];
  hasExplicitlyClearedDevelopers.value = true;
}

// 日期快捷选项
const dateShortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近半个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
];

// 禁用超过一个月范围的日期
const disabledDate = (time) => {
  if (!consultDateRange.value || consultDateRange.value.length === 0) {
    return false;
  }

  const startDate = new Date(consultDateRange.value[0]);
  const endDate = new Date(consultDateRange.value[1]);
  const currentDate = new Date(time);

  // 如果已选择了开始日期，限制结束日期不能超过开始日期后的31天
  if (consultDateRange.value.length === 1) {
    const oneMonthLater = new Date(startDate);
    oneMonthLater.setDate(startDate.getDate() + 31);
    return currentDate > oneMonthLater;
  }

  return false;
};

// 添加科室变化处理函数
function handleDeptChange(dept) {
  selectedDept.value = dept;
  projectIds.value = [];
}

// 监听日期变化，确保不超过一个月
watch(consultDateRange, (newValue) => {
  if (newValue && newValue.length === 2) {
    const start = new Date(newValue[0]);
    const end = new Date(newValue[1]);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays > 31) {
      // 如果超过31天，将结束日期设置为开始日期后的31天
      const maxEnd = new Date(start);
      maxEnd.setDate(start.getDate() + 31);
      const formattedMaxEnd = formatDate(maxEnd);

      consultDateRange.value = [newValue[0], formattedMaxEnd];
      ElMessage.warning("查询范围已限制为一个月内");
    }
  }
});

// 格式化日期为YYYY-MM-DD
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  return `${year}-${month}-${day}`;
};

// 原始数据和筛选后的表格数据
const originalData = ref([]);
const tableData = ref([]);

// 用于存储项目ID到名称的映射
const projectNameMap = ref({});

// 修改获取项目统计数据函数，采用与e-store-report相同的方法处理科室
async function fetchProjectStatistics() {
  try {
    // 构建查询参数 - 包含所有筛选条件
    const today = new Date();
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(today.getMonth() - 1);

    const params = {
      startDate: consultDateRange.value?.[0] || formatDate(oneMonthAgo),
      endDate: consultDateRange.value?.[1] || formatDate(today),
    };

    // 添加所有筛选条件到API参数（除了项目ID）
    // 注意：我们不再将项目ID发送到后端，而是保存到本地用于前端筛选

    // 添加信息来源筛选
    if (selectedSources.value && selectedSources.value.length > 0) {
      params.infoSource = selectedSources.value;
    }

    // 添加开发人员筛选 - 根据管理员权限处理
    if (isManager.value) {
      // 管理员可以筛选多个开发人员
      if (developerUserIds.value && developerUserIds.value.length > 0) {
        params.developerUserIds = developerUserIds.value;
      }
    } else {
      // 非管理员只能查看自己的数据
      params.developerUserIds = [localStorage.getItem("userId")];
    }

    if (deptId.value) {
      params.deptId = deptId.value; // 添加科室ID筛选条件
      params.projectIds = (await deptStore.fetchProjectIdsByDeptId(deptId.value)) || [];
    }

    // 项目筛选
    if (projectIds.value && projectIds.value.length > 0) {
      params.projectIds = projectIds.value;
    }

    const { data, success, message } = await getEConsultProjectStatistics(params);

    if (success) {
      // 保存原始数据
      originalData.value = data.data;

      // 收集所有项目ID
      const allProjectIds = [];
      data.data.forEach((record) => {
        // 收集 projectId 和 projectIds 数组中的所有项目ID
        if (record.projectId) {
          allProjectIds.push(record.projectId);
        }

        // 处理 projectIds 数组
        const projectIds = Array.isArray(record.projectIds) ? record.projectIds : record.projectIds ? [record.projectIds] : [];
        allProjectIds.push(...projectIds);
      });

      // 去重项目ID
      const uniqueProjectIds = [...new Set(allProjectIds)];

      // 如果有项目ID，调用接口获取项目名称
      if (uniqueProjectIds.length > 0) {
        try {
          const { data: nameData, success: nameSuccess } = await getProjectIntentNames({ ids: uniqueProjectIds });
          if (nameSuccess && nameData) {
            // 将返回的数组转换为ID到名称的映射
            const nameMap = {};
            nameData.data.forEach((item) => {
              nameMap[item._id] = item.projectName;
            });
            projectNameMap.value = nameMap;
          }
        } catch (error) {
          console.error("获取项目名称失败:", error);
        }
      }

      // 处理数据：展开projectIds并按项目分组合并
      const expandedProjects = [];

      // 1. 遍历每条咨询记录，展开项目IDs
      data.data.forEach((record) => {
        // 确保projectIds是数组
        const projectIds = Array.isArray(record.projectIds) ? record.projectIds : record.projectIds ? [record.projectIds] : [];

        // 如果有单独的projectId，也添加到数组中（避免丢失原始projectId）
        if (record.projectId && !projectIds.includes(record.projectId)) {
          projectIds.push(record.projectId);
        }

        // 如果没有项目，添加一个"未知项目"记录
        if (projectIds.length === 0) {
          expandedProjects.push({
            ...record,
            projectId: "unknown",
            projectName: "未知项目",
            source: record.source || "unknown", // 保留信息来源
          });
        } else {
          // 对每个项目ID创建一条记录
          projectIds.forEach((projectId) => {
            // 优先使用record中的projectInfos，如果没有，则使用getProjectIntentNames获取的映射
            const projectInfo = record.projectInfos?.find((p) => p.id === projectId);
            const projectName = projectInfo?.name || projectNameMap.value[projectId] || record.projectName || projectId;

            expandedProjects.push({
              ...record,
              projectId: projectId,
              projectName: projectName,
              // 确保每个记录都有基础数据
              isVisited: record.inHospitalTimes && record.inHospitalTimes.length > 0,
              isDeal: (record.consumeRecordCount || 0) > 0,
              // 确保信息来源正确传递
              source: record.source || "unknown",
            });
          });
        }
      });

      // 2. 按项目ID分组合并数据
      const projectGroups = {};
      expandedProjects.forEach((record) => {
        const projectKey = record.projectId || "unknown";

        if (!projectGroups[projectKey]) {
          projectGroups[projectKey] = {
            projectKey,
            projectName: record.projectName || "未知项目",
            consultCount: 0,
            visitCount: 0,
            dealCount: 0,
            source: record.source, // 保留信息来源
            userId: record.userId, // 保留用户ID用于筛选
          };
        }

        // 累加统计数据
        projectGroups[projectKey].consultCount += 1;
        projectGroups[projectKey].visitCount += record.isVisited ? 1 : 0; // 到诊人次
        projectGroups[projectKey].dealCount += record.isDeal ? 1 : 0; // 成交人次
      });

      // 转换为数组并计算比率
      tableData.value = Object.values(projectGroups).map((item) => {
        // 计算到诊占比和成交占比
        const visitRate = item.consultCount > 0 ? item.visitCount / item.consultCount : 0;
        const dealRate = item.consultCount > 0 ? item.dealCount / item.consultCount : 0;

        return {
          ...item,
          visitRate,
          dealRate,
        };
      });
    } else {
      ElMessage.error(message || "获取统计数据失败");
      tableData.value = [];
    }
  } catch (error) {
    ElMessage.error("获取统计数据出错：" + (error.message || error));
    tableData.value = [];
    console.error("获取统计数据出错:", error);
  }
}

// 修改前端筛选逻辑，从tableData中筛选出符合项目条件的数据
const filteredTableData = computed(() => {
  // 如果没有选择项目，则返回所有数据
  if (!selectedProjects.value || selectedProjects.value.length === 0) {
    return tableData.value;
  }

  // 根据选择的项目ID进行筛选
  return tableData.value.filter((item) => {
    // 检查此项目ID是否在选中列表中
    return selectedProjects.value.includes(item.projectKey);
  });
});

// 按项目分组的数据 - 使用filteredTableData而不是原始tableData
const projectGroupsMap = computed(() => {
  // 创建项目分组
  const projectMap = {};

  filteredTableData.value.forEach((item) => {
    // 获取项目类型（可以根据项目ID的某些特征来分组）
    // 这里简单实现，假设所有项目都归为一组
    const projectType = "all";

    if (!projectMap[projectType]) {
      projectMap[projectType] = {
        projectType,
        projects: [],
        totalConsultCount: 0,
        totalVisitCount: 0,
        totalDealCount: 0,
      };
    }

    // 添加项目数据
    projectMap[projectType].projects.push(item);

    // 更新项目组汇总数据
    projectMap[projectType].totalConsultCount += item.consultCount || 0;
    projectMap[projectType].totalVisitCount += item.visitCount || 0;
    projectMap[projectType].totalDealCount += item.dealCount || 0;
  });

  // 计算比率
  Object.values(projectMap).forEach((group) => {
    group.totalVisitRate = group.totalConsultCount > 0 ? group.totalVisitCount / group.totalConsultCount : 0;
    group.totalDealRate = group.totalConsultCount > 0 ? group.totalDealCount / group.totalConsultCount : 0;
  });

  return projectMap;
});

// 列表形式的项目分组数据
const projectGroupedData = computed(() => {
  return Object.values(projectGroupsMap.value);
});

// 计算总计数据 - 基于筛选后的数据
const totalConsultCount = computed(() => {
  return filteredTableData.value.reduce((sum, item) => sum + (item.consultCount || 0), 0);
});

const totalVisitCount = computed(() => {
  return filteredTableData.value.reduce((sum, item) => sum + (item.visitCount || 0), 0);
});

const totalVisitRate = computed(() => {
  return totalConsultCount.value > 0 ? totalVisitCount.value / totalConsultCount.value : 0;
});

const totalDealCount = computed(() => {
  return filteredTableData.value.reduce((sum, item) => sum + (item.dealCount || 0), 0);
});

const totalDealRate = computed(() => {
  return totalConsultCount.value > 0 ? totalDealCount.value / totalConsultCount.value : 0;
});

// 格式化百分比
const formatPercentage = (value) => {
  if (value === undefined || value === null || isNaN(value)) {
    return "0.00%";
  }
  return `${(value * 100).toFixed(2)}%`;
};

// 处理日期变化
const handleDateChange = () => {
  if (consultDateRange.value && consultDateRange.value.length === 2) {
    fetchProjectStatistics();
  }
};

// 处理项目变化
function handleProjectChange(projects) {
  selectedProjects.value = projects;
  // 不再需要重新调用API，只需更新前端筛选条件
}

// 重置筛选条件
const resetFilters = () => {
  // 设置默认日期范围为当月
  const today = new Date();
  const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
  const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

  consultDateRange.value = [formatDate(firstDayOfMonth), formatDate(lastDayOfMonth)];
  selectedSources.value = [];

  selectedDevelopers.value = [];

  // 重置项目和科室筛选
  projectIds.value = [];
  deptId.value = "";
  selectedDept.value = {};
  selectedProjects.value = [];

  // 重置后重新获取数据
  fetchProjectStatistics();
};

// 处理查询 - 触发前端筛选
const handleSearch = () => {
  if (consultDateRange.value && consultDateRange.value.length === 2) {
    const start = new Date(consultDateRange.value[0]);
    const end = new Date(consultDateRange.value[1]);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays > 31) {
      ElMessage.warning("查询范围已限制为一个月内");
      return;
    }

    // 获取新的日期范围的数据
    fetchProjectStatistics();
  } else {
    ElMessage.warning("请选择咨询日期范围");
  }
};

// 导出Excel
const exportToExcel = () => {
  // 准备导出数据
  const exportData = [];

  // 添加标题信息
  exportData.push(["项目统计表"]);

  // 添加筛选条件信息
  if (consultDateRange.value && consultDateRange.value.length === 2) {
    exportData.push(["咨询日期范围", `${consultDateRange.value[0]} 至 ${consultDateRange.value[1]}`]);
  }

  if (developerUserIds.value.length > 0) {
    const developerNames = developerUserIds.value
      .map((id) => {
        const staff = staffList.value.find((s) => s.userid === id);
        return staff ? staff.anotherName || id : id;
      })
      .join(", ");
    exportData.push(["开发人员", developerNames]);
  }

  if (selectedSources.value.length > 0) {
    const sourcesText = selectedSources.value.join(", ");
    exportData.push(["咨询来源", sourcesText]);
  }

  exportData.push([]); // 空行

  // 添加表头
  exportData.push(["项目", "咨询人次", "占比%", "到诊人次", "占比%", "成交人次", "占比%"]);

  // 添加按项目分组的数据
  projectGroupedData.value.forEach((projectGroup) => {
    // 添加每个项目的数据
    projectGroup.projects.forEach((item) => {
      exportData.push([item.projectName || "未知项目", item.consultCount, `${((item.consultCount / totalConsultCount.value) * 100).toFixed(2)}%`, item.visitCount, `${(item.visitRate * 100).toFixed(2)}%`, item.dealCount, `${(item.dealRate * 100).toFixed(2)}%`]);
    });

    // 添加小计行
    if (projectGroup.projects.length > 1) {
      exportData.push(["小计", projectGroup.totalConsultCount, `${((projectGroup.totalConsultCount / totalConsultCount.value) * 100).toFixed(2)}%`, projectGroup.totalVisitCount, `${(projectGroup.totalVisitRate * 100).toFixed(2)}%`, projectGroup.totalDealCount, `${(projectGroup.totalDealRate * 100).toFixed(2)}%`]);
    }
  });

  // 添加总计行
  exportData.push(["总计", totalConsultCount.value, "100.00%", totalVisitCount.value, `${(totalVisitRate.value * 100).toFixed(2)}%`, totalDealCount.value, `${(totalDealRate.value * 100).toFixed(2)}%`]);

  // 创建工作簿和工作表
  const wb = XLSX.utils.book_new();
  const ws = XLSX.utils.aoa_to_sheet(exportData);

  // 设置列宽
  const colWidths = [
    { wch: 20 }, // 项目
    { wch: 12 }, // 咨询人次
    { wch: 12 }, // 占比%
    { wch: 12 }, // 到诊人次
    { wch: 12 }, // 占比%
    { wch: 12 }, // 成交人次
    { wch: 12 }, // 占比%
  ];

  ws["!cols"] = colWidths;

  // 合并标题单元格
  ws["!merges"] = [
    { s: { r: 0, c: 0 }, e: { r: 0, c: 6 } }, // 标题行
  ];

  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(wb, ws, "电网咨询项目表");

  // 生成Excel文件并下载
  const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
  const blob = new Blob([excelBuffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });

  // 使用当前日期作为文件名的一部分
  const now = new Date();
  const fileName = `电网咨询项目表_${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}.xlsx`;

  saveAs(blob, fileName);

  ElMessage.success("导出Excel成功");
};

onMounted(async () => {
  // 确保科室列表已加载
  await deptStore.fetchDeptList();
  await judgmentIsAdmin();
  // 加载员工列表并判断管理员权限
  if (staffList.value.length === 0) await getStaffList();

  // 设置默认日期范围为当月
  const end = new Date();
  const start = new Date(end.getFullYear(), end.getMonth(), 1);
  consultDateRange.value = [formatDate(start), formatDate(end)];
  // 初始加载数据
  fetchProjectStatistics();
});

// 修改科室变化监听 - 科室和开发人员变化时重新获取数据，项目变化不再触发API调用
watch([selectedSources, developerUserIds, deptId], () => {
  if (consultDateRange.value && consultDateRange.value.length === 2) {
    fetchProjectStatistics();
  }
});

// 单独监听项目选择变化 - 只更新前端筛选，不调用API
watch(projectIds, (newProjectIds) => {
  selectedProjects.value = newProjectIds || [];
});
</script>

<style scoped>
.report-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
  background-color: #f5f7fa;
  overflow: hidden;
  min-width: 1200px;
}

.report-header {
  background-color: white;
  padding: 15px 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
}

.filter-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
  margin-bottom: 10px;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-label {
  white-space: nowrap;
  margin-right: 5px;
  font-size: 14px;
  color: #606266;
}

.filter-actions {
  margin-left: auto;
  display: flex;
  gap: 10px;
}

.date-range,
.source-select {
  min-width: 220px;
}

.dept-select,
.project-select {
  min-width: 180px;
}

:deep(.el-date-editor.el-input__wrapper),
:deep(.el-select) {
  width: 100%;
  flex: 1;
}

.table-container {
  flex: 1;
  margin: 15px 0 0 0;
  background-color: white;
  overflow: auto;
  position: relative;
  border-top: 1px solid #ebeef5;
  border-bottom: 1px solid #ebeef5;
}

.performance-table {
  width: 100%;
  min-width: 860px; 
  border-collapse: collapse;
  font-size: 14px;
  table-layout: fixed;
}

.performance-table th,
.performance-table td {
  border: 1px solid #ebeef5;
  padding: 12px 8px;
  text-align: center;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.performance-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 10;
}

.total-row {
  background-color: #ebeef5;
  font-weight: bold;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.subtotal-row {
  background-color: #f8f8f8;
  font-weight: bold;
}

.total-label {
  text-align: right;
  padding-right: 10px;
}

.footer-info {
  background-color: white;
  padding: 10px 20px;
  margin: 0;
  display: flex;
  justify-content: end;
  color: #606266;
  font-size: 14px;
  flex-shrink: 0;
  border-top: 1px solid #ebeef5;
}

.date-limit-info {
  color: #e6a23c;
  font-style: italic;
}

@media print {
  .report-container {
    background-color: white;
    height: auto;
  }

  .report-header {
    box-shadow: none;
  }

  .report-actions,
  .footer-info {
    display: none;
  }

  .table-container {
    margin: 0;
    border: none;
    overflow: visible;
    height: auto;
  }

  .performance-table th,
  .performance-table td {
    font-size: 12px;
    padding: 8px 4px;
  }
}

@media (max-width: 768px) {
  .filter-item {
    max-width: 100%;
  }

  .report-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .filters-row {
    flex-direction: column;
  }

  .filter-item {
    width: 100%;
  }

  .filter-controls {
    margin-top: 10px;
    width: 100%;
    justify-content: space-between;
  }

  .dept-select,
  .project-select {
    width: 100%;
  }
}

@media (min-width: 1400px) {
  .performance-table .amount {
    min-width: 120px;
    max-width: 150px;
  }
}
</style>
