<template>
  <count-to :startVal='startVal' ref="countToRef" :endVal='endVal' :duration='duration' :decimals="decimals"
    :suffix="suffix" @click="animate()" />
</template>
<script setup>
import { ref, watch } from 'vue';
import { CountTo } from 'vue3-count-to';

const props = defineProps({
  decimals: {
    type: Number,
    default: 0
  },
  startVal: {
    type: Number,
    default: 0
  },
  endVal: {
    type: Number,
    default: 0
  },
  duration: {
    type: Number,
    default: 1500
  },
  suffix: {
    type: String, default: ''
  }
})

const countToRef = ref(null)

function animate() {
  countToRef.value && countToRef.value.start()
}

watch(() => props.endVal, animate)



</script>
<style lang="scss" scoped></style>
