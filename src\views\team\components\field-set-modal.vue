<template>
  <el-dialog :model-value="visible" :title="title" :width="width" @close="close()">
    <el-scrollbar wrap-style="max-height: calc(85vh - 200px)">
      <el-form class="px-10px pt-10px" label-position="top">
        <el-form-item>
          <template #label> <span color-normal>已选字段</span> </template>
          <div class="flex-grow flex flex-wrap pb-20px border-b border-gray-200">
            <div class="flex-grow flex flex-wrap" :class="list.length ? '' : 'pb-15px'">
              <div v-for="i in selections" :key="'selection_' + i.title"
                class="w-90px relative flex-shrink-0 px-10px py-6px mr-10px mb-15px flex items-center justify-center text-14px leading-4 rounded border-1 border-gray-200 hover:border-blue-500">
                {{ i.name }}
                <el-icon v-if="!i.required" class="absolute -right-9px -top-9px cursor-pointer text-red-500" size="18"
                  @click="toggle(i)">
                  <CircleCloseFilled />
                </el-icon>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item>
          <template #label> <span color-normal>{{title}}</span> <span class="ml-4px text-12px text-gray-500">请点击选择</span>
          </template>
          <div class="flex-grow flex flex-wrap" :class="list.length ? '' : 'pb-15px'">
            <div v-for="i in list" :key="i.title"
              :class="choice[i.title] ? 'text-white bg-orange-500' : 'text-gray-500 bg-gray-100'"
              class="w-90px flex-shrink-0 px-10px py-8px mr-10px mb-15px flex items-center justify-center text-14px leading-4 rounded cursor-pointer hover:text-white hover:bg-orange-500"
              @click="toggle(i)">
              {{ i.name }}
            </div>
          </div>
        </el-form-item>
      </el-form>
    </el-scrollbar>

    <template #footer>
      <div text-center>
        <el-button class="w-100px" @click="close">取消</el-button>
        <el-button class="w-100px" type="primary" @click="confirm()"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref, watch } from 'vue';

const emits = defineEmits(['close', 'confirm']);
const props = defineProps({
  title: { type: String, default: '' },
  width: { type: Number },
  data: { type: Object, default: () => ({}) },
  list: { type: Array, default: () => ([]) },
  visible: { type: Boolean, default: false },
})

const selections = computed(() => props.list.filter(i => choice.value[i.title]))

const choice = ref({});

watch(() => props.visible, n => {
  if (n) {
    choice.value = props.list.reduce((val, item) => {
      if (item.required || props.data.some(i => i.title === item.title)) val[item.title] = true;
      return val
    }, {})
    // form.value = { ...props.data };
  }
})

function close() {
  emits('close')
}

function confirm() {
  emits('confirm', selections.value.map(i => ({ ...i })));
  close()
}

function toggle(i) {
  if (i.required) return;
  choice.value[i.title] = !choice.value[i.title]
}
</script>
<style scoped>
.append-select {
  width: 80px;
}

.title-bar {
  position: relative;
  padding-left: 10px;
  margin-bottom: 15px;
  font-size: 14px;
  font-weight: bold;
  color: var(--yc-main-text-color);
}

.modal-footer {
  /* padding-top: 15px; */
  text-align: center;
}

.mr-20 {
  margin-right: 20px;
}
</style>