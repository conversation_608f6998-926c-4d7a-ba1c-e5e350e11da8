<template>
  <el-dialog :close-on-click-modal="false" :model-value="visible" title="批量编辑" :width="width" @close="close">
    <el-form class="p-15px" label-suffix="：" :label-width="100">
      <el-form-item label="修改字段">
        <el-select v-model="modifyField" class="w-full" clearable placeholder="请选择修改字段">
          <el-option v-for="item in fieldList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <div v-if="modifyField" class="border-t border-gray-200 mb-15px"></div>

      <customer-source-edit v-if="modifyField === 'customerSource'" v-model:customerSource="customerSource.customerSource" v-model:referenceType="customerSource.referenceType" v-model:reference="customerSource.reference" v-model:referenceCustomerId="customerSource.referenceCustomerId" v-model:referenceUserId="customerSource.referenceUserId" />

      <el-form-item v-else-if="modifyField === 'customerStage'" class="mb-0" label="所处阶段">
        <el-select v-model="customerStage" class="w-full" clearable placeholder="请选择所处阶段">
          <el-option v-for="item in stageList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>

      <tag-edit-panel v-else-if="modifyField === 'tagIds'" v-model:tagIds="tagIds" v-model:tagType="tagType" />
    </el-form>
    <template #footer>
      <div text-center>
        <el-button type="primary" plain class="w-100px" @click="close()">取消</el-button>
        <el-button :loading="loading" class="w-100px" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { ElMessage } from "element-plus";
import { configStore } from "@/store/config";
import { batchUpdateCustomer } from "@/api/member";

import customerSourceEdit from "./customer-source-edit.vue";
import tagEditPanel from "./tag-edit-panel.vue";

const emits = defineEmits(["close", "change"]);
const props = defineProps({
  customers: { type: Object, default: () => ({}) },
  visible: { type: Boolean, default: false },
  width: { type: Number },
});
const config = configStore();
const { stageList } = storeToRefs(config);
const loading = ref(false);
const modifyField = ref("");
const customerStage = ref("");
const tagIds = ref([]);
const tagType = ref("append");
const fieldList = [
  { label: "来源", value: "customerSource" },
  { label: "标签", value: "tagIds" },
  { label: "所处阶段", value: "customerStage" },
];

const customerSource = ref({
  customerSource: [],
  referenceType: "",
  reference: "",
  referenceCustomerId: "",
  referenceUserId: "",
});

async function confirm() {
  if (modifyField.value === "customerSource") {
    verifyCustomerSource() && save({ ...customerSource.value });
  } else if (modifyField.value === "customerStage") {
    if (customerStage.value) {
      save({ customerStage: customerStage.value });
    } else {
      ElMessage.warning("请选择所处阶段");
    }
  } else if (modifyField.value === "tagIds") {
    if (tagType.value === "add" && tagIds.value.length === 0) {
      close();
    } else {
      save({ tagIds: tagIds.value, tagType: tagType.value });
    }
  }
}

async function save(data) {
  const params = {
    customerIds: props.customers.map((item) => item._id),
    corpId: localStorage.getItem("corpId"),
    modifyField: modifyField.value,
    ...data,
  };
  loading.value = true;
  const { success, message } = await batchUpdateCustomer(params);
  if (success) {
    ElMessage.success("批量修改成功");
    emits("change", modifyField.value);
  } else {
    ElMessage.error(message || "批量修改失败");
  }
  loading.value = false;
}
function verifyCustomerSource() {
  if (customerSource.value.customerSource.length === 0) {
    ElMessage.warning("请选择客户来源");
  } else if (customerSource.value.referenceType === "同事" && !customerSource.value.referenceUserId) {
    ElMessage.warning("请选择推荐同事");
  } else if (customerSource.value.referenceType === "客户" && !customerSource.value.referenceCustomerId) {
    ElMessage.warning("请选择推荐客户");
  } else {
    return true;
  }
}

function close() {
  emits("close");
}
watch(
  () => props.visible,
  (n) => {
    if (n) {
      modifyField.value = "";
      tagIds.value = [];
      tagType.value = "append";
      customerStage.value = "";
      customerSource.value.customerSource = [];
    }
  }
);
</script>