import { teamStore } from "@/store/team";
import { post } from "./axios";
async function useCorp(data) {
  const res = await post("corp", data);
  return res;
}

export async function getProjectIntentList(data) {
  const res = await useCorp({ type: "getProjectIntentList", ...data });
  return res;
}

export async function addProjectIntent(data) {
  const res = await useCorp({ type: "addProjectIntent", ...data });
  return res;
}

export async function updateProjectIntent(data) {
  const res = await useCorp({ type: "updateProjectIntent", ...data });
  return res;
}

export async function getProjectIntentNames(data) {
  const res = await useCorp({ type: "getProjectIntentNames", ...data });
  return res;
}

export async function updateProjectIntentStatus(data) {
  const res = await useCorp({ type: "updateProjectIntentStatus", ...data });
  return res;
}

export async function getSourceList(data) {
  const res = await useCorp({ type: "getSourceList", ...data });
  return res;
}

export async function addSource(data) {
  const res = await useCorp({ type: "addSource", ...data });
  return res;
}

export async function updateSource(data) {
  const res = await useCorp({ type: "updateSource", ...data });
  return res;
}

export async function updateSourceStatus(data) {
  const res = await useCorp({ type: "updateSourceStatus", ...data });
  return res;
}

export async function deleteSource(data) {
  const res = await useCorp({ type: "deleteSource", ...data });
  return res;
}

// 新增分类相关API
export async function getSourceCateList(data) {
  const res = await useCorp({ type: "getSourceCateList", ...data });
  return res;
}

export async function addSourceCate(data) {
  const res = await useCorp({ type: "addSourceCate", ...data });
  return res;
}

export async function updateSourceCate(data) {
  const res = await useCorp({ type: "updateSourceCate", ...data });
  return res;
}

export async function deleteSourceCate(data) {
  const res = await useCorp({ type: "deleteSourceCate", ...data });
  return res;
}

export async function sortSourceCate(data) {
  const res = await useCorp({ type: "sortSourceCate", ...data });
  return res;
}
