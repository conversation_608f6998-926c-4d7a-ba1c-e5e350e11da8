<template>
  <my-layout :radius="8">
    <layout-item>
      <div p-15 border-bottom>
        <slot name="header"></slot>
        <el-input v-model="name" flex-grow :placeholder="placeholder">
          <template #append>
            <el-button :icon="Search" @click="search" />
          </template>
        </el-input>
        <slot name="prepend"></slot>
      </div>
    </layout-item>
    <layout-main>
      <el-tree accordion ref="treeRef" :default-expanded-keys="defaultExpandedKeys" :draggable="canDrag" :style="style" :data="treeData" :allow-drag="allowDrag" :allow-drop="allowDrop" node-key="_id" :props="{ class: 'classify-list-tree' }" :expand-on-click-node="false" :filter-node-method="filterNode" @node-drag-start="handleDragStart" @node-drop="nodeDrop" @node-click="onNodeClick">
        <template #default="{ node, data }">
          <div class="w-0 flex-grow flex items-center h-full px-15px border-b border-gray-200">
            <div class="flex-shrink-0 mr-6px text-14px" :class="data.children && data.children.length ? '' : 'opacity-0'">
              <el-icon v-if="node.expanded">
                <CaretBottom />
              </el-icon>
              <el-icon v-else>
                <CaretRight />
              </el-icon>
            </div>
            <div class="flex-shrink-0 text-16px mr-6px text-yellow-500">
              <el-icon>
                <Folder />
              </el-icon>
            </div>

            <div class="w-0 flex-grow truncate" :class="checkedCate && checkedCate._id === data._id ? 'text-blue-500' : ''">
              {{ node.label }}
            </div>
            <div class="flex-shrink-0 flex items-center" @click.stop="() => {}">
              <el-dropdown flex-shrink-0>
                <el-icon class="outline-none">
                  <MoreFilled />
                </el-icon>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item v-if="data.level < level" @click.stop="addChild(data)">
                      <div class="px-12px text-14px py-4px">{{ getLabel(data, "add") }}</div>
                    </el-dropdown-item>
                    <el-dropdown-item @click.stop="rename(data)">
                      <div class="px-12px text-14px py-4px" color-primary>{{ getLabel(data, "edit") }}</div>
                      <!-- {{custom ? '编辑':'重命名'}} -->
                    </el-dropdown-item>
                    <el-dropdown-item @click.stop="remove(data)">
                      <div class="px-12px text-14px py-4px" color-danger>{{ getLabel(data, "remove") }}</div>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>

            <span v-if="canDrag" class="ml-8px flex-shrink-0" title="拖拽可进行排序">
              <svg-icon style="color: #999" size="16" name="drag"></svg-icon>
            </span>
          </div>
        </template>
      </el-tree>
    </layout-main>
    <layout-item>
      <div p-15 class="border-t border-gray-200">
        <el-button class="w-full" type="primary" @click="add()">新增院区</el-button>
      </div>
    </layout-item>
  </my-layout>
  <el-dialog v-model="visible" :title="current._id ? `重命名${label}` : `新增${label}`" :width="width">
    <div p-15>
      <div v-if="!current._id" color-normal class="mb-10px">
        <span v-if="current.parent" color-normal font-14>在【{{ current.parent.label }}】下方新增{{ label }}</span>
        <span v-else color-normal font-14>添加一级{{ label }}</span>
      </div>
      <el-input v-model="current.label" :maxlength="10" :placeholder="current.label || `请输入${label}名称`" />
    </div>
    <template #footer>
      <div text-center>
        <el-button @click="close()">取消</el-button>
        <el-button type="primary" @click="save()">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, nextTick, ref } from "vue";
import { watchDebounced } from "@vueuse/core";
import useModal from "@/hooks/useModal";
import { getCateTreeData } from "@/utils/index";

import { Search, CaretBottom, CaretRight, Folder, MoreFilled } from "@element-plus/icons-vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import SvgIcon from "@/components/svg-icon";
import { ElMessage, ElMessageBox } from "element-plus";

const default_label = {
  add: "新增科室",
  edit: "编辑",
  remove: "删除",
};

const emits = defineEmits(["change", "search", "onHandle"]);
const props = defineProps({
  addMethod: {
    type: Function,
    default: () => {},
  },
  checkedCate: {
    type: Object,
    default: () => ({}),
  },
  custom: { type: Boolean, default: false },
  label: { type: String, default: "分类" },
  labelOption: {
    default: () => ({}),
    type: [Object, Function],
  },
  level: { type: Number, default: 4 },
  data: {
    type: Array,
    default: () => [],
  },
  placeholder: { type: String, default: "请输入分类名称" },
  removeMethod: {
    type: Function,
    default: () => {},
  },
  sortMethod: {
    type: Function,
    default: () => {},
  },
  updateMethod: {
    type: Function,
    default: () => {},
  },
});

const style = {
  "--el-tree-node-content-height": "40px",
};

const name = ref("");
const treeRef = ref();
const treeExpandMap = ref({});
const treeData = ref();
const canDrag = computed(() => name.value.trim() === "");
const defaultExpandedKeys = ref([]);

function search() {
  emits("search", name.value.trim());
}

function filterNode(value, data) {
  return value ? data.label.includes(value) : true;
}

async function onNodeClick(data, node, treeNode) {
  // 安全检查 checkedCate 是否存在
  const currentCheckedId = props.checkedCate?._id;
  
  if (data._id !== currentCheckedId) {
    emits("change", { label: data.label, _id: data._id, childrenIds: data.childrenIds, level: data.level, type: data.type, districtName: data.districtName, deptName: data.deptName, districtId: data.districtId, deptId: data.deptId });
  }
  if (data.children && data.children.length) {
    // node.expanded = !node.expanded;
    node.expanded = !node.expanded;
    treeExpandMap.value[data._id] = node.expanded;
    defaultExpandedKeys.value = Object.keys(treeExpandMap.value).filter((key) => treeExpandMap.value[key]);
  }
  // const nodesMap = treeRef.value.store.nodesMap;
  // if (!nodesMap[data._id]) return;
  // if (data.children && data.children.length) {
  //   nodesMap[data._id].expanded = !nodesMap[data._id].expanded;
  //   treeExpandMap.value = Object.keys(nodesMap).reduce((acc, key) => {
  //     const node = nodesMap[key];
  //     if (node.expanded) {
  //       acc[key] = node.expanded;
  //     }
  //     return acc;
  //   }, {});
  //   defaultExpandedKeys.value = Object.keys(treeExpandMap.value);
  // } else {
  //   nodesMap[data._id].checked = !nodesMap[data._id].checked;
  // }
}

function allowDrag(node) {
  // 所有层级都可以拖拽排序
  return true;
}

function allowDrop(draggingNode, dropNode, type) {
  // 只允许同级节点之间的拖拽，且只能是前后位置调整
  if (!["next", "prev"].includes(type)) {
    return false;
  }
  
  // 获取两个节点的父节点ID
  const draggingParentId = draggingNode.data.parentId || null;
  const dropParentId = dropNode.data.parentId || null;
  
  // 必须是同一个父节点下的子节点才能排序
  return draggingParentId === dropParentId;
}

function handleDragStart(node, event) {}

async function nodeDrop(draggingNode, dropNode, dropType) {
  // 获取被拖拽节点的父节点ID
  const parentId = draggingNode.data.parentId || null;
  
  // 收集同一父节点下的所有兄弟节点，并重新排序
  let siblings = [];
  
  if (parentId === null) {
    // 一级节点（院区）
    siblings = treeData.value.slice();
  } else {
    // 找到父节点，获取其所有子节点
    const findSiblings = (nodes) => {
      for (const node of nodes) {
        if (node._id === parentId) {
          return node.children || [];
        }
        if (node.children && node.children.length > 0) {
          const result = findSiblings(node.children);
          if (result.length > 0) return result;
        }
      }
      return [];
    };
    siblings = findSiblings(treeData.value);
  }
  
  // 生成新的排序数据
  const changedOrder = siblings.map((item, idx) => ({ 
    _id: item._id, 
    label: item.label, 
    sort: idx 
  }));
  
  if (props.custom) {
    // 通过事件传递排序数据
    emits("onHandle", { action: 'sort', data: changedOrder });
  } else {
    // 直接调用排序方法
    const { success, message } = await props.sortMethod(changedOrder);
    if (!success) {
      ElMessage.error(message);
    }
  }
}

const { close, show, visible, width } = useModal(560);
const current = ref({});
function add() {
  if (props.custom) {
    emits("onHandle", { _type: "add", level: 1 });
  } else {
    current.value = { label: "", _type: "add" };
    show();
  }
}

function addChild(data) {
  const { level, _id, label, type } = data;
  if (props.custom) {
    emits("onHandle", { 
      ...data, 
      parentId: _id, 
      parentName: label, 
      parentType: type, // 添加父节点类型
      _type: "addChild" 
    });
  } else {
    current.value = {
      level: level + 1,
      _type: "addChild",
      parent: { _id, label },
    };
    show();
  }
}

function getLabel(data, type) {
  const option = Object.prototype.toString.call(props.labelOption) === "[object Object]" ? props.labelOption : {};
  const labels = { ...default_label, ...option };
  if (typeof props.labelOption === "function") {
    const label = props.labelOption(data, type);
    return label || labels[type];
  }
  return labels[type];
}

function rename(data) {
  const { _id, label } = data;
  if (props.custom) {
    emits("onHandle", { ...data, _type: "edit" });
  } else {
    current.value = { _id, label, _type: "rename" };
    show();
  }
}

async function remove(data) {
  const { _id, label, type } = data;
  const typeText = type === 'district' ? '院区' : '科室';
  
  if (props.custom) {
    // 在custom模式下，直接通过onHandle事件传递删除操作
    emits("onHandle", { ...data, _type: "delete" });
  } else {
    // 非custom模式下，使用removeMethod
    await ElMessageBox.confirm(`确认删除${typeText}【${label}】及其所有内容吗？`, "提示", { type: "warning" });
    const { success, message } = await props.removeMethod(data);
    if (success) {
      ElMessage.success(message);
    } else {
      ElMessage.error(message);
    }
    close();
  }
}

async function save() {
  if (typeof current.value.label !== "string" || current.value.label.trim() === "") {
    ElMessage.info("分类名称不能为空");
    return;
  }
  let fn;
  let params = {};
  if (current.value._type === "add") {
    fn = props.addMethod;
    params = { label: current.value.label };
  } else if (current.value._type === "addChild") {
    fn = props.addMethod;
    params = { label: current.value.label, parentId: current.value.parent._id };
  } else if (current.value._type === "rename") {
    fn = props.updateMethod;
    params = { id: current.value._id, label: current.value.label };
  }
  const { success, message } = await fn(params);
  if (success) {
    ElMessage.success(message);
  } else {
    ElMessage.error(message);
  }
  close();
}

function findCurrentNode(_id, tree) {
  const item = props.data.find((i) => _id && i._id === _id);
  if (item) {
    const ids = [_id];
    let parentId = item.parentId;
    while (parentId) {
      const parent = props.data.find((i) => parentId && i._id === parentId);
      if (parent) {
        ids.unshift(parent._id);
        parentId = parent.parentId;
      } else {
        parentId = null;
      }
    }
    let node = null;
    let list = tree;
    for (let i = 0; i < ids.length; i++) {
      const id = ids[i];
      node = list.find((i) => i._id === id);
      if (node) {
        list = node.children;
      } else {
        break;
      }
    }
    return node;
  }
  return tree[0];
}

watchDebounced(name, (val, old) => {
  if (val.trim() !== old.trim()) {
    treeRef.value && treeRef.value.filter(val.trim());
  }
});

// 专门用于互联网医院的数据转换函数
function getInternetHospitalTreeData(list) {
  if (!Array.isArray(list)) return [];
  
  // 递归构建子节点的函数
  function buildChildren(parentId, parentLevel) {
    const children = list.filter(item => {
      // 检查level是否匹配
      if (item.level !== parentLevel + 1) return false;

      // 兼容多种parentId匹配方式
      if (item.parentId === parentId) return true;

      // 如果parentId是业务ID，尝试通过业务ID匹配
      if (item.type === 'dept') {
        // 查找父节点，看是否匹配
        const parentNode = list.find(p =>
          p._id === parentId ||
          p.deptId === parentId ||
          p.hlwDeptId === parentId ||
          p.districtId === parentId
        );

        if (parentNode) {
          // 如果父节点是院区，检查科室的areaId
          if (parentNode.type === 'district') {
            return item.areaId === parentNode.districtId ||
                   item.areaId === parentNode.areaId ||
                   item.parentId === parentNode._id;
          }
          // 如果父节点是科室，检查业务ID匹配
          else if (parentNode.type === 'dept') {
            return item.parentId === parentNode.deptId ||
                   item.parentId === parentNode.hlwDeptId ||
                   item.parentId === parentNode._id;
          }
        }
      }

      return false;
    });
    
    return children.map(child => {
      // 递归获取子节点的子节点，优先使用业务ID
      const childId = child.deptId || child.hlwDeptId || child.districtId || child._id;
      const grandChildren = buildChildren(childId, child.level);
      
      return {
        ...child,
        children: grandChildren,
        childrenIds: grandChildren.map(gc => gc._id)
      };
    }).sort((a, b) => {
      const aSort = a.sort >= 0 ? a.sort : 10000;
      const bSort = b.sort >= 0 ? b.sort : 10000;
      return aSort - bSort;
    });
  }
  
  // 获取所有院区（level 0）
  const districts = list.filter(item => item.level === 0 || item.type === 'district');
  
  // 为每个院区递归构建子树
  const treeData = districts.map(district => {
    // 优先使用业务ID，兼容MongoDB _id
    const districtId = district.districtId || district.areaId || district._id;
    const children = buildChildren(districtId, district.level);
    
    // 收集所有后代ID（包括子科室和孙科室）
    function collectAllChildrenIds(nodes) {
      let ids = [];
      nodes.forEach(node => {
        ids.push(node._id);
        if (node.children && node.children.length > 0) {
          ids = ids.concat(collectAllChildrenIds(node.children));
        }
      });
      return ids;
    }
    
    const childrenIds = collectAllChildrenIds(children);
    
    return {
      ...district,
      children,
      childrenIds
    };
  }).sort((a, b) => {
    const aSort = a.sort >= 0 ? a.sort : 10000;
    const bSort = b.sort >= 0 ? b.sort : 10000;
    return aSort - bSort;
  });
  
  return treeData;
}

watchDebounced(
  () => props.data,
  async (n) => {
    // 使用专门的互联网医院数据转换函数
    treeData.value = getInternetHospitalTreeData(n);
    
    if (treeData.value.length === 0) {
      emits("change", {});
      return;
    }
    
    const currentNode = findCurrentNode(props.checkedCate?._id, treeData.value);
    
    if (currentNode) {
      emits("change", { 
        label: currentNode.label, 
        _id: currentNode._id, 
        childrenIds: currentNode.childrenIds, 
        level: currentNode.level,
        type: currentNode.type,
        districtName: currentNode.districtName,
        deptName: currentNode.deptName,
        districtId: currentNode.districtId,
        deptId: currentNode.deptId
      });
    }
    await nextTick();
    if (name.value && name.value.trim()) {
      treeRef.value && treeRef.value.filter(name.value.trim());
    }
  },
  { immediate: true }
);
</script>
<style lang="scss" scoped>
:deep(.el-input-group__append) {
  background-color: #fff;
}

:deep(.classify-list-tree > .el-tree-node__content > .el-tree-node__expand-icon) {
  display: none;
}
</style>
