<template>
  <div color-normal class="flex flex-col h-full text-14px bg-white rounded w-160px">
    <div class="flex-shrink-0 flex justify-between text-15px px-15px py-12px border-b border-gray-200">
      <div class="font-semibold">存档员工</div>
      <el-icon title="同步存档员工" class="hover:text-blue-500 cursor-pointer" :class="refreshing ? 'text-blue-500 animate-spin' : ''" @click="refresh">
        <Refresh />
      </el-icon>
    </div>
    <div class="flex-grow relative">
      <div class="absolute inset-0" v-loading="memberLoading">
        <el-scrollbar wrap-class="h-full">
          <empty-data v-if="authMemberList.length == 0" :top="200" title="暂无员工" padding="0 0 15px" text-top="10px" :image-width="60"></empty-data>
          <div v-for="(i, idx) in authMemberList" class="truncate px-15px py-10px cursor-pointer hover:text-blue-500" :class="selectdUserId === i.userid ? 'text-blue-500' : ''" @click="getSelectdUser(i)">
            <span class="leading-24px">
              <ww-user :openid="i.userid"></ww-user>
            </span>
          </div>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted } from "vue";
import EmptyData from "@/components/empty-data.vue";
import { authMemberList, getUserAuthList, selectdUserId, getSelectdUser, memberLoading } from "./api/authMember.js";
import WwUser from "@/components/ww-user/index.vue";
const emits = defineEmits(["adjust", "change"]);
const refreshing = ref(false);
async function refresh() {
  refreshing.value = true;
  await getUserAuthList();
  refreshing.value = false;
}
</script>
<style lang="scss" scoped></style>
