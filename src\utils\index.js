import QRCode from "qrcode";
import dayjs from "dayjs";
import copy from "copy-to-clipboard";
import { ElMessage } from "element-plus";
import validate from "./validate";

export function getBirthFromIdNo(idNo = "") {
  const [isIdNo] = validate.isChinaId(idNo);
  if (isIdNo) {
    const birthDay = idNo.slice(6, 14).replace(/(?=\d{2}$)|(?=\d{4}$)/g, "-");
    return birthDay;
  }
  return "";
}
export function maskPhone(phone = "", headNumLength = 3, tailNumLength = 2) {
  if (!phone) return;

  phone = phone + "";
  const isMobile = validate.isMobile(phone);
  const phoneRegxp = new RegExp(`^(\\d{${headNumLength}})\\d{${11 - headNumLength - tailNumLength}}(\\d{${tailNumLength}})$`);
  return isMobile ? phone.replace(phoneRegxp, `$1${"*".repeat(11 - headNumLength - tailNumLength)}$2`) : phone;
}

export function maskIdNo(idNo = "") {
  if (!idNo) return;
  const [isIdNo] = validate.isChinaId(idNo);
  return isIdNo ? idNo.replace(/^(\d{3})\d+(\w{4})$/, "$1***********$2") : idNo;
}

export async function downloadQrcode(content, name, options = { width: 560 }) {
  const base64 = await QRCode.toDataURL(content, options);
  download(base64, name);
}

export function download(href, filename) {
  let tag = document.createElement("a");
  tag.href = href;
  tag.download = filename;
  tag.click();
  tag = null;
}
export function useCopy(text, msg = "已复制到剪切板") {
  copy(text);
  ElMessage.success(msg);
}

export function getRandomStr(letterNum = 10, tailNum = 5) {
  const prefix = new Array(letterNum).fill(1).reduce((s) => s + getRandomLetter());
  const tail = String(Math.floor(Math.random() * 10 ** tailNum)).padStart(tailNum, "0");
  return `${prefix}${+new Date()}${tail}`;
}

function getRandomLetter() {
  let num = Math.floor(Math.random() * 58) + 65;
  if (num > 90 && num < 97) {
    num -= 7;
  }
  return String.fromCharCode(num);
}

export function file2Base64(file) {
  const reader = new FileReader();
  return new Promise((resolve) => {
    reader.readAsDataURL(file);
    reader.onload = (evt) => resolve(evt.target.result);
  });
}
export function urlParamsToObject(url = window.location.href) {
  const obj = {};
  // 获取 URL 中的查询字符串部分
  const queryString = url.split("?")[1];
  if (queryString) {
    const params = new URLSearchParams(queryString);
    // 2. 创建一个空对象来存储解析后的参数
    // 3. 遍历每个参数，并将其存储在对象中
    for (const [key, value] of params) {
      obj[key] = value;
    }
  }
  return obj;
}

export function transformData(data) {
  return data.map((item) => {
    const transformedItem = {
      label: item.sourceName,
      value: item.sourceId,
    };

    if (item.children && item.children.length > 0) {
      transformedItem.children = transformData(item.children);
    }

    return transformedItem;
  });
}

export function imageToBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = function () {
      const base64String = reader.result.split(",")[1];
      resolve(base64String);
    };

    reader.onerror = function (error) {
      reject(error);
    };

    reader.readAsDataURL(file);
  });
}

export function blobUrlToBase64(blobUrl) {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();
    xhr.responseType = "arraybuffer";

    xhr.onload = function () {
      if (xhr.status === 200) {
        const arrayBuffer = xhr.response;
        const uint8Array = new Uint8Array(arrayBuffer);
        const base64String = btoa(String.fromCharCode.apply(null, uint8Array));

        // Determine the appropriate image type based on the blob URL
        const contentType = xhr.getResponseHeader("Content-Type");
        const imageType = contentType.substring(contentType.indexOf("/") + 1);

        // Add the appropriate image base64 prefix
        const base64WithPrefix = `data:image/${imageType};base64,${base64String}`;

        resolve(base64WithPrefix);
      } else {
        reject(new Error("Failed to fetch the Blob content."));
      }
    };

    xhr.open("GET", blobUrl, true);
    xhr.send();
  });
}

export function validateColor(color) {
  // 使用正则表达式检查颜色字符串是否符合 RGB、RGBA、十六进制颜色、HSL 或 HSLA 的格式
  const rgbRegex = /^rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)$/i;
  const rgbaRegex = /^rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*(0|1|0\.\d+)\s*\)$/i;
  const hexRegex = /^#([0-9a-f]{3}|[0-9a-f]{6})$/i;
  const hslRegex = /^hsl\(\s*(\d+)\s*,\s*(\d+%)\s*,\s*(\d+%)\s*\)$/i;
  const hslaRegex = /^hsla\(\s*(\d+)\s*,\s*(\d+%)\s*,\s*(\d+%)\s*,\s*(0|1|0\.\d+)\s*\)$/i;

  if (rgbRegex.test(color) || rgbaRegex.test(color) || hexRegex.test(color) || hslRegex.test(color) || hslaRegex.test(color)) {
    return true; // 颜色格式匹配
  } else {
    return false; // 颜色格式不匹配
  }
}

export function getCateTreeData(list) {
  if (!Array.isArray(list)) return [];
  const level3 = list
    .filter((i) => i.level === 3)
    .map((i) => {
      const children = list.filter((j) => j.parentId === i._id).map((j) => ({ ...j }));
      const childrenIds = children.map((j) => j._id);
      return { ...i, children, childrenIds };
    });
  const level2 = list
    .filter((i) => i.level === 2)
    .map((i) => {
      const children = level3.filter((j) => j.parentId === i._id).map((j) => ({ ...j }));
      const childrenIds = children.map((j) => j._id);
      children.forEach((child) => {
        if (Array.isArray(child.childrenIds) && child.childrenIds.length > 0) {
          childrenIds.push(...child.childrenIds);
        }
      });
      return { ...i, children, childrenIds };
    });
  return list
    .filter((i) => i.level === 1)
    .map((i) => {
      const children = level2.filter((j) => j.parentId === i._id);
      const childrenIds = children.map((j) => j._id);
      children.forEach((child) => {
        if (Array.isArray(child.childrenIds) && child.childrenIds.length > 0) {
          childrenIds.push(...child.childrenIds);
        }
      });
      return { ...i, children, childrenIds };
    })
    .sort((a, b) => {
      const aSort = a.sort >= 0 ? a.sort : 10000;
      const bSort = b.sort >= 0 ? b.sort : 10000;
      return aSort - bSort;
    });
}

export function disableBeforeDay(date) {
  return dayjs(date).isBefore(dayjs().startOf("day"));
}

export function getAttachmentFileList(list) {
  return list.map((i) => {
    let typeStr = "";
    const type = i.type;
    const fileType = i.file && typeof i.file.type === "string" ? i.file.type : "";
    if (fileType.includes("video")) typeStr = "【视频】";
    else if (fileType.includes("image") || type === "image") typeStr = "【图片】";
    else if (fileType === "article") typeStr = "【文章】";
    else if (fileType === "questionnaire") typeStr = "【问卷】";
    else if (type === "link") typeStr = "【链接】";
    return { name: i.file && i.file.name ? i.file.name : "", type: typeStr };
  });
}

export function generateRandomString(length = 24) {
  let result = "";
  let characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}
