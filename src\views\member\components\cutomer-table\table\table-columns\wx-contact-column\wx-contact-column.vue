<template>
  <el-table-column label="微信联系人" prop="wechatContact" :width="180">
    <template #default="{ row }">
      <td-wrapper v-if="row.externalUserId && contactName[row.externalUserId]" classnames="w-full overflow-hidden">
        <wechat-td :customer="row" :canUnbind="canUnbind" @onUnbind="onUnbind" />
      </td-wrapper>
      <td-wrapper v-else classnames="group">
        <div class="absolute inset-0 flex items-center group-hover:hidden">-</div>
        <div
          class="absolute inset-0 flex items-center opacity-0 cursor-pointer underline underline-offset-2 text-gray-500 group-hover:opacity-100"
          @click="setWechat(row)">绑定客户微信</div>
      </td-wrapper>
    </template>
  </el-table-column>
</template>
<script setup>
import tdWrapper from "../td-wrapper.vue";
import { storeToRefs } from "pinia";
import wxContact from "@/store/wxContact";
import { bindWechat } from "../../../utils";
import wechatTd from './wechat-td.vue';

defineProps({ canUnbind: { type: Boolean, default: false } });
const emits = defineEmits(["cellClick"]);
const { contactName } = storeToRefs(wxContact());

async function setWechat(customer) {
  const res = await bindWechat(customer);
  res && emits("cellClick", { type: "reload" });
}

function onUnbind() {
  emits("cellClick", { type: "reload" });

}
</script>
