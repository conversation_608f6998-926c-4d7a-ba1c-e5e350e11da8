<template>
  <el-table-column label="所处阶段" prop="customerStage" :width="200">
    <template #default="{row:{customerStage}}">
      <td-wrapper>{{ stageMap[customerStage] || customerStage || "" }}</td-wrapper>
    </template>
  </el-table-column>
</template>
<script setup>
import { storeToRefs } from 'pinia';
import { configStore } from "@/store/config";
import tdWrapper from './td-wrapper.vue';

const { stageMap } = storeToRefs(configStore());
</script>
<style lang="scss" scoped></style>
