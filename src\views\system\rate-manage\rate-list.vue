<template>
  <my-layout>
    <layout-item>
      <div class="flex px-15px py-6px mb-10px bg-white">
        <div class="flex flex-wrap w-0 flex-grow mr-10px">
          <date-range-filter v-model:dates="dates" label="服务时间" :text="datesText" :width="320" />
          <check-box-filter v-model="rates" label="满意度评分" :list="rateList">
            <template #item="{ item }">
              <el-rate :model-value="item.value" disabled />
            </template>
          </check-box-filter>
          <check-box-filter v-model="tags" label="评价标签" :list="tagList" />
          <check-box-filter v-model="teamIds" label="服务团队" :list="teamList" />
          <check-box-filter v-model="userIds" label="团队成员" :list="memberList">
            <template #item="{ item }">
              <ww-user :openid="item.value"></ww-user>
            </template>
          </check-box-filter>
        </div>
        <div class="flex-shrink-0 h-40px flex items-center">
          <el-button type="primary" @click="search()">搜索</el-button>
          <el-button text type="primary" @click="setVisible = true">评价标签设置</el-button>
        </div>
      </div>
    </layout-item>
    <layout-main v-loading="loading" :scroll="false" class="bg-white">
      <el-scrollbar v-if="list.length" wrap-class="h-full">
        <div v-for="i in list" :key="i._id" class="p-15px border-b border-gray-100">
          <div class="flex-shrink-0 flex items-center pl-10px">
            <el-avatar :size="30">
              <el-icon :size="20">
                <Avatar />
              </el-icon>
            </el-avatar>
            <div class="flex-grow flex items-center mx-10px">
              <div class="text-16px">{{ i.customerName }}</div>
              <el-rate class="ml-10px" :model-value="i.rate" :texts="['非常不满意', '不满意', '一般', '满意', '很满意']" show-text text-color="#f7ba2a" disabled />
              <el-tag v-if="i.isExpired" class="ml-10px" type="warning">超时自动好评</el-tag>
            </div>
            <el-button text plain class="flex-shrink-0" type="primary" @click="showChatRecord(i)" v-if="i.userId && i.externalUserId && usrAuthList.includes(i.userId)">聊天记录</el-button>
            <el-button text plain class="flex-shrink-0" type="primary" @click="showRecord(i)">关联服务记录</el-button>
            <el-button text plain class="ml-10px flex-shrink-0" type="primary" @click="viewCustomer(i.customerId)">客户详情</el-button>
          </div>
          <div v-if="i.tags && i.tags.length" class="flex flex-wrap pl-50px">
            <el-tag v-for="(tag, idx) in i.tags" :key="`${i._id}_${idx}_${tag}`" effect="dark" class="mr-10px mt-10px" type="priamry">{{ tag }}</el-tag>
          </div>
          <div v-if="i.words && i.words.length" color-normal class="pl-50px break-all text-14px mt-15px">
            {{ i.words }}
          </div>
          <div class="mt-15px flex flex-wrap pl-50px text-14px text-gray-400">
            <div class="mr-20px">服务团队：{{ i.teamName }}</div>
            <div class="mr-20px">服务成员：{{ i.userName }}</div>
            <div class="mr-20px">服务日期：{{ i.createDate }}</div>
            <div class="mr-20px">评价时间：{{ i.updateTime }}</div>
          </div>
        </div>
      </el-scrollbar>
      <div v-else="list.length === 0" class="h-full flex items-center justify-center">
        <el-empty description="暂无数据" />
      </div>
    </layout-main>
    <layout-item class="bg-white">
      <div class="px-15px py-10px flex justify-end border-t border-gray-200">
        <el-pagination small layout="total, prev, pager, next, sizes, jumper" :current-page="page" :page-size="pageSize" :page-sizes="[10, 20, 50]" :total="total" @current-change="changePage" @size-change="changeSize" />
      </div>
    </layout-item>
  </my-layout>
  <service-table-modal :list="serviceList" :visible="visible" :width="width" @close="close" />
  <tag-set-drawer :rate-tags="rateTags" :visible="setVisible" @close="setVisible = false" @updated="updated" @remove="remove" />
  <customer-detail :visible="customerDetailVisible" @close="customerDetailVisible = false" :customerId="customerId" title="客户详情" customerType="corpCustomer" />
  <el-drawer v-model="chatVisible" title="聊天记录" direction="rtl" size="60%" :destroy-on-close="false">
    <custoner-chat-record :customerUserId="customerUserId" :memberUserId="memberUserId" chatType="rate" />
  </el-drawer>
</template>
<script setup>
import { computed, ref, onMounted, watch } from "vue";
import { authMemberList, getUserAuthList } from "@/views/chat-history/api/authMember.js";
import { storeToRefs } from "pinia";
import { teamStore } from "@/store/team";
import { getCorpRateConfig, getCorpRateRecord } from "@/api/knowledgeBase.js";
import useElPagination from "@/hooks/useElPagination";
import useModal from "@/hooks/useModal";
import { getCustomerServiceRecord } from "@/api/todo";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import { DateRangeFilter, CheckBoxFilter } from "@/components/filter-bar";
import serviceTableModal from "./service-table-modal.vue";
import tagSetDrawer from "./tag-set-drawer.vue";
import { dayjs, ElMessage } from "element-plus";
import customerDetail from "@/views/member/detail/index.vue";
import custonerChatRecord from "@/views/chat-history/customer-chat-record.vue";
const customerUserId = ref("");
const memberUserId = ref("");
const chatVisible = ref(false);

const usrAuthList = computed(() => {
  return authMemberList && Array.isArray(authMemberList) ? authMemberList.value.map((i) => i.userid) : [];
});

const rateTags = ref([]);
const { visible, show, close, width } = useModal(800);
const setVisible = ref(false);
const dates = ref([]);
const datesText = computed(() => dates.value.join(" ~ "));
const rateList = [
  { value: 1, label: "一星好评" },
  { value: 2, label: "二星好评" },
  { value: 3, label: "三星好评" },
  { value: 4, label: "四星好评" },
  { value: 5, label: "五星好评" },
];
const rates = ref([]);
const tags = ref([]);
const tagList = computed(() =>
  rateTags.value.reduce((list, item) => {
    if (Array.isArray(item.rateTags)) {
      item.rateTags.forEach((tag) => tag && tag.text && list.push({ value: tag.text, label: tag.text }));
    }
    return list;
  }, [])
);
const { allTeams } = storeToRefs(teamStore());
const teamList = computed(() => allTeams.value.map((i) => ({ label: i.name, value: i.teamId })));
const teamIds = ref([]);
const userIds = ref([]);
const memberList = computed(() => {
  const teams = teamIds.value.length ? allTeams.value.filter((i) => teamIds.value.includes(i.teamId)) : allTeams.value;
  const userids = teams.reduce((list, item) => {
    if (Array.isArray(item.memberLeaderList)) {
      list = [...list, ...item.memberLeaderList];
    }
    if (Array.isArray(item.memberList)) {
      list = [...list, ...item.memberList];
    }
    return list;
  }, []);
  return Array.from(new Set(userids)).map((userid) => ({ value: userid }));
});

const total = ref(0);
const list = ref([]);
const loading = ref(false);
const { page, pageSize, changePage, changeSize } = useElPagination(getList);

async function showChatRecord(record) {
  customerUserId.value = record.externalUserId;
  memberUserId.value = record.userId;
  chatVisible.value = true;
}

async function getList() {
  loading.value = true;
  const params = {
    corpId: localStorage.getItem("corpId"),
    page: page.value,
    pageSize: pageSize.value,
  };
  if (dates.value.length) params.serviceDates = dates.value;
  if (rates.value.length) params.rate = rates.value;
  if (tags.value.length) params.tags = tags.value;
  if (teamIds.value.length) params.teamIds = teamIds.value;
  if (userIds.value.length) params.userIds = userIds.value;
  const { data, success, message } = await getCorpRateRecord(params);
  if (success) {
    list.value = Array.isArray(data.list)
      ? data.list.map((i) => {
          const isExpired = !i.updateTime && Date.now() > i.expireTime;
          const data = { ...i, createDate: i.createTime && dayjs(i.createTime).isValid() ? dayjs(i.createTime).format("YYYY-MM-DD") : "", isExpired };
          if (isExpired) {
            data.updateTime = dayjs(i.expireTime).format("YYYY-MM-DD HH:mm");
            data.rate = 5;
          } else data.updateTime = dayjs(i.updateTime).format("YYYY-MM-DD HH:mm");
          return data;
        })
      : [];
    total.value = data.total;
  } else {
    ElMessage.error(message);
  }
  loading.value = false;
}

function search() {
  changePage(1);
}
const customerDetailVisible = ref(false);
const customerId = ref("");
function viewCustomer(id) {
  customerId.value = id;
  customerDetailVisible.value = true;
}

onMounted(() => {
  getUserAuthList();
  getRateTags();
  getList();
});
async function getRateTags() {
  const { data } = await getCorpRateConfig();
  rateTags.value = Array.isArray(data.data) ? data.data : [];
}

function updated({ id, text, rateStar }) {
  const index = rateTags.value.findIndex((item) => rateStar && item.rateStar === rateStar);
  if (index >= 0) {
    const tagIndex = rateTags.value[index].rateTags.findIndex((item) => item._id === id);
    if (tagIndex >= 0) {
      rateTags.value[index].rateTags[tagIndex].text = text;
    } else {
      rateTags.value[index].rateTags.push({ text, _id: id, rateStar });
    }
  }
}

function remove({ id, rateStar }) {
  const index = rateTags.value.findIndex((item) => rateStar && item.rateStar === rateStar);
  if (index >= 0) {
    const tagIndex = rateTags.value[index].rateTags.findIndex((item) => item._id === id);
    rateTags.value[index].rateTags.splice(tagIndex, 1);
  }
}

const serviceList = ref([]);
async function showRecord(i) {
  const { corpId, customerId, userId, teamId, createTime: executionTime } = i;
  const { success, data, message } = await getCustomerServiceRecord({
    corpId,
    customerId,
    userId,
    teamId,
    executionTime,
  });
  if (success) {
    serviceList.value = data && Array.isArray(data.data) ? data.data : [];
    show();
  } else {
    ElMessage.error(message);
  }
}

watch(teamIds, () => (userIds.value = []));
</script>
<style lang="scss" scoped></style>
