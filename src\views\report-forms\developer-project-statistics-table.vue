<template>
  <div class="report-container">
    <div class="report-header">
      <div class="report-actions">
        <div class="filter-section">
          <div class="filter-row">
            <div class="filter-item">
              <span class="filter-label">咨询日期：</span>
              <el-date-picker v-model="consultDateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :shortcuts="dateShortcuts" :disabled-date="disabledDate" @change="handleDateChange" class="date-range" value-format="YYYY-MM-DD" />
            </div>
            <div class="filter-item" v-if="isManager">
              <check-box-filter v-model="developerFilter" label="开发人员" :list="developerList" @clear="clearDevelopers">
                <template #item="{ item }">
                  <ww-user :openid="item.value"></ww-user>
                </template>
              </check-box-filter>
            </div>
            <div class="filter-item">
              <span class="filter-label">咨询科室：</span>
              <dept-select v-model="deptId" placeholder="请选择科室" @change="handleDeptChange" class="dept-select" />
            </div>
            <div class="filter-item">
              <span class="filter-label">咨询项目：</span>
              <project-intent-select v-model="projectIntentIds" :deptId="deptId" placeholder="请选择或搜索咨询项目" @change="handleProjectChange" />
            </div>
            <div class="filter-item">
              <filter-info-source v-model="sourceFilter" label="信息来源" />
            </div>
            <div class="filter-actions">
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button type="success" @click="exportToExcel">导出Excel</el-button>
              <el-button @click="resetFilters">重置</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="table-container">
      <table class="performance-table">
        <colgroup>
          <col style="width: 12%" />
          <!-- 开发人员 -->
          <col style="width: 18%" />
          <!-- 咨询项目 -->
          <col style="width: 14%" />
          <!-- 咨询人次 -->
          <col style="width: 14%" />
          <!-- 到诊人次 -->
          <col style="width: 14%" />
          <!-- 到诊占比 -->
          <col style="width: 14%" />
          <!-- 成交人次 -->
          <col style="width: 14%" />
          <!-- 成交占比 -->
        </colgroup>
        <thead>
          <tr>
            <th>开发人员</th>
            <th>咨询项目</th>
            <th>咨询人次</th>
            <th>到诊人次</th>
            <th>到诊占比</th>
            <th>成交人次</th>
            <th>成交占比</th>
          </tr>
        </thead>
        <tbody>
          <template v-for="(developer, index) in groupedData" :key="developer.userId">
            <!-- 项目行 -->
            <tr v-for="(item, pIndex) in developer.projects" :key="`${developer.userId}-${item.projectId}-${pIndex}`">
              <!-- 第一个项目显示开发人员，使用rowspan -->
              <td v-if="pIndex === 0" :rowspan="developer.projects.length">
                <ww-user v-if="developer.userId" :openid="developer.userId" />
                <span v-else>未知开发人员</span>
              </td>
              <td>{{ item.projectName }}</td>
              <td>{{ item.consultCount }}</td>
              <td>{{ item.visitCount }}</td>
              <td>{{ formatPercentage(item.visitRate) }}</td>
              <td>{{ item.dealCount }}</td>
              <td>{{ formatPercentage(item.dealRate) }}</td>
            </tr>
          </template>
          <tr class="total-row">
            <td colspan="2" class="total-label">总计：</td>
            <td>{{ totalConsultCount }}</td>
            <td>{{ totalVisitCount }}</td>
            <td>{{ formatPercentage(totalVisitRate) }}</td>
            <td>{{ totalDealCount }}</td>
            <td>{{ formatPercentage(totalDealRate) }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="footer-info">
      <span class="date-limit-info">* 查询限制为一个月内的数据</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { ElButton, ElMessage, ElDatePicker, ElSelect, ElOption } from "element-plus";
import { CheckBoxFilter, filterInfoSource } from "@/components/filter-bar";
import { storeToRefs } from "pinia";
import { staffStore } from "@/store/staff";
import { memberStore } from "@/store/member";
import { teamStore } from "@/store/team";
import ProjectIntentSelect from "@/components/benefit-management/project-intent-select.vue";
import { getEConsultProjectStatistics } from "@/api/bill-record";
import { getProjectIntentNames } from "@/api/benefitManagement";
import DeptSelect from "@/components/dept-components/dept-select.vue";
import { useDeptStore } from "@/components/dept-components/dept-store";

// 用户筛选

const projectIntentIds = ref([]);
const selectedProjects = ref([]);
const consultDateRange = ref([]);
const sourceFilter = ref([]);
const deptId = ref("");
const selectedDept = ref({});
const deptStore = useDeptStore();

// 从store获取员工列表
const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());
const { isAdmin } = storeToRefs(memberStore());
const { managerList } = storeToRefs(teamStore());
const { judgmentIsAdmin } = memberStore();

// 添加管理员判断逻辑
const isManager = computed(() => {
  return managerList.value.includes(90006) || isAdmin.value;
});

// 添加开发人员列表计算属性，只显示开发人员角色
const developerList = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("onlineCustomerService")).map((i) => ({ value: i.userid }));
});

const selectedDevelopers = ref([]);
const hasExplicitlyClearedDevelopers = ref(false);

const developerFilter = computed({
  get() {
    if (selectedDevelopers.value.length > 0) {
      return selectedDevelopers.value;
    }
    if (hasExplicitlyClearedDevelopers.value) {
      return [];
    }
    const currentUserId = localStorage.getItem("userId");
    if (!isManager.value) {
      // 不是主管，只能看到自己
      return [currentUserId];
    } else {
      // 主管
      const inList = developerList.value.some((i) => i.value === currentUserId);
      if (inList) {
        return [currentUserId];
      } else {
        // 展示全部
        return [];
      }
    }
  },
  set(newVal) {
    selectedDevelopers.value = newVal;
    hasExplicitlyClearedDevelopers.value = false;
  },
});

function clearDevelopers() {
  selectedDevelopers.value = [];
  hasExplicitlyClearedDevelopers.value = true;
}

// 项目选择变更处理
const handleProjectChange = (projects) => {
  selectedProjects.value = projects;
};

// 科室变化处理函数
function handleDeptChange(dept) {
  selectedDept.value = dept;
  projectIntentIds.value = [];
}

// 日期快捷选项
const dateShortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近半个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
];

// 禁用超过一个月范围的日期
const disabledDate = (time) => {
  if (!consultDateRange.value || consultDateRange.value.length === 0) {
    return false;
  }

  const startDate = new Date(consultDateRange.value[0]);
  const endDate = new Date(consultDateRange.value[1]);
  const currentDate = new Date(time);

  // 如果已选择了开始日期，限制结束日期不能超过开始日期后的31天
  if (consultDateRange.value.length === 1) {
    const oneMonthLater = new Date(startDate);
    oneMonthLater.setDate(startDate.getDate() + 31);
    return currentDate > oneMonthLater;
  }

  return false;
};

// 格式化日期为YYYY-MM-DD
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  return `${year}-${month}-${day}`;
};

// 原始数据和筛选后的表格数据
const originalData = ref([]);
const tableData = ref([]);

// 用于存储项目ID到名称的映射
const projectNameMap = ref({});

// 获取咨询项目统计数据
async function fetchProjectStatistics() {
  try {
    // 构建查询参数 - 使用所有筛选条件作为API查询参数
    const today = new Date();
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(today.getMonth() - 1);

    const params = {
      startDate: consultDateRange.value?.[0] || formatDate(oneMonthAgo),
      endDate: consultDateRange.value?.[1] || formatDate(today),
    };

    // 添加开发人员筛选参数 - 根据管理员权限处理
    if (isManager.value) {
      // 管理员可以筛选多个开发人员
      if (developerFilter.value && developerFilter.value.length > 0) {
        params.developerUserIds = developerFilter.value;
      }
    } else {
      // 非管理员只能查看自己的数据
      params.developerUserIds = [localStorage.getItem("userId")];
    }

    // 添加信息来源筛选参数
    if (sourceFilter.value && sourceFilter.value.length > 0) {
      params.infoSource = sourceFilter.value;
    }

    // 添加科室筛选参数
    if (deptId.value) {
      params.deptId = deptId.value; // 添加科室ID筛选条件
      params.projectIds  = (await deptStore.fetchProjectIdsByDeptId(deptId.value)) || [];
    }

    // 添加项目筛选参数
    if (projectIntentIds.value && projectIntentIds.value.length > 0) {
      params.projectIds = projectIntentIds.value;
    }

    // 调用API获取数据
    const { data, success, message } = await getEConsultProjectStatistics(params);

    if (success) {
      // 保存原始数据
      originalData.value = data.data;

      // 收集所有项目ID
      const allProjectIds = [];
      data.data.forEach((record) => {
        const projectIds = Array.isArray(record.projectIds) ? record.projectIds : record.projectIds ? [record.projectIds] : [];
        allProjectIds.push(...projectIds);
      });

      // 去重项目ID
      const uniqueProjectIds = [...new Set(allProjectIds)];

      // 如果有项目ID，调用新接口获取项目名称
      if (uniqueProjectIds.length > 0) {
        try {
          const { data: nameData, success: nameSuccess } = await getProjectIntentNames({ ids: uniqueProjectIds });
          if (nameSuccess && nameData) {
            // 将返回的数组转换为ID到名称的映射
            const nameMap = {};
            nameData.data.forEach((item) => {
              nameMap[item._id] = item.projectName;
            });
            projectNameMap.value = nameMap;
          }
        } catch (error) {
          console.error("获取项目名称失败:", error);
        }
      }

      // 处理数据：展开projectIds并按项目分组合并
      const expandedProjects = [];

      // 1. 遍历每条咨询记录，展开项目IDs
      data.data.forEach((record) => {
        // 确保projectIds是数组
        const projectIds = Array.isArray(record.projectIds) ? record.projectIds : record.projectIds ? [record.projectIds] : [];

        // 如果没有项目，添加一个"未知项目"记录
        if (projectIds.length === 0) {
          expandedProjects.push({
            ...record,
            projectId: "unknown",
            projectName: "未知项目",
          });
        } else {
          // 对每个项目ID创建一条记录
          projectIds.forEach((projectId) => {
            // 优先使用record中的projectInfos，如果没有，则使用getProjectIntentNames获取的映射
            const projectInfo = record.projectInfos?.find((p) => p.id === projectId);
            const projectName = projectInfo?.name || projectNameMap.value[projectId] || projectId;

            expandedProjects.push({
              ...record,
              projectId: projectId,
              projectName: projectName,
              // 确保每个记录都有基础数据
              consultCount: 1, // 每条记录算一次咨询
              isVisited: record.inHospitalTimes && record.inHospitalTimes.length > 0,
              isDeal: (record.consumeRecordCount || 0) > 0,
              // 确保信息来源正确传递
              source: record.source || "unknown",
            });
          });
        }
      });

      // 2. 按项目ID分组合并数据
      const projectGroups = {};
      expandedProjects.forEach((record) => {
        const projectKey = `${record.projectId}-${record.userId || "unknown"}`;

        if (!projectGroups[projectKey]) {
          projectGroups[projectKey] = {
            projectId: record.projectId,
            projectName: record.projectName,
            userId: record.userId,
            consultCount: 0,
            visitCount: 0,
            dealCount: 0,
            source: record.source || [],
          };
        }
        // 累加统计数据
        projectGroups[projectKey].consultCount += 1; // 每条记录算一次咨询
        projectGroups[projectKey].visitCount += record.isVisited ? 1 : 0; // 到诊人次
        projectGroups[projectKey].dealCount += record.isDeal ? 1 : 0; // 成交人次
      });

      // 3. 转换为数组并计算比率
      tableData.value = Object.values(projectGroups).map((item) => {
        // 计算到诊占比和成交占比
        const visitRate = item.consultCount > 0 ? item.visitCount / item.consultCount : 0;
        const dealRate = item.consultCount > 0 ? item.dealCount / item.consultCount : 0;

        return {
          ...item,
          visitRate,
          dealRate,
        };
      });

      console.log("获取咨询项目统计数据成功，共 " + tableData.value.length + " 条记录");
    } else {
      ElMessage.error(message || "获取咨询项目统计数据失败");
      tableData.value = [];
    }
  } catch (error) {
    ElMessage.error("获取咨询项目统计数据出错：" + (error.message || error));
    tableData.value = [];
    console.error("获取咨询项目统计数据出错:", error);
  }
}

// 直接使用API返回的数据，不再需要前端筛选
const filteredTableData = computed(() => {
  return tableData.value;
});

// 按开发人员分组的数据
const groupedData = computed(() => {
  // 获取数据
  const data = tableData.value;

  // 创建开发人员映射
  const developerMap = new Map();

  // 遍历数据
  data.forEach((item) => {
    const userId = item.userId || "unknown";

    // 如果开发人员不存在于映射中，则添加
    if (!developerMap.has(userId)) {
      const developerName = staffList.value.find((staff) => staff.userid === userId)?.anotherName || "未知开发人员";
      developerMap.set(userId, {
        userId,
        developerName,
        projects: [],
        source: item.source || [],
        totalConsultCount: 0,
        totalVisitCount: 0,
        totalDealCount: 0,
      });
    }

    // 获取当前开发人员数据
    const developer = developerMap.get(userId);

    // 添加项目数据
    developer.projects.push(item);

    // 更新开发人员汇总数据
    developer.totalConsultCount += item.consultCount || 0;
    developer.totalVisitCount += item.visitCount || 0;
    developer.totalDealCount += item.dealCount || 0;
  });

  // 将映射转换为数组
  return Array.from(developerMap.values());
});

// 计算总计数据 - 基于筛选后的数据
const totalConsultCount = computed(() => {
  return filteredTableData.value.reduce((sum, item) => sum + (item.consultCount || 0), 0);
});

const totalVisitCount = computed(() => {
  return filteredTableData.value.reduce((sum, item) => sum + (item.visitCount || 0), 0);
});

const totalVisitRate = computed(() => {
  return totalConsultCount.value > 0 ? totalVisitCount.value / totalConsultCount.value : 0;
});

const totalDealCount = computed(() => {
  return filteredTableData.value.reduce((sum, item) => sum + (item.dealCount || 0), 0);
});

const totalDealRate = computed(() => {
  return totalConsultCount.value > 0 ? totalDealCount.value / totalConsultCount.value : 0;
});

// 格式化百分比
const formatPercentage = (value) => {
  if (value === undefined || value === null || isNaN(value)) {
    return "0.00%";
  }
  return `${(value * 100).toFixed(2)}%`;
};

// 处理日期变化
const handleDateChange = () => {
  // 日期改变时只更新日期值，不触发查询
  // 查询将由用户点击查询按钮触发
};

// 重置筛选条件
const resetFilters = () => {
  // 设置默认日期范围为当月
  const today = new Date();
  const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
  const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

  consultDateRange.value = [formatDate(firstDayOfMonth), formatDate(lastDayOfMonth)];
  projectIntentIds.value = [];
  selectedProjects.value = [];
  sourceFilter.value = [];
  deptId.value = "";
  selectedDept.value = {};

  selectedDevelopers.value = [];

  // 重置后重新获取数据
  fetchProjectStatistics();
};

// 处理查询 - 现在触发API调用获取筛选后的数据
const handleSearch = () => {
  if (consultDateRange.value && consultDateRange.value.length === 2) {
    const start = new Date(consultDateRange.value[0]);
    const end = new Date(consultDateRange.value[1]);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays > 31) {
      ElMessage.warning("查询范围已限制为一个月内");
      return;
    }

    // 使用所有筛选条件获取新数据
    fetchProjectStatistics();
  } else {
    ElMessage.warning("请选择咨询日期范围");
  }
};

// 监听信息来源变化不需要立即触发查询，由用户点击查询按钮触发
watch(sourceFilter, () => {
  console.log("信息来源筛选变更:", sourceFilter.value);
});

// 导出Excel - 为分组后的数据格式调整导出逻辑
const exportToExcel = () => {
  // 准备导出数据
  const exportData = [];

  // 添加标题信息
  exportData.push(["咨询项目情况统计表"]);

  // 添加筛选条件信息
  if (consultDateRange.value && consultDateRange.value.length === 2) {
    exportData.push(["咨询日期范围", `${consultDateRange.value[0]} 至 ${consultDateRange.value[1]}`]);
  }

  if (developerFilter.value.length > 0) {
    const developerNames = developerFilter.value
      .map((id) => {
        const staff = staffList.value.find((s) => s.userid === id);
        return staff ? staff.anotherName || id : id;
      })
      .join(", ");
    exportData.push(["开发人员", developerNames]);
  }

  if (projectIntentIds.value.length > 0) {
    // 使用projectNameMap来获取项目名称
    const projectsText = projectIntentIds.value.map((id) => projectNameMap.value[id] || selectedProjects.value.find((p) => p.id === id)?.name || id).join(", ");
    exportData.push(["咨询项目", projectsText]);
  }

  if (sourceFilter.value.length > 0) {
    exportData.push(["信息来源", sourceFilter.value.join(", ")]);
  }

  if (deptId.value) {
    exportData.push(["咨询科室", selectedDept.value.name || ""]);
  }

  exportData.push([]); // 空行

  // 添加表头
  exportData.push(["开发人员", "咨询项目", "咨询人次", "到诊人次", "到诊占比", "成交人次", "成交占比"]);

  // 添加按开发人员分组的数据
  groupedData.value.forEach((developer) => {
    // 获取开发人员姓名
    const developerName = developer.developerName || "未知开发人员";

    // 添加每个项目的数据，为第一行添加开发人员名称
    developer.projects.forEach((item, index) => {
      exportData.push([
        index === 0 ? developerName : "", // 只在开发人员的第一个项目显示名字
        item.projectName,
        item.consultCount,
        item.visitCount,
        `${(item.visitRate * 100).toFixed(2)}%`,
        item.dealCount,
        `${(item.dealRate * 100).toFixed(2)}%`,
      ]);
    });
  });

  // 添加总计行
  exportData.push(["总计", "", totalConsultCount.value, totalVisitCount.value, `${(totalVisitRate.value * 100).toFixed(2)}%`, totalDealCount.value, `${(totalDealRate.value * 100).toFixed(2)}%`]);

  // 创建工作簿和工作表
  const wb = XLSX.utils.book_new();
  const ws = XLSX.utils.aoa_to_sheet(exportData);

  // 设置列宽
  const colWidths = [
    { wch: 15 }, // 开发人员
    { wch: 18 }, // 咨询项目
    { wch: 10 }, // 咨询人次
    { wch: 10 }, // 到诊人次
    { wch: 10 }, // 到诊占比
    { wch: 10 }, // 成交人次
    { wch: 10 }, // 成交占比
  ];

  ws["!cols"] = colWidths;

  // 合并标题单元格
  ws["!merges"] = [
    { s: { r: 0, c: 0 }, e: { r: 0, c: 6 } }, // 标题行
    // 其他合并单元格设置可以根据需要添加
  ];

  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(wb, ws, "开发项目统计表");

  // 生成Excel文件并下载
  const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
  const blob = new Blob([excelBuffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });

  // 使用当前日期作为文件名的一部分
  const now = new Date();
  const fileName = `开发项目统计_${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}.xlsx`;

  saveAs(blob, fileName);

  ElMessage.success("导出Excel成功");
};

onMounted(async () => {
  // 确保科室列表已加载
  await deptStore.fetchDeptList();

  // 加载员工列表并判断管理员权限
  if (staffList.value.length === 0) await getStaffList();
  await judgmentIsAdmin();

  const end = new Date();
  const start = new Date(end.getFullYear(), end.getMonth(), 1);

  consultDateRange.value = [formatDate(start), formatDate(end)];

  // 初始加载数据
  fetchProjectStatistics();
});

watch([sourceFilter, projectIntentIds, developerFilter, deptId], () => {
  if (consultDateRange.value && consultDateRange.value.length === 2) {
    fetchProjectStatistics();
  }
});
</script>

<style scoped>
.report-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
  background-color: #f5f7fa;
  overflow: hidden;
  min-width: 1200px;
}

.report-header {
  background-color: white;
  padding: 15px 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
}

.report-title {
  text-align: center;
  font-weight: bold;
  font-size: 20px;
  margin-bottom: 5px;
  color: #303133;
}

.report-date {
  text-align: center;
  font-size: 14px;
  margin-bottom: 15px;
  color: #606266;
}

.filter-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
  margin-bottom: 10px;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-label {
  white-space: nowrap;
  margin-right: 5px;
  font-size: 14px;
  color: #606266;
}

.filter-actions {
  margin-left: auto;
  display: flex;
  gap: 10px;
}

.date-range {
  min-width: 320px;
}

.dept-select {
  min-width: 180px;
}

:deep(.el-date-editor.el-input__wrapper),
:deep(.el-select) {
  width: 100%;
  flex: 1;
}

.table-container {
  flex: 1;
  margin: 15px 0 0 0;
  background-color: white;
  overflow: auto;
  position: relative;
  border-top: 1px solid #ebeef5;
  border-bottom: 1px solid #ebeef5;
}

.performance-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  table-layout: fixed;
}

.performance-table th,
.performance-table td {
  border: 1px solid #ebeef5;
  padding: 12px 8px;
  text-align: center;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.performance-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 10;
}

.total-row {
  background-color: #ebeef5;
  font-weight: bold;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.total-label {
  text-align: right;
  padding-right: 10px;
}

.performance-table td[rowspan] {
  vertical-align: middle;
  background-color: #f8f9fa;
  font-weight: 500;
  border-right: 2px solid #e6e6e6;
}

.footer-info {
  background-color: white;
  padding: 10px 20px;
  margin: 0;
  display: flex;
  justify-content: end;
  color: #606266;
  font-size: 14px;
  flex-shrink: 0;

  border-top: 1px solid #ebeef5;
}

.date-limit-info {
  color: #e6a23c;
  font-style: italic;
}

@media print {
  .report-container {
    background-color: white;
    height: auto;
  }

  .report-header {
    box-shadow: none;
  }

  .report-actions,
  .footer-info {
    display: none;
  }

  .table-container {
    margin: 0;
    border: none;
    overflow: visible;
    height: auto;
  }

  .performance-table th,
  .performance-table td {
    font-size: 12px;
    padding: 8px 4px;
  }
}

@media (max-width: 768px) {
  .filter-item {
    max-width: 100%;
  }

  .report-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .filters-row {
    flex-direction: column;
  }

  .filter-item {
    width: 100%;
  }

  .filter-controls {
    margin-top: 10px;
    width: 100%;
    justify-content: space-between;
  }
}

@media (min-width: 1400px) {
  .performance-table .amount {
    min-width: 120px;
    max-width: 150px;
  }
}
</style>
