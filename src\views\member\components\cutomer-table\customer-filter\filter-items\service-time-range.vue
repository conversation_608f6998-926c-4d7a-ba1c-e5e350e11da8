<template>
  <filter-item type="base" label="最近服务时间" :text="text" :width="320" @onClick="show()">
    <date-range v-model="serviceTime" v-model:text="text" v-model:visible="visible" />
  </filter-item>
</template>
<script setup>
import { computed, ref } from 'vue';
import dayjs from 'dayjs'
import dateRange from './date-range.vue'
import FilterItem from './filter-item.vue';

const serviceTime = ref([]);
const visible = ref(false)
const text = ref('');

function show() {
  visible.value = true
}

function reset() { serviceTime.value = [] }

function getParam() {
  const params = {};
  if (serviceTime.value[0] && dayjs(serviceTime.value[0]).isValid()) {
    params.startServiceTime = serviceTime.value[0];
  }
  if (serviceTime.value[1] && dayjs(serviceTime.value[1]).isValid()) {
    params.endServiceTime = serviceTime.value[1];
  }
  return params
}

defineExpose({
  getParam,
  reset
})
</script>
<style lang="scss" scoped></style>
