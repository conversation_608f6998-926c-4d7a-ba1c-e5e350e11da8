<template>
  <el-input v-model.trim="filterText" :prefix-icon="Search" placeholder="输入搜索内容"></el-input>
  <el-scrollbar class="mt-10px" wrap-style="max-height: 40vh">
    <el-tree ref="sourceTreeRef" :data="sourceTree" show-checkbox node-key="id" :props="{class:'customer-source-tree'}"
      :expand-on-click-node="false" :filter-node-method="filterNode" @check-change="changeSource">
      <template #default="{ node, data }">
        <div class="w-0 flex-grow flex items-center" @click="expand(data)">
          <div class="w-0 flex-grow truncate">{{ node.label }}</div>
          <div class></div>
          <a v-if="data.children && data.children.length" class="flex-shrink-0 ml-5px">
            <el-icon v-if="treeExpandMap[data.id]">
              <CaretTop />
            </el-icon>
            <el-icon v-else>
              <CaretBottom />
            </el-icon>
          </a>
        </div>
      </template>
    </el-tree>
  </el-scrollbar>
</template>
<script setup>
import { computed, ref, watch } from 'vue';
import { useVModels } from '@vueuse/core';
import { storeToRefs } from 'pinia';
import { memberStore } from "@/store/member";
import { CaretBottom, CaretTop, Search } from '@element-plus/icons-vue';

const props = defineProps({
  modelValue: { type: String, default: '' }
})
const emits = defineEmits(['update:modelValue'])
const { modelValue } = useVModels(props, emits);
const { corpInfo } = storeToRefs(memberStore());
const sourceTree = computed(() => Array.isArray(corpInfo.value.customerSourceList) ? convertData(corpInfo.value.customerSourceList) : [])
const treeExpandMap = ref({});
const sourceTreeRef = ref();
const filterText = ref('');

watch(filterText, (val) => {
  sourceTreeRef.value && sourceTreeRef.value.filter(val)
})

function expand(data) {
  const nodesMap = sourceTreeRef.value.store.nodesMap;
  if (!nodesMap[data.id]) return;
  if (data.children && data.children.length) {
    nodesMap[data.id].expanded = !nodesMap[data.id].expanded;
    treeExpandMap.value = Object.keys(nodesMap).reduce((acc, key) => {
      const node = nodesMap[key];
      if (node.expanded) {
        acc[key] = node.expanded;
      }
      return acc;
    }, {});
  } else {
    nodesMap[data.id].checked = !nodesMap[data.id].checked;
  }
}

function filterNode(value, data) {
  return value ? data.label.includes(value) : true
}

function changeSource() {
  const nodes = sourceTreeRef.value.getCheckedNodes()
  modelValue.value = nodes.filter(i => !i.children).map(i => i.label);
}

function reset() {
  sourceTreeRef.value && sourceTreeRef.value.setCheckedKeys([], false)
}

function convertData(data) {
  return data.reduce((acc, item) => {
    if (!item.disable) {
      const newItem = {
        id: item.sourceId,
        label: item.sourceName
      };
      if (item.children && item.children.length > 0) {
        newItem.children = convertData(item.children);
      }
      acc.push(newItem);
    }
    return acc;
  }, []);
}

defineExpose({
  reset
})

</script>

<style lang="scss" scoped>
:deep(.customer-source-tree > .el-tree-node__content > .el-tree-node__expand-icon) {
  display: none;
}
</style>
