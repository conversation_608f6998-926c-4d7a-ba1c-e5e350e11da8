<template>
  <my-layout v-loading="loading">
    <layout-item>
      <div class="mt-12px">
        <div class="mb-12px px-18px py-12px bg-white rounded">
          <div class="flex items-center justify-between">
            <div class="flex flex-nowrap items-center">
              <div class="flex gap-10px">
                <div class="px-14px py-7px text-14px rounded-full cursor-pointer transition-all duration-200 border" :class="isAllStatusSelected ? 'bg-blue-500 text-white border-blue-500' : 'bg-gray-50 text-gray-600 border-gray-200 hover:border-blue-300'" @click="selectAllStatus">全部</div>
                <div v-for="status in ToDoTaskStatus" :key="status.value" class="px-14px py-7px text-14px rounded-full cursor-pointer transition-all duration-200 border" :class="eventStatus && eventStatus.length === 1 && eventStatus[0] === status.value ? 'bg-blue-500 text-white border-blue-500' : 'bg-gray-50 text-gray-600 border-gray-200 hover:border-blue-300'" @click="toggleStatus(status.value)">
                  {{ status.label }}
                </div>
              </div>
            </div>
            <el-popover placement="left-start" trigger="click" :width="800" v-model:visible="showMoreFilters" :teleported="false">
              <template #reference>
                <div class="flex items-center cursor-pointer">
                  <div class="text-14px text-gray-600 mr-8px">更多筛选</div>
                  <el-icon class="transition-transform duration-300" :class="showMoreFilters ? 'rotate-180' : ''">
                    <ArrowDown />
                  </el-icon>
                </div>
              </template>
              <div class="overflow-y-auto space-y-20px p-16px">
                <div>
                  <div class="text-14px text-gray-600 mb-12px">状态筛选：</div>
                  <div class="flex flex-wrap gap-8px">
                    <el-button :type="tempFilterData.selectedStatusValue === '' ? 'primary' : ''" :plain="tempFilterData.selectedStatusValue !== ''" size="default" @click="selectAllStatusInFilter" style="width: 80px">全部</el-button>
                    <el-button v-for="status in ToDoTaskStatus" :key="status.value" :type="tempFilterData.selectedStatusValue === status.value ? 'primary' : ''" :plain="tempFilterData.selectedStatusValue !== status.value" size="default" @click="selectSingleStatusInFilter(status.value)" style="width: 80px">
                      {{ status.label }}
                    </el-button>
                  </div>
                </div>
                <div>
                  <div class="text-14px text-gray-600 mb-12px">任务类型：</div>
                  <div class="flex flex-wrap gap-8px">
                    <el-button :type="tempFilterData.selectedTypeValue === '' ? 'primary' : ''" :plain="tempFilterData.selectedTypeValue !== ''" size="default" @click="selectAllType" style="width: 80px">全部</el-button>
                    <el-button v-for="type in eventTypeList" :key="type.value" :type="tempFilterData.selectedTypeValue === type.value ? 'primary' : ''" :plain="tempFilterData.selectedTypeValue !== type.value" size="default" @click="selectSingleEventType(type.value)" style="width: 80px">
                      {{ type.label }}
                    </el-button>
                  </div>
                </div>

                <div class="flex gap-32px">
                  <div>
                    <div class="text-14px text-gray-600 mb-12px">客户姓名：</div>
                    <el-input v-model="tempFilterData.name" placeholder="请输入客户姓名" clearable size="default" style="width: 340px; text-align: left" />
                  </div>
                  <div>
                    <div class="text-14px text-gray-600 mb-12px">所属团队：</div>
                    <el-select v-model="tempFilterData.selectedTeamId" placeholder="请选择团队" clearable size="default" style="width: 340px" :teleported="false">
                      <el-option v-for="team in teamList" :key="team.teamId" :label="team.name" :value="team.teamId" />
                    </el-select>
                  </div>
                </div>
                <div class="flex gap-32px">
                  <div>
                    <div class="text-14px text-gray-600 mb-12px">回访日期：</div>
                    <el-date-picker v-model="tempFilterData.dates" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" size="default" style="width: 340px" :teleported="false" placement="top" />
                  </div>
                  <div>
                    <div class="text-14px text-gray-600 mb-12px">处理日期：</div>
                    <el-date-picker v-model="tempFilterData.processDates" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" size="default" style="width: 340px" :teleported="false" placement="top" />
                  </div>
                </div>

                <div class="flex justify-center gap-12px mt-20px">
                  <el-button type="primary" size="default" @click="handleFilterConfirm">查询</el-button>
                  <el-button plain type="primary" size="default" @click="handleFilterReset">重置</el-button>
                </div>
              </div>
            </el-popover>
          </div>
        </div>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <div class="h-full flex">
        <div class="w-1/3 min-w-200px max-w-300px flex-shrink-0 h-full">
          <!-- 刷新按钮 -->
          <div class="flex items-center justify-between mb-8px">
            <div class="text-14px text-gray-600">待办列表</div>
            <el-button :loading="refreshing" size="small" type="primary" :icon="RefreshRight" circle @click="refreshData" title="刷新列表" />
          </div>

          <div style="height: calc(100% - 50px - 32px)">
            <div ref="refreshContainer" class="h-full relative overflow-hidden" @touchstart="handleTouchStart" @touchmove="handleTouchMove" @touchend="handleTouchEnd" @mousedown="handleMouseDown" @mousemove="handleMouseMove" @mouseup="handleMouseUp" @mouseleave="handleMouseLeave">
              <div v-if="refreshing || pullDistance > 60" class="absolute top-0 left-0 w-full bg-blue-50 text-center py-10px text-14px text-blue-600 z-10 transition-all duration-300" :style="{ transform: `translateY(${Math.min(pullDistance, 60)}px)` }">
                <div v-if="refreshing" class="flex items-center justify-center">
                  <el-icon class="animate-spin mr-5px">
                    <Loading />
                  </el-icon>
                  正在刷新...
                </div>
                <div v-else-if="pullDistance > 60" class="flex items-center justify-center">
                  <el-icon class="mr-5px">
                    <ArrowDown />
                  </el-icon>
                  松开刷新
                </div>
                <div v-else class="flex items-center justify-center">
                  <el-icon class="mr-5px">
                    <ArrowDown />
                  </el-icon>
                  下拉刷新
                </div>
              </div>

              <div class="h-full" :style="{ transform: `translateY(${refreshing ? '60px' : '0px'})` }">
                <el-scrollbar v-if="list.length" ref="scrollbarRef" class="h-full" @scroll="handleScroll">
                  <div v-for="i in list" :key="i._id" class="bg-white rounded px-15px mb-10px cursor-pointer" :class="todo && todo._id === i._id ? 'shadow-lg border-blue-500 border-2px' : ''" @click="toggle(i)">
                    <div class="flex items-center justify-between py-10px">
                      <div class="px-10px py-5px bg-orange-400 text-white text-13px rounded" :class="i.taskType ? '' : 'opacity-0'">{{ i.taskType }}</div>
                      <div v-if="i.taskStatus" class="text-13px">{{ i.taskStatus }}</div>
                    </div>
                    <div class="line-clamp-2 text-13px leading-20px max-h-40px overflow-hidden">{{ i.taskContent }}</div>
                    <div class="mt-10px py-10px flex items-center justify-between border-t border-gray-200">
                      <div class="flex-grow w-0 truncate mr-10px text-14px">
                        <el-icon class="transform translate-y-2px">
                          <Avatar />
                        </el-icon>
                        {{ i.customerName }}
                      </div>
                      <div class="text-13px text-gray-500">计划执行： {{ i.planTime }}</div>
                    </div>
                  </div>

                  <!-- 加载更多提示 -->
                  <div v-if="list.length > 0" class="py-15px text-center">
                    <div v-if="isLoadingMore" class="flex items-center justify-center text-14px text-gray-500">
                      <el-icon class="animate-spin mr-8px">
                        <Loading />
                      </el-icon>
                      正在加载更多...
                    </div>
                    <div v-else-if="!hasMoreData" class="text-14px text-gray-400">已加载全部数据</div>
                    <div v-else class="text-14px text-gray-400">下拉加载更多</div>
                  </div>
                </el-scrollbar>
                <div v-else class="h-full flex flex-col items-center justify-center bg-white">
                  <empty-data :top="0" title="暂无待办" padding="0 0 15px" text-top="10px" :image-width="100"></empty-data>
                </div>
              </div>
            </div>
          </div>
          <div class="p-10px mt-10px flex items-center justify-between h-40px rounded bg-white">
            <div class="text-13px flex-shrink-0 text-gray-500">共{{ total }}条</div>
            <el-pagination v-model:current-page="page" :page-size="15" size="small" layout="prev,slot, next" pager-count="0" :total="total" @current-change="handleCurrentChange">
              <span>{{ page }}</span>
            </el-pagination>
          </div>
        </div>
        <el-scrollbar v-if="todo && todo._id" ref="todoScrollbarRef" class="flex-grow h-full ml-10px bg-white">
          <div class="p-15px border-b border-gray-200">
            <div class="flex items-center">
              <user-avatar flex-shrink-0 class="mr-10px" :size="30" />
              <div class="mr-10px text-16px font-semibold">{{ todo.customerName }}</div>
              <div v-if="customer.externalUserId && wechatName" class="mr-10px flex items-center text-[#66CB70] hover:underline cursor-pointer hover:bg-gray-100 px-6px py-4px rounded" @click="chat()">
                <svg-icon name="wechat" size="16"></svg-icon>
                <div class="ml-5px max-w-120px truncate text-14px" :title="`微信联系人：${wechatName}`">{{ wechatName }}</div>
              </div>
              <div class="mr-10px text-13px">{{ customer.sex }} {{ customer.sex && customer.age ? " | " : "" }} {{ customer.age }}</div>
              <el-button v-if="customer._id" plain type="primary" size="small" @click="showCustomer()">客户详情</el-button>
              <el-button v-if="customer._id" plain type="primary" size="small" @click="openDrawer()">回访记录</el-button>
            </div>
            <div  class="text-14px mt-15px">联系方式：{{ customer.mobile || customer.phone1 || customer.phone2 || customer.phone3 }}</div>
          </div>
          <div class="p-15px border-b border-gray-200">
            <div class="text-16px font-semibold">{{ todo.taskType }}</div>
            <div class="text-14px mt-15px leading-20px">任务内容：{{ todo.taskContent }}</div>
            <div v-if="todo.sendContent || (todo.files && todo.files.length)" class="text-14px leading-20px mt-15px bg-gray-100 bg-opacity-80 p-10px">
              <div>向客户发送内容：</div>
              <div>{{ todo.sendContent }}</div>
              <template v-if="todo.files && todo.files.length">
                <div v-for="file in todo.files" class="cursor-pointer" color-primary @click="readFile(file)">附件：{{ file.showName }}</div>
              </template>
              <div class="text-right">
                <el-button type="primary" @click="send()">去发送</el-button>
              </div>
            </div>
          </div>
          <div class="p-15px border-b border-gray-200">
            <div class="flex text-14px">
              <div class="mr-1/10">
                <span class="text-gray-500">创建人：</span>
                <span v-if="todo.creatorUserId === 'system'">系统自动</span>
                <ww-user v-else :openid="todo.creatorUserId"></ww-user>
              </div>
              <div class="mr-1/10">
                <span class="text-gray-500">处理人：</span>
                <ww-user v-if="todo.executorUserId" :openid="todo.executorUserId"></ww-user>
              </div>
              <div v-if="todo.eventStatus === 'untreated'">
                <span class="text-gray-500">计划执行时间：</span>
                {{ todo.planTime }}
                <svg-icon name="pen" class="inline-block cursor-pointer ml-4px text-blue-500" @click="show()" />
              </div>
            </div>
            <div class="text-14px mt-15px flex" v-if="todo.executeMethod !== 'groupTask'">
              <div class="text-gray-500 flex-shrink-0">处理结果：</div>
              <el-input v-if="todo.eventStatus == 'untreated'" v-model="todo.result" :autosize="{ minRows: 4, maxRows: 8 }" placeholder="" type="textarea" resize="none"></el-input>
              <span v-else>{{ todo.result }}</span>
            </div>
          </div>
          <div v-if="todo.eventStatus === 'untreated' && todo.executeMethod !== 'groupTask'" class="py-10px text-center">
            <el-button plain class="w-100px" type="primary" @click="confirm('closed')">取消任务</el-button>
            <el-button class="w-100px" type="primary" @click="confirm('treated')">完成任务</el-button>
            <el-button class="w-120px" type="primary" @click="completeAndAddNew">完成并新增待办</el-button>
          </div>
        </el-scrollbar>
        <div v-else class="flex-grow flex items-center justify-center h-full">
          <empty-data :top="0" title="暂无待办" padding="0 0 15px" text-top="10px" :image-width="200"></empty-data>
        </div>
      </div>
    </layout-main>
  </my-layout>
  <customer-detail :visible="customerDetailVisible" @close="customerDetailVisible = false" :customerId="customer._id" title="客户详情" @update="updateCustomer" customerType="corpCustomer" />
  <change-date :todo="todo" :width="width" :visible="visible" @close="close" @change="confirmDate" />
  <survery-dialog :data="currentSurvery" :visible="surveryVisible" @close="surveryVisible = false" />
  <task-drawer :selectCustomers="newTaskData.customerId ? [{ _id: newTaskData.customerId, name: newTaskData.customerName, externalUserId: newTaskData.externalUserId }] : []" :type="taskDrawerType" :teamId="teamId" :memberList="memberList" :customer="newTaskData.customerId ? { _id: newTaskData.customerId, name: newTaskData.customerName, externalUserId: newTaskData.externalUserId } : {}" :data="newTaskData" :visible="taskDrawerVisible" @close="closeTaskDrawer" @change="handleChangeTask" />
  <component v-if="ManagePlanDrawer" :is="ManagePlanDrawer" :customer="customer || {}" :teamId="team.teamId" :visible="drawerVisible" @close="closeDrawer" />
  <el-image-viewer
    v-if="showViewer"
    :url-list="[previewUrl]"
    @close="
      () => {
        showViewer = false;
      }
    "
  />
</template>
<script setup>
import { computed, nextTick, onMounted, ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { ElMessage, ElMessageBox } from "element-plus";
import { ArrowDown, Loading, RefreshRight } from "@element-plus/icons-vue";
import dayjs from "dayjs";
import { getCustomerInfoById } from "@/api/member";
import { getToDoList, setTodoStatus } from "@/api/todo";
import { getAnswer, getDetail } from "@/api/survery";
import { ToDoEventType, ToDoTaskStatus, ToDoTaskStatusObj } from "@/baseData";
import useManagePlanDrawer from "@/composables/useManagePlanDrawer";
import { getCustomerAge } from "@/helpers/customer";
import useModal from "@/hooks/useModal";
import wxContact from "@/store/wxContact";
import { teamStore } from "@/store/team";
import { openChatWindow } from "@/utils/common";
import { formatTodoFiles } from "@/utils/todo";

import customerDetail from "@/views/member/detail/index.vue";
import changeDate from "./change-date.vue";
import EmptyData from "@/components/empty-data.vue";
import { DateRangeFilter, CheckBoxFilter } from "@/components/filter-bar";
import SvgIcon from "@/components/svg-icon";
import SurveryDialog from "@/views/system/survery/dialog.vue";
import MyLayout, { LayoutMain } from "@/components/layout";
import UserAvatar from "@/components/user-avatar";
import TaskDrawer from "@/components/manage-plan-drawer/task-drawer.vue";

const pic = new URL("@/assets/group-msg.png", import.meta.url).href; // 默认logo

const emits = defineEmits(["change", "update:followUpDate"]);
const props = defineProps({
  showFilter: { type: Boolean, default: false },
  team: { type: Object, default: () => ({}) },
  todoStatus: { type: String, default: "pending" },
  followUpDate: { type: Array, default: () => [] },
});
const { getContacts } = wxContact();
const { contactName } = storeToRefs(wxContact());
const { teams, allTeams, isAdmin } = storeToRefs(teamStore());
const customerIdMap = ref({});
const customerDetailVisible = ref(false);
const dates = ref([dayjs().subtract(7, "day").format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")]);
const processDates = ref([]);
const selectedTeamId = ref("");
const currentStatus = ref("untreated");
const eventStatus = ref(["untreated"]); // 默认显示未处理状态
const eventType = ref([]);
const showMoreFilters = ref(false);
const tempFilterData = ref({
  name: "",
  selectedTeamId: "",
  dates: [dayjs().subtract(7, "day").format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")],
  processDates: [],
  selectedTypeValue: "", // 单选任务类型
  selectedStatusValue: "untreated", // 单选状态
});
const list = ref([]);
const loading = ref(false);
const name = ref("");
const page = ref(1);
let payload = {};
const scrollbarRef = ref();
const todoScrollbarRef = ref();
const total = ref(0);
const todo = ref();
const previewUrl = ref("");
const showViewer = ref(false);
const { close, show, visible, width } = useModal(560);
const { visible: drawerVisible, ManagePlanDrawer, openDrawer, closeDrawer } = useManagePlanDrawer();
const currentSurvery = ref({});
const surveryVisible = ref(false);
const taskDrawerVisible = ref(false);
const taskDrawerType = ref("editTask");
const newTaskData = ref({});
const memberList = computed(() => {
  const teamsToSearch = isAdmin.value ? allTeams.value : teams.value;
  const currentTeam = teamsToSearch.find((team) => team.teamId === teamId.value);
  return currentTeam && Array.isArray(currentTeam.memberList) ? currentTeam.memberList : [];
});
const customer = computed(() => {
  // 使用当前选择的团队ID或默认团队ID
  const currentTeamId = selectedTeamId.value || teamId.value;
  const res = todo.value?.customerId ? customerIdMap.value[`${todo.value.customerId}_${currentTeamId}`] : null;
  return res ? { ...res, age: getCustomerAge(res) } : {};
});
const datesText = computed(() => (dates.value ? dates.value.join(" ~ ") : ""));
const processDatesText = computed(() => (processDates.value ? processDates.value.join(" ~ ") : ""));
const statusText = computed(() => {
  if (eventStatus.value && eventStatus.value.length === 1) return ToDoTaskStatusObj[eventStatus.value[0]];
  return "";
});
const typeText = computed(() => {
  if (eventType.value && eventType.value.length === 1) return ToDoEventType[eventType.value[0]];
  return "";
});
const teamId = computed(() => props.team?.teamId || "");
const teamList = computed(() => (isAdmin.value ? allTeams.value : teams.value) || []);
const eventTypeList = Object.keys(ToDoEventType).map((i) => ({
  label: ToDoEventType[i],
  value: i,
}));
const wechatName = computed(() => contactName.value[customer.value.externalUserId] || "");

const isAllStatusSelected = computed(() => {
  return !eventStatus.value || eventStatus.value.length === 0;
});

const isAllTypeSelected = computed(() => {
  return tempFilterData.value.selectedTypeValue === "";
});

const isAllStatusSelectedInFilter = computed(() => {
  return tempFilterData.value.selectedStatusValue === "";
});

// 下拉刷新相关状态
const refreshing = ref(false);
const pullDistance = ref(0);
const startY = ref(0);
const currentY = ref(0);
const isPulling = ref(false);
const refreshContainer = ref(null);
const scrollTop = ref(0);

// 下拉加载更多相关状态
const loadingMore = ref(false);
const hasMoreData = ref(true);
const isLoadingMore = ref(false);

// 在筛选器中选择所有状态
function selectAllStatusInFilter() {
  tempFilterData.value.selectedStatusValue = "";
}

// 在筛选器中选择单个状态
function selectSingleStatusInFilter(statusValue) {
  tempFilterData.value.selectedStatusValue = statusValue;
}

// 处理 Popover 确认事件
function handleFilterConfirm() {
  // 将临时数据应用到实际筛选条件
  const previousTeamId = selectedTeamId.value;
  name.value = tempFilterData.value.name;
  selectedTeamId.value = tempFilterData.value.selectedTeamId;
  dates.value = tempFilterData.value.dates;
  processDates.value = tempFilterData.value.processDates;
  eventType.value = tempFilterData.value.selectedTypeValue ? [tempFilterData.value.selectedTypeValue] : [];
  eventStatus.value = tempFilterData.value.selectedStatusValue ? [tempFilterData.value.selectedStatusValue] : [];

  // 通知父组件日期变化（包括清空的情况）
  if (dates.value && dates.value.length === 2) {
    emits("update:followUpDate", [...dates.value]);
  } else if (!dates.value || dates.value.length === 0) {
    emits("update:followUpDate", []);
  }

  // 如果团队发生变化，清空当前选中的待办
  if (previousTeamId !== selectedTeamId.value) {
    todo.value = null;
  }

  // 重置加载更多状态
  hasMoreData.value = true;
  isLoadingMore.value = false;

  console.log("筛选确认:", {
    selectedTypeValue: tempFilterData.value.selectedTypeValue,
    eventType: eventType.value,
    selectedStatusValue: tempFilterData.value.selectedStatusValue,
    eventStatus: eventStatus.value,
    selectedTeamId: selectedTeamId.value,
  });

  // 关闭 Popover 并执行搜索
  showMoreFilters.value = false;
  search();
}

function handleFilterReset() {
  // 重置时默认选择当前团队
  const defaultTeamId = teamId.value || "";

  tempFilterData.value = {
    name: "",
    selectedTeamId: defaultTeamId,
    dates: [dayjs().subtract(7, "day").format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")],
    processDates: [],
    selectedTypeValue: "",
    selectedStatusValue: "untreated",
  };

  // 应用重置的数据
  name.value = tempFilterData.value.name;
  selectedTeamId.value = tempFilterData.value.selectedTeamId;
  dates.value = tempFilterData.value.dates;
  processDates.value = tempFilterData.value.processDates;
  eventType.value = [];
  eventStatus.value = ["untreated"];

  // 重置加载更多状态
  hasMoreData.value = true;
  isLoadingMore.value = false;

  // 通知父组件日期重置
  emits("update:followUpDate", [...tempFilterData.value.dates]);

  // 关闭 Popover 并执行搜索
  showMoreFilters.value = false;
  search();
}

function chat() {
  openChatWindow(customer.value._id, customer.value.externalUserId);
}

function selectAllStatus() {
  eventStatus.value = [];
  search();
}

function selectAllType() {
  tempFilterData.value.selectedTypeValue = "";
}

function selectSingleEventType(typeValue) {
  tempFilterData.value.selectedTypeValue = typeValue;
}

function toggleStatus(statusValue) {
  if (eventStatus.value && eventStatus.value.length === 1 && eventStatus.value[0] === statusValue) {
    return;
  }
  eventStatus.value = [statusValue];
  search();
}

// 下拉刷新处理函数
function handleTouchStart(e) {
  console.log("Touch start, scrollTop:", scrollTop.value);
  if (scrollTop.value <= 0) {
    startY.value = e.touches[0].clientY;
    isPulling.value = true;
    pullDistance.value = 0;
    console.log("开始下拉刷新手势");
  }
}

function handleTouchMove(e) {
  if (!isPulling.value || refreshing.value) return;

  currentY.value = e.touches[0].clientY;
  const deltaY = currentY.value - startY.value;

  if (deltaY > 0 && scrollTop.value <= 0) {
    e.preventDefault();
    pullDistance.value = Math.min(deltaY * 0.5, 100); // 限制拖拽距离
  } else {
    isPulling.value = false;
    pullDistance.value = 0;
  }
}

function handleTouchEnd() {
  if (!isPulling.value || refreshing.value) return;

  isPulling.value = false;

  if (pullDistance.value > 60) {
    refreshData();
  } else {
    pullDistance.value = 0;
  }
}

// 鼠标事件处理函数
function handleMouseDown(e) {
  if (scrollTop.value <= 0) {
    startY.value = e.clientY;
    isPulling.value = true;
    pullDistance.value = 0;
  }
}

function handleMouseMove(e) {
  if (!isPulling.value || refreshing.value) return;

  currentY.value = e.clientY;
  const deltaY = currentY.value - startY.value;

  if (deltaY > 0 && scrollTop.value <= 0) {
    e.preventDefault();
    pullDistance.value = Math.min(deltaY * 0.5, 100);
  } else {
    isPulling.value = false;
    pullDistance.value = 0;
  }
}

function handleMouseUp() {
  if (!isPulling.value || refreshing.value) return;

  isPulling.value = false;

  if (pullDistance.value > 60) {
    refreshData();
  } else {
    pullDistance.value = 0;
  }
}

function handleMouseLeave() {
  if (isPulling.value) {
    isPulling.value = false;
    pullDistance.value = 0;
  }
}

function handleScroll(e) {
  scrollTop.value = e.scrollTop;
  // 通过 ref 获取滚动容器的信息
  if (scrollbarRef.value) {
    // 尝试不同的属性名
    const scrollContainer = scrollbarRef.value.wrapRef || scrollbarRef.value.wrap$ || scrollbarRef.value.$el?.querySelector(".el-scrollbar__wrap");

    if (scrollContainer) {
      const currentScrollTop = scrollContainer.scrollTop;
      const scrollHeight = scrollContainer.scrollHeight;
      const clientHeight = scrollContainer.clientHeight;
      const distance = scrollHeight - clientHeight - currentScrollTop;

      // 当距离底部50px以内时触发加载更多
      if (distance <= 50 && !isLoadingMore.value && hasMoreData.value) {
        console.log("触发加载更多");
        loadMore();
      }
    } else {
      console.log("未找到滚动容器");
    }
  } else {
    console.log("scrollbarRef.value 为空");
  }
}

// 刷新数据
async function refreshData() {
  if (refreshing.value) return;

  refreshing.value = true;
  pullDistance.value = 60;

  // 重置加载更多状态
  hasMoreData.value = true;
  isLoadingMore.value = false;
  page.value = 1;

  try {
    await getList();
    ElMessage.success("刷新成功");
  } catch (error) {
    ElMessage.error("刷新失败");
  } finally {
    setTimeout(() => {
      refreshing.value = false;
      pullDistance.value = 0;
    }, 500);
  }
}

// 加载更多数据
async function loadMore() {
  console.log("loadMore 被调用", {
    isLoadingMore: isLoadingMore.value,
    hasMoreData: hasMoreData.value,
    currentPage: page.value,
    listLength: list.value.length,
    total: total.value,
  });

  if (isLoadingMore.value || !hasMoreData.value) {
    console.log("loadMore 提前返回:", {
      isLoadingMore: isLoadingMore.value,
      hasMoreData: hasMoreData.value,
    });
    return;
  }

  isLoadingMore.value = true;
  const nextPage = page.value + 1;

  console.log("开始加载第", nextPage, "页");

  try {
    const targetTeamId = selectedTeamId.value || teamId.value;
    const { success, data, message } = await getToDoList(
      nextPage,
      15,
      {
        ...payload,
        executorUserId: localStorage.getItem("userId"),
        corpId: localStorage.getItem("corpId"),
      },
      { orderBy: "plannedExecutionTime", orderType: "asc" },
      targetTeamId
    );

    console.log("API 响应:", { success, data, message });

    if (success) {
      const { data: tableData = [], total: count } = data;
      const newData = Array.isArray(tableData) ? formatList(tableData) : [];

      console.log("新数据:", {
        newDataLength: newData.length,
        totalCount: count,
        currentListLength: list.value.length,
      });

      if (newData.length > 0) {
        // 追加新数据到现有列表
        list.value = [...list.value, ...newData];
        page.value = nextPage;
        total.value = count;

        console.log("数据追加完成:", {
          newListLength: list.value.length,
          currentPage: page.value,
          total: total.value,
        });

        // 检查是否还有更多数据 (基于页数判断)
        const pageSize = 15;
        if (page.value * pageSize >= count) {
          hasMoreData.value = false;
          console.log("没有更多数据了", {
            page: page.value,
            pageSize,
            loaded: page.value * pageSize,
            total: count,
          });
        }
      } else {
        hasMoreData.value = false;
        console.log("新数据为空，设置为没有更多数据");
      }
    } else {
      console.error("API 调用失败:", message);
      ElMessage.error(message);
    }
  } catch (error) {
    console.error("loadMore 异常:", error);
    ElMessage.error("加载更多失败");
  } finally {
    isLoadingMore.value = false;
    console.log("loadMore 完成");
  }
}

function send() {
  if (!customer.value.externalUserId) {
    ElMessage.info("暂未绑定客户微信");
    return;
  }
  if (todo.value.executeMethod === "groupTask") {
    previewUrl.value = pic;
    showViewer.value = true;
  } else {
    openChatWindow(customer.value._id, customer.value.externalUserId, "", { name: "todo", time: new Date().getTime() });
  }
}

function confirmDate(date) {
  todo.value.plannedExecutionTime = dayjs(date).valueOf();
  todo.value.planTime = formatTime(date);
  todo.value.expireTime = dayjs(date).add(7, "day").valueOf();
  const index = list.value.findIndex((i) => i._id === todo.value._id);
  if (index >= 0) {
    list.value[index].plannedExecutionTime = dayjs(date).valueOf();
    list.value[index].planTime = formatTime(date);
    list.value[index].expireTime = dayjs(date).add(7, "day").valueOf();
  }
}

function formatTime(str) {
  return str && dayjs(str).isValid() ? dayjs(str).format("YYYY-MM-DD HH:mm").replace("00:00", "") : "";
}

function formatList(list) {
  return list.map((i) => {
    const status = ToDoTaskStatus.find((item) => item.value === i.eventStatus);
    const item = {
      ...i,
      taskStatus: status ? status.label : "",
      taskType: ToDoEventType[i.eventType] || "",
      executionTime: formatTime(i.executionTime),
      createTime: formatTime(i.createTime),
      planTime: formatTime(i.plannedExecutionTime),
      expireTime: formatTime(i.expireTime),
      endTime: formatTime(i.endTime),
    };
    item.willBeExpired = item.expireTime ? dayjs().isAfter(dayjs(item.expireTime).subtract(2, "days")) && dayjs().isBefore(dayjs(item.expireTime)) : false;
    return item;
  });
}

function search() {
  const query = {};
  if (name.value.trim()) query.customerName = name.value.trim();
  if (dates.value && dates.value.length) query.plannedExecutionEndTime = [...dates.value];
  if (processDates.value && processDates.value.length) query.processTime = [...processDates.value];
  // 注意：teamId 不在这里设置，而是在 getList() 中直接传递
  if (eventStatus.value && eventStatus.value.length) query.eventStatus = eventStatus.value.join(",");
  if (eventType.value && eventType.value.length) {
    // 单选模式：如果只有一个值，直接传递字符串
    query.eventType = eventType.value.length === 1 ? eventType.value[0] : eventType.value.join(",");
  }
  console.log("搜索参数:", { eventType: eventType.value, query, selectedTeamId: selectedTeamId.value });
  payload = { ...query };
  page.value = 1;
  // 重置加载更多状态
  hasMoreData.value = true;
  isLoadingMore.value = false;
  getList();
}

function reset() {
  // 重置时默认选择当前团队
  const defaultTeamId = teamId.value || "";

  dates.value = [dayjs().subtract(7, "day").format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")];
  processDates.value = [];
  selectedTeamId.value = defaultTeamId;
  name.value = "";
  eventStatus.value = [];
  eventType.value = [];
  showMoreFilters.value = false;
  tempFilterData.value = {
    name: "",
    selectedTeamId: defaultTeamId,
    dates: [dayjs().subtract(7, "day").format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")],
    processDates: [],
    selectedTypeValue: "",
    selectedStatusValue: "untreated",
  };

  // 重置加载更多状态
  hasMoreData.value = true;
  isLoadingMore.value = false;

  // 通知父组件日期重置
  emits("update:followUpDate", [...dates.value]);

  search();
}

function showCustomer() {
  customerDetailVisible.value = true;
}

function toggle(i) {
  todo.value = { ...i, ...formatTodoFiles(i) };
}

function handleCurrentChange(p) {
  page.value = p;
  // 重置加载更多状态
  hasMoreData.value = true;
  isLoadingMore.value = false;
  getList();
}

function updateCustomer() {
  getCustomerInfo(customer.value._id);
}

function readFile(file) {
  if (file._type === "questionnaire") {
    getSurvery(file.file || {});
  }
  if (file._type === "article") {
    file.file && file.file.url && window.open(file.file.url);
  }
  if (file._type === "image" && file.URL) {
    previewUrl.value = file.URL;
    showViewer.value = true;
  }
}

async function getSurvery(item) {
  const { surveryId, answerId } = item;
  if (answerId && surveryId) {
    const { data } = await getAnswer(surveryId, form.value.customerId, answerId);
    if (data && data.record && data.record.submitTime) {
      currentSurvery.value = data && data.record ? data.record : {};
      surveryVisible.value = true;
      return;
    }
  }
  surveryId && getSurveryDetail(surveryId);
}
async function getSurveryDetail(id) {
  const corpId = localStorage.getItem("corpId");
  const { data } = await getDetail(corpId, id);
  currentSurvery.value = data && data.data ? data.data : {};
  surveryVisible.value = true;
}

async function confirm(type) {
  const action = type === "closed" ? "取消" : "完成";
  await ElMessageBox.confirm(`确定${action}该待办事项吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  });
  loading.value = true;
  const { success, message } = await setTodoStatus(todo.value._id, type, todo.value.result || `已${action}`, localStorage.getItem("userId"));
  if (success) {
    ElMessage.success(message);
    emits("change");
    getList();
  } else {
    ElMessage.error(message);
  }
  loading.value = false;
}

// 完成并新增待办
async function completeAndAddNew() {
  try {
    await ElMessageBox.confirm("确定完成该待办事项并新增待办吗?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    if (!todo.value) {
      ElMessage.error("请先选择一个待办事项");
      return;
    }

    if (!customer.value || !customer.value._id) {
      ElMessage.error("客户信息不完整，无法新增任务");
      return;
    }

    loading.value = true;

    // 在完成任务前保存当前待办和客户信息
    const currentTodo = { ...todo.value };
    const currentCustomer = { ...customer.value };

    const { success, message } = await setTodoStatus(currentTodo._id, "treated", currentTodo.result || "已完成", localStorage.getItem("userId"));

    if (success) {
      ElMessage.success(message);
      emits("change");

      // 准备新任务数据，使用保存的客户信息
      newTaskData.value = {
        executeMethod: "todo",
        customerId: currentCustomer._id,
        customerName: currentCustomer.name,
        externalUserId: currentCustomer.externalUserId,
        executorUserId: currentTodo.executorUserId,
        eventType: currentTodo.eventType,
        taskContent: currentTodo.taskContent,
        sendContent: currentTodo.sendContent || "",
        planExecutionTime: dayjs().format("YYYY-MM-DD"),
        fileList: currentTodo.files
          ? currentTodo.files.map((file) => ({
              type: file._type || "link",
              URL: file.URL || "",
              file: {
                name: file.showName || file.name || "",
                type: file._type || "",
                url: file.URL || "",
                surveryId: file.file?.surveryId || file.surveryId || "",
              },
            }))
          : [],
        enableSend: !!(currentTodo.sendContent || (currentTodo.files && currentTodo.files.length > 0)),
      };

      // 先打开任务抽屉，再刷新列表
      taskDrawerType.value = "editTask";
      taskDrawerVisible.value = true;

      // 刷新待办列表
      await getList();
    } else {
      ElMessage.error(message);
    }
  } catch (error) {
    // ElMessage.error('操作失败，请重试');
  } finally {
    loading.value = false;
  }
}

// 关闭任务抽屉
function closeTaskDrawer() {
  taskDrawerVisible.value = false;
  newTaskData.value = {};
  getList();
}

// 处理任务变更
function handleChangeTask(taskData) {
  closeTaskDrawer();
  ElMessage.success("任务创建成功");
  getList();
}

async function getCustomerInfo(_id) {
  // 使用当前选择的团队ID或默认团队ID
  const currentTeamId = selectedTeamId.value || teamId.value;
  const { success, data, message } = await getCustomerInfoById(localStorage.getItem("userId"), { _id });
  if (success) {
    customerIdMap.value[`${_id}_${currentTeamId}`] = data.data && data.data[0] ? data.data[0] : null;
  } else {
    ElMessage.error(message);
  }
}

async function getList() {
  loading.value = true;
  const currentTodoId = todo.value?._id;
  // 确定要使用的团队ID：优先使用筛选器中选择的团队，否则使用当前团队
  const targetTeamId = selectedTeamId.value || teamId.value;
  const { success, data, message } = await getToDoList(
    page.value,
    15,
    {
      ...payload,
      executorUserId: localStorage.getItem("userId"),
      corpId: localStorage.getItem("corpId"),
    },
    { orderBy: "plannedExecutionTime", orderType: "asc" },
    targetTeamId
  );
  if (success) {
    const { data: tableData = [], total: count } = data;
    list.value = Array.isArray(tableData) ? formatList(tableData) : [];
    total.value = count;

    console.log("getList 响应数据:", {
      listLength: list.value.length,
      totalCount: count,
      currentPage: page.value,
    });

    // 检查是否还有更多数据 (基于页数判断)
    const pageSize = 15;
    if (page.value * pageSize >= count) {
      hasMoreData.value = false;
      console.log("getList: 没有更多数据 (page * pageSize >= total)", {
        page: page.value,
        pageSize,
        loaded: page.value * pageSize,
        total: count,
      });
    } else {
      hasMoreData.value = true;
      console.log("getList: 还有更多数据", {
        page: page.value,
        pageSize,
        loaded: page.value * pageSize,
        total: count,
      });
    }

    console.log("getList 完成后状态:", {
      hasMoreData: hasMoreData.value,
      isLoadingMore: isLoadingMore.value,
    });
  } else {
    ElMessage.error(message);
  }
  if (list.value.length) {
    await nextTick();
    scrollbarRef.value.setScrollTop(0);
  }
  loading.value = false;

  let itemToSelect;
  if (currentTodoId) {
    itemToSelect = list.value.find((i) => i._id === currentTodoId);
  }
  if (!itemToSelect && list.value.length > 0) {
    itemToSelect = list.value[0];
  }

  if (itemToSelect) {
    todo.value = { ...itemToSelect, ...formatTodoFiles(itemToSelect) };
  } else {
    todo.value = null;
  }
}

watch(todo, async (val) => {
  if (val && val._id) {
    await nextTick();
    todoScrollbarRef.value.setScrollTop(0);
    // 使用当前选择的团队ID或默认团队ID
    const currentTeamId = selectedTeamId.value || teamId.value;
    const key = `${val.customerId}_${currentTeamId}`;
    if (!(key in customerIdMap.value)) {
      customerIdMap.value[key] = {};
      getCustomerInfo(val.customerId);
    }
  }
});
watch(
  teamId,
  (n) => {
    if (n) {
      // 重置加载更多状态
      hasMoreData.value = true;
      isLoadingMore.value = false;
      search();
    } else {
      list.value = [];
      total.value = 0;
    }
  },
  { immediate: true }
);

watch(
  [teamList, teamId],
  ([teams, currentTeamId]) => {
    // 如果没有手动选择团队，且有当前团队，则自动选择当前团队
    if (!selectedTeamId.value && currentTeamId && teams.length > 0) {
      const currentTeam = teams.find((team) => team.teamId === currentTeamId);
      if (currentTeam) {
        selectedTeamId.value = currentTeamId;
        tempFilterData.value.selectedTeamId = currentTeamId;
        console.log("自动设置默认团队:", currentTeamId, currentTeam.name);
      }
    }
    // 如果当前选择的团队不在团队列表中，重置为当前团队
    else if (selectedTeamId.value && currentTeamId && teams.length > 0) {
      const selectedTeam = teams.find((team) => team.teamId === selectedTeamId.value);
      if (!selectedTeam) {
        selectedTeamId.value = currentTeamId;
        tempFilterData.value.selectedTeamId = currentTeamId;
        console.log("重置为当前团队:", currentTeamId);
      }
    }
  },
  { immediate: true }
);

watch(customer, (n) => {
  if (n && n.externalUserId) getContacts([n.externalUserId]);
});

// 监听父组件传入的日期范围
watch(
  () => props.followUpDate,
  (newDate) => {
    if (newDate && newDate.length === 2) {
      dates.value = [...newDate];
      // 同时更新tempFilterData中的日期，确保筛选器显示正确的值
      tempFilterData.value.dates = [...newDate];
      search();
    } else if (!newDate || newDate.length === 0) {
      // 当日期被清空时
      dates.value = [];
      tempFilterData.value.dates = [];
      search();
    }
  },
  { immediate: true }
);

watch(showMoreFilters, (isVisible) => {
  if (isVisible) {
    tempFilterData.value = {
      name: name.value,
      selectedTeamId: selectedTeamId.value,
      dates: dates.value ? [...dates.value] : [],
      processDates: processDates.value ? [...processDates.value] : [],
      selectedTypeValue: eventType.value && eventType.value.length > 0 ? eventType.value[0] : "",
      selectedStatusValue: eventStatus.value && eventStatus.value.length > 0 ? eventStatus.value[0] : "",
    };
  }
});

// 组件初始化时确保设置默认团队
onMounted(() => {
  // 确保在组件挂载后立即检查并设置默认团队
  if (!selectedTeamId.value && teamId.value) {
    const teamsToSearch = isAdmin.value ? allTeams.value : teams.value;
    if (teamsToSearch.length > 0) {
      const currentTeam = teamsToSearch.find((team) => team.teamId === teamId.value);
      if (currentTeam) {
        selectedTeamId.value = teamId.value;
        tempFilterData.value.selectedTeamId = teamId.value;
        console.log("组件初始化时设置默认团队:", teamId.value, currentTeam.name);
      }
    }
  }
});
</script>

<style scoped>
.flex.flex-wrap.gap-8px .el-button + .el-button {
  margin-left: 0 !important;
}
</style>