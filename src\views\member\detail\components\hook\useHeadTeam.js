import { computed, ref, watch } from 'vue';
import { getTeamById } from "@/api/corp";

export default function useTeams(customer) {
  const teamList = ref([]);

  const teamIds = computed(() => {
    if (Array.isArray(customer.value.teamId)) {
      return customer.value.teamId
    } else if (typeof customer.value.teamId === 'string' && customer.value.teamId) {
      return [customer.value.teamId]
    }
    return []
  })

  async function getTeams() {
    const { success, data = {} } = await getTeamById('', teamIds.value);
    if (success) {
      const { data: list } = data;
      teamList.value = list
    } else {
      teamList.value = []
    }
  }

  watch(teamIds, n => {
    if (n.length) {
      getTeams()
    } else {
      teamList.value = []
    }
  }, { immediate: true })
  return teamList
}