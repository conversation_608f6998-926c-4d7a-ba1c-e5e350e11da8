import { teamStore } from "@/store/team";
import { memberStore } from "@/store/member";
import { post } from "./axios";
async function useMember(data) {
  const res = await post("member", data);
  return res;
}

export async function getFeeRecord(params) {
  const res = await useMember({
    type: "getFeeRecord",
    corpId: localStorage.getItem("corpId"),
    ...params,
  });
  return res;
}

export async function merageHisFeeRecord(params) {
  const res = await useMember({
    type: "merageHisFeeRecord",
    corpId: localStorage.getItem("corpId"),
    ...params,
  });
  return res;
}

export async function feeRecordStatistics(data) {
  const res = await useMember({
    type: "feeRecordStatistics",
    ...data,
  });
  return res;
}

export async function getCustomers(params = {}, externalUserIds) {
  const res = await useMember({
    type: "getMember",
    params,
    corpId: localStorage.getItem("corpId"),
    externalUserIds,
  });
  return res;
}

export async function getUnionidByExternalUserId(externalUserId) {
  const res = await useMember({
    type: "getUnionidByExternalUserID",
    externalUserId,
    corpId: localStorage.getItem("corpId"),
  });
  return res;
}

export async function getCustomerInfoById(userId, params) {
  const res = await useMember({
    type: "getMemberInfo",
    userId,
    params,
  });
  return res;
}

export async function addMember(params) {
  if (!params.addMethod) params.addMethod = "manual";
  const { corpInfo } = memberStore();
  const { giveCustomerCount, customerCount } = corpInfo.package;
  const res = await useMember({ type: "add", params, createTeamId: teamStore().currentTeamId, createTeamName: teamStore().currentTeamName, giveCustomerCount, customerCount });
  return res;
}

export async function batchImportCustomer(params) {
  const res = await useMember({ type: "batchImportCustomer", params, createTeamId: teamStore().currentTeamId, createTeamName: teamStore().currentTeamName });
  return res;
}
export async function deleteMember(id) {
  const res = await useMember({ type: "delete", id });
  return res;
}
export async function updateMember(id, params, operationType) {
  const res = await useMember({ type: "update", id, params, operationType, userId: localStorage.getItem("userId"), createTeamId: teamStore().currentTeamId, createTeamName: teamStore().currentTeamName });
  return res;
}

export async function getmemberCountForTeamId(time, params) {
  const res = await useMember({
    type: "getmemberCountForTeamId",
    time,
    params,
  });
  return res;
}

export async function getSelectedChatMemberUrl({ externalUserId }) {
  const res = await useMember({
    type: "getSelectedChatMember",
    externalUserId,
    userId: localStorage.getItem("userId"),
    corpId: localStorage.getItem("corpId"),
  });
  return res;
}

export async function getmemberAndserviceCountForUserId(time, params) {
  const res = await useMember({
    type: "getmemberAndserviceCountForUserId",
    time,
    params,
  });
  return res;
}

export async function getCustomerList(params, page = 1, pageSize = 10) {
  const res = await useMember({
    type: "getMemberForTeam",
    corpId: localStorage.getItem("corpId"),
    userId: localStorage.getItem("userId"),
    teamId: teamStore().currentTeamId,
    page,
    pageSize,
    params,
  });
  return res;
}

export async function getCustomerType() {
  const res = await useMember({
    type: "getCustomerType",
  });
  return res;
}

export async function getMemberBytags(tagIds) {
  const res = await useMember({
    type: "getMemberBytags",
    corpId: localStorage.getItem("corpId"),
    userId: localStorage.getItem("userId"),
    tagIds,
  });
  return res;
}

export async function createPannedEvent(params) {
  const res = await useMember({
    type: "createPannedEvent",
    params,
  });
  return res;
}

export async function updatePannedEvent(_id, params) {
  const res = await useMember({
    type: "updatePannedEvent",
    id: _id,
    params,
  });
  return res;
}

export async function getPannedEvent(params) {
  const res = await useMember({
    type: "getPannedEvent",
    teamId: teamStore().currentTeamId,
    params,
  });
  return res;
}

export async function removePannedEvent(_id) {
  const res = await useMember({
    type: "removePannedEvent",
    id: _id,
  });
  return res;
}

export async function getPannedEventById(_id) {
  const res = await useMember({
    type: "getPannedEventById",
    id: _id,
  });
  return res;
}

export async function getPannedEventResult(params) {
  const res = await useMember({
    type: "getPannedEventResult",
    params,
  });
  return res;
}

export async function getServiceAndmemberTotalByTeamId(dates) {
  const res = await useMember({
    type: "getServiceAndmemberTotalByTeamId",
    corpId: localStorage.getItem("corpId"),
    teamId: teamStore().currentTeamId,
    dates,
  });
  return res;
}

export async function getMemberTrendByTeamId(dates, userId = "", cardType) {
  const res = await useMember({
    type: "getMemberTrendByTeamId",
    corpId: localStorage.getItem("corpId"),
    teamId: teamStore().currentTeamId,
    dates,
    dataType: cardType,
    userId,
  });
  return res;
}

export async function getServiceTrendByTeamId(teamId, dates) {
  const res = await useMember({
    type: "getServiceTrendByTeamId",
    corpId: localStorage.getItem("corpId"),
    teamId,
    dates,
  });
  return res;
}

export async function getUsersMemberByTeamId(dates) {
  const { currentTeam, currentTeamId } = teamStore();
  const res = await useMember({
    type: "getUsersMemberByTeamId",
    corpId: localStorage.getItem("corpId"),
    memberList: currentTeam.memberList,
    teamId: currentTeamId,
    permanentCode: memberStore().corpInfo.permanent_code,
    dates,
  });
  return res;
}

export async function addMedicalRecord(data) {
  const res = await useMember({ type: "addMedicalRecord", ...data, createTeamId: teamStore().currentTeamId, createTeamName: teamStore().currentTeamName });
  return res;
}

export async function getCustomerMedicalRecord(data) {
  const res = await useMember({ type: "getCustomerMedicalRecord", ...data });
  return res;
}

export async function getMedicalRecordById(data) {
  const res = await useMember({ type: "getMedicalRecordById", ...data });
  return res;
}

export async function removeMedicalRecord(data) {
  const res = await useMember({ type: "removeMedicalRecord", ...data });
  return res;
}

export async function updateMedicalRecord(data) {
  const res = await useMember({ type: "updateMedicalRecord", ...data });
  return res;
}

export async function selectedChatMember(data) {
  const res = await useMember({ type: "selectedChatMember", ...data });
  return res;
}

export async function getArriveTime(corpId, memberId) {
  const res = await useMember({ type: "getArriveTime", corpId, memberId });
  return res;
}

export async function getRegistrationCountByTeam(timeType) {
  const res = await useMember({
    type: "getRegistrationCountByTeam",
    teamId: teamStore().currentTeamId,
    timeType,
  });
  return res;
}

export async function getRegistrationCountByUserId(timeType) {
  const res = await useMember({
    type: "getRegistrationCountByUserId",
    teamId: teamStore().currentTeamId,
    userId: localStorage.getItem("userId"),
    timeType,
  });
  return res;
}

export async function getCustomerStatistics() {
  const res = await useMember({
    type: "getCustomerStatistics",
    teamId: teamStore().currentTeamId,
    corpId: localStorage.getItem("corpId"),
  });
  return res;
}

export async function getCorpCustomer(params) {
  const { permanent_code } = memberStore().corpInfo;
  const res = await useMember({
    type: "getCorpCustomer",
    permanent_code,
    ...params,
  });
  return res;
}

export async function searchCorpCustomer(params) {
  const res = await useMember({
    type: "searchCorpCustomer",
    createTeamId: teamStore().currentTeamId,
    ...params,
  });
  return res;
}

export async function batchUpdateCustomer(params) {
  const res = await useMember({
    type: "batchUpdateCustomer",
    ...params,
  });
  return res;
}

export async function getReferredPeople(corpId, id, page, pageSize) {
  const res = await useMember({
    type: "getReferredPeople",
    corpId,
    id,
    page,
    pageSize,
  });
  return res;
}

export async function getCustomerInSidebar(params) {
  const res = await useMember({
    type: "getCustomerInSidebar",
    corpId: localStorage.getItem("corpId"),
    ...params,
  });
  return res;
}

export async function batchUpdateCustomerTeamIds({ customerIds, operationType, teamIds }) {
  const res = await useMember({
    type: "batchUpdateCustomerTeamIds",
    corpId: localStorage.getItem("corpId"),
    userId: localStorage.getItem("userId"),
    customerIds,
    operationType,
    teamIds,
  });
  return res;
}

export async function transferCustomers(data) {
  const res = await useMember({
    type: "transferCustomers",
    corpId: localStorage.getItem("corpId"),
    creatorUserId: localStorage.getItem("userId"),
    ...data,
  });
  return res;
}
export async function batchRemoveMember(customerIds) {
  const res = await useMember({
    type: "batchRemoveMember",
    corpId: localStorage.getItem("corpId"),
    customerIds,
  });
  return res;
}
export async function getGroupmsgCustomerCount({ sendRequirement }) {
  const res = await useMember({
    type: "getGroupmsgCustomerCount",
    corpId: localStorage.getItem("corpId"),
    sendRequirement,
  });
  return res;
}

export async function getGroupmsgCustomerList({ sendRequirement, page, pageSize, name }) {
  const res = await useMember({
    type: "getGroupmsgCustomer",
    corpId: localStorage.getItem("corpId"),
    sendRequirement,
    page,
    pageSize,
    name,
  });
  return res;
}

export async function mergeMedicalHisRecord(params) {
  const res = await useMember({
    type: "mergeMedicalHisRecord",
    corpId: localStorage.getItem("corpId"),
    ...params,
  });
  return res;
}

export async function getTeamsServiceAndMemberCount({ dates, teamList }) {
  const res = await useMember({
    type: "getTeamsServiceAndMemberCount",
    corpId: localStorage.getItem("corpId"),
    dates,
    teamList,
  });
  return res;
}

export async function getStaffServiceAndMemberCount({ dates }) {
  const res = await useMember({
    type: "getStaffServiceAndMemberCount",
    corpId: localStorage.getItem("corpId"),
    dates,
  });
  return res;
}

export async function getCustomerSourceCountSort(sources) {
  const res = await useMember({
    type: "getCustomerSourceCountSort",
    corpId: localStorage.getItem("corpId"),
    sources,
  });
  return res;
}

export async function getCustomerGroupCountSort() {
  const res = await useMember({
    type: "getCustomerGroupCountSort",
    corpId: localStorage.getItem("corpId"),
  });
  return res;
}

export async function getCusomterAndServiceCount() {
  const res = await useMember({
    type: "getCusomterAndServiceCount",
    corpId: localStorage.getItem("corpId"),
  });
  return res;
}

export async function getStaffRate(data = {}) {
  const res = await useMember({
    type: "getStaffRate",
    corpId: localStorage.getItem("corpId"),
    ...data,
  });
  return res;
}
export async function getScoreRateTrend({ dates }) {
  const res = await useMember({
    type: "getScoreRateTrend",
    corpId: localStorage.getItem("corpId"),
    dates,
  });
  return res;
}

export async function getHealthIndicators({ memberId }) {
  const res = await useMember({
    type: "getHealthIndicators",
    corpId: localStorage.getItem("corpId"),
    memberId,
  });
  return res;
}

export async function getHealthIndicatorsTemplate() {
  const res = await useMember({
    type: "getHealthIndicatorsTemplate",
    corpId: localStorage.getItem("corpId"),
  });
  return res;
}

export async function getMedicalFiledsTrend({ memberId, field, dates }) {
  const res = await useMember({
    type: "getMedicalFiledsTrend",
    corpId: localStorage.getItem("corpId"),
    memberId,
    field,
    dates,
  });
  return res;
}

export async function createSopTask(data) {
  const res = await useMember({
    type: "createSopTask",
    ...data,
  });
  return res;
}

export async function updateSopTask(data) {
  const res = await useMember({
    type: "updateSopTask",
    ...data,
  });
  return res;
}

export async function getSopFilterRule() {
  const res = await useMember({
    type: "getSopFilterRule",
    corpId: localStorage.getItem("corpId"),
  });
  return res;
}

export async function getSopTaskList(data) {
  const res = await useMember({
    type: "getSopTaskList",
    ...data,
    corpId: localStorage.getItem("corpId"),
  });
  return res;
}

export async function getSopTask(sopTaskId) {
  const res = await useMember({
    type: "getSopTask",
    sopTaskId,
    corpId: localStorage.getItem("corpId"),
  });
  return res;
}

export async function removeSopTask(sopTaskId) {
  const res = await useMember({
    sopTaskId,
    type: "removeSopTask",
    corpId: localStorage.getItem("corpId"),
  });
  return res;
}

export async function addSopCate(data) {
  const res = await useMember({ type: "addSopCate", ...data });
  return res;
}

export async function updateSopCate(data) {
  const res = await useMember({ type: "updateSopCate", ...data });
  return res;
}

export async function deleteSopCate(data) {
  const res = await useMember({ type: "deleteSopCate", ...data });
  return res;
}

export async function getSopCateList(data) {
  const res = await useMember({ type: "getSopCateList", ...data });
  return res;
}

export async function sortSopCate(data) {
  const res = await useMember({ type: "sortSopCate", ...data });
  return res;
}

export async function updateSopTaskField(data) {
  const res = await useMember({ type: "updateSopTaskField", ...data });
  return res;
}

export async function getSopTaskResultList(data) {
  const res = await useMember({ type: "getSopTaskResultList", corpId: localStorage.getItem("corpId"), ...data });
  return res;
}

export async function getSopTaskResultForTag(data) {
  const res = await useMember({ type: "getSopTaskResultForTag", corpId: localStorage.getItem("corpId"), ...data });
  return res;
}

export async function getCustomerSopTask(data) {
  const res = await useMember({ type: "getCustomerSopTask", corpId: localStorage.getItem("corpId"), ...data });
  return res;
}

export async function updateGroupMsgTaskResult(data) {
  const res = await useMember({ type: "updateGroupMsgTaskResult", corpId: localStorage.getItem("corpId"), ...data });
  return res;
}

export async function getCustomerNamesById(data) {
  const res = await useMember({ type: "getCustomerNamesById", corpId: localStorage.getItem("corpId"), ...data });
  return res;
}

export async function getCustomersCount(data) {
  const res = await useMember({ type: "getCustomersCount", ...data });
  return res;
}

export async function getInHospitalCustomersCount(data) {
  const res = await useMember({ type: "getInHospitalCustomersCount", ...data });
  return res;
}

export async function addProjectPackageCate(data) {
  const res = await useMember({ type: "addProjectPackageCate", ...data });
  return res;
}

export async function updateProjectPackageCate(data) {
  const res = await useMember({ type: "updateProjectPackageCate", ...data });
  return res;
}

export async function deleteProjectPackageCate(data) {
  const res = await useMember({ type: "deleteProjectPackageCate", ...data });
  return res;
}

export async function getProjectPackageCateList(data) {
  const res = await useMember({ type: "getProjectPackageCateList", ...data });
  return res;
}

export async function sortProjectPackageCate(data) {
  const res = await useMember({ type: "sortProjectPackageCate", ...data });
  return res;
}

export async function addProjectPackage(data) {
  const res = await useMember({ type: "addProjectPackage", ...data });
  return res;
}
export async function getProjectPackageList(data) {
  const res = await useMember({ type: "getProjectPackageList", ...data });
  return res;
}

export async function removeProjectPackage(data) {
  const res = await useMember({ type: "removeProjectPackage", ...data });
  return res;
}

export async function updateProjectPackage(data) {
  const res = await useMember({ type: "updateProjectPackage", ...data });
  return res;
}

export async function setProjectPackageEnable(data) {
  const res = await useMember({ type: "setProjectPackageEnable", ...data });
  return res;
}

export async function addConsumeRecord(data) {
  const res = await useMember({ type: "addConsumeRecord", ...data });
  return res;
}

export async function pushCustomerTeamId(data) {
  const res = await useMember({ type: "pushCustomerTeamId", ...data });
  return res;
}

export async function updateCustomerReportPeople(data) {
  const res = await useMember({ type: "updateCustomerReportPeople", ...data });
  return res;
}

export async function getCustomerReportLog(data) {
  const res = await useMember({ type: "getCustomerReportLog", ...data });
  return res;
}

export async function getGroupTaskResultList(data) {
  const res = await useMember({ type: "getGroupTaskResultList", ...data });
  return res;
}

export async function getGroupByIds(data) {
  const res = await useMember({ type: "getGroupByIds", ...data });
  return res;
}

export async function getGroupTaskResult(data) {
  const res = await useMember({ type: "getGroupTaskResult", ...data });
  return res;
}

export async function updateCustomerIntroducer(data) {
  const res = await useMember({ type: "updateCustomerIntroducer", ...data });
  return res;
}

export async function updateCustomerCounselor(data) {
  const res = await useMember({ type: "updateCustomerCounselor", ...data });
  return res;
}