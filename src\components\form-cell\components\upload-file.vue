<template>
  <div class="mb-10px">
    <div font-14 color-666 class="mb-10px w-full" v-if="!isRead"><span color-primary>支持上传5M以下图片、pdf文件格式</span></div>
    <div flex flex-wrap>
      <div relative v-for="(item, idx) in value" :key="idx + '_file'" class="imageItem mr-15px mb-15px">
        <el-image v-if="!item.name.includes('.pdf')" class="w-100px h-100px" :src="getImageUrl(item)" :initial-index="idx" v-loading="item.loading" fit="cover" :previewSrcList="previewSrcList" />
        <el-image v-else class="w-100px h-100px pointer" :src="getImageUrl(item)" :initial-index="idx" v-loading="item.loading" fit="cover" @click="openImage(item)" />
        <div class="name">{{ item.name }}</div>
        <img class="removeImage" @click="removeImage(idx)" v-if="!isRead" src="@/assets/shanchu.svg" />
      </div>
      <el-button v-if="!value.length < item.limit && !isRead" class="w-100px h-100px mr-15px mb-5px text-28px" type="primary" :icon="CameraFilled" plain @click="chooseImage()"></el-button>
    </div>
    <input v-if="!isRead" class="w-0 h-0 invisible fixed" type="file" :accept="item.accept" ref="uploadRef" @change="handleSelect($event)" />
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getRandomStr, file2Base64 } from "@/utils";
import { CameraFilled } from "@element-plus/icons-vue";
import pdfSvg from "@/assets/pdf.svg";
const emit = defineEmits(["change"]);
const props = defineProps({
  value: {
    type: Object,
    default: () => [],
  },
  isRead: {
    type: Boolean,
    default: false,
  },
  item: {
    type: Object,
    default: () => ({}),
  },
});
function getImageUrl(item) {
  if (item.name && item.name.includes(".pdf")) {
    return pdfSvg;
  } else {
    return item.url || item.base64;
  }
}
// props.value 字符串转数组
const uploadRef = ref();
const previewSrcList = computed(() => {
  return Array.isArray(props.value) && props.value.map((item) => item.url || item.base64);
});
function openImage(item) {
  if (item.url) window.open(item.url);
}
async function chooseImage() {
  uploadRef.value.click();
}

// 图片压缩函数
async function compressImage(file, maxSizeMB = 1) {
  return new Promise((resolve, reject) => {
    if (!file.type.includes("image/")) {
      resolve(file);
      return;
    }

    const maxSize = maxSizeMB * 1024 * 1024; // 转换为字节
    if (file.size <= maxSize) {
      resolve(file);
      return;
    }

    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = (e) => {
      try {
        const img = new Image();
        img.src = e.target.result;
        img.onload = () => {
          try {
            const canvas = document.createElement("canvas");
            const ctx = canvas.getContext("2d");

            // 保持原始宽高比
            let width = img.width;
            let height = img.height;

            // 限制最大尺寸
            const MAX_WIDTH = 1920;
            const MAX_HEIGHT = 1920;
            if (width > MAX_WIDTH) {
              height = (MAX_WIDTH / width) * height;
              width = MAX_WIDTH;
            }
            if (height > MAX_HEIGHT) {
              width = (MAX_HEIGHT / height) * width;
              height = MAX_HEIGHT;
            }

            canvas.width = width;
            canvas.height = height;

            // 绘制图片到canvas
            ctx.drawImage(img, 0, 0, width, height);

            // 从0.9开始逐步降低图片质量，直到满足大小要求或达到最小质量0.3
            let quality = 0.9;
            const minQuality = 0.3;

            const compressLoop = () => {
              try {
                const dataURL = canvas.toDataURL(file.type, quality);
                const blobBin = atob(dataURL.split(",")[1]);
                const array = [];
                for (let i = 0; i < blobBin.length; i++) {
                  array.push(blobBin.charCodeAt(i));
                }
                const blob = new Blob([new Uint8Array(array)], { type: file.type });

                if (blob.size <= maxSize || quality <= minQuality) {
                  // 创建新的File对象
                  const compressedFile = new File([blob], file.name, {
                    type: file.type,
                    lastModified: new Date().getTime(),
                  });
                  resolve(compressedFile);
                } else {
                  quality -= 0.1;
                  compressLoop();
                }
              } catch (error) {
                console.error("压缩循环出错:", error);
                // 压缩失败时返回原始文件
                resolve(file);
              }
            };

            compressLoop();
          } catch (error) {
            console.error("canvas处理出错:", error);
            resolve(file); // 压缩失败时返回原始文件
          }
        };
        img.onerror = (error) => {
          console.error("图片加载出错:", error);
          resolve(file); // 图片加载失败时返回原始文件
          ElMessage.warning("图片处理遇到问题，将使用原始图片");
        };
      } catch (error) {
        console.error("图片处理出错:", error);
        resolve(file); // 出错时返回原始文件
      }
    };
    reader.onerror = (error) => {
      console.error("文件读取失败:", error);
      resolve(file); // 读取失败时返回原始文件
      ElMessage.warning("文件读取遇到问题，将使用原始文件");
    };
  });
}

async function handleSelect(e) {
  const [file] = e.target.files;
  e.target.value = "";
  if (!file) return;

  // 验证文件类型
  const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'application/pdf'];
  if (!validTypes.includes(file.type)) {
    ElMessage.error("不支持的文件类型，请上传图片或PDF文件");
    return;
  }

  if (file.size > 5242880) {
    ElMessage.info("文件大小不能超过5MB");
    return;
  }
  
  try {
    const key = getRandomStr();
    // 添加加载状态
    let list = [...(props.value || [])]; // 创建副本避免直接修改props
    const newItem = {
      name: file.name,
      key,
      size: file.size,
      type: file.type,
      loading: true,
    };
    list.push(newItem);
    emit("change", {
      title: props.item.title,
      value: list,
    });

    // 处理图片压缩
    let processedFile = file;
    try {
      if (file.type.includes("image/")) {
        processedFile = await compressImage(file, 1); // 压缩到1MB以下
      }
    } catch (compressError) {
      console.error("图片压缩失败:", compressError);
      ElMessage.warning("图片压缩失败，将使用原始图片");
      processedFile = file; // 压缩失败时使用原始文件
    }

    // 转换为base64
    let base64 = '';
    try {
      base64 = await file2Base64(processedFile);
    } catch (base64Error) {
      console.error("文件转base64失败:", base64Error);
      ElMessage.error("文件处理失败");
      
      // 从列表中删除失败的项
      list = [...(props.value || [])];
      const index = list.findIndex(item => item.key === key);
      if (index !== -1) {
        list.splice(index, 1);
        emit("change", {
          title: props.item.title,
          value: list,
        });
      }
      return;
    }

    // 更新项目信息
    list = [...(props.value || [])]; // 重新获取最新的列表
    const index = list.findIndex((item) => item.key === key);
    if (index !== -1) {
      list[index] = {
        ...list[index],
        base64,
        file: processedFile,
        size: processedFile.size,
        loading: false,
      };
      emit("change", {
        title: props.item.title,
        value: list,
      });
    }
  } catch (error) {
    console.error("文件处理错误:", error);
    ElMessage.error("文件处理失败，请重试");
    // 从列表中删除失败的项
    let list = [...(props.value || [])];
    const index = list.findIndex((item) => !item.base64 && item.loading);
    if (index !== -1) {
      list.splice(index, 1);
      emit("change", {
        title: props.item.title,
        value: list,
      });
    }
  }
}

async function removeImage(idx) {
  await ElMessageBox.confirm("确定删除该图片吗？", "提示", { type: "warning" });
  let list = props.value || [];
  list.splice(idx, 1);
  emit("change", {
    title: props.item.title,
    value: list,
  });
}
</script>
<style lang="scss">
.name {
  text-align: center;
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100px;
  opacity: 1;
  white-space: nowrap;
  /* 防止文本换行 */
  overflow: hidden;
  /* 隐藏超出的文本 */
  text-overflow: ellipsis;
  margin-top: -5px;
  /* 将超出的文本显示为省略号 */
}

.removeImage {
  position: absolute;
  top: -10px;
  right: -10px;
  padding: 4px;
  text-align: center;
  font-size: 12px;
  cursor: pointer;
  width: 30px;
  height: 30px;
}

.el-date-editor.el-input.w-half {
  width: 50%;
}

.el-date-editor.el-input.w-full {
  width: 100%;
}
</style>