[p-15] {
  padding: 15px;
}

[px-15] {
  padding-left: 15px;
  padding-right: 15px;
}

[py-10] {
  padding-top: 10px;
  padding-bottom: 10px;
}

[py-15] {
  padding-top: 15px;
  padding-bottom: 15px;
}

[pt-10] {
  padding-top: 10px;
}

[pb-10] {
  padding-bottom: 10px;
}

[pb-15] {
  padding-bottom: 15px;
}

[mx-15] {
  margin-left: 15px;
  margin-right: 15px;
}

[mt-10] {
  margin-top: 10px;
}

[mt-15] {
  margin-top: 15px;
}

[mr-5] {
  margin-right: 5px;
}

[mr-10] {
  margin-right: 10px;
}

[mr-15] {
  margin-right: 15px;
}

[mr-20] {
  margin-right: 20px;
}

[mb-10] {
  margin-bottom: 10px;
}

[mb-15] {
  margin-bottom: 15px;
}

[ml-10] {
  margin-left: 10px;
}

[bg-fff] {
  background: white;
}

[text-center] {
  text-align: center;
}

[text-right] {
  text-align: right;
}

[font-12] {
  font-size: 12px;
}

[font-14] {
  font-size: 14px;
}

[font-16] {
  font-size: 16px;
}

[font-18] {
  font-size: 18px;
}

[font-30] {
  font-size: 30px;
}

[font-semibold] {
  font-weight: 600;
}

[text-ellipsis] {
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

[flex] {
  display: flex;
}

[flex-col] {
  flex-direction: column;
}

[items-center] {
  align-items: center;
}

[justify-around] {
  justify-content: space-around;
}

[justify-between] {
  justify-content: space-between;
}

[justify-center] {
  justify-content: center;
}

[justify-end] {
  justify-content: end;
}

[flex-wrap] {
  flex-wrap: wrap;
}

[flex-grow] {
  flex-grow: 1;
}

[align-center] {
  align-items: center;
}

[flex-shrink-0] {
  flex-shrink: 0;
}

[common-shadow] {
  box-shadow: 0 2px 4px 0 rgba(54, 58, 80, 0.32);
}

[common-shadow--r] {
  box-shadow: 0 -2px 4px 0 rgba(54, 58, 80, 0.2);
}

[title-bar] {
  position: relative;
  font-size: 18px;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.9);
}

[w-0] {
  width: 0;
}

[w-full] {
  width: 100%;
}

[h-full] {
  height: 100%;
}

[rounded-4] {
  border-radius: 4px;
}

[rounded-8] {
  border-radius: 8px;
}


[overflow-hidden] {
  overflow: hidden;
}

[color-fff] {
  color: #fff;
}

[color-666] {
  color: #666;
}

[color-danger] {
  color: #F56C6C;
}

[color-normal] {
  color: rgba(0, 0, 0, .9);
}

[color-primary] {
  color: var(--el-color-primary);
}

[pointer] {
  cursor: pointer;
}

[relative] {
  position: relative;
}

[absolute] {
  position: absolute;
}