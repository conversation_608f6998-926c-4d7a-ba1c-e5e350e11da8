export const statusMap = {
  executing: '进行中',
  unexecuted: '未开始',
  closed: '已结束'
}
export const statusList = Object.keys(statusMap).map((value) => ({ value, label: statusMap[value] }));

// 执行方式  groupmsg-群发  todo-待办  tag-标签

export const executeMethodList = [
  { label: '群发任务', value: 'groupmsg', enable: true, des: '群发任务(批量发送)' },
  { label: '待办事项', value: 'todo', enable: true, des: '待办事项 (生成待办单，1对1跟进)' },
  { label: '标签', value: 'tag', enable: true, des: '自动打标签' },
  { label: '自动入组', value: 'enterGroup', enable: false, des: '自动入组' },
  { label: '短信发送', value: 'sms', enable: false, des: '短信发送' }
]
export const executeMethodMap = executeMethodList.reduce((prev, curr) => {
  prev[curr.value] = curr.label
  return prev
}, {})


export const executeTypeList = [
  { label: '员工任务', value: 'manual' },
  { label: '自动化任务', value: 'auto' },
]
export const executeTypeMap = executeMethodList.reduce((prev, curr) => {
  prev[curr.value] = curr.label
  return prev
}, {})