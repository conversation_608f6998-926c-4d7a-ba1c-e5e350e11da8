import { createRouter, createWebHashHistory } from "vue-router";
import useGuard from "@/utils/route-guard";
// vue项目自带路由
let routes = [
  {
    path: "/",
    name: "Home",
    meta: {
      keepAlive: true, //设置页面是否需要使用缓存
    },
    component: () => import("@/views/home/<USER>"),
    children: [
      {
        name: "MEMBER",
        path: "/MEMBER",
        component: () => import("../views/member/index.vue"),
        meta: {
          keepAlive: false,
          title: "团队患者",
        },
      },
      {
        name: "DETAIL",
        path: "/DETAIL/:id/:routerName?",
        component: () => import("../views/member/detail"),
        meta: {
          keepAlive: false,
          title: "客户详情",
          multTag: true, // 允许多个菜单tag存在
        },
      },
      {
        name: "ADDCUSTOMER",
        path: "/ADDCUSTOMERL/:routerName?/:idCard?",
        component: () => import("../views/member/customer/add-customer"),
        meta: {
          keepAlive: false,
          title: "新增客户",
        },
      },
      {
        name: "CORPCUSTOMER",
        path: "/CORPCUSTOMER",
        component: () => import("../views/member/corp-customer/index.vue"),
        meta: {
          keepAlive: false,
          title: "机构患者",
          multTag: true, // 允许多个菜单tag存在
        },
      },

      {
        name: "EDITHEALTH",
        path: "/EDITHEALTH/:id",
        component: () => import("../views/member/edit-health/edit-health"),
        meta: {
          keepAlive: false,
          title: "健康指标",
        },
      },
      {
        name: "MEDICALRECORD",
        path: "/MEDICALRECORD/:type/:memberId/:memberName/:id?",
        component: () => import("../views/member/medical-record"),
        meta: {
          keepAlive: true,
          title: "病历档案",
        },
      },
      {
        name: "SCHEDULE",
        path: "/SCHEDULE",
        component: () => import("../views/schedule/index.vue"),
        meta: {
          keepAlive: false,
          title: "工作记录",
        },
      },
      {
        name: "SCHEDULEDETAIL",
        path: "/SCHEDULEDETAIL/:id",
        component: () => import("../views/schedule/detail.vue"),
        meta: {
          keepAlive: false,
          title: "待办详情",
          multTag: true,
        },
      },
      {
        name: "TEAM",
        path: "/TEAM",
        component: () => import("../views/team/list.vue"),
        meta: {
          keepAlive: false,
          title: "团队管理",
        },
      },
      {
        name: "MYTEAM",
        path: "/MYTEAM",
        component: () => import("../views/myTeam"),
        meta: {
          keepAlive: false,
          title: "我的团队",
        },
      },
      {
        name: "TEAMDETAIL",
        path: "/TEAMDETAIL/:teamId?",
        component: () => import("../views/team/detail.vue"),
        meta: {
          keepAlive: false,
          title: "团队信息",
        },
      },
      {
        name: "AUTHORITY",
        path: "/AUTHORITY",
        component: () => import("../views/system/authority.vue"),
        meta: {
          keepAlive: false,
          title: "权限管理",
        },
      },
      {
        name: "MANAGEMENTPLAN",
        path: "/managementPlan",
        component: () => import("../views/managementPlan/team-manage-plan"),
        meta: {
          keepAlive: false,
          title: "回访计划",
        },
      },
      {
        name: "CORPVISITPLAN",
        path: "/CORPVISITPLAN",
        component: () => import("../views/managementPlan/corp-manage-plan"),
        meta: {
          keepAlive: false,
          title: "机构回访计划",
        },
      },
      {
        name: "MANAGEMENTPLANDETAIL",
        path: "/managementPlanDetail/:id?/:type?",
        component: () => import("../views/managementPlan/detail.vue"),
        meta: {
          keepAlive: false,
          title: "新增计划",
        },
      },
      {
        name: "MEMBERMANAGE",
        path: "/MEMBERMANAGE",
        component: () => import("../views/system/staff"),
        meta: {
          keepAlive: false,
          title: "成员列表",
        },
      },
      {
        name: "STAFFINFO",
        path: "/STAFFINFO",
        component: () => import("../views/staff-info/index.vue"),
        meta: {
          keepAlive: false,
          title: "员工信息",
        },
      },
      {
        name: "STAFFACCOUNT",
        path: "/STAFFACCOUNT",
        component: () => import("../views/system/staff/staff-account.vue"),
        meta: {
          keepAlive: false,
          title: "员工账号",
        },
      },
      {
        name: "WORK",
        path: "/WORK",
        component: () => import("../views/workSpace/index.vue"),
        meta: {
          keepAlive: false,
          title: "工作台",
        },
      },
      {
        name: "WELCOMELIST",
        path: "/WELCOMELIST",
        component: () => import("../views/welcome/list.vue"),
        meta: {
          keepAlive: true,
          title: "欢迎语",
        },
      },
      {
        name: "WELCOMEDETAIL",
        path: "/WELCOMEDETAIL/:id?",
        component: () => import("../views/welcome/detail.vue"),
        meta: {
          keepAlive: true,
          title: "配置欢迎语",
        },
      },
      {
        name: "MEMBERMANAGEDETIAL",
        path: "/MEMBERMANAGEDETIAL",
        component: () => import("../views/system/memberManageDetial.vue"),
        meta: {
          keepAlive: false,
          title: "成员详情",
        },
      },
      {
        name: "DISAGNOSIS",
        path: "/DISAGNOSIS",
        component: () => import("../views/system/diagnosis.vue"),
        meta: {
          keepAlive: true,
          title: "常用诊断",
        },
      },
      {
        name: "CUSTOMERTAG",
        path: "/CUSTOMERTAG",
        component: () => import("../views/system/customerTag.vue"),
        meta: {
          keepAlive: true,
          title: "客户标签",
        },
      },
      {
        name: "SERVICEITEM",
        path: "/SERVICEITEM",
        component: () => import("../views/project-manage/project-manage"),
        meta: {
          keepAlive: false,
          title: "服务项目",
        },
      },
      {
        name: "SOURCECAUSATION",
        path: "/SOURCECAUSATION",
        component: () => import("../views/system/sourceCausation.vue"),
        meta: {
          keepAlive: false,
          title: "来源配置",
        },
      },
      {
        name: "COMMONWORDS",
        path: "/COMMONWORDS",
        component: () => import("../views/system/commonWords"),
        meta: {
          keepAlive: false,
          title: "常用语",
        },
      },
      {
        name: "ARTICLE",
        path: "/ARTICLE",
        component: () => import("../views/system/article"),
        meta: {
          keepAlive: false,
          title: "宣教文章",
        },
      },
      {
        name: "ARTICLEDETAIL",
        path: "/ARTICLEDETAIL/:id?",
        component: () => import("../views/system/article/detail.vue"),
        meta: {
          keepAlive: true,
          title: "文章",
        },
      },
      {
        name: "ARTICLESTATS",
        path: "/ARTICLESTATS/:id?",
        component: () => import("../views/system/article/stats.vue"),
        meta: { keepAlive: true, title: "文章详情" },
      },
      {
        name: "SURVERY",
        path: "/SURVERY",
        component: () => import("../views/system/survery/list.vue"),
        meta: {
          keepAlive: false,
          title: "问卷管理",
        },
      },
      {
        name: "SURVERYDETAIL",
        path: "/SURVERYDETAIL/:id?",
        component: () => import("../views/system/survery/detail.vue"),
        meta: {
          keepAlive: true,
          title: "问卷",
        },
      },
      {
        name: "SURVERYANSWER",
        path: "/SURVERYANSWER/:id",
        component: () => import("../views/system/survery/answer.vue"),
        meta: {
          keepAlive: true,
          title: "发送记录",
        },
      },
      {
        name: "PANNEDEVENT",
        path: "/PANNEDEVENT",
        component: () => import("../views/pannedEvent/index.vue"),
        meta: {
          keepAlive: false,
          title: "计划事项",
        },
      },
      {
        name: "PANNEDEVENTDETIAL",
        path: "/PANNEDEVENTDETIAL/:id?",
        component: () => import("../views/pannedEvent/detial.vue"),
        meta: {
          keepAlive: false,
          title: "新增计划事项",
        },
      },
      {
        name: "PANNEDEVENTINFORMATION",
        path: "/PANNEDEVENTINFORMATION/:id",
        component: () => import("../views/pannedEvent/information.vue"),
        meta: {
          keepAlive: false,
          title: "计划事项详情",
        },
      },
      {
        name: "DAYWORK",
        path: "/DAYWORK",
        component: () => import("../views/dayWork"),
        meta: {
          keepAlive: true,
          title: "日程列表",
        },
      },
      {
        name: "ARCHIVETEMPLATE",
        path: "/ARCHIVETEMPLATE",
        component: () => import("../views/system/archive-template/index"),
        meta: {
          keepAlive: true,
          title: "档案模版",
        },
      },
      {
        name: "MEMBERGROUP",
        path: "/MEMBERGROUP",
        component: () => import("../views/memberGroup"),
        meta: {
          keepAlive: false,
          title: "分组管理",
        },
      },
      {
        name: "CORPMEMBERGROUP",
        path: "/CORPMEMBERGROUP",
        component: () => import("../views/memberGroup/corp-group.vue"),
        meta: {
          keepAlive: false,
          title: "机构分组",
        },
      },
      {
        name: "CORPINFO",
        path: "/CORPINFO",
        component: () => import("../views/system/corpInfo"),
        meta: {
          keepAlive: false,
          title: "机构信息",
        },
      },
      {
        name: "PACKAGELIST",
        path: "/PACKAGELIST",
        component: () => import("../views/system/corpInfo/packageList"),
        meta: {
          keepAlive: false,
          title: "套餐信息",
        },
      },
      {
        name: "DOCMANAGE",
        path: "/DOCMANAGE",
        component: () => import("../views/system/doc-manage/index"),
        meta: {
          keepAlive: true,
          title: "档案规范管理",
        },
      },
      {
        name: "GROUPTASK",
        path: "/GROUPTASK",
        component: () => import("../views/massMessageing/group-task/group-task"),
        meta: {
          keepAlive: false,
          title: "群发任务",
        },
      },
      {
        name: "SENDMESSAGE",
        path: "/SENDMESSAGE/:sendTaskType",
        component: () => import("../views/massMessageing/group-task/send-message/send-message"),
        meta: {
          keepAlive: false,
          title: "群发消息",
        },
      },
      {
        name: "GROUPTASKDETAIL",
        path: "/GROUPTASKDETAIL/:id/:sendTaskType",
        component: () => import("../views/massMessageing/group-task/group-task-detail/group-task-detail"),
        meta: {
          keepAlive: false,
          title: "群发任务详情",
        },
      },
      {
        name: "TEAMGROUPTASK",
        path: "/TEAMGROUPTASK",
        component: () => import("../views/massMessageing/team-group-send/team-group-send"),
        meta: {
          keepAlive: false,
          title: "团队群发",
        },
      },
      {
        name: "TEAMGROUPTASKDETAIL",
        path: "/TEAMGROUPTASKDETAIL/:id",
        component: () => import("../views/massMessageing/team-group-send/team-group-task-detail/team-group-task-detail"),
        meta: {
          keepAlive: false,
          title: "群发任务详情",
        },
      },
      {
        name: "CORPGROUPTASK",
        path: "/CORPGROUPTASK",
        component: () => import("../views/massMessageing/corp-group-send/corp-group-send"),
        meta: {
          keepAlive: false,
          title: "机构群发",
        },
      },
      {
        name: "MARKETCQRCODE",
        path: "/MARKETCQRCODE",
        component: () => import("../views/marketCenter/memberQrcode/index"),
        meta: {
          keepAlive: false,
          title: "获客拉新",
        },
      },
      {
        name: "PERSONALMARKETCQRCODE",
        path: "/PERSONALMARKETCQRCODE",
        component: () => import("../views/marketCenter/memberQrcode/personal-qrcode"),
        meta: {
          keepAlive: false,
          title: "员工个人码",
        },
      },
      {
        name: "MARKETCQRCODEDETAIL",
        path: "/MARKETCQRCODEDETAIL/:id",
        component: () => import("../views/marketCenter/memberQrcode/statistics/index"),
        meta: {
          keepAlive: false,
          title: "数据统计",
        },
      },
      {
        name: "CHATHISTORY",
        path: "/CHATHISTORY",
        component: () => import("../views/chat-history/chat-history.vue"),
        meta: {
          keepAlive: false,
          title: "员工会话",
        },
      },
      {
        name: "RATELIST",
        path: "/RATELIST",
        component: () => import("../views/system/rate-manage/rate-list"),
        meta: {
          keepAlive: false,
          title: "评价管理",
        },
      },
      {
        name: "BIGSCREEN",
        path: "/BIGSCREEN",
        component: () => import("../views/big-screen/big-screen.vue"),
        meta: {
          keepAlive: false,
          title: "平台数据",
        },
      },
      {
        name: "SOPANALYSIS",
        path: "/SOPANALYSIS/:id/:executeMethod",
        component: () => import("../views/sop/sop-analysis"),
        meta: {
          keepAlive: false,
          title: "使用分析",
        },
      },
      {
        name: "SOP",
        path: "/SOP/:id?",
        component: () => import("../views/sop/sop"),
        meta: {
          keepAlive: false,
          title: "SOP",
        },
      },
      {
        name: "SOPLOG",
        path: "/SOPLOG/:sopTaskId?/:userId?/:sopName?",
        component: () => import("../views/sop/sop-log"),
        meta: {
          keepAlive: false,
          title: "使用日志",
        },
      },
      {
        name: "SOPLIST",
        path: "/SOPLIST",
        component: () => import("../views/sop/sop-list"),
        meta: {
          keepAlive: false,
          title: "SOP列表",
        },
      },
      {
        name: "CUSTOMERCONTACT",
        path: "/CUSTOMERCONTACT/:time?",
        component: () => import("../views/member/customer-contact"),
        meta: {
          keepAlive: false,
          title: "微信好友",
        },
      },
      {
        name: "DEPTMANAGE",
        path: "/DEPTMANAGE",
        component: () => import("../views/dept-manage/dept-manage"),
        meta: {
          keepAlive: false,
          title: "科室管理",
        },
      },
      {
        name: "INTERNETDEPTMANAGE",
        path: "/INTERNETDEPTMANAGE",
        component: () => import("../views/internet-dept-manage/internet-dept-manage"),
        meta: {
          keepAlive: false,
          title: "互联网科室管理",
        },
      },
      {
        name: "ESTOREREPORT",
        path: "/ESTOREREPORT",
        component: () => import("../views/e-store-report/e-store-report"),
        meta: {
          keepAlive: false,
          title: "网店报备",
        },
      },
      {
        name: "NETWORKCONSULTATIONRECORDS",
        path: "/NETWORKCONSULTATIONRECORDS",
        component: () => import("../views/e-store-report/network-consultation-records"),
        meta: {
          keepAlive: false,
          title: "网络咨询记录",
        },
      },
      {
        name: "STOREAPPOINTMENT",
        path: "/STOREAPPOINTMENT",
        component: () => import("../views/e-store-report/appointment-record"),
        meta: {
          keepAlive: false,
          title: "预约记录",
        },
      },
      {
        name: "CONSULTRECEPTION",
        path: "/CONSULTRECEPTION",
        component: () => import("../views/consult-reception/consult-reception"),
        meta: {
          keepAlive: false,
          title: "咨询接待",
        },
      },
      {
        name: "CONSULTRECEPTIONRECORD",
        path: "/CONSULTRECEPTIONRECORD",
        component: () => import("../views/consult-reception/consult-reception-record"),
        meta: {
          keepAlive: false,
          title: "现场咨询记录",
        },
      },
      {
        name: "CONSULTAPPOINT",
        path: "/CONSULTAPPOINT",
        component: () => import("../views/consult-appoint/consult-appoint"),
        meta: {
          keepAlive: false,
          title: "咨询分诊",
        },
      },
      {
        name: "SERVICEPOINTS",
        path: "/SERVICEPOINTS",
        component: () => import("../views/service-points/points-manage"),
        meta: {
          keepAlive: false,
          title: "积分管理",
        },
      },
      {
        name: "SERVICEPOINTSLIST",
        path: "/SERVICEPOINTSLIST/:dates?",
        component: () => import("../views/service-points/user-point-list"),
        meta: {
          keepAlive: false,
          title: "积分发放明细",
        },
      },
      {
        name: "CORPSERVICEPOINTSLIST",
        path: "/CORPSERVICEPOINTSLIST",
        component: () => import("../views/service-points/corp-point-list"),
        meta: {
          keepAlive: false,
          title: "积分发放明细",
        },
      },
      {
        name: "PROJECTPACKAGE",
        path: "/PROJECTPACKAGE",
        component: () => import("../views/project-package/project-package"),
        meta: {
          keepAlive: false,
          title: "套餐管理",
        },
      },
      {
        name: "TREATEMENT",
        path: "/TREATEMENT",
        component: () => import("../views/treatment/index"),
        meta: {
          keepAlive: false,
          title: "治疗划扣",
        },
      },
      {
        name: "CURE",
        path: "/CURE",
        component: () => import("../views/treatment/cure.vue"),
        meta: {
          keepAlive: false,
          title: "治疗记录",
        },
      },
      {
        name: "FRIENDSTATS",
        path: "/FRIENDSTATS",
        component: () => import("../views/big-screen/wx-friend-screen.vue"),
        meta: {
          keepAlive: false,
          title: "好友统计",
        },
      },
      {
        name: "CHATSTSTS",
        path: "/CHATSTSTS",
        component: () => import("../views/big-screen/chat-stats/index.vue"),
        meta: {
          keepAlive: false,
          title: "会话统计",
        },
      },
      {
        name: "PROJECTINTENT",
        path: "/PROJECTINTENT",
        component: () => import("../views/benefit-management/project-intent/project-intent.vue"),
        meta: {
          keepAlive: false,
          title: "意向项目配置",
        },
      },
      {
        name: "INFORMATIONSOURCE",
        path: "/INFORMATIONSOURCE",
        component: () => import("../views/benefit-management/information-source/information-source.vue"),
        meta: {
          keepAlive: false,
          title: "信息来源配置",
        },
      },
      {
        name: "DOCTORPERFORMANCETABLE",
        path: "/DOCTORPERFORMANCETABLE",
        component: () => import("../views/report-forms/doctor-performance-table"),
        meta: {
          keepAlive: false,
          title: "医生面诊表",
        },
      },
      {
        name: "MEDICALORDERNATIVETABLE",
        path: "/MEDICALORDERNATIVETABLE",
        component: () => import("../views/report-forms/medical-order-native-table"),
        meta: {
          keepAlive: false,
          title: "开发消息明细表",
        },
      },
      {
        name: "CONSULTANTRANKINGTABLE",
        path: "/CONSULTANTRANKINGTABLE",
        component: () => import("../views/report-forms/consultant-ranking-table"),
        meta: {
          keepAlive: false,
          title: "咨询业绩排行表",
        },
      },
      {
        name: "CONSULTANTPERFORMANCETABLE",
        path: "/CONSULTANTPERFORMANCETABLE",
        component: () => import("../views/report-forms/consultant-performance-table"),
        meta: {
          keepAlive: false,
          title: "咨询面诊表",
        },
      },
      {
        name: "CONSULTANTCONSUMPTIONDETAIL",
        path: "/CONSULTANTCONSUMPTIONDETAIL",
        component: () => import("../views/report-forms/consultant-consumption-detail.vue"),
        meta: {
          keepAlive: false,
          title: "咨询消费明细表",
        },
      },
      {
        name: "DEVELOPERPROJECTSTATISTICSTABLE",
        path: "/DEVELOPERPROJECTSTATISTICSTABLE",
        component: () => import("../views/report-forms/developer-project-statistics-table"),
        meta: {
          keepAlive: false,
          title: "开发项目统计表",
        },
      },
      {
        name: "PROJECTSTATISTICSTABLE",
        path: "/PROJECTSTATISTICSTABLE",
        component: () => import("../views/report-forms/project-statistics-table"),
        meta: {
          keepAlive: false,
          title: "电网咨询项目表",
        },
      },
      {
        name: "SOURCESTATISTICSTABLE",
        path: "/SOURCESTATISTICSTABLE",
        component: () => import("../views/report-forms/source-statistics-table"),
        meta: {
          keepAlive: false,
          title: "电网咨询来源表",
        },
      },
      {
        name: "ONSITESOURCESTATISTICSTABLE",
        path: "/ONSITESOURCESTATISTICSTABLE",
        component: () => import("../views/report-forms/onsite-source-statistics-table"),
        meta: {
          keepAlive: false,
          title: "现场咨询信息来源表",
        },
      },
      {
        name: "ONSITEPROJECTSTATISTICSTABLE",
        path: "/ONSITEPROJECTSTATISTICSTABLE",
        component: () => import("../views/report-forms/onsite-project-statistics-table"),
        meta: {
          keepAlive: false,
          title: "现场咨询项目表",
        },
      },
      {
        name: "CONSULTANTPROJECTSTATISTICSTABLE",
        path: "/CONSULTANTPROJECTSTATISTICSTABLE",
        component: () => import("../views/report-forms/counselor-project-statistics-table"),
        meta: {
          keepAlive: false,
          title: "咨询师项目统计表",
        },
      },
      {
        name: "DEPARTMENTDOCTORDETAIL",
        path: "/DEPARTMENTDOCTORDETAIL",
        component: () => import("../views/report-forms/department-doctor-detail.vue"),
        meta: {
          keepAlive: false,
          title: "科室执行明细表",
        },
      },
      {
        name: "NETWORKCONSULTATIONRECORDS",
        path: "/NETWORKCONSULTATIONRECORDS",
        component: () => import("../views/e-store-report/network-consultation-records.vue"),
        meta: {
          keepAlive: false,
          title: "网络咨询记录",
        },
      },
    ],
  },
  {
    name: "CUSTOMINFO",
    path: "/CUSTOMINFO",
    component: () => import("../views/chatBar/chat-bar"),
    meta: {
      keepAlive: true,
      title: "客户详情",
      macthMobile: "isTrue",
    },
  },
  {
    name: "CHATCOMMONWORDS",
    path: "/CHATCOMMONWORDS",
    component: () => import("../views/chatBar/common-words"),
    meta: {
      keepAlive: true,
      title: "常用语",
      macthMobile: "isTrue",
    },
  },
  {
    name: "CHATARTICLELIST",
    path: "/CHATARTICLELIST",
    component: () => import("../views/chatBar/article"),
    meta: {
      keepAlive: false,
      title: "文章列表",
      macthMobile: "isTrue",
    },
  },
  {
    name: "SENDSURVERY",
    path: "/SENDSURVERY",
    component: () => import("../views/chatBar/survery/index"),
    meta: {
      keepAlive: true,
      title: "发送问卷",
      macthMobile: "isTrue",
    },
  },
  {
    name: "UNAVAILABLE",
    path: "/UNAVAILABLE",
    component: () => import("../views/unavailable/index"),
    meta: {
      keepAlive: true,
      title: "",
      validLogin: false,
    },
  },
  {
    name: "FULLBIGSCREEN",
    path: "/FULLBIGSCREEN",
            component: () => import("../views/big-screen/full-big-screen.vue"),
    meta: {
      keepAlive: false,
      title: "平台数据",
    },
  },
];

// 是否需要登录, 如果需要登陆的话, 配置登陆地址
if (import.meta.env.VITE_NEED_LOGIN === "true") {
  routes.push({
    name: "LOGIN",
    path: "/LOGIN",
    component: () => import("../views/login/index"),
    meta: {
      keepAlive: false,
      title: "登录",
      validLogin: false,
    },
  });
}

const router = createRouter({
  history: createWebHashHistory("/dist/"),
  routes: [...routes],
});
export function getRoutes(includesParentName = false) {
  const flatRoutes = (children, list = []) => {
    if (Array.isArray(children)) {
      children.forEach((i) => {
        if (Array.isArray(i.children)) flatRoutes(i.children, list);
        else {
          list.push(i);
          return;
        }
        if (includesParentName) list.push(i);
      });
    }
    return list;
  };
  return flatRoutes(routes);
}

router.beforeEach(async (to, from, next) => {
  const res = await useGuard(to, from, next);
  if (res === true) {
    next();
  } else if (res && res.name) {
    next(res);
  }
});

export default router;
