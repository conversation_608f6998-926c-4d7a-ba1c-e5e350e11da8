<template>
  <el-form-item class="px-15px py-6px el-form-item--autoheight" :class="required ? 'is-required' : ''" :label="name"
    border-bottom>
    <cell-bmi :form="form" />
  </el-form-item>
</template>
<script setup>
import { computed } from 'vue';
import CellBmi from '../display/cell-bmi.vue'

const props = defineProps({
  form: { type: Object, default: () => ({}) },
  name: { type: String, default: '' },
  required: { type: Boolean, default: false },
  title: { type: String, default: '' },
})

</script>
<style lang="scss" scoped></style>
