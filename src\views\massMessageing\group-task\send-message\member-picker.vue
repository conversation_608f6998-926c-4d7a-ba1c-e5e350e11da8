<template>
  <el-dialog :model-value="visible" :width="width" title="选择团队成员" @close="close">
    <div class="flex">
      <div class="flex flex-col w-1/2 border-r border-gray-100 h-40vh">
        <div class="flex-shrink-0 px-10px">
          <!-- <el-input v-model="name" placeholder="搜索" size="large" class="text-center" :suffix-icon="Search" /> -->
          <!-- <el-checkbox class="my-5px">
            <span class="hover:text-blue-500">团队成员</span>
          </el-checkbox> -->
        </div>

        <div class="flex-grow relative">
          <div class="absolute inset-0">
            <div v-infinite-scroll="load" :infinite-scroll-disabled="disabled" :infinite-scroll-immediate="false" class="scroll-container">
              <div class="flex justify-end pr-15px">
                <el-checkbox :model-value="showAllMember" @change="selectAllMember()" />
              </div>
              <div v-for="i in list" :key="i._id" class="flex items-center py-10px px-15px cursor-pointer border-b border-dashed border-gray-200 hover:border-gray-300">
                <img class="flex-shrink-0 w-40px h-40px" :src="i.avatar" alt="" />
                <div class="flex-grow w-0 truncate mx-10px text-dark-500">
                  <ww-user :openid="i.userid"></ww-user>
                </div>
                <el-checkbox :model-value="selectUsers.some((item) => item._id === i._id)" @change="selectCheckBox(i)" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <el-scrollbar class="w-1/2 flex-shrink-0" wrap-class="h-40vh">
        <div v-for="i in selectUsers" :key="i._id" class="flex items-center py-10px px-15px border-b border-dashed border-gray-200 text-transparent hover:border-gray-300 hover:text-red-500">
          <img class="flex-shrink-0 w-40px h-40px" :src="i.avatar" alt="" />
          <div class="flex-grow w-0 truncate mx-10px text-dark-500">
            <ww-user :openid="i.userid"></ww-user>
          </div>
          <el-icon class="cursor-pointer" @click="remove(i)"><CloseBold /></el-icon>
        </div>
      </el-scrollbar>
    </div>
    <template #footer>
      <div class="text-center">
        <el-button class="w-100px" @click="close()">取消</el-button>
        <el-button class="w-100px" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { teamStore } from "@/store/team";
import { storeToRefs } from "pinia";
const { teams, allTeams } = storeToRefs(teamStore());
import { ElMessage } from "element-plus";
import { getCorpMember } from "@/api/corp.js";
import WwUser from "@/components/ww-user/index.vue";
const selectUsers = ref([]);
const props = defineProps({
  visible: { type: Boolean, default: false },
  width: { type: [String, Number], default: 200 },
  teamIds: { type: Array, default: () => [] },
  memberList: { type: Array, default: () => [] },
  sendTaskType: { type: String, default: "" },
});
const memberList = ref([]);
let more = ref(false);
let page = 1;
let pageSize = 10;
let list = ref([]);
watch(
  () => props.visible,
  async (val) => {
    if (val) {
      let teamList = allTeams.value.filter((i) => props.teamIds.includes(i.teamId));
      let arr = teamList.flatMap((item) => item.memberList);
      memberList.value = Array.from(new Set(arr));
      await getList();
      selectUsers.value = list.value.filter((i) => props.memberList.some((j) => j === i.userid));
    }
  }
);
const emits = defineEmits(["close", "confirm"]);
const name = ref("");
function close() {
  emits("close");
}
const showAllMember = computed(() => selectUsers.value.length === list.value.length);
function selectAllMember() {
  if (showAllMember.value) {
    selectUsers.value = [];
  } else {
    selectUsers.value = list.value;
  }
}

function selectCheckBox(user) {
  if (selectUsers.value.some((i) => i._id === user._id)) {
    selectUsers.value = selectUsers.value.filter((i) => i._id !== user._id);
  } else {
    selectUsers.value.push(user);
  }
}
function remove(i) {
  selectUsers.value = selectUsers.value.filter((item) => item._id !== i._id);
}
async function getList() {
  let params = {
    corpId: localStorage.getItem("corpId"),
    memberList: memberList.value.map((i) => i),
  };
  let { success, data } = await getCorpMember(params, pageSize, page);
  if (success) {
    list.value = page === 1 ? data.data : [...list.value, ...data.data];
    more = data.total > pageSize * page;
  }
}
function load(event) {
  if (more) {
    page += 1;
    getList();
  }
}
function confirm() {
  if (selectUsers.value.length === 0) return ElMessage.info("请选择员工");
  emits(
    "confirm",
    selectUsers.value.map((i) => i.userid)
  );
}
</script>
<style lang="scss" scoped>
.scroll-container {
  height: 100%; // 请根据你的需求设置高度
  overflow-y: auto;
}
</style>
