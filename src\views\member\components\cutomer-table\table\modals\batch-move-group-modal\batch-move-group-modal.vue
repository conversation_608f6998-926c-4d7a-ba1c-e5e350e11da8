<template>
  <el-dialog :model-value="visible" title="批量分配团队" :width="width" @close="close">
    <div class="py-15px px-5px">
      <div v-if="showRadio" class="pl-10px border-b border-gray-200 pb-10px">
        <el-radio-group v-model="type">
          <el-radio class="flex" label="merge" value="merge">保留客户已授权团队并新增新的团队。</el-radio>
          <el-radio class="flex" label="replace" value="replace">清空客户已授权团队并新增新的团队。</el-radio>
        </el-radio-group>
      </div>
      <el-scrollbar wrap-style="max-height:40vh">
        <el-checkbox-group v-if="allTeams.length" v-model="teamIds" :class="showRadio?'pt-15px':''">
          <div v-for="item in allTeams" :key="item.teamId" class="px-10px">
            <el-checkbox :label="item.teamId" :value="item.teamId">{{ item.name }}</el-checkbox>
          </div>
        </el-checkbox-group>
      </el-scrollbar>
    </div>

    <template #footer>
      <div text-center>
        <el-button type="primary" plain class="w-100px" @click="close()">取消</el-button>
        <el-button :loading="loading" class="w-100px" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { ElMessage } from "element-plus";
import { batchUpdateCustomerTeamIds } from "@/api/member";
import { batchUpdateToDoAndManagePlan } from "@/api/todo";
import { teamStore } from "@/store/team";

const emits = defineEmits(["close", "change"]);
const props = defineProps({
  fixedType: { type: String, default: '' },
  customers: { type: Object, default: () => ({}) },
  visible: { type: Boolean, default: false },
  width: { type: Number },
});
const loading = ref(false);
const teamIds = ref([]);
const type = ref("replace");
const { allTeams } = storeToRefs(teamStore());
const showRadio = ref(true)

async function confirm() {
  if (!type.value) {
    ElMessage.warning("请选择操作类型");
    return;
  }
  if (teamIds.value.length == 0) {
    ElMessage.warning("请选择团队");
    return;
  }
  loading.value = true;
  const params = {
    customerIds: props.customers.map((item) => item._id),
    operationType: type.value,
    teamIds: teamIds.value,
  };
  await batchUpdateCustomerTeamIds(params);
  if (type.value === "replace") {
    let query = {
      customerIds: params.customerIds,
      operationType: 'closed'
    };
    await batchUpdateToDoAndManagePlan(query);
  }
  ElMessage.success("批量修改成功");
  loading.value = false;
  emits("change");
}

function close() {
  emits("close");
}
watch(
  () => props.visible,
  (n) => {
    if (n) {
      if (['merge', 'replace'].includes(props.fixedType)) {
        type.value = props.fixedType;
        showRadio.value = false
      } else {
        showRadio.value = true
        type.value = "";
      }
      teamIds.value = [];
    }
  }
);
</script>