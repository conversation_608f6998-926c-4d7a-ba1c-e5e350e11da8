<template>
  <div class="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-400 to-blue-700">
    <!-- 登录主卡片 -->
    <div class="w-96 p-8 bg-white rounded-2xl shadow-xl transition-all duration-300 hover:shadow-2xl">
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-800 mb-2">欢迎回来</h1>
        <p class="text-gray-500">请输入您的账号信息</p>
      </div>
      <el-form :model="loginForm" :rules="loginRules" ref="loginFormRef" @keyup.enter="handleLogin">
        <el-form-item prop="username">
          <el-input v-model="loginForm.username" placeholder="请输入用户账号" class="!rounded-lg" :prefix-icon="User" ref="usernameInput" />
        </el-form-item>
        <el-form-item prop="password">
          <el-input v-model="loginForm.password" type="password" placeholder="请输入密码" class="!rounded-lg" :prefix-icon="Lock" show-password />
        </el-form-item>

        <div class="mb-4 flex items-center">
          <el-checkbox v-model="rememberUsername" label="记住用户名"></el-checkbox>
        </div>

        <el-button type="primary" class="w-full !rounded-lg h-12 text-base hover:opacity-90 transition-opacity" @click="handleLogin" :loading="loading">立即登录</el-button>
      </el-form>
      <div class="mt-6 flex justify-between text-sm">
        <a href="#" class="text-gray-600 hover:text-blue-500 transition-colors" @click.prevent="forgotPassword">忘记密码?</a>
        <a href="#" class="text-gray-600 hover:text-blue-500 transition-colors" @click.prevent="editPassword">修改密码</a>
      </div>
    </div>
    <ModifyPasswordDialog :showModifyDialog="showModifyDialog" @close="closeModifyDialog" :username="loginForm.username" />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import { User, Lock } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { login } from "@/api/account";
import { memberStore } from "@/store/member";
import { templateStore } from "@/store/template";
import { getRolesByRoleId } from "@/api/corp";
import { teamStore as useTeamStore } from "@/store/team";
import { configStore } from "@/store/config";
import { useRouter } from "vue-router";
import ModifyPasswordDialog from "@/components/modify-password-dialog/modify-password-dialog.vue";

const router = useRouter();
const usernameInput = ref(null);

// 登录相关
const loginFormRef = ref();
const loading = ref(false);
const rememberUsername = ref(localStorage.getItem("rememberUsername") === "true");
const loginForm = reactive({
  username: localStorage.getItem("rememberUsername") === "true" ? localStorage.getItem("lastUsername") || "" : "",
  password: "",
});

const loginRules = {
  username: [{ required: true, message: "请输入用户名", trigger: "blur" }],
  password: [{ required: true, message: "请输入密码", trigger: "blur" }],
};

// 页面加载时自动聚焦到用户名输入框
onMounted(async () => {
  await nextTick();
  if (!loginForm.username && usernameInput.value) {
    usernameInput.value.focus();
  }
});

const handleLogin = async () => {
  try {
    await loginFormRef.value.validate();
    loading.value = true;
    const { username, password } = loginForm;
    // 记住用户名功能
    if (rememberUsername.value) {
      localStorage.setItem("lastUsername", username);
      localStorage.setItem("rememberUsername", "true");
    } else {
      localStorage.removeItem("lastUsername");
      localStorage.setItem("rememberUsername", "false");
    }
    // 调登录接口
    const res = await login({ username, password });
    const { success, message, data } = res;
    if (success) {
      const { user } = data;
      await loginSuccess(user);
      ElMessage.success("登录成功");
      return;
    }
    if (message === "密码未设置") {
      ElMessageBox.confirm("您的密码为初始密码，请修改密码", "提示", {
        confirmButtonText: "修改密码",
        type: "warning",
      }).then(() => {
        showModifyDialog.value = true;
      });
    } else {
      ElMessage.error(message);
    }
  } catch (error) {
    console.error("登录校验错误:", error);
  } finally {
    loading.value = false;
  }
};

const forgotPassword = () => {
  ElMessageBox.alert("请联系系统管理员重置您的密码", "忘记密码", {
    confirmButtonText: "确定",
  });
};

// 修改密码相关
function editPassword() {
  if (!loginForm.username) {
    return ElMessage.warning("请先输入用户名");
  }
  showModifyDialog.value = true;
}

// 登陆成功后的操作
async function loginSuccess({ userid, roleIds, corpId }) {
  localStorage.setItem("corpId", corpId);
  localStorage.setItem("userId", userid);
  const { getMemberInfo, getCorpInfo, setLogined } = memberStore();
  const { setCurrentTeam, getTeams, getAllTeams } = useTeamStore();
  const { getGroups } = configStore();
  const { getCorpTemplate } = templateStore();

  try {
    getCorpTemplate();

    // 并行加载所有数据提高性能
    const [user, corp, role] = await Promise.all([getMemberInfo(), getCorpInfo(), getRolesByRoleId(roleIds), getGroups(), getTeams(), getAllTeams()]);

    setCurrentTeam();

    if (!isAdminOrHaveTeam(role.data.data, user)) {
      ElMessage.error("应用管理员未给您配置角色,请联系应用管理员配置角色");
    } else if (user.accountState === "disable") {
      ElMessage.error("您已被禁用,如有疑问请联系机构管理员");
    } else if (!user.teamId || user.teamId.length === 0) {
      ElMessage.error("应用管理员未给您配置团队,请联系应用管理员配置角色");
    } else if (corp && corp.package && (corp.package.packageStatus === "expire" || corp.package.packageStatus === "closed")) {
      ElMessage.error("账号已到期，请联系平台客服升级套餐");
    } else {
      setLogined("logined");
      router.replace({ name: "Home" });
    }
  } catch (error) {
    console.error("加载用户数据失败:", error);
    ElMessage.error("登录后加载数据失败，请重试");
  }
}

function isAdminOrHaveTeam(roleList, user) {
  //1、不是机构管理员
  //2、没有加入任何团队
  return (Array.isArray(roleList) && roleList.some((item) => item.roleId === "admin")) || (user.teamId && user.teamId.length > 0);
}

// 修改密码相关
const showModifyDialog = ref(false);

function closeModifyDialog() {
  showModifyDialog.value = false;
}
</script>
  
<style lang="scss">
.el-input__wrapper {
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1) inset !important;
  transition: all 0.2s ease;

  &:hover,
  &:focus-within {
    box-shadow: 0 0 0 1px var(--el-color-primary) inset !important;
  }
}

.el-button {
  &:focus-visible {
    outline: 2px solid var(--el-color-primary);
    outline-offset: 2px;
  }
}
</style>