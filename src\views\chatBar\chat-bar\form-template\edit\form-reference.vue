<template>
  <el-form-item v-if="show" class="px-15px py-6px el-form-item--autoheight" :class="required ? 'is-required' : ''"
    :label="name" border-bottom>
    <div flex items-center class="w-full">
      <!-- <el-select class="flex-shrink-0 mr-15px" style="width: 90px;" placeholder="请选择" clearable
        :model-value="form.referenceType" @change="change('referenceType', $event)">
        <el-option v-for="option in referenceType" :label="option.label" :value="option.label" />
      </el-select> -->
      <div v-if="form.referenceType === '同事'" flex items-center flex-grow>
        <div v-if="form.referenceUserId" class="text-black mr-10px" flex-grow>
          <ww-user :openid="form.referenceUserId"></ww-user>
          <el-icon class="ml-10px cursor-pointer text-[#a8abb2]" @click="changeOrigin('','同事')">
            <CircleClose />
          </el-icon>
        </div>
        <el-input v-else placeholder="请选择同事" class="mr-10px" readonly flex-grow />
        <el-text flex-shrink-0 pointer type="primary" @click="chooseCoWorker()">选择</el-text>
      </div>
      <remote-select-customer v-else-if="form.referenceType === '客户'" class="flex-grow" :default-name="form.reference"
        :option-width="280" :value="form.referenceCustomerId" placeholder="请搜索客户姓名" flex-grow
        @change="changeOrigin($event, '客户')" />
    </div>
  </el-form-item>
</template>
<script setup>
import { computed, toRefs, watch } from 'vue';
import { selectEnterpriseContact } from '@/utils/jssdk';

import RemoteSelectCustomer from '@/components/remote-select-customer/remote-select-customer';

const emits = defineEmits(['change'])
const props = defineProps({
  form: { type: Object, default: () => ({}) },
  name: { type: String, default: '' },
  required: { type: Boolean, default: false },
  title: { type: String, default: '' },
})

const referenceType = computed(() => [
  { label: '同事推荐', value: '同事推荐' },
  { label: '客户推荐', value: '客户推荐' },
])
const { form } = toRefs(props)
const show = computed(() => {
  const customerSource = Array.isArray(form.value.customerSource) ? form.value.customerSource : []
  return customerSource.length === 1 && ['同事推荐', '客户推荐'].includes(customerSource[0])
})
async function chooseCoWorker() {
  const res = await selectEnterpriseContact('single');
  const { selectedUserList = [] } = res;
  if (selectedUserList && selectedUserList[0]) changeOrigin(selectedUserList[0].id, '同事')
}

function changeOrigin(data, type) {
  if (type === '同事') {
    const userid = data;
    change('referenceUserId', userid);
    change('reference', '');
    change('referenceCustomerId', '');
  } else if (type === '客户') {
    const customer = data;
    change('referenceUserId', '');
    change('reference', customer.name);
    change('referenceCustomerId', customer._id);
  }
}

function change(title, value) {
  emits('change', { title, value })
}


watch(() => form.value.customerSource, n => {
  const source = Array.isArray(n) ? n : []
  if ((source.join() === '同事推荐' && form.value.referenceType == '同事') || (source.join() === '客户推荐' && form.value.referenceType == '客户')) return;
  if (source.join() === '同事推荐' && form.value.referenceType !== '同事') {
    change('referenceType', '同事');
    change('reference', '');
    change('referenceCustomerId', '');
    change('referenceUserId', '');
  } else if (source.join() === '客户推荐' && form.value.referenceType !== '客户') {
    change('referenceType', '客户');
    change('reference', '');
    change('referenceCustomerId', '');
    change('referenceUserId', '');
  } else {
    change('referenceType', '');
    change('reference', '');
    change('referenceCustomerId', '');
    change('referenceUserId', '');
  }
}, { immediate: true })


</script>
