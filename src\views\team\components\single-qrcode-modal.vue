<template>
  <el-dialog :model-value="visible" :width="width" @close="close()">
    <template #header>
      <div class="max-w-2/3 truncate font-semibold">{{ data.teamName ? `${data.teamName}-二维码` : '' }}</div>
    </template>
    <el-scrollbar ref="scrollbarRef" wrap-style="max-height: calc(85vh - 200px)">
      <div class="pt-10px">
        <div color-normal class="px-20px text-center text-16px font-semibold leading-6">{{ data.name }}</div>
        <template v-if="data.qrcode">
          <vue-qrcode class="mx-auto mt-5px" :value="data.qrcode" :options="{ width: 180, margin: 2 }"></vue-qrcode>
          <div class="flex justify-center mt-15px">
            <el-button type="primary" class="w-120px" @click="save()">下载二维码图片</el-button>
            <el-button type="primary" class="w-120px" @click="copyLink(data.qrcode)">复制二维码链接</el-button>
          </div>
        </template>
      </div>
      <div class="border-t border-solid border-[#ebeef5] mt-10px pt-10px px-15px flex items-center">
        <div class="text-14px flex-shrink-0" color-normal>建档模版链接：</div>
        <div class="border border-[#ebeef5] py-4px px-8px rounded break-all"> {{archiveLink }}</div>
        <el-text type="primary" class="cursor-pointer flex-shrink-0 ml-10px" @click="copyLink(archiveLink)">复制</el-text>
      </div>
    </el-scrollbar>

    <template #footer>
      <div text-center>
        <el-button type="primary" plain class="w-100px" @click="close">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed } from 'vue';
import { downloadQrcode, useCopy } from '@/utils';
import { teamStore } from "@/store/team";

const { getArchiveLink } = teamStore();
import VueQrcode from "@chenfengyuan/vue-qrcode";

const props = defineProps({
  visible: Boolean,
  data: Object,
  width: [String, Number],
})
const emit = defineEmits(['close'])
const archiveLink = computed(() => getArchiveLink(props.data.teamId, props.data.id))
const close = () => {
  emit('close')
}
function save() {
  downloadQrcode(props.data.qrcode, `${props.data.name}.png`);
}

function copyLink(content) {
  useCopy(content);
}
</script>
