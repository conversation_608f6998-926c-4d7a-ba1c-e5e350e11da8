<template>
  <div class="selectMember">
    <el-dialog :model-value="visible" :width="500" :show-close="false">
      <div class="flex h-450px">
        <div class="w-250px bac-color overflow-y-auto r_10">
          <div class="member_title">平台数据源</div>
          <div>
            <div
              v-for="item in list"
              class="mx-10px pl-20px my-15px pointer"
              @click="checkedMember(item)"
              :class="
                selectItem.title === item.title && !item.required
                  ? 'check-adtive'
                  : ''
              "
            >
              <div class="flex align-center w-200px">
                <img
                  src="@/assets/check-active.png"
                  class="cell-radio"
                  v-if="showChecked(item) || item.required"
                />
                <img src="@/assets/check.png" class="cell-radio" v-else />
                <span> {{ item.name }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="w-250px">
          <div class="member_num_title">已选字段</div>
          <div class="h-360px overflow-y-auto px-20px">
            <div
              class="flex pb-10px"
              style="justify-content: space-between; align-items: center"
              v-for="item in checkedList"
            >
              <div class="flex">
                {{ item.name }}
              </div>
              <el-icon
                class="pointer"
                @click="remove(item)"
                v-if="!item.required"
                ><Close
              /></el-icon>
            </div>
          </div>
          <div class="flex button">
            <div class="b_l pointer" @click="cancel">取消</div>
            <div class="b_r pointer" @click="submit">确定</div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import WwUser from "@/components/ww-user/index.vue";
import { getCorpMember } from "@/api/corp.js";
import { computed, ref } from "vue";
const checkedList = ref([]);
const list = ref([]);
const selectItem = ref({
  title:''
});
const visible = ref(false);
let selectType = "";
const emit = defineEmits("getSelectItems", "close");
function checkedMember(item) {
  const index = checkedList.value.findIndex((i) => i.title === item.title);
  if (index !== -1) {
    if (item.required) return;
    checkedList.value.splice(index, 1);
  } else {
    selectItem.value = item;
    checkedList.value.push(item);
  }
}
function remove(item) {
  const index = checkedList.value.findIndex((i) => i.title === item.title);
  if (index !== -1) {
    checkedList.value.splice(index, 1);
  }
}
function showChecked(item) {
  let arr = checkedList.value.filter((i) => i.title === item.title);
  return arr.length > 0;
}
function cancel() {
  visible.value = false;
}
function submit() {
  visible.value = false;
  emit("getItems", checkedList.value, selectType);
}
function openDialog(allList, checkList, type) {
  visible.value = true;
  list.value = allList;
  checkedList.value = checkList;
  selectType = type;
}
defineExpose({
  openDialog,
});
</script>
<style scoped lang="scss">
.member_title {
  background-color: rgb(24, 143, 243);
  border-radius: 4px;
  margin: 10px;
  height: 25px;
  color: #fff;
  line-height: 25px;
  padding-left: 5px;
}

.member_num_title {
  font-weight: 800;
  font-size: 14px;
  padding: 10px;
}

.check-adtive {
  background-color: rgb(24, 143, 243);
  border-radius: 4px;
  color: #fff;
}
.bac-color {
  background-color: rgb(244, 244, 255);
}
.r_10 {
  border-radius: 10px;
}

.button {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: center;

  .b_l {
    background-color: rgb(244, 244, 255);
    width: 100px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    margin-right: 20px;
    border-radius: 3px;
  }
  .b_r {
    width: 100px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    color: #fff;
    background: #006eff;
    border-radius: 3px;
  }
}
.cell-radio {
  width: 16px;
  height: 16px;
  cursor: pointer;
  margin-right: 10px;
}
</style>
