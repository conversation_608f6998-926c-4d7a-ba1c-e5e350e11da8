<template>
  <div flex>
    <div flex-grow bg-fff common-shadow rounded-8>
      <div flex items-center justify-between p-15>
        <div font-16 font-semibold active-title-bar>
          {{ now }} 今日团队待办情况
        </div>
        <div font-14>
          更新时间：{{ updateTime }}
          <span color-primary pointer @click="reload()">刷新</span>
        </div>
      </div>
      <el-row text-center py-10 border-bottom font-18 font-semibold>
        <el-col :span="5">
          <div class="mb-6px">待办总数</div>
          <div>{{ total }}</div>
        </el-col>
        <el-col :span="4">
          <div class="mb-6px">已处理</div>
          <div>{{ teamStatistics["treated"] || 0 }}</div>
        </el-col>
        <el-col :span="5">
          <div class="mb-6px">已关闭</div>
          <div>{{ closedNum }}</div>
        </el-col>
        <el-col :span="4">
          <div class="mb-6px">待处理</div>
          <div>{{ teamStatistics["untreated"] || 0 }}</div>
        </el-col>
        <el-col :span="6">
          <div class="mb-6px">重点待办未处理</div>
          <div>0</div>
        </el-col>
      </el-row>
      <div text-center font-14 py-10 relative>
        <el-text type="primary" pointer @click="toggle()">
          团队成员待办详情
          <el-icon>
            <CaretTop v-show="props.showToday" />
            <CaretBottom v-show="!props.showToday" />
          </el-icon>
        </el-text>
        <template v-if="props.showToday">
          <span class="triangle"></span>
          <span class="triangle-shadow"></span>
        </template>
      </div>
    </div>
    <div flex-shrink-0 class="hidden-md-and-down w-10px"></div>
  </div>
</template>
<script setup>
import { ref, onActivated, onDeactivated, inject, watch, computed } from "vue";
import { useTimestamp, watchThrottled } from "@vueuse/core";
import dayjs from "dayjs";
import { WeekDay } from "@/baseData";
import { statisticsEventsByTeamId } from "@/api/todo.js";
const teamStatistics = ref({});
const updateTime = ref('');
updateTime.value = dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss");
const props = defineProps({
  showToday: { type: Boolean, default: false },
  currentTeam: {
    type: Object,
    default: {}
  }
});
watch(
  () => props.currentTeam,
  (newValue) => {
    statisticsUserEventsByTeamIdAction(newValue.teamId);
  },
  { immediate: true }
);

const closedNum = computed(() => {
  const close = teamStatistics.value["closed"] || 0;
  const expire = teamStatistics.value["expire"] || 0;
  return close + expire;
});
const total = computed(() => {
  const close = teamStatistics.value["closed"] || 0;
  const expire = teamStatistics.value["expire"] || 0;
  const treated = teamStatistics.value["treated"] || 0;
  const untreated = teamStatistics.value["untreated"] || 0;

  return close + expire + treated + untreated;
});
const { timestamp, pause, resume } = useTimestamp({ controls: true });
const now = ref("");
function formatNow() {
  const weekdayIndex = dayjs(timestamp.value).day();
  now.value = dayjs(timestamp.value).format(
    `YYYY年MM月DD日 ${WeekDay[weekdayIndex]} HH:mm`
  );
}
onActivated(() => resume());
onDeactivated(() => pause());
watchThrottled(
  timestamp,
  () => {
    formatNow();
  },
  { throttle: 1000 * 15 }
);

const emits = defineEmits(["reload", "toggle"]);
function reload() {
  updateTime.value = dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss");
}
function toggle() {
  emits("toggle");
}

async function statisticsUserEventsByTeamIdAction(teamId) {
  let { data, success } = await statisticsEventsByTeamId(teamId);
  if (success) {
    teamStatistics.value = data.data.reduce((result, item) => {
      result[item._id] = item.num;
      return result;
    }, {});
  }
}
</script>
<style scoped>
.triangle {
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-bottom: 20px solid white;
  /*我们一般根据方向来写三角形，这样容易记忆；箭头指向的反方向的颜色设置为你想要的，然后箭头方向不要写，另外两个方向的颜色设置为transperent透明*/
  position: absolute;
  bottom: -15px;
  left: 50%;
  margin-left: -10px;
  z-index: 2;
}

.triangle-shadow {
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-bottom: 20px solid rgba(0, 0, 0, 0.1);
  /*我们一般根据方向来写三角形，这样容易记忆；箭头指向的反方向的颜色设置为你想要的，然后箭头方向不要写，另外两个方向的颜色设置为transperent透明*/
  position: absolute;
  bottom: -14px;
  left: 50%;
  margin-left: -10px;
  z-index: 1;
}
</style>
