<template>
    <el-input :model-value="value" :placeholder="item.placeholder || ''" :maxlength="item.wordLimit"
        @update:model-value="change($event)" class="w-200px">
        <template v-if="item.appendText" #append>{{ item.appendText }}</template>
    </el-input>
</template>
<script setup>
import { watch,ref } from "vue";
const $emit = defineEmits(["change"]);
const props = defineProps({
    item: {
        type: Object,
        default: {}
    },
    value:{
        type: String,
        default: ''
    }
})


function change(value) {
    value = value.replace(/[^0-9.]/g, '');
    // 保留两位小数
    const parts = value.split('.');
    if (parts.length > 1) {
        value = `${Number(parts[0])}.${parts[1].slice(0, 2)}`;
    } else if (parts[0].length) {
        value = `${Number(parts[0])}`
    }
    $emit("change", {
        title: props.item.title,
        value
    })
}
</script>

<style></style>