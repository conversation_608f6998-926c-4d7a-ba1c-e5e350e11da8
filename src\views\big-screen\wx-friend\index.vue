<template>
  <el-scrollbar class="h-full">
    <div class="flex justify-between items-center py-5px text-gray-500">
      <div class="text-15px text-gray-600">数据更新截止时间：{{ currentTime }}</div>
      <!-- <div class="flex items-center cursor-pointer hover:text-blue-500">
        <el-icon class="text-18px" :class="refreshing ? 'text-blue-500 animate-spin' : ''" @click="refresh">
          <Refresh />
        </el-icon>
        <div class="ml-5px text-14px">刷新</div>
      </div> -->
    </div>
    <div class="flex items-center mt-5px">
      <div v-for="(item, idx) in friendCount" class="rounded bg-white px-20px py-15px flex-grow"
        :class="idx > 0 ? 'ml-15px' : ''">
        <div class="flex items-center">
          <div class="text-18px font-semibold mr-5px">{{ item.title }}</div>
          <el-tooltip placement="right" effect="light" :content="item.tip">
            <el-icon class="text-16px text-gray-400 hover:text-blue-500">
              <QuestionFilled />
            </el-icon>
          </el-tooltip>
        </div>
        <div class="mt-10px">
          <count-to-bar class="text-32px font-semibold text-blue-700" :end-val="item.count" :duration="1000" />
        </div>
      </div>
    </div>
    <div v-loading="trendLoading" class="mt-10px bg-white rounded">
      <card-head :date-items="trendDateItems" title="数据趋势" :tabs="trendTabs" @change="onTrendChange"
        :staffList="staffList" />
      <div ref="trendChart" class="h-280px"></div>
    </div>
    <div v-loading="tableLoading" class="mt-10px bg-white rounded">
      <card-head :date-items="detailDateItems" title="数据明细" :tabs="detailTabs" @change="onDetailChange"
        :staffList="staffList">
        <template v-if="currentTab === 'channel'" #append>
          <el-cascader v-model="source" class="w-280px ml-10px" collapse-tags :show-all-levels="false"
            :props="{ multiple: true, value: '_id', checkStrictly: false }" :options="cateTree" placeholder="请选择活码分类"
            filterable clearable />
          <span class="ml-auto text-blue-700">数据来源：【获客拉新】模块</span>
        </template>
      </card-head>
      <template v-if="currentTab === 'staff'">
        <el-table stripe :data="employeeList" :height="300">
          <el-table-column label="员工姓名" type="userid" min-width="160">
            <template #default="{ row: { userId } }">
              <ww-user :openid="userId" />
            </template>
          </el-table-column>
          <el-table-column label="添加好友数" prop="newContactCount" min-width="160">
            <template #default="{ row: { newContactCount } }">
              {{ newContactCount || 0 }}
            </template>
          </el-table-column>

          <el-table-column label="流失好友数" prop="negativeFeedbackCnt" min-width="160">
            <template #default="{ row: { negativeFeedbackCnt } }">
              {{ negativeFeedbackCnt || 0 }}
            </template>
          </el-table-column>
        </el-table>
        <div class="px-15px flex justify-between items-center text-14px text-gray-600">
          <div class="flex-shrink-0 mr-10">
            <span class="mr-20px">添加好友数：{{ allContactCount }}</span>
            <span class="mr-20px">流失好友数：{{ allNegativeFeedbackCnt }}</span>
          </div>
          <div>
            <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize"
              @handle-current-change="changePage" />
          </div>
        </div>
      </template>
      <template v-else-if="currentTab === 'channel'">
        <el-table stripe :data="codeList" :height="300">
          <el-table-column label="活码一级分类" prop="label1" min-width="120" />
          <el-table-column label="活码二级分类" prop="label2" min-width="120" />
          <el-table-column label="活码三级分类" prop="label3" min-width="120" />
          <el-table-column label="活码名称" prop="qrCodeName" min-width="160"></el-table-column>
          <el-table-column label="添加好友数" prop="count" min-width="160" />
          <el-table-column label="占比" prop="percent" min-width="160">
            <template #header>
              <div class="inline-flex items-center">
                <span>占比</span>
                <el-tooltip placement="top" effect="light" content="占比 = 单个活码添加客户数 / 全部活码添加客户数 * 100%">
                  <el-icon class="ml-2px transform -translate-y-1px hover:text-blue-500">
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="p-15px flex justify-between items-center text-14px text-gray-600">
          <div class="flex-shrink-0 mr-10">
            <span class="mr-20px">添加好友数：{{ codeTotal }}</span>
          </div>
        </div>
      </template>
    </div>
  </el-scrollbar>
</template>
<script setup>
import { computed, onMounted, ref } from "vue";
import { watchDebounced } from "@vueuse/core";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import { staffStore } from "@/store/staff";
import BigNumber from "bignumber.js";
import { getStaffQrcodeCateList, getQrcodeCustomerStatsByCateId } from "@/api/knowledgeBase";
import { getBehaviorStatistics } from "@/api/corp";
import { getCorpAllStaffUseBehaviorDataToday } from "@/api/wecom";
import { memberStore } from "@/store/member";
import useElPagination from "@/hooks/useElPagination";
import useLineChart from "@/hooks/useLineChart";
import useClassifyList from "@/components/classify-list/useClassifyList";
import pagination from "@/components/pagination/pagination.vue";
import cardHead from "./card-head.vue";
import countToBar from "../count-to-bar.vue";
const currentTime = ref("");
const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());
const allTotalStats = ref({});
const allTotalStatsList = ref({});
const allContactCount = ref(0);
const todayNegativeFeedbackCnt = ref(0);
const todayNewContactCount = ref(0);
const allNegativeFeedbackCnt = ref(0);
const yesterdayNegativeFeedbackCnt = ref(0);
const yesterdayNewContactCount = ref(0);
const total = ref(0);
const friendCount = computed(() => {
  const totals = allTotalStats.value.newContactCount;
  return [
    { title: "添加好友总数", count: totals, tip: "本平台成员添加客户好友总数" },
    { title: "昨日添加好友数", count: yesterdayNewContactCount.value, tip: "昨日本平台成员添加客户好友总数" },
    { title: "昨日流失好友数", count: yesterdayNegativeFeedbackCnt.value, tip: "昨日删除/拉黑成员的客户数" },
  ];
});
const trendTabs = [
  { title: "添加好友数", key: "add" },
  { title: "好友总数", key: "total" },
];
const detailTabs = [
  { title: "以员工统计", key: "staff" },
  { title: "以渠道统计", key: "channel" },
];
const trendDateItems = [{ label: "近7天", value: -7 }, { value: "lastWeek" }, { value: "thisMonth" }, { value: "lastMonth" }];
const detailDateItems = [{ value: "all" }, { label: "近7天", value: -7 }, { value: "lastWeek" }, { value: "thisMonth" }, { value: "lastMonth" }];

const { memberInfo } = storeToRefs(memberStore());
const trendChart = ref(null);
const currentTab = ref("staff");
const staffQuery = ref({});
const list = ref([]);
const trendData = ref([]);
const codeQuery = ref({});
const codeList = ref([]);
const codeTotal = ref(0);
const source = ref([]);
const { xAxisData, seriesData } = useLineChart(trendChart, { grid: { left: 30, right: 40 } });
const { page, pageSize, changePage, changeSize } = useElPagination();
const { cateList, cateTree } = useClassifyList({ getList: getStaffQrcodeCateList });
const cateGroup = computed(() => {
  const m = new Map();
  cateList.value
    .filter((i) => i.level === 1)
    .forEach((i) => {
      m.set(i._id, { labels: [i.label] });
    });
  cateList.value
    .filter((i) => i.level === 2)
    .forEach((i) => {
      const data = { labels: ["", i.label] };
      if (m.get(i.parentId)) {
        data.labels = [...m.get(i.parentId).labels, i.label];
      }
      m.set(i._id, data);
    });
  cateList.value
    .filter((i) => i.level === 3)
    .forEach((i) => {
      const data = { labels: ["", "", i.label] };
      if (m.get(i.parentId)) {
        data.labels = [...m.get(i.parentId).labels, i.label];
      }
      m.set(i._id, data);
    });
  return m;
});
const trendLoading = ref(false);
const tableLoading = ref(false);
const employeeList = computed(() => {
  const start = (page.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  // 合并今日之前 和 今日的 数据
  // const mergedArray = [...list.value, ...todayStaffContactList.value].reduce((acc, item) => {
  //   const existingItem = acc.find((i) => i.userId === item.userId);
  //   if (existingItem) {
  //     // 如果已存在相同 userId，累加字段
  //     existingItem.newContactCount += item.newContactCount || 0;
  //     existingItem.negativeFeedbackCnt += item.negativeFeedbackCnt || 0;
  //   } else {
  //     // 如果没有找到相同的 userId，直接添加
  //     acc.push({ ...item });
  //   }
  //   return acc;
  // }, []);
  list.value.sort((a, b) => b.newContactCount - a.newContactCount);
  let arr = list.value;
  if (staffQuery.value.userId) arr = list.value.filter((i) => i.userId == staffQuery.value.userId);
  return arr.slice(start, end);
});
// const total = computed(() => {
//   let map = new Map();
//   // 遍历第一个数组并添加到 Map 中
//   for (let item of list.value) {
//     if (!map.has(item.userId)) {
//       map.set(item.userId, item);
//     }
//   }
//   // 遍历第二个数组并添加到 Map 中
//   for (let item of todayStaffContactList.value) {
//     if (!map.has(item.userId)) {
//       map.set(item.userId, item);
//     }
//   }
//   // 从 Map 中获取去重后的值，并转换为数组
//   return map.size;
// });
async function onTrendChange({ dates, userId, tab }) {
  if (dates.length == 0) dates = [dayjs().startOf("isoWeek").format("YYYY-MM-DD"), dayjs().endOf("isoWeek").format("YYYY-MM-DD")];
  trendLoading.value = true;
  const x = getDates(dates);
  const s = await getSeriesData(dates, userId, tab);
  xAxisData.value = x;
  seriesData.value = s;
  trendLoading.value = false;
}
function onDetailChange({ tab, type, ...data }) {
  currentTab.value = tab;
  if (tab === "staff") {
    staffQuery.value = data;
    if (type !== "selectStaff") getweChatStatisticsByUser({ type: "userId", dates: staffQuery.value.dates });
    // 查询员工统计列表
  } else {
    codeQuery.value = data;
    getCodeList();
    // 查询活码统计列表
  }
}
async function getweChatStatisticsByUser({ type, dates, userId }) {
  tableLoading.value = true;
  let { data } = await getBehaviorStatistics({ statisticType: type, dates, userId });
  tableLoading.value = false;
  // dates 为空时, 获取全部数据
  if (type === "userId") {
    const totalStats = calculateTotals(data.totalStats);
    if (staffQuery.value.dates.length === 0) {
      allTotalStats.value = totalStats;
      allTotalStatsList.value = data.totalStats;
    }
    list.value = Object.entries(data.totalStats).map(([id, stats]) => ({
      userId: id,
      ...stats,
    }));
    total.value = list.value.length;
    allContactCount.value = totalStats.newContactCount;
    allNegativeFeedbackCnt.value = totalStats.negativeFeedbackCnt;
  } else {
    trendData.value = data.totalStats;
  }
  tableLoading.value = false;
}

async function getYesterdayweChatStatistics() {
  // 获取到昨日的日期
  const yesterdayTime = dayjs().subtract(1, "day").format("YYYY-MM-DD");
  const dates = [yesterdayTime, yesterdayTime];
  let { data, success } = await getBehaviorStatistics({ statisticType: "statTime", dates });
  if (success) {
    const totalStats = calculateTotals(data.totalStats);
    yesterdayNewContactCount.value = totalStats.newContactCount;
    yesterdayNegativeFeedbackCnt.value = totalStats.negativeFeedbackCnt;
  }
}

// async function getstaffUseBehaviorDataToday() {
//   const userIds = staffList.value.map((i) => i.userid);
//   let { data, success } = await getCorpAllStaffUseBehaviorDataToday({ userIds });
//   if (success) {
//     todayStaffContactList.value = data.data;
//     const totals = data.data.reduce(
//       (acc, item) => {
//         acc.newContactCount += item.newContactCount || 0;
//         acc.negativeFeedbackCnt += item.negativeFeedbackCnt || 0;
//         return acc;
//       },
//       {
//         newContactCount: 0,
//         negativeFeedbackCnt: 0,
//       }
//     );
//     todayNewContactCount.value = totals.newContactCount;
//     todayNegativeFeedbackCnt.value = totals.negativeFeedbackCnt;
//   }
// }
async function getCodeList() {
  tableLoading.value = true;
  const query = {
    corpId: memberInfo.value.corpId,
    startTime: codeQuery.value.dates && codeQuery.value.dates[0] ? codeQuery.value.dates[0] : "",
    endTime: codeQuery.value.dates && codeQuery.value.dates[1] ? codeQuery.value.dates[1] : "",
    cateIds: source.value.length ? source.value.map((i) => (Array.isArray(i) ? i[i.length - 1] : "")).filter(Boolean) : cateList.value.map((i) => i._id),
  };
  const { data, success, message } = await getQrcodeCustomerStatsByCateId(query);
  const qrcodes = data && Array.isArray(data.qrcodes) ? data.qrcodes : [];
  const countList = data && Array.isArray(data.data) ? data.data : [];
  const qrcodeMap = qrcodes.reduce((m, i) => {
    m[i.configId] = i;
    return m;
  }, {});
  const totalNum = countList.reduce((m, i) => m.plus(i.count > 0 ? i.count : 0), new BigNumber(0));
  codeTotal.value = totalNum.toNumber();
  codeList.value = countList.map((i) => {
    const code = qrcodeMap[i._id];
    console.log("code: ", code);
    if (code) {
      i.qrCodeName = code.qrCodeName;
      i.cateId = code.cateId;
      const cate = cateGroup.value.get(code.cateId);
      const labels = cate ? cate.labels : [];
      i.label1 = labels[0];
      i.label2 = labels[1];
      i.label3 = labels[2];
    }
    i.percent = new BigNumber(i.count).div(Math.max(codeTotal.value, 1)).multipliedBy(100).toNumber().toFixed(2) + "%";
    return i;
  });
  tableLoading.value = false;
}
function calculateTotals(data) {
  // 如果对象为空，直接返回初始值
  if (Object.keys(data).length === 0) {
    return {
      newContactCount: 0,
      replyPercentage: 0,
      negativeFeedbackCnt: 0,
    };
  }
  const totals = {
    newContactCount: 0,
    replyPercentage: 0,
    negativeFeedbackCnt: 0,
  };
  // 遍历每个数据项并累加
  Object.values(data).forEach((item) => {
    totals.newContactCount += item.newContactCount;
    totals.replyPercentage += item.replyPercentage;
    totals.negativeFeedbackCnt += item.negativeFeedbackCnt;
  });
  return totals;
}
onMounted(async () => {
  currentTime.value = dayjs().format("YYYY-MM-DD HH:mm:ss");
  if (!staffList.value.length) await getStaffList();
  await getYesterdayweChatStatistics();
});
watchDebounced(
  source,
  () => {
    getCodeList();
  },
  { debounce: 500 }
);
// mock......
function getDates(dates) {
  if (Array.isArray(dates) && dates.length === 2) {
    let [startDate, endDate] = dates;
    const res = [];
    while (dayjs(endDate).isSameOrAfter(dayjs(startDate)) && dayjs().isAfter(dayjs(startDate))) {
      res.push(dayjs(startDate).format("YYYY/MM/DD"));
      startDate = dayjs(startDate).add(1, "day").format("YYYY-MM-DD");
    }
    return res;
  }
  return [];
}
async function getSeriesData(dates, userId, tab) {
  if (dates.length === 0) return [];
  await getweChatStatisticsByUser({ type: "statTime", dates, userId });
  const list = fillMissingDatesAndConvertToArray(dates, trendData.value);
  if (tab === "add") return list;
  let newContactCount = allTotalStats.value.newContactCount;
  if (userId) {
    newContactCount = allTotalStatsList.value[userId].newContactCount;
  }
  return calculateDailyTotals(list, newContactCount);
}

// 算出每日总量
function calculateDailyTotals(list, totals) {
  const dailyTotals = new Array(list.length); // 创建一个与 list 长度相同的数组
  dailyTotals[list.length - 1] = totals; // 最后一项总量为 totals
  // 倒序计算每日总
  for (let i = list.length - 2; i >= 0; i--) {
    dailyTotals[i] = dailyTotals[i + 1] - list[i + 1]; // 当前天的总量等于后一天的总量减去后一天的新增量
  }
  return dailyTotals;
}

function fillMissingDatesAndConvertToArray(dateArray, obj) {
  // 获取日期范围内的所有时间戳
  const dateRange = generateDateRange(new Date(dateArray[0]).getTime(), new Date(dateArray[1]).getTime());
  // 创建一个新的对象用于存放补全后的数据
  const result = [];
  // 遍历日期范围，检查每个时间戳是否在 obj 中
  dateRange.forEach((dateTimestamp) => {
    // 检查时间戳对应的日期是否存在
    const date = dayjs(dateTimestamp).format("YYYY-MM-DD");
    const newDateTimestamp = dayjs(date).unix();
    if (obj[newDateTimestamp]) {
      result.push(obj[newDateTimestamp].newContactCount);
    } else {
      result.push(0); // 补全的日期，num 为 0
    }
  });
  // 按时间戳顺序返回 num 值的数组
  return result;
}

function generateDateRange(startDate, endDate) {
  const dates = [];
  let currentDate = new Date(startDate);
  const end = new Date(endDate);

  while (currentDate <= end) {
    // 获取当前日期的时间戳，并存入数组
    dates.push(currentDate.getTime());
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return dates;
}
</script>
<style lang="scss" scoped></style>
