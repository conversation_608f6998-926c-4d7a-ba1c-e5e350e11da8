<template>
  <div v-if="item.title === 'createTime'">
    {{ isTimestamp(value) ? dayjs(value).format("YYYY-MM-DD HH:mm") : value }}
  </div>
  <referenceCount v-else-if="item.title === 'referenceCount'" :value="value" :form="form" @change="onChange" />
  <positive-find-table v-else-if="item.type === 'positiveFind'" :title="item.title" :value="value" :form="form" :isRead="isRead" @change="onChange" />
  <self-date v-else-if="item.type === 'date'" @change="onChange" :item="item" :value="value" :isRead="isRead"></self-date>
  <self-input v-else-if="item.type === 'input'" @change="onChange" :item="item" :value="value" :isRead="isRead"></self-input>
  <self-textarea v-else-if="item.type === 'textarea'" @change="onChange" :item="item" :value="value" :isRead="isRead"></self-textarea>
  <self-input-amount v-else-if="item.type === 'inputAmount'" @change="onChange" :item="item" :value="value" :isRead="isRead"></self-input-amount>
  <self-select v-else-if="item.type === 'select'" @change="onChange" :item="item" :value="value" :isRead="isRead"></self-select>
  <self-radio v-else-if="item.type === 'radio'" @change="onChange" :item="item" :value="value" :isRead="isRead"></self-radio>
  <self-region v-else-if="item.type === 'region'" @change="onChange" :item="item" :value="value" :isRead="isRead"></self-region>
  <customer-stage v-else-if="item.type === 'customerStage'" @change="onChange" :item="item" :value="value" :isRead="isRead"></customer-stage>
  <self-multiple-other v-else-if="item.type === 'multiSelectAndOther'" @change="onChange" :item="item" :value="value" :isRead="isRead"></self-multiple-other>
  <self-select-other v-else-if="item.type === 'selectAndOther'" @change="onChange" :item="item" :value="value" :isRead="isRead"></self-select-other>
  <self-select-image v-else-if="item.type === 'selectAndImage'" @change="onChange" :item="item" :value="value" :isRead="isRead"></self-select-image>
  <self-multiple-diseases v-else-if="item.type === 'selfMultipleDiseases'" @change="onChange" :item="item" :value="value" :isRead="isRead"></self-multiple-diseases>
  <reference v-else-if="item.type === 'reference'" @change="onChange" :item="item" :value="value" :form="form" :isRead="isRead"></reference>
  <diagnosis v-else-if="item.type === 'diagnosis'" @change="onChange" :item="item" :value="value" :form="form" :isRead="isRead"></diagnosis>
  <tag v-else-if="item.type === 'tag'" @change="onChange" :item="item" :tagIds="value" :isRead="isRead"></tag>
  <customer-sources v-else-if="item.type === 'customerSource'" @change="onChange" :item="item" :value="value" :isRead="isRead"></customer-sources>
  <custom-corp-project v-else-if="item.type === 'corpProject'" @change="onChange" :item="item" :value="value" :isRead="isRead" />
  <upload-file v-else-if="item.type === 'files'" @change="onChange" :item="item" :value="value" :isRead="isRead"></upload-file>
  <upload-fly-file v-else-if="item.type === 'plyFile'" @change="onChange" :item="item" :value="value" :isRead="isRead"></upload-fly-file>
  <custom-bmi class="flex-grow" v-else-if="item.type === 'BMI'" @change="onChange" :form="form" :item="item" :isRead="isRead" />
  <self-select-mobile v-else-if="item.type === 'selectMobile'" @change="onChange" :form="form" :item="item" :isRead="isRead"></self-select-mobile>
  <run-time v-else-if="item.type === 'runTime'" @change="onChange" :form="form" :value="value" :item="item" :isRead="isRead"></run-time>
  <dcm-file v-else-if="item.type === 'dcmFile'" @change="onChange" :form="form" :value="value" :item="item" :isRead="isRead"></dcm-file>
  <div v-else-if="item.type === 'wwUser' && item.value">
    <ww-user :openid="item.value"></ww-user>
  </div>
  <div v-else-if="item.type === 'creator'">
    <div v-if="form.creator">
      <ww-user :openid="form.creator" type="userName"></ww-user>
      <el-text v-if="!form.creator.includes('自动建档')" size="default">（内部员工）</el-text>
    </div>
    <el-text v-else size="default">客户本人</el-text>
  </div>
  <custom-blood-pressure v-else-if="item.type === 'bloodPressure'" @change="onChange" :form="form" :value="value" :isRead="isRead" />

  <div v-else-if="item.operateType === 'onlyRead'">{{ value }}</div>
</template>
<script setup>
import selfSelectMobile from "./components/self-select-mobile.vue";
import selfMultipleDiseases from "./components/self-multiple-diseases.vue";
import selfDate from "./components/self-date.vue";
import selfInput from "./components/self-input.vue";
import selfInputAmount from "./components/self-input-amount.vue";
import selfSelect from "./components/self-select.vue";
import selfTextarea from "./components/self-textarea.vue";
import selfRadio from "./components/self-radio.vue";
import selfRegion from "./components/self-region.vue";
import reference from "./components/reference.vue";
import customerStage from "./components/customer-stage.vue";
import tag from "./components/tag.vue";
import customerSources from "./components/customer-sources.vue";
import selfMultipleOther from "./components/self-multiple-other.vue";
import customCorpProject from "./components/custom-corp-project.vue";
import customBmi from "./components/custom-bmi.vue";
import diagnosis from "./components/diagnosis.vue";
import WwUser from "@/components/ww-user/index.vue";
import uploadFile from "./components/upload-file.vue";
import referenceCount from "./components/reference-count.vue";
import customBloodPressure from "./components/custom-blood-pressure.vue";
import positiveFindTable from "./components/positive-find-table.vue";
import runTime from "./components/run-time.vue";
import selfSelectOther from "./components/self-select-other.vue";
import selfSelectImage from "./components/self-select-image.vue";
import uploadFlyFile from "./components/upload-fly-file.vue";
import dcmFile from "./components/dcm-file.vue";
import dayjs from "dayjs";

const $emit = defineEmits(["change"]);
const props = defineProps({
  form: {
    type: Object,
    default: () => ({}),
  },
  item: {
    type: Object,
    default: {},
  },
  value: {
    type: String,
    default: "",
  },
  isRead: {
    type: Boolean,
    default: false,
  },
});
function onChange(item) {
  $emit("change", item);
}

function isTimestamp(value) {
  const timestamp = parseFloat(value);
  return !isNaN(timestamp) && timestamp > 0 && timestamp < new Date().getTime() + 24 * 60 * 60 * 1000; // 未来一天内
}
</script>
<style lang="scss"></style>