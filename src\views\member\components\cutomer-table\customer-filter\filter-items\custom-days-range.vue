<template>
  <filter-item type="custom" label="更多筛选">
    <days-filter v-model:daysType="daysType" v-model:days="days" />
  </filter-item>
</template>
<script setup>
import { computed, ref } from 'vue';
import dayjs from 'dayjs';
import FilterItem from './filter-item.vue';
import daysFilter from './days-filter.vue';

const daysType = ref('');
const days = ref('');

function getParam() {
  const params = {}
  if (daysType.value === 'arrive' && parseInt(days.value) > 0) {
    params.startArriveTime = '';
    params.endArriveTime = dayjs().subtract(parseInt(days.value), 'days').format('YYYY-MM-DD')
  }
  if (daysType.value === 'service' && parseInt(days.value) > 0) {
    params.startServiceTime = '';
    params.endServiceTime = dayjs().subtract(parseInt(days.value), 'days').format('YYYY-MM-DD')
  }
  if (daysType.value === 'noArrive') {
    params.lastVisitTime = 'NO'
  }
  if (daysType.value === 'noService') {
    params.serviceTime = 'NO'
  }

  return params
}

function reset() {
  daysType.value = '';
  days.value = ''
}

defineExpose({
  getParam,
  reset
})
</script>
<style lang="scss" scoped></style>
