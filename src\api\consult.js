import { teamStore } from "@/store/team";
import { post } from "./axios";
async function useMember(data) {
  const res = await post("member", data, "MEMBER");
  return res;
}
export async function addConsultRecord(data) {
  const res = await useMember({ type: "addConsultRecord", ...data });
  return res;
}

export async function getConsultRecord(data) {
  const res = await useMember({ type: "getConsultRecord", createTeamId: teamStore().currentTeamId, ...data });
  return res;
}

export async function deleteConsultRecord(data) {
  const res = await useMember({ type: "deleteConsultRecord", ...data });
  return res;
}

export async function todayOrderExists(data) {
  const res = await useMember({ type: "todayOrderExists", ...data });
  return res;
}

export async function getPreConsultRecord(data) {
  const res = await useMember({ type: "getPreConsultRecord", ...data });
  return res;
}

export async function updateConsultRecord(data) {
  const res = await useMember({ type: "updateConsultRecord", ...data });
  return res;
}

export async function getConsultRecordCount(data) {
  const res = await useMember({ type: "getConsultRecordCount", ...data });
  return res;
}

export async function getConsultStageCount(data) {
  const res = await useMember({ type: "getConsultStageCount", ...data });
  return res;
}

export async function addEConsuleRecord(data) {
  const res = await useMember({ type: "addEConsuleRecord", ...data });
  return res;
}

export async function updateEConsuleRecord(data) {
  const res = await useMember({ type: "updateEConsuleRecord", ...data });
  return res;
}

export async function getEConsuleRecord(data) {
  const res = await useMember({ type: "getEConsuleRecord", ...data });
  return res;
}

export async function getFirstTriagePersonUserId(data) {
  const res = await useMember({ type: "getFirstTriagePersonUserId", ...data });
  return res;
}

export async function getFirstEConsuleRecord(data) {
  const res = await useMember({ type: "getFirstEConsuleRecord", ...data });
  return res;
}

export async function updateCustomerPersonResponsibles(data) {
  const res = await useMember({ type: "updateCustomerPersonResponsibles", ...data });
  return res;
}

export async function voidIssuedBill(data) {
  const res = await useMember({ type: "voidIssuedBill", ...data });
  return res;
}

export async function addConsultBillRecord(data) {
  const res = await useMember({ type: "addConsultBillRecord", ...data });
  return res;
}

export async function getAppointmentRecord(data) {
  const res = await useMember({ type: "getAppointmentRecord", ...data });
  return res;
}

export async function updateAppointmentRecord(data) {
  const res = await useMember({ type: "updateAppointmentRecord", ...data });
  return res;
}

export async function addAppointmentRecord(data) {
  const res = await useMember({ type: "addAppointmentRecord", ...data });
  return res;
}
export async function batchUpdateAppointmentRecordStatus(data) {
  const res = await useMember({ type: "batchUpdateAppointmentRecordStatus", ...data });
  return res;
}

export async function getProjectStatistics(data) {
  const res = await useMember({ type: "getProjectStatistics", ...data });
  return res;
}

export async function getConsultantSourceStatistics(data) {
  const res = await useMember({ type: "getConsultantSourceStatistics", ...data });
  return res;
}

export async function getProjectConsultStatistics(data) {
  const res = await useMember({ type: "getProjectConsultStatistics", ...data });
  return res;
}

export async function getDoctorDeductRecords(data) {
  const res = await useMember({ type: "getDoctorDeductRecords", ...data });
  return res;
}

export function getConsultationStats(data) {
  return request({
    url: '/api/consult/stats',
    method: 'post',
    data
  });
}



