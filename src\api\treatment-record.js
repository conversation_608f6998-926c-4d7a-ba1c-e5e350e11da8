import { post } from "./axios";
async function useMember(data) {
  const res = await post("member", data);
  return res;
}
export async function addTreatmentRecord(data) {
  const res = await useMember({ type: "addTreatmentRecord", ...data });
  return res;
}
export async function getTreatmentRecord(data) {
  const res = await useMember({ type: "getTreatmentRecord", ...data });
  return res;
}
export async function removeTreatmentRecord(data) {
  const res = await useMember({ type: "removeTreatmentRecord", ...data });
  return res;
}
export async function updateTreatmentRecord(data) {
  const res = await useMember({ type: "updateTreatmentRecord", ...data });
  return res;
}
export async function treatmentRecordStatistic(data) {
  const res = await useMember({ type: "treatmentRecordStatistic", ...data });
  return res;
}
export async function deductRecordStatistic(data) {
  const res = await useMember({ type: "deductRecordStatistic", ...data });
  return res;
}
export async function getDeductRecord(data) {
  const res = await useMember({ type: "getDeductRecord", ...data });
  return res;
}
export async function updateDeductRecord(data) {
  const res = await useMember({ type: "updateDeductRecord", ...data });
  return res;
}
export async function addDeductRecord(data) {
  const res = await useMember({ type: "addDeductRecord", ...data });
  return res;
}

export async function treatmentRecordStatisticByStatus(data) {
  const res = await useMember({ type: "treatmentRecordStatisticByStatus", ...data });
  return res;
}

export async function removeDeductRecord(data) {
  const res = await useMember({ type: "removeDeductRecord", ...data });
  return res;
}

export async function getYizhipaiCustomPhotos(data) {
  const res = await useMember({ type: "getYizhipaiCustomPhotos", ...data });
  return res;
}
