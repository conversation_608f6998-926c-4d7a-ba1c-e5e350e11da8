<template>
  <!-- 科室列表 -->
  <department-list 
    v-if="currentView === 'departments'"
    :customer="customer"
    :teams="teams"
    :unionId="unionId"
    @close="$emit('close')"
    @search="onSearch"
    @selectTertiary="onSelectTertiary"
  />
  
  <!-- 搜索页面 -->
  <search-page
    v-else-if="currentView === 'search'"
    :search-query="searchQuery"
    :customer="customer"
    :teams="teams"
    :unionId="unionId"
    @close="$emit('close')"
    @back="goBack"
    @select="onSelectItem"
  />
  
  <!-- 医生页面 -->
  <doctor-list
    v-else-if="currentView === 'doctors'"
    :department="selectedDepartment"
    :customer="customer"
    :teams="teams"
    :unionId="unionId"
    @close="$emit('close')"
    @back="goBack"
    @select="onSelectItem"
  />
</template>

<script setup>
import { ref } from 'vue'
import DepartmentList from './department-list.vue'
import SearchPage from './search-page.vue'
import DoctorList from './doctor-list.vue'
import { ElMessage } from 'element-plus'
import * as ww from "@/libs/@wecom/jssdk/wecom.esm.js"
import { addRecommendRecord } from '@/api/corp'
import { memberStore } from '@/store/member'

const props = defineProps({
  customer: { type: Object, default: () => ({}) },
  teams: { type: Array, default: () => [] },
  unionId: { type: String, default: '' }
})

const emits = defineEmits(['close', 'send'])

const currentView = ref('departments')
const selectedDepartment = ref(null)
const searchQuery = ref('')

// 选择三级科室（进入医生页面）
function onSelectTertiary(tertiaryDept) {
  selectedDepartment.value = tertiaryDept
  currentView.value = 'doctors'
}

// 搜索（进入搜索页面）
function onSearch(query) {
  searchQuery.value = query
  currentView.value = 'search'
}

// 选择项目（科室或医生）
async function onSelectItem(data) {

  if (data.type === 'doctor') {
    // 发送医生小程序链接
    await sendDoctorMiniProgramLink(data.data)
    emits('close')
  } else if (data.type === 'department') {
    // 选择科室，进入医生列表
    onSelectTertiary(data.data)
  }
}

// 发送医生小程序链接
async function sendDoctorMiniProgramLink(doctor) {
  try {
    // 导入生成小程序链接的API
    const { generateMiniProgramLink } = await import('@/api/wecom')

    // 构建小程序页面路径，包含医生工号等参数
    const miniProgramPath = `pages/tab_bar/hospHome/hospHome`

    // 调用API生成小程序链接 - 使用doctorNo优先
    const linkResult = await generateMiniProgramLink({
      path: miniProgramPath,
      hisDrId: doctor.workId, // workId 字段已经在doctor-list.vue中设置为优先使用doctorNo
      is_expire: false
    })
    if (linkResult.success && linkResult.data?.data?.openlink) {
      const sendLink = linkResult.data.data.openlink
      // 在发送消息之前先保存推荐记录
      try {
        await saveRecommendRecord(doctor, sendLink)
      } catch (error) {
        // 即使保存失败，也继续发送消息
      }

      // 直接使用 ww.sendChatMessage，参考问卷发送方式
      ww.sendChatMessage({
        msgtype: "text", // 消息类型，必填
        text: {
          content: sendLink, // H5消息页面url 必填
          // title: `${doctor.name} ${doctor.title}`, // H5消息标题
          // desc: `专长：${doctor.expertise || '暂无专长介绍'}`, // H5消息摘要
          // imgUrl: "https://796f-youcan-clouddev-1-8ewcqf31dbb2b5-1317294507.tcb.qcloud.la/other/19-%E9%97%AE%E5%8D%B7.png?sign=55a4cd77c418b2c548b65792a2cf6bce&t=1701328694" // 使用问卷的图片URL
        },
        success: function (res) {
          console.log('企业微信发送成功:', res)
          ElMessage.success('医生信息发送成功')
        },
        fail: function (res) {
          console.log('企业微信发送失败:', res)
          ElMessage.error('发送医生信息失败: ' + (res.errMsg || '未知错误'))
        }
      })
    } else {
      ElMessage.error('生成医生链接失败')
    }
  } catch (error) {
    console.error('发送医生小程序链接失败:', error)
    ElMessage.error('发送医生小程序链接失败，请稍后重试')
  }
}

// // 发送科室信息
// async function sendDepartmentInfo(department) {
//   try {
//     // 构建科室信息文本
//     const deptInfo = `科室：${department.name}\n${department.description || '暂无科室介绍'}`

//     // 发送文本消息
//     ww.sendChatMessage({
//       msgtype: "text",
//       enterChat: true,
//       text: {
//         content: deptInfo
//       }
//     })
//     ElMessage.success('科室信息发送成功')
//   } catch (error) {
//     console.error('发送科室信息失败:', error)
//     ElMessage.error('发送科室信息失败，请稍后重试')
//   }
// }

// 保存推荐记录
async function saveRecommendRecord(doctor, sendLink) {
  try {
    const { memberInfo } = memberStore()
    // 构建推荐记录数据
    const recordData = {
      creatorUserId: localStorage.getItem('userId'),
      creatorName: memberInfo.anotherName || memberInfo.name || '未知用户',
      hlwDeptId: doctor.department?.hlwDeptId || doctor.department?.deptId || doctor.department?.id || doctor.department?.parentId || '',
      hlwDeptName: doctor.department?.name || '',
      doctorId: doctor.originalData?.doctorNo || doctor.workId || '',
      doctorName: doctor.name || '',
      doctorTitle: doctor.title || '',
      doctorSpecialty: doctor.speciality || doctor.specialityFields ||'', // 使用 speciality 字段
      sendLink: sendLink
    }
    const result = await addRecommendRecord(recordData)
  } catch (error) {
    console.error('保存推荐记录异常:', error)
    throw error
  }
}

// 返回上一页
function goBack() {
  currentView.value = 'departments'
  selectedDepartment.value = null
  searchQuery.value = ''
}
</script>
