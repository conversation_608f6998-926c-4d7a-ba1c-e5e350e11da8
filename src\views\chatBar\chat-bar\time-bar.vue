<template>
  <div class="flex text-center py-10px bg-white divide-x divide-gray-500/50">
    <div class="w-1/3">
      <div class="text-12px mb-8px">建档时间</div>
      <div class="text-12px text-gray-500">{{ createTime }}</div>
    </div>
    <div class="w-1/3">
      <div class="text-12px mb-8px">最近服务时间</div>
      <div class="text-12px text-gray-500">{{ serviceTime }}</div>
    </div>
    <div class="w-1/3">
      <div class="text-12px mb-8px">最近到院日期</div>
      <div class="text-12px text-gray-500">{{ lastVisitTime }}</div>
    </div>
  </div>
</template>
<script setup>
import { computed, ref, watch } from "vue";
import dayjs from "dayjs";
import { getArriveTime } from "@/api/member";

const props = defineProps({
  customer: { type: Object, default: () => ({}) },
  teams: { type: Array, default: () => [] },
});
const createTime = computed(() => (props.customer && props.customer.createTime ? dayjs(props.customer.createTime).format("YYYY-MM-DD HH:mm") : "--"));
const serviceTime = computed(() => (props.customer && props.customer.serviceTime ? dayjs(props.customer.serviceTime).format("YYYY-MM-DD HH:mm") : "--"));
// const lastVisitTime = computed(() => {
//   const visitTimes = Array.isArray(props.customer.visitTimes) ? props.customer.visitTimes.filter(i => i && dayjs(i).isValid()).map(i => dayjs(i).valueOf()).sort((a, b) => b - a) : [];
//   const lastVisitTime = visitTimes[0];
//   return lastVisitTime ? dayjs(lastVisitTime).format("YYYY-MM-DD") : "--";
// });

const lastVisitTime = computed(() => {
  const inHospitalTimes = props.customer.inHospitalTimes;
  const lastTime = Array.isArray(inHospitalTimes) && inHospitalTimes.length > 0 ? inHospitalTimes[inHospitalTimes.length - 1] : "";
  return lastTime ? dayjs(lastTime).format("YYYY-MM-DD HH:mm") : "";
});
</script>
<style lang="scss" scoped></style>
