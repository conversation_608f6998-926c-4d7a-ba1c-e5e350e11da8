<template>
  <div :class="isExpand ? '':'h-40px overflow-hidden'">
    <div ref="boxRef" class="flex flex-wrap">
      <slot> </slot>
    </div>
  </div>
</template>
<script setup>
import { computed, ref, watch, onMounted } from "vue";
import { useElementBounding, useVModels } from "@vueuse/core";

const emits = defineEmits(['update:canExpand',])
const props = defineProps({
  canExpand: { type: Boolean, default: false },
  enableExpand: { typeof: Boolean, default: false },
  expand: { type: Boolean, default: false }
})

const boxRef = ref(null)
const { height } = useElementBounding(boxRef)
const { canExpand } = useVModels(props, emits);
const isExpand = computed(() => {
  return props.enableExpand && !props.expand ? false : true
})
const hasExpandArea = computed(() => height.value > 40)

onMounted(() => {
  canExpand.value = hasExpandArea.value
})

watch(hasExpandArea, val => {
  canExpand.value = val
}, { immediate: true })
</script>
<style lang="scss" scoped></style>
