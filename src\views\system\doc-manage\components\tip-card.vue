<template>
  <div class="tip-card text-14px  rounded-8px py-12px px-15px">
    <div class=" text-gray-500">提示：</div>
    <!-- <el-scrollbar wrap-style="max-height: 100px"> -->
    <div v-for="(txt, idx) in tips" :key="`${idx}_${txt}_tip`" class="break-all text-gray-700 mt-10px">
      {{ idx + 1 }}、{{ txt }}
    </div>
    <!-- </el-scrollbar> -->
  </div>
</template>
<script setup>
defineProps({
  tips: { type: Array, default: () => [] }
})
</script>

<style lang="scss" scoped>
.tip-card {
  background: #ecf4ff;
}
</style>
