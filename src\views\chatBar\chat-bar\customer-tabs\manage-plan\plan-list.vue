<template>
  <my-layout>
    <layout-main>
      <div v-if="list.length === 0" class="absolute inset-0 flex items-center justify-center">
        <empty-data :size="300" title="暂无回访计划" />
      </div>
      <div v-for="(plan, idx) in list" :key="idx" class="flex items-center py-12px px-15px border-b border-gray-100">
        <div class="mr-10px w-0 flex-grow">
          <div class="text-14px font-semibold truncate mb-6px">{{ plan.planName }}</div>
          <div class="text-14px truncate text-gray-400">应用范围：{{ plan.planDetail }}</div>
        </div>
        <div class="px-10px text-14px text-blue-500 leading-20px border-1 border-blue-500 rounded-10px" @click="select(plan)">选择</div>
      </div>
    </layout-main>
    <layout-item>
      <div class="p-15px flex bg-white shadow-up relative z-2" common-shadow--r>
        <el-button class="flex-grow" plain @click="cancel()">取消</el-button>
        <el-button class="flex-grow ml-15px" type="primary" @click="add()">新增回访计划</el-button>
      </div>
    </layout-item>
  </my-layout>
</template>
<script setup>
import { ref } from "vue";
import { storeToRefs } from "pinia";
import { memberStore } from "@/store/member";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import { managePlanStore } from "@/store/managePlan/managePlan.js";
import { taskStore } from "@/store/managePlan/task";
import { memberMangePlanStore } from "@/store/managePlan/memberMangePlan";
const { planForm, taskList } = storeToRefs(memberMangePlanStore());
const { getManagementPlan } = managePlanStore();
const { initPlan } = memberMangePlanStore();
const { operationType } = storeToRefs(taskStore());
const { list, pageSize, currentPage } = storeToRefs(managePlanStore());
pageSize.value = 1000;
currentPage.value = 1;
const { memberInfo } = storeToRefs(memberStore());
const emits = defineEmits(["cancel", "selectPlan", "addNewPlan"]);
const props = defineProps({
  team: { type: Object, default: () => ({}) },
});
getManagementPlan(props.team.teamId);
function cancel() {
  emits("cancel");
}
function add() {
  initPlan();
  emits("addNewPlan", "add");
}
function select(i) {
  planForm.value = {
    planName: i.planName,
    planId: i.planId,
  };
  taskList.value = JSON.parse(JSON.stringify(i.taskList));
  emits("selectPlan", "add");
}
</script>
<style lang="scss" scoped></style>
