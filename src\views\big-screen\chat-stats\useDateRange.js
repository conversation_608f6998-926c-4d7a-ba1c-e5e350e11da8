import { computed, onMounted, ref, watch } from 'vue'
import dayjs from 'dayjs'
import isoWeek from "dayjs/plugin/isoWeek";

dayjs.extend(isoWeek);

const rangekey = {
  all: () => [],
  today: date => [dayjs(date).format('YYYY-MM-DD'), dayjs(date).format('YYYY-MM-DD')],
  yesterday: date => [dayjs(date).subtract(1, 'day').format('YYYY-MM-DD'), dayjs(date).subtract(1, 'day').format('YYYY-MM-DD')],
  thisWeek: (date, untilToday) => {
    if (untilToday) {
      return [dayjs(date).startOf('isoWeek').format('YYYY-MM-DD'), dayjs(date).format('YYYY-MM-DD')]
    }
    return [dayjs(date).startOf('isoWeek').format('YYYY-MM-DD'), dayjs(date).endOf('isoWeek').format('YYYY-MM-DD')]
  },
  lastWeek: date => [dayjs(date).subtract(1, 'week').startOf('isoWeek').format('YYYY-MM-DD'), dayjs(date).subtract(1, 'week').endOf('isoWeek').format('YYYY-MM-DD')],
  thisMonth: (date, untilToday) => {
    if (untilToday) {
      return [dayjs(date).startOf('month').format('YYYY-MM-DD'), dayjs(date).format('YYYY-MM-DD')]
    }
    return [dayjs(date).startOf('month').format('YYYY-MM-DD'), dayjs(date).endOf('month').format('YYYY-MM-DD')]
  },
  lastMonth: date => [dayjs(date).subtract(1, 'month').startOf('month').format('YYYY-MM-DD'), dayjs(date).subtract(1, 'month').endOf('month').format('YYYY-MM-DD')],
  thisYear: (date, untilToday) => {
    if (untilToday) {
      return [dayjs(date).startOf('year').format('YYYY-MM-DD'), dayjs(date).format('YYYY-MM-DD')]
    }
    return [dayjs(date).startOf('year').format('YYYY-MM-DD'), dayjs(date).endOf('year').format('YYYY-MM-DD')]
  }
}
const rangeLabel = {
  all: '全部',
  today: '今天',
  yesterday: '昨天',
  thisWeek: '本周',
  lastWeek: '上周',
  thisMonth: '本月',
  lastMonth: '上月',
  thisYear: '本年'
}
const defaultItems = [
  { value: 'all', label: rangeLabel.all },
  { value: 'today', label: rangeLabel.today },
  { value: 'yesterday', label: rangeLabel.yesterday },
  { value: 'thisWeek', label: rangeLabel.thisWeek },
  { value: 'lastWeek', label: rangeLabel.lastWeek },
  { value: 'thisMonth', label: rangeLabel.thisMonth },
  { value: 'lastMonth', label: rangeLabel.lastMonth },
  { value: 'thisYear', label: rangeLabel.thisYear }
]
export default function useDateRange({ items = defaultItems, untilToday = false, callback }) {
  const current = ref('');
  const date = ref(dayjs().format('YYYY-MM-DD'));
  const dates = ref([]);
  const fn = typeof callback === 'function' ? callback : () => { };
  const itemList = Array.isArray(items) ? items.filter(i => i && (typeof i.value === 'number' || rangeLabel[i.value])) : [];
  const list = computed(() => itemList.map(i => {
    let dates = []
    if (typeof i.value === 'number' && i.value >= 0) {
      dates = [date.value, dayjs(date.value).add(i.value, 'day').format('YYYY-MM-DD')]
    } else if (typeof i.value === 'number' && i.value < 0) {
      dates = [dayjs(date.value).add(i.value, 'day').format('YYYY-MM-DD'), date.value]
    } else if (rangeLabel[i.value]) {
      dates = rangekey[i.value](date.value, untilToday)
    }
    return { value: i.value, label: i.label || rangeLabel[i.value || ''], dates }
  }).filter(i => i.dates.length))

  function change(value) {
    date.value = dayjs().format('YYYY-MM-DD');
    current.value = value;
    const item = list.value.find(i => i.value === value)
    dates.value = [...item.dates];
  }

  watch(dates, n => {
    if (Array.isArray(n) && n.length === 0) {
      current.value === 'all'
    } else if (Array.isArray(n) && n.length === 2) {
      const item = list.value.find(i => i.dates[0] === n[0] && i.dates[1] === n[1]);
      current.value = item ? item.value : ''
    } else {
      current.value = ''
    }
    fn(n)
  })

  onMounted(() => change(list.value[0].value))

  return {
    list,
    date,
    change,
    dates,
    current
  }
}