import { ElMessage } from 'element-plus';
import dayjs from 'dayjs';
import validate from "@/utils/validate";

const FormRule = {
  date: checkDate,
  input: checkInput,
  radio: checkListItem,
  select: checkListItem,
  textarea: checkInput,
}

const fixedRule = {
  mobile: checkMobile,
  idCard: checkIdCard,
  age: checkAge
}


export default function verify(items, rule = {}, data = {}) {
  const rules = { ...fixedRule, ...rule };
  for (let item of items) {
    const value = data[item.title];
    // 非自定义表单 数据验证
    if (item.operateType === 'formCell' && FormRule[item.type]) {
      const valid = FormRule[item.type](item, value);
      if (!valid) return false
    }
    // 自定义校验规则 检验表单
    // 自定义校验表单规则 必须是函数且返回结果只能是 错误信息或者 true
    if (typeof rules[item.title] === 'function') {
      const res = rules[item.title](value, item.name);
      if (typeof res === 'string') {
        ElMessage.info(res);
        return false
      } else if (res !== true) {
        console.error('自定义检验规则只能返回 字符串或者true')
      }
    }
  }
  return true
}

function checkDate({ name, required }, value) {
  if (isTrueValue(value)) {
    if (dayjs(value).isValid()) return true;
    ElMessage.info(`${name}格式不正确`)
    return false
  }
  if (required) {
    ElMessage.info(`请选择${name}`)
    return false
  }
  return true
}

function checkListItem({ range, name, required }, value) {
  if (isTrueValue(value)) {
    if (!Array.isArray(range) || !range.includes(value)) {
      ElMessage.info(`${name}所选值无效，请重新选择`)
      return false
    }
  } else {
    if (required) {
      ElMessage.info(`请选择${name}`)
      return false
    }
  }
  return true
}

function checkMobile(value, name) {
  if (isTrueValue(value)) {
    const res = validate.isMobile(value);
    if (res) return true;
    else return `${name}格式不正确`
  }
  return true
}

function checkIdCard(value, name) {
  if (isTrueValue(value)) {
    const [res, msg] = validate.isChinaId(value);
    if (res) return true;
    else return msg || `${name}格式不正确`
  }
  return true
}

function checkInput({ name, required, wordLimit, inputType }, value) {
  if (!required && !value) return true;
  if (required && !isTrueValue(value)) {
    ElMessage.info(`请输入${name}`)
    return false
  }
  if (wordLimit > 0 && inputType === 'text' && value.toString().length > wordLimit) {
    ElMessage.info(`${name}最多输入${wordLimit}个字符`)
    return false
  }
  return true
}

function checkAge(value, name) {
  if ((value === Number(value).toString()) || typeof value === 'number') {
    if (Number(value) % 1 !== 0) return `${name} 请输入整数`;
    if (Number(value) > 0 && Number(value) < 150) return true;
    return `请输入有效的${name}`
  } else if (isTrueValue(value)) {
    return `${name}格式不正确`
  }
}

function isTrueValue(value) {
  if (typeof value === 'string') return value.trim() !== '';
  if (typeof value === 'number') return true;
  return Boolean(value)
}

export function checkInteger(value, min, max) {
  if ((typeof value === 'string' && value.trim() === '') || (value !== 0 && !value)) return true;
  const number = Number(value);
  if (parseInt(value) !== number) return false
  if (number < min || number > max) return false
  return true
}

export function checkNumber(value, min, max) {
  if ((typeof value === 'string' && value.trim() === '') || (value !== 0 && !value)) return true;
  const number = parseFloat(value);
  if (number !== Number(value)) return false;
  if (checkDecimalPlaces(number) > 2) return '最多输入2位小数'
  if (number < min || number > max) return false;
  return true
}

function checkDecimalPlaces(num) {
  const str = num.toString();
  const decimalIndex = str.indexOf('.');
  return decimalIndex === -1 ? 0 : str.length - decimalIndex - 1;
}