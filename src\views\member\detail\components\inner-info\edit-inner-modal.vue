<template>
  <el-dialog :model-value="props.visible" title="内部信息编辑" :close-on-click-modal="false" :width="750" @close="onClose()">
    <el-scrollbar wrap-style="max-height:50vh">
      <el-form class="pt-20px pr-20px" :model="form" :label-width="labelWidth" label-suffix="：">
        <el-row>
          <el-col :span="24">
            <el-form-item label="卡号">
              <el-input v-model="form.cardNo" placeholder="请输入卡号" :maxlength="50" show-word-limit clearable />
            </el-form-item>
          </el-col>
          <el-col v-if="isShowCell('tagIds')" :span="24">
            <el-form-item label="标签">
              <div flex class="pt-6px">
                <customer-tag v-for="tagId in form.tagIds || []" :key="idx" :tag-id="tagId" />
                <el-button class="mb-5px" type="primary" size="small" :icon="Plus" plain @click="changeTag()">添加标签</el-button>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input type="textarea" placeholder="请填写备注信息" v-model="form.notes" :autosize="{ minRows: 6 }" />
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="isShowCell('intentedProject')">
            <el-form-item label="意向项目" prop="intentedProject">
              <el-select placeholder="请选择" v-model="form.intentedProject" style="width: 100%">
                <el-option v-for="item in intenteds" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="客户阶段">
              <el-select v-model="form.customerStage" class="w-full" placeholder="请选择客户阶段">
                <el-option v-for="opt in stageList" :key="opt.value" :label="opt.label" :value="opt.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="isShowCell('customerSource')">
            <el-form-item label="客户来源" prop="customerSource">
              <el-select v-model="form.customerSource" multiple class="w-full" placeholder="请选择客户来源">
                <el-option v-for="item in customerSources" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="isShowCell('reference')">
            <el-form-item label="推荐类型" prop="referenceType">
              <el-select v-model="form.referenceType" class="w-full" placeholder="请选择推荐类型" @change="referenceTypeChange">
                <el-option v-for="item in referenceType" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="isShowCell('reference') && form.referenceType === '同事推荐'">
            <el-form-item label="推荐人" prop="referenceUserId">
              <el-button @click="() => selectEnterpriseContact().then((res) => (form.referenceUserId = res))">选择推荐人</el-button>
              <ww-user v-if="form.referenceUserId" :openid="form.referenceUserId" type="userName"></ww-user>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="isShowCell('reference') && form.referenceType === '客户推荐'">
            <el-form-item label="推荐人" prop="referenceCustomerId">
              <remote-select-customer v-model="form.referenceCustomerId" class="w-full"></remote-select-customer>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="isShowCell('reference') && (form.referenceType === '朋友推荐' || form.referenceType === '其他推荐')">
            <el-form-item label="推荐人姓名" prop="reference">
              <el-input v-model="form.reference" placeholder="请输入推荐人姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="建档人员" prop="creator">
              <ww-user v-if="form.creator" :openid="form.creator" type="userName"></ww-user>
              <el-text v-if="form.creator && form.creator !== '系统自动建档'" size="default">（内部员工）</el-text>
              <el-text v-else size="default">{{ form.creator ? form.creator : "客户本人" }}</el-text>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="我的推荐" prop="creator">
              <span></span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="建档时间" prop="creator">
              <span>{{ createTime }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>

    <template #footer>
      <div class="text-center">
        <el-button class="w-100px" @click="onClose()">取消</el-button>
        <el-button class="w-100px" type="primary" @click="save">保存</el-button>
      </div>
    </template>
  </el-dialog>
  <disease-drawer ref="diseaseRef" @get-select-disease="onGetSelectDisease"></disease-drawer>
  <tag-drawer ref="tagDrawerRef" @get-select-tag="onTagChanged($event)"></tag-drawer>
</template>
<script setup>
import { computed, inject, ref, watch } from "vue";
import dayjs from "dayjs";
import { storeToRefs } from "pinia";
import { referenceType } from "@/baseData";
import { configStore } from "@/store/config";
import { selectEnterpriseContact } from "@/utils/jssdk";
import { templateStore } from "@/store/template";

import { getCustomerSources, getIntenteds } from "@/views/member/corpInfo";

import customerTag from "@/components/customer-tag/customer-tag.vue";
import diseaseDrawer from "@/views/member/components/diseaseDrawer/index.vue";
import RemoteSelectCustomer from "@/components/remote-select-customer/remote-select-customer";
import TagDrawer from "@/components/tag-drawer/index.vue";
const { isShowCell } = templateStore();
const customerSources = getCustomerSources();
const intenteds = getIntenteds();
const labelWidth = inject("labelWidth");
const props = defineProps({
  visible: { type: Boolean, default: false },
  customer: { type: Object, default: () => ({}) },
});
const { stageList } = storeToRefs(configStore());
const form = ref({});
async function initForm() {
  const data = { ...props.customer };
  form.value = data;
}
const createTime = computed(() => (form.value ? dayjs(form.value.createTime).format("YYYY-MM-DD HH:mm") : ""));
watch(
  () => props.visible,
  (n) => n && initForm()
);

const tagDrawerRef = ref();
function changeTag() {
  tagDrawerRef.value.openDialog(Array.isArray(form.value.tagIds) ? form.value.tagIds : []);
}
function onTagChanged(val) {
  form.value.tagIds = val;
}

const diseaseRef = ref();
function onGetSelectDisease(e) {
  form.value.pastMedicalHistory = e;
}

function referenceTypeChange() {
  form.value.referenceUserId = "";
  form.value.referenceCustomerId = "";
  form.value.reference = "";
}

const emits = defineEmits(["close", "update"]);
function onClose() {
  emits("close");
}
function save() {
  // const params = getFormData()
  emits("update", getFormData());
}

function getFormData() {
  const params = {
    cardNo: typeof form.value.cardNo === "string" ? form.value.cardNo : "",
    notes: typeof form.value.notes === "string" ? form.value.notes : "",
    customerStage: form.value.customerStage || "",
  };
  if (isShowCell("tagList")) params.tagIds = Array.isArray(form.value.tagIds) ? form.value.tagIds : [];
  if (isShowCell("intentedProject")) params.intentedProject = form.value.intentedProject || "";
  if (isShowCell("customerSource")) params.customerSource = form.value.customerSource || "";
  if (isShowCell("reference")) {
    params.referenceType = form.value.referenceType || "";
    params.referenceUserId = form.value.referenceUserId || "";
    params.referenceCustomerId = form.value.referenceCustomerId || "";
    params.reference = form.value.reference || "";
  }
  return params;
}

</script>
<style scoped>
.mb-5 {
  margin-bottom: 5px;
}

.append-select {
  width: 80px;
}

.title-bar {
  position: relative;
  padding-left: 10px;
  margin-bottom: 15px;
  font-size: 14px;
  font-weight: bold;
  color: var(--yc-main-text-color);
}

.mr-20 {
  margin-right: 20px;
}
</style>