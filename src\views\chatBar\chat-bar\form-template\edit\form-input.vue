<template>
  <el-form-item class="px-15px py-6px el-form-item--autoheight" :class="required ? 'is-required' : ''" :label="name"
    border-bottom>
    <el-input :maxlength="wordLimit" :model-value="value" :placeholder="'请输入' + name"
      @update:model-value="change($event)">
      <template v-if="appendText" #append>{{ appendText }}</template>
    </el-input>
  </el-form-item>
</template>
<script setup>
import { computed } from 'vue';

const emits = defineEmits(['change'])
const props = defineProps({
  appendText: { type: String, default: '' },
  form: { type: Object, default: () => ({}) },
  name: { type: String, default: '' },
  required: { type: Boolean, default: false },
  title: { type: String, default: '' },
  wordLimit: { type: Number, default: 20 },
})
const value = computed(() => props.form[props.title] || '');

function change(value) { emits('change', { title: props.title, value }) }

</script>
<style lang="scss" scoped></style>
