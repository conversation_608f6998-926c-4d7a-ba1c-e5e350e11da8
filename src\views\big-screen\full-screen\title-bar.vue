<template>
  <div class="text-15px">
    <span class="text-[#e5e5e5] font-bold truncate" :class="gapUnit === 'vh' ?'mx-[2vh]':'mx-[0.2rem]'">{{title}}</span>
  </div>
</template>
<script setup>
defineProps({
  gapUnit: {
    type: String,
    default: 'vh',
    validator(val) {
      return ['vh', 'rem'].includes(val)
    }
  },
  showIcon: {
    type: Boolean,
    default: true
  },
  title: {
    type: String,
    default: ''
  },

})
</script>
<style lang="scss" scoped></style>
