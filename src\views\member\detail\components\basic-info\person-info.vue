<template>
  <info-card title="基础信息">
    <template #head>
      <el-button class="edit-btn" type="primary" text plain @click="$emit('edit')">编辑</el-button>
    </template>
    <el-form :label-width="140" :size="formSize" label-position="left" label-suffix="：">
      <el-row>
        <el-col v-for="item in formatData" :key="item.key" :span="24">
          <el-form-item :label="item.label">
            <el-button v-if="item.key === 'archivesNo'" type="primary" size="small" text @click="copyNo()">复制</el-button>
            <el-text size="default" v-else>{{ item.value }}</el-text>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </info-card>
</template>
<script setup>
import { inject, computed } from "vue";
import { ElMessage } from 'element-plus'
import copy from 'copy-to-clipboard';
import dayjs from 'dayjs';
import { maskIdNo } from "@/utils"
import InfoCard from "../info-card";
import validate from '@/utils/validate';
import { memberStore } from "@/store/member";
const labelWidth = inject('labelWidth');
const formSize = inject('formSize');
const customer = inject('customer');

const colAttr = {
  common: { span: 24, md: 12, lg: 8 },
  name: { span: 24, md: 12, lg: 8 },
  address: { span: 24 }
}
const { corpInfo } = memberStore();
const {
  corpFileds: { baseInfo }
} = corpInfo;

let cols = Array.isArray(baseInfo) && baseInfo.map((item) => {
  if (item.title === 'mobile') {
    item.name = '预留手机号'
  }
  return {
    value: '',
    label: item.name,
    key: item.title
  }
})
let phone = [
  { value: "", label: "联系方式①", key: "phone1" },
  { value: "", label: "联系方式②", key: "phone2" },
  { value: "", label: "联系方式③", key: "phone3" }
]
cols = [...cols, ...phone];

// [
//   // { value: "", label: "档案编号：", key: "archivesNo" },
//   { value: "", label: "客户姓名：", key: "name" },
//   { value: "", label: "出生日期：", key: "birthday" },
//   { value: "", label: "性别：", key: "sex" },
//   { value: "", label: "年龄：", key: "age" },
//   { value: "", label: "证件类型：", key: "certType" },
//   { value: "", label: "证件号码：", key: "certNo" },
//   { value: "", label: "预留手机号：", key: "mobile" },
//   { value: "", label: "联系方式①：", key: "phone1" },
//   { value: "", label: "联系方式②：", key: "phone2" },
//   { value: "", label: "联系方式③：", key: "phone3" },
//   // { value: "", label: "详细地址：", key: "address" },
// ]
const formatData = computed(() => {
  const phones = Array.isArray(customer.value.assistantByMobile) ? customer.value.assistantByMobile.map(i => ({ ...i })) : [];
  return cols.map(i => {
    const item = { ...i };
    if (i.key === 'idCard') {
      item.value = maskIdNo(customer.value.idCard)
    } else if (i.key === 'certType') {
      item.value = validate.isChinaId(customer.value.idCard || '')[0] ? '身份证' : '';
    } else if (/^phone\d+$/.test(i.key)) {
      const phone = phones.shift();
      item.value = phone ? `${phone.mobile} ${phone.note}` : '';
    } else if (i.key === 'birthday') {
      item.value = customer.value[i.key] ? dayjs(customer.value[i.key]).format('YYYY-MM-DD') : ''
    } else if (i.key === 'address') {
      const region = Array.isArray(customer.value.region) ? customer.value.region.join('') : '';
      item.value = `${region}${customer.value.address || ''}`
    } else if (i.key === 'cardType') {
      const certType = customer.value.cardType ? customer.value.cardType : (customer.value.idCard ? '身份证' : '');
      item.value = "身份证";
    } else {
      item.value = customer.value[i.key] || i.value || ''
    }
    return item
  })
})

function copyNo() {
  copy(customer.value._id);
  ElMessage.success('已复制到剪切板')
}
</script>
<style scoped>
.edit-btn {
  float: right;
}
</style>