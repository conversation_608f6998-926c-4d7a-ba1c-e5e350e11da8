<template>
  <div flex-grow bg-fff common-shadow rounded-8 flex items-center justify-between class="p-15px">
    <div>
      <div flex items-center justify-between>
        <div class="flex" align-center>
          <div class="mr-10px" font-semibold font-16>{{ qrcodeInfo.qrCodeName }}</div>
          <div class="text-14px">({{ typeObj[qrcodeInfo.qrCodeStatus] }})</div>
          <el-button type="primary" size="small" text style="margin-left: 10px" @click="readDetial">查看详情</el-button>
        </div>
      </div>
      <div flex items-center pt-10 font-14>
        <div class="min-w-80px text-between">使用人员：</div>
        <div class="flex">
          <div v-for="(item, index) in qrcodeInfo.corpUserId" :key="item" class="flex">
            <span v-if="index != 0">、</span>
            <ww-user :openid="item"></ww-user>
          </div>
        </div>
      </div>
      <div flex items-center pt-10 font-14>
        <div class="min-w-80px text-between">客户标签：</div>
        <div v-if="qrcodeInfo.tagIds && qrcodeInfo.tagIds.length" class="mt-5px flex flex-wrap">
          <customer-tag v-for="tagId in qrcodeInfo.tagIds" :key="tagId" :tag-id="tagId" />
        </div>
      </div>
    </div>
    <div>
      <img class="h-50px w-50px mx-auto" src="@/assets/svg-icons/qrcode.svg" @click="openQrcode" />
      <div class="flex mt-10px">
        <el-button type="primary" size="small" text style="margin-left: 0" @click="downloadImage">下载二维码</el-button>
        <el-button type="primary" size="small" text style="margin-left: 0" @click="copyUrl">复制链接</el-button>
      </div>
    </div>
  </div>
  <qrcode-view :qrcodeVisible="imageVisible" :qrCodeInfo="qrcodeInfo" @close="imageVisible = false"></qrcode-view>
  <create-qrcode :memberQrcodeVisible="memberQrcodeVisible" @close="memberQrcodeVisible = false" operationType="read" :qrCodeInfo="qrcodeInfo"></create-qrcode>
</template>
<script setup>
import { computed, ref, inject } from "vue";
import WwUser from "@/components/ww-user/index.vue";
import customerTag from "@/components/customer-tag/customer-tag";
import qrcodeView from "../../components/qrcodeView.vue";
import { ElMessage } from "element-plus";
import createQrcode from "../../components/createQrcode.vue";
import axios from "axios";
const typeObj = {
  disable: "已停用",
  enable: "启用中",
};
const memberQrcodeVisible = ref(false);
const imageVisible = ref(false);
const props = defineProps({
  qrcodeInfo: { type: Object, default: () => {} },
});
function openQrcode() {
  imageVisible.value = true;
}
const downloadImage = async () => {
  const { qrCode, qrCodeName } = props.qrcodeInfo;
  try {
    const response = await axios.get(qrCode, { responseType: "blob" });
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", `${qrCodeName}.jpg`); // 设置下载的文件名
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error("There has been a problem with your fetch operation:", error);
  }
};

const copyUrl = () => {
  const { qrCode, qrCodeName } = props.qrcodeInfo;
  const input = document.createElement("input");
  document.body.appendChild(input);
  input.setAttribute("value", qrCode);
  input.select();
  document.execCommand("copy");
  document.body.removeChild(input);
  ElMessage.success("复制成功");
};

const readDetial = () => {
  memberQrcodeVisible.value = true;
};
</script>
<style scoped>
.text-between {
  text-align: justify;
  text-align-last: justify;
  /*兼容ie*/
  text-justify: distribute-all-lines;
}

.team {
  width: 100%;
  text-align: center;
  height: 30px;
  line-height: 30px;
  cursor: pointer;
}

.team:hover {
  color: #006eff;
}
</style>
