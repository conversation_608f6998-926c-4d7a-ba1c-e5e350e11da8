<template>
  <my-layout :radius="0" v-loading="loading">
    <layout-main>
      <div bg-fff common-shadow class="p-15px rounded-8px mb-10px">
        <div class="flex items-center" justify-between>
          <div class="flex items-center">
            <div class="mr-10px text-16px font-semibold">{{ sendSource }}【群发消息】</div>
            <div class="text-yellow-500 border border-yellow-500 rounded-4px px-10px py-5px text-14px">{{ executeStatusTitle }}</div>
          </div>
          <div class="flex text-14px">
            <div class="mr-10px">数据最近更新：{{ refreshTime }}</div>
            <div main-color class="pointer" @click="reFresh">刷新</div>
          </div>
        </div>

        <el-form label-suffix="：" :label-width="90" label-position="left">
          <el-row>
            <el-col :span="24">
              <el-form-item label="创建时间">
                <span>{{ groupmsg.createTime }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="任务时间">
                <div class="flex-grow">
                  <span>{{ groupmsg.taskTime }}</span>
                  <div class="ml-10px inline-flex items-center cursor-pointer" @click="show">
                    <span class="text-blue-500">任务详情</span>
                    <el-icon class="ml-5px text-blue-500">
                      <DArrowRight />
                    </el-icon>
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="overflow-hidden">
        <el-row :gutter="10">
          <el-col :md="12" :span="24">
            <div bg-fff common-shadow class="p-15px rounded-8px mb-10px">
              <div class="mb-10px text-16px font-semibold" active-title-bar>员工执行情况</div>
              <div class="flex justify-between">
                <div class="w-100px h-100px rounded-full text-white bg-blue-500 flex flex-col justify-center items-center">
                  <div class="text-20px mb-8px">{{ memberExecutefRate }}</div>
                  <div class="text-12px">完成率</div>
                </div>
                <div class="flex-shrink-0">
                  <div class="border-b border-dashed py-6px text-right pl-80px border-gray-200">
                    <span class="inline-block text-14px">预计执行员工：</span>
                    <span class="inline-block min-w-60px text-center">{{ memberExecuteCount }}</span>
                  </div>
                  <div class="py-6px text-right">
                    <span class="inline-block text-14px">已执行员工：</span>
                    <span class="inline-block min-w-60px text-center">{{ memberExecuteSuccessCount }}</span>
                  </div>
                  <div class="pb-6px text-right">
                    <span class="inline-block text-14px">未执行员工：</span>
                    <span class="inline-block min-w-60px text-center" style="color: red">{{ memberNoExecuteCount }}</span>
                  </div>
                  <div class="pb-6px text-right">
                    <span class="inline-block text-14px">无需执行员工：</span>
                    <span class="inline-block min-w-60px text-center" style="color: red">{{ noNeedExecuteList }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :md="12" :span="24">
            <div bg-fff common-shadow class="p-15px rounded-8px mb-10px">
              <div class="mb-10px text-16px font-semibold" active-title-bar>客户送达情况</div>
              <div class="flex justify-between">
                <div class="w-100px h-100px rounded-full text-white bg-blue-500 flex flex-col justify-center items-center">
                  <div class="text-20px mb-8px">{{ customerSendRate }}</div>
                  <div class="text-12px">送达率</div>
                </div>
                <div class="flex-shrink-0">
                  <div class="border-b border-dashed py-6px text-right pl-80px border-gray-200">
                    <span class="inline-block text-14px">预计发送客户：</span>
                    <span class="inline-block min-w-60px text-center">{{ sendCustomersCount }}</span>
                  </div>
                  <div class="py-6px text-right">
                    <span class="inline-block text-14px">成功发送客户：</span>
                    <span class="inline-block min-w-60px text-center">{{ customerSendSuccessCount }}</span>
                  </div>
                  <div class="pb-6px text-right">
                    <span class="inline-block text-14px">发送失败客户：</span>
                    <span class="inline-block min-w-60px text-center" style="color: red">{{ customerFailSuccessCount }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <data-table :memberSendResult="memberSendResult" :groupmsg="groupmsg" />
    </layout-main>
  </my-layout>
  <detail-modal :visible="visible" :width="width" @close="close" :groupmsg="groupmsg" @upload="uploadData" />
</template>
<script setup>
import useModal from "@/hooks/useModal";
import MyLayout, { LayoutMain } from "@/components/layout";
import { useRoute } from "vue-router";
import { ref, computed, onMounted, onUnmounted } from "vue";
import DataTable from "./data-table.vue";
import DetailModal from "./detail-modal.vue";
import dayjs from "dayjs";
import { getGroupmesgById, getTeamMemberExecuteState } from "../../api/groupmsg.js";
const { close, show, visible, width } = useModal(640); //  选择客户弹窗
const route = useRoute();
const id = route.params.id;
const loading = ref(false);
let memberSendResult = ref([]);

let refreshTime = ref("");
getData();
onMounted(() => {
  setInterval(() => {
    getData();
  }, 5 * 60 * 1000);
});

onUnmounted(() => {
  clearInterval();
});
function reFresh() {
  getData();
}
async function getData() {
  refreshTime.value = dayjs().format("YYYY-MM-DD HH:mm:ss");
  await getMesgDetial();
}
const templateType = {
  link: "网页",
  image: "图片",
  video: "视频",
};
const sendType = {
  MESSAGE: "群发消息",
  FRIEND: "群发朋友圈",
};
const groupmsg = ref({});
const sendSource = computed(() => {
  return groupmsg.value.sendSource === "TEAM" ? "团队" : "机构";
});

const memberExecuteCount = computed(() => {
  return memberSendResult.value.length;
});
const memberExecuteSuccessCount = computed(() => {
  return memberSendResult.value.filter((item) => item.unexecutedCount === 0 && item.customersCount > 0).length;
});

const noNeedExecuteList = computed(() => {
  return memberSendResult.value.filter((item) => item.unexecutedCount === 0 && item.customersCount === 0).length;
});

const memberNoExecuteCount = computed(() => {
  return memberSendResult.value.filter((item) => item.unexecutedCount > 0 && item.customersCount > 0).length;
});
const memberExecutefRate = computed(() => {
  if (memberExecuteCount.value === 0 || !memberExecuteCount.value) {
    return 0;
  }
  return (memberExecuteSuccessCount.value / memberExecuteCount.value).toFixed(2) * 100 + "%";
});
const customerSendSuccessCount = computed(() => {
  // 合并 memberSendResult.value 中的 sendSuccessList数组
  let sendSuccessList = memberSendResult.value.reduce((acc, item) => {
    return acc.concat(item.sendSuccessList);
  }, []);
  let sendSuccessIds = sendSuccessList.map((item) => item._id);
  // 数组去重
  return Array.from(new Set(sendSuccessIds)).length;
});
const customerFailSuccessCount = computed(() => {
  // 合并 memberSendResult.value 中的 sendSuccessList数组
  let sendFileList = memberSendResult.value.reduce((acc, item) => {
    return acc.concat(item.sendFileList);
  }, []);
  let ids = sendFileList.map((item) => item._id);
  // 数组去重
  return Array.from(new Set(ids)).length;
});

const sendCustomersCount = computed(() => {
  if (!groupmsg.value.customers) return 0;
  return groupmsg.value.customers.length;
});
const customerSendRate = computed(() => {
  if (sendCustomersCount.value === 0 || !sendCustomersCount.value) {
    return 0;
  }
  return (customerSendSuccessCount.value / sendCustomersCount.value).toFixed(2) * 100 + "%";
});

async function getExecuteState(id) {
  let { data } = await getTeamMemberExecuteState(id, groupmsg.value.customers);
  memberSendResult.value = data;
}
const statusList = [
  { label: "未开始", value: "notStart" },
  { label: "进行中", value: "doing" },
  { label: "已结束", value: "end" },
];
const executeStatusTitle = computed(() => {
  return statusList.find((source) => source.value === groupmsg.value.executeStatus)?.label;
});
async function getMesgDetial() {
  loading.value = true;
  let res = await getGroupmesgById(id);
  groupmsg.value = res.data;
  await getExecuteState(res.data._id);
  loading.value = false;
  // 根据开始时间和结束时间判断任务状态
  groupmsg.value.createTime = groupmsg.value.createTime ? dayjs(groupmsg.value.createTime).format("YYYY-MM-DD HH:mm") : "";
  groupmsg.value.taskTime = groupmsg.value.startTaskDate && groupmsg.value.endTaskDate ? `${dayjs(groupmsg.value.startTaskDate).format("YYYY-MM-DD")}至${dayjs(groupmsg.value.endTaskDate).format("YYYY-MM-DD")}` : "";
}

async function uploadData() {
  await getMesgDetial();
  visible.value = false;
}
</script>
<style scoped>
:deep(.el-form-item) {
  margin-bottom: 0;
}
</style>
