<template>
  <my-layout bg-fff common-shadow>
    <layout-main>
      <div flex items-center px-15 pt-15>
        <el-text pointer type="primary" @click="back()">返回</el-text>
        <el-text mx-6>|</el-text>
        <el-breadcrumb :separator-icon="ArrowRight">
          <el-breadcrumb-item :to="{ path: '/TEAM' }">团队管理</el-breadcrumb-item>
          <el-breadcrumb-item>{{ form.teamId ? "编辑团队" : "新增团队" }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <el-form :model="form" :label-width="120" label-position="right" label-suffix="：">
        <div p-15><span class="text-16px" active-title-bar>基础信息</span></div>
        <div mx-15 border-bottom>
          <el-form-item class="is-required" label="团队名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入团队名称" :maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="团队介绍" prop="teamTroduce">
            <el-input v-model="form.teamTroduce" type="textarea" :maxlength="3000"
              :autosize="{ minRows: 6, maxRows: 10 }" resize="none" placeholder="请输入团队介绍" show-word-limit></el-input>
          </el-form-item>
        </div>
        <div p-15><span class="text-16px" active-title-bar>成员信息</span></div>
        <div mx-15 border-bottom>
          <el-form-item class="is-required" label="团队成员" prop="teammate">
            <div flex-grow class="overflow-hidden">
              <div>
                <el-text pointer type="primary" class="mr-5px" @click="selectMember()">
                  {{ memberList.length ? "修改" : "添加" }}
                </el-text>
                <span color-666 font-14>选择团队成员后，可设置团队负责人、是否支持加好友</span>
              </div>
              <el-table stripe class="team-member-draggable-table" :data="memberList" :row-key="getRowKey">
                <el-table-column property="teammate" label="成员" min-width="130">
                  <template #default="{ row: userid }"><ww-user :openid="userid"></ww-user></template>
                </el-table-column>
                <el-table-column property="captain" label="团队角色" min-width="130">
                  <template #default="{ row: userid }">
                    <span>{{ leaders[userid] ? "团队负责人" : "" }}</span>
                  </template>
                </el-table-column>
                <el-table-column property="friend" label="支持客户加好友" min-width="130">
                  <template #default="{ row: userid }">
                    <el-checkbox :model-value="friendlyMember[userid]"
                      @update:model-value="changeFriend($event, userid)" />
                  </template>
                </el-table-column>
                <el-table-column property="action" label="操作" min-width="130">
                  <template #default="{ row: userid }">
                    <div flex items-center>
                      <el-text pointer type="primary" @click="removeMember(userid)">删除</el-text>
                      <span class="ml-8px" title="按住可进行排序">
                        <svg-icon class="team-member-drag-icon cursor-move" style="color: #999" size="16"
                          name="drag"></svg-icon>
                      </span>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-form-item>
        </div>
        <div p-15><span class="text-16px" active-title-bar>二维码</span></div>
        <div v-if="qrcodes.length" class="qrcode-list">
          <div v-for="item in qrcodes" :key="item.id" class="qrcode-item">
            <vue-qrcode class="pointer mx-auto" :value="item.qrcode" :options="{ width: 120, margin: 2 }"
              @click="showQrcode(item)"></vue-qrcode>
            <div class="qrcode-name">{{ item.name }}</div>
            <el-button class="qrcode-btn" type="primary" @click="editQrcode(item)">编辑</el-button>
          </div>
          <div class="add-qrcode-btn" @click="editQrcode()">
            <div>
              <el-icon>
                <Plus size="20" />
              </el-icon>
            </div>
            <div>新增二维码</div>
          </div>
        </div>
        <div v-else class="flex flex-col items-center justify-center py-30px">
          <div class="text-14px leading-5">
            创建团队二维码，可下载打印制作物料，客户扫码后查看团队信息，
            <br />
            完成初始建档。并且可以选择医生/医助加为好友。
          </div>
          <el-button class="mt-20px" type="primary" @click="editQrcode()">创建二维码</el-button>
        </div>
      </el-form>
    </layout-main>
    <layout-item>
      <div text-center p-15 common-shadow>
        <el-button :loading="loading" class="w-100px" type="primary" @click="save">保存</el-button>
      </div>
    </layout-item>
  </my-layout>
  <select-member-vue ref="selectMemberRef" @getSelectItems="onGetSelectItems"></select-member-vue>
  <qrcode-modal :data="qrcodeData" :team-id="teamId" :visible="visible" :width="width" @close="close"
    @confirm="changeQrcode($event)" @remove="remove($event)" />
  <single-qrcode-modal :data="currentQrcode" :width="qrcodeWidth" :visible="qrcodeVisible" @close="closeQrcode" />
</template>
<script setup>
import { ref, onMounted, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import Sortable from "sortablejs";
import { getTeamById, updateTeam, updateMemberRolesByTeam } from "@/api/corp";
import { tagsStore } from "@/store/tags";
import { teamStore } from "@/store/team";
import useModal from "@/hooks/useModal";
import { downloadQrcode, useCopy, getRandomStr } from "@/utils";
import { Plus, ArrowRight } from "@element-plus/icons-vue";
import VueQrcode from "@chenfengyuan/vue-qrcode";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import selectMemberVue from "@/components/select-member/index.vue";
import WwUser from "@/components/ww-user/index.vue";
import SvgIcon from "@/components/svg-icon";
import QrcodeModal from "./components/qrcode-modal.vue";
import singleQrcodeModal from "./components/single-qrcode-modal.vue";

const route = useRoute();
const router = useRouter();
const form = ref({});
const qrcodes = ref([]);
const friendlyMember = ref({});
const loading = ref(false)

const memberList = ref([]);
const memberLeaderList = ref([]);
let oldMemberList = [];
let oldmemberLeaderList = [];

const leaders = computed(() => memberLeaderList.value.reduce((m, userid) => ((m[userid] = true), m), {}));
const teamId = ref("");
onMounted(() => {
  if (route.params.teamId) getTeam(route.params.teamId);
  teamId.value = route.params.teamId || getRandomStr();
  rowDrop();
});
async function getTeam(teamId) {
  const { success, data = {}, message } = await getTeamById(teamId);
  if (success && data.data && data.data.length) {
    const {
      data: [team],
    } = data;
    form.value = team;
    qrcodes.value = Array.isArray(team.qrcodes) ? team.qrcodes : [];
    memberList.value = Array.isArray(team.memberList) ? team.memberList.filter(Boolean) : [];
    memberLeaderList.value = Array.isArray(team.memberLeaderList) ? team.memberLeaderList : [];
    oldMemberList = memberList.value;
    oldmemberLeaderList = memberLeaderList.value;
    friendlyMember.value = Array.isArray(team.friendlyMembers)
      ? team.friendlyMembers.reduce((val, userid) => {
        val[userid] = true;
        return val;
      }, {})
      : {};
  } else if (success) {
    ElMessage.error("未查询到团队信息");
  } else {
    ElMessage.error(message);
  }
}
function changeFriend(val, userid) {
  friendlyMember.value[userid] = val;
}
function removeMember(userid) {
  memberList.value = memberList.value.filter((i) => i !== userid);
  memberLeaderList.value = memberLeaderList.value.filter((i) => i !== userid);
  friendlyMember.value[userid] = false;
}

const selectMemberRef = ref("");
async function selectMember() {
  const leaders = Array.isArray(form.value.memberLeaderList) ? form.value.memberLeaderList : [];
  selectMemberRef.value.openDialog(memberList.value, leaders);
}
function onGetSelectItems({ leaders, checkedMemberList }) {
  form.value.memberLeaderList = leaders;
  memberLeaderList.value = leaders;
  memberList.value = checkedMemberList;
}
async function changedTeamMember() {
  const addLeaders = form.value.memberLeaderList.filter((i) => !oldmemberLeaderList.includes(i));
  const removeLeaders = oldmemberLeaderList.filter((i) => !form.value.memberLeaderList.includes(i));
  const addTeamMember = memberList.value.filter((i) => !oldMemberList.includes(i)).filter((i) => !addLeaders.includes(i));
  const removeTeamMember = oldMemberList.filter((i) => !memberList.value.includes(i));
  const params = {
    addLeaders,
    removeLeaders,
    addTeamMember,
    removeTeamMember,
  };
  await updateMemberRolesByTeam(params, form.value.teamId);
}
const { removeTag: removeRouteTag } = tagsStore();
function verify() {
  let message = "";
  if (typeof form.value.name !== "string" || form.value.name.trim() === "") {
    message = "请输入团队名称";
  } else if (!Array.isArray(form.value.memberLeaderList) || form.value.memberLeaderList.length === 0) {
    message = "请选择团队负责人";
  }
  message && ElMessage.info(message);
  return !message;
}
async function save() {
  if (!verify() || loading.value) return;
  loading.value = true;
  const params = {
    corpId: localStorage.getItem("corpId"),
    ...form.value,
    teamId: form.value.teamId || teamId.value,
    qrcodes: qrcodes.value,
    friendlyMembers: memberList.value.filter((i) => friendlyMember.value[i]),
    memberList: memberList.value,
  };
  const { success, message } = await updateTeam(params);
  if (success) {
    await teamStore().getTeams();
    await teamStore().getAllTeams();
    await changedTeamMember();
    ElMessage.success(message);
    removeRouteTag(route.fullPath);
  } else {
    ElMessage.error(message);
  }
  loading.value = false;
}
function back() {
  router.push({ name: "TEAM" });
  removeRouteTag(route.fullPath);
}
const currentQrcode = ref({});
const { close: closeQrcode, show: displayQrcode, visible: qrcodeVisible, width: qrcodeWidth } = useModal(480);
function showQrcode(item) {
  currentQrcode.value = { ...item, teamName: form.value.name, teamId: form.value.teamId || teamId.value };
  displayQrcode();
}
const { close, show, visible, width } = useModal(640);
const qrcodeData = ref({});
function editQrcode(item = {}) {
  qrcodeData.value = { ...item };
  show();
}
function changeQrcode(qrcode) {
  const index = qrcodes.value.findIndex((i) => i.id === qrcode.id);
  if (index >= 0) {
    qrcodes.value.splice(index, 1, qrcode);
  } else {
    qrcodes.value.push(qrcode);
  }
  close();
}

function remove(id) {
  const index = qrcodes.value.findIndex((i) => i.id === id);
  if (index >= 0) {
    qrcodes.value.splice(index, 1);
  }
}

function rowDrop() {
  const tbody = document.querySelector(".team-member-draggable-table .el-table__body-wrapper tbody");
  Sortable.create(tbody, {
    draggable: ".team-member-draggable-table .el-table__row",
    handle: ".team-member-drag-icon",
    onEnd({ newIndex, oldIndex }) {
      const list = [...memberList.value];
      const [moveItem] = list.splice(oldIndex, 1);
      list.splice(newIndex, 0, moveItem);
      memberList.value = list;
    },
  });
}

function getRowKey(row) {
  return row;
}
</script>

<style scoped lang="scss">
.qrcode-list {
  display: flex;
  flex-wrap: wrap;
  text-align: center;
  padding: 0 15px 10px 100px;
}

.qrcode-item {
  margin-right: 15px;
  padding: 0 10px 10px;
}

.qrcode-btn {
  margin-top: 10px;
  width: 100%;
}

.qrcode-btn--add {
  width: 100px;
  height: 100px;
}

.model-qrcode {
  margin: 0 auto;
}

.modal-qrcode-name {
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 600;
  color: var(--yc-main-text-color);
}

.add-qrcode-btn {
  margin-top: 10px;
  margin-left: 10px;
  width: 100px;
  height: 100px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: #006eff;
  border: 1px solid #006eff;
  text-align: center;
  border-radius: 8px;
  cursor: pointer;
  box-sizing: border-box;
}

.friend-area {
  margin-left: 100px;
  width: auto;
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 8px;
}

[pt-15] {
  padding-top: 15px;
}

[mx-6] {
  margin: 0 6px;
}

.template {
  .b_n {
    border-radius: 5px;
    padding: 0 10px;
    background: rgb(244, 244, 255);
    margin-right: 10px;
  }

  .text {
    color: #006eff;
  }
}
</style>
