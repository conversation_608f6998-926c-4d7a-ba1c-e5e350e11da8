<template>
  <el-dialog :model-value="visible" title="获客拉新-员工活码" :width="560" @close="close()">
    <el-scrollbar wrap-style="max-height: calc(100vh - 360px)">
      <div v-for="(item, idx) in list" :key="idx" class="flex mt-15px px-15px">
        <div
          class="flex-shrink-0 flex items-center justify-center w-45px h-45px mr-15px text-white text-20px font-semibold linear-blue rounded-4px">
          {{ idx + 1 }}
        </div>
        <div class="flex-grow break-all">
          <div class="font-semibold text-15px leading-24px">{{ item.title }}</div>
          <div class="mt-4px text-14px leading-20px text-zinc-500">{{ item.desc }}</div>
        </div>
      </div>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button class="w-100px" type="primary" plain @click="close">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(["close"]);
const list = [
  { title: "意向客户转化建档", desc: "为一名或者多名员工创建一个智能二维码，客户扫码添加员工自动成为好友，并且自动发送建档链接，实现客户建档。" },
  { title: "下载活码图片，衔接物料或线上渠道", desc: "可下载活码图片或者链接，为员工批量制作物料、或配置在微信公众号等线上渠道。" },
  { title: "灵活添加好友，轻松区分客户", desc: "客户添加时，无需经过确认自动成为好友，并可向客户自动推送个性化的欢迎语，以及自动打上客户来源标签，做好客户分类。" },
  { title: "实时数据看板", desc: "管理员/负责人可以实时查看添加的客户数据，了解增长情况，从而优化投放策略。" },
];
function close() {
  emit("close");
}
</script>
<style lang="scss" scoped>
.linear-blue {
  background: linear-gradient(180deg, #418ff5 0%, #6cc0e8 100%);
}
</style>