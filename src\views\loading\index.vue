<template>
  <div class="loading">
    <div>
      <img src="@/assets/round-loading.png" alt="" class="loadingIcon" />
      <div>加载中...</div>
    </div>
  </div>
</template>
<script setup></script>
<style scoped lang="scss">
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  .loadingIcon {
    width: 60px;
    height: 60px;
    animation: spin 3s linear infinite;
  }
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}
</style>
