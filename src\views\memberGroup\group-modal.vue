<template>
  <el-dialog :model-value="props.visible" :title="props.group._id ? '编辑分组' : '新增分组'" :width="520" @close="close">
    <div p-15>
      <el-form :label-width="100">
        <el-form-item label="分组名称：" class="is-required" prop="groupName">
          <el-input v-model="groupName" placeholder="请输入分组名称"></el-input>
        </el-form-item>
        <el-form-item label="使用说明：">
          <el-input v-model="description" maxlength="100" :autosize="{ minRows: 5, maxRows: 8 }" resize="none" show-word-limit type="textarea" placeholder="请输入使用说明"></el-input>
        </el-form-item>
      </el-form>
      <div class="mt-15px flex items-center text-28rpx">
        <div class="color-666 flex-shrink-0">回访计划名称:</div>
        <div v-if="managementPlan.planId" class="w-0 flex-grow flex items-center ml-15px">
          <div>{{ managementPlan.planName }}</div>
          <span class="ml-4px" color-danger pointer>
            <el-icon @click="removePlan">
              <DeleteFilled />
            </el-icon>
          </span>
        </div>
        <div v-else flex items-center color-primary pointer class="ml-15px">
          <el-icon>
            <Plus />
          </el-icon>
          <div class="ml-4px" @click="addPlan()">添加</div>
        </div>
      </div>
    </div>
    <template #footer>
      <div text-center>
        <el-button class="w-100px" @click="close()">取消</el-button>
        <el-button class="w-100px" type="primary" @click="save()">保存</el-button>
      </div>
    </template>
  </el-dialog>
  <select-mamagement-plan :visible="planVisible" @close="planVisible = false" @success="onSuccess" :planId="managementPlan.planId"></select-mamagement-plan>
</template>
<script setup>
import { ref, watch, reactive } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import selectMamagementPlan from "@/views/member/detail/components/select-management-plan";
const props = defineProps({
  visible: { type: Boolean, default: false },
  group: { type: Object, default: () => ({}) },
});
const planVisible = ref(false);
const managementPlan = ref({});
const groupName = ref("");
const description = ref("");
const emits = defineEmits(["close", "saveSuccess"]);
function close() {
  emits("close");
}
function save() {
  if (!groupName.value) {
    ElMessage.warning("未输入分组名称");
    return;
  }
  if (groupName.value.length > 20) {
    ElMessage.warning("分组名称不得超过20个字");
    return;
  }
  const { planId = "", planName = "", taskList = [] } = managementPlan.value;
  const item = {
    group: props.group,
    groupName: groupName.value,
    description: description.value,
    managementPlan: {
      planId,
      planName,
      taskList,
    },
  };
  emits("saveSuccess", item);
}
function addPlan() {
  planVisible.value = true;
}
function onSuccess(item) {
  managementPlan.value = item;
  planVisible.value = false;
}
async function removePlan(item) {
  // await ElMessageBox.confirm(`确定解绑该回访计划？`, "提示", { type: 'warning' });
  managementPlan.value = {
    planId: "",
    planName: "",
    taskList: [],
  };
}
watch(
  () => props.visible,
  (n) => {
    if (n) {
      managementPlan.value = props.group && props.group.managementPlan ? props.group.managementPlan : {};
      groupName.value = props.group && props.group.groupName ? props.group.groupName : "";
      description.value = props.group && props.group.description ? props.group.description : "";
    }
  }
);
</script>

