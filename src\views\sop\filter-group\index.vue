<template>
  <div class="pl-40px">
    <div ref="ruleRef"></div>
    <div class="flex" :class="list.length ? 'py-5px mb-10px' : ''">
      <div class="w-40px relative" :class="list.length > 1 ? '' : 'opacity-0'">
        <div class="absolute left-1/2 top-0 bottom-0 w-2px bg-gray-300 transform -translate-x-1/2"></div>
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-blue-500 text-12px p-4px text-white rounded">且</div>
      </div>
      <div class="flex-grow text-14px">
        <div v-for="(i,idx) in list" :key="i.key">
          <filter-case v-model="i.value" v-model:condition="i.condition" v-model:conditionType="conditionType"
            v-model:isDayFilter="i.isDayFilter" :case-id="i.key" :day-filters="dayFilters" :config="i.config" :name="i.name"
            :theme="i.theme" :conditions="conditions" @remove="remove(idx)" />
        </div>
      </div>
    </div>
    <el-popover v-model:visible="visible" placement="bottom-start" :width="300" trigger="#">
      <template #reference>
        <el-button class="block w-200px" type="primary" :icon="Plus" @click="visible = true">添加筛选项</el-button>
      </template>
      <el-scrollbar max-height="40vh">
        <div v-for="i in conditions" :key="i.value" class="p-10px rounded cursor-pointer hover:bg-blue-100 hover:text-blue-500" @click="handleClick(i)">
          {{ i.name }}
        </div>
      </el-scrollbar>
    </el-popover>
  </div>
  <el-form v-if="hasDayFilter" class="mt-10px px-15px" label-suffix="：" label-position="right" :label-width="140">
    <el-form-item class="is-required mb-0" label="SOP触发限制">
      <el-radio-group v-model="sop.triggerLimit">
        <el-radio :label="false" :value="false">无限制</el-radio>
        <el-radio :label="true" :value="true">
          <div class="flex-grow items-center text-gray-500">
            <span>同一客户需间隔</span>
            <el-input-number v-model="sop.triggerInterval" class="mx-5px" :min="1" :max="1000" controls-position="right" />
            <span>天以上再次执行</span>
          </div>
        </el-radio>
      </el-radio-group>
    </el-form-item>
  </el-form>
</template>
<script setup>
import { computed, provide, ref, watch } from "vue";
import { useVModel } from "@vueuse/core";
import { useElementBounding } from "@vueuse/core";
import { ElMessageBox } from "element-plus";
import filterCase from "./filter-case/index.vue";

const emit = defineEmits(["update:modelValue"]);
const props = defineProps({
  conditions: { type: Array, default: [] },
  modelValue: {
    type: Object,
    default: () => ({}),
  },
});

const ruleRef = ref();
const { width } = useElementBounding(ruleRef);
const rightWidth = computed(() => width.value - 50);
const sop = useVModel(props, "modelValue", emit);
const visible = ref(false);

const list = computed({
  get() { return sop.value && Array.isArray(sop.value.filterConditions) ? sop.value.filterConditions : [] },
  set(val) { sop.value.filterConditions = val }
})
const dayFilters = computed(() => list.value.filter(i => i.isDayFilter));
const hasDayFilter = computed(() => dayFilters.value > 0)

provide("filter-group-right-width", rightWidth);

function handleClick(i) {
  visible.value = false;
  const item = {
    key: `${Date.now()}_${Math.ceil(Math.random() * 10000)}`,
    theme: i.value, // 主题
    condition: "", // 筛选条件
    value: {}, // 条件值
  };
  list.value = [...list.value, item];
}

async function remove(idx) {
  await ElMessageBox.confirm("确定要删除吗？", "提示", { type: "warning" });
  list.value = list.value.filter((_, index) => index !== idx);
}

let canWatch = false;
watch(hasDayFilter, (n, o) => {
  if (n && !o) {
    if (sop.value._id && !canWatch) {
      canWatch = true;
      return;
    }
    sop.value.triggerLimit = false;
    sop.value.triggerInterval = 1;
  }
});
</script>
