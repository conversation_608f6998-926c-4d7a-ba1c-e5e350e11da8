import { computed, ref } from 'vue';
import { useWindowSize } from '@vueuse/core';

export default function useModal(modalWidth = 520) {
  const { width: winWidth } = useWindowSize();
  const visible = ref(false);
  const width = computed(() => Math.min(modalWidth, winWidth.value - 40))
  function close() {
    visible.value = false
  }

  function show() {
    visible.value = true
  }

  return { close, show, visible, width }
}