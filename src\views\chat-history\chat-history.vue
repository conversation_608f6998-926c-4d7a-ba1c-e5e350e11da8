<template>
  <my-layout>
    <template v-if="enableChatRecord">
      <layout-item>
        <div class="py-12px px-15px bg-white rounded mb-10px">
          <el-input v-model="keyword" ref="inputRef" class="mr-15px w-1/2" placeholder="搜索" @blur="searchBlur" />
          <el-button type="primary" @click="search">搜索</el-button>
          <el-button v-if="keyword" @click="clear()">取消</el-button>
        </div>
      </layout-item>
      <Layout-main v-if="showSearchResult" :scroll="false">
        <search-result @readChat="readChat" />
      </Layout-main>
      <Layout-main v-else :scroll="false">
        <div class="flex h-full">
          <chat-member :list="list" :member="currentMember" class="flex-shrink-0 mr-10px" @adjust="showAuthModal('adjust')" @change="changeMember($event)" />
          <chat-customer :customer="currentCustomer" :member="currentMember" @change="changeCustomer($event)" />
          <chat-window class="w-0 flex-grow border-l border-gray-200" :customer="currentCustomer" :member="currentMember" />
        </div>
      </Layout-main>
    </template>
    <Layout-main v-else :scroll="false">
      <div class="flex flex-col justify-center items-center h-full">
        <empty-data :top="0" :title="text" padding="0 0 15px" text-top="10px" :image-width="200"></empty-data>
        <!-- <el-button type="primary" @click="showAuthModal()">去授权启用</el-button> -->
      </div>
    </Layout-main>
  </my-layout>
  <auth-modal :type="authType" :visible="visible" :width="width" @close="close" @authed="handleAuth" />
</template>
<script setup>
import { onMounted, ref } from "vue";
import useModal from "@/hooks/useModal";
import authModal from "./auth-modal.vue";
import chatCustomer from "./chat-customer.vue";
import chatMember from "./chat-member.vue";
import chatWindow from "./chat-window.vue";
import EmptyData from "@/components/empty-data.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import searchResult from "./search-result.vue";
import { getUserAuthList, getCorpSessionArchiveInfo, selectdUserId, enableChatRecord } from "./api/authMember.js";
import { selectedCustomer } from "./api/chatCustomer.js";
import { searchChatRecord, searchChatRecordList, keyword, search, showSearchResult } from "./api/chatSearch.js";
// // 3. 生命周期钩子
onMounted(async () => {
  // initOpenMessage();
  getUserAuthList();
  getCorpSessionArchiveInfo();
});
// 1. 声明响应式变量
const inputRef = ref();
const currentMember = ref();
const currentCustomer = ref();
const authType = ref("auth");
const { close, show, visible, width } = useModal(560); // 选择客户弹窗

const text = ref("未启用会话存档功能，请先启用");

// 2. 定义方法和函数
function changeCustomer(customer) {
  currentCustomer.value = customer;
}

function changeMember(member) {
  currentMember.value = member;
}

function clear() {
  inputRef.value.blur();
  showSearchResult.value = false;
  keyword.value = "";
  searchChatRecordList.value = [];
}
function searchBlur() {
  if (!keyword.value) clear();
}
function readChat(item) {
  inputRef.value.blur();
  showSearchResult.value = false;
  keyword.value = "";
  searchChatRecordList.value = [];
  selectdUserId.value = item.memberUserId;
  // selectedCustomer.value = {
  //   memberUserId: item.memberUserId,
  //   customerUserId: item.customerUserId,
  // };
}

// function showAuthModal(type = "auth") {
//   authType.value = type;
//   show();
// }

function handleAuth() {
  close();
}
</script>

<style lang="scss" scoped></style>

