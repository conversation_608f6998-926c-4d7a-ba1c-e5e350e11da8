<template>
  <el-dialog :close-on-click-modal="false" :model-value="visible" title="批量删除" :width="width" @close="close">
    <div class="p-15px text-center">
      <div class="text-15px font-semibold mb-15px text-dark-500">确定删除客户吗？</div>
      <div class="text-14px text-gray-500">客户删除后，该客户关联的所有数据将全部删除。</div>
    </div>
    <template #footer>
      <div text-center>
        <el-button type="primary" plain class="w-100px" @click="close()">取消</el-button>
        <el-button :loading="loading" class="w-100px" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { batchUpdateToDoAndManagePlan } from "@/api/todo";
import { batchRemoveMember } from "@/api/member";
import { ElMessage } from "element-plus";
import { ref } from "vue";

const emits = defineEmits(["close", "change"]);
const props = defineProps({
  customers: { type: Object, default: () => ({}) },
  visible: { type: Boolean, default: false },
  width: { type: Number },
});
function close() {
  emits("close");
}
const loading = ref(false);
async function confirm() {
  loading.value = true;
  const ids = props.customers.map((item) => item._id);
  const { success, message } = await batchRemoveMember(ids);
  if (!success) {
    ElMessage.error(message);
  }
  // 更新待办和管理计划
  await batchUpdateToDoAndManagePlan({ customerIds: ids, operationType: "closed" });
  loading.value = false;
  emits("change");
}
</script>