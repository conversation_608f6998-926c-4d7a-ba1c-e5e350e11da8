<template>
  <my-layout bg-fff common-shadow>
    <layout-item>
      <div px-15 py-10 flex items-center border-bottom>
        <el-button flex-shrink-0 class="mr-10px w-100px" plain type="primary" @click="addTag()">新增</el-button>
        <span class="min-w-300px" font-14 grew-color>客户标签由管理员或一级管理规则的负责人配置。配置后，机构成员可以对客户打客户标签。</span>
      </div>
    </layout-item>
    <layout-main :scroll="true">
      <div v-if="tags.length > 0" px-15>
        <div v-for="(item, index) in tags" :key="item.groupId" flex items-center py-10 border-bottom>
          <div class="tag-cell-item w-0 mr-10px" flex-grow font-14>
            <div class="label font-14 w-160px flex-shrink-0">
              {{ item.groupName }}
              <span class="pl-5px text-12px" main-color v-if="item.createType == 'corpAsync'">(企微标签)</span>
            </div>
            <div class="options w-0 flex-grow flex-wrap">
              <div class="item" v-for="item2 in item.tag" font-12 :key="item2.id">
                {{ item2.name }}
              </div>
            </div>
          </div>
          <div flex-shrink-0 class="right">
            <el-text class="mr-10px" type="primary" pointer @click="editTag(item)" v-if="item.createType !== 'corpAsync'">编辑</el-text>
          </div>
        </div>
      </div>
      <empty-data v-else :top="80" title="暂无标签" padding="0 0 15px" text-top="10px"></empty-data>
    </layout-main>
  </my-layout>
  <el-dialog :title="dialogTitle" v-model="visibleTag" width="500">
    <div class="add-tab-pane">
      <el-form :model="form" label-width="80px">
        <el-form-item label="标签名:" :rules="[{ required: true }]">
          <div class="option">
            <el-input v-model="form.groupName" :maxlength="20" placeholder="请填写标签名" class="option-input" />
          </div>
        </el-form-item>
        <div v-for="(item, index) in form.tag" :key="item.id">
          <el-form-item :label="'选项' + (index + 1) + ':'" :rules="[{ required: true }]">
            <div class="option">
              <el-input type="text" v-model="item.name" class="option-input" placeholder="请填写选项" />
              <el-icon style="color: #006eff" class="pointer" @click="onRemoveOption(index)">
                <Delete />
              </el-icon>
            </div>
          </el-form-item>
        </div>
        <div main-color class="pointer add-tag" @click="addOptions">
          <el-icon size="12">
            <Plus />
          </el-icon>
          新增选项
        </div>
      </el-form>
    </div>
    <template #footer>
      <div text-center>
        <el-button v-if="actionType === 'add'" class="w-100px" @click="onClose">取消</el-button>
        <el-button v-else class="w-100px" @click="removeTag()">删除</el-button>
        <el-button class="w-100px" type="primary" @click="saveTag">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref } from "vue";
import { ElMessage } from "element-plus";
import { storeToRefs } from "pinia";
import { getRandomStr } from "@/utils";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import { ElMessageBox } from "element-plus";
import EmptyData from "@/components/empty-data.vue";
import { updateCorpGroupTag, addCorpGroupTag, deleteCorpGroupTag } from "@/api/corp";
import { configStore } from "@/store/config";
const { getGroupTag } = configStore();
const { tags } = storeToRefs(configStore());
let actionType = ref("add");
const visibleTag = ref(false); // 是否显示标签弹窗
let editTagItem = {}; // 编辑的标签
const dialogTitle = computed(() => {
  return actionType.value === "add" ? "新增标签" : "编辑标签";
});
getGroupTag();

/***********新增选项***********/
const form = ref({
  label: "",
});
function addTag() {
  actionType.value = "add";
  visibleTag.value = true;
  form.value = {
    groupName: "",
    groupId: getRandomStr(),
    tag: [],
  };
}
function editTag(item) {
  actionType.value = "edit";
  visibleTag.value = true;
  editTagItem = item;
  form.value = JSON.parse(JSON.stringify(item));
}
function addOptions() {
  form.value.tag.push({
    name: "",
    id: getRandomStr(),
  });
}
function onRemoveOption(index) {
  form.value.tag.splice(index, 1);
}
function onClose() {
  visibleTag.value = false;
}
function removeTag() {
  ElMessageBox.confirm("是否删除该标签?", "提示", { type: "warning" }).then(() => {
    tags.value = tags.value.filter((item) => item.groupId !== editTagItem.groupId);
    visibleTag.value = false;
    deleteCorpGroupTag({
      groupId: editTagItem.groupId,
    });
  });
}
async function saveTag() {
  if (!form.value.groupName) return ElMessage.error("请填写标签名");
  form.value.tag = form.value.tag.filter((item) => item.name);
  if (form.value.tag.length === 0) return ElMessage.error("请填写选项");
  actionType.value === "add" ? await addCorpGroupTag(form.value) : await updateCorpGroupTag(form.value.groupId, form.value);
  visibleTag.value = false;
  getGroupTag();
}
</script>
<style scoped lang="scss">
.add-tab-pane {
  min-height: 200px;
  padding-top: 10px;
  margin-left: 20px;

  .tab-label {
    width: 300px;
  }

  .warn-title {
    min-width: 300px;
  }

  .option {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    &-input {
      width: 300px;
      margin-right: 20px;
    }
  }

  .add-tag {
    width: 80px;
    margin-left: 10px;
  }
}

.tag-cell {
  display: flex;
  align-items: center;
  justify-content: space-between;

  &-item {
    display: flex;
    align-items: center;

    .label {
      font-weight: 500;
    }

    .options {
      display: flex;
      // align-items: center;
      margin-left: 6px;
      margin-bottom: -10px;

      .item {
        flex-shrink: 0;
        border: 1px solid;
        border-radius: 4px;
        color: #888888;
        padding: 2px 10px;
        min-width: 30px;
        max-width: 100%;
        // text-align: center;
        margin-right: 15px;
        margin-bottom: 10px;
      }
    }
  }

  .right {
    width: 130px;
  }
}
</style>
