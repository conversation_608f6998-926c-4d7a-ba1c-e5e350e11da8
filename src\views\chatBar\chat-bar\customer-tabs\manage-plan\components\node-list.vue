<template>
  <div class="relative list-node pl-30px mr-15px" v-bind="attrs">
    <slot></slot>
  </div>
</template>
<script setup>
import { computed } from 'vue';

const props = defineProps({
  index: { type: Number, default: 1 },
  last: { type: Boolean, default: false }
})

const attrs = computed(() => ({
  class: props.last ? 'list-node--last' : '',
  style: `--index: '${props.index}';--fontSize: ${props.index > 9 ? '10px' : '12px'}`
}))


</script>
<style lang="scss" scoped>
.list-node::before {
  content: var(--index);
  position: absolute;
  top: 10px;
  left: 15px;
  width: 20px;
  height: 20px;
  line-height: 20px;
  color: white;
  font-size: var(--fontSize);
  text-align: center;
  border-radius: 50%;
  transform: translateX(-50%);
  background-color: var(--yc-primary-color);
  z-index: 3;
}

.list-node::after {
  content: "";
  position: absolute;
  top: 10px;
  left: 15px;
  width: 1px;
  height: 100%;
  border-radius: 50%;
  transform: translateX(-50%);
  background-color: var(--yc-primary-color);
  z-index: 2;
}

.list-node--last::after {
  display: none;
}
</style>
