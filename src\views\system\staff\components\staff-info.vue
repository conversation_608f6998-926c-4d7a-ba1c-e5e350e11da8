<template>
  <my-layout bg-fff>
    <layout-main>
      <div px-15>
        <div active-title-bar font-semibold font-18 class="my-15px">员工详情</div>
        <div flex items-center pb-15 border-bottom>
          <div flex-shrink-0 class="mr-20px">
            <upload-file ref="coverRef" classnames="h-80px w-80px" :showFileList="false" listType=""
              @hand-file-change="onHandFileChange" :fileList="fileList" fileType="image/*">
              <el-avatar v-loading="uploadLoading" element-loading-text="正在上传..." shape="square" :size="80" fit="fill"
                :src="cover || staff.avatar">
                <div w-full h-full flex flex-col justify-center bg-fff items-center>
                  <el-button class="w-80px h-80px is-hover" plain>上传头像</el-button>
                </div>
              </el-avatar>
            </upload-file>
          </div>
          <div flex-grow>
            <div font-16 font-semibold style="line-height: 40px;">Super Admin</div>
            <div font-14 style="line-height: 40px;">
              <span class="mr-20px">部门：营销中心</span>
              <span>ID: 110222</span>
            </div>
          </div>
          <el-button flex-shrink-0 type="primary">编辑</el-button>
        </div>

        <div font-semibold font-18 class="my-15px">基础信息</div>
        <div pb-15 border-bottom>
          <el-form class="staff-info" :label-width="90" label-position="left" label-suffix="：">
            <el-row>
              <el-col :lg="8" :md="12" :sm="24">
                <el-form-item label="姓名">张某某</el-form-item>
              </el-col>
              <el-col :lg="8" :md="12" :sm="24">
                <el-form-item label="性别">男</el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :lg="8" :md="12" :sm="24">
                <el-form-item label="手机号">178******</el-form-item>
              </el-col>
              <el-col :lg="8" :md="12" :sm="24">
                <el-form-item label="岗位角色">职工</el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-form-item label="员工介绍">员工介绍员工介绍员工介绍员工介绍员工介绍员工介绍员工介绍员工介绍</el-form-item>
            </el-row>
          </el-form>
        </div>
        <div font-semibold font-18 class="my-15px">权限信息</div>
        <div pb-15>
          <el-form class="staff-info" :label-width="90" label-position="left" label-suffix="：">
            <el-row>
              <el-col :span="24">
                <el-form-item label="平台角色">
                  <el-button class="is-hover">超级管理员</el-button>
                  <el-button class="is-hover">角色</el-button>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="所属团队">
                  <div flex-grow>
                    <div flex items-center>
                      <el-button class="is-hover mr-10px w-160px " :icon="Medal" type="success" plain>
                        <span class="inline-block truncate max-w-120px">健康健康小队健康小队健康小队健康小队小队健康小队</span>
                      </el-button>
                      <span>团队角色：团队负责人</span>
                    </div>
                    <div flex items-center class="mt-10px">
                      <el-button class="is-hover mr-10px w-160px " type="success" plain>
                        <span class="truncate">健康小队</span>
                      </el-button>
                      <span>团队角色：团队负责人</span>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
    </layout-main>
  </my-layout>
</template>
<script setup>
import { Medal } from "@element-plus/icons-vue";
import uploadFile from "@/components/upload-file/index.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
const props = defineProps({
  staff: { type: Object, default: () => ({}) }
})
</script>
<style scoped>
.staff-info .el-form-item {
  margin-bottom: 6px;
}
</style>
