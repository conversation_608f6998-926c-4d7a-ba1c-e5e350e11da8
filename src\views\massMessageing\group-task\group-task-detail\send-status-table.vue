<template>
  <div bg-fff common-shadow class="p-15px rounded-8px">
    <div class="text-16px font-semibold">客户发送情况</div>
    <div class="flex items-center justify-between">
      <el-button-group class="my-15px flex-shrink-0">
        <el-button :type="status == 'success' ? 'primary' : ''" :plain="status !== 'success'" @click="changeStatus('success')">发送成功（{{ sendSuccessCount }}）</el-button>
        <el-button :type="status == 'fail' ? 'primary' : ''" :plain="status !== 'fail'" @click="changeStatus('fail')">发送失败（{{ sendFailCount }}）</el-button>
      </el-button-group>
    </div>
    <el-table stripe ref="multipleTableRef" :data="tableData" empty-text="暂无数据" @selection-change="handleSelectionChange">
      <!-- <el-table-column v-if="status === 'fail'" class-name="first-td" type="selection" width="55" /> -->
      <el-table-column :class-name="status === 'fail' ? '' : 'first-td'" prop="customer" label="客户" :min-width="80">
        <template #default="{ row }">
          {{ row.name }}
        </template>
      </el-table-column>
      <el-table-column prop="send_time" label="发送时间" :min-width="120" v-if="status === 'success'">
        <template #default="{ row }">
          {{ dayjs.unix(row.send_time).format("YYYY-MM-DD HH:mm") }}
        </template>
      </el-table-column>
      <el-table-column prop="send_time" label="失败原因" :min-width="120" v-if="status === 'fail'">
        <template #default="{ row }">
          {{ row.status === 2 || row.status === 3 ? failReason[row.status] : "未知原因" }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" class-name="el-table-fixed-column--shadow last-td" label="操作" width="120">
        <template #default="{ row }">
          <el-text type="primary" class="cursor-pointer pr-10px" @click="customerDetial(row._id)">详情</el-text>
          <el-text v-if="row.status === 2 || row.status === 3" type="primary" class="cursor-pointer" @click="resend(row)">重发</el-text>
        </template>
      </el-table-column>
    </el-table>
    <div class="pt-15px px-15px flex justify-end border-t border-gray-200">
      <el-pagination layout="prev, pager, next, sizes, jumper" :current-page="page" :page-size="pageSize" :page-sizes="[10, 20, 50]" :total="total" @current-change="changePage" @size-change="changeSize"></el-pagination>
    </div>
  </div>
  <customer-detail :visible="customerDetailVisible" @close="customerDetailVisible = false" :customerId="customerId" title="客户详情" customerType="member" />
</template>
<script setup>
import { computed, ref } from "vue";
import { ElMessage } from "element-plus";
import WwUser from "@/components/ww-user/index.vue";
import { openChatWindow } from "@/utils/common";
import customerDetail from "@/views/member/detail/index.vue";
import dayjs from "dayjs";
import { useRouter } from "vue-router";
const router = useRouter();
const props = defineProps({
  sendFailList: {
    type: Array,
    default: () => [],
  },
  isNoSend: {
    type: Boolean,
    default: false,
  },
  customers: {
    type: Array,
    default: () => [],
  },
  sendSuccessList: {
    type: Array,
    default: () => [],
  },
  groupmsg: {
    type: Object,
    default: () => ({}),
  },
});
const page = ref(1);
const pageSize = ref(10);
const total = ref(0);
const status = ref("success");
const customerId = ref("");
const customerDetailVisible = ref(false);
function changeStatus(val) {
  status.value = val;
}
const failReason = {
  2: "因客户不是好友导致发送失败",
  3: "因客户已经收到其他群发消息导致发送失败",
};
const sendSuccessCount = computed(() => {
  return props.sendSuccessList.length;
});
const sendFailCount = computed(() => {
  return props.sendFailList.length;
});

const tableData = computed(() => {
  let arr = status.value === "success" ? props.sendSuccessList : props.sendFailList;
  total.value = arr.length;
  // 对数组进行分页处理
  arr = arr.slice((page.value - 1) * pageSize.value, page.value * pageSize.value);
  return arr;
});
const selections = ref([]);
function handleSelectionChange(val) {
  selections.value = val;
}
function send() {
  if (selections.value.length === 0) return ElMessage.warning("请选择需要重发的客户");
}
function changePage(val) {
  page.value = val;
}
function changeSize(val) {
  pageSize.value = val;
}
function customerDetial(id) {
  customerId.value = id;
  customerDetailVisible.value = true;
}
// 重新发送
function resend(item) {
  const msgList = [];
  if (props.groupmsg.content) msgList.push({ msgtype: "text", text: { content: props.groupmsg.content } });
  const attachments = Array.isArray(props.groupmsg.attachments) ? props.groupmsg.attachments : [];
  attachments.forEach((item) => {
    if (item.msgtype === "image" && item.image && item.image.media_id) {
      msgList.push({ msgtype: "image", image: { mediaid: item.image.media_id } });
    } else if (item.msgtype === "video" && item.video && item.video.media_id) {
      msgList.push({ msgtype: "video", video: { mediaid: item.video.media_id } });
    } else if (item.msgtype === "file" && item.file && item.file.media_id) {
      msgList.push({ msgtype: "file", file: { mediaid: item.file.media_id } });
    } else if (item.msgtype === "link" && item.link) {
      msgList.push({
        msgtype: "news",
        news: {
          link: item.link.url || "", //H5消息页面url 必填
          title: item.link.title || "", //H5消息标题
          desc: item.link.desc || "", //H5消息摘要
          imgUrl: item.link.picurl || "", //H5消息封面图片URL
        },
      });
    }
  });
  item.externalUserId && openChatWindow(item._id, item.externalUserId, "", msgList.length ? { name: "sendGroupMsg", msgList, time: new Date().getTime() } : {});
}
</script>
<style lang="scss" scoped></style>
