<template>
  <my-layout>
    <layout-item>
      <div bg-fff class="pt-10px mb-10px">
        <el-form inline :label-width="90" label-suffix="：">
          <div class="flex flex-wrap px-15px query">
            <el-form-item class="query__item" label-width="auto">
              <ww-user-select :list="memberList" placeholder="请选择使用员工" :value="currentMember"
                @change="change($event)" />
            </el-form-item>
            <el-form-item class="query__item" label-width="auto">
              <el-select v-model="qrCodeStatus" placeholder="活码状态" clearable>
                <el-option label="启用中" value="enable" />
                <el-option label="未启用" value="NOTEXIST" />
                <!-- <el-option :label="item.label" :value="item.value" /> -->
              </el-select>
            </el-form-item>
            <el-form-item class="query__item">
              <el-button class="ml-20px" type="primary" @click="getList()">查询 </el-button>
              <el-button type="primary" @click="reset()" plain>重置</el-button>
              <el-text class="cursor-pointer ml-15px" type="primary" @click="visible = true">了解员工个人码</el-text>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <el-table stripe height="100%" :data="tableData" v-loading="loading">
        <el-table-column property="qrCodeName" label="活码名称" :min-width="150">
          <template #default="{ row:{corpUserId} }">
            <div v-if="corpUserId && corpUserId[0]">
              <ww-user :openid="corpUserId[0]" />
              个人码
            </div>
          </template>
        </el-table-column>
        <el-table-column property="taskContent" label="使用成员" :min-width="200">
          <template #default="{ row: { corpUserId } }">
            <el-popover v-if="corpUserId && corpUserId.length" placement="top-start" :width="200" trigger="hover">
              <template #reference>
                <div text-ellipsis>
                  <span v-for="item in corpUserId" :key="item" class="mr-4px">
                    <ww-user :openid="item"></ww-user>
                  </span>
                </div>
              </template>
              <span v-for="item in corpUserId" :key="item" class="mr-4px">
                <ww-user :openid="item"></ww-user>
              </span>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column property="allAddCustomerCount" label="总添加客户数" :min-width="150" />
        <el-table-column property="todayAddCustomerCount" label="今日添加客户数" :min-width="200" />
        <el-table-column property="createor" label="启用人" :min-width="120">
          <template #default="{ row }">
            <ww-user :openid="row.creator" />
          </template>
        </el-table-column>
        <el-table-column property="createTime" label="启用时间 " :min-width="120" />
        <el-table-column property="qrCodeStatusName" label="状态 " :min-width="100">
          <template #default="{row}">
            <span> {{ row.noExist ?'未启用': row.qrCodeStatusName }}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" class-name="el-table-fixed-column--shadow" label="操作" width="180">
          <template #default="{ row }">
            <el-text v-if="row.noExist" class="cursor-pointer" type="primary" @click="activateCode(row)">启用</el-text>
            <template v-else>
              <el-text v-if="row.qrCodeStatus === 'enable'" class="cursor-pointer mr-10px" type="primary"
                @click="edit(row)">编辑</el-text>
              <el-text v-if="row.qrCodeStatus === 'enable'" class="cursor-pointer mr-10px" type="primary"
                @click="downloadImage(row)">下载</el-text>
              <el-text class="cursor-pointer mr-10px" type="primary" @click="toStatisticsView(row)">统计</el-text>
            </template>
          </template>
        </el-table-column>
      </el-table>
    </layout-main>
  </my-layout>
  <personal-code-modal :visible="visible" @close="visible = false" />

</template>
<script setup>
import { computed, onMounted, ref, watch } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import dayjs from "dayjs";
import { getOpenedAccount } from "@/api/corp.js";
import { getPersonalQrcodeList, getPersonalQrCode } from "@/api/knowledgeBase";
import WwUserSelect from "@/components/ww-user-select";

import personalCodeModal from "./personal-code-modal.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";

const emits = defineEmits(['add', 'edit', 'download']);
const props = defineProps({
  qrcodeStatus: { type: Array, default: () => [] },
  statusMap: { type: Object, default: () => ({}) }
});
const router = useRouter();

const visible = ref(false);
const loading = ref(false);
const qrCodeStatus = ref('');
const memberList = ref([]);
const currentMember = ref();

const list = ref([]);
const tableData = computed(() => {
  return list.value.map((item, index) => {
    item.createTime = item.createTime ? dayjs(item.createTime).format("YYYY-MM-DD") : "";
    item.qrCodeStatusName = props.statusMap[item.qrCodeStatus];
    return { ...item, index: index + 1 }
  }).filter(i => {
    const matchMember = currentMember.value ? i.corpUserId && i.corpUserId.length === 1 && i.corpUserId[0] === currentMember.value : true;
    if (qrCodeStatus.value === 'enable') {
      return matchMember && i.qrCodeStatus === 'enable'
    } else if (qrCodeStatus.value === 'NOTEXIST') {
      return matchMember && i.noExist === true;
    }

    return matchMember
  });
});

function change(value) {
  currentMember.value = value;
}

function downloadImage(row) {
  emits('download', row)
}

function edit(row) {
  emits('edit', row)
}

function reset() {
  qrCodeStatus.value = '';
  currentMember.value = ''
  getList()
}

function toStatisticsView(item) {
  router.push({ name: "MARKETCQRCODEDETAIL", params: { id: item._id } });
}

async function getMemberList() {
  let { data } = await getOpenedAccount();
  memberList.value = data && Array.isArray(data.data) ? data.data.map(i => i.userid) : [];
}

async function getList() {
  if (memberList.value.length) {
    const { data, success, message } = await getPersonalQrcodeList({
      corpId: localStorage.getItem('corpId'),
    });
    loading.value = true;
    if (success) {
      list.value = formatList(Array.isArray(data.data) ? data.data : []);
    } else {
      ElMessage.error(message);
      return;
    }
  } else {
    list.value = [];
  }

  loading.value = false;
}

async function activateCode(row) {
  await ElMessageBox.confirm("是否启用该个人员工码?", '提示', { type: 'warning' })
  const { success, message } = await getPersonalQrCode(row.corpUserId[0], localStorage.getItem('corpId'), localStorage.getItem('userId'));
  if (success) {
    ElMessage.success('启用成功');
    getList()
  } else {
    ElMessage.error(message);
  }
}

function formatList(list) {
  // if (list.length === 0) return [];
  const res = [];
  memberList.value.forEach(useid => {
    const records = list.filter(i => i.corpUserId && i.corpUserId.length === 1 && i.corpUserId[0] === useid);
    list = list.filter(i => !(i.corpUserId && i.corpUserId.length === 1 && i.corpUserId[0] === useid));
    if (records.length) {
      res.push(...records)
    } else {
      res.push({ corpUserId: [useid], noExist: true, _id: `${useid}_${new Date().getTime()}` })
    }
  })
  if (list.length) {
    res.push(...list)
  }
  return res
}

onMounted(async () => {
  loading.value = true;
  await getMemberList()
  getList()
})

defineExpose({ refresh: getList })

</script>
<style lang="scss" scoped>
.query {
  .query__item {
    margin-bottom: 15px;
    margin-right: 10px;

    @at-root &:last-of-type {
      margin-left: auto;
      margin-right: 0;
    }
  }
}
</style>
