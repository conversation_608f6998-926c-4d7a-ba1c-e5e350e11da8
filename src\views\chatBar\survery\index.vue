<template>
  <my-layout bg-fff v-loading="loading">
    <layout-main v-if="list.length">
      <div v-for="item in list" :key="item._id" flex items-center px-15 border-bottom>
        <div flex-grow w-0 @click="preview(item)">
          <div py-10 font-16 font-semibold>{{ item.name }}</div>
          <div pb-10 font-14 text-ellipsis>
            <el-text type="info">{{ item.description || "暂无问卷说明" }}</el-text>
          </div>
        </div>
        <div flex-shrink-0>
          <!-- <el-text pointer class="mr-8" type="success" @click="showSurvery(item)">预览</el-text> -->
          <el-text pointer type="primary" @click.stop="send(item)">发送</el-text>
        </div>
      </div>
    </layout-main>
    <layout-main v-else-if="list.length === 0 && !loading" :scroll="false">
      <div flex flex-col h-full justify-center items-center>
        <empty-data :top="0" :title="emptyText" />
      </div>
    </layout-main>
  </my-layout>

  <survery-dialog isMobile :data="survery" :visible="visible" :y-padding="300" @close="visible = false" />
  <el-dialog v-model="pickVisible" title="选择客户" :width="width" @close="pickVisible = false">
    <template #header>
      <div font-16 font-semibold>选择客户</div>
    </template>
    <el-scrollbar wrap-style="max-height: calc(100vh - 300px)">
      <div font-14 px-15 color-666>该账号下有{{ memberList.length }}位客户信息，请你确认该问卷的关联客户是哪一位？</div>
      <div flex items-center px-15 v-for="member in memberList" :key="member._id">
        <div flex-shrink-0 mr-10>
          <el-icon :class="currentMember._id !== member._id ? 'transparent' : ''" size="20" color="#006eff"><Select /></el-icon>
        </div>
        <div flex-grow>
          <customer-bar :member="member" @click="select(member)"></customer-bar>
        </div>
      </div>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button :loading="sendLoading" type="primary" @click="sendSurvery()">发送问卷</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { onMounted, ref, watch } from "vue";
import { useDocumentVisibility } from "@vueuse/core";
import { ElMessage } from "element-plus";
import { getList as getSurveyList, createRecord } from "@/api/survery";
import { configStore } from "@/store/config";
import { envjudgwxWork } from "@/utils/common";
import { jsSDKConfig } from "@/utils/jssdk";
import { getCurExternalContact } from "@/utils/jssdk.js";
import { memberStore } from "@/store/member";
import CustomerBar from "../components/customer-bar.vue";
import { createServiceRecord } from "@/utils/service";
import SurveryDialog from "@/views/system/survery/dialog.vue";
import EmptyData from "@/components/empty-data.vue";
import MyLayout, { LayoutMain } from "@/components/layout";
import { sendChatMessagePromise } from "@/utils/jssdk.js";
// onMounted(getList)

const list = ref({});
const loading = ref(false);
const emptyText = ref("");
async function getList() {
  loading.value = true;
  const { success, message, data = {} } = await getSurveyList(1, 99, "", "enable");
  const { list: tableData = [], total: count = 0 } = data;
  emptyText.value = success ? "暂无问卷信息" : message;
  list.value = tableData;
  if (!success) ElMessage.error(message);
  loading.value = false;
}

const survery = ref({});
const visible = ref(false);
const memberList = ref([]);
const pickVisible = ref(false);
const width = ref(520);
const currentMember = ref({});
function preview(item) {
  survery.value = item;
  visible.value = true;
}

async function send(item) {
  loading.value = true;
  survery.value = item;
  memberList.value = await getMemberList();
  if (memberList.value.length === 1) {
    currentMember.value = memberList.value[0];
    sendSurvery();
  } else if (memberList.value.length > 1) {
    currentMember.value = memberList.value.some((i) => i._id === currentMember.value._id) ? currentMember.value : {};
    width.value = Math.min(window.innerWidth * 0.9, 520);
    pickVisible.value = true;
  } else {
    ElMessage.error("未查询到客户信息");
  }
  loading.value = false;
}

function select(member) {
  currentMember.value = member;
}
const sendLoading = ref(false);
async function sendSurvery() {
  if (currentMember.value.name) {
    sendLoading.value = true;
    const { success, data, message } = await createRecord(survery.value._id, currentMember.value._id, currentMember.value.name);
    if (success) {
      sendLink(data.id, currentMember.value.name);
    } else {
      ElMessage.error(message);
      sendLoading.value = false;
    }
  } else {
    ElMessage.info("请选择客户");
  }
}
async function createServiceRecordAction(answerId) {
  const { memberInfo } = memberStore();
  const { anotherName } = memberInfo;
  let item = {
    taskContent: `${anotherName}发送问卷"${survery.value.name}"给客户${currentMember.value.name}`,
    executionTime: new Date().getTime(),
    customerId: currentMember.value._id,
    executeTeamId: "",
    eventType: "questionnaire",
    customerName: currentMember.value.name,
    pannedEventSendFile: {
      name: survery.value.name,
      surveryId: survery.value._id,
      type: "questionnaire",
      answerId,
    },
  };
  await createServiceRecord(item);
}

async function getMemberList() {
  let externalUserId = "";
  if (envjudgwxWork()) {
    await jsSDKConfig();
    externalUserId = await getCurExternalContact();
  }
  const data = await getMemberInfo(externalUserId);
  return data;
}
async function getMemberInfo(externalUserId) {
  // 根据externalUserID 获取到外部联系人信息
  const { data } = await getCustomerInfoById(localStorage.getItem("userId"), {
    externalUserId,
  });
  return data.data;
}
const config = configStore();
async function sendLink(id, name) {
  //发送问卷触发服务记录
  createServiceRecordAction(id);
  const corpId = localStorage.getItem("corpId");
  // const url = `https://www.youcan365.com/h5/#/pages/survery/fill?corpId=${corpId}&surveryId=${survery.value._id}&memberId=${currentMember.value._id}&unionid=${currentMember.value.unionid || ''}`;
  const url = `${import.meta.env.VITE_PATIENT_PAGE_BASE_URL}pages/survery/fill?corpId=${corpId}&surveryId=${survery.value._id}&memberId=${currentMember.value._id}&unionid=${currentMember.value.unionid || ""}&answerId=${id}&name=${encodeURIComponent(name || "")}`;
  const desc = `请填写问卷`;
  try {
    const item = {
      type: "news",
      title: survery.value.name || "填写问卷",
      desc,
      url,
      imgUrl: "https://796f-youcan-clouddev-1-8ewcqf31dbb2b5-**********.tcb.qcloud.la/other/19-%E9%97%AE%E5%8D%B7.png?sign=55a4cd77c418b2c548b65792a2cf6bce&t=**********", //H5消息封面图片URL
    };
    await sendChatMessagePromise(item);
    ElMessage.success("发送成功");
    visible.value = false;
    pickVisible.value = false;
    sendLoading.value = false;
  } catch (e) {
    console.log(e);
    ElMessage.error("发送失败");
    sendLoading.value = false;
  }
}
const visibility = useDocumentVisibility();
watch(
  visibility,
  (n) => {
    if (n == "visible") {
      getList();
    }
  },
  { immediate: true }
);
</script>
<style scoped>
.transparent {
  opacity: 0;
}

[border-bottom-dashed] {
  border-bottom: 1px dashed #eee;
}

.mr-8 {
  margin-right: 8px;
}
</style>