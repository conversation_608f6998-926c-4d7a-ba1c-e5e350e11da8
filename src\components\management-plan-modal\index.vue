<template>
  <el-dialog :model-value="props.visible" title="新增跟进计划" @close="close()" :width="width">
    <el-scrollbar wrap-class="el-dialog-body-full-scroll">
      <div class="pb-10px" :loading="taskLoading">
        <el-form label-suffix="：" :rules="rules" :model="planForm" v-if="planForm.planExecutStaus == 'untreated'">
          <el-row class="border-b mb-15px border-gray-200">
            <el-col :span="12">
              <el-form-item required label="开始时间" label-width="110px" class="mt-10px" style="margin-right: 0" prop="planExecutionTime">
                <el-date-picker v-model="planForm.planExecutionTime" style="width: 180px" format="YYYY-MM-DD" placeholder="请选择开始时间" type="date" value-format="YYYY-MM-DD" :disabled-date="judgeDisabled" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item required label="计划跟踪人" label-width="110px" prop="executorUserId" class="mt-10px" style="margin-right: 0">
                <el-select v-model="planForm.executorUserId" class="el-select--hiddenValue w-full" placeholder="请选择计划跟踪人" filterable>
                  <template #prefix>
                    <div class="h-30px" color-666>
                      <ww-user :openid="planForm.executorUserId"></ww-user>
                    </div>
                  </template>
                  <el-option v-for="item in peopleList" :key="item.value" :value="item.value">
                    <ww-user :openid="item.value"></ww-user>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template v-else>
          <div class="flex" align-center>
            <div class="text-16px font-600">{{ planForm.planName }}</div>
            <div class="text-14px pl-10px" main-color>
              {{ managementPlanStatus[planForm.planExecutStaus] }}
            </div>
          </div>
          <div>
            <div class="flex my-10px">
              <div class="text-14px">
                <span grew-color class="pr-10px">开始时间:</span>
                <span>{{ planForm.planExecutionTime }}</span>
              </div>
              <div class="text-14px ml-30px">
                <span grew-color class="pr-10px">计划跟踪人:</span>
                <ww-user v-if="planForm.executorUserId" :openid="planForm.executorUserId"></ww-user>
              </div>
            </div>
            <div class="py-10px" border-top></div>
          </div>
        </template>
        <div v-if="planForm.planExecutStaus !== 'closed'">
          <el-button text class="item form-tag mb-10px" type="primary" :icon="BottomRight" plain @click="showPlanModal()">选择模板</el-button>
          <el-button text class="item form-tag mb-10px" type="primary" :icon="Plus" plain @click="addTask">新增任务项</el-button>
        </div>
        <task-timeline ref="taskTimelineRef" :taskList="taskList" :planExecutStaus="planForm.planExecutStaus" :planExecutionTime="planForm.planExecutionTime" @saveSuccess="saveTaskSuccess" :planForm="planForm" :customer="customer" @editTaskSuccess="onEditTaskSuccess"></task-timeline>
      </div>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button class="w-100px" plain @click="closePlan()">取消</el-button>
        <template v-if="planForm && planForm.planExecutStaus === 'executing'">
          <el-button class="w-100px" type="primary" @click="stopPlan()">终止计划</el-button>
        </template>
        <template v-else-if="planForm && planForm.planExecutStaus === 'closed'">
          <el-button class="w-100px" type="primary" @click="addPlan()">添加计划</el-button>
        </template>
        <template v-else>
          <el-button :loading="loading" class="w-100px" type="primary" @click="toExecutPlan">执行计划</el-button>
        </template>
      </div>
    </template>
  </el-dialog>
  <select-plan :visible="planVisible" @close="closePlanModal" @success="onSuccess" :planId="planForm.planId" />
</template>
<script setup>
import { ref, watch, reactive, computed } from "vue";
import { storeToRefs } from "pinia";
import { BottomRight, Plus } from "@element-plus/icons-vue";
import WwUser from "@/components/ww-user/index.vue";
import { managementPlanStatus } from "@/baseData";
import dayjs from "dayjs";
import { teamStore } from "@/store/team";
import { memberMangePlanStore } from "@/store/managePlan/memberMangePlan";
import { stopManagementPlan } from "@/api/managementPlan";
import useModal from "@/hooks/useModal";
import { staffStore } from "@/store/staff";
import selectPlan from "./select-plan.vue";
import taskTimeline from "@/views/managementPlan/components/task-timeline";

const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());
const peopleList = computed(() => staffList.value.map((i) => ({ value: i.userid })));
const { currentTeam } = storeToRefs(teamStore());
const props = defineProps({
  visible: { type: Boolean, default: false },
  data: { type: Object, default: () => {} },
  width: { type: Number, default: 700 },
  executorUserId: { type: String, default: "" },
});
const emits = defineEmits(["success", "close"]);
function close() {
  emits("close");
}
const { planForm, taskList, taskLoading, isAddCustomerTask, customer, team } = storeToRefs(memberMangePlanStore());
const { initPlan, executPlan, stopPlan, getMemberManagementPlanFnc, onEditTaskSuccess, updateMemberMangePlanStatusFnc } = memberMangePlanStore();
const { close: closePlanModal, show: showPlanModal, visible: planVisible } = useModal(640);
const loading = ref(false);
let rules = reactive({
  planExecutionTime: [{ required: true, message: "请选择开始时间", trigger: "blur" }],
  executorUserId: [{ required: true, message: "请选择计划跟踪人", trigger: "blur" }],
});
team.value = currentTeam.value;
watch(
  () => props.visible,
  async (n) => {
    if (n) {
      if (!staffList.value.length) getStaffList();
      customer.value = props.data;
      isAddCustomerTask.value = true;
      await getMemberManagementPlanFnc();
    } else {
      isAddCustomerTask.value = false;
      initPlan();
    }
  }
);
// 监听计划表单变化
watch(
  () => planForm.value,
  () => {
    if (!props.visible) return;
    if (!planForm.value.planName) planForm.value.planName = `${customer.value.name}的跟进计划`;
    if (!planForm.value.planExecutionTime) planForm.value.planExecutionTime = dayjs().format("YYYY-MM-DD");
    if (!planForm.value.executorUserId && props.executorUserId) {
      planForm.value.executorUserId = props.executorUserId;
    }
  }
);

function toExecutPlan() {
  if (loading.value) return;
  loading.value = true;
  executPlan(() => {
    loading.value = false;
    emits("close");
    emits("success");
  });
}

// 取消计划
function closePlan() {
  initPlan();
  emits("close");
}
//添加计划

function addPlan() {
  initPlan();
  taskList.value = [];
}

// 编辑计划
function saveTaskSuccess(list) {
  // 回访计划状态为已变更
  planForm.value.changedStatus = "isChanged";
  updateMemberMangePlanStatusFnc();
  taskList.value = list;
}

function judgeDisabled(date) {
  return dayjs(date).endOf("day").isBefore(dayjs());
}

const taskTimelineRef = ref(null);
function addTask() {
  taskTimelineRef.value.addTask(planForm.value.planId);
}

function onSuccess(item) {
  closePlanModal();
  const { planId, planName, taskList: list } = item;
  planForm.value = {
    planExecutionTime: "",
    planName,
    planId,
    planExecutStaus: "untreated",
  };
  taskList.value = list;
}
</script>

<style lang="scss">
.el-select.el-select--hiddenValue .el-input__inner {
  display: none;
}

.el-select.el-select--hiddenValue .el-input__prefix {
  flex-grow: 1;
}

:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-timeline-item__tail) {
  border-left-color: #006eff;
}
</style>