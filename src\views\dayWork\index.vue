<template>
  <my-layout bg-fff common-shadow>
    <layout-item>
      <div flex justify-between p-15 ref="headerRef">
        <div flex-shrink-0>
          <el-button type="primary" :icon="Notebook" @click="addWork('visit-reg')">就诊登记</el-button>
          <el-button type="primary" :icon="DocumentAdd" plain @click="addWork('schedule')">新增日程</el-button>
        </div>
        <div flex-shrink-0 flex items-center font-18 font-semibold>
          <el-icon pointer @click="toggle(-1)">
            <CaretLeft />
          </el-icon>
          <span>{{ dateTitle }}</span>
          <el-icon pointer @click="toggle(1)">
            <CaretRight />
          </el-icon>
        </div>
        <div flex-shrink-0 flex items-center>
          <el-button type="primary" class="mr-6px" @click="toggleToday()">今天</el-button>
          <el-radio-group v-model="type" @change="changeType">
            <el-radio-button label="month">月</el-radio-button>
            <el-radio-button label="week">周</el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <div px-15 pb-10 h-full v-loading="loading">
        <el-scrollbar>
          <month-work v-if="type === 'month'" :days="days" :timestamp="timestamp" :works="works" :width="width" @showDetail="showDetail" @edit="toEdit" @addWork="addDayWork"></month-work>
          <week-work v-else-if="type === 'week'" :days="days" :timestamp="timestamp" :works="works" :width="width" @showDetail="showDetail" @edit="toEdit" @addWork="addDayWork"></week-work>
        </el-scrollbar>
      </div>
    </layout-main>
  </my-layout>
  <edit-modal :data="data" :teams="teamList" :timestamp="timestamp" :visible="visible" @close="visible = false" @show-survery="showSurvery" @success="edited"></edit-modal>
  <card-modal :timestamp="timestamp" :work="currentWork" :visible="cardVisible" @change="changeCurrentWork()" @close="cardVisible = false" @edit="toEdit" @show-survery="showSurvery" @remove="handleRemove()"></card-modal>
  <survery-dialog :data="currentSurvery" :visible="surveryVisible" @close="surveryVisible = false" />
</template>
<script setup>
import { computed, onMounted, ref, watch } from "vue";
import { useWindowSize, watchDebounced, useTimestamp } from "@vueuse/core";
import dayjs from "dayjs";
import solarLunar from "solarLunar";
import { ElMessage } from "element-plus";
import { getCorpTeams, getWork } from "@/api/corp";
import { getDetail } from "@/api/survery";
import { WeekDay } from "@/baseData";
import { DocumentAdd, Notebook } from "@element-plus/icons-vue";
import CardModal from "./card-modal";
import EditModal from "./dialog";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import MonthWork from "./month-work";
import SurveryDialog from "@/views/system/survery/dialog.vue";
import WeekWork from "./week-work";
import { teamStore } from "@/store/team";
import { storeToRefs } from "pinia";
const { currentTeam } = storeToRefs(teamStore());
watch(currentTeam, (newItem, oldItem) => {
  if (newItem && oldItem && newItem.teamId !== oldItem.teamId) {
    getList();
  }
});
const timestamp = useTimestamp({ interval: 1000 });
const type = ref("month");
function changeType(val) {
  loading.value = true;
  if (val === "month") {
    currentMonth.value = dayjs();
    getDays(dayjs().startOf("month"));
  } else {
    getDays(dayjs());
  }
  getList();
}
const teamList = ref([]);
const data = ref({});
const visible = ref(false);
async function addWork(workType, payload = {}) {
  await getTeams();
  data.value = {
    workType,
    userId: localStorage.getItem("userId"),
    corpId: localStorage.getItem("corpId"),
    ...payload,
  };
  if (workType === "visit-reg") {
    data.value.title = "复诊预约";
  }
  visible.value = true;
}

function addDayWork(date) {
  addWork("schedule", { workTime: dayjs(`${date} 12:00:00`).valueOf() });
}
async function getTeams() {
  if (teamList.value.length === 0) {
    const { success, message, data } = await getCorpTeams(1, 9999, localStorage.getItem("corpId"));
    teamList.value = Array.isArray(data.list) ? data.list : [];
    if (!success) ElMessage.error(message);
  }
}
async function edited() {
  visible.value = false;
  getList();
}

const cardVisible = ref(false);
const currentWork = ref({});

function showDetail(detail) {
  currentWork.value = detail;
  cardVisible.value = true;
}

function handleRemove() {
  cardVisible.value = false;
  currentWork.value = {};
  getList();
}

async function changeCurrentWork() {
  const list = await getList();
  const item = list.find((i) => i._id === currentWork.value._id);
  if (item) {
    currentWork.value = item;
  } else {
    cardVisible.value = false;
  }
}

async function toEdit(detail) {
  if (detail.workTimeStamp > timestamp.value) {
    loading.value = true;
    await getTeams();
    loading.value = false;
    data.value = detail;
    visible.value = true;
  }
}

const width = ref(0);
const headerRef = ref();
const { width: windowWidth } = useWindowSize();
watchDebounced(
  windowWidth,
  () => {
    headerRef.value && (width.value = headerRef.value.offsetWidth - 30);
  },
  { debounce: 500, maxWait: 1000 }
);

const loading = ref(true);
const works = ref({});
const days = ref([]);
async function getList() {
  const [endDate] = days.value.slice(-1);
  const startDate = days.value[0];
  const { data, message, success } = await getWork({
    corpId: localStorage.getItem("corpId"),
    userId: localStorage.getItem("userId"),
    startDate: dayjs(startDate.timestamp).startOf("day").valueOf(),
    endDate: dayjs(endDate.timestamp).endOf("day").valueOf(),
  });
  const { list = [] } = data;
  works.value = handleList(list);
  loading.value = false;
  if (!success) ElMessage.error(message);
  return list;
}
onMounted(() => {
  getDays(dayjs().startOf("month"));
  getList();
  width.value = headerRef.value.offsetWidth - 30;
});

function getDays(date) {
  const startDate = type.value === "week" ? dayjs(date) : dayjs(date).startOf("month");
  const reduceDays = (startDate.day() || 7) - 1;
  const closestMonday = startDate.subtract(reduceDays, "day"); // 最近一个星期一的日期
  const endDate = type.value === "week" ? dayjs(date) : dayjs(date).endOf("month");
  const addDays = 7 - (endDate.day() || 7);
  const endSunday = endDate.add(addDays, "day"); // 最后一个星期天的日期
  const list = [];
  let day = closestMonday.startOf("day");
  while (day.isSame(endSunday) || day.isBefore(endSunday)) {
    const crossMonth = day.date() === 1;
    const lunar = solarLunar.solar2lunar(...day.format("YYYY-M-D").split("-"));
    list.push({
      date: day.format("YYYY-MM-DD"),
      timestamp: day.valueOf(),
      weekstr: WeekDay[day.day()],
      crossMonth,
      dayCn: lunar.dayCn,
      monthDay: day.format("M月D日"),
      month: day.format("M月"),
    });
    day = day.add(1, "day");
  }
  days.value = list;
}

function handleList(arr) {
  const works = arr.reduce((val, item) => {
    const key = dayjs(item.workTimeStamp).format("YYYY-MM-DD");
    item.noonTime = dayjs(item.workTimeStamp).format("HH:mm");
    val[key] = val[key] || [];
    val[key].push(item);
    return val;
  }, {});
  Object.values(works).forEach((i) => i.sort((a, b) => a.workTimeStamp - b.workTimeStamp));
  return works;
}
function toggleToday() {
  loading.value = true;
  getDays(type.value === "month" ? dayjs().startOf("month") : dayjs());
  getList();
}

const currentMonth = ref(dayjs());
const dateTitle = computed(() => {
  if (type.value === "week") {
    const date = days.value[0] ? days.value[0].date : "";
    return date ? dayjs(date).format("YYYY年MM月") : "";
  }
  return currentMonth.value.format("YYYY年MM月");
});
function toggle(num) {
  if (loading.value) return;
  loading.value = true;
  if (type.value === "week") {
    const date = days.value[0].date;
    getDays(dayjs(date).add(num * 7, "day"));
  } else {
    currentMonth.value = currentMonth.value.add(num, "month").startOf("month");
    getDays(currentMonth.value);
  }
  getList();
}

const currentSurvery = ref({});
const surveryVisible = ref(false);
async function showSurvery(id) {
  const corpId = localStorage.getItem("corpId");
  const { success, data, message } = await getDetail(corpId, id);
  if (success) {
    currentSurvery.value = data && data.data ? data.data : {};
    surveryVisible.value = true;
  } else {
    ElMessage.error(message);
  }
}
</script>
