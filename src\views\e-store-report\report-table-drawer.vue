<template>
  <el-drawer :model-value="visible" title="报备详情" size="70%" style="--el-drawer-padding-primary: 0" @close="close()">
    <my-layout class="bg-[#f2f3f4]">
      <layout-main>
        <customer-header request :customer-id="customer._id" @showCustomerDetail="showCustomerDetail" :request="true" @reload="customerReload" />
        <div class="mt-10px bg-white">
          <div class="px-10px text-16px font-semibold py-10px">报备信息</div>
          <div class="pt-30px pb-60px px-60px flex items-center border-b border-gray-200">
            <template v-for="(step, idx) in steps" :key="step.key">
              <div v-if="idx > 0" class="flex-grow rounded h-4px mx-4px" :class="step.checked ? 'bg-green-500' : 'bg-gray-200'"></div>
              <div class="relative flex-shrink-0 h-20px w-20px text-center rounded" :class="step.checked ? 'bg-green-500' : 'bg-gray-200'">
                <el-icon v-if="step.checked" class="text-16px text-white"><Select /></el-icon>
                <div class="absolute -top-24px w-80px -left-30px text-14px text-gray-500">{{ step.title || "" }}</div>
                <div class="absolute -bottom-44px w-230px text-14px truncate" :class="idx === 2 ? '-right-10px text-right' : '-left-30px text-left'">
                  <div v-if="step.time">{{ step.timeLabel }} {{ step.time }}</div>
                  <div v-if="step.userid" class="mt-6px">
                    {{ step.label }}
                    <ww-user :openid="step.userid" />
                  </div>
                  <div v-else-if="step.text" class="mt-6px">{{ step.label }}：{{ step.text }}</div>
                </div>
              </div>
            </template>
          </div>
        </div>
        <div class="mt-10px bg-white">
          <div class="px-10px text-16px font-semibold py-10px">网络咨询记录</div>
          <div color-normal class="p-10px flex items-center flex-wrap text-14px">
            <div class="max-w-1/2 truncate">
              <span class="text-gray-500">当前报备人：</span>
              <ww-user v-if="reportUserId" :openid="reportUserId" />
            </div>
            <div>
              <span class="ml-15px text-gray-500 max-w-1/2 truncate">报备时间：</span>
              {{ currentReportTime }}
            </div>
            <el-popover placement="bottom" :width="300" trigger="click">
              <template #reference>
                <div class="ml-15px text-blue-500 cursor-pointer">更新记录</div>
              </template>
              <el-scrollbar wrap-class="max-h-40vh">
                <div class="pl-5px pt-20px">
                  <el-timeline>
                    <el-timeline-item v-for="item in introducerRecord" :key="item._id" type="primary">
                      <div class="">
                        更新报备人:
                        <ww-user v-if="item.introducer" :openid="item.introducer"></ww-user>
                      </div>
                      <div class="mt-5px">更新时间: {{ dayjs(item.time).format("YYYY-MM-DD HH:mm") }}</div>
                      <div class="mt-5px">
                        操作人:
                        <ww-user v-if="item.operator" :openid="item.operator"></ww-user>
                      </div>
                    </el-timeline-item>
                  </el-timeline>
                </div>
              </el-scrollbar>
            </el-popover>
            <el-button class="ml-auto" type="primary" @click="editEConsult()">新增</el-button>
          </div>
          <el-table border stripe :loading="loading" :height="500" :data="list">
            <el-common-column prop="date" label="咨询时间" :min-width="120" />
            <el-common-column prop="project" label="意向项目" :min-width="160">
              <template #default="{ row: { projectNames } }">
                <el-tooltip :disabled="!projectNames" placement="top" effect="light" :content="projectNames" popper-class="max-w-480px">
                  <span>{{ projectNames }}</span>
                </el-tooltip>
              </template>
            </el-common-column>            <el-common-column prop="remark" label="报备说明" :min-width="240">
              <template #default="{ row: { reportDesc } }">
                <el-popover placement="top" width="400" trigger="click" :content="reportDesc" v-if="reportDesc">
                  <template #reference>
                    <div class="cursor-pointer whitespace-pre-wrap leading-5 max-h-10 overflow-hidden line-clamp-2 hover:text-blue-500 transition-colors" @click.stop>
                      {{ reportDesc }}
                    </div>
                  </template>
                  <div class="whitespace-pre-wrap text-14px leading-6 max-h-300px overflow-y-auto">
                    {{ reportDesc }}
                  </div>
                </el-popover>
                <div v-else class="text-gray-400">暂无说明</div>
              </template>
            </el-common-column>
            <el-common-column prop="source" label="信息来源" :min-width="100">
              <template #default="{ row: { sourceName } }">
                <el-tooltip placement="top" effect="light" :disabled="!sourceName || sourceName.length < 7" :content="sourceName">
                  <span>{{ sourceName }}</span>
                </el-tooltip>
              </template>
            </el-common-column>
            <el-common-column prop="register" label="登记人" :min-width="140">
              <template #default="{ row: { userId, registrantUserId } }">
                <ww-user v-if="registrantUserId || userId" :openid="registrantUserId || userId" />
                
              </template>
            </el-common-column>
            <el-common-column fixed="right" prop="action" label="操作" :width="120">
              <template #default="{ row }">
                <span class="cursor-pointer text-blue-500" @click="viewConsultDetail(row)">详情</span>
                <span class="cursor-pointer text-blue-500 ml-10px" @click="editEConsult(row)" v-if="isReportToday(row)">编辑</span>
              </template>
            </el-common-column>
          </el-table>
          <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize" @handle-current-change="changePage" />
        </div>
      </layout-main>
      <layout-item>
        <div text-center class="py-10px mt-1px bg-white" common-shadow--r>
          <el-button plain class="w-100px" type="primary" @click="close()">关闭</el-button>
        </div>
      </layout-item>
    </my-layout>  </el-drawer>
  <add-e-consult :customer="customer" :record="currentConsult" :visible="addEConsultVisible" :mode="consultMode" @close="closeAddConsult" @change="changeSuccess" />
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import { getEConsuleRecord, getFirstTriagePersonUserId, getFirstEConsuleRecord } from "@/api/consult";

import useElPagination from "@/hooks/useElPagination";
import { memberStore } from "@/store/member";

import addEConsult from "./add-e-consult.vue";
import elCommonColumn from "@/components/el-common-column/el-common-column.vue";
import customerHeader from "@/components/customer-header/customer-header.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import { ElMessage } from "element-plus";

const emits = defineEmits(["close", "success", "showCustomerDetail"]);
const props = defineProps({
  visible: { type: Boolean, default: false },
  width: { type: String, default: "500px" },
  customer: { type: Object, default: () => ({}) },
});
const addEConsultVisible = ref(false);
const currentConsult = ref({});
const consultMode = ref("edit"); // 新增：模式标识，edit 或 view

const { corpInfo } = storeToRefs(memberStore());
const { list, page, pageSize, changePage } = useElPagination(getList);
const introducerRecord = computed(() => {
  return Array.isArray(props.customer.introducerRecord) ? props.customer.introducerRecord : [];
});

const reportUserId = computed(() => {
  const record = Array.isArray(introducerRecord.value) && introducerRecord.value.length > 0 ? introducerRecord.value[introducerRecord.value.length - 1] : null;
  return record ? record.introducer : "";
});

const currentReportTime = computed(() => {
  const record = Array.isArray(introducerRecord.value) && introducerRecord.value.length > 0 ? introducerRecord.value[introducerRecord.value.length - 1] : null;
  return record ? dayjs(record.time).format("YYYY-MM-DD HH:mm") : "";
});

const loading = ref(false);
const total = ref(0);
const firstUerIds = ref("");
const firstRecord = ref(null);

async function getList() {
  loading.value = true;
  const { data, message, success } = await getEConsuleRecord({
    customerId: props.customer._id,
    page: page.value,
    pageSize: pageSize.value,
    corpId: corpInfo.value.corpId,
  });
  loading.value = false;
  list.value = Array.isArray(data.list)
    ? data.list.map((i) => ({
        ...i,
        projectNames: Array.isArray(i.projectNames) ? i.projectNames.join("、") : "",
        sourceName: Array.isArray(i.source) ? i.source.join("-") : "",
      }))
    : [];
  total.value = data.total > 0 ? data.total : 0;
  if (!success) ElMessage.error(message);
}

async function getFirstRecord() {
  const { data } = await getFirstEConsuleRecord({
    customerId: props.customer._id,
    corpId: corpInfo.value.corpId,
  });
  firstRecord.value = data.record ? data.record : null;
}

function changeSuccess() {
  changePage(1);
  emits("success");
}

function customerReload() {
  emits("success");
}

async function getFirstUserId() {
  firstUerIds.value = "";
  const { data } = await getFirstTriagePersonUserId({
    customerId: props.customer._id,
    corpId: corpInfo.value.corpId,
  });
  firstUerIds.value = data.data && data.data.receptionPersonUserId ? data.data.receptionPersonUserId : "";
}

const step2 = computed(() => {
  const inHospitalTimes = Array.isArray(props.customer.inHospitalTimes) ? props.customer.inHospitalTimes : [];
  const validVisitTimes = inHospitalTimes.filter((i) => i && dayjs(i).isValid()).sort();
  const res = { key: "step2", title: validVisitTimes[0] ? "已到院" : "未到院", checked: validVisitTimes[0] ? true : false };
  if (validVisitTimes[0]) {
    res.time = dayjs(validVisitTimes[0]).format("YYYY-MM-DD HH:mm");
    res.timeLabel = "首次到院时间：";
  }
  if (validVisitTimes[0] && firstUerIds.value) {
    res.userid = firstUerIds.value;
    res.label = "接待咨询：";
  }
  return res;
});

const steps = computed(() => {
  const list = [];
  const step1 = { key: "step1", title: firstRecord.value ? "报备成功" : "未报备", checked: Boolean(firstRecord.value) };
  if (step1.checked) {
    step1.time = dayjs(firstRecord.value.createTime).format("YYYY-MM-DD HH:mm");
    step1.timeLabel = "首次报备时间：";
    step1.label = "首次报备人：";
    step1.userid = firstRecord.value.userId;
  }
  list.push(step1, step2.value);
  const consumeRecords = Array.isArray(props.customer.consumeRecord) ? props.customer.consumeRecord : [];
  if (consumeRecords.length > 0) {
    // Get the most recent record for the date
    const latestRecord = consumeRecords[consumeRecords.length - 1];
    const latestDate = dayjs(latestRecord.createTime).format("YYYY-MM-DD");

    // Sum all consumeAmount values from the same day
    const totalAmount = consumeRecords.filter((record) => dayjs(record.createTime).format("YYYY-MM-DD") === latestDate).reduce((sum, record) => sum + (Number(record.consumeAmount) || 0), 0);

    list.push({
      key: "step3",
      title: "已成交",
      checked: true,
      time: dayjs(latestRecord.createTime).format("YYYY-MM-DD HH:mm"),
      timeLabel: "最近成交时间：",
      label: "成交金额",
      text: "￥" + totalAmount,
    });
  } else {
    list.push({ key: "step3", title: "未成交", checked: false });
  }
  return list;
});

function close() {
  emits("close");
}

function editEConsult(data = {}) {
  // 检查是否为最新记录
  const isLatestRecord = data._id && list.value.length > 0 && data._id === list.value[0]._id;
  currentConsult.value = {
    ...data,
    isLatestRecord, // 添加最新记录标识
  };
  consultMode.value = "edit";
  addEConsultVisible.value = true;
}

function viewConsultDetail(data) {
  currentConsult.value = { ...data };
  consultMode.value = "view";
  addEConsultVisible.value = true;
}

function isReportToday(row) {
  if (!row || !row.date) return false;
  const reportDate = dayjs(row.date).format("YYYY-MM-DD");
  const today = dayjs().format("YYYY-MM-DD");
  return reportDate === today;
}

function closeAddConsult() {
  addEConsultVisible.value = false;
}

function showCustomerDetail(customer) {
  emits("showCustomerDetail", customer._id);
}

watch(
  () => props.visible,
  (n) => {
    if (n) {
      changePage(1);
      getFirstUserId();
      getFirstRecord();
    }
  }
);
</script>