<template>
  <div class="report-container">
    <div class="report-header">
      <div class="report-actions">
        <div class="filter-section">
          <div class="filter-row">
            <div class="filter-item">
              <span class="filter-label">日期：</span>
              <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" :shortcuts="dateShortcuts" :disabledDate="disabledDate" @change="handleDateChange" />
            </div>
            <div class="filter-item">
              <check-box-filter v-model="consultantFilter" label="咨询师" :list="consultantList">
                <template #item="{ item }">
                  <ww-user :openid="item.value"></ww-user>
                </template>
              </check-box-filter>
            </div>
            <div class="filter-item">
              <span class="filter-label">成交状态：</span>
              <el-select v-model="dealStatusFilter" placeholder="请选择成交状态" clearable>
                <el-option label="已成交" :value="1" />
                <el-option label="未成交" :value="0" />
              </el-select>
            </div>
            <div class="filter-item">
              <filter-info-source v-model="infoSourceFilter" label="信息来源" />
            </div>
            <div class="filter-item">
              <filter-customer-source v-model="devPathFilter" label="开发渠道" />
            </div>
            <div class="filter-actions">
              <el-button type="primary" @click="handleSearch" :loading="loading" :disabled="loading">查询</el-button>
              <el-button type="success" @click="exportToExcel" :disabled="loading">导出Excel</el-button>
              <el-button @click="resetFilters" :disabled="loading">重置</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="table-container" v-loading="loading" element-loading-text="正在获取数据...">
      <table class="performance-table">
        <colgroup>
          <col style="width: 15%" />
          <!-- 咨询项目 -->
          <col style="width: 8%" />
          <!-- 咨询人次 -->
          <col style="width: 6%" />
          <!-- 占比 -->
          <col style="width: 8%" />
          <!-- 成交情况-成交人次 -->
          <col style="width: 8%" />
          <!-- 成交情况-成功率 -->
          <col style="width: 8%" />
          <!-- 成交情况-成交占比 -->
          <col style="width: 8%" />
          <!-- 未成交情况-未成交人次 -->
          <col style="width: 8%" />
          <!-- 未成交情况-未成交占比 -->
          <col style="width: 6%" />
          <!-- 初诊人次 -->
          <col style="width: 6%" />
          <!-- 初诊-成交人次 -->
          <col style="width: 6%" />
          <!-- 复诊人次 -->
          <col style="width: 6%" />
          <!-- 复诊-成交人次 -->
          <col style="width: 6%" />
          <!-- 再消费人次 -->
          <col style="width: 6%" />
          <!-- 再消费-成交人次 -->
          <col style="width: 8%" />
          <!-- 参考金额 -->
        </colgroup>
        <thead>
          <tr>
            <th rowspan="2">咨询项目</th>
            <th rowspan="2">咨询人次</th>
            <th rowspan="2">占比%</th>
            <th rowspan="2">成交人次</th>
            <th rowspan="2">成交率</th>
            <th rowspan="2">成交占比%</th>
            <th colspan="2">未成交情况</th>
            <th colspan="2">初诊人次</th>
            <th colspan="2">复诊人次</th>
            <th colspan="2">再消费人次</th>
            <th rowspan="2">参考金额</th>
          </tr>
          <tr>
            <th>未成交人次</th>
            <th>未成交占比%</th>
            <th>咨询人次</th>
            <th>成交人次</th>
            <th>咨询人次</th>
            <th>成交人次</th>
            <th>咨询人次</th>
            <th>成交人次</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in tableData" :key="index">
            <td>{{ item.projectName || "未知项目" }}</td>
            <td>{{ item.consultCount }}</td>
            <td>{{ formatPercentage(item.consultRatio) }}</td>
            <td>{{ item.successCount }}</td>
            <td>{{ formatPercentage(item.successRate) }}</td>
            <td>{{ formatPercentage(item.successRatio) }}</td>
            <td>{{ item.consultCount - item.successCount }}</td>
            <td>{{ formatPercentage(1 - item.successRate) }}</td>
            <td>{{ item.firstVisitCount }}</td>
            <td>{{ item.firstVisitSuccessCount || 0 }}</td>
            <td>{{ item.returnVisitCount }}</td>
            <td>{{ item.returnVisitSuccessCount || 0 }}</td>
            <td>{{ item.moreConsumedCount }}</td>
            <td>{{ item.moreConsumedSuccessCount || 0 }}</td>
            <td class="amount">{{ formatCurrency(item.successAmount) }}</td>
          </tr>
          <tr class="total-row">
            <td class="total-label">合计：</td>
            <td>{{ totalConsultCount }}</td>
            <td>{{ formatPercentage(1) }}</td>
            <td>{{ totalSuccessCount }}</td>
            <td>{{ formatPercentage(totalSuccessRate) }}</td>
            <td>{{ formatPercentage(1) }}</td>
            <td>{{ totalConsultCount - totalSuccessCount }}</td>
            <td>{{ formatPercentage(1 - totalSuccessRate) }}</td>
            <td>{{ totalFirstVisitCount }}</td>
            <td>{{ totalFirstVisitSuccessCount }}</td>
            <td>{{ totalReturnVisitCount }}</td>
            <td>{{ totalReturnVisitSuccessCount }}</td>
            <td>{{ totalMoreConsumedCount }}</td>
            <td>{{ totalMoreConsumedSuccessCount }}</td>
            <td class="amount">{{ formatCurrency(totalSuccessAmount) }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="footer-info">
      <span>共 {{ loading ? '...' : tableData.length }} 条记录</span>
      <span class="date-limit-info">* 查询限制为一个月内的数据</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { storeToRefs } from "pinia";
import { staffStore } from "@/store/staff";
import { ElDatePicker, ElButton, ElMessage, ElSelect, ElOption } from "element-plus";
import { getProjectStatistics } from "@/api/consult"; 
import { CheckBoxFilter, baseFilterItem, filterCustomerSource, filterInfoSource } from "@/components/filter-bar";

const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());

const consultantFilter = ref([]);
const infoSourceFilter = ref([]);
const devPathFilter = ref([]);
const departmentFilter = ref([]);
const receptionStatusFilter = ref([]);
const dealStatusFilter = ref(null);

const consultantList = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("consultant")).map((i) => ({ value: i.userid }));
});

// 日期筛选
const dateRange = ref([]);

// 日期快捷选项
const dateShortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近半个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "本月",
    value: () => {
      const end = new Date();
      const start = new Date(end.getFullYear(), end.getMonth(), 1);
      return [start, end];
    },
  },
];

// 禁用超过一个月范围的日期
const disabledDate = (time) => {
  if (!dateRange.value || dateRange.value.length === 0) {
    return false;
  }

  const startDate = new Date(dateRange.value[0]);
  const endDate = new Date(dateRange.value[1]);
  const currentDate = new Date(time);

  // 如果已选择了开始日期，限制结束日期不能超过开始日期后的31天
  if (dateRange.value.length === 1) {
    const oneMonthLater = new Date(startDate);
    oneMonthLater.setDate(startDate.getDate() + 31);
    return currentDate > oneMonthLater;
  }

  return false;
};

// 监听日期变化，确保不超过一个月
watch(dateRange, (newValue) => {
  if (newValue && newValue.length === 2) {
    const start = new Date(newValue[0]);
    const end = new Date(newValue[1]);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays > 31) {
      // 如果超过31天，将结束日期设置为开始日期后的31天
      const maxEnd = new Date(start);
      maxEnd.setDate(start.getDate() + 31);
      const formattedMaxEnd = formatDate(maxEnd);

      dateRange.value = [newValue[0], formattedMaxEnd];
      ElMessage.warning("查询范围已限制为一个月内");
    }
  }
});

// 格式化显示的日期范围
const formatDateRange = computed(() => {
  if (dateRange.value && dateRange.value.length === 2) {
    return `${dateRange.value[0]} 至 ${dateRange.value[1]}`;
  }
  return "全部日期";
});

// 格式化日期为YYYY-MM-DD
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  return `${year}-${month}-${day}`;
};

// 表格数据
const tableData = ref([]);
const projectMap = ref({}); // 存储项目ID与项目名称的映射
// 添加 loading 状态
const loading = ref(false);

// 格式化百分比
const formatPercentage = (value) => {
  if (value === undefined || value === null || isNaN(value)) {
    return "0.00%";
  }
  return `${(value * 100).toFixed(2)}%`;
};

async function fetchProjectStatistics() {
  loading.value = true;
  try {
    const { data, success, message } = await getProjectStatistics({
      startDate: dateRange.value && dateRange.value.length > 0 ? dateRange.value[0] : undefined,
      endDate: dateRange.value && dateRange.value.length > 0 ? dateRange.value[1] : undefined,
      consultantFilter: consultantFilter.value.length > 0 ? consultantFilter.value : undefined,
      infoSource: infoSourceFilter.value.length > 0 ? infoSourceFilter.value : undefined,
      devPath: devPathFilter.value.length > 0 ? devPathFilter.value : undefined,
      department: departmentFilter.value.length > 0 ? departmentFilter.value : undefined,
      receptionStatus: receptionStatusFilter.value.length > 0 ? receptionStatusFilter.value : undefined,
      dealStatus: dealStatusFilter.value !== null ? dealStatusFilter.value : undefined,
    });

    if (!success) {
      ElMessage.error(message || "获取数据失败");
      return;
    }

    // 计算总咨询人次，用于计算占比
    const total = data.data.reduce((sum, item) => sum + item.consultCount, 0);

    // 处理返回的数据，添加新的计算字段
    tableData.value = data.data.map((item) => {
      // 获取项目名称
      const projectName = projectMap.value[item.projectId] || "未知项目";

      // 计算咨询占比
      const consultRatio = total > 0 ? item.consultCount / total : 0;

      // 计算成交占比 (该项目成交人次/总成交人次)
      const totalSuccess = data.data.reduce((sum, i) => sum + i.successCount, 0);
      const successRatio = totalSuccess > 0 ? item.successCount / totalSuccess : 0;

      // 计算成功率 (该项目成交人次/该项目咨询人次)
      const successRate = item.consultCount > 0 ? item.successCount / item.consultCount : 0;

      // 计算初诊成交人次、复诊成交人次、再消费成交人次
      const firstVisitSuccessCount = item.firstVisitSuccessCount || 0;
      const returnVisitSuccessCount = item.returnVisitSuccessCount || 0;
      const moreConsumedSuccessCount = item.moreConsumedSuccessCount || 0;

      return {
        ...item,
        projectName,
        consultRatio,
        successRatio,
        successRate,
        firstVisitSuccessCount,
        returnVisitSuccessCount,
        moreConsumedSuccessCount,
      };
    });

    // 按咨询人次降序排序
    tableData.value.sort((a, b) => b.consultCount - a.consultCount);
  } catch (error) {
    console.error("获取数据异常:", error);
    ElMessage.error("获取数据异常");
  } finally {
    loading.value = false;
  }
}

// 处理日期变化
const handleDateChange = () => {
  // 当日期变化时自动触发查询
  if (dateRange.value && dateRange.value.length === 2) {
    handleSearch();
  }
};

// 处理查询
const handleSearch = () => {
  if (!dateRange.value || dateRange.value.length !== 2) {
    // 如果没有选择日期范围，不进行查询
    ElMessage.warning("请选择日期范围");
    return;
  }

  const start = new Date(dateRange.value[0]);
  const end = new Date(dateRange.value[1]);
  const diffTime = Math.abs(end - start);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays > 31) {
    ElMessage.warning("查询范围已限制为一个月内");
    return;
  }

  // 重新加载数据
  fetchProjectStatistics();
};

// 计算合计
const totalConsultCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + item.consultCount, 0);
});

const totalSuccessCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + item.successCount, 0);
});

const totalSuccessAmount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + item.successAmount, 0);
});

const totalSuccessRate = computed(() => {
  return totalConsultCount.value > 0 ? totalSuccessCount.value / totalConsultCount.value : 0;
});

const totalFirstVisitCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + item.firstVisitCount, 0);
});

const totalFirstVisitSuccessCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (item.firstVisitSuccessCount || 0), 0);
});

const totalReturnVisitCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + item.returnVisitCount, 0);
});

const totalReturnVisitSuccessCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (item.returnVisitSuccessCount || 0), 0);
});

const totalMoreConsumedCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + item.moreConsumedCount, 0);
});

const totalMoreConsumedSuccessCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (item.moreConsumedSuccessCount || 0), 0);
});

// 格式化货币
const formatCurrency = (value) => {
  if (value === undefined || value === null || isNaN(value)) {
    return "¥0.00";
  }
  return new Intl.NumberFormat("zh-CN", {
    style: "currency",
    currency: "CNY",
    minimumFractionDigits: 2,
  }).format(value);
};

// 导出Excel
const exportToExcel = () => {
  // 准备导出数据
  const exportData = [];

  // 添加报表标题和日期范围
  exportData.push(["咨询项目统计表"]);
  exportData.push([`统计日期：${formatDateRange.value}`]);

  // 添加查询条件信息
  exportData.push([
    `咨询师：${
      consultantFilter.value.length > 0
        ? consultantFilter.value
            .map((id) => {
              const staff = staffList.value.find((s) => s.userid === id);
              return staff ? staff.anotherName || staff.name : id;
            })
            .join(", ")
        : "全部"
    }`,
  ]);

  // 空行
  exportData.push([]);

  // 添加表头
  exportData.push(["咨询项目", "咨询人次", "占比%", "成交人次", "成交率", "成交占比%", "未成交人次", "未成交占比%", "初诊人次", "成交人次", "复诊人次", "成交人次", "再消费人次", "成交人次", "参考金额"]);
  exportData.push(["", "", "", "", "", "", "", "", "咨询人次", "成交人次", "咨询人次", "成交人次", "咨询人次", "成交人次", ""]);

  // 添加数据行
  tableData.value.forEach((item) => {
    exportData.push([item.projectName || "未知项目", item.consultCount, (item.consultRatio * 100).toFixed(2) + "%", item.successCount, (item.successRate * 100).toFixed(2) + "%", (item.successRatio * 100).toFixed(2) + "%", item.consultCount - item.successCount, ((1 - item.successRate) * 100).toFixed(2) + "%", item.firstVisitCount, item.firstVisitSuccessCount || 0, item.returnVisitCount, item.returnVisitSuccessCount || 0, item.moreConsumedCount, item.moreConsumedSuccessCount || 0, item.successAmount]);
  });

  // 添加总计行
  exportData.push(["合计", totalConsultCount.value, "100.00%", totalSuccessCount.value, (totalSuccessRate.value * 100).toFixed(2) + "%", "100.00%", totalConsultCount.value - totalSuccessCount.value, ((1 - totalSuccessRate.value) * 100).toFixed(2) + "%", totalFirstVisitCount.value, totalFirstVisitSuccessCount.value, totalReturnVisitCount.value, totalReturnVisitSuccessCount.value, totalMoreConsumedCount.value, totalMoreConsumedSuccessCount.value, totalSuccessAmount.value]);

  // 创建工作簿和工作表
  const wb = XLSX.utils.book_new();
  const ws = XLSX.utils.aoa_to_sheet(exportData);

  // 设置列宽
  const colWidths = [
    { wch: 20 }, // 咨询项目
    { wch: 10 }, // 咨询人次
    { wch: 8 }, // 占比
    { wch: 10 }, // 成交人次
    { wch: 10 }, // 成功率
    { wch: 10 }, // 成交占比
    { wch: 10 }, // 未成交人次
    { wch: 10 }, // 未成交占比
    { wch: 10 }, // 初诊-咨询人次
    { wch: 10 }, // 初诊-成交人次
    { wch: 10 }, // 复诊-咨询人次
    { wch: 10 }, // 复诊-成交人次
    { wch: 10 }, // 再消费-咨询人次
    { wch: 10 }, // 再消费-成交人次
    { wch: 12 }, // 参考金额
  ];

  ws["!cols"] = colWidths;

  // 合并单元格
  ws["!merges"] = [
    { s: { r: 0, c: 0 }, e: { r: 0, c: 14 } }, // 标题行
    { s: { r: 1, c: 0 }, e: { r: 1, c: 14 } }, // 日期行
    { s: { r: 2, c: 0 }, e: { r: 2, c: 14 } }, // 咨询师行
    { s: { r: 4, c: 0 }, e: { r: 5, c: 0 } }, // 咨询项目
    { s: { r: 4, c: 1 }, e: { r: 5, c: 1 } }, // 咨询人次
    { s: { r: 4, c: 2 }, e: { r: 5, c: 2 } }, // 占比
    { s: { r: 4, c: 3 }, e: { r: 5, c: 3 } }, // 成交人次
    { s: { r: 4, c: 4 }, e: { r: 5, c: 4 } }, // 成交率
    { s: { r: 4, c: 5 }, e: { r: 5, c: 5 } }, // 成交占比
    { s: { r: 4, c: 6 }, e: { r: 5, c: 6 } }, // 未成交人次
    { s: { r: 4, c: 7 }, e: { r: 5, c: 7 } }, // 未成交占比
    { s: { r: 4, c: 8 }, e: { r: 4, c: 9 } }, // 初诊人次
    { s: { r: 4, c: 10 }, e: { r: 4, c: 11 } }, // 复诊人次
    { s: { r: 4, c: 12 }, e: { r: 4, c: 13 } }, // 再消费人次
    { s: { r: 4, c: 14 }, e: { r: 5, c: 14 } }, // 参考金额
  ];

  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(wb, ws, "咨询项目统计");

  // 生成Excel文件并下载
  const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
  const blob = new Blob([excelBuffer], {
    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  });

  // 使用当前日期作为文件名的一部分
  const now = new Date();
  const fileName = `咨询项目统计_${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}.xlsx`;

  saveAs(blob, fileName);

  ElMessage.success("导出Excel成功");
};

// 获取项目数据
async function fetchProjectList() {
  try {
    // 此处应调用获取项目列表的API，将结果保存到projectMap中
    // 模拟数据
    projectMap.value = {
      e4i4j7xq44hwhf62i56qmrsu1745486655074: "牙正",
      jajblfpwalbaqiuirp8r3sqn1745486558597: "牙冠",
      // 其他项目...
    };
  } catch (error) {
    console.error("获取项目列表异常:", error);
  }
}

onMounted(async () => {
  if (staffList.value.length === 0) await getStaffList();
  await fetchProjectList(); // 获取项目列表

  // 设置默认日期范围为本月
  const end = new Date();
  const start = new Date(end.getFullYear(), end.getMonth(), 1);

  dateRange.value = [formatDate(start), formatDate(end)];

  // 调用API获取数据
  fetchProjectStatistics();
});

// 添加重置筛选器函数
const resetFilters = () => {
  dateRange.value = [];
  consultantFilter.value = [];
  infoSourceFilter.value = [];
  devPathFilter.value = [];
  departmentFilter.value = [];
  receptionStatusFilter.value = [];
  dealStatusFilter.value = null;
  handleSearch();
};

// 添加监听筛选器变化
watch(
  [consultantFilter, infoSourceFilter, devPathFilter, departmentFilter, receptionStatusFilter, dealStatusFilter],
  () => {
    // 当筛选条件变化时自动触发查询
    handleSearch();
  },
  { deep: true }
);
</script>

<style scoped>
.report-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
  background-color: #f5f7fa;
  overflow: hidden;
  min-width: 1200px;
}

.report-header {
  background-color: white;
  padding: 15px 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
}

.filter-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
  margin-bottom: 10px;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-label {
  white-space: nowrap;
  margin-right: 5px;
  font-size: 14px;
  color: #606266;
}

.filter-actions {
  margin-left: auto;
  display: flex;
  gap: 10px;
}

:deep(.el-date-editor.el-input__wrapper) {
  width: 350px;
}

.table-container {
  flex: 1;
  margin: 15px 0 0 0;
  background-color: white;
  overflow: auto;
  position: relative;
  border-top: 1px solid #ebeef5;
  border-bottom: 1px solid #ebeef5;
}

.performance-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  table-layout: fixed;
}

.performance-table th,
.performance-table td {
  border: 1px solid #ebeef5;
  padding: 12px 8px;
  text-align: center;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.performance-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 10;
}

.performance-table .amount {
  text-align: right;
  padding-right: 10px;
  min-width: 100px;
  max-width: 120px;
  white-space: nowrap;
  overflow: visible;
}

.total-row {
  background-color: #ebeef5;
  font-weight: bold;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.total-label {
  text-align: right;
  padding-right: 10px;
}

.footer-info {
  background-color: white;
  padding: 10px 20px;
  margin: 0;
  display: flex;
  justify-content: space-between;
  color: #606266;
  font-size: 14px;
  flex-shrink: 0;
  border-top: 1px solid #ebeef5;
}

.date-limit-info {
  color: #e6a23c;
  font-style: italic;
}

.performance-table th[rowspan="2"] {
  vertical-align: middle;
}

@media print {
  .report-header {
    display: none;
  }

  .table-container {
    margin: 0;
    border: none;
    overflow: visible;
    height: auto;
  }

  .performance-table th,
  .performance-table td {
    font-size: 12px;
    padding: 8px 4px;
  }
}

@media (min-width: 1400px) {
  .performance-table .amount {
    min-width: 120px;
    max-width: 150px;
  }
}
</style>
