<template>
  <filter-item label="来源" :text="text">
    <customer-source-popover v-model="customerSource" ref="popoverRef" />
  </filter-item>
</template>
<script setup>
import { computed, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { configStore } from "@/store/config";
import FilterItem from './filter-item.vue';
import customerSourcePopover from './customer-source-popover.vue';

const config = configStore();

const { getStage } = config;
const { stageList } = storeToRefs(config);
const customerSource = ref([]);
const popoverRef = ref();
const text = computed(() => customerSource.value && customerSource.value.length ? `已选${customerSource.value.length}项` : '');

function getParam() {
  return customerSource.value.length ? { customerSource: customerSource.value } : {}
}

function reset() {
  customerSource.value = []
  popoverRef.value && popoverRef.value.reset()
}

defineExpose({
  getParam,
  reset
})
</script>
<style lang="scss" scoped></style>
