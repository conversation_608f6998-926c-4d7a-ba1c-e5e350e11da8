<template>
  <el-table-column label="备注" prop="notes" :width="200">
    <template #default="{row}">
      <td-wrapper>
        <div class="absolute inset-0 flex items-center group">
          <el-tooltip class="box-item" effect="light" :disabled="!row.notes" :content="row.notes" placement="right"
            offset="30">
            <div class="line-clamp-2">{{ row.notes || '-' }}</div>
            <template #content>
              <el-scrollbar wrap-style="max-height:40vh">
                <div class="max-w-50vh text-14px leading-20px break-all p-5px">{{ row.notes }}</div>
              </el-scrollbar>
            </template>
          </el-tooltip>
          <div
            class="flex-shrink-0 hidden translate-y-4px transform ml-5px text-blue-500 cursor-pointer group-hover:block"
            @click="editNotes(row)">
            <el-icon size="16">
              <EditPen />
            </el-icon>
          </div>
        </div>
      </td-wrapper>
    </template>
  </el-table-column>
</template>
<script setup>
import tdWrapper from './td-wrapper.vue';
const emits = defineEmits(['cellClick'])
function editNotes(data) {
  emits('cellClick', { type: 'editNotes', data })
}
</script>
<style lang="scss" scoped></style>
