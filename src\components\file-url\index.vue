<template>
  <div>
    <el-dialog :model-value="visible" title="添加地址链接" draggable :width="480">
      <div class="py-20px flex" justify-center>
        <el-input style="width: 400px" v-model="url" placeholder="请填写地址链接" />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogCancel">取消</el-button>
          <el-button type="primary" @click="dialogSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import { ref, watch } from "vue";
import { getPageTitleAndContent } from "@/api/wecom";
import { ElMessage } from "element-plus";
const emit = defineEmits(["cancel", "submit"]);
const props = defineProps({
  visible: { type: Boolean, default: false },
});
const url = ref("");
function dialogCancel() {
  url.value = "";
  emit("cancel");
}

watch(
  () => props.visible,
  (val) => {
    if (!val) {
      url.value = "";
    }
  }
);

async function dialogSubmit() {
  //url 去除两边空格
  let { data, success, message } = await getPageTitleAndContent(url.value.trim());
  if (!success) {
    ElMessage.error(message);
    return;
  }
  let { images, subtitle, weixinTitle, title } = data.data;
  if (!weixinTitle && !title) {
    ElMessage.error("无法获取链接信息");
    return;
  }
  if (subtitle.includes("因网络连接问题，剩余内容暂无法加载")) {
    subtitle = "";
  }
  const obj = {
    URL: images,
    type: "link",
    file: {
      name: title || weixinTitle,
      subtitle,
      url: url.value,
    },
  };
  emit("submit", obj);
}
</script>
<style lang="scss"></style>