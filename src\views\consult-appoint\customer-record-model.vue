<template>
  <el-dialog :model-value="props.visible" :width="width" title="咨询记录" @close="close()">
    <el-scrollbar wrap-class="el-dialog-body-full-scroll" class="mx-10px">
      <el-form-item label="接诊时间:" :label-width="90">
        <div>{{ consultRecord.receptionTimeStr }}</div>
      </el-form-item>
      <el-form-item label="成交状态:" class="el-form-item--autoHeight" :label-width="90">
        <div>{{ consultRecord.dealStatus }}</div>
      </el-form-item>
      <el-form-item label="咨询项目:" class="el-form-item--autoHeight" :label-width="90">
        <div>{{projectNames}}</div>
      </el-form-item>
      <el-form-item label="接诊人:" class="el-form-item--autoHeight" :label-width="90">
        <div>
          <ww-user :openid="consultRecord.receptionPersonUserId"></ww-user>
        </div>
      </el-form-item>
      <el-form-item label="客户主诉:" class="el-form-item--autoHeight" :label-width="90">
        <div>{{ consultRecord.customerComplaint }}</div>
      </el-form-item>
      <el-form-item label="处理方案:" class="el-form-item--autoHeight" :label-width="90">
        <div>{{ consultRecord.dealPlan }}</div>
      </el-form-item>
      <el-form-item label="未成交原因:" :label-width="90">
        <div>{{ consultRecord.noDealReason }}</div>
      </el-form-item>
    </el-scrollbar>
  </el-dialog>
</template>
<script setup>
import dayjs from "dayjs";
import WwUser from "@/components/ww-user/index.vue";
import { computed } from "vue";
const emits = defineEmits(["close", "success"]);

const props = defineProps({
  visible: { type: Boolean, default: false },
  width: { type: String, default: "500px" },
  consultRecord: { type: Object, default: () => ({}) },
});
const projectNames = computed(() => {
  return props.consultRecord.projectNames?.join("、") || ""
})

function close() {
  emits("close");
}
</script>

<style lang="scss" scoped></style>