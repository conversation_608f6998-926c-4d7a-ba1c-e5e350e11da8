import { computed, ref } from "vue";
import { defineStore } from "pinia";
import { memberStore } from "@/store/member";
import { ElMessage, ElMessageBox } from "element-plus";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import { memberMangePlanStore } from "@/store/managePlan/memberMangePlan";
const { getMemberManagementPlanFnc, getPlanTaskFnc } = memberMangePlanStore();
import { removePlanTask as removePlanTaskUrl, updatePlanTask as updatePlanTaskUrl, createPlanTask, getPlanTask } from "@/api/managementPlan";
export const taskStore = defineStore("task", () => {
  const planExecutionTime = ref("");
  const operationType = ref("add");
  const taskForm = ref({});
  async function removeTask(taskId, planId) {
    const { success, message } = await removePlanTaskUrl(taskId, planId);
    if (success) {
      getMemberManagementPlanFnc();
      return true;
    } else {
      ElMessage.error(message);
    }
  }
  async function createTask(params) {
    const { success, message } = await createPlanTask(params);
    if (success) {
      await getPlanTaskFnc();
      return true;
    } else {
      ElMessage.error(message);
      return false;
    }
  }
  async function updatePlanTask(id = "", params) {
    const { success, message } = await updatePlanTaskUrl(id, params);
    if (success) {
      getPlanTaskFnc();
      return true;
    } else {
      ElMessage.error(message);
    }
  }

  return {
    removeTask,
    createTask,
    updatePlanTask,
    taskForm,
    planExecutionTime,
    operationType,
  };
});
