import { computed, ref, watch } from 'vue'
import dayjs from 'dayjs';
import isoWeek from "dayjs/plugin/isoWeek";

dayjs.extend(isoWeek);

const _list = [
  { label: '全部', value: 'all' },
  { label: '今日', value: 'today' },
  { label: '昨日', value: 'yesterday' },
  { label: '本周', value: 'thisWeek' },
  { label: '上周', value: 'lastWeek' },
  { label: '本月', value: 'thisMonth' },
  { label: '上月', value: 'lastMonth' },
  { label: '今年', value: 'thisYear' }
]

export default function useDateRange(labels = _list.map(i => i.value)) {
  const dates = ref([]);
  const list = _list.filter(i => labels.includes(i.value));
  const today = ref(dayjs());
  const ruler = computed(() => ({
    today: today.value.format('YYYY-MM-DD'),
    yesterday: today.value.subtract(1, 'day').format('YYYY-MM-DD'),
    startOfWeek: today.value.startOf('isoWeek').format('YYYY-MM-DD'),
    endOfWeek: today.value.endOf('isoWeek').format('YYYY-MM-DD'),
    startOfLastWeek: today.value.subtract(1, 'week').startOf('isoWeek').format('YYYY-MM-DD'),
    endOfLastWeek: today.value.subtract(1, 'week').endOf('isoWeek').format('YYYY-MM-DD'),
    startOfMonth: today.value.startOf('month').format('YYYY-MM-DD'),
    endOfMonth: today.value.endOf('month').format('YYYY-MM-DD'),
    startOfLastMonth: today.value.subtract(1, 'month').startOf('month').format('YYYY-MM-DD'),
    endOfLastMonth: today.value.subtract(1, 'month').endOf('month').format('YYYY-MM-DD'),
    startOfYear: today.value.startOf('year').format('YYYY-MM-DD'),
    endOfYear: today.value.endOf('year').format('YYYY-MM-DD')
  }))
  const active = ref(list[0].value);
  function toggle(key) {
    if (active.value === key) return;
    dates.value = getDates(key);
  }

  function getDates(key) {
    if (key === 'all') return [];
    if (key === 'today') return [ruler.value.today, ruler.value.today];
    if (key === 'yesterday') return [ruler.value.yesterday, ruler.value.yesterday];
    if (key === 'thisWeek') return [ruler.value.startOfWeek, ruler.value.today];
    if (key === 'lastWeek') return [ruler.value.startOfLastWeek, ruler.value.endOfLastWeek];
    if (key === 'thisMonth') return [ruler.value.startOfMonth, ruler.value.today];
    if (key === 'lastMonth') return [ruler.value.startOfLastMonth, ruler.value.endOfLastMonth];
    if (key === 'thisYear') return [ruler.value.startOfYear, ruler.value.endOfYear];
  }

  watch(dates, n => {
    today.value = dayjs();
    if (n.length === 0 && active.value !== 'all') active.value = 'all';
    else if (n[0] === ruler.value.today && n[1] === ruler.value.today && active.value !== 'today') active.value = 'today';
    else if (n[0] === ruler.value.yesterday && n[1] === ruler.value.yesterday && active.value !== 'yesterday') active.value = 'yesterday';
    else if (n[0] === ruler.value.startOfWeek && n[1] === ruler.value.endOfWeek && active.value !== 'thisWeek') active.value = 'thisWeek';
    else if (n[0] === ruler.value.startOfLastWeek && n[1] === ruler.value.endOfLastWeek && active.value !== 'lastWeek') active.value = 'lastWeek';
    else if (n[0] === ruler.value.startOfMonth && n[1] === ruler.value.endOfMonth && active.value !== 'thisMonth') active.value = 'thisMonth';
    else if (n[0] === ruler.value.startOfLastMonth && n[1] === ruler.value.endOfLastMonth && active.value !== 'lastMonth') active.value = 'lastMonth';
    else if (n[0] === ruler.value.startOfYear && n[1] === ruler.value.endOfYear && active.value !== 'thisYear') active.value = 'thisYear';
    else active.value = ''

  })

  return { active, dates, list, toggle }
} 
