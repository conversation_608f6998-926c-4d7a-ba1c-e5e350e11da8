import { ref } from 'vue';
import useModal from '@/hooks/useModal';
import { isURL } from '@/utils/common';


export default function usePreviewArticle() {
  const { close, show, visible, width } = useModal(); //  选择客户弹窗
  const content = ref('')
  const articleTitle = ref('')
  function preview(article) {
    if (isURL(article.link)) window.open(article.link, '_target');
    else if (article.content) {
      content.value = getIframeContent(article.content);
      articleTitle.value = article.title;
      show()
    }
  }

  function getIframeContent(content) {
    return `<!DOCTYPE html>
  <html lang="en">
  <head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Embedded HTML</title>
  <style>
  img{
    max-width:100%;
  }
  /* 定义滚动条的样式 */
  ::-webkit-scrollbar {
      width: 4px; /* 滚动条的宽度 */
  }
  
  /* 定义滚动条轨道的样式 */
  ::-webkit-scrollbar-track {
      background-color: #eee; /* 滚动条轨道的背景颜色 */
  }
  
  /* 定义滚动条滑块的样式 */
  ::-webkit-scrollbar-thumb {
      background-color: #aaa; /* 滑块的背景颜色 */
      border-radius: 2px; /* 滑块的圆角 */
  }
  
  /* 定义滑块在被激活或鼠标悬停时的样式 */
  ::-webkit-scrollbar-thumb:hover {
      background-color: #555; /* 滑块的背景颜色 */
  }
  </style>
  </head>
  <body>
  
  ${content}
  
  </body>
  </html>
  `
  }

  return { content, close, preview, visible, width, articleTitle }
}