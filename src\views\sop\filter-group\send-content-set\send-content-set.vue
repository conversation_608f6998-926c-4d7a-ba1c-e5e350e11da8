<template>
  <div class="">
    <el-input ref="textareaRef" v-model="data.sendContent" show-word-limit type="textarea" :placeholder="placeholder" :autosize="{ minRows, maxRows }" :maxlength="maxlength" />
    <!-- <div class="mt-10px">
      <span class="text-14px text-gray-500">昵称：</span>
      <el-button text type="primary" size="small" :icon="Link" @click="insertNickName">插入客户昵称</el-button>
    </div> -->
    <div class="mt-10px flex">
      <span class="text-14px flex-shrink-0 text-gray-500">附件：</span>
      <div class="-mt-4px">
        <el-button v-if="!isTodo" plain class="add-btn" type="primary" size="small" :loading="fileLoading" :icon="Plus" @click="chooseFile('image/*')">图片</el-button>
        <!-- <el-button plain class="add-btn" type="primary" size="small" :loading="fileLoading" :icon="Plus"
                      @click="chooseFile('video/*')">视频</el-button> -->
        <el-button v-if="!isTodo" plain class="add-btn" type="primary" size="small" :icon="Plus" @click="addFileUrl">链接</el-button>
        <el-button plain class="add-btn" type="primary" size="small" :icon="Plus" @click="addArticle('article')">宣教文章</el-button>
        <el-button plain class="add-btn" type="primary" size="small" :icon="Plus" @click="addArticle('questionnaire')">问卷</el-button>
        <!-- <el-button plain class="add-btn" type="primary" size="small" :icon="Plus"
          @click="showMiniProgram">小程序</el-button> -->
        <!-- <el-button v-if="!isTodo" plain class="add-btn" type="primary" size="small" :icon="Plus" @click="showLink">建档链接</el-button> -->
      </div>
    </div>
    <div v-if="showFileList.length > 0" class="mt-10px w-1/2">
      <div v-for="(i, idx) in showFileList" :key="idx" class="flex items-center py-6px px-12px overflow-hidden bg-gray-100 rounded-8px" :class="idx > 0 ? 'mt-10px' : ''">
        <div color-normal class="flex-grow h-24px leading-24px w-0 truncate">
          <span v-if="i.typeStr" class="font-semibold text-14px">{{ i.typeStr }}</span>
          <span v-if="i.file && i.file.name">{{ i.file.name }}</span>
        </div>
        <el-icon :size="16" @click="removeFile(idx)" class="pointer">
          <CloseBold />
        </el-icon>
      </div>
    </div>
    <div v-if="data.archiveLink" class="mt-10px w-1/2">
      <div class="flex items-center py-6px px-12px overflow-hidden bg-gray-100 rounded-8px">
        <div color-normal class="flex-grow h-24px leading-24px w-0 truncate">
          <span class="font-semibold text-14px">【建档】</span>
          <span>{{ data.archiveLink.name }}</span>
        </div>
        <el-icon :size="16" @click="clearArchiveLink()" class="pointer">
          <CloseBold />
        </el-icon>
      </div>
    </div>
    <div v-if="data.miniProgram" class="mt-10px w-1/2">
      <div class="flex items-center py-6px px-12px overflow-hidden bg-gray-100 rounded-8px">
        <div color-normal class="flex-grow h-24px leading-24px w-0 truncate">
          <span class="font-semibold text-14px">【小程序】</span>
          <span>{{ data.miniProgram.name }}</span>
        </div>
        <el-icon :size="16" @click="clearMiniProgram()" class="pointer">
          <CloseBold />
        </el-icon>
      </div>
    </div>
    <input ref="fileInputRef" class="fixed -z-1 w-0 h-0 invisible" type="file" :accept="accept" @change="handleSelect($event)" />
    <file-url :visible="fileUrlVisible" @cancel="fileUrlVisible = false" @submit="addLink"></file-url>
    <add-file ref="addFileRefs" @onGetSelectFile="getSelectFile"></add-file>
    <archive-link-modal :corpUserId="[userId]" :link="data.archiveLink" :visible="linkVisible" :width="linkWidth" @close="closeLink" @change="changeArchiveLink" />
    <mini-program-modal :visible="miniProgramVisible" :miniProgram="data.miniProgram" @close="closeMiniProgram" :width="miniWidth" @change="($event) => (data.miniProgram = $event)" />
  </div>
</template>
<script setup>
import { computed, nextTick, ref, onMounted, onUnmounted } from "vue";
import { useVModel } from "@vueuse/core";
import { ElMessage } from "element-plus";
import { updateFile } from "@/api/uploadFIle.js";
import useModal from "@/hooks/useModal";
import { Plus, Link } from "@element-plus/icons-vue";
import addFile from "@/views/pannedEvent/components/addFile.vue";
import archiveLinkModal from "./archive-link-modal.vue";
import fileUrl from "@/components/file-url";
import miniProgramModal from "./mini-program-modal.vue";

const emit = defineEmits(["update:modelValue"]);
const props = defineProps({
  maxRows: { type: Number, default: 8 },
  maxlength: { type: Number, default: 1000 },
  minRows: { type: Number, default: 6 },
  placeholder: { type: String, default: "请输入" },
  modelValue: { type: Object, default: () => ({}) }, //  {min: '', max: ''}
});
const data = useVModel(props, "modelValue", emit);
const textareaRef = ref();
const fileInputRef = ref();
const addFileRefs = ref(null);
const accept = ref("");
const fileLoading = ref(false);
const fileUrlVisible = ref(false);
const { show: showLink, close: closeLink, visible: linkVisible, width: linkWidth } = useModal();
const { show: showMiniProgram, close: closeMiniProgram, visible: miniProgramVisible, width: miniWidth } = useModal();
const isTodo = computed(() => data.value.executeMethod === "todo");

const userId = ref("");
const fileList = computed({
  get() {
    return Array.isArray(data.value.fileList) ? data.value.fileList : [];
  },
  set(val) {
    data.value.fileList = val;
  },
});
const showFileList = computed(() => {
  return fileList.value.map((i) => {
    let typeStr = "";
    const type = i.type;
    const fileType = i.file && typeof i.file.type === "string" ? i.file.type : "";
    if (fileType.includes("video")) typeStr = "【视频】";
    else if (fileType.includes("image") || type === "image") typeStr = "【图片】";
    else if (fileType === "article") typeStr = "【文章】";
    else if (fileType === "questionnaire") typeStr = "【问卷】";
    else if (type === "link") typeStr = "【链接】";
    return { ...i, typeStr };
  });
});

function addArticle(name) {
  addFileRefs.value.open(name, {});
}
function addFileUrl() {
  if (fileList.value.length >= 9) {
    ElMessage.info("最多上传9个附件");
    return;
  }
  fileUrlVisible.value = !fileUrlVisible.value;
}
function addLink(obj) {
  if (obj.file.name.length > 30) {
    ElMessage.info("链接标题字数不得大于30字");
    return;
  }
  fileList.value = [...fileList.value, obj];
  fileUrlVisible.value = false;
}
function changeArchiveLink(link) {
  data.value.archiveLink = link;
}

function clearArchiveLink() {
  data.value.archiveLink = "";
}

async function chooseFile(type) {
  if (fileList.value.length >= 9) {
    ElMessage.info("最多上传9个附件");
    return;
  }
  accept.value = type;
  await nextTick();
  fileInputRef.value.click();
}

function getSelectFile(file) {
  if (fileList.value.length >= 9) {
    ElMessage.info("最多上传9个附件");
    return;
  }
  const { url, name, summary = "", type, surveryId } = file;
  const item = {
    URL: "https://796f-youcan-clouddev-1-8ewcqf31dbb2b5-1317294507.tcb.qcloud.la/other/19-%E9%97%AE%E5%8D%B7.png?sign=55a4cd77c418b2c548b65792a2cf6bce&t=1701328694",
    type: "link",
    file: {
      name,
      type: type,
      subtitle: summary,
      url,
    },
  };
  if (type === "questionnaire") item.file.surveryId = surveryId;
  if (isTodo.value) {
    fileList.value = [item];
  } else {
    fileList.value = [...fileList.value, item];
  }
}

async function handleSelect(e) {
  const [file] = e.target.files;
  e.target.value = "";
  if (!file) return;
  const fileTypeName = accept.value === "image/*" ? "图片" : "视频";
  const type = accept.value === "image/*" ? "image" : "video";
  if (fileList.value.some((i) => i.file.name === file.name)) {
    ElMessage.info("附件已存在");
    return;
  }
  if (file.size > 10485760) {
    ElMessage.info(`${fileTypeName}大小不能超过10MB`);
    return;
  }
  const { name } = file;
  fileLoading.value = true;
  let { media_id, success, message } = await updateFile(name, file, localStorage.getItem("corpId"), type, "mediaId");
  fileLoading.value = false;
  if (!success) {
    ElMessage.error(message);
    return;
  }
  const item = {
    type,
    URL: URL.createObjectURL(file),
    file: {
      name,
    },
    mediaId: media_id,
  };
  fileList.value = [...fileList.value, item];
}
async function insertNickName() {
  const textarea = textareaRef.value.$refs.textarea;
  const start = textarea.selectionStart; // 获取光标起始位置
  const newContent = (data.value.sendContent || "") + "[微信昵称]";
  if (newContent.length > 1000) {
    ElMessage.error("欢迎语内容不能超过1000字");
    return;
  } else {
    if (start >= 0) {
      const text = data.value.sendContent || "";
      const before = text.substring(0, start);
      const after = text.substring(start);
      const newText = before + "[微信昵称]" + after;
      data.value.sendContent = newText;
    } else {
      data.value.sendContent = newContent;
    }
  }
  await nextTick();
  start >= 0 ? textarea.setSelectionRange(start + 6, start + 6) : "";
  textareaRef.value.focus();
}

function removeFile(idx) {
  data.value.fileList.splice(idx, 1);
}
function clearMiniProgram() {
  data.value.miniProgram = "";
}

onMounted(() => (userId.value = localStorage.getItem("userId")));
onUnmounted(() => {
  data.value.sendContent = "";
  data.value.fileList = [];
  data.value.archiveLink = "";
  data.value.miniProgram = "";
});
</script>