<template>
    <div class="flex flex-wrap">
        <customer-tag v-for="tagId in (tagIds || [])" :key="idx" :tag-id="tagId" />
        <el-button type="primary" size="small" :icon="Plus" plain @click="changeTag()"
            v-if="!isRead">添加标签</el-button>
    </div>

    <tag-drawer ref="tagDrawerRef" :mult="true" @get-select-tag="onTagChanged($event)"></tag-drawer>
</template>
<script setup>
import TagDrawer from "@/components/tag-drawer/index.vue";
import customerTag from "@/components/customer-tag/customer-tag.vue";
import { ref, watch } from "vue";
const $emit = defineEmits(["change"]);
const props = defineProps({
  tagIds: {
    type: Array,
    default: [],
  },
  isRead: {
    type: Boolean,
    default: false,
  },
});

const modelValue = ref([]);
watch(
  () => props.tagIds,
  (item) => {
    modelValue.value = item;
  }
);

const tagDrawerRef = ref();
function changeTag() {
  tagDrawerRef.value.openDialog(Array.isArray(props.tagIds) ? props.tagIds : []);
}
function onTagChanged(value) {
  $emit("change", {
    title: "tagIds",
    value,
  });
}
</script>

<style></style>