<template>
  <div class="pl-10px border-b border-gray-200 -mt-15px py-10px">
    <el-radio v-model="tagType" class="flex" label="append">保留客户已有标签并新增新的标签。（标签重复只保留一个）</el-radio>
    <el-radio v-model="tagType" class="flex" label="cover">清空客户已有标签并新增新的标签。</el-radio>
  </div>
  <el-scrollbar wrap-style="max-height:30vh">
    <el-form class="pt-10px pl-10px" label-suffix="：">
      <el-form-item v-for="(group, idx) in tags" :key="idx" class="el-form-item--autoheight mb-0">
        <template #label>
          <div class="w-max-90px">{{ group.groupName }}：</div>
        </template>
        <el-tag v-for="item in group.tag" :key="item.id" pointer size="large" class="tag__item"
          :type="selectMap[item.id] ? 'primary' : 'info'" :effect="selectMap[item.id] ? 'light' : 'plain'"
          @click="toggle(item)">
          {{ item.name }}
        </el-tag>
      </el-form-item>
    </el-form>
  </el-scrollbar>
</template>
<script setup>
import { computed } from 'vue';
import { storeToRefs } from 'pinia';
import { useVModels } from '@vueuse/core';
import { configStore } from "@/store/config";
const { tags } = storeToRefs(configStore());

const props = defineProps({
  tagIds: { type: Array, default: () => [] },
  tagType: { type: String, default: '' }
})
const emit = defineEmits(['update:tagIds', 'update:tagType'])
const { tagIds, tagType } = useVModels(props, emit)

const selectMap = computed(() => tagIds.value.reduce((acc, cur) => {
  acc[cur] = true;
  return acc;
}, {}))


function toggle(item) {
  if (selectMap.value[item.id]) {
    tagIds.value = tagIds.value.filter(id => id !== item.id)
  } else {
    tagIds.value.push(item.id)
  }
}
</script>
<style scoped>
.tag__item {
  margin-right: 15px;
  margin-bottom: 15px;
  padding: 6px 10px;
  min-width: 80px;
}
</style>
