<template>
  <el-form-item class="px-15px py-6px el-form-item--autoheight" :class="required ? 'is-required' : ''" :label="name"
    border-bottom>
    <el-select style="width: 100%;" filterable remote reserve-keyword :multiple="mult" :model-value="value"
      :remote-method="searchDisease" @change="change($event)" :placeholder="placeholder">
      <el-option v-for="option in diseases" :label="option.label" :value="option.value" />
    </el-select>
  </el-form-item>
</template>
<script setup>
import { computed, ref, watch } from 'vue';
import { watchDebounced } from "@vueuse/core";
import { storeToRefs } from "pinia";
import { getDisease } from '@/api/knowledgeBase';
import { memberStore } from '@/store/member';

const emits = defineEmits(['change'])
const props = defineProps({
  form: { type: Object, default: () => ({}) },
  mult: { type: Boolean, default: false },
  name: { type: String, default: '' },
  required: { type: Boolean, default: false },
  title: { type: String, default: '' },
})
const value = computed(() => {
  const val = props.form[props.title];
  if (props.mult) return Array.isArray(val) ? val : [];
  return val
});
const placeholder = computed(() => `请搜索${props.name || ''}`);
const { corpInfo } = storeToRefs(memberStore());
const corpDiseases = computed(() => corpInfo.value && Array.isArray(corpInfo.value.diseases) ? corpInfo.value.diseases.map(i => ({ label: i, value: i })) : []);

function change(value) { emits('change', { title: props.title, value }) }

const diseaseName = ref('');
function searchDisease(e) { diseaseName.value = e.trim() }
const diseases = ref([...corpDiseases.value.map(i => ({ ...i }))]);

watchDebounced(diseaseName, async () => {
  if (diseaseName.value === '' && props.mult) {
    const list = corpDiseases.value.filter(i => !value.value.includes(i.label));
    const selections = value.value.map(i => ({ lable: i, value: i }));
    diseases.value = [...selections, ...list];
  } else if (diseaseName.value === '' && !props.mult) {
    const list = corpDiseases.value.filter(i => i.label !== value.value);
    const options = value.value ? [{ label: value.value, value: value.value }] : [];
    diseases.value = [...options, ...list];
  } else {
    const res = await getDisease(diseaseName.value);
    const list = res.data && Array.isArray(res.data.data) ? res.data.data.map(i => ({ label: i.diseaseName, value: i.diseaseName })) : [];
    const firstOption = list.find(i => i.label === diseaseName.value) || { label: diseaseName.value, value: diseaseName.value }; // 完全匹配项
    const diseaseList = corpDiseases.value.filter(i => i.label !== diseaseName.value && !list.some(item => item.label == i.label));
    diseases.value = [firstOption, ...list.filter(i => i.label !== diseaseName.value), ...diseaseList];
  }
}, { debounce: 500, maxWait: 5 * 1000 })

// watch(() => props.form, (newVal) => {
//   const { diagnosisName, diagnosisNameLabel } = newVal;
//   if (diagnosisName && diagnosisNameLabel) {
//     initDisease(diagnosisNameLabel, diagnosisName);
//   }
// })
// function initDisease(label, value) {
//   diseases.value = [{ label, value }, ...corpDiseases.value]
// }

</script>
<style lang="scss" scoped></style>
