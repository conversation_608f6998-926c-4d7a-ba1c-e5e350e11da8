<template>
  <el-dialog :model-value="visible" :width="width" title="更多筛选" @close="close">
    <template #header>
      <div class="flex items-center">
        <span class="text-16px font-semibold mr-10px">{{ field.name }}变化趋势图</span>
        <el-dropdown v-if="field.type === 'number'">
          <span class="el-dropdown-link cursor-pointer text-blue-500">
            {{ type.text }}
            <el-icon class="transform translate-y-1px cursor-pointer">
              <arrow-down />
            </el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item v-for="item in typeList" :key="item.value" @click="changeType(item)">{{ item.text }}</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </template>
    <div class="flex flex-col" style="height: calc(85vh - 200px)">
      <div class="flex-shrink-0 flex items-center justify-between h-40px">
        <div class="text-sm">单位： {{ field.unit }}</div>
        <div color-normal class="flex items-center">
          <span>统计日期：</span>
          <span v-for="i in rangeList" :key="i.key" class="cursor-pointer mr-10px text-sm hover:text-blue-500" :class="currentRange === i.key ? 'text-blue-500' : 'text-gray-500'" @click="toggle(i)">
            {{ i.text }}
          </span>
          <el-date-picker v-model="dates" type="daterange"  :unlink-panels="true"
 :clearable="false" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD" @change="changeTime" />
        </div>
      </div>
      <!-- 表格 -->
      <template v-if="type.value === 'table'">
        <div class="flex-grow relative">
          <div class="absolute inset-0">
            <el-table height="100%" :data="tableData">
              <el-table-column label="测量日期" prop="date" width="180" />
              <el-table-column :label="field.name" prop="value" width="180" />
            </el-table>
          </div>
        </div>
      </template>
      <!-- 折线图 -->
      <template v-else>
        <div class="flex-grow relative">
          <div ref="chartRef" class="absolute inset-0"></div>
        </div>
      </template>
    </div>
    <template #footer>
      <div class="text-center">
        <el-button class="w-100px" :loading="loading" plain @click="close()">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { nextTick, onMounted, ref, watch } from "vue";
import dayjs from "dayjs";
import useLineChart from "@/hooks/useLineChart";
import { getMedicalFiledsTrend } from "@/api/member";
const emits = defineEmits(["change", "close"]);
const props = defineProps({
  width: { type: Number },
  visible: { type: Boolean, default: false },
  field: { type: Object, default: () => ({}) },
  customerId: { type: String },
});
const typeList = [
  { text: "折线图", value: "lineChart" },
  { text: "表格", value: "table" },
];
const type = ref({});
async function changeType(item) {
  type.value = item;
  await nextTick();
  if (item.value === "lineChart") paint();
  else clear();
}
const tableData = ref([]);
async function getList() {
  let { success, data } = await getMedicalFiledsTrend({
    memberId: props.customerId,
    dates: dates.value,
    field: props.field.title,
  });
  if (success) {
    tableData.value = data.list.map((i) => {
      return {
        date: dayjs(i.createTime).format("YYYY-MM-DD"),
        value: i[props.field.title],
      };
    });
  } else {
    tableData.value = [];
  }
  getChartData();
}
const chartRef = ref();
const dates = ref([]);
const rangeList = [
  { text: "近7天", value: 7, unit: "day", key: "7day" },
  { text: "近30天", value: 30, unit: "day", key: "30day" },
  { text: "近90天", value: 90, unit: "day", key: "90day" },
  { text: "近180天", value: 180, unit: "day", key: "180day" },
  { text: "近1年", value: 1, unit: "year", key: "year" },
];
const currentRange = ref("7day");
const { xAxisData, seriesData, paint, clear, chart } = useLineChart(chartRef, { grid: { left: 30, right: 40 } });
function toggle(i) {
  const start = dayjs().subtract(i.value, i.unit);
  dates.value = [start.format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")];
  currentRange.value = i.key;
  getList();
}
function getChartData() {
  xAxisData.value = tableData.value.map((i) => i.date);
  seriesData.value = tableData.value.map((i) => {
    return Number(i.value);
  });
}
function close() {
  emits("close");
}
watch(
  () => props.visible,
  async (val) => {
    if (val) {
      toggle(rangeList[0]);
      if (props.field.type === "number") {
        type.value = typeList[0];
      } else {
        type.value = typeList[1];
      }
      if (type.value.value === "lineChart") {
        if (chart.value) clear();
        await nextTick();
      }
    }
  }
);
function changeTime() {
  currentRange.value = "";
  getList();
}
</script>
