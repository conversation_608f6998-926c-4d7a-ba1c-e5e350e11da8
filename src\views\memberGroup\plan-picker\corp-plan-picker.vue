<template>
  <el-dialog title="机构回访计划" :model-value="visible" :width="width" @close="close()">
    <classify-seach-panel height="40vh" placeholder="输入机构回访计划名称搜索" :cate-method="getManageMentPlanCate"
      :search-method="getPlans" :list-method="getPlans">
      <template #searchItem="{item}">
        <div class="py-10px px-15px cursor-pointer flex items-center border-b border-gray-200" @click="toggle(item)">
          <div class="flex-grow w-0 break-all mr-15px">{{ item.planName }}</div>
          <el-checkbox :model-value="Boolean(map[item._id])" />
        </div>
      </template>
      <template #selectItem="{item}">
        <div class="py-10px px-15px cursor-pointer flex items-center border-b border-gray-200" @click="toggle(item)">
          <div class="flex-grow w-0 break-all mr-15px">{{ item.planName }}</div>
          <el-checkbox :model-value="Boolean(map[item._id])" />
        </div>
      </template>
    </classify-seach-panel>
    <template #footer>
      <span class="block text-center">
        <el-button class="w-100px" @click="close()">取消</el-button>
        <el-button class="w-100px" type="primary" @click="confirm()">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { memberStore } from "@/store/member";
import {  getManageMentPlanCate, getManagementPlanList } from "@/api/managementPlan";

import classifySeachPanel from "@/components/classify-list/classify-search-panel.vue";
const props = defineProps({
  data: [],
  visible: { type: Boolean, default: false },
  width: { type: [String, Number], default: '600px' }
})
const emits = defineEmits(['close', 'change'])

const { memberInfo } = storeToRefs(memberStore())
const selections = ref([]);
const map = computed(() => {
  return selections.value.reduce((acc, cur) => {
    acc[cur._id] = true
    return acc
  }, {})
})

function toggle(item) {
  if (map.value[item._id]) {
    selections.value = []
  } else {
    selections.value = [item]
  }
}

async function getPlans({ page, pageSize, cateIds, name: planName }) {
  const { data } = await getManagementPlanList({ page, pageSize, cateIds, planName, planStatus: true, corpId: memberInfo.value.corpId, userId: memberInfo.value.userid, planType: 'corp' });
  const list = Array.isArray(data.data) ? data.data : [];
  const total = data.total > 0 ? data.total : 0;
  return { list, total };
}

function close() {
  emits('close')
}

function confirm() {
  emits('change', selections.value[0])
  close()
}

watch(() => props.visible, n => {
  if (n) {
    selections.value = [...props.data];
  }
})
</script>
<style lang="scss" scoped></style>
