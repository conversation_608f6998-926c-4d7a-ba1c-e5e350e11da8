<template>
  <div ref="container" :style="{ fontSize: currentFontSize + 'px' }">
    {{ text }}
  </div>
</template>

<script>
export default {
  props: {
    text: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      currentFontSize: 24,
    };
  },
  mounted() {
    this.adjustFontSize();
  },
  watch: {
    text() {
      this.adjustFontSize();
    },
  },
  methods: {
    adjustFontSize() {
      const containerWidth = this.$refs.container.offsetWidth;
      const textWidth = this.getTextWidth();

      if (textWidth > containerWidth) {
        const newFontSize = Math.floor((containerWidth / textWidth) * this.currentFontSize);
        this.currentFontSize = newFontSize;
      } else {
        this.currentFontSize = 24; // 默认字体大小
      }
    },
    getTextWidth() {
      const span = document.createElement("span");
      span.style.fontSize = this.currentFontSize + "px";
      span.style.visibility = "hidden";
      span.style.position = "absolute";
      span.style.whiteSpace = "nowrap";
      span.textContent = this.text;
      document.body.appendChild(span);
      const width = span.offsetWidth;
      document.body.removeChild(span);

      return width;
    },
  },
};
</script>

<style scoped>
div {
  font-size: 24px;
}
</style>