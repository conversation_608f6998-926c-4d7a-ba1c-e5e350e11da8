<template>
  <div class="relative h-34px py-4px px-5px bg-white ml-10px"
    :class="active ? 'bg-[#ecf5ff] rounded-b-l-10px rounded-b-r-10px':''">
    <div class="h-24px px-10px flex items-center rounded-5px hover:bg-[#ecf5ff]" @click="change">
      <div class="mr-20px text-14px">{{ name }}</div>
      <!-- <div class=""></div> -->
      <el-icon v-if="closable" @click.stop="close()">
        <CloseBold />
      </el-icon>
    </div>
    <div v-if="active" class="absolute -left-15px top-0 h-15px w-15px overflow-hidden bg-[#ecf5ff]">
      <div class="absolute right-0 top-0 h-40px w-40px bg-white rounded-t-r-10px"></div>
    </div>
    <div v-if="active" class="absolute -right-15px top-0 h-15px w-15px overflow-hidden bg-[#ecf5ff]">
      <div class="absolute left-0 top-0 h-40px w-40px bg-white rounded-t-l-10px"></div>
    </div>
  </div>
</template>
<script setup>
defineProps({
  active: { type: Boolean, default: false },
  closable: { type: Boolean, default: false },
  name: { type: String, default: '' },
})
const emits = defineEmits(['change', 'close']);
const change = () => emits('change')
const close = () => {
  emits('close');
}
</script>
<style lang="scss" scoped></style>
