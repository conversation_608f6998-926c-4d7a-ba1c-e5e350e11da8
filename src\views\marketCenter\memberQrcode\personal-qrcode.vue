<template>
  <div class="h-full">
    <personal-qrcode-table ref="tableRef" :statusMap="statusMap" :qrcodeStatus="qrcodeStatus" @edit="edit" @download="downloadImage" />
  </div>
  <create-qrcode :memberQrcodeVisible="memberQrcodeVisible" :qrcodeType="qrcodeType" @onCreateQrcode="createQrcode"
    @close="memberQrcodeVisible = false" @submit="submit" :operationType="operationType"
    :qrCodeInfo="qrCodeInfo"></create-qrcode>
  <qrcode-view :qrcodeVisible="imageVisible" :qrCodeInfo="qrCodeInfo" @close="imageVisible = false"></qrcode-view>
  <el-dialog v-model="addQrcodeVisible" class="activeQrcodeDialog" :width="560">
    <template #header>
      <span color-normal class="text-16px font-semibold">新建员工活码</span>
    </template>
    <div color-normal class="px-10px">
      <div class="text-16px font-semibold text-center py-20px">请选择员工活码类型</div>
      <div
        class="flex items-center border border-gray-200 rounded-4px px-15px py-12px cursor-pointer hover:border-blue-400"
        @click="addQrcode(1)">
        <div class="flex-grow">
          <div class="font-semibold text-15px pb-6px">单人码</div>
          <div class="text-14px text-zinc-500">为一个员工创建活动码，客户扫码添加对应员工</div>
        </div>
        <el-icon class="text-zinc-500" size="16">
          <ArrowRight />
        </el-icon>
      </div>
      <div
        class="flex items-center border mt-15px mb-10px border-gray-200 rounded-4px px-15px py-12px cursor-pointer hover:border-blue-400"
        @click="addQrcode(2)">
        <div class="flex-grow">
          <div class="font-semibold text-15px pb-6px">多人码</div>
          <div class="text-14px text-zinc-500">为多个员工创建活动码，客户扫码后随机添加员工</div>
        </div>
        <el-icon class="text-zinc-500" size="16">
          <ArrowRight />
        </el-icon>
      </div>
    </div>
    <template #footer>
      <div class="flex items-center justify-center">
        <el-button class="w-100px" @click="addQrcodeVisible = false">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref } from "vue";

import createQrcode from "./components/createQrcode.vue";
import qrcodeView from "./components/qrcodeView.vue";
import personalQrcodeTable from './components/personal-code-table.vue';

const memberQrcodeVisible = ref(false);
const addQrcodeVisible = ref(false);
const imageVisible = ref(false);
const operationType = ref("add");
let qrCodeInfo = ref({});
const qrcodeType = ref(1);

const qrcodeStatus = [{ label: "启用中", value: "enable" }, { label: "已停用", value: "disable" }]
const statusMap = qrcodeStatus.reduce((acc, item) => (acc[item.value] = item.label, acc), {});

function addQrcode(type) {
  operationType.value = "add";
  qrcodeType.value = type;
  addQrcodeVisible.value = false;
  memberQrcodeVisible.value = true;
}

function edit(item) {
  qrCodeInfo.value = item;
  operationType.value = "edit";
  memberQrcodeVisible.value = true;
}

const tableRef = ref(null);
function submit() {
  memberQrcodeVisible.value = false;
  tableRef.value.refresh();
}

function downloadImage(item) {
  qrCodeInfo.value = item;
  imageVisible.value = true;
};

</script>
<style lang="scss" scoped>
:deep(.el-tabs__content) {
  height: calc(100% - 40px);
}

:deep(.el-tabs__header) {
  background-color: white;
  margin-bottom: 0;
}

:global(.activeQrcodeDialog.el-dialog .el-dialog__header) {
  display: none;
}
</style>