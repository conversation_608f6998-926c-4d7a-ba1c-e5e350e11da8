<template>
  <el-table ref="tableRef" class="screen-el-table" :data="list" height="100%" v-bind="tableConfig">
    <el-table-column prop="taskContent" label="待办内容">
      <template #default="{ row: { taskContent } }">
        <div class="truncate text-[#a7cdff]">{{ taskContent }}</div>
      </template>
    </el-table-column>
    <el-table-column prop="eventType" label="待办类型" width="100">
      <template #default="{ row: { eventType } }">
        <div class="truncate text-[#a7cdff]">{{ ServiceType[eventType] }}</div>
      </template>
    </el-table-column>
    <el-table-column prop="customerName" label="客户" width="100">
      <template #default="{ row: { customerName } }">
        <div class="truncate text-[#a7cdff]">{{ maskName(customerName) }}</div>
      </template>
    </el-table-column>
    <el-table-column prop="userName" label="跟进人" width="100">
      <template #default="{ row: { executorUserId } }">
        <div class="truncate text-[#a7cdff]">
          <ww-user :openid="executorUserId"></ww-user>
        </div>
      </template>
    </el-table-column>
    <el-table-column prop="time" label="计划执行时间" width="180">
      <template #default="{ row: { time } }">
        <div class="truncate text-[#a7cdff]">{{ time }}</div>
      </template>
    </el-table-column>
  </el-table>
</template>
<script setup>
import { computed, onMounted, ref } from "vue";
import dayjs from "dayjs";
import { ServiceType } from "@/baseData";
import useBigScreenTable from "./use-big-screen-table";
import { getCorpToDolistApi, corpToDolist } from "../api";
import WwUser from "@/components/ww-user/index.vue";
const tableRef = ref();
const { tableConfig } = useBigScreenTable(tableRef);
onMounted(() => {
  getCorpToDolistApi();
});
const list = computed(() => corpToDolist.value.map((i) => ({ ...i, time: `${dayjs(i.plannedExecutionTime).format("YYYY-MM-DD")}` })));
function maskName(name) {
  if (!name) return "";
  if (name.length === 2) {
    return name[0] + "*";
  } else if (name.length > 2) {
    return name[0] + "*".repeat(name.length - 2) + name[name.length - 1];
  } else {
    return name;
  }
}
</script>
<style lang="scss" scoped>
:global(.el-table.screen-el-table .el-table__header tr) {
  background-color: rgba(13, 93, 171, 0.5);
}

:global(.el-table.screen-el-table .el-table__inner-wrapper::before) {
  display: none;
}

:deep(.el-table__cell) {
  background-color: transparent !important;
}
</style>
