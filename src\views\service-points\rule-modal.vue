<template>
  <el-dialog :model-value="props.visible" :width="width" :title="title" @close="close()">
    <el-scrollbar wrap-class="el-dialog-body-full-scroll text-14px">
      <div class="px-10px pt-10px text-14px">
        <div class="mb-10px">
          <span class="text-red-500 mr-5px">*</span>
          <span class="">选择类型</span>
        </div>
        <el-radio-group v-model="form.triggerCondition" :disabled="isRead" v-if="!isRead">
          <el-radio value="service" label="service">提供患者服务</el-radio>
          <el-radio value="rate" label="rate">获取服务评价</el-radio>
        </el-radio-group>
        <div v-else class="m-15px font-600">{{ form.triggerCondition === "service" ? "提供患者服务" : "获取服务评价" }}</div>

        <el-form v-if="form.triggerCondition === 'service' || form.triggerCondition === 'rate'" inline label-suffix="："
          label-position="right" class="mt-10px">
          <el-form-item v-if="form.triggerCondition === 'service'" class="is-required mb-5px" label="服务类型">
            <el-select v-model="form.serviceType" class="w-140px" :disabled="isRead" v-if="!isRead">
              <el-option v-for="i in serviceList" :key="i.key" :value="i.value" :label="i.label" />
            </el-select>
            <div v-else class="font-600">{{ ServiceType[form.serviceType] }}</div>
          </el-form-item>
          <el-form-item v-else-if="form.triggerCondition === 'rate'" class="is-required mb-5px" label="满意度评分">
            <el-select v-model="form.serviceRate" class="w-140px" v-if="!isRead">
              <el-option v-for="i in rateList" :key="i.key" :value="i.value" :label="i.label" />
            </el-select>
            <div v-else class="font-600">{{ currentRate }}</div>
          </el-form-item>
          <el-form-item class="is-required mb-5px" label="奖励积分">
            <el-input v-model="form.points" class="w-140px" :disabled="isRead" v-if="!isRead" />
            <div v-else class="font-600">{{ form.points }}分</div>
          </el-form-item>
        </el-form>
        <div
          v-if="form.triggerCondition === 'service' && form.serviceType && ServiceType[form.serviceType] && form.points"
          class="mt-10px mb-15px text-gray-500">
          <el-icon class="mr-5px transform translate-y-2px text-red-400">
            <WarningFilled />
          </el-icon>
          完成一条类型为“{{ ServiceType[form.serviceType] }}”服务，奖励积分“{{ form.points }}分”
        </div>
        <div v-else-if="form.triggerCondition === 'rate' && currentRate && form.points"
          class="mt-10px mb-15px text-gray-500">
          <el-icon class="mr-5px transform translate-y-2px text-red-400">
            <WarningFilled />
          </el-icon>
          服务满意度评分获得一次“{{ currentRate }}”，奖励积分“{{ form.points }}分”
        </div>
        <div class="my-10px pt-5px">
          <span class="text-red-500 mr-5px">*</span>
          <span>奖励限制</span>
          <el-tooltip placement="top-start" effect="light">
            <template #content>
              <span color-normal class="text-14px whitespace-pre-line">
                {{ rewardExplain }}
              </span>
            </template>
            <el-icon class="ml-5px cursor-pointer text-14px font-normal text-gray-400 translate-y-2px mr-5px transform">
              <QuestionFilled />
            </el-icon>
          </el-tooltip>
        </div>
        <div class="pl-10px">
          <div class="flex items-center flex-wrap pt-5px">
            <div class="flex-shrink-0">总奖励次数：</div>
            <div class="flex items-center flex-shrink-0" v-if="!isRead">
              <el-checkbox v-model="limitTotal" @change="limit($event, 'limitTotal')"
                :disabled="isRead">限制次数</el-checkbox>
              <div class="flex items-center ml-10px">
                <el-input v-model="form.taskTriggerTotal" class="ml-10px" placeholder="请输入限制次数" :readonly="!limitTotal"
                  type="number" :disabled="isRead" />
                <span class="ml-5px text-14px text-gray-500">次</span>
              </div>
            </div>
            <div v-else class="ml-10px font-600">
              {{ form.taskTriggerTotal === -1 ? "无限次" : form.taskTriggerTotal + "次" }}
            </div>
          </div>
          <div class="flex items-center flex-wrap mt-15px">
            <div class="flex-shrink-0">每人每日上限奖励次数：</div>
            <div class="flex items-center flex-shrink-0" v-if="!isRead">
              <el-checkbox v-model="limitDay" @change="limit($event, 'limitDay')"
                :disabled="isRead">限制次数</el-checkbox>
              <div class="flex items-center ml-10px">
                <el-input v-model="form.userTriggerTodayLimitCount" class="ml-10px" placeholder="请输入限制次数"
                  :readonly="!limitDay" type="number" :disabled="isRead" />
                <span class="ml-5px text-14px text-gray-500">次</span>
              </div>
            </div>
            <div v-else class="ml-10px font-600">{{ form.userTriggerTodayLimitCount === -1 ? "无限次" :
              form.userTriggerTodayLimitCount + "次" }}</div>
          </div>
          <div class="flex items-center flex-wrap mt-15px">
            <div class="flex-shrink-0">每人总上限次数：</div>
            <div class="flex items-center flex-shrink-0" v-if="!isRead">
              <el-checkbox v-model="limitPerson" @change="limit($event, 'limitPerson')"
                :disabled="isRead">限制次数</el-checkbox>
              <div class="flex items-center ml-10px">
                <el-input v-model="form.userTriggerlimitTotal" class="ml-10px" placeholder="请输入限制次数"
                  :readonly="!limitPerson" type="number" :disabled="isRead" />
                <span class="ml-5px text-14px text-gray-500">次</span>
              </div>
            </div>
            <div v-else class="ml-10px font-600">
              {{ form.userTriggerlimitTotal === -1 ? "无限次" : form.userTriggerlimitTotal + "次" }}
            </div>
          </div>
        </div>
      </div>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button plain class="w-100px" type="primary" @click="close()">取消</el-button>
        <el-button class="w-100px" type="danger" @click="detele()" v-if="isRead">删除</el-button>
        <el-button class="w-100px" :loading="loading" type="primary" @click="confirm()" v-if="!isRead">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { ServiceType } from "@/baseData";
import { createPointsTask, deletePointsTask } from "@/api/points";
import { ElMessageBox } from "element-plus";

const serviceList = Object.keys(ServiceType).map((key) => ({ label: ServiceType[key], value: key }));
const rateList = [
  { label: "一星", value: 1 },
  { label: "二星", value: 2 },
  { label: "三星", value: 3 },
  { label: "四星", value: 4 },
  { label: "五星", value: 5 },
];
const emits = defineEmits(["close", "change", "success"]);
const props = defineProps({
  data: { type: Object, default: () => ({}) },
  id: { type: String, default: "" },
  visible: { type: Boolean, default: false },
  width: { type: String, default: "500px" },
});
const isRead = ref(false);
const currentRate = computed(() => {
  const item = rateList.find((i) => i.value === form.value.serviceRate);
  return item ? item.label : "";
});
const loading = ref(false);
const title = computed(() => (isRead.value ? "查看积分规则" : "新增积分规则"));
const form = ref({});
const limitTotal = ref(false);
const limitDay = ref(false);
const limitPerson = ref(false);
function close() {
  emits("close");
}
async function confirm() {
  if (!["service", "rate"].includes(form.value.triggerCondition)) {
    ElMessage.info("请选择类型");
  } else if (form.value.triggerCondition === "service" && (!form.value.serviceType || !ServiceType[form.value.serviceType])) {
    ElMessage.info("请选择服务类型");
  } else if (form.value.triggerCondition === "rate" && !form.value.serviceRate) {
    ElMessage.info("请选择评分");
  } else if (form.value.triggerCondition === "service" && !form.value.points) {
    ElMessage.info("请输入奖励积分");
  } else if (form.value.triggerCondition === "service" && !(Number(form.value.points) > 0)) {
    ElMessage.info("请输入有效的奖励积分");
  } else if (limitTotal.value && !(Number(form.value.taskTriggerTotal) > 0)) {
    ElMessage.info("请输入有效的总奖励次数");
  } else if (limitDay.value && !(Number(form.value.userTriggerTodayLimitCount) > 0)) {
    ElMessage.info("请输入有效的每人每日上限奖励次数");
  } else if (limitPerson.value && !(Number(form.value.userTriggerlimitTotal) > 0)) {
    ElMessage.info("请输入有效的每人奖励上限总次数");
  } else {
    const data = {
      triggerCondition: form.value.triggerCondition,
      taskTriggerTotal: limitTotal.value ? Number(form.value.taskTriggerTotal) : -1,
      userTriggerTodayLimitCount: limitDay.value ? Number(form.value.userTriggerTodayLimitCount) : -1,
      userTriggerlimitTotal: limitPerson.value ? Number(form.value.userTriggerlimitTotal) : -1,
      points: Number(form.value.points),
    };
    if (form.value.triggerCondition === "service") {
      data.serviceType = form.value.serviceType;
      data.serviceRate = "";
    } else if (form.value.triggerCondition === "rate") {
      data.serviceRate = form.value.serviceRate;
      data.serviceType = "serviceRate";
    }
    console.log(data);
    const { success, message } = await createPointsTask(data);
    if (success) {
      ElMessage.success("操作成功");
      emits("success");
      close();
    } else {
      ElMessage.error(message);
    }
  }
}
async function detele() {
  await ElMessageBox.confirm("确定要删除吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    console.log("删除");
  });
  await deletePointsTask(props.data._id);
  close();
  emits("success");
}

function limit(e, type) {
  if (e) return;
  if (type === "limitTotal") form.value.taskTriggerTotal = "";
  if (type === "limitDay") form.value.userTriggerTodayLimitCount = "";
  if (type === "limitPerson") form.value.userTriggerlimitTotal = "";
}

watch(
  () => props.visible,
  (n) => {
    if (n) {
      isRead.value = !!props.data._id;
      form.value = props.data;
      limitTotal.value = props.data.taskTriggerTotal > 0;
      limitDay.value = props.data.userTriggerTodayLimitCount > 0;
      limitPerson.value = props.data.userTriggerlimitTotal > 0;
    }
  }
);

const rewardExplain = `【总奖励次数】
在规则启用阶段，触发该规则总共发放的奖励次数；
如：总奖励次数10次，甲员工发放了4次，乙员工发放了6次。其余员工就算触发规则无法得到积分奖励。

【每人每日上限奖励次数】
在规则启用阶段，每人每日获得奖励的总次数。
如：限制次数为10次，甲员工当日触发规则11次，但是只能得到10次积分。

【每人奖励上限总次数】
在规则启用阶段，每人获得奖励的总次数。
如：限制次数为20次。甲员工总共触发规则25次，但是只能得到20次积分。
`;
</script>