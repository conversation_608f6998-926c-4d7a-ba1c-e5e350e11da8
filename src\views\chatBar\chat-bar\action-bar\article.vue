<template>
  <my-layout bg-fff v-loading="loading">
    <layout-item>
      <div class="px-15px pt-20px pb-10px font-semibold" font-16 border-bottom>文章列表</div>
      <div class="py-12px px-15px" border-bottom>
        <el-input v-model="title" placeholder="输入内容名称搜索" :suffix-icon="Search" />
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <div v-if="cateList.length" h-full flex>
        <div flex-shrink-0 h-full class="w-120px border-left">
          <el-scrollbar class="h-full">
            <classify-list appendAll :checked-cate="current" :data="cateList" @change="changeCurrent($event)" />
          </el-scrollbar>
        </div>
        <div flex-grow h-full class="flex-shrink">
          <el-scrollbar h-full ref="scrollRef" @scroll="handleScroll($event)">
            <div v-for="item in list" :key="item._id" px-15 py-10 border-bottom>
              <div class="w-full text-sm break-all" @click="previewArticle(item)">{{ item.title }}</div>
              <div class="flex items-center justify-between mt-6px">
                <div class="text-sm text-gray-500">创建时间：{{ item.date }}</div>
                <el-text class="flex-shrink-0" pointer type="primary" @click="sendLink(item)">发送</el-text>
              </div>
            </div>
            <empty-data v-if="list.length === 0" title="暂无文章"></empty-data>
          </el-scrollbar>
        </div>
      </div>
      <div v-else h-full w-full items-center justify-center>
        <empty-data :top="0" title="暂无文章"></empty-data>
      </div>
    </layout-main>
    <layout-item>
      <div class="py-12px px-15px" common-shadow--r>
        <el-button class="block w-full" @click="cancel()">取消</el-button>
      </div>
    </layout-item>
  </my-layout>
  <el-dialog :model-value="visible" :width="width" title="文章预览" @close="cancelPreview">
    <div class="px-15px pt-6px pb-12px text-16px text-dark-500 text-center font-semibold">{{ articleTitle }}</div>
    <iframe class="w-full" style="height:calc(100vh - 360px) ;" :srcdoc="iframeContent"></iframe>
    <template #footer>
      <div text-center>
        <el-button type="primary" plain class="w-100px" @click="cancelPreview()">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref } from "vue";
import { useDebounceFn, watchDebounced } from "@vueuse/core";
import { storeToRefs } from "pinia";
import { ElMessage } from "element-plus";
import dayjs from 'dayjs';
import { Search } from "@element-plus/icons-vue";
import { getArticleList, addArticleSendRecord, getArticleCateList, getArticle } from "@/api/knowledgeBase";
import EmptyData from "@/components/empty-data.vue";
import { memberStore } from "@/store/member";
import { createServiceRecord } from "@/utils/service";
import { isURL } from "@/utils/common";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import * as ww from "@/libs/@wecom/jssdk/wecom.esm.js";
import classifyList from '@/components/classify-list/classify-list-side.vue';
import useClassifyList from '@/components/classify-list/useClassifyList';
import usePreviewArticle from '@/hooks/usePreviewArticle';


const props = defineProps({ customer: { type: Object, default: () => ({}) }, teams: { type: Array, default: () => [] }, unionId: { type: String, default: "" } });
const emits = defineEmits(["close"]);
const team = computed(() => props.teams[0] || {});
const { memberInfo } = storeToRefs(memberStore());
function cancel() {
  emits("close");
}

const title = ref('');
const list = ref([]);
const loading = ref(false);
const page = ref(1);
const total = ref(0);
const options = {
  getList: getArticleCateList,
  callback: handleCurrentChang
}

const { cateList, current, cateTree, changeCurrent } = useClassifyList(options);
function handleCurrentChang() {
  page.value = 1;
  getList()
}

watchDebounced(title, () => {
  page.value = 1;
  getList()
}, { debounce: 1000 })

async function getList() {
  loading.value = true;
  const childrenIds = Array.isArray(current.value.childrenIds) ? current.value.childrenIds : [];
  const cateIds = current.value._id ? [current.value._id, ...childrenIds] : [];
  const { data, message, success } = await getArticleList({ corpId: localStorage.getItem("corpId"), page: page.value, pageSize: 30, enable: true, cateIds, title: title.value });
  const { list: articleList = [], total: count = 0 } = data;
  const arr = articleList.map(i => ({
    ...i,
    date: i.createTime && dayjs(i.createTime).isValid() ? dayjs(i.createTime).format("YYYY-MM-DD") : "",
  }))
  list.value = page.value === 1 ? arr : list.value.concat(arr);
  total.value = count;
  loading.value = false;
  if (!success) ElMessage.error(message);
}
const scrollRef = ref();

const handleScroll = useDebounceFn(function scroll({ scrollTop }) {
  if (loading.value) return;
  const isBottom = scrollRef.value.wrapRef.scrollHeight - scrollRef.value.wrapRef.clientHeight - scrollTop <= 20;
  if (isBottom && !loading.value && list.value.length < total.value) {
    page.value += 1;
    getList();
  }
});
async function sendLink(item) {
  const { title } = item;
  const desc = "宣教文章";
  let articlePageUrl = `${import.meta.env.VITE_PATIENT_PAGE_BASE_URL}pages/article/index?id=${item._id}&corpId=${memberInfo.value.corpId}&unionId=${props.unionId}&customerId=${props.customer._id || ""}`;
  const id = await createServiceRecordAction(articlePageUrl, title);
  articlePageUrl = `${articlePageUrl}&serviceRecordId=${id}`;
  createSendRecord(item, id);
  ww.sendChatMessage({
    msgtype: "news",
    enterChat: true,
    news: {
      link: articlePageUrl, //H5消息页面url 必填
      title,
      desc, //H5消息摘要
      imgUrl: "https://796f-youcan-clouddev-1-8ewcqf31dbb2b5-**********.tcb.qcloud.la/other/19-%E9%97%AE%E5%8D%B7.png?sign=55a4cd77c418b2c548b65792a2cf6bce&t=**********",
    },
    success: function (res) {
      ElMessage.success("发送成功");
      cancel();
    },
    fail: function (res) {
      ElMessage.error("发送失败");
    },
  });
}

async function createServiceRecordAction(link, title) {
  const { anotherName } = memberInfo.value;
  let item = {
    taskContent: `${anotherName}发送宣教"${title}"给客户${props.customer.name}`,
    executionTime: new Date().getTime(),
    customerId: props.customer._id,
    executeTeamId: team.value.teamId || "",
    teamName: team.value.name || "",
    eventType: "ContentReminder",
    customerName: props.customer.name,
    pannedEventSendFile: {
      name: title,
      url: link,
      type: "article",
    },
  };
  const res = await createServiceRecord(item);
  return res && res.data && res.data.data ? res.data.data.id : "";
}


function createSendRecord(item, serviceRecordId) {
  addArticleSendRecord({ corpId: memberInfo.value.corpId, userId: memberInfo.value.userid, articleId: item._id, customerId: props.customer._id, serviceRecordId });
}

const { content: iframeContent, close: cancelPreview, preview, visible, width, articleTitle } = usePreviewArticle();

async function previewArticle(item) {
  const { success, message, data } = await getArticle({ id: item._id, corpId: localStorage.getItem('corpId') });
  if (success) {
    preview(data.data)
  } else {
    ElMessage.error(message || '预览文章失败')
  }
}
</script>
<style>
.border-left {
  border: 1px solid #eee;
}
</style>