<template>
  <el-dialog :model-value="props.visible" :width="750" title="新增治疗" @close="close()">
    <el-scrollbar wrap-class="el-dialog-body-full-scroll h-300px">
      <el-table stripe height="300" :data="list" v-loading="loading" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column property="projectName" label="项目" min-width="150" />
        <el-table-column property="packageName" label="所属套餐" />
        <el-table-column label="剩余/总数" width="100">
          <template #default="{ row }">{{ row.restUsageCount }}/{{ row.usageCount }}</template>
        </el-table-column>
        <el-table-column property="treatmentDeptName" label="治疗科室" width="120" />
        <el-table-column property="type" label="治疗人员" width="200">
          <template #default="{ row }">
            <el-select class="w-150px el-select--hiddenValue" v-model="row.treatmentDoctorUserId">
              <template #prefix>
                <div class="h-30px" color-666>
                  <ww-user v-if="row.treatmentDoctorUserId" :openid="row.treatmentDoctorUserId"></ww-user>
                </div>
              </template>
              <el-option v-for="userId in getDoctors(row)" :key="userId" :label="userId" :value="userId">
                <ww-user :openid="userId"></ww-user>
              </el-option>
            </el-select>
          </template>
        </el-table-column>
      </el-table>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button plain class="w-100px" type="primary" @click="close()">取消</el-button>
        <el-button class="w-100px" :loading="loading" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ElMessage } from "element-plus";
import { ref, watch } from "vue";
import { getTreatmentRecord, addDeductRecord, updateTreatmentRecord } from "@/api/treatment-record";
import { pushCustomerTeamId } from "@/api/member";
import { staffStore } from "@/store/staff";
import { storeToRefs } from "pinia";
import { teamStore } from "@/store/team";
import { memberStore } from "@/store/member";
import { getRandomStr } from "@/utils";
const { staffList } = storeToRefs(staffStore());
const { memberInfo } = storeToRefs(memberStore());
const { currentTeamId, currentTeamName } = storeToRefs(teamStore());
const emits = defineEmits(["close", "success"]);
const selectedRows = ref([]);
const props = defineProps({
  id: { type: String, default: "" },
  visible: { type: Boolean, default: false },
  width: { type: Number, default: 500 },
  customer: { type: Object, default: () => ({}) },
  treatmentOrderId: { type: String, default: "" },
  unTreatedRecord: { type: Array, default: () => [] },
});
function close() {
  emits("close");
}
const list = ref([]);
const loading = ref(false);
async function getRecord() {
  const query = {
    customerId: props.customer._id,
    haveValid: true,
    treatmentDept_ids: memberInfo.value.deptIds,
    treatmentStatus: ["init"],
  };
  let { success, data } = await getTreatmentRecord(query);
  if (success) list.value = data.list;
}
function getDoctors(project) {
  if (!project.treatmentDept_id) return [];
  let userIds =
    staffList.value && Array.isArray(staffList.value)
      ? staffList.value
          .filter((item) => {
            if (item.deptIds && Array.isArray(item.deptIds)) {
              return item.deptIds.includes(project.treatmentDept_id);
            } else {
              return false;
            }
          })
          .map((item) => {
            return item.userid;
          })
      : [];
  return userIds;
}
function handleSelectionChange(val) {
  selectedRows.value = val;
}
async function confirm() {
  if (loading.value) return;
  if (selectedRows.value.length === 0) {
    ElMessage.warning("请选择治疗项目");
    return;
  }
  loading.value = true;
  // 新增治疗时间
  const treatmentOrderId = props.treatmentOrderId || getRandomStr();
  for (let i = 0; i < selectedRows.value.length; i++) {
    const item = selectedRows.value[i];
    const { _id, customerInfo, createTime, treatmentStatus, deductUsageCount, ...rest } = item;
    const params = {
      treatmentOrderId,
      creator: localStorage.getItem("userId"),
      belongTeamId: currentTeamId.value,
      belongTeamName: currentTeamName.value,
      deductStatus: "pending",
      treatmentId: _id,
      ...rest,
    };
    await addDeductRecord({ params });
    await updateTreatmentRecordApi({ id: _id });
  }
  pushTeamIdToCustomer();
  ElMessage.success("新增治疗成功");
  emits("success");
  close();
  loading.value = false;
}
// 更新治疗时间为治疗中
async function updateTreatmentRecordApi({ id }) {
  await updateTreatmentRecord({ id, params: { treatmentStatus: "pending" } });
}
// 添加客户团队
async function pushTeamIdToCustomer() {
  await pushCustomerTeamId({ customerId: props.customer._id, teamId: currentTeamId.value });
}
watch(
  () => props.visible,
  (n) => {
    if (n) {
      if (props.unTreatedRecord && props.unTreatedRecord.length) {
        list.value = props.unTreatedRecord;
      } else {
        getRecord();
      }
    }
  }
);
</script>
<style scoped>
.el-select.el-select--hiddenValue .el-input__inner {
  display: none;
}

.el-select.el-select--hiddenValue .el-input__prefix {
  flex-grow: 1;
}
</style>