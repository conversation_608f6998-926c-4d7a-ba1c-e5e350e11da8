<template>
  <el-dialog :model-value="visible" :width="width" title="编辑字段" @close="close">
    <el-scrollbar wrap-style="max-height: calc(100vh - 360px)">
      <div px-15>
        <div class="text-14px text-black py-12px">系统字段名称</div>
        <el-input :model-value="field.systemFieldName" disabled />
        <div class="text-14px text-black py-12px">字段显示名称</div>
        <el-input v-model="name" placeholder="请输入模版名称" :maxlength="8" />
        <template v-if="list.length">
          <div class="text-14px text-black pt-12px pb-10px">可选数据</div>
          <div class="pb-15px">
            <el-table stripe ref="tableRef" :show-header="false" :data="list" @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="55" />
              <el-table-column prop="label" label="名称" />
            </el-table>
          </div>
        </template>
      </div>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button class="w-100px" @click="close()">取消</el-button>
        <el-button class="w-100px" type="primary" @click="confirm()"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { nextTick, ref, toRefs, watch } from 'vue';
import { ElMessage } from 'element-plus';

const emits = defineEmits(['close', 'onChangeTemplate']);
const props = defineProps({
  field: { type: Object, default: () => ({}) },
  visible: { type: Boolean, default: false },
  width: { type: Number, default: 520 },
  templateType: { type: String, default: "" }
})
const { field, visible, width } = toRefs(props);

const name = ref('');
const list = ref([]);
function close() {
  emits('close')
}
function confirm() {
  if (name.value.trim() === '') ElMessage.info('请输入模版名称')
  // 判断是否有选中的数据 给他出提示
  if (field.value.type === "select" && list.value.filter((i) => i._checked).length === 0) {
    ElMessage.info('请选择可选数据')
    return;
  }
  if (field.value.type === "select") {
    field.value.corpRange = list.value.filter((i) => i._checked).map((i) => i.label);
  }
  field.value.name = name.value;
  // 拷贝 field
  let item = JSON.parse(JSON.stringify(field.value));
  item.range = item.corpRange;
  delete item.corpRange;
  // 标记为机构字段
  field.value.isCorpField = true;
  // 获取
  emits('onChangeTemplate', item);
  close();
}


function handleSelectionChange(val) {
  list.value.forEach((i) => {
    i._checked = val.includes(i);
  })
}
const tableRef = ref();
function toggle() {
  if (!tableRef.value) return;
  tableRef.value.clearSelection();
  list.value.forEach((i) => {
    tableRef.value.toggleRowSelection(i, field.value.corpRange.includes(i.label));
  })
}
watch(visible, async n => {
  if (n) {
    name.value = typeof field.value.name === 'string' ? field.value.name : '';
    list.value = field.value.type === "select" && Array.isArray(field.value.range) ? field.value.range : [];
    list.value = list.value.map((i) => {
      return {
        label: i
      }
    })
    await nextTick()
    toggle()
  }
})
</script>
<style lang="scss" scoped>
.customer-item:hover {
  background: #FFFAFA;
}
</style>
