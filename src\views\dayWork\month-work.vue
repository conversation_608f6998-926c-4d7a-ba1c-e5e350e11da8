<template>
  <div v-if="props.width > 0">
    <div flex class="work-tr" font-14>
      <div v-for="i in 7" :key="i" flex-shrink-0 text-center class="work-cell work-cell-th" :style="cellStyle">
        <div font-14>{{ WeekDay[i % 7] }}</div>
      </div>
    </div>
    <div v-for="(group, index) in groupWorks" :key="`group_row_${index}`" class="work-tr" flex font-14>
      <div v-for="item in group" :key="item.date" flex-shrink-0 class="work-cell" :style="noonCellStyle">
        <div flex justify-between pb-10 :class="today === item.date ? 'work-cell--active' : ''">
          <div flex items-center>
            <div v-if="item.crossMonth" font-semibold font-14>{{ item.month }}</div>
            <div v-else font-semibold font-14>{{ item.date.slice(-2) }}</div>
            <div v-if="today === item.date" class="work-cell-today ml-6px">今</div>
          </div>
          <div font-14 color-666>{{ item.dayCn }}</div>
        </div>
        <template v-if="item.works && item.works.length">
          <div v-for="work in item.works" :key="work._id" flex items-center pointer class="mb-10px work-cell-item"
            @click="handleClick($event, work)">
            <div flex-shrink-0 class="dot" :class="work.workTimeStamp > props.timestamp ? '' : 'dot--gray'"></div>
            <div flex-shrink-0 class="mr-6px">{{ work.noonTime }}</div>
            <div flex-grow class="w-0px truncate">{{ work.title }}</div>
            <!-- <div flex-shrink-0>{{ work.customerName }}</div> -->
          </div>
        </template>
        <div class="work-cell-bg" @click="addWork(item.date)"></div>
      </div>
    </div>

  </div>
</template>
<script setup>
import { computed } from 'vue';
import { WeekDay } from '@/baseData';
import dayjs from 'dayjs';
import useClick from '@/hooks/useClick';

const props = defineProps({
  days: { type: Array, default: () => [] },
  timestamp: { type: Number, default: 0 },
  width: { type: Number, default: 0 },
  works: { type: Object, default: () => ({}) }
})
const today = computed(() => {
  return dayjs(props.timestamp).format('YYYY-MM-DD')
})
const cellWidth = computed(() => {
  return Math.max(150, (props.width - 9) / 7)
})
const cellStyle = computed(() => {
  return { width: cellWidth.value + 'px' }
})
const noonCellStyle = computed(() => {
  return { ...cellStyle.value, minHeight: `calc((100vh - 280px) / ${groupWorks.value.length || 1})` }
})
const groupWorks = computed(() => {
  const list = props.days.map(day => ({
    ...day,
    works: props.works[day.date] || []
  }))
  const groups = []
  while (list.length) {
    groups.push(list.splice(0, 7))
  }
  return groups
})

const emits = defineEmits(['showDetail', 'toEdit', 'addWork']);
const handleClick = useClick({ delay: 400, click: showDetail, dbclick: toEdit })
function showDetail(detail) {
  emits('showDetail', detail)
}

function toEdit(work) {
  emits('edit', work)
}

function addWork(date) {
  emits('addWork', date)
}

</script>
<style>
@import "./work.css";

.dot {
  margin-right: 6px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #006eff;
}

.dot--gray {
  background-color: #666
}

.noon {
  writing-mode: vertical-lr;
}
</style>