<template>
  <el-dialog class="drop-dialog" :model-value="visible" :width="width" :show-close="false" @close="close()">
    <div>
      <template v-if="files.length > 0">
        <div class="pb-20px overflow-hidden">
          <div class="h-80px w-80px relative mx-auto" :style="{ transform: `translateX(-${(files.length - 1) * 15}px)` }">
            <img v-for="(item, idx) in files" :key="idx" class="absolute thumb top-0 h-80px w-80px" :src="item.isImage ? item.base64 || item.url : iconPdf" :alt="item.file.name" :style="`--thumb-z-index:${files.length + 2 - idx};--thumb-offset:${idx * 15}px`" />
          </div>
        </div>
        <div class="font-semibold px-10px pb-15px mb-10px text-14px text-dark-500 font-semibold text-center border-b border-dashed border-gray-200">是否要存入客户“{{ name }}”的健康档案?</div>
      </template>
      <div class="px-10px pt-5px pb-15px text-14px text-dark-500 font-semibold">请选择新增档案类型:</div>
      <div>
        <div v-for="(item, idx) in templist" :key="item.value" :label="item.label" class="py-12px px-10px flex items-center cursor-pointer border-b border-gray-200" :class="idx === 0 ? 'border-t' : ''" @click="select(item)">
          <div class="w-0 mr-10px flex-grow text-dark-500">{{ item.label }}</div>
          <el-icon class="text-16px">
            <ArrowRight />
          </el-icon>
        </div>
        <!-- <el-select v-model="value" class="w-full" placeholder="请选择">
          <el-option v-for="item in templist" :key="item.value" :label="item.label" :value="item.value" />
        </el-select> -->
      </div>
    </div>
    <!-- <template #footer>
      <div text-center>
        <el-button type="primary" plain class="w-100px" @click="close()">取消</el-button>
        <el-button class="w-100px" type="primary" @click="confirm()"> 确定新增档案 </el-button>
      </div>
    </template> -->
  </el-dialog>
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { templateStore } from "@/store/template";
import { ElMessage } from "element-plus";

const props = defineProps({
  files: { type: Array, default: () => [] },
  name: { type: String, default: "" },
  visible: { type: Boolean, default: false },
  width: { type: [String, Number], default: "80%" },
});

const iconPdf = new URL("@/assets/pdf.svg", import.meta.url).href;
const { corpTemplateList } = storeToRefs(templateStore());

const templist = computed(() => (corpTemplateList.value || []).filter((i) => i.parentTemplateType === "medicalRecord").map((i) => ({ value: i.templateType, label: i.name })));
const value = ref("");
const emits = defineEmits(["close", "confirm"]);
function select(item) {
  value.value = item.value;
  confirm();
}
function close() {
  emits("close");
}

function confirm() {
  const template = templist.value.find((i) => i.value === value.value);
  if (template) {
    emits("confirm", { templateType: value.value, files: props.files });
  } else {
    ElMessage.info("请选择新增档案类型");
  }
}
watch(
  () => props.visible,
  (val) => {
    if (val) value.value = "";
  }
);
</script>
<style lang="scss">
.drop-dialog .el-dialog__header {
  display: none;
}

.thumb {
  // left: -60px;
  border: 2px solid #fff;
  z-index: var(--thumb-z-index);
  left: var(--thumb-offset);
  background: #fff;
}
</style>