<template>
  <my-layout v-loading="loading">
    <layout-main class="text-14px" :scroll="false">
      <div class="h-full flex">
        <my-layout class="min-w-120px w-1/3 max-w-180px" flex-shrink-0 no-radius border-right>
          <layout-main>
            <classify-list :checked-cate="current" :data="cateList" @change="changeCurrent($event)" />
          </layout-main>
        </my-layout>
        <my-layout class="flex-grow">
          <layout-main>
            <empty-data v-if="list.length === 0" :size="240" text="暂无问卷" />
            <div v-for="item in list" :key="item._id" class="flex items-center p-10px border-b border-gray-100"
              @click="select(item)">
              <div class="flex items-center w-0 flex-grow">
                <div class="w-0 flex-grow">
                  <div class="truncate leading-20px">{{ item.name }} </div>
                  <div class="truncate leading-20px text-gray-500 text-12px">{{ item.description }} </div>
                </div>
              </div>
              <el-icon class="ml-5px flex-shrink-0" :class="survery._id === item._id ? '' : 'opacity-0'"
                color="#007aff">
                <CircleCheckFilled />
              </el-icon>
            </div>
          </layout-main>
        </my-layout>
      </div>

    </layout-main>
    <layout-item>
      <div class="p-15px flex bg-white shadow-up relative z-2" common-shadow--r>
        <el-button class="flex-grow" plain @click="cancel()">取消</el-button>
        <el-button class="flex-grow ml-15px" type="primary" @click="confirm()">确定</el-button>
      </div>
    </layout-item>
  </my-layout>
</template>
<script setup>
import { onMounted, ref } from 'vue';
// import { storeToRefs } from 'pinia';
// import { memberStore } from "@/store/member";
import { getList as getSurveyList, getSurveryCateList } from '@/api/survery';
import useChatBarSub from '../../useChatBarSub';
import classifyList from '@/components/classify-list/classify-list-side.vue';
import useClassifyList from '@/components/classify-list/useClassifyList';
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";

const emits = defineEmits(['cancel'])
const props = defineProps({
  eventName: { type: String, default: '' },
})
// const { memberInfo } = storeToRefs(memberStore());

const list = ref([]);
const more = ref(false)
const page = ref(1);
const loading = ref(false);
const survery = ref({});
const options = {
  getList: getSurveryCateList,
  loading,
  callback: ()=>{
    page.value = 1;
    getList()
  }
}

const { cateList, current, cateIds, changeCurrent } = useClassifyList(options);

// onMounted(() => getList())

function cancel() {
  emits('cancel')
  // uni.navigateBack()
}

function select(item) {
  survery.value = item
}

const { trigger } = useChatBarSub();
function confirm() {
  if (survery.value._id) {
    trigger.$invoke(props.eventName, { type: 'survery', data: survery.value });
    cancel()
  } else {
    ElMessage.info('请选择文章')
  }
}


async function getList() {
  if (loading.value) return;
  loading.value = true;
  const { data = {} } = await getSurveyList(page.value, 20, '', 'enable',false, cateIds.value)
  if (data && Array.isArray(data.list)) {
    list.value = data.list;
  } else {
    list.value = []
  }
  more.value = data.total > list.value.length;
  loading.value = false;
}

</script>
<style scoped>
.label--active::before {
  content: "";
  width: 3px;
  height: calc(100% - 2px);
  background: #0094ff;
  display: inline-block;
  margin-right: 5px;
  transform: translateY(1px);
  border-radius: 1px;
}
</style>