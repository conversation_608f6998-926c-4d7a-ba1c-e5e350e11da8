<template>
  <my-layout class="bg-white rounded">
    <layout-item>
      <div class="flex flex-wrap items-center px-15px py-10px border-b border-gray-100">
        <date-filter v-model="serviceDate" label="服务日期" :text="serviceDate" />
        <radio-filter v-model="rateStatus" label="评价状态" :list="rateStatusList" :width="200" />
        <input-filter v-model="customerName" label="客户名称" :width="200" />
        <el-button class="ml-30px" type="primary" @click="search()">查询</el-button>
        <el-button type="primary" @click="reset()" plain>重置</el-button>
      </div>
    </layout-item>
    <layout-main v-loading="loading" :scroll="false">
      <el-table stripe height="100%" :data="list">
        <el-table-column property="dateStr" label="服务日期" />
        <el-table-column property="customerName" label="服务客户">
          <template #default="{ row }">
            <el-text type="primary" class="cursor-pointer" @click="viewCustomer(row)">{{ row.customerName }}</el-text>
          </template>
        </el-table-column>
        <el-table-column property="count" label="服务次数">
          <template #default="{ row }">
            <el-text type="primary" class="cursor-pointer" @click="showRecord(row)">{{ row.count || 0 }}</el-text>
          </template>
        </el-table-column>
        <el-table-column property="customerName" label="评价状态">
          <template #default="{ row: { rated, expired } }">
            <!-- 成员待建档不显示昵称-->
            <span v-if="expired">已评价(超时默认好评)</span>
            <span v-else-if="rated">已评价</span>
            <span v-else-if="outRateTime">已过期（未发送评价）</span>
            <span v-else class="text-gray-500">未评价</span>
          </template>
        </el-table-column>
      </el-table>
    </layout-main>
    <layout-item>
      <div class="flex justify-end p-15px">
        <el-pagination class="ml-auto" layout="total, prev, pager, next, sizes, jumper" :current-page="page" :page-size="pageSize" :page-sizes="[10, 20, 50]" :total="total" @current-change="changePage" @size-change="changeSize"></el-pagination>
      </div>
    </layout-item>
  </my-layout>
  <service-table-modal :list="serviceList" :visible="visible" :width="width" @close="close" />
</template>
<script setup>
import { ref } from "vue";
import { useRouter } from "vue-router";
import dayjs from "dayjs";
import { ElMessage } from "element-plus";
import useElPagination from "@/hooks/useElPagination";
import { getCustomerDayServiceCount } from "@/api/todo.js";
import { getMemberRateRecord } from "@/api/knowledgeBase";
import useTeamId from "../useTeamId";
import { getCustomerServiceRecord } from "@/api/todo";
import useModal from "@/hooks/useModal";

import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import { InputFilter, DateFilter, RadioFilter } from "@/components/filter-bar";
import serviceTableModal from "./service-table-modal.vue";

const currentTeamId = useTeamId(search);

const router = useRouter();
const loading = ref(false);
const serviceDate = ref(dayjs().format("YYYY-MM-DD"));
const rateStatus = ref("");
const customerName = ref("");
const rateStatusList = [
  { label: "已评价", value: "rated" },
  { label: "待评价", value: "init" },
  { label: "已过期", value: "expired" },
];
const rates = ref([]);

const { page, pageSize, changePage, changeSize } = useElPagination(getList);
const list = ref([]);
const total = ref(0);
let payload = {};

function search() {
  const params = {};
  if (rateStatus.value) params.rateStatus = rateStatus.value;
  if (serviceDate.value) params.date = serviceDate.value;
  if (customerName.value.trim()) params.customerName = customerName.value.trim();
  payload = { ...params };
  changePage(1);
}

async function getList() {
  loading.value = true;
  const { success, data, message } = await getCustomerDayServiceCount({
    ...payload,
    page: page.value,
    pageSize: pageSize.value,
    userId: localStorage.getItem("userId"),
    corpId: localStorage.getItem("corpId"),
    teamId: currentTeamId.value,
  });
  if (success) {
    const rates = await getMemberRateList(data.list);
    const rateList = Array.isArray(rates)
      ? rates.map((i) => {
          const expired = !i.updateTime && Date.now() > i.expireTime; // 评价时间不存在且过期为已过期
          const rated = Boolean(i.updateTime) || expired; // 评价时间存在或已过期为已评价
          return {
            ...i,
            dateStr: i.createTime && dayjs(i.createTime).isValid() ? dayjs(i.createTime).format("YYYY-MM-DD") : "",
            rated,
            expired,
          };
        })
      : [];
    const rateMap = rateList.reduce((prev, i) => {
      prev[`_${i.dateStr}_${i.customerId}`] = i;
      return prev;
    }, {});

    list.value = Array.isArray(data.list)
      ? data.list.map((i) => {
          const item = rateMap[`_${i.dateStr}_${i.customerId}`] || { noRateRecord: true };
          return {
            ...i,
            ...item,
            outRateTime: i.rateExpireTime < Date.now() && i.noRateRecord, // 超过评价过期时间 且没有评价记录 视为未发送给客户评价
            rated: item.rated || rateStatus === "rated",
          };
        })
      : [];
    total.value = data.total > 0 ? data.total : 0;
  } else {
    ElMessage.error(message);
  }
  loading.value = false;
}

async function getMemberRateList(list) {
  const customerIds = list.map((i) => i.customerId);
  const minTime = Math.min(...list.map((i) => i.date).filter((i) => typeof i === "number"));
  const maxTime = Math.max(...list.map((i) => i.rateExpireTime).filter((i) => typeof i === "number"));
  const query = {
    corpId: localStorage.getItem("corpId"),
    customerIds,
    userId: localStorage.getItem("userId"),
    teamId,
    minTime,
    maxTime,
  };
  const { data, success } = await getMemberRateRecord(query);
  if (success) {
    return Array.isArray(data.rateList) ? data.rateList : [];
  } else {
    return [];
  }
}

function viewCustomer(row) {
  row.customerId && router.push({ name: "DETAIL", params: { id: row.customerId } });
}

function reset() {
  rates.value = [];
  rateStatus.value = "";
  serviceDate.value = "";
  search();
}

const { visible, show, close, width } = useModal(800);
const serviceList = ref([]);
async function showRecord(i) {
  const { corpId, customerId, userId, teamId, createTime: executionTime } = i;
  const { success, data, message } = await getCustomerServiceRecord({
    corpId,
    customerId,
    userId,
    teamId,
    executionTime,
  });
  if (success) {
    serviceList.value = data && Array.isArray(data.data) ? data.data : [];
    show();
  } else {
    ElMessage.error(message);
  }
}
</script>
