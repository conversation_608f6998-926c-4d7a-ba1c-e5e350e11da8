<template>
  <el-dialog :model-value="visible" :width="width" title="切换团队信息" @close="close">
    <div px-15 class="text-14px pt-6px pb-12px" color-666>请选择团队</div>
    <el-scrollbar wrap-style="max-height: calc(100vh - 360px)">
      <div v-for="item in teams" :key="item.teamId" class="px-15px pointer rounded-4px customer-item flex items-center"
        @click="select(item)">
        <div class="flex-grow py-12px text-14px text-black">{{ item.name }}</div>
        <span color-primary flex-shrink-0 class="ml-10px" :class="teamId && teamId === item.teamId ? '' : 'opacity-0'">
          <el-icon size="20"><Select /></el-icon>
        </span>
      </div>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button class="w-100px" @click="close">取消</el-button>
        <el-button class="w-100px" type="primary" @click="confirm()"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ElMessage } from 'element-plus';
import { ref, toRefs, watch } from 'vue';

const props = defineProps({
  team: { type: Object, default: () => ({}) },
  teams: { type: Array, default: () => [] },
  visible: { type: Boolean, default: false },
  width: { type: Number, default: 520 }
})
const { team, teams, visible, width } = toRefs(props)
const emits = defineEmits(['close', 'select', 'send']);
function close() {
  emits('close')
}

const teamId = ref('')
function select(item) {
  teamId.value = item.teamId;
}

function confirm() {
  if (teams.value.some(i => i.teamId === teamId.value && teamId.value)) {
    close()
    emits('select', teamId.value)
  } else {
    ElMessage.info('请选择团队')
  }

}
watch(visible, n => {
  if (n) teamId.value = team.value && team.value.teamId ? team.value.teamId : ''
})
</script>
<style lang="scss" scoped>
.customer-item:hover {
  background: #FFFAFA;
}
</style>
