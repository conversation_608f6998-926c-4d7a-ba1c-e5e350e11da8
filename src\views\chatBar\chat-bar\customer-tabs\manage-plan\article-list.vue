<template>
  <my-layout v-loading="loading">
    <layout-main class="text-14px" :scroll="false">
      <div class="h-full flex">
        <el-scrollbar class="w-120px flex-shrink-0 border-r border-gray-100">
          <!-- <div v-for="cate in cates" :key="cate._id"
            :class="currentCate && cate._id === currentCate._id ? 'text-blue-500' : ''"
            class="p-10px border-r border-gray-100" @click="toggle(cate)">
            {{ cate.name }}
          </div> -->
          <classify-list :checked-cate="current" :data="cateList" @change="changeCurrent($event)" />

        </el-scrollbar>
        <el-scrollbar class="flex-grow">
          <empty-data v-if="list.length === 0" :size="240" title="暂无文章" />
          <div v-for="item in  list " :key="item._id" class="flex items-center p-10px border-r border-gray-100"
            @click="select(item)">
            <div class="flex items-center w-0 flex-grow">
              <img v-if="item.cover" :src="item.cover" class="w-40px h-40px flex-shrink-0 rounded-4px mr-10px" />
              <div class="w-0 flex-grow">
                <div class="truncate leading-20px">{{ item.title }} </div>
                <div class="truncate leading-20px text-gray-500 text-12px">{{ item.summary }} </div>
              </div>
            </div>
            <el-icon class="ml-5px flex-shrink-0" :class="article._id === item._id ? '' : 'opacity-0'" color="#007aff">
              <CircleCheckFilled />
            </el-icon>
            <!-- <uni-icons class=" ml-5px flex-shrink-0" :type="article._id === item._id ? 'circle-filled' : 'circle'"
              :color="article._id === item._id ? '#007aff' : ''"></uni-icons> -->
          </div>
        </el-scrollbar>
      </div>

    </layout-main>
    <layout-item>
      <div class="p-15px flex bg-white shadow-up relative z-2" common-shadow--r>
        <el-button class="flex-grow" plain @click="cancel()">取消</el-button>
        <el-button class="flex-grow ml-15px" type="primary" @click="confirm()">确定</el-button>
      </div>
    </layout-item>
  </my-layout>
</template>
<script setup>
import { onMounted, ref, watch } from 'vue';
import { storeToRefs } from 'pinia'
import { memberStore } from "@/store/member";
import { getArticleCates, getArticleList, getArticleCateList } from '@/api/knowledgeBase'
import useChatBarSub from '../../useChatBarSub';
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import EmptyData from "@/components/empty-data.vue";
import { ElMessage } from 'element-plus';
import classifyList from '@/components/classify-list/classify-list-side.vue';
import useClassifyList from '@/components/classify-list/useClassifyList';

const emits = defineEmits(['cancel'])
const props = defineProps({
  eventName: { type: String, default: '' },
})
const { memberInfo } = storeToRefs(memberStore());


function cancel() {
  emits('cancel')
  // uni.navigateBack()
}

const currentCate = ref({});
const list = ref([]);
const page = ref(1);
const more = ref(false)
const article = ref({});
const loading = ref(false);

const { trigger } = useChatBarSub();

const options = {
  getList: getArticleCateList,
  callback: handleCurrentChang,
  loading
}

const { cateList, current,cateIds, changeCurrent } = useClassifyList(options);
function handleCurrentChang() {
  page.value = 1;
  getList()
}

function select(item) {
  article.value = item;
}


function confirm() {
  if (article.value._id) {
    trigger.$invoke(props.eventName, { type: 'article', data: article.value });
    cancel()
  } else {
    ElMessage.info('请选择文章')
  }
}

async function getList() {
  if (loading.value) return;
  loading.value = true;
  const { data } = await getArticleList({ corpId: memberInfo.value.corpId, page: page.value, pageSize: 20, enable: true, cateIds: cateIds.value });
  const { list: articleList = [], total: count = 0 } = data;
  list.value = Array.isArray(articleList) ? articleList : [];
  more.value = count > list.value;
  loading.value = false;
}

</script>
<style scoped>
.label--active::before {
  content: "";
  width: 3px;
  height: calc(100% - 2px);
  background: #0094ff;
  display: inline-block;
  margin-right: 5px;
  transform: translateY(1px);
  border-radius: 1px;
}
</style>