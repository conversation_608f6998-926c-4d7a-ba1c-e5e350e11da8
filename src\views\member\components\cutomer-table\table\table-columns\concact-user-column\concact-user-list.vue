<template>
  <div class="">
    <span v-for="(i, idx) in data" :key="customerId + '_' + i">
      <span v-if="idx > 0">、</span>
      <ww-user :openid="i"></ww-user>
    </span>
  </div>
</template>
<script setup>
import WwUser from "@/components/ww-user/index.vue";
defineProps({
  customerId: {
    type: String,
    default: "",
  },
  data: {
    type: Array,
    default: () => [],
  },
});
</script>
<style lang="scss" scoped></style>
