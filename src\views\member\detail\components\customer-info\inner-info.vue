<template>
  <customer-view operateType="edit" :templateList="templateList" @onClose="close" @onSuccess="success"
    :formData="selfCustomer" :filter-rule="filterRule" :headerHeight="0">
    <template #other>
      <el-row>
        <el-col :span="24">
          <el-form-item label="分组"
            v-if="customer.groupIds && Array.isArray(customer.groupIds) && customer.groupIds.length > 0">{{
            getGroupName(customer.groupIds) }}</el-form-item>
          <el-form-item label="卡号" v-if="customer.cardNo">{{ customer.cardNo }}</el-form-item>
          <!-- <el-form-item label="回访计划">1212</el-form-item> -->
        </el-col>
      </el-row>
    </template>
  </customer-view>
</template>
<script setup>
import customerView from "../../../customer/customer-view.vue";
import dayjs from "dayjs";
import { templateStore } from "@/store/template";
import { watch, ref, onMounted, computed } from "vue";
import { storeToRefs } from "pinia";
import { updateMember as updateMemberUrl } from "@/api/member";
import { ElMessage } from "element-plus";
import { configStore } from "@/store/config";
const { getGroupName } = configStore();
const close = () => {
  getInitData(props.customer);
};
const emit = defineEmits(["reload"]);
const store = templateStore();
const { corpTemplateList } = storeToRefs(store);
const { getCorpTemplate } = store;
const props = defineProps({ customer: { type: Object, default: () => ({}) } });
const hiddenFormItems = { tagIds: true, notes: true, createTime: true }

const templateList = ref([
  {
    name: "内部信息",
    type: "internalTemplate",
  },
]);
const selfCustomer = computed(() => {
  if (props.customer.assistantByMobile) {
    props.customer.assistantByMobile.forEach((item, index) => {
      const { note, mobile } = item;
      props.customer[`phone${index + 1}`] = mobile;
      props.customer[`phone${index + 1}Note`] = note;
    });
  }
  return JSON.parse(JSON.stringify(props.customer));
});

watch(
  () => props.customer,
  (val) => {
    if (val) {
      getInitData(val);
    }
  },
  { immediate: true }
);
async function getInitData(customer) {
  if (!Array.isArray(corpTemplateList.value) || corpTemplateList.value.length === 0) {
    await getCorpTemplate();
  }
  templateList.value = templateList.value.map((item) => {
    const temp = corpTemplateList.value.find((i) => i.templateType === item.type);
    if (!temp) return item;
    item.templateList =
      temp && Array.isArray(temp.templateList)
        ? temp.templateList.map((e) => {
          const row = { ...e };
          if (customer[e.title] && e.type === "date") {
            row.value = dayjs(customer[e.title]).isValid() ? dayjs(customer[e.title]).format(e.format || "YYYY-MM-DD") : "";
          } else if (customer[e.title]) {
            row.value = customer[e.title];
          }
          return row;
        }).filter((i) => !hiddenFormItems[i.title])
        : [];
    return item;
  });
}
const success = async (item, callback) => {
  const { success, message } = await updateMemberUrl(props.customer._id, item);
  if (success) {
    emit("reload");
    callback && callback("success");
    ElMessage.success(message);
  } else {
    callback && callback("fail");
    ElMessage.error(message);
  }
};
const filterRule = {
  reference(form) {
    const customerSource = Array.isArray(form.customerSource) ? form.customerSource : [];
    const res = ["同事推荐", "客户推荐"].includes(customerSource[0]) && customerSource.length === 1;
    return res;
  },
};
</script>

<style lang="scss">
.group-title {
  padding: 0 12px 16px;
  color: var(--yc-main-text-color);
  font-weight: 600;
  font-size: 14px;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}
</style>
