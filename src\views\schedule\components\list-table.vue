<template>
  <el-table stripe height="100%" :data="list" v-loading="props.loading">
    <el-table-column property="executionTime" label="服务时间" v-if="props.isServiceType"
      :min-width="150"></el-table-column>
    <el-table-column property="taskType" :label="props.isServiceType ? '服务类型' : '待办类型'" :min-width="150" />
    <el-table-column property="taskContent" :label="props.isServiceType ? '服务内容' : '待办内容'" :min-width="200">
      <template #default="{ row }">
        <el-popover v-if="row.taskContent" placement="top-start" :width="300" trigger="hover">
          <template #reference>
            <el-text truncated class="pointer" style="transform: translateY(4px)">
              <content-userid-transform :content="row.taskContent"></content-userid-transform>
            </el-text>
          </template>
          <content-userid-transform :content="row.taskContent"></content-userid-transform>
        </el-popover>
      </template>
    </el-table-column>
    <el-table-column property="customerName" label="客户">
      <template #default="{ row: { customerName, eventType } }" :min-width="70">
        <!-- 成员待建档不显示昵称-->
        <span v-if="eventType == 'remindFiling'"></span>
        <span v-else>{{ customerName }}</span>
      </template>
    </el-table-column>
    <el-table-column property="executorUserId" label="处理人" :min-width="70">
      <template #default="{ row: { executorUserId } }">
        <span v-if="executorUserId === 'system'">系统自动</span>
        <ww-user v-else-if="executorUserId" :openid="executorUserId"></ww-user>
      </template>
    </el-table-column>
    <el-table-column property="teamName" label="所属团队" :min-width="160"></el-table-column>
    <template v-if="props.tab === ToDoTaskType.pendding || props.tab === ToDoTaskType.common">
      <el-table-column property="creatorUserId" label="创建人">
        <template #default="{ row: { creatorUserId } }">
          <span v-if="creatorUserId === 'system'">系统自动</span>
          <ww-user v-else-if="creatorUserId" :openid="creatorUserId"></ww-user>
        </template>
      </el-table-column>
      <el-table-column v-if="props.tab === ToDoTaskType.common" property="executorUserId" label="处理人" :min-width="70">
        <template #default="{ row: { executorUserId } }">
          <span v-if="executorUserId === 'system'">系统自动</span>
          <ww-user v-else-if="executorUserId" :openid="executorUserId"></ww-user>
        </template>
      </el-table-column>
      <el-table-column property="plannedExecutionTime" label="计划执行时间" :min-width="150" />

      <el-table-column property="expireTime" label="失效时间" :min-width="150">
        <template #default="{ row: { expireTime, willBeExpired } }">
          <span>{{ expireTime }}</span>
          <el-text v-if="willBeExpired" type="danger">（即将失效）</el-text>
        </template>
      </el-table-column>
    </template>
    <template v-if="props.tab === ToDoTaskType.teamEnd">
      <el-table-column property="creatorUserId" label="创建人" :min-width="70">
        <template #default="{ row: { creatorUserId } }">
          <span v-if="creatorUserId === 'system'">系统自动</span>
          <ww-user v-else-if="creatorUserId" :openid="creatorUserId"></ww-user>
        </template>
      </el-table-column>
      <el-table-column property="executorUserId" label="执行人" :min-width="70">
        <template #default="{ row: { executorUserId } }">
          <span v-if="executorUserId === 'system'">系统自动</span>
          <ww-user v-else-if="executorUserId" :openid="executorUserId"></ww-user>
        </template>
      </el-table-column>
      <el-table-column property="taskStatus" label="任务状态" :min-width="100" />
      <el-table-column property="endTime" label="结束时间" :min-width="150" />
    </template>

    <template v-if="props.tab === ToDoTaskType.end">
      <el-table-column property="executorUserId" label="处理人" :min-width="70">
        <template #default="{ row: { executorUserId } }">
          <span v-if="executorUserId === 'system'">系统自动</span>
          <ww-user v-else-if="executorUserId" :openid="executorUserId"></ww-user>
        </template>
      </el-table-column>
      <el-table-column property="taskStatus" label="任务状态" :min-width="100" />
      <el-table-column property="endTime" label="处理时间" :min-width="150">
        <template #default="{ row: { eventStatus, endTime } }">
          {{ endTime }}
        </template>
      </el-table-column>
    </template>
    <el-table-column fixed="right" class-name="el-table-fixed-column--shadow" label="操作" width="150">
      <template #default="{ row }">
        <div flex items-center>
          <template v-if="props.isServiceType">
            <el-text type="primary" class="mr-2" pointer @click="serviceEditRecord(row)">编辑</el-text>
            <el-text v-if="row.pannedEventSendFile && Object.keys(row.pannedEventSendFile).length > 0" type="primary"
              class="mr-2" pointer @click="read(row.pannedEventSendFile, row)">{{ row.eventType === "questionnaire" ?
              "查看问卷" : "宣教文章" }}</el-text>
          </template>
          <template v-else>
            <el-text type="primary" class="mr-10" pointer @click="showDetail(row)">任务详情</el-text>
            <el-text v-if="wxContact[row.customerUserId] || wxContact[row.externalUserId]" type="primary" pointer
              @click="toChat(row)">聊天</el-text>
          </template>
        </div>
      </template>
    </el-table-column>
  </el-table>
  <create-service ref="createServiceRef" @remove="removeServiceRecord" @save="onSaveService"></create-service>
  <survery-dialog :data="currentSurvery" :visible="visible" @close="visible = false" />
</template>
<script setup>
import { computed, ref, watch, onBeforeMount } from "vue";
import { storeToRefs } from "pinia";
import { useRouter } from "vue-router";
import { openChatWindow } from "@/utils/common";
import { memberStore } from "@/store/member";
import wxContact from "@/store/wxContact";

import { getDetail, getAnswer } from "@/api/survery";
import dayjs from "dayjs";
import { isURL } from "@/utils/common";
import { updateServiceRecord } from "@/api/todo";
import SurveryDialog from "@/views/system/survery/dialog.vue";
import { ToDoEventType, ToDoTaskType, ToDoTaskStatus, ServiceType } from "@/baseData";
import WwUser from "@/components/ww-user/index.vue";
import createService from "@/components/create-service/index.vue";
import contentUseridTransform from "@/components/content-userid-transform/index.vue";
let $emits = defineEmits(["getList"]);
const props = defineProps({
  list: { type: Array, default: () => [] },
  loading: { type: Boolean, default: false },
  tab: { type: String, default: "" },
  isServiceType: { type: Boolean, default: false },
  serviceType: { type: String, default: "" },
});
const store = memberStore();
const { wxContact } = storeToRefs(store);
const { getContacts } = wxContact();
const { contactName } = storeToRefs(wxContact());

const router = useRouter();
async function toChat(item) {
  const { customerUserId, externalUserId } = item;
  if (contactName.value[customerUserId]) {
    openChatWindow(item.customerId, customerUserId, "", props.isServiceType ? {} : { name: "todo", time: new Date().getTime() });
  } else if (contactName.value[externalUserId]) {
    openChatWindow(item.customerId, externalUserId, "", props.isServiceType ? {} : { name: "todo", time: new Date().getTime() });
  }
}
const list = computed(() => {
  return props.list.map((i) => {
    const status = ToDoTaskStatus.find((item) => item.value === i.eventStatus);
    const willBeExpired = i.expireTime ? dayjs().isAfter(dayjs(i.expireTime).subtract(2, "days")) && dayjs().isBefore(dayjs(i.expireTime)) : false;
    return {
      ...i,
      willBeExpired,
      taskStatus: status ? status.label : "",
      taskType: ServiceType[i.eventType] || "",
      executionTime: dayjs(i.executionTime).format(`YYYY-MM-DD`),
      createTime: i.createTime ? dayjs(i.createTime).format("YYYY-MM-DD HH:mm") : "",
      planTime: i.plannedExecutionTime ? dayjs(i.plannedExecutionTime).format("YYYY-MM-DD") : "",
      expireTime: i.expireTime ? dayjs(i.expireTime).format("YYYY-MM-DD") : "",
      plannedExecutionTime: i.plannedExecutionTime ? dayjs(i.plannedExecutionTime).format("YYYY-MM-DD HH:mm") : "",
      endTime: i.endTime ? dayjs(i.endTime).format("YYYY-MM-DD HH:mm") : "",
    };
  });
});

function showDetail(row) {
  router.push({ name: "SCHEDULEDETAIL", params: { id: row._id } });
}

function showCustomerDetail({ customerId }) {
  if (customerId) {
    router.push({ name: "DETAIL", params: { id: customerId } });
  }
}

const visible = ref(false);
const currentSurvery = ref({});
const ruleFormRef = ref("");
const dialogVisible = ref(false);
let selectRecord = ref({});
const ruleForm = ref({
  taskContent: "",
  executionTime: "",
});
async function dialogSubmit(formEl) {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      await updateRecord();
    }
  });
}
async function updateRecord() {
  const params = {
    taskContent: ruleForm.value.taskContent,
  };
  if (ruleForm.value.executionTime) {
    params["executionTime"] = new Date(ruleForm.value.executionTime).getTime();
  }
  let res = await updateServiceRecord(selectRecord.value._id, params);
  $emits("getList");
  dialogVisible.value = false;
}
// 删除服务记录
// async function removeServiceRecord(id) {
//   ElMessageBox.confirm("是否删除该服务记录?", '提示', { type: 'warning' }).then(async () => {
//     let { success, message } = await removeRecord(id);
//     if (success) {
//       ElMessage.success('删除成功');
//       $emits('getList')
//     } else {
//       ElMessage.error(message);
//     }
//   });
// }
function removeServiceRecord() {
  $emits("getList");
}
// 编辑服务记录
const createServiceRef = ref("");

async function serviceEditRecord(item) {
  createServiceRef.value.open("edit", null, item);
}

function onSaveService() {
  $emits("getList");
}
async function read(item, row) {
  if (item.type === "article") {
    isURL(item.url) && window.open(item.url);
  } else {
    await getSurvery(item, row.customerId);
    visible.value = true;
  }
}

async function getSurvery(item, memberId) {
  const { surveryId, answerId } = item;
  if (answerId && surveryId && memberId) {
    const { data } = await getAnswer(surveryId, memberId, answerId);
    if (data && data.record && data.record.submitTime) {
      currentSurvery.value = data && data.record ? data.record : {};
      return;
    }
  }
  surveryId && getSurveryDetail(surveryId);
}
async function getSurveryDetail(id) {
  const corpId = localStorage.getItem("corpId");
  const { data } = await getDetail(corpId, id);
  currentSurvery.value = data && data.data ? data.data : {};
}
watch(() => props.list, val => {
  const maybeExternalUserIds = val.reduce((acc, cur) => {
    acc.push(cur.customerUserId, cur.externalUserId)
    return acc
  }, [])
  const data = Array.from(new Set(maybeExternalUserIds.filter(Boolean)))
  getContacts(data)
})
</script>
<style scoped>
.mr-10 {
  margin-right: 10px;
}
</style>
