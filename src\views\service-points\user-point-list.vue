<template>
  <my-layout bg-fff>
    <layout-item>
      <div class="flex justify-between mb-10px">
        <div class="pt-15px pr-15px pb-5px pl-25px text-14px">
          <span class="cursor-pointer text-gray-500 hover:text-blue-500" @click="back()">返回</span>
          <el-divider direction="vertical" />
          <span color-normal class="text-15px font-semibold">积分管理明细</span>
        </div>
        <div class="flex p-10px align-center pointer" @click="pointsExplanation">
          <el-icon color="#006eff" size="20"><Tickets /></el-icon>
          <div class="text-14px pl-5px">积分说明</div>
        </div>
      </div>

      <div class="px-15px pb-15px flex">
        <date-range-filter v-model:dates="dates" label="日期" :text="datesText" :width="320" />
        <trigger-condition-filter v-model:serviceTypes="serviceTypes" v-model:serviceRates="serviceRates" />
        <check-box-filter v-model="teamIds" label="服务团队" :list="teamList" />
        <el-button type="primary" class="ml-auto" @click="search">搜索</el-button>
      </div>
    </layout-item>
    <Layout-main :scroll="false">
      <el-table height="100%" :data="list">
        <el-table-column width="150" label="时间" prop="time">
          <template #default="{ row: { createTime } }">
            {{ dayjs(createTime).format("YYYY-MM-DD HH:mm") }}
          </template>
        </el-table-column>
        <el-table-column prop="condition" width="160" label="类型">
          <template #default="{ row: { eventType } }">
            {{ triggerConditionTitle(eventType) }}
          </template>
        </el-table-column>
        <el-table-column prop="detail" width="240" label="详情">
          <template #default="{ row }">
            <div class="truncate">{{ serviceTypeTitle(row) }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="rewardPoints" width="100" label="奖励积分">
          <template #default="{ row: { points } }">
            <div class="truncate">{{ points }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="teams" width="200" label="服务团队">
          <template #default="{ row: { executeTeamId } }">
            <div class="truncate">{{ getTeamName(executeTeamId) }}</div>
          </template>
        </el-table-column>
        <el-table-column width="120" prop="user" label="员工">
          <template #default="{ row: { executorUserId } }">
            <ww-user :openid="executorUserId"></ww-user>
          </template>
        </el-table-column>
        <el-table-column width="120" prop="customer" label="客户">
          <template #default="{ row }">
            <div class="truncate cursor-pointer text-blue-500" @click="show(row)">{{ row.customerName }}</div>
          </template>
        </el-table-column>
      </el-table>
    </Layout-main>
    <Layout-item>
      <div class="flex justify-between align-center flex-wrap">
        <div class="ml-20px w-120px text-14px">累计积分：{{ allPoints }} 分</div>
        <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize" @handle-current-change="changePage" />
      </div>
    </Layout-item>
  </my-layout>
  <customer-detail :visible="customerDetailVisible" :customerId="customerId" title="客户详情" customerType="corpCustomer" @close="customerDetailVisible = false" />
  <el-dialog v-model="pointsExplanationVisible" title="积分说明" @close="pointsExplanationVisible = false">
    <div class="dialog-box px-5px">
      <div class="font-600">一、员工服务积分概述</div>
      <div class="mt-10px text-14px leading-normal">员工在完成患者服务的特定任务后，可获得相应积分奖励。例如，当员工完成一个客户的一次就诊回访后，奖励积分5分。</div>
      <div class="mt-10px font-600">二、积分奖励规则</div>
      <el-table class="mt-10px" height="100%" :data="pointsTaskList" border>
        <el-table-column width="150" label="类型" prop="time">
          <template #default="{ row: { triggerCondition } }">
            <div class="truncate">{{ triggerConditionTitle(triggerCondition) }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="detail" width="240" label="详情">
          <template #default="{ row }">
            <div class="truncate">{{ pointsTypeTitle(row) }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="points" width="100" label="奖励积分" />
      </el-table>
    </div>
  </el-dialog>
</template>
<script setup>
import { computed, onMounted, ref } from "vue";
import { storeToRefs } from "pinia";
import { teamStore } from "@/store/team";
import useElPagination from "@/hooks/useElPagination";
import { getServicePointsStatistics } from "@/api/todo";
import { CheckBoxFilter, DateRangeFilter, RadioFilter } from "@/components/filter-bar";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import triggerConditionFilter from "./trigger-condition-filter.vue";
import WwUser from "@/components/ww-user/index.vue";
import { getCorpServicePointsList } from "@/api/todo";
import { ServiceType } from "@/baseData";
import dayjs from "dayjs";
import { useRoute } from "vue-router";
import { tagsStore } from "@/store/tags";
import customerDetail from "@/views/member/detail/index.vue";
import { getPointsTaskList } from "@/api/points";
const pointsExplanationVisible = ref(false);
const route = useRoute();
const { removeTag: removeRouteTag } = tagsStore();
const dates = ref([]);
const datesText = computed(() => dates.value.join(" ~ "));
const serviceTypes = ref([]);
const serviceRates = ref([]);
const { teams, allTeams } = storeToRefs(teamStore());
const teamList = computed(() => teams.value.map((i) => ({ label: i.name, value: i.teamId })));
const teamIds = ref([]);
const userIds = ref([]);
const list = ref([]);
const total = ref(0);
const pointsTaskList = ref([]);
const customerDetailVisible = ref(false);
const customerId = ref("");

const rateList = [
  { label: "一星", value: 1 },
  { label: "二星", value: 2 },
  { label: "三星", value: 3 },
  { label: "四星", value: 4 },
  { label: "五星", value: 5 },
];
const { page, pageSize, changePage, changeSize } = useElPagination(getList);
function search() {
  page.value = 1;
  getList();
}

function pointsExplanation() {
  pointsExplanationVisible.value = true;
  getPointsTask();
}

function serviceTypeTitle(row) {
  if (row.serviceRate) return `服务评价获得一次"${rateList.find((i) => i.value === row.serviceRate).label}"`;
  else return ServiceType[row.eventType] ? `完成一条类型为"${ServiceType[row.eventType]}"服务` : "";
}
function pointsTypeTitle(row) {
  if (row.serviceRate) return `服务评价获得一次"${rateList.find((i) => i.value === row.serviceRate).label}"`;
  else return ServiceType[row.serviceType] ? `完成一条类型为"${ServiceType[row.serviceType]}"服务` : "";
}
function triggerConditionTitle(type) {
  if (!type) return "";
  else if (type === "serviceRate") return "获得服务评价";
  else return "提供患者服务";
}
function getTeamName(teamId) {
  return allTeams.value.find((i) => i.teamId === teamId)?.name || "";
}

async function getList() {
  const { data } = await getCorpServicePointsList({
    page: page.value,
    pageSize: pageSize.value,
    dates: dates.value,
    serviceTypes: serviceTypes.value,
    serviceRates: serviceRates.value,
    executeTeamIds: teamIds.value,
    executorUserIds: [localStorage.getItem("userId")],
  });
  list.value = data.list;
  total.value = data.total;
}

async function getPointsTask() {
  const params = {
    page: 1,
    pageSize: 1000,
    taskStatus: "enable",
  };
  const { data } = await getPointsTaskList(params);
  pointsTaskList.value = data.list;
}

const allPoints = ref(0);
async function getStatistics() {
  const { data } = await getServicePointsStatistics({
    page: page.value,
    pageSize: pageSize.value,
    dates: dates.value,
    serviceTypes: serviceTypes.value,
    serviceRates: serviceRates.value,
    executeTeamIds: teamIds.value,
    executorUserIds: [localStorage.getItem("userId")],
  });
  allPoints.value = data ? data.allPoints : 0;
}
onMounted(async () => {
  if (route.params.dates) {
    dates.value = JSON.parse(route.params.dates);
  }
  getList();
  getStatistics();
});

function back() {
  removeRouteTag(route.fullPath);
}

function show(data) {
  if (data.customerId) {
    customerId.value = data.customerId;
    customerDetailVisible.value = true;
  }
}
</script>

<style lang="scss" scoped>
.dialog-box {
  max-height: 500px;
  overflow-y: auto;
  min-height: 300px;
}
</style>