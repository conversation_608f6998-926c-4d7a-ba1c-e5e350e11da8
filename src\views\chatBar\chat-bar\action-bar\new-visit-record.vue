<template>
  <!-- <my-layout bg-fff>
    <layout-main>
      <div class="pb-20px">
        <div font-16 font-semibold px-15 border-bottom class="py-12px mb-6px">{{ temp.name }}</div>
        <form-template ref="formRef" :items="showlist" :rule="rule" :form="form" @change="change($event)" />
      </div>
    </layout-main>
    <layout-item>
      <div class="py-12px px-15px text-center flex" common-shadow--r>
        <el-button class="w-100px flex-grow" plain @click="close()">取消</el-button>
        <el-button class="w-100px flex-grow" type="primary" :loading="loading" @click="confirm()">确定</el-button>
      </div>
    </layout-item>
  </my-layout> -->
  <page-wrapper v-loading="tempLoading" @back="close()">
    <template #header>
      <div font-16 font-semibold px-15 border-bottom class="py-6px">{{ temp.name }}</div>
    </template>
    <form-template ref="formRef" :items="showlist" :rule="rule" :form="forms" @change="change($event)" />
    <template #footer>
      <div class="flex py-12px px-15px text-center bg-white relative z-2" common-shadow--r>
        <el-button class="w-100px flex-grow" plain @click="close()">取消</el-button>
        <el-button class="w-100px flex-grow" type="primary" :loading="loading" @click="confirm()">确定</el-button>
      </div>
    </template>
  </page-wrapper>
</template>
<script setup>
import { computed, onMounted, onUnmounted, ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import { addMedicalRecord, updateMedicalRecord } from "@/api/member.js";
import { memberStore } from "@/store/member";
import { templateStore } from "@/store/template";
import useChatBarSub from "@/views/chatBar/chat-bar/useChatBarSub";
import { checkNumber } from "../form-template/edit/verify";
import { createServiceRecord } from "@/utils/service";
import PageWrapper from "../../components/page-wrapper.vue";

import formTemplate from "../form-template/edit/index.vue";

const props = defineProps({
  record: { type: Object, default: () => ({}) },
  customer: { type: Object, default: () => ({}) },
  teams: { type: Array, default: () => [] },
  files: { type: Array, default: () => [] },
  templateType: { type: String, default: "outpatient" },
});
const emits = defineEmits(["close"]);

const { trigger } = useChatBarSub();

const { memberInfo } = storeToRefs(memberStore());
const store = templateStore();
const { getTempateByType } = store;
const { corpTemplateList } = storeToRefs(store);

const formRef = ref();
const rule = {
  amount(value, name) {
    const res = checkNumber(value, 0, 9999999);
    if (res === true) return true;
    else if (typeof res === "string") return `${name}: ${res}`;
    return `请输入有效的${name}`;
  },
};
const form = ref({});
const forms = computed(() => ({ ...props.record, ...form.value }));
const temp = computed(() => {
  const temp = Array.isArray(corpTemplateList.value) ? corpTemplateList.value.find((i) => i.templateType === props.templateType) : null;
  const tempList = temp && Array.isArray(temp.templateList) ? temp.templateList.filter((i) => i && i.fieldStatus !== "disable" && i.operateType !== "onlyRead") : [];
  return { ...(temp || {}), tempList };
});

const showlist = computed(() =>
  temp.value.tempList.filter((i) => {
    if ("referenceField" in i && typeof i.referenceField === "string") {
      if (forms.value[i.referenceField] === i.referenceValue) return true;
      return false;
    }
    return true;
  })
);

// 模板列表存在关联关系 需要根据关联关系动态展示表单
const relatMap = computed(() =>
  temp.value.tempList.reduce((val, item) => {
    if ("referenceField" in item && typeof item.referenceField === "string") {
      let index = val.findIndex((i) => i.key === item.referenceField && i.value === i.referenceValue);
      if (index === -1) {
        val.push({
          key: item.referenceField,
          value: item.referenceValue,
          props: [],
        });
        index = val.length - 1;
      }
      val[index].props.push(item.title);
    }
    return val;
  }, [])
);

onMounted(() => {
  const item = showlist.value.find((i) => i.type === "files");
  if (item && props.files.length) {
    form.value[item.title] = props.files;
  }
});

function change({ title, value }) {
  form.value[title] = value;
  const item = relatMap.value.find((i) => i.key === title);
  if (item) item.props.forEach((prop) => (form.value[prop] = ""));
}

function close() {
  emits("close");
}

const loading = ref(false);
async function confirm() {
  if (loading.value || !formRef.value.verify()) return;
  loading.value = true;
  const res = await formRef.value.beforeSave();
  if (!res) {
    loading.value = false;
    return;
  }
  const id = props.record && props.record._id ? props.record._id : "";
  const params = {
    ...form.value,
    corpId: memberInfo.value.corpId,
    memberId: props.customer._id,
    customerName: props.customer.name,
    medicalType: props.templateType,
  };
  if (id) {
    params._id = props.record._id;
    params.userId = memberInfo.value.userid;
  } else {
    params.creator = memberInfo.value.userid;
    const sortTimeKey = temp.value && temp.value.service && temp.value.service.timeTitle ? temp.value.service.timeTitle : "";
    if (sortTimeKey && form.value[sortTimeKey] && dayjs(form.value[sortTimeKey]).isValid()) {
      params.sortTime = dayjs(form.value[sortTimeKey]).valueOf();
    } else {
      params.sortTime = dayjs().valueOf();
    }
  }
  const { success, message } = await (id ? updateMedicalRecord : addMedicalRecord)(params);
  if (success) {
    ElMessage.success(message);
    trigger.$invoke("change-customer-visit-record");
    // if (!id) createServiceRecordAction(params.sortTime);
    close();
  } else {
    ElMessage.error(message);
  }
  loading.value = false;
}

async function createServiceRecordAction(time) {
  if (!time) return;
  const { timeName } = temp.value.service;
  const anotherName = memberInfo.value.anotherName || "";
  let taskContent = `${anotherName}为客户${props.customer.name}新增一份${temp.value.name}`;
  if (time && dayjs(time).isValid()) {
    taskContent += `，${timeName}为${dayjs(time).format("YYYY-MM-DD")}`;
  }
  const currentTeam = props.teams[0];
  let item = {
    taskContent,
    executionTime: new Date().getTime(),
    customerId: props.customer._id,
    executeTeamId: currentTeam ? currentTeam.teamId : "",
    teamName: currentTeam ? currentTeam.name : "",
    eventType: "remindFiling",
    customerName: props.customer.name,
  };
  await createServiceRecord(item);
  trigger.$invoke("update-service-record");
}

onMounted(() => {
  trigger.$invoke("sub-sidebar-dropFiles", onDrop);
  getLatestTemp();
});
onUnmounted(() => trigger.$invoke("unsub-sidebar-dropFiles", onDrop));
async function onDrop(files) {
  const item = showlist.value.find((i) => i.type === "files");
  const value = Array.isArray(forms.value[item.title]) ? forms.value[item.title] : [];
  if (!item || files.length === 0) return;
  await ElMessageBox.confirm(`是否把文件加入该份档案？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    cancelButtonClass: "w-100px",
    confirmButtonClass: "w-100px",
    type: "warning",
  });
  const list = [...value, ...files];
  form.value[item.title] = list;
}

const tempLoading = ref(false);
async function getLatestTemp() {
  tempLoading.value = true;
  await getTempateByType(props.templateType);
  tempLoading.value = false;
}
</script>
<style lang="scss" scoped></style>
