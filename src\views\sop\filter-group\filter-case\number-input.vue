<template>
  <div class="flex items-center">
    <template v-if="type === 'range'">
      <el-input v-model.trim="data.min" clearable class="w-100px" @blur="handleBlur('min')" />
      <span class="mx-5px text-14px text-gray-500">~</span>
      <el-input v-model.trim="data.max" clearable class="w-100px" @blur="handleBlur('max')" />
    </template>
    <el-input-number v-else v-model="inputValue" :precision="decimalLength" :min="min" :max="max"
      controls-position="right" @blur="onBlur" />
    <span v-if="unit" class="mx-5px text-14px text-gray-500">{{unit}}</span>
  </div>
</template>
<script setup>
import { computed } from 'vue';
import { useVModel } from '@vueuse/core';
import { ElMessage } from 'element-plus';

const emit = defineEmits(['update:modelValue', 'update:value'])
const props = defineProps({
  precision: { type: Number, default: 2 }, //小数位数
  integer: { type: Boolean, default: true }, // 是否只能输入整数
  max: { type: Number, default: 10000 }, // 最大值
  min: { type: Number, default: 0 }, // 最小值
  value: { type: [String, Number], default: '' }, // 值
  modelValue: { type: Object, default: () => ({}) }, //  {min: '', max: ''}
  type: { type: String, default: 'single', validtor: val => ['range', 'single'].includes(val) }, // 类型
  unit: { type: String, default: '' }, // 单位
})
const decimalLength = computed(() => props.integer ? 0 : props.precision)
const data = useVModel(props, 'modelValue', emit);
const inputValue = useVModel(props, 'value', emit);

function handleBlur(type) {
  data.value[type] = getValidNumber(data.value[type]);
  if ([data.value.min, data.value.max].every(i => typeof i == 'number' || (typeof i === 'string' && i.trim() !== '' && Number(i.trim()) > 0))) {
    [data.value.min, data.value.max] = [data.value.min - 0, data.value.max - 0].sort((a, b) => a - b)
  }
}

function onBlur() {
  inputValue.value = getValidNumber(inputValue.value);
}

function getValidNumber(value) {
  const isNumberStr = typeof value === 'string' && value.trim() !== '' && Number(value.trim()) == value.trim();
  const isNumer = typeof value === 'number' && !Number.isNaN(value);
  if (!isNumberStr && !isNumer) return '';
  let num = isNumberStr ? Number(value.trim()) : value;
  if (props.integer && num % 1 !== 0) {
    ElMessage.info('请输入整数')
    num = parseInt(num)
  }
  if (decimalLength.value > 0) {
    const [, precision = ''] = num.toString().split('.')
    if (precision.length > decimalLength.value) {
      ElMessage.info('最多保留' + decimalLength.value + '位小数')
      num = num.toFixed(decimalLength.value) - 0
    }
  }
  if (num < props.min) {
    ElMessage.info('最小值不能小于' + props.min)
    num = props.min
  }
  if (num > props.max) {
    ElMessage.info('最大值不能大于' + props.max)
    num = props.max
  }
  return num
}

</script>
<style lang="scss" scoped></style>
