import { computed, nextTick, onMounted, onUnmounted, ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { useDebounceFn } from "@vueuse/core";
import dayjs from "dayjs";
import { getTeams } from "@/api/corp";
import { jsSDKConfig, getCurExternalContact } from "@/utils/jssdk";
import useCustomerApi from "./useCustomerApi";
import { getCustomerInSidebar, getSelectedChatMemberUrl, getUnionidByExternalUserId } from "@/api/member";
import { teamStore } from "@/store/team";
import { getCustomerAge } from "@/helpers/customer";

/**
 *
 * @param {*} teams 当前员工所属团队
 * @returns
 */
export default function useCustomer(trigger) {
  const { get } = useCustomerApi();
  const externalUserId = ref("");
  const customer = ref(null);
  const customerList = ref([]);
  const loading = ref(true);
  const unionId = ref("");
  const watcher = ref(null);
  onMounted(getCustomer);
  onUnmounted(stop);
  function updateCustomer(props = {}) {
    Object.assign(customer.value || {}, props);
  }

  async function refreshCustomer() {
    if (!customer.value || !customer.value._id) return;
    const { customer: data } = await get(customer.value._id);
    if (data) {
      customer.value = { ...data, ageStr: getCustomerAge(data) };
      const index = customerList.value.findIndex((i) => i._id === data._id);
      index >= 0 ? (customerList.value[index] = data) : null;
    }
  }

  async function refreshCustomerList(id) {
    customerList.value = await getCustomerList();
    const currentId = id || (customer.value && customer.value._id ? customer.value._id : "");
    selectCustomer(currentId);
  }

  function selectCustomer(id) {
    const data = customerList.value.find((i) => id && i._id === id);
    customer.value = data || customerList.value[0] || null;
    loading.value = false;
  }

  async function getCustomer() {
    externalUserId.value = await getContactExternalUserId();
    const [chatCustomer, customers] = await Promise.all([getSelectedChatMember(), getCustomerList()]);
    customerList.value = customers;
    selectCustomer(chatCustomer.memberId, chatCustomer.teamId);
    await nextTick();
    chatCustomer.teamId && setLatestTeamId(chatCustomer.teamId);
    loading.value = false;
    broadcast(chatCustomer.memberId, chatCustomer.teamId, chatCustomer);
  }

  // 获取当前聊天微信用户的外部联系人id
  async function getContactExternalUserId() {
    if (import.meta.env.DEV) return "wmLgjyawAAQzaoYIGD668-Mg8jMWKpgw"; // " wmLgjyawAAS1Z2UhJ2I3rkD5q6siz1bQ" "wmLgjyawAANJMbPBz8h2RKwliPWdR03w"  wmLgjyawAAf7a1m1Jjm_ESHLG6o4FpFg wmLgjyawAAQzaoYIGD668-Mg8jMWKpgw
    await jsSDKConfig();
    const externalUserId = await getCurExternalContact();
    return externalUserId || "";
  }
 
  // 获取 选中的当前微信用户 客户信息
  async function getSelectedChatMember() {
    const { data, success } = getSelectedChatMemberUrl({ externalUserId: externalUserId.value });
    if (!success) return {};
    const [customer] = res && res.result && Array.isArray(data.data) ? data.data : [];
    return customer || {};
  }

  // 根据externalUserID 获取当前微信用户的客户列表
  async function getCustomerList() {
    const { teams } = teamStore();
    const userId = localStorage.getItem("userId");
    // 获取成员所属团队
    const teamIds = teams && teams.filter((i) => i.memberList.includes(userId)).map((i) => i.teamId);
    const params = {
      corpId: localStorage.getItem("corpId"),
      teamIds,
      externalUserId: externalUserId.value,
    };
    const { data } = await getCustomerInSidebar(params);
    const list = Array.isArray(data.data)
      ? data.data.map((i) => ({
          ...i,
          ageStr: getCustomerAge(i),
        }))
      : [];
    // unionId.value = unionId.value || (list.find(i => i.unionid) || {}).unionid;
    if (externalUserId.value && !unionId.value) {
      unionId.value = await getUnionid();
    }
    return list;
  }

  // 监听选中的客户信息变化
  async function watchCustomerChange() {
    watcher.value = await cloudbase
      .database()
      .collection("selected-chat-member")
      .where({
        externalUserId: externalUserId.value,
        userId: localStorage.getItem("userId"),
        corpId: localStorage.getItem("corpId"),
      })
      .watch({
        onChange: async (snapshot) => {
          if (snapshot.type === "init") {
            const [chatCustomer, customers] = await Promise.all([getSelectedChatMember(), getCustomerList()]);
            customerList.value = customers;
            selectCustomer(chatCustomer.memberId, chatCustomer.teamId);
            await nextTick();
            chatCustomer.teamId && setLatestTeamId(chatCustomer.teamId);
            loading.value = false;
            broadcast(chatCustomer.memberId, chatCustomer.teamId, chatCustomer);
            return;
          }
          const docChanges = snapshot && Array.isArray(snapshot.docChanges) ? snapshot.docChanges : [];
          const data = docChanges.find((change) => ["add", "update", "init"].includes(change.dataType));
          if (data) {
            customerList.value = await getCustomerList();
            const doc = data.doc || {};
            selectCustomer(doc.memberId || "", doc.teamId);
            await nextTick();
            doc.teamId && setLatestTeamId(doc.teamId);
            broadcast(doc.memberId, doc.teamId, doc);
          }
          // 处理获取到的数据
          // snapshot.docChanges.forEach(async (change) => {
          //   if (["add", "update", "init"].includes(change.dataType)) {
          //     const { memberId } = change.doc;
          //     customerList.value = await getCustomerList()
          //     selectCustomer(memberId)
          //   }
          // });
        },
        onError: (error) => {
          console.log("收到error**********", error);
        },
      });
  }
  function stop() {
    watcher.value && typeof watcher.value.close === "function" ? watcher.value.close() : null;
  }

  const latestTeamId = ref("");
  const { currentTeam, teams } = storeToRefs(teamStore());
  const selectTeam = useDebounceFn((teamId) => {
    currentTeam.value = teams.value.find((i) => latestTeamId.value && i.teamId === latestTeamId.value) || teams.value.find((i) => teamId && i.teamId === teamId) || teams.value[0] || null;
    latestTeamId.value = "";
  }, 500);

  function setLatestTeamId(teamId) {
    latestTeamId.value = teamId || "";
    selectTeam();
  }

  /**
   *
   */
  const events = [];
  function sub(...rest) {
    rest.forEach((i) => {
      if (typeof i === "function" && !events.some((e) => e === i)) events.push(i);
    });
  }

  function broadcast(id, teamId, data) {
    if (data && data.payload) handleWorkBenchEvent(data.payload);
    if ((customer.value && id && customer.value._id !== id) || (currentTeam.value && teamId && currentTeam.value._id !== teamId)) {
      events.forEach((i) => i());
    }
  }

  /**
   * 处理 工作台应用跳转侧边栏的自定义事件
   * @example 【工作台 待办详情】点击“去处理”跳转到侧边栏,侧边栏底部tab切换到待跟进tab
   */
  async function handleWorkBenchEvent(payload) {
    trigger && trigger.$invoke("handle-work-bench-event", payload);
  }

  async function getUnionid() {
    const res = await getUnionidByExternalUserId(externalUserId.value);
    if (res.data && res.data.data && res.data.data.unionid) {
      return res.data.data.unionid;
    } else {
      return "";
    }
  }

  const inTeam = computed(() => verifyInTeam(customer.value));

  function verifyInTeam(customer) {
    const teamId = customer && customer.teamId ? customer.teamId : "";
    const teamIds = customer && Array.isArray(customer.teamId) ? customer.teamId : [];
    const currentTeamId = currentTeam.value && currentTeam.value.teamId ? currentTeam.value.teamId : "";

    return currentTeamId && (teamId === currentTeamId || teamIds.includes(currentTeamId));
  }

  trigger &&
    trigger.$on("update-customer-latest-service-time", ({ _id, time }) => {
      const index = customerList.value.findIndex((i) => i._id === _id);
      const item = customerList.value[index];
      if (!item || !time || !dayjs(time).isValid()) return;
      if (item.serviceTime && dayjs(item.serviceTime).isValid() && dayjs(item.serviceTime).isAfter(dayjs(time))) return;
      customerList.value[index].serviceTime = time;
      if (customer.value._id === _id) customer.value.serviceTime = time;
    });

  return {
    customer,
    customerList,
    customerTeam: currentTeam,
    externalUserId,
    loading,
    inTeam,
    refresh: refreshCustomer,
    refreshCustomerList,
    selectCustomer,
    selectTeam,
    sub,
    unionId,
    updateCustomer,
    verifyInTeam,
    getCustomer,
  };
}
