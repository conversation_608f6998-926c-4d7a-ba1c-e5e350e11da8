<template>
  <el-popover :visible="visible" placement="bottom" :width="width">
    <template #reference>
      <div ref="selectRef"
        class="flex items-center border border-gray-400 border-opacity-70 cursor-pointer rounded py-5px min-h-30px w-full px-11px">
        <div class="w-0 flex-grow flex flex-wrap -mb-5px" @click="show()">
          <div class="flex items-center rounded border border-gray-200 px-10px py-6px mr-5px mb-5px group bg-gray-50"
            v-for="(item, index) in projects" :key="index" @click.stop="">
            <div class="text-14px"> 112232</div>
            <el-icon class="ml-5px text-gray-500 hover:text-red-500" :size="14" @click.stop="remove(item)">
              <CloseBold />
            </el-icon>
          </div>
        </div>
        <span class="text-12px flex-shrink-0 text-gray-500">
          <el-icon>
            <ArrowDown />
          </el-icon>
        </span>
      </div>
    </template>
    <div ref="contentRef">
      <el-input v-model.trim="name" clearable :prefix-icon="Search" class="mb-10px" placeholder="输入搜索内容"></el-input>
      <el-scrollbar height="40vh">
        <div class="h-80vh bg-red-400"></div>
      </el-scrollbar>
    </div>

  </el-popover>

</template>
<script setup>
import { ref } from 'vue';
import { onClickOutside, useElementBounding, useVModel } from "@vueuse/core";

import { CaretBottom, CaretTop, Search } from '@element-plus/icons-vue';

const emit = defineEmits(["update:modelValue"]);
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [1, 2, 3],
  },
});

const name = ref('');
const selectRef = ref()
const contentRef = ref(null);
const projects = useVModel(props, "modelValue", emit);
const visible = ref(false);
const { width } = useElementBounding(selectRef);

onClickOutside(contentRef, () => {
  if (visible.value) visible.value = false;
});


function show() {
  visible.value = !visible.value;
}
</script>
<style lang="scss" scoped></style>
