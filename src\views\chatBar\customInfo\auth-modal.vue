<template>
  <el-dialog :model-value="props.visible" :width="width" title="确认授权团队" @close="close">
    <div p-15 text-center font-semibold color-normal>您当前入驻{{ props.teams.length }}个团队，客户建档需要确认授权团队，请选择。 </div>
    <el-scrollbar wrap-style="max-height: calc(85vh - 200px)">
      <div v-for="team in props.teams" :key="team.teamId" class="auth-team" flex items-center justify-between rounded-8
        px-15 py-10 @click="selectTeam(team)">
        <div flex-grow font-14 color-normal class="w-0 truncate mr-10px">{{ team.name }}</div>
        <el-icon>
          <ArrowRightBold />
        </el-icon>
      </div>
    </el-scrollbar>
  </el-dialog>
  <el-dialog v-model="qrCodeVisible" :width="width" :title="currentTeam.name" @close="qrCodeVisible = false">
    <div p-15 text-center font-semibold color-normal>{{ currentTeam.name }}有{{ currentTeam.qrcodes ?
      currentTeam.qrcodes.length : 0 }}个二维码，请选择要发送的建档二维码</div>
    <el-scrollbar wrap-style="max-height: calc(100vh - 300px)">
      <div v-for="code in currentTeam.qrcodes" :key="code.id" class="auth-team" flex items-center justify-between
        rounded-8 px-15 py-10 @click="sendLink(code)">
        <div flex-grow font-14 color-normal class="w-0 truncate mr-10px">{{ code.name }}</div>
        <el-icon>
          <ArrowRightBold />
        </el-icon>
      </div>
    </el-scrollbar>
  </el-dialog>
</template>
<script setup>
import { ElMessage } from 'element-plus';
import { ref, watch } from 'vue';

const props = defineProps({
  visible: { type: Boolean, default: false },
  teams: { type: Array, default: () => [] }
})

const emits = defineEmits(['close', 'send']);
function close() {
  emits('close')
}

const qrCodeVisible = ref(false);
const currentTeam = ref({})
function selectTeam(team) {
  currentTeam.value = team;
  const qrcodes = Array.isArray(team.qrcodes) ? team.qrcodes : [];
  if (qrcodes.length === 0) {
    ElMessage.info(`${team.name}还未配置二维码, 暂不支持建档`)
  } else if (qrcodes.length === 1) {
    sendLink(qrcodes[0])
  } else {
    qrCodeVisible.value = true
  }

}
function sendLink(item) {
  qrCodeVisible.value = false;
  close();
  emits('send', { qrid: item.id, teamId: currentTeam.value.teamId ,name:currentTeam.value.name})
}

const width = ref(0);
function setWidth() {
  width.value = Math.min(520, Math.floor(window.innerWidth - 40));
}

watch(() => props.visible, n => {
  if (n) setWidth()
})
</script>
<style scoped>
.auth-team {
  border: 1px solid #eee;
}

.auth-team+.auth-team {
  margin-top: 15px;
}
</style>