<template>
  <el-drawer :model-value="planVisible" :title="title" size="70%" @close="close">
    <el-scrollbar>
      <el-form :rules="rules" :validate-on-rule-change="false" :label-width="180" :model="taskForm" label-suffix="："
        ref="planFormRef" class="p-15px">
        <el-form-item label="执行时间" prop="taskTime">
          <div flex items-center>
            <div class="mr-10px" color-666>计划开始后:</div>
            <el-input-number class="mr-10px" :min="minTime" v-model="taskForm.taskTime" style="width: 100px" />
            <div class="mr-10px" color-666>
              <el-select v-model="taskForm.timeType" class="w-60px" @change="selectTimeType">
                <el-option v-for="opt in timeTypeList" :label="opt.label" :value="opt.value" />
              </el-select>
            </div>
            <div class="main-color" main-color>{{ startTime }}</div>
            <el-tooltip effect="light" content="设置客户加入任务后多少天提醒执行该任务，具体回访日期以客户加入该任务的开始日期计算。" placement="bottom">
              <el-icon class="text-20px text-gray-500 ml-10px">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </div>
        </el-form-item>
        <el-form-item label="类型" class="is-required mt-20px">
          <el-select v-model="taskForm.eventType" filterable style="width: 100%" placeholder="请选择类型">
            <el-option v-for="item in eventTypeList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="目的" prop="taskContent" class="mt-20px">
          <el-input v-model="taskForm.taskContent" maxlength="500" resize="none" :autosize="{ minRows: 6, maxRows: 12 }"
            show-word-limit type="textarea" placeholder="请输入文字提醒" />
        </el-form-item>
        <el-form-item label="任务跟进方式" prop="executeMethod" class="mt-20px">
          <el-radio-group v-model="taskForm.executeMethod" @change="changeExecuteMethod">
            <el-radio label="todo">生成待办单（员工手动处理）</el-radio>
            <el-radio label="groupTask">生成群发单（员工批量发送）</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="taskForm.executeMethod === 'todo'" label="是否向联系人发送内容" prop="enableSend" class="mt-20px">
          <el-switch v-model="taskForm.enableSend" inline-prompt active-text="是" inactive-text="否" />
          <el-tooltip effect="light" content="可提前预设想给联系人发送的标准话术及附件" placement="top">
            <el-icon class="text-20px text-gray-500 ml-10px">
              <QuestionFilled />
            </el-icon>
          </el-tooltip>
        </el-form-item>
        <el-form-item v-if="taskForm.enableSend" :class="taskForm.executeMethod === 'groupTask' ?'is-required':''"
          label="发送内容" prop="sendContent" class="mt-20px">
          <send-content-set v-model="taskForm" class="w-full" />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button class="w-100px" @click="close()">取消</el-button>
        <el-button class="w-100px" type="primary" @click="save(planFormRef)">保存</el-button>
      </div>
    </template>
  </el-drawer>
  <add-file ref="addFileRefs" @onGetSelectFile="getSelectFile"></add-file>
  <survery-dialog :data="currentSurvery" :visible="visible" @close="visible = false" />

</template>
<script setup>
import { ref, watch, computed, nextTick } from "vue";
import dayjs from "dayjs";
import { getRandomStr } from "@/utils";
import { ToDoEventType } from "@/baseData";
import addFile from "@/views/pannedEvent/components/addFile.vue";
import SurveryDialog from "@/views/system/survery/dialog.vue";
import sendContentSet from "@/components/send-content-set/send-content-set.vue";

const eventTypeList = Object.keys(ToDoEventType).map((key) => ({ label: ToDoEventType[key], value: key }));
const timeTypeList = [
  { label: "天", value: "day" },
  { label: "周", value: "week" },
  { label: "月", value: "month" },
  { label: "年", value: "year" },
];
const addFileRefs = ref("");
const planVisible = ref(false);
const minTime = ref(0);
const taskForm = ref({
  taskTime: 0,
  timeType: "day",
  taskContent: "",
  sendContent: "",
  enableSend: false,
  pannedEventSendFile: {},
});
const planFormRef = ref("");
const type = ref("add");
const $emit = defineEmits(["success"]);
const title = computed(() => {
  return type.value === "add" ? "新增任务" : "编辑任务";
});

const rules = computed(() => {
  const config = {
    eventType: [{ required: true, message: "请选择任务类型", trigger: "blur" }],
    // taskContent: [{ required: true, message: "请输入目的", trigger: "blur" }],
    taskTime: [{ required: true, message: "请输入计划执行时间", trigger: "blur" }],
  }
  if (taskForm.value.executeMethod === 'groupTask') {
    config.sendContent = [{ required: true, message: "请输入发送内容", trigger: "blur" }]
  }
  return config
});

function selectTimeType(val) {
  if (val === "day") {
    minTime.value = 0;
  } else if (val === "week" || val === "month" || val === "year") {
    minTime.value = 1;
  }
  taskForm.value.timeType = val;
  if (taskForm.value.taskTime < minTime.value) {
    taskForm.value.taskTime = minTime.value;
  }
}
const planExecutionTime = ref("");
function openDialog(item) {
  planExecutionTime.value = item.planExecutionTime;
  if (item.type === "add") {
    taskForm.value = {
      taskId: getRandomStr(),
      taskTime: 0,
      taskContent: "",
      sendContent: "",
      timeType: "day", // 默认天
      pannedEventSendFile: {},
      executeMethod: 'todo'
    };
  } else {
    taskForm.value = JSON.parse(JSON.stringify(item.params));
  }
  type.value = item.type;
  planVisible.value = true;
}

const startTime = computed(() => {
  if (!planExecutionTime.value) return;
  const { taskTime, timeType } = taskForm.value;
  if (!timeType) {
    return dayjs(planExecutionTime.value).add(taskTime, "day").format("YYYY-MM-DD");
  } else {
    return dayjs(planExecutionTime.value).add(taskTime, timeType).format("YYYY-MM-DD");
  }
});

function getSelectFile(file) {
  taskForm.value.pannedEventSendFile = file;
}

function close() {
  planVisible.value = false;
}

function save(formEl) {
  if (!formEl) return;
  formEl.validate((valid, fields) => {
    if (valid) {
      planVisible.value = false;
      $emit("success", JSON.parse(JSON.stringify(taskForm.value)));
    }
  });
}

const currentSurvery = ref({});
const visible = ref(false);

function changeExecuteMethod(val) {
  if (val === 'groupTask' && !taskForm.value.enableSend) {
    taskForm.value.enableSend = true
  }
}

watch(planVisible, async n => {
  if (n) {
    await nextTick()
    planFormRef.value && planFormRef.value.clearValidate(Object.keys(rules.value))
  }
})

defineExpose({
  openDialog,
});

</script>
<style scoped>
:deep(.el-textarea__inner) {
  padding-bottom: 20px;
}
</style>