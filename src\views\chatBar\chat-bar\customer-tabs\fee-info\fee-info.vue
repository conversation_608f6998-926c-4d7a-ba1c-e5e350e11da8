<template>
  <div v-loading="loading" class="text-14px">
    <div ref="headRef" class="pb-15px">
      <div class="bg-white shadow-xl">
        <div class="flex items-center justify-between px-15px py-12px">
          <el-input v-model="projectName" :prefix-icon="Search" placeholder="项目名称" clearable class="w-3/4" />
          <div class="ml-10px flex-shrink-0 text-center cursor-pointer hover:text-blue-500" @click="show()">
            <el-icon>
              <Filter />
            </el-icon>
            <div class="text-blue-500 text-12px mt-4px">筛选</div>
          </div>
          <!-- <div class="flex-shrink-0 ml-10px text-center cursor-pointer hover:text-blue-500">
            <el-icon>
              <Refresh />
            </el-icon>
            <div class="text-blue-500 text-12px mt-4px">同步</div>
          </div> -->
        </div>
        <div class="mt-6px px-15px py-12px border-t border-gray-200">已结算：￥{{ freeStatistic.paidTotalPayment }} 未结算： ￥{{ freeStatistic.noPayTotalPayment }} 退费：￥{{ freeStatistic.refundTotalPayment }}</div>
      </div>
    </div>
    <el-lock-scrollbar :lock="lock" :max-height="maxScrollHeight" @scroll="handleScroll">
      <div v-if="records.length === 0" class="pt-30px">
        <empty-data class="mt-20px" :top="0" text="暂无费用信息"></empty-data>
      </div>
      <div v-for="(record, idx) in records" :key="record._id" class="bg-white shadow-md rounded-8px mx-15px mb-15px">
        <div class="flex items-center justify-between py-10px px-15px" border-bottom>
          <div class="flex-grow truncate w-0">{{ record.projectName }}</div>
          <div class="flex-shrink-0 font-semibold pl-10px leading-14px">
            {{ record.totalFee || record.totalFee === 0 ? `￥${record.totalFee}` : "" }}
          </div>
        </div>
        <div class="px-15px pb-12px leading-21px">
          <div class="flex pt-12px">
            <div class="flex-shrink-0 w-80px text-gray-500">开单科室：</div>
            <div class="break-all">{{ record.billDept }}</div>
          </div>
          <div class="flex pt-12px">
            <div class="flex-shrink-0 w-80px text-gray-500">开单医生：</div>
            <div class="break-all">{{ record.billDoctor }}</div>
          </div>
          <div class="flex pt-12px">
            <div class="flex-shrink-0 w-80px text-gray-500">数量：</div>
            <div class="break-all">{{ record.projectCount }}</div>
          </div>
          <div class="flex pt-12px">
            <div class="flex-shrink-0 w-80px text-gray-500">类型：</div>
            <div class="break-all">{{ record.doctorAdvice }}</div>
          </div>
          <div class="flex pt-12px">
            <div class="flex-shrink-0 w-80px text-gray-500">支付状态：</div>
            <div class="break-all">{{ record.orderStatus }}</div>
          </div>
          <div class="flex pt-12px">
            <div class="flex-shrink-0 w-80px text-gray-500">开单时间：</div>
            <div class="break-all">{{ record.date }}</div>
          </div>
        </div>
      </div>
      <div v-if="more" class="pb-15px rounded-4px cursor-pointer flex items-center justify-center text-gray-400 hover:text-blue-500" @click="loadmore()">
        <div class="text-14px">加载更多</div>
        <el-icon>
          <ArrowDownBold />
        </el-icon>
      </div>
    </el-lock-scrollbar>
  </div>
  <filter-modal :status="orderStatus" :start="billTime[0]" :end="billTime[1]" :width="width" :visible="visible" @close="close()" @change="change" />
</template>
<script setup>
import { computed, inject, ref, watch } from "vue";
import { watchDebounced } from "@vueuse/core";

import { storeToRefs } from "pinia";
import { useElementBounding } from "@vueuse/core";
import dayjs from "dayjs";
import { getFeeRecord, merageHisFeeRecord, feeRecordStatistics } from "@/api/member";
import useModal from "@/hooks/useModal";
import { memberStore } from "@/store/member";
import useLockScrollbar from "../useLockScrollbar.js";
import elLockScrollbar from "@/components/el-lock-scrollbar/el-lock-scrollbar";
import EmptyData from "@/components/empty-data.vue";
import { Search } from "@element-plus/icons-vue";
import filterModal from "./filter-modal.vue";

const trigger = inject("side-bar-event-trigger");

const props = defineProps({
  maxHeight: { type: Number },
  customer: { type: Object, default: () => ({}) },
});
const { memberInfo, corpInfo } = storeToRefs(memberStore());
const headRef = ref(null);
const { height } = useElementBounding(headRef);
const maxScrollHeight = computed(() => props.maxHeight - height.value);

const projectName = ref("");
const orderStatus = ref("");
const billTime = ref([]);
const loading = ref(false);
const records = ref([]);
const more = ref(false);
const page = ref(1);

function change(data) {
  orderStatus.value = data.status;
  billTime.value = [data.start, data.end].filter(Boolean);
  page.value = 1;
  getList();
}

async function getList() {
  if (loading.value) return;
  if (memberInfo.value.corpId && props.customer._id && corpInfo.value.isConnectHis && props.customer.customerNumber) {
    loading.value = true;
    const params = {
      memberId: props.customer._id,
      isConnectHis: corpInfo.value.isConnectHis,
      customerNumber: props.customer.customerNumber,
      page: page.value,
      pageSize: 10,
      projectName: projectName.value,
      orderStatus: orderStatus.value,
      billTime: billTime.value && billTime.value.length ? [dayjs(billTime.value[0]).format("YYYY-MM-DD"), dayjs(billTime.value[1]).format("YYYY-MM-DD")] : [],
    };
    const { success, data } = await getFeeRecord(params);
    if (success) {
      const { list: tableData = [], total: count } = data;
      const arr = Array.isArray(tableData)
        ? tableData.map((i) => ({
            ...i,
            date: i.billTime && dayjs(i.billTime).isValid() ? dayjs(i.billTime).format("YYYY-MM-DD") : "",
          }))
        : [];
      records.value = page.value === 1 ? arr : [...records.value, ...arr];
      more.value = records.value.length < count;
    }
    loading.value = false;
  }
}
function loadmore() {
  if (more.value && !loading.value) {
    page.value = page.value + 1;
    getList();
  }
}

const freeStatistic = ref({ refundTotalPayment: 0, paidTotalPayment: 0, noPayTotalPayment: 0 });
async function getStatistics() {
  if (memberInfo.value.corpId && props.customer._id && corpInfo.value.isConnectHis && props.customer.customerNumber) {
    const params = {
      memberId: props.customer._id,
    };
    const { data } = await feeRecordStatistics(params);
    freeStatistic.value = {
      refundTotalPayment: data.refundTotalPayment || 0,
      paidTotalPayment: data.paidTotalPayment || 0,
      noPayTotalPayment: data.noPayTotalPayment || 0,
    };
  }
}

async function merageFeeRecord() {
  const params = {
    memberId: props.customer._id,
    customerNumber: props.customer.customerNumber,
  };
  await merageHisFeeRecord(params);
}

watch(
  () => props.customer._id,
  async () => {
    page.value = 1;
    if (memberInfo.value.corpId && props.customer._id && corpInfo.value.isConnectHis && props.customer.customerNumber) {
      await merageFeeRecord();
      getList();
      getStatistics();
    }
  },
  { immediate: true }
);

watchDebounced(
  projectName,
  (n) => {
    page.value = 1;
    getList();
  },
  { debounce: 500 }
);

const { close, show, visible, width } = useModal();

const { lock, handleScroll } = useLockScrollbar();
</script>
<style lang="scss" scoped>
.visit-node::before {
  content: "";
  position: absolute;
  top: 20px;
  left: 15px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transform: translateX(-50%);
  background-color: var(--yc-primary-color);
  z-index: 2;
}

.visit-node::after {
  content: "";
  position: absolute;
  top: 20px;
  left: 15px;
  width: 1px;
  height: 100%;
  border-radius: 50%;
  transform: translateX(-50%);
  background-color: var(--yc-primary-color);
  z-index: 2;
}

.visit-node--last::after {
  display: none;
}
</style>
