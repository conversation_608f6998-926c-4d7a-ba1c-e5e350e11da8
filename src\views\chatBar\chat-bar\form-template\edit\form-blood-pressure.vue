<template>
  <div>
    <form-date :form="form" name="测量日期" :required="required" title="bloodPressureDate" @change="change"></form-date>
    <form-input :form="form" name="收缩压" :required="required" title="systolicPressure" appendText="mmHg" :wordLimit="3"
      @change="change" />
    <form-input :form="form" name="舒张压" :required="required" title="diastolicPressure" appendText="mmHg" :wordLimit="3"
      @change="change" />
  </div>
</template>
<script setup>
import { onMounted, inject } from 'vue';
import dayjs from 'dayjs';
import { checkInteger } from "./verify";

import formDate from './form-date.vue';
import formInput from './form-input.vue';
const props = defineProps({
  form: { type: Object, default: () => ({}) },
  name: { type: String, default: '' },
  required: { type: Boolean, default: false },
  title: { type: String, default: '' },
})

const addRule = inject('addRule')
onMounted(() => {
  addRule(props.title, check)
})

const emits = defineEmits(['change']);

function change(data) {
  emits('change', data);
}

function check() {
  const data = props.form && props.form ? props.form : {};
  if (props.required && !data['bloodPressureDate']) return `请选择测量日期`;
  if (props.required && !data['systolicPressure']) return `请输入收缩压`;
  if (props.required && !data['diastolicPressure']) return `请输入舒张压`;
  if (data['bloodPressureDate'] && (!dayjs(data['bloodPressureDate']).isValid() || dayjs(data['bloodPressureDate'])).isAfter(dayjs())) return `请选择正确的测量日期`;
  if (data['systolicPressure']) {
    const res = checkInteger(data['systolicPressure'], 40, 250)
    if (!res) return `收缩压 请输入40-250之间的数字`;
  }
  if (data['diastolicPressure']) {
    const res = checkInteger(data['diastolicPressure'], 30, 200)
    if (!res) return `舒张压 请输入30-200之间的数字`;
  };
  return true
}
</script>
<style lang="scss" scoped></style>
