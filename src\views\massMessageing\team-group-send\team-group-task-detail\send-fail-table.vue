<template>
  <el-dialog :model-value="visible" :width="width" title="发送失败客户" @close="close">
    <el-table stripe height="40vh" :data="list">
      <el-table-column prop="name" label="客户" :width="120">
        <template #default="scope">
          <div class="flex items-center w-full">
            {{ scope.row.name }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="失败原因">
        <template #default="{ row }">
          {{ row.status === 2 || row.status === 3 ? failReason[row.status] : "未知原因" }}
        </template>
      </el-table-column>
    </el-table>
    <div class="pt-15px px-15px flex justify-end border-t border-gray-200">
      <el-pagination layout="prev, pager, next, sizes" :current-page="page" :page-size="pageSize" :page-sizes="[10, 20, 50]" :pager-count="3" :total="total" @current-change="changePage" @size-change="changeSize"></el-pagination>
    </div>
    <template #footer>
      <div class="text-center">
        <el-button class="w-100px" @click="close()">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref } from "vue";
const props = defineProps({
  visible: { type: Boolean, default: false },
  width: { type: [String, Number], default: 200 },
  failCustomers: { type: Array, default: () => [] },
});

const emits = defineEmits(["close"]);

function close() {
  emits("close");
}
const failReason = {
  2: "因客户不是好友导致发送失败",
  3: "因客户已经收到其他群发消息导致发送失败",
};
const page = ref(1);
const pageSize = ref(10);
const total = computed(() => props.failCustomers.length);
function changePage(val) {
  page.value = val;
}
function changeSize(val) {
  pageSize.value = val;
}
const list = computed(() => {
  // 分页
  return props.failCustomers.slice((page.value - 1) * pageSize.value, page.value * pageSize.value);
});
</script>
<style lang="scss" scoped></style>
