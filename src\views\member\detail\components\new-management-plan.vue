<template>
  <my-layout bg-fff>
    <layout-item>
      <div class="flex items-center justify-between flex-wrap py-10px px-15px">
        <div class="flex">
          <check-box-filter v-model="searchForm.visitStatus" label="回访状态" :list="statusList"></check-box-filter>
          <date-range-filter v-model:dates="searchForm.dates" label="回访日期" :text="getDatesText(searchForm.dates)" />
          <check-box-filter v-model="searchForm.eventType" label="回访类型" :list="eventTypeList"></check-box-filter>
          <check-box-filter v-model="searchForm.teamIds" label="处理团队" :list="teamList"></check-box-filter>
          <check-box-filter v-model="searchForm.userIds" label="处理人" :list="peopleList">
            <template #item="{ item }">
              <ww-user :openid="item.value"></ww-user>
            </template>
          </check-box-filter>
        </div>
        <div class="flex-shrin-0 ml-10px">
          <el-button type="primary" @click="search()">查询</el-button>
          <el-button plain type="primary" @click="reset()">重置</el-button>
        </div>
      </div>
    </layout-item>
    <layout-main>
      <el-table stripe border v-loading="loading" :data="tableData" height="100%">
        <el-table-column label="回访状态" prop="status" :width="100">
          <template #default="{row:{status}}">
            <span :class="statusClassNames[status]"> {{ statusNames[status] }}</span>
          </template>
        </el-table-column>
        <el-table-column label="回访日期" prop="plannedExecutionTime" :width="120">
          <template #default="{row:{plannedExecutionTime}}">
            {{ plannedExecutionTime ? dayjs(plannedExecutionTime).format('YYYY-MM-DD') : ''}}
          </template>
        </el-table-column>
        <el-table-column label="类型" prop="type" :min-width="120">
          <template #default="{row:{eventType}}">
            {{ ServiceType[eventType]}}
          </template>
        </el-table-column>
        <el-table-column label="回访跟进方式" prop="method" :min-width="140">
          <template #default="{row:{executeMethod}}">
            {{executeMethodMap[executeMethod]}}
          </template>
        </el-table-column>
        <el-table-column label="目的" prop="taskContent" :min-width="240">
          <template #default="{row:{taskContent}}">
            <el-popover placement="top" width="400" trigger="click" v-if="taskContent">
              <template #reference>
                <div class="cursor-pointer whitespace-pre-wrap leading-5 max-h-10 overflow-hidden line-clamp-2 hover:text-blue-500 transition-colors" @click.stop>
                  {{ taskContent }}
                </div>
              </template>
              <div class="whitespace-pre-wrap text-14px leading-6 max-h-300px overflow-y-auto">
                {{ taskContent }}
              </div>
            </el-popover>
            <div v-else class="text-gray-400">暂无目的</div>
          </template>
        </el-table-column>
        <el-table-column label="结果" prop="result" :min-width="240">
          <template #default="{row:{result}}">
            <el-popover placement="top" width="400" trigger="click" v-if="result">
              <template #reference>
                <div class="cursor-pointer whitespace-pre-wrap leading-5 max-h-10 overflow-hidden line-clamp-2 hover:text-blue-500 transition-colors" @click.stop>
                  {{ result }}
                </div>
              </template>
              <div class="whitespace-pre-wrap text-14px leading-6 max-h-300px overflow-y-auto">
                {{ result }}
              </div>
            </el-popover>
            <div v-else class="text-gray-400">暂无结果</div>
          </template>
        </el-table-column>
        <el-table-column label="处理时间" prop="endTime" :width="160">
          <template #default="{row:{endTime}}">
            {{ endTime ? dayjs(endTime).format('YYYY-MM-DD HH:mm') : ''}}
          </template>
        </el-table-column>
        <el-table-column label="处理团队" prop="status" :min-width="200">
          <template #default="{row:{executeTeamId,executeTeamName}}">
            <el-tooltip :disabled="!executeTeamId && !executeTeamName" placement="top" effect="light"
              popper-class="max-w-480px">
              <div class="truncate"> <team-data :teamId="executeTeamId" /></div>
              <template #content>
                <team-data :teamId="executeTeamId" />
              </template>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="处理人" prop="executorUserId" :min-width="160">
          <template #default="{row:{executorUserId}}">
            <ww-user v-if="executorUserId" :openid="executorUserId" />
          </template>
        </el-table-column>
      </el-table>
    </layout-main>
    <layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize"
        @handle-current-change="changePage" />
    </layout-item>
  </my-layout>
</template>
<script setup>
import { ref, watch, computed, onMounted } from "vue";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import { getCustomerTodos } from "@/api/todo";
import { ToDoEventType, ServiceType } from "@/baseData";
import useElPagination from "@/hooks/useElPagination";
import { staffStore } from "@/store/staff";
import { teamStore } from "@/store/team";
import { statusClassNames, statusNames, getVisitPlanStatus, executeMethodMap } from '@/baseData/visit-plan'

import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import WwUser from "@/components/ww-user/index.vue";
import { CheckBoxFilter, DateRangeFilter } from "@/components/filter-bar";
import pagination from "@/components/pagination/pagination.vue";
import TeamData from '@/components/team-data/team-data.vue'

const { allTeams } = storeToRefs(teamStore());
const { staffList } = storeToRefs(staffStore());
const { getStaffList } = staffStore();

const props = defineProps(["customer", "customerType"]);
const emits = defineEmits(["reload"]);
const { list, page, pageSize, changePage, changeSize } = useElPagination(getList);
const searchForm = ref({});
const params = ref({})
const loading = ref(false)
const total = ref(0)
const eventTypeList = Object.keys(ToDoEventType).map((key) => ({ label: ToDoEventType[key], value: key }));
const teamList = computed(() => allTeams.value.map((i) => ({ label: i.name, value: i.teamId })));
const peopleList = computed(() => staffList.value.map((i) => ({ value: i.userid })));
const statusList = Object.keys(statusNames).map(i => ({ label: statusNames[i], value: i }));
const tableData = computed(() => {
  return list.value.map(item => ({ ...item, status: getVisitPlanStatus(item) }))
})
function getDatesText(dates) {
  return dates && Array.isArray(dates) ? dates.join(" ~ ") : "";
}

function search() {
  const { visitStatus = [], dates = [], ...rest } = searchForm.value;
  params.value = { ...rest };
  if (visitStatus.length > 0 && visitStatus.length < statusList.length) {
    params.value.statusList = [...visitStatus]
  }
  if (dates.length) {
    const [startDate, endDate] = dates;
    params.value.startDate = startDate;
    params.value.endDate = endDate;
  }
  changePage(1)
}
function reset() {
  searchForm.value = {};
  search()
}

async function getList() {
  loading.value = true;
  const { data, success, message } = await getCustomerTodos({
    customerId: props.customer._id,
    page: page.value,
    pageSize: pageSize.value,
    ...params.value
  })
  list.value = data && Array.isArray(data.data) ? data.data : [];
  total.value = data.total || 0;
  if (!success) {
    ElMessage.error(message || '获取失败')
  }
  loading.value = false;
}

onMounted(() => {
  if (staffList.value.length === 0) getStaffList()
  changePage(1)
})

watch(
  () => props.customer,
  (n) => {
    changePage(1)
  },
  { immediate: true }
);

</script>
