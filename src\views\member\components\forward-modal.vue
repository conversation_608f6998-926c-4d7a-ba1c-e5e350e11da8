<template>
  <el-dialog :model-value="props.visible" :close-on-click-modal="false" :width="520" title="转发同事" @close="close">
    <div p-15>
      <el-select class="forward-popover-select" multiple placeholder="请选择接收同事" style="display: block;"
        popper-class="forward-popover">
        <template #prefix>
          <div style="width: 420px;white-space: wrap;text-align: left;">
            <el-tag mr-5 v-for="item in selectionValue" :key="item.value" closable :type="primary"
              @close="change(false, item)">
              <!-- {{ item.userId }} -->
              <ww-user :openid="item.userId" type="userName"></ww-user>
            </el-tag>
          </div>
        </template>
        <el-option v-if="teams.length">
          <el-cascader-panel multiple :options="teams">
            <template #default="{ data }">
              <div flex items-center v-if="data.isTeam">
                <el-checkbox :model-value="selection[data.value] ? true : false" @change="change($event, data)" />
                <span ml-8>{{ data.label }}</span>
              </div>
              <template v-else>
                <el-checkbox :model-value="selection[data.value] ? true : false" @change="change($event, data)">
                  <ww-user :openid="data.userId"></ww-user>
                  <!-- <span>{{ data.userId }}</span> -->
                </el-checkbox>
              </template>
            </template>
          </el-cascader-panel>
        </el-option>
      </el-select>
      <el-input type="textarea" :maxlength="200" show-word-limit v-model="content" placeholder="留言内容将生成待办任务单派送给接收同事"
        :autosize="{ minRows: 4, maxRows: 8 }"></el-input>
    </div>
    <template #footer>
      <div text-center>
        <el-button @click="close()">取消</el-button>
        <el-button :loading="loading" type="primary" @click="save()"> 保存 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, h, ref, watch } from 'vue';
import { ElMessage } from 'element-plus'
import { getTeamList } from "@/api/corp.js";
import { batchAddTodo } from '@/api/todo';
import WwUser from '@/components/ww-user/index.vue';

const props = defineProps({
  visible: { type: Boolean, defautl: false },
  customers: { type: Array, defautl: () => [] },
})
const emits = defineEmits(['close', 'success']);

function close() {
  emits('close')
}

const teams = ref([]);
async function getTeamData() {
  let res = await getTeamList();
  const { data } = res;
  const list = data && Array.isArray(data.data) ? data.data.map(team => {
    const children = Array.isArray(team.memberList) ? team.memberList.map(i => ({ userId: i, value: `${team.teamId}_${i}`, teamId: team.teamId })) : [];
    return { value: team.teamId, label: team.name, isTeam: true, children }
  }) : [];
  teams.value = list;
}

const selection = ref({});
const selectionValue = computed(() => {
  return Object.values(selection.value).filter(Boolean).map(i => ({ ...i })).filter(i => !i.isTeam)
})
function change(val, data) {
  selection.value[data.value] = val ? { ...data } : null;
  if (data.isTeam && val) { // 团队勾选 成员全选
    data.children.forEach(i => selection.value[i.value] = i)
  } else if (data.isTeam) { // 团队取消勾选 成员取消勾选
    data.children.forEach(i => selection.value[i.value] = null)
  } else {
    const team = teams.value.find(i => i.value == data.teamId);
    const allMemberChecked = team.children.every(i => Boolean(selection.value[i.value]));
    selection.value[data.teamId] = allMemberChecked ? team : null;
  }
}

const content = ref('')
const loading = ref(false)
async function save() {
  if (selectionValue.value.length && content.value.trim()) {
    loading.value = true;
    const list = Object.values(selection.value).filter(Boolean).map(i => ({ ...i }));
    const teamIds = list.filter(i => i.isTeam).map(i => i.value);
    const members = list.filter(i => !i.isTeam && !teamIds.some(id => i.teamId === id));
    const executors = [...(teamIds.map(teamId => ({ teamId }))), ...(members.map(i => ({ teamId: i.teamId, userId: i.userId })))];
    const todos = createTodos(executors);
  
    const group = groupTodos(todos);
    const tasks = group.map(item => batchAddTodo(item));
    const res = await Promise.all(tasks);
    const isSuccess = res.every(i => i.success);
    if (isSuccess) {
      ElMessage.success('操作成功');
      emits('success')
    } else {
      const failItems = res.reduce((val, item) => [...val, ...(item.data && Array.isArray(item.data.fail) ? item.data.fail : [])], []);
      if (failItems.length) {
        showFail(failItems);
      } else {
        ElMessage.error('操作失败');
      }
    }
    loading.value = false;
  } else if (!selectionValue.value.length) {
    ElMessage.info('请选择接收同事')
  } else {
    ElMessage.info('请输入留言内容')
  }
}

function groupTodos(list, groupLength = 5) {
  const group = [];
  while (list.length > 0) {
    const item = list.splice(0, groupLength);
    group.push(item)
  }
  return group
}

function createTodos(executors) {
  return executors.reduce((val, { teamId, userId = '' }) => {
    const todos = props.customers.map(member => ({
      corpId: localStorage.getItem('corpId'),
      creatorUserId: localStorage.getItem('userId'),
      customerId: member._id,
      customerName: member.name,
      customerUserId: member.externalUserId || '',
      eventType: 'mateForward',
      executeTeamId: teamId,
      executorUserId: userId,
      taskContent: content.value.trim()
    }))
    return [...val, ...todos]
  }, [])
}

function showFail(list) {
  const res = groupFailByName(list);
  const message = h('div', null, res.map(({ customName, list }) => {
    return h('span', null, [
      h('span', null, `转发${customName}给`),
      ...list.map(({ executorUserId, teamId }, idx) => {
        const isTeam = !Boolean(executorUserId);
        const team = teams.value.find(i => i.value == teamId);
        return isTeam ? h('span', null, `${team.label}${idx === list.length - 1 ? '' : '、'}`)
          : h(WwUser, { openid: executorUserId }, idx === list.length - 1 ? '' : '、')
      }),
      h('span', null, `失败`),
    ])
  }))
  ElMessage({
    type: 'error',
    // duration: 0,
    message
  })
}
function groupFailByName(list) {
  const res = [];
  while (list.length) {
    const customName = list[0].customName;
    res.push({
      customName,
      list: list.filter(i => i.customName === customName)
    })
    list = list.filter(i => i.customName !== customName)
  }
  return res;
}

watch(props, (n, o) => {
  if (n.visible) {
    getTeamData();
    content.value = '';
    selection.value = {}
  }
})
</script>
<style scoped>
:deep(.el-icon.el-cascader-node__prefix) {
  opacity: 0;
}

[ml-8] {
  margin-left: 8px;
}
</style>
<style lang="scss">
.forward-popover-select {
  margin-bottom: 15px;

  .el-input__prefix {
    width: 0;
    flex-grow: 1;
  }

  .el-input__inner {
    flex-grow: 0;
    width: 0;
  }
}

.forward-popover {
  .el-select-dropdown__item {
    height: auto;
    min-height: 34px;
  }

  .el-select-dropdown__item:hover {
    background-color: #fff;
  }

  .el-input__prefix {
    width: 0;
    flex-grow: 1;
  }

  .el-input__inner {
    flex-grow: 0;
  }
}
</style>