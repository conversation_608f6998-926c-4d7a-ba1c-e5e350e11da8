<template>
  <div class="truncate ml-5px" >
    <customer-tag custom v-for="(tagId, idx) in tagIds" :key="tagId" :tag-id="tagId">
      <template #default="{ tagName }">
        <span class="text-14px mx-3px text-gray-500"> {{ idx > 0 ? logicStr : '' }} </span>
        <span color-noraml class="text-14px">“{{ tagName }}”</span>
      </template>
    </customer-tag>
  </div>
  <el-button text type="primary" size="small" class="ml-5px flex-shrink-0" @click="select()">
    <svg-icon v-if="tagIds.length" name="pen" class="mr-3px"></svg-icon>
    <el-icon v-else class="mr-3px">
      <Plus />
    </el-icon>
    {{tagIds.length ? '编辑' : '添加'}}
  </el-button>
  <tag-drawer ref="tagDrawerRef" :mult="true" @get-select-tag="change($event)"></tag-drawer>
</template>
<script setup>
import { computed, inject, ref } from 'vue';
import { useVModel } from '@vueuse/core';
import customerTag from "@/components/customer-tag/customer-tag.vue";
import TagDrawer from "@/components/tag-drawer/index.vue";

const props = defineProps({
  condition: { type: String, default: '' },
  modelValue: {
    type: Object,
    default: () => ({})
  }
})
const emit = defineEmits(['update:modelValue']);
const data = useVModel(props, 'modelValue', emit);
const logic = { and: '且', or: '或' }
const width = inject('filter-group-right-width');
const logicStr = computed(() => logic[props.condition] || '')
const tagIds = computed(() => Array.isArray(data.value) ? data.value : [])
const tagDrawerRef = ref();
const tagWidth = computed(() => {
  const fixedWidth = 140 * 2 + 62 * 2 + 20;
  if (width && width.value > fixedWidth) return width.value - fixedWidth;
  return ''
})


function change(ids) {
  data.value = ids
}

function select() {
  tagDrawerRef.value.openDialog([...tagIds.value]);
}

</script>
