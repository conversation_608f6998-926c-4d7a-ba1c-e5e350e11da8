<template>
  <el-scrollbar wrap-class="h-full bg-white rounded" v-loading="searchLoading">
    <div class="px-15px">
      <div class="text-16px font-semibold pt-12px">聊天记录</div>
      <div ref="searchRef" class="search-record"></div>
    </div>
  </el-scrollbar>
</template>
<script setup>
import { searchChatRecordList, searchLoading, seatchFrame, handleScroll } from "./api/chatSearch";
import { ref, onMounted, nextTick } from "vue";
import * as ww from "@/libs/@wecom/jssdk/wecom.esm.js";
import { template, style } from "./template/searchRecord.js";
const searchRef = ref(null);
const emit = defineEmits(["readChat"]);
function readChat(item) {
  emit("readChat", item.currentTarget.dataset.info);
}
onMounted(async () => {
  await nextTick();
  const factory = ww.createOpenDataFrameFactory();
  seatchFrame.value = factory.createOpenDataFrame({
    el: searchRef.value, // “容器”元素，用于挂载展示组件
    template,
    style,
    data: {
      migList: searchChatRecordList.value,
    },
    methods: {
      chat(item) {
        readChat(item);
      },
      bindscrolltolower() {
        handleScroll();
      },
    },
  });
});
</script>
<style lang="scss" scoped>
iframe {
  width: 100%;
  height: 100%;
}

.search-record {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 35px;
}
</style>
