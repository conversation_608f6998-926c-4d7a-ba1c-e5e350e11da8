<template>
  <my-layout class="bg-white rounded">
    <layout-item>
      <div class="flex flex-wrap items-center px-15px py-10px border-b border-gray-100">
        <input-filter v-model="taskContent" label="任务内容" placeholder="请输入任务内容" :width="200" />
        <radio-filter v-model="eventType" label="任务类型" :list="eventTypeList" :width="200" />
        <date-filter v-model="plannedExecutionTime" label="计划执行时间" :text="plannedExecutionTime" />
        <el-button class="ml-30px" type="primary" @click="search()">查询</el-button>
        <el-button type="primary" @click="reset()" plain>重置</el-button>
      </div>
    </layout-item>
    <layout-main v-loading="loading" :scroll="false">
      <el-table stripe height="100%" :data="list">
        <el-table-column property="taskType" label="待办类型" :min-width="150" />
        <el-table-column property="taskContent" label="待办内容" :min-width="200">
          <template #default="{ row }">
            <el-popover v-if="row.taskContent" placement="top-start" :width="300" trigger="hover">
              <template #reference>
                <el-text truncated class="pointer" style="transform: translateY(4px)">
                  <content-userid-transform :content="row.taskContent"></content-userid-transform>
                </el-text>
              </template>
              <content-userid-transform :content="row.taskContent"></content-userid-transform>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column property="customerName" label="患者" :min-width="100">
          <template #default="{ row: { customerName, eventType } }">
            <!-- 成员待建档不显示昵称-->
            <span v-if="eventType == 'remindFiling'"></span>
            <span v-else>{{ customerName }}</span>
          </template>
        </el-table-column>
        <el-table-column property="executorUserId" label="处理人" :min-width="100">
          <template #default="{ row: { executorUserId } }">
            <span v-if="executorUserId === 'system'">系统自动</span>
            <ww-user v-else-if="executorUserId" :openid="executorUserId"></ww-user>
          </template>
        </el-table-column>
        <el-table-column property="teamName" label="所属团队" :min-width="160">
          <template #default="{ row: { executeTeamId } }">
            <team-data :teamId="executeTeamId" />
          </template>
        </el-table-column>

        <el-table-column property="creatorUserId" label="创建人" :min-width="100">
          <template #default="{ row: { creatorUserId } }">
            <span v-if="creatorUserId === 'system'">系统自动</span>
            <ww-user v-else-if="creatorUserId" :openid="creatorUserId"></ww-user>
          </template>
        </el-table-column>
        <el-table-column property="plannedExecutionTime" label="计划执行时间" :min-width="150" />
        <el-table-column property="expireTime" label="失效时间" :min-width="150">
          <template #default="{ row: { expireTime, willBeExpired } }">
            <span>{{ expireTime }}</span>
            <el-text v-if="willBeExpired" type="danger">（即将失效）</el-text>
          </template>
        </el-table-column>
        <el-table-column fixed="right" class-name="el-table-fixed-column--shadow" label="操作" width="150">
          <template #default="{ row }">
            <div flex items-center>
              <el-text type="primary" class="mr-10" pointer @click="showDetail(row)">任务详情</el-text>
              <el-text v-if="wxContact[row.customerUserId] || wxContact[row.externalUserId]" type="primary" pointer
                @click="toChat(row)">聊天</el-text>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </layout-main>
    <layout-item>
      <div class="flex justify-end p-15px">
        <el-pagination class="ml-auto" layout="total, prev, pager, next, sizes, jumper" :current-page="page"
          :page-size="pageSize" :page-sizes="[10, 20, 50]" :total="total" @current-change="changePage"
          @size-change="changeSize"></el-pagination>
      </div>
    </layout-item>
  </my-layout>
</template>
<script setup>
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { getToDoList } from "@/api/todo";
import { ToDoEventType, ServiceType } from "@/baseData";
import useElPagination from "@/hooks/useElPagination";
import useTodoTable from "./useTodoTable";
import useTeamId from "./useTeamId";

import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import { DateFilter, InputFilter, RadioFilter } from "@/components/filter-bar";
import contentUseridTransform from "@/components/content-userid-transform/index.vue";
import TeamData from '@/components/team-data/team-data.vue'

useTeamId(search);
const { formatList, showDetail, toChat, wxContact } = useTodoTable();

const loading = ref(false);
const eventType = ref("");
const plannedExecutionTime = ref("");
const taskContent = ref("");
const eventTypeList = Object.keys(ToDoEventType).map((i) => ({
  label: ToDoEventType[i],
  value: i,
}));
const { page, pageSize, changePage, changeSize } = useElPagination(getList);
const list = ref([]);
const total = ref(0);
let payload = {};

function search() {
  const params = {};
  if (eventType.value) params.eventType = eventType.value;
  if (plannedExecutionTime.value) params.plannedExecutionTime = plannedExecutionTime.value;
  if (taskContent.value) params.taskContent = taskContent.value;
  payload = { ...params };
  changePage(1);
}

async function getList() {
  loading.value = true;
  const { success, data, message } = await getToDoList(
    page.value,
    pageSize.value,
    {
      ...payload,
      executorUserId: localStorage.getItem("userId"),
      eventStatus: "untreated",
      corpId: localStorage.getItem("corpId"),
    },
    { orderBy: "plannedExecutionTime", orderType: "asc" }
  );
  if (success) {
    const { data: tableData = [], total: count } = data;
    list.value = Array.isArray(tableData) ? formatList(tableData) : [];
    total.value = count;
  } else {
    ElMessage.error(message);
  }
  loading.value = false;
}

function reset() {
  taskContent.value = "";
  plannedExecutionTime.value = "";
  eventType.value = "";
  search();
}
</script>
