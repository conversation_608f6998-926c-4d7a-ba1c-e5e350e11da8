<template>
  <div v-if="isRead">
    {{ item.value && Array.isArray(item.value) ? item.value.join("/") : item.value }}
  </div>
  <el-cascader v-else :model-value="value" :options="customerSources" placeholder="请选择客户来源" class="w-200px" filterable clearable @change="change($event)" />
</template>
<script setup>
import { onMounted, ref } from "vue";
const $emit = defineEmits(["change"]);
import { memberStore } from "@/store/member";
const customerSources = ref([]);

onMounted(() => getCustomerSources());

const props = defineProps({
  item: {
    type: Object,
    default: {},
  },
  value: {
    type: String,
    default: "",
  },
  isRead: {
    type: Boolean,
    default: false,
  },
});
function change(value) {
  $emit("change", {
    title: props.item.title,
    value,
  });
}
function getCustomerSources() {
  let { corpInfo } = memberStore();
  let customerSourceList = corpInfo.customerSourceList ? JSON.parse(JSON.stringify(corpInfo.customerSourceList)) : [];
  customerSources.value = updateValue(customerSourceList, "sourceName");
}
function updateValue(data, field) {
  if (Array.isArray(data)) {
    data = data.filter((item) => !item.disable);
    return data.map((item) => updateValue(item, field));
  } else if (data && typeof data === "object") {
    let newData = { ...data }; // 创建一个新的对象以避免直接修改原始对象
    if (newData[field] !== undefined) {
      newData.value = newData[field];
      newData.label = newData[field];
    }
    Object.keys(newData).forEach((key) => {
      newData[key] = updateValue(newData[key], field);
    });
    return newData;
  }
  return data;
}
</script>

<style></style>