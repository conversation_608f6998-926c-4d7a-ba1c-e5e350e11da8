<template>
  <el-table stripe height="100%" empty-text="暂无数据" :data="list">
    <el-table-column prop="date" label="日期" />
    <el-table-column prop="count" label="新增客户数" />
  </el-table>
</template>
    <script setup>
import { computed, ref, watch } from "vue";
import { dates } from "./useDateTag";
const props = defineProps({
  addCustomerList: { type: Array, default: () => [] },
});
const list = computed(() => {
  let dateCounts =
    props.addCustomerList.length > 0 &&
    props.addCustomerList.reduce((acc, item) => {
      const itemDate = item.createTime;
      if (!acc[itemDate]) {
        acc[itemDate] = 0;
      }
      acc[itemDate]++;
      return acc;
    }, {});
  if (dateCounts) {
    return Object.entries(dateCounts).map(([date, count]) => ({ date, count }));
  } else {
    return [];
  }
});

</script>
    