<template>
  <el-dialog title="套餐" :model-value="visible" :width="width" @close="close()">
    <classify-seach-panel height="40vh" placeholder="输入套餐名称搜索" :cate-method="getProjectPackageCateList"
      :search-method="getPackages" :list-method="getPackages">
      <template #searchItem="{item}">
        <div class="py-10px px-15px cursor-pointer flex items-center border-b border-gray-200" @click="toggle(item)">
          <div class="flex-grow w-0 break-all mr-15px">{{ item.name }}</div>
          <el-checkbox :model-value="Boolean(map[item._id])" />
        </div>
      </template>
      <template #selectItem="{item}">
        <div class="py-10px px-15px cursor-pointer flex items-center border-b border-gray-200" @click="toggle(item)">
          <div class="flex-grow w-0 break-all mr-15px">{{ item.name }}</div>
          <el-checkbox :model-value="Boolean(map[item._id])" />
        </div>
      </template>
    </classify-seach-panel>
    <template #footer>
      <span class="block text-center">
        <el-button class="w-100px" @click="close()">取消</el-button>
        <el-button class="w-100px" type="primary" @click="confirm()">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { memberStore } from "@/store/member";
import { getProjectPackageCateList, getProjectPackageList } from '@/api/member';

import classifySeachPanel from "@/components/classify-list/classify-search-panel.vue";
const props = defineProps({
  data: [],
  visible: { type: Boolean, default: false },
  width: { type: [String, Number], default: '600px' }
})
const emits = defineEmits(['close', 'change'])

const { memberInfo } = storeToRefs(memberStore())
const selections = ref([]);
const map = computed(() => {
  return selections.value.reduce((acc, cur) => {
    acc[cur._id] = true
    return acc
  }, {})
})

function toggle(item) {
  if (map.value[item._id]) {
    selections.value = selections.value.filter(i => i._id !== item._id)
  } else {
    selections.value.push(item)
  }
}

async function getPackages({ page, pageSize, cateIds: classIds, name }) {
  const { data } = await getProjectPackageList({ page, pageSize, classIds, name, enable: true, corpId: memberInfo.value.corpId });
  const list = Array.isArray(data.list) ? data.list : [];
  const total = data.total > 0 ? data.total : 0;
  return { list, total };
}

function close() {
  emits('close')
}

function confirm() {
  emits('change', [...selections.value])
  close()
}

watch(() => props.visible, n => {
  if (n) {
    selections.value = [...props.data];
  }
})
</script>
<style lang="scss" scoped></style>
