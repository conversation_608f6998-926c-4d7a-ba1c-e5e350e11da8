import { defineStore } from "pinia";
import { transformData, validateColor } from "@/utils";
import { getCustomCorpInfo, getCorpInfo, getCorpMemberData, getRolesByRoleId } from "@/api/corp";

const THEME_COLOR = "#007aff"; // 默认主题色
const DEFAULT_LOGO = new URL("@/assets/icons/logo-plain.png", import.meta.url).href; // 默认logo

const memberStore = defineStore("member", {
  state: () => {
    // 检查 localStorage 中是否有用户信息来初始化登录状态
    const hasUserInfo = localStorage.getItem("corpId") && localStorage.getItem("userId");
    const initLogined = hasUserInfo ? "restore" : "unlogin";

    return {
      logined: initLogined,
      memberInfo: {},
      corpInfo: {},
      userId: localStorage.getItem("userId") || "",
      corpId: localStorage.getItem("corpId") || "",
      suite_token: "",
      myExternalcontact: [],
      myExternalcontactLoading: false,
      wxContact: {}, //微信好友 非企微员工
      teams: [],
      currentTeam: [],
      isAdmin: false,
    };
  },
  getters: {
    isHangzheng(state) {
      // 杭州整形医院
      return state.corpInfo && state.corpInfo.corpId === "wpLgjyawAAJ69XjD39GMOXp2EsYHYb3w";
    },
    corpPic(state) {
      return state.corpInfo && state.corpInfo.corpPic ? state.corpInfo.corpPic : "";
    },
    customLogo(state) {
      return state.corpInfo && state.corpInfo.customLogo ? state.corpInfo.customLogo : DEFAULT_LOGO;
    },
    themeColor(state) {
      return state.corpInfo && validateColor(state.corpInfo.themeColor) ? state.corpInfo.themeColor : THEME_COLOR;
    },
    customerSourceList(state) {
      return state.corpInfo && Array.isArray(state.corpInfo.customerSourceList) ? transformData(state.corpInfo.customerSourceList) : [];
    },
    menus(state) {
      return state.memberInfo && Array.isArray(state.memberInfo.menu) ? state.memberInfo.menu : [];
    },
    intentedProjectList(state) {
      return state.corpInfo && Array.isArray(state.corpInfo.intentedProjectList)
        ? state.corpInfo.intentedProjectList.map((i) => ({
            label: i,
            value: i,
          }))
        : [];
    },
    roleIds(state) {
      return state.memberInfo && Array.isArray(state.memberInfo.roleIds) ? state.memberInfo.roleIds : [];
    },
    baseInfo(state) {
      const fixed = [{ label: "联系电话", prop: "assistantByMobile" }];
      const rows =
        state.corpInfo && state.corpInfo.corpFileds && Array.isArray(state.corpInfo.corpFileds.baseInfo)
          ? state.corpInfo.corpFileds.baseInfo.map(({ name: label, required, title: prop }) => ({
              label,
              prop,
              required,
            }))
          : [];
      return [...rows, ...fixed];
    },
    healthInfo(state) {
      return state.corpInfo && state.corpInfo.corpFileds && Array.isArray(state.corpInfo.corpFileds.healthInfo)
        ? state.corpInfo.corpFileds.healthInfo.map(({ name: label, required, title: prop }) => ({
            label,
            prop,
            required,
          }))
        : [];
    },
    viceInfo(state) {
      const fixed = [{ label: "客户阶段", prop: "customerStage", required: true }];
      const fixedTail = [{ label: "备注", prop: "notes" }];
      const rows =
        state.corpInfo && state.corpInfo.corpFileds && Array.isArray(state.corpInfo.corpFileds.viceInfo)
          ? state.corpInfo.corpFileds.viceInfo.map(({ name: label, required, title: prop }) => ({
              label,
              prop,
              required,
            }))
          : [];
      return [...fixed, ...rows, ...fixedTail];
    },
    tagList(state) {
      const tags = state.corpInfo && Array.isArray(state.corpInfo.tags) ? state.corpInfo.tags : [];
      return tags.reduce((list, item) => {
        if (Array.isArray(item.options)) {
          list.push(...item.options);
        }
        return list;
      }, []);
    },
    intentedProjectList(state) {
      return state.corpInfo && Array.isArray(state.corpInfo.intentedProjectList)
        ? state.corpInfo.intentedProjectList.map((i) => ({
            label: i,
            value: i,
          }))
        : [];
    },
  },
  actions: {
    async reloadCorpInfo(fields) {
      if (!fields || fields.length === 0) return;
      const { success, data } = await getCustomCorpInfo({ corpId: this.corpInfo.corpId, fields });
      if (success && data.data && data.data.tags && Array.isArray(data.data.tags)) {
        fields.forEach((i) => {
          if (data.data && data.data[i]) {
            this.corpInfo[i] = data.data[i];
          }
        });
      }
    },
    changeCorpInfo({ key, value }) {
      if (key) this.corpInfo[key] = value;
    },
    setLogined(status) {
      this.logined = status;
    },
    async getMemberInfo() {
      if (!localStorage.getItem("corpId") || !localStorage.getItem("userId")) {
        this.memberInfo = {};
        return {};
      }
      const { data, success } = await getCorpMemberData();
      if (success) {
        this.memberInfo = data.data;
        // 确保 store 中的 userId 和 corpId 与 localStorage 一致
        this.userId = localStorage.getItem("userId") || "";
        this.corpId = localStorage.getItem("corpId") || "";
        return data.data;
      } else {
        return {};
      }
    },
    async getCorpInfo() {
      if (!localStorage.getItem("corpId")) {
        this.corpInfo = {};
        return {};
      }
      const { success, data } = await getCorpInfo();
      if (success) {
        this.corpInfo = data.data[0];
        // 确保 store 中的 corpId 与 localStorage 一致
        this.corpId = localStorage.getItem("corpId") || "";
        return data.data[0];
      }
    },
    // 判断是否为超级管理员
    async judgmentIsAdmin() {
      let { roleIds = [] } = this.memberInfo;
      let { data: list } = await getRolesByRoleId(roleIds);
      if (list.data && list.data.length > 0) {
        this.isAdmin = list.data.some((i) => i.roleId === "admin");
      }
    },

    // 完整的退出登录清理方法
    async logout() {
      // 1. 清理 localStorage (保留记住用户名相关的设置)
      const rememberUsername = localStorage.getItem("rememberUsername");
      const lastUsername = localStorage.getItem("lastUsername");

      localStorage.clear();

      // 恢复记住用户名的设置
      if (rememberUsername === "true" && lastUsername) {
        localStorage.setItem("rememberUsername", rememberUsername);
        localStorage.setItem("lastUsername", lastUsername);
      }

      // 2. 清理 sessionStorage 中的用户相关信息
      sessionStorage.removeItem("doctorNo");
      sessionStorage.removeItem("CORPVISITPLAN_CATEID");
      sessionStorage.removeItem("CORPVISITPLAN_PAGE");
      // 清理微信联系人缓存
      sessionStorage.removeItem("wx-contact-store");

      // 3. 重置 store 状态
      this.logined = "unlogin";
      this.memberInfo = {};
      this.corpInfo = {};
      this.userId = "";
      this.corpId = "";
      this.suite_token = "";
      this.myExternalcontact = [];
      this.myExternalcontactLoading = false;
      this.wxContact = {};
      this.teams = [];
      this.currentTeam = [];
      this.isAdmin = false;

      // 4. 清理其他相关 store 状态
      // 重置团队相关状态
      const { teamStore } = await import("@/store/team");
      const teamStoreInstance = teamStore();
      teamStoreInstance.currentTeam = {};
      teamStoreInstance.teams = [];
      teamStoreInstance.allTeams = [];
      teamStoreInstance.roles = [];
      teamStoreInstance.isAdmin = false;
      localStorage.removeItem("currentTeamId");

      console.log("退出登录清理完成");
    },
  },
});
export { memberStore };
