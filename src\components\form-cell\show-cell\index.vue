<template>
  <custom-bmi v-if="type === 'BMI'" :form="form" />
  <custom-blood-pressure v-else-if="type === 'bloodPressure'" :isRead="true" :form="form" />

  <el-text v-else size="default">{{ content }}
  </el-text>
</template>
<script setup>
import { computed, defineAsyncComponent } from 'vue';
import dayjs from 'dayjs';
const customBmi = defineAsyncComponent(() => import('../components/custom-bmi.vue'))
const customBloodPressure = defineAsyncComponent(() => import('../components/custom-blood-pressure.vue'))

const props = defineProps({
  form: { type: Object, default: () => ({}) },
  type: { default: '' },
  appendText: { default: '' },
  format: { default: 'YYYY-MM-DD' },
  title: { default: '' }
})
const value = computed(() => props.form[props.title]);
const content = computed(() => {
  if (props.type === 'input') return `${value.value || ''} ${value.value && props.appendText ? props.appendText : ''}`
  if (props.type === 'multiSelectAndOther') return Array.isArray(value.value) ? value.value.filter(i => i && i !== '其他').join('、') : value.value;
  if (props.type === 'selfMultipleDiseases') return Array.isArray(value.value) ? value.value.filter(i => i && i !== '其他').join('、') : value.value;
  if (props.type === 'date') return value.value && dayjs(value.value).isValid() ? dayjs(value.value).format(props.format) : ''
  return value.value || ''
})
</script>
<style lang="scss" scoped></style>
