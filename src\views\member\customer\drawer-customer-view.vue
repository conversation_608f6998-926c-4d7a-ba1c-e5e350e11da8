<template>
  <my-layout class="pt-10px relative" bg-fff>
    <layout-item>
      <div class="h-10px"></div>
      <el-button text class="absolute right-15px top-15px z-1" type="primary" @click="edit">编辑</el-button>
    </layout-item>
    <layout-main>
      <div px-15 class="pb-50px">
        <el-form :label-width="200" label-suffix="：" ref="formRef" :model="form">
          <div v-for="(item, idx) in selfTemplateList" :key="item.type">
            <div v-if="item.templateList && item.templateList.length > 0">
              <div class="group-title pt-15px">{{ item.name }}</div>
              <el-row>
                <template v-for="field in item.templateList" :key="field.sort">
                  <el-col v-if="showField(field)" :span="layoutCol[field.title] || 12">
                    <el-form-item :label="field.name" class="el-form-item--autoHeight" :prop="field.title" :rules="ruleGroup[idx][field.title]">
                      <form-cell :item="field" :value="form[field.title]" class="w-full" :isRead="!isCanEdit" :form="form"></form-cell>
                    </el-form-item>
                  </el-col>
                </template>
              </el-row>
            </div>
          </div>
        </el-form>
      </div>
    </layout-main>
  </my-layout>
  <customer-repeat :visible="repeatVisible" :customer="customer" @acceptCustomer="acceptCustomer" @close="repeatVisible = false" :operateType="operateType"></customer-repeat>
  <el-drawer :model-value="visible" :title="title" size="55%" @close="visible = false" :with-header="false">
    <customer-view ref="editRef" :canRemove="formData._id" class="pt-10px w-full pb-80px" operateType="edit" :templateList="selfTemplateList" @onClose="visible = false" @onSuccess="success" :headerHeight="-10" :formData="editFormData" @onRemove="remove"></customer-view>
  </el-drawer>
</template>
<script setup>
import { computed, nextTick, onMounted, ref, watch, provide } from "vue";
import { useElementBounding } from "@vueuse/core";
import { ElMessage } from "element-plus";
import dayjs from "dayjs";
import validate from "@/utils/validate";

import customerRepeat from "../components/customer-repeat/index.vue";
import customerView from "./customer-view.vue";
import formCell from "@/components/form-cell";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import repeatComponentApi from "@/views/member/components/customer-repeat/api";
import specialRule from "./special-rule";

const form = ref({});
const editFormData = ref({});
const isRead = ref(true);
const { repeatVisible, customer } = repeatComponentApi();
const emits = defineEmits(["onClose", "onSuccess", "onRemove"]);
const props = defineProps({
  removeSymbol: {
    type: [String, Number],
    default: "",
  },
  operateType: {
    type: String,
    default: "add",
  },
  templateList: {
    type: Array,
    default: () => [],
  },
  formData: {
    type: Object,
    default: () => ({}),
  },
  headerHeight: {
    type: Number,
    default: 60,
  },
  col: {
    type: Object,
    default: () => ({}),
  },
  styleClass: {
    type: String,
    default: "",
  },
  canRemove: {
    type: Boolean,
    default: false,
  },
  filterRule: {
    type: Object,
    default: () => ({}),
  },
});

const layoutCol = ref({});
provide("set-form-template-layout-col", setLayoutCol);
function setLayoutCol(title, col) {
  if (title && typeof col === "number" && col % 1 === 0 && col > 0 && col <= 24) {
    layoutCol.value[title] = col;
  }
}

watch(
  () => [props.templateList, props.formData],
  async ([, formData]) => {
    form.value = JSON.parse(JSON.stringify(formData));
    editFormData.value = JSON.parse(JSON.stringify(form.value));
    isRead.value = true;
  },
  { immediate: true, deep: true }
);

const selfTemplateList = computed(() => {
  return props.templateList.map((temp) => {
    const item = { ...temp };
    let list = Array.isArray(item.templateList) ? item.templateList : [];
    list = list.filter((i) => {
      const filterOnlyRead = props.operateType === "add" ? item.operateType !== "onlyRead" : true;
      const ruleFilter = props.filterRule && typeof props.filterRule[i.title] === "function" ? props.filterRule[i.title](form.value) : true;
      return filterOnlyRead && ruleFilter;
    });
    return { ...item, templateList: list };
  });
});
const ruleGroup = computed(() => {
  return selfTemplateList.value.map((i) => {
    const templateList = Array.isArray(i.templateList) ? i.templateList : [];
    return templateList.reduce((val, item) => {
      if (item.title && typeof specialRule[item.type] === "function") {
        val[item.title] = specialRule[item.type](form, item.required);
      } else if (item.title) {
        val[item.title] = { required: item.required, trigger: "blur", message: `请输入${item.name}` };
      }
      return val;
    }, {});
  });
});

function showField(item) {
  return (!item.referenceField || (item.referenceField && form.value[item.referenceField] === item.referenceValue)) && item.fieldStatus !== "disable";
}
const isCanEdit = computed(() => {
  return props.operateType === "add" || (props.operateType === "edit" && !isRead.value);
});

// 接收数据库里的档案数据
function acceptCustomer() {
  form.value = JSON.parse(JSON.stringify(customer.value));
  if (form.value.idCard) {
    form.value.age = validate.getAge(form.value.idCard);
    form.value.sex = validate.getGender(form.value.idCard);
    form.value.birthday = validate.getBirthdateFromId(form.value.idCard);
    form.value.relationship = "本人";
  }
  repeatVisible.value = false;
}

const editRef = ref();
// const headRef = ref()
// const { width } = useElementBounding(headRef)
// const formItemCol = computed(() => width.value >= 960 ? 12 : 24)

const visible = ref(false);
async function edit() {
  editFormData.value = JSON.parse(JSON.stringify(form.value));
  visible.value = true;
  await nextTick();
  typeof editRef.value.editAction === "function" && editRef.value.editAction();
}

function success(...rest) {
  emits("onSuccess", ...rest);
}

function remove() {
  emits("onRemove");
}

watch(
  () => props.removeSymbol,
  () => (visible.value = false)
);

defineExpose({
  closeDrawer: () => (visible.value = false),
});
</script>

<style lang="scss">
.group-title {
  padding: 0 12px 16px;
  color: var(--yc-main-text-color);
  font-weight: 600;
  font-size: 14px;
}

.el-form-item__label {
  line-height: normal;
  align-items: center;
}
</style>
