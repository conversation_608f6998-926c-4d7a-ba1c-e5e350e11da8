<template>
  <el-dialog title="选择项目" :model-value="visible" :width="width" :close-on-click-modal="false" @close="close()">
    <div class="pb-10px border-b border-gray-200">
      <el-input v-model.trim="keyword" placeholder="搜索项目" :prefix-icon="Search" clearable />
    </div>
    <div v-show="keyword" v-loading="loading">
      <el-scrollbar class="h-40vh" color-normal>
        <div v-for="i in showSearchList" :key="`search_${i.checkedKey}`" class="py-10px px-15px cursor-pointer flex items-center border-b border-gray-200" @click="toggle(i)">
          <div class="flex-grow w-0 break-all mr-15px">
            <div>{{ i.projectName }}</div>
            <div v-if="i.cateNames" class="mt-5px text-gray-500">{{ i.cateNames }}</div>
          </div>
          <el-checkbox :model-value="Boolean(map[i.checkedKey])" />
        </div>
        <div v-if="searchMore" class="mt-10px px-10px flex items-center justify-center py-4px cursor-pointer text-gray-400 hover:text-blue-500" @click="getMore()">
          <div class="flex-grow h-1px bg-gray-200"></div>
          <div class="text-14px ml-10px">加载更多</div>
          <el-icon class="flex-shrink-0 ml-5px mr-10px text-14px">
            <ArrowDownBold />
          </el-icon>
          <div class="flex-grow h-1px bg-gray-200"></div>
        </div>
      </el-scrollbar>
    </div>
    <div v-show="!keyword" v-loading="loading">
      <el-scrollbar class="h-40vh" color-normal>
        <div v-if="list.length === 0" class="h-36vh flex items-center justify-center">
          <empty-data :title="emtpyText" />
        </div>
        <div v-for="i in currentCateList" :key="i.checkedKey" class="py-10px px-15px cursor-pointer flex items-center border-b border-gray-200" @click="toggle(i)">
          <div class="flex-grow w-0 break-all mr-15px">{{ i.projectName }}</div>
          <el-checkbox :model-value="Boolean(map[i.checkedKey])" />
        </div>
        <div v-if="more" class="mt-10px px-10px flex items-center justify-center py-4px cursor-pointer text-gray-400 hover:text-blue-500" @click="loadMore()">
          <div class="flex-grow h-1px bg-gray-200"></div>
          <div class="text-14px ml-10px">加载更多</div>
          <el-icon class="flex-shrink-0 ml-5px mr-10px text-14px">
            <ArrowDownBold />
          </el-icon>
          <div class="flex-grow h-1px bg-gray-200"></div>
        </div>
      </el-scrollbar>
    </div>
    <template #footer>
      <span class="block text-center">
        <el-button class="w-100px" @click="close()">取消</el-button>
        <el-button class="w-100px" type="primary" @click="dialogSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, watch } from "vue";
import { watchDebounced } from "@vueuse/core";
import { Search, ArrowDownBold } from "@element-plus/icons-vue";
import { getProjectList, getProjectListWithCates } from "@/api/project-manage";
import EmptyData from "@/components/empty-data.vue";

const emits = defineEmits(["close", "change"]);
const props = defineProps({
  selectWidthCate: { type: Boolean, default: false },
  visible: { type: Boolean, default: false },
  value: { type: Array, default: () => [] },
  width: { type: [Number, String], default: 400 },
  formatter: { type: Function, default: (i) => ({ _id: i._id, projectName: i.projectName, projectCateId: i.projectCateId }) },
  currentCategory: { type: Array, default: () => [] }, // 改为接收数组类型，适配多选分类
});

const keyword = ref("");
const loading = ref(false);
const list = ref([]);
const more = ref(false);
const page = ref(1);
const searchPage = ref(1);
const searchList = ref([]);
const searchMore = ref(false);
const selections = ref([]);

const showSearchList = computed(() => searchList.value.map((i) => ({ ...i, checkedKey: setCheckedKey(i, i.projectCateId) })));
const currentCateList = computed(() => {
  return list.value.map((i) => ({ ...i, checkedKey: setCheckedKey(i, i.projectCateId) }));
});
const map = computed(() => {
  return selections.value.reduce((p, c) => {
    p[c.checkedKey] = c;
    return p;
  }, {});
});
const emtpyText = computed(() => (list.value.length === 0 ? "暂无项目" : ""));
const formatFn = computed(() => {
  if (typeof props.formatter === "function") return props.formatter;
  if (props.selectWidthCate) return (project) => ({ _id: project._id, projectName: project.projectName, projectCateId: project.projectCateId });
  return (project) => ({ _id: project._id, projectName: project.projectName });
});

function setCheckedKey(project, cateId) {
  return props.selectWidthCate ? `${cateId}_${project._id}` : project._id;
}

function close() {
  emits("close");
}

function loadMore() {
  if (more.value && !loading.value) {
    page.value++;
    getProjects();
  }
}

function toggle(project) {
  if (map.value[project.checkedKey]) {
    selections.value = selections.value.filter((i) => i.checkedKey !== project.checkedKey);
  } else {
    selections.value.push({ ...project });
  }
}

function dialogSubmit() {
  emits("change", {
    projects: selections.value.map(formatFn.value),
  });
  close();
}

async function getProjects() {
  let cateIds = [];

  // 修改为处理数组类型的currentCategory
  if (props.currentCategory && props.currentCategory.length > 0) {
    // 从所有选中的分类中收集ID和子分类ID
    props.currentCategory.forEach((category) => {
      if (category && category._id) {
        cateIds.push(category._id);
        // 如果有子分类且不是按分类选择模式，添加所有子分类
        if (!props.selectWidthCate && Array.isArray(category.childrenIds)) {
          cateIds.push(...category.childrenIds);
        }
      }
    });

    // 去重
    cateIds = [...new Set(cateIds)];
  }
  const p = {
    page: page.value,
    pageSize: 15,
    corpId: localStorage.getItem("corpId"),
    projectStatus: "enable",
  };

  if (cateIds.length > 0) {
    p.cateIds = cateIds;
  }

  const { arr, count } = await getList(p, true);

  // 处理获取到的项目列表
  const projects = arr.map((i) => {
    // 如果有多个分类，尝试找到项目属于的第一个分类
    let projectCateId = "";
    if (props.currentCategory && props.currentCategory.length > 0) {
      // 检查项目的分类ID是否在选中的分类中
      if (i.projectCateId) {
        const matchedCategory = props.currentCategory.find((cat) => cat._id === i.projectCateId || (Array.isArray(cat.childrenIds) && cat.childrenIds.includes(i.projectCateId)));
        if (matchedCategory) {
          projectCateId = matchedCategory._id;
        }
      }
    }

    return {
      ...i,
      projectCateId: projectCateId || "",
      checkedKey: setCheckedKey(i, projectCateId || ""),
    };
  });

  list.value = page.value === 1 ? projects : [...list.value, ...projects];
  more.value = count > list.value.length;
}

async function getProjectsByIds(ids) {
  const unexistIds = ids.filter((i) => !map.value[i]);
  const existProjects = ids.map((i) => map.value[i]).filter(Boolean);
  if (unexistIds.length) {
    const p = {
      page: 1,
      pageSize: ids.length,
      corpId: localStorage.getItem("corpId"),
      ids: unexistIds,
    };
    const { arr } = await getList(p, false);
    return [...existProjects, ...arr];
  }
  return existProjects;
}

async function getMore() {
  if (searchMore.value && !loading.value) {
    searchPage.value++;
    loading.value = true;
    search();
  }
}

async function search() {
  let cateIds = [];

  // 修改为处理数组类型的currentCategory
  if (props.currentCategory && props.currentCategory.length > 0) {
    // 从所有选中的分类中收集ID和子分类ID
    props.currentCategory.forEach((category) => {
      if (category && category._id) {
        cateIds.push(category._id);
        // 如果有子分类且不是按分类选择模式，添加所有子分类
        if (!props.selectWidthCate && Array.isArray(category.childrenIds)) {
          cateIds.push(...category.childrenIds);
        }
      }
    });
    // 去重
    cateIds = [...new Set(cateIds)];
  }

  const p = {
    page: searchPage.value,
    pageSize: 15,
    corpId: localStorage.getItem("corpId"),
    projectStatus: "enable",
    projectName: keyword.value,
  };

  if (cateIds.length > 0) {
    p.cateIds = cateIds;
  }

  if (props.selectWidthCate) {
    const { data } = await getProjectListWithCates(p);
    const count = data && data.total >= 0 ? data.total : 0;
    const list =
      data && Array.isArray(data.list)
        ? data.list.map((i) => {
            const cateParentGroupNames = Array.isArray(i.cateParentGroupNames) ? i.cateParentGroupNames : [];
            const projectCateName = typeof i.projectCateName === "string" ? i.projectCateName : "";
            const cateNames = [...cateParentGroupNames, projectCateName];
            return {
              ...i,
              checkedKey: setCheckedKey(i, i.projectCateId),
              cateNames: cateNames.join("/"),
            };
          })
        : [];
    searchList.value = searchPage.value === 1 ? list : [...searchList.value, ...list];
    searchMore.value = searchPage.value < data.pages;
  } else {
    const { arr, count } = await getList(p, true);
    searchList.value = searchPage.value === 1 ? arr : [...searchList.value, ...arr];
    searchMore.value = count > searchList.value.length;
  }

  loading.value = false;
}

async function getList(payload, showMessage) {
  const { success, data, message } = await getProjectList(payload);
  const { list: arr, total: count = 0 } = data;
  if (success) {
    return { arr: Array.isArray(arr) ? arr : [], count };
  } else if (showMessage) {
    ElMessage.error(message);
  }
  return { arr: [], count: 0 };
}

watch(
  () => props.visible,
  async (n) => {
    if (!n) return;
    keyword.value = "";
    selections.value = props.value.map((i) => ({ ...i, checkedKey: setCheckedKey(i, i.projectCateId) }));
    page.value = 1;
    getProjects();
  }
);

watch(
  () => props.currentCategory,
  (n) => {
    if (n && Array.isArray(n)) {
      page.value = 1;
      getProjects();
    }
  },
  { deep: true }
);

watchDebounced(
  keyword,
  (n) => {
    if (n.trim() === "") {
      searchList.value = [];
    } else {
      loading.value = true;
      searchPage.value = 1;
      search();
    }
  },
  { debounce: 500 }
);

defineExpose({
  id2Project: getProjectsByIds,
  clear: () => {
    selections.value = [];
  },
});
</script>

<style lang="scss" scoped></style>
