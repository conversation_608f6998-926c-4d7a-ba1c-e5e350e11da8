<template>
  <customer-view class="pt-10px" operateType="edit" :templateList="templateList" @onClose="close" @onSuccess="success"
    :formData="selfCustomer" :headerHeight="0"></customer-view>
</template>
<script setup>
import customerView from "../../../customer/customer-view.vue";
import { tagsStore } from "@/store/tags";
const { removeTag: removeRouteTag } = tagsStore();
import { templateStore } from "@/store/template";
import { storeToRefs } from "pinia";
import { watch, ref, onMounted, computed } from "vue";
import { updateMember as updateMemberUrl } from "@/api/member";
import { ElMessage } from "element-plus";
const props = defineProps({
  customer: { type: Object, default: () => ({}) },
  memberId: { type: String, default: "" },
  memberName: { type: String, default: "" },
});
const emit = defineEmits(["reload"]);
const close = () => {
  getInitData(props.customer);
};
const store = templateStore();
const { corpTemplateList } = storeToRefs(store);
const { getCorpTemplate } = store;
const templateList = ref([
  {
    name: "基础信息",
    type: "baseTemplate",
  },
]);
const selfCustomer = computed(() => {
  return JSON.parse(JSON.stringify(props.customer));
});
watch(
  () => props.customer,
  (val) => {
    if (val) {
      getInitData(val);
    }
  },
  { immediate: true }
);
async function getInitData(customer) {
  if (!Array.isArray(corpTemplateList.value) || corpTemplateList.value.length === 0) {
    await getCorpTemplate();
  }
  templateList.value = templateList.value.map((item) => {
    const temp = corpTemplateList.value.find((i) => i.templateType === item.type);
    item.templateList = temp && temp.templateList;
    item.templateList &&
      item.templateList.forEach((e) => {
        if (customer[e.title]) {
          e.value = customer[e.title];
        }
      });
    return item;
  });
}
const success = async (item, callback) => {
  if (item.telphone && !/^\d{3,4}-\d{6,8}$/.test(item.telphone)) {
    ElMessage.warning("请输入正确的座机号码");
    callback && callback("fail");
    return;
  }
  const { success, message } = await updateMemberUrl(props.customer._id, item);
  if (success) {
    callback && callback("success");
    emit("reload");
    ElMessage.success(message);
  } else {
    callback && callback("fail");
    ElMessage.error(message);
  }
};
</script>

<style lang="scss">
.group-title {
  padding: 0 12px 16px;
  color: var(--yc-main-text-color);
  font-weight: 600;
  font-size: 14px;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}
</style>
