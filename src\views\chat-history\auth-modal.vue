<template>
  <el-dialog :model-value="visible" :title="title" @close="close" width="400">
    <img src="@/assets/sessionArchiveQrc.png" alt="" width="400" />
    <!-- <el-scrollbar wrap-style="max-height: 50vh;">
      <div color-normal class="font-semibold text-14px text-center leading-6">
        使用手机企业微信扫码 <br>
        {{type==='auth' ? '授权会话内容存档接口权限':'进入会话存档授权页面，调整存档员工' }}
      </div>
      <vue-qrcode class="mx-auto mb-10px" :value="docUrl" :options="{ width: 160, margin: 2 }"></vue-qrcode>
      <div v-if="type==='auth'" color-normal class="mx-15px p-15px bg-gray-100 rounded">
        <div class="text-16px font-semibold text-center mb-10px">授权说明</div>
        <div class="text-14px leading-24px">为了保护您的信息安全和隐私，企业微信规定开通存档需要企业管理员授权企微官方会话存档接口，展示的会话内容 仅支持企业查看，不可获取和导出
          。您的企业信息仅用于授权企小码使用展示组件展示会话内容，我们
          极度重视用户隐私，请放心使用。更多详细信息请阅读<span color-primary class="cursor-pointer hover:underline">《会话内容存档第三方接口授权协议》</span>
        </div>
      </div>
    </el-scrollbar>
    <template v-if="type==='auth'" #footer>
      <div text-center>
        <el-button class="w-100px" @click="close()">取消授权</el-button>
        <el-button class="w-100px" type="primary" @click="handleAuth()">
          我已完成授权
        </el-button>
      </div>
    </template> -->
  </el-dialog>
</template>
<script setup>
import { computed } from "vue";
import VueQrcode from "@chenfengyuan/vue-qrcode";

const emits = defineEmits(["close", "authed"]);
const props = defineProps({
  type: {
    type: String,
    default: "auth",
    validator: (val) => {
      return ["auth", "adjust"].includes(val);
    },
  },
  visible: { type: Boolean, default: false },
  width: { type: Number, default: 300 },
});
const docUrl = "https://wxwork/doc";
const title = computed(() => (props.type === "auth" ? "请先完成会话存档接口授权，开通聊天记录存档查看功能。" : "会话存档授权调整"));

const close = () => {
  emits("close");
};
const handleAuth = () => {
  emits("authed");
};
</script>