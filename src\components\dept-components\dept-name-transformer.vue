<template>
  <span v-if="loading">
    <el-spinner size="mini"></el-spinner>
  </span>
  <span v-else>{{ deptNames || '-' }}</span>
</template>

<script setup>
import { ref, watch, onMounted, computed } from 'vue';
import { useDeptStore } from "./dept-store";
import { ElMessage } from "element-plus";

const props = defineProps({
  deptId: {
    type: [String, Number, Array],
    default: null
  },
  separator: {
    type: String,
    default: '、'
  },
  deptList: {
    type: Array,
    default: null
  }
});

const deptStore = useDeptStore();
const loading = ref(false);
const deptNames = ref('');

// 是否使用外部传入的科室列表
const useExternalList = computed(() => Array.isArray(props.deptList) && props.deptList.length > 0);

// 获取科室列表
const fetchDeptList = async () => {
  // 如果提供了外部科室列表，则不需要获取
  if (useExternalList.value) {
    return;
  }
  
  // 如果store已有数据，不重复获取
  if (deptStore.hasDeptData) {
    return;
  }
  
  try {
    loading.value = true;
    await deptStore.fetchDeptList();
  } catch (error) {
    console.error('获取科室列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 转换ID为名称
const transformIdToName = () => {
  if (!props.deptId) {
    deptNames.value = '';
    return;
  }

  const deptIdArray = Array.isArray(props.deptId) ? props.deptId : [props.deptId];
  
  // 使用外部科室列表
  if (useExternalList.value) {
    const uniqueIds = [...new Set(deptIdArray)];
    const names = uniqueIds
      .map(id => {
        const dept = props.deptList.find(item => String(item.id) === String(id));
        return dept ? dept.name : '';
      })
      .filter(Boolean);
    
    deptNames.value = names.join(props.separator);
    return;
  }
  
  // 使用store中的数据
  if (deptStore.hasDeptData) {
    deptNames.value = deptStore.getDeptNamesByIds(deptIdArray, props.separator);
    return;
  }
  
  // 如果store中没有数据，且不在加载中，则获取数据
  if (!loading.value) {
    fetchDeptList();
  }
};

// 监听依赖变化
watch(
  () => [props.deptId, props.deptList, props.separator, deptStore.deptList],
  transformIdToName,
  { deep: true, immediate: true }
);

// 监听store加载状态
watch(
  () => deptStore.loading,
  (newLoading) => {
    if (!useExternalList.value) {
      loading.value = newLoading;
    }
  },
  { immediate: true }
);

onMounted(fetchDeptList);

// 暴露方法给父组件
defineExpose({
  fetchDeptList
});
</script>
