<template>
  <el-form-item class="px-15px py-6px el-form-item--autoheight" :class="required ? 'is-required' : ''" :label="name"
    border-bottom>
    <div class="flex flex-grow items-center">
      <el-input v-model="year" @update:model-value="change($event, 'year')" class="flex-grow"></el-input>
      <div class="mx-5px flex-shrink-0">年</div>
      <el-input v-model="month" @update:model-value="change($event, 'month')" class="flex-grow"></el-input>
      <div class="mx-5px flex-shrink-0">月</div>
      <el-input v-model="day" @update:model-value="change($event, 'day')" class="flex-grow"></el-input>
      <div class="mx-5px flex-shrink-0">日</div>
    </div>
  </el-form-item>
</template>
<script setup>
import { computed, ref, watch } from "vue";

const emits = defineEmits(["change"]);
const props = defineProps({
  appendText: { type: String, default: "" },
  form: { type: Object, default: () => ({}) },
  name: { type: String, default: "" },
  required: { type: Boolean, default: false },
  title: { type: String, default: "" },
  wordLimit: { type: Number, default: 20 },
});
const year = ref(null);
const month = ref(null);
const day = ref(null);
const value = computed(() => props.form[props.title] || "");

watch(
  () => props.form,
  (val) => {
    if (!value.value) return;
    const yearMatch = value.value.match(/(\d+)年/);
    const monthMatch = value.value.match(/(\d+)月/);
    const dayMatch = value.value.match(/(\d+)日/);
    year.value = yearMatch ? Number(yearMatch[1]) : "";
    month.value = monthMatch ? Number(monthMatch[1]) : "";
    day.value = dayMatch ? Number(dayMatch[1]) : "";
  },
  { immediate: true }
);

function change(value) {
  const yearStr = year.value && year.value !== "0" ? `${year.value}年` : "";
  const monthStr = month.value && month.value !== "0" ? `${month.value}月` : "";
  const dayStr = day.value && day.value !== "0" ? `${day.value}日` : "";
  emits("change", { title: props.title, value: `${yearStr}${monthStr}${dayStr}` });
}
</script>
<style lang="scss" scoped></style>