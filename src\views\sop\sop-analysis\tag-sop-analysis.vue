<template>
  <my-layout bg-fff>
    <layout-item>
      <div class="p-15px">
        <div class="flex items-center">
          <div class="text-16px font-semibold">{{ sop.sopName }}</div>
          <el-tag v-if="sop.statusName" class="ml-10px" :type="sop.status === 'executing' ? 'warning' : 'info'">
            {{ sop.statusName }}
          </el-tag>
        </div>
        <div class="mt-15px text-gray-500 text-14px truncate">
          <span>任务详情：打上标签</span>
          <customer-tag custom v-for="tagId in sop.newTagIds" :key="`show_${tagId}`" :tag-id="tagId">
            <template #default="{ tagName }">
              <span>【{{ tagName }}】</span>
            </template>
          </customer-tag>
          <span v-if="sop.removeTagIds && sop.removeTagIds.length">；删除标签</span>
          <customer-tag custom v-for="(tagId, idx) in sop.removeTagIds" :key="`show_${tagId}`" :tag-id="tagId">
            <template #default="{ tagName }">
              <span>【{{ tagName }}】</span>
            </template>
          </customer-tag>
        </div>
        <div class="mt-15px flex items-center overflow-hidden">
          <el-date-picker v-model="dates" class="" type="date" value-format="YYYY-MM-DD" @change="change($event)" />
          <el-button class="ml-10px" @click="reset">重置</el-button>
        </div>
      </div>
    </layout-item>
    <Layout-main :scroll="false">
      <el-table height="100%" :data="newList" @sort-change="handleSort($event)">
        <el-table-column prop="customerName" width="160" label="目标客户" />
        <el-table-column prop="time" width="120" label="打标签日期" />
        <el-table-column prop="tags" width="160" label="客户当前标签">
          <template #default="{ row: { tagIds } }">
            <div class="flex flex-wrap">
              <customer-tag custom v-for="(tagId, idx) in tagIds" :key="`show_${tagId}`" :tag-id="tagId">
                <template #default="{ tagName }">
                  <div class="mb-4px max-w-full h-20px text-12px leading-20px px-10px rounded truncate text-white bg-[#FC8352]" :class="idx > 0 ? 'ml-4px' : ''">
                    {{ tagName }}
                  </div>
                </template>
              </customer-tag>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </Layout-main>
    <Layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize" @handle-current-change="changePage" />
    </Layout-item>
  </my-layout>
</template>
<script setup>
import { computed, onMounted, ref, toRefs } from "vue";
import useElPagination from "@/hooks/useElPagination";
import pagination from "@/components/pagination/pagination.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import customerTag from "@/components/customer-tag/customer-tag";
import { getSopTaskResultForTag } from "@/api/member";
import { dayjs } from "element-plus";
const teamName = ref("");

const props = defineProps({
  sop: { type: Object, default: () => ({}) },
});
const total = ref(0);
const { sop } = toRefs(props);
const { page, pageSize, changePage, changeSize } = useElPagination(getList);
const dates = ref([]);
const list = ref([]);
function change() {
  getList();
}
const newList = computed(() => {
  if (teamName.value) {
    return list.value.filter((i) => i.executeTeamNames.includes(teamName.value));
  } else return list.value;
});
onMounted(async () => {
  getList();
});
async function getList() {
  let params = {
    sopTaskId: sop.value._id,
    dates: dates.value,
    page: page.value,
    pageSize: pageSize.value,
  };
  const { data, success } = await getSopTaskResultForTag(params);
  if (!success) return;
  total.value = data.total;
  list.value = data.data.map((i) => {
    const customer = i.customer[0];
    return {
      tagIds: customer.tagIds,
      customerName: customer.name,
      time: dayjs(i.createTime).format("YYYY-MM-DD"),
    };
  });
  console.log(list);
}
</script>
<style lang="scss" scoped>
.text-primary {
  color: var(--el-color-primary);
}
</style>
