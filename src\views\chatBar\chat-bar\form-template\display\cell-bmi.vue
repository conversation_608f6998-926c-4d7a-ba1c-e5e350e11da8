<template>
  <div v-if="BMI" :class="standard[stage]">
    <span v-if="stage === 'thin'">过轻</span>
    <span v-else-if="stage === 'normal'">正常</span>
    <span v-else-if="stage === 'overweight'">超重</span>
    <span v-else-if="stage === 'fat'">肥胖</span>
    <span>（{{ BMI }}）</span>
  </div>
  <div v-else-if="showPlaceholder" class="text-gray-400">
    输入身高体重后系统会自动计算
  </div>
</template>
<script setup>
import { computed, toRefs } from "vue";

const standard = {
  'thin': 'text-blue-400',
  'normal': 'text-green-400',
  'overweight': 'text-yellow-400',
  'fat': 'text-red-400',
}


const props = defineProps({
  form: {
    type: Object,
    default: {}
  },
  showPlaceholder: { default: false }
})
const { form } = toRefs(props);

const height = computed(() => getNumber(form.value.height));
const weight = computed(() => getNumber(form.value.weight));
const BMI = computed(() => {
  if (height.value > 0 && weight.value > 0) return (weight.value / Math.pow(height.value / 100, 2)).toFixed(1);
  return ''
})
const stage = computed(() => {
  if (BMI.value > 0 && BMI.value < 18.5) return 'thin';
  else if (BMI.value >= 18.5 && BMI.value <= 24) return 'normal';
  else if (BMI.value > 24 && BMI.value < 28) return 'overweight';
  else if (BMI.value >= 28) return 'fat';
  return ''
})

function getNumber(data) {
  if (typeof data === 'number') return data;
  if (typeof data === 'string' && Number(data).toString() === data) return Number(data);
}

</script>