<template>
  <div class="flex-shrink-0 mx-5px text-gray-500">包含任意</div>
  <el-select multiple collapse-tags v-model="names" filterable clearable remote allow-create default-first-option
    reserve-keyword placeholder="请填写" :remote-method="findDisease" :loading="loading" :remote-show-suffix="true">
    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
  </el-select>
</template>
<script setup>
import { computed, ref } from 'vue'
import { useVModel, watchDebounced } from '@vueuse/core';

const emit = defineEmits(['update:modelValue'])
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})
const data = useVModel(props, 'modelValue', emit);
const names = computed({
  get() { return Array.isArray(data.value) ? data.value : [] },
  set(val) { data.value = val }
})
const loading = ref(false);
const diseaseList = ref([]);
const searchText = ref("");
const options = computed(() => {
  const prefix = names.value.map(item => ({ value: item, label: item }));
  const next = diseaseList.value.filter(item => !prefix.some(i => i.value === item.value));
  return [...next, ...prefix];
})

function findDisease(val) {
  if (val.trim()) {
    searchText.value = val.trim();
  }
}
async function getDieaseList(diseaseName) {
  diseaseList.value = [{ value: diseaseName, label: diseaseName }];
}
watchDebounced(searchText, async () => {
  if (searchText.value) await getDieaseList(searchText.value);
}, { debounce: 1000 });
</script>
