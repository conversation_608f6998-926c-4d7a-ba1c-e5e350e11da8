<template>
  <el-dialog v-model="dialogVisible" title="修改信息来源" width="400px" :close-on-click-modal="false" destroy-on-close>
    <div class="p-4">
      <div class="mb-4">请选择信息来源：</div>
      <remote-select-customer-source v-model="selectedSource" placeholder="请选择信息来源" />
    </div>
    <template #footer>
      <div class="flex justify-end">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="submit" :loading="submitting">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue';
import RemoteSelectCustomerSource from "@/components/benefit-management/remote-select-customer-source.vue";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  currentSource: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:visible', 'submit']);

const dialogVisible = ref(false);
const selectedSource = ref([]);
const submitting = ref(false);

watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val;
    if (val) {
      selectedSource.value = [...props.currentSource];
    }
  }
);

watch(
  () => dialogVisible.value,
  (val) => {
    if (!val) {
      emit('update:visible', false);
    }
  }
);

function closeDialog() {
  dialogVisible.value = false;
}

function submit() {
  submitting.value = true;
  
  emit('submit', {
    source: selectedSource.value
  });
  
  // 重置组件状态 (实际提交操作由父组件处理)
  submitting.value = false;
}
</script>
