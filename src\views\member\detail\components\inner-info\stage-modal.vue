<template>
  <el-dialog :model-value="props.visible" :width="520" title="选择客户阶段" @close="close">
    <div p-15 flex items-center>
      <el-form class="w-full">
        <el-form-item class="is-required" label="客户阶段">
          <el-select v-model="value" class="w-full" placeholder="请选择客户阶段">
            <el-option v-for="opt in stageList" :key="opt.value" :label="opt.label" :value="opt.value" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div text-center>
        <el-button class="w-100px" plain @click="close()">取消</el-button>
        <el-button class="w-100px" type="primary" @click="save()">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ElMessage } from 'element-plus';
import { ref, watch } from 'vue';

const props = defineProps({
  stageList: { type: Array, default: () => [] },
  value: { type: String, default: '' },
  visible: { type: Boolean, default: false }
})

const value = ref('');
const emits = defineEmits(['close', 'change'])
function close() {
  emits('close')
}
function save() {
  if (value.value) {
    emits('change', value.value)
  } else {
    ElMessage.info('请选择客户阶段')
  }
}

watch(() => props.visible, n => {
  if (n) value.value = props.value;
})
</script>