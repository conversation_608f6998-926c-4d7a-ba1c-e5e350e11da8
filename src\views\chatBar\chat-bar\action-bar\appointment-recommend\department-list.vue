<template>
  <my-layout bg-fff v-loading="loading">
    <!-- 标题
    <layout-item>
      <div class="px-15px pt-20px pb-10px font-semibold" font-16 border-bottom>科室列表</div>
    </layout-item> -->
    
    <!-- 搜索 -->
    <layout-item>
      <div class="py-12px px-15px" border-bottom>
        <el-input 
          v-model="searchKeyword" 
          placeholder="请输入医生" 
          :suffix-icon="Search" 
          @input="handleSearch" 
          @click="handleSearchClick"
          @keyup.enter="handleSearch"
        />
      </div>
    </layout-item>
    
    <!-- 院区选择 -->
    <layout-item>
      <div class="px-15px py-10px" border-bottom>
        <div class="area-selector">
          <div class="grid grid-cols-4 gap-8px area-grid">
            <!-- 前7个院区 -->
            <button
              v-for="(area, idx) in hospitalAreas.slice(0, 7)"
              :key="area.key"
              @click="selectArea(area.key)"
              class="area-button px-8px py-6px rounded transition-colors"
              :class="selectedArea === area.key ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
            >
              {{ area.name }}
            </button>
            <!-- 第二行第4个：el-popover展开按钮 -->
            <el-popover
              v-if="hospitalAreas.length > 7"
              placement="bottom"
              :width="'99%'"
              trigger="click"
              v-model:visible="showAreaPopup"
              popper-class="area-popover-popper"
            >
              <template #reference>
                <button
                  class="area-button px-8px py-6px rounded transition-colors flex items-center justify-center"
                  :class="showAreaPopup ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
                  style="width:100%"
                >
                  <span>展开</span>
                  <span class="area-arrow ml-4px" :class="{ 'area-arrow-expanded': showAreaPopup }">⯆</span>
                </button>
              </template>
              <div style="max-width: 100%; min-width: 320px; padding: 0;">
                <div class="grid grid-cols-4 gap-8px p-0" style="width: 100%;">
                  <button
                    v-for="area in hospitalAreas.slice(7)"
                    :key="area.key"
                    @click="selectAreaFromPopup(area.key)"
                    class="area-button px-8px py-6px rounded transition-colors"
                    :class="selectedArea === area.key ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
                    style="width: 100%"
                  >
                    {{ area.name }}
                  </button>
                </div>
              </div>
            </el-popover>
          </div>
        </div>
      </div>
    </layout-item>

    <!-- 科室列表 -->
    <layout-main :scroll="false">
      <div v-if="primaryDepts.length" h-full flex>
        <!-- 左侧：一级科室 -->
        <div flex-shrink-0 h-full class="w-120px left-panel">
          <el-scrollbar class="h-full">
            <div class="left-content">
              <div 
                v-for="primaryDept in primaryDepts" 
                :key="primaryDept.id"
                class="px-15px py-12px primary-dept-item cursor-pointer hover:bg-gray-50 transition-colors"
                :class="selectedPrimaryDept?.id === primaryDept.id ? 'bg-blue-50 text-blue-600' : ''"
                @click="selectPrimaryDept(primaryDept)"
              >
                <div class="text-sm font-medium">{{ primaryDept.name }}</div>
              </div>
            </div>
          </el-scrollbar>
        </div>
        
        <!-- 右侧：二级/三级科室 -->
        <div flex-grow h-full class="flex-shrink right-panel">
          <el-scrollbar h-full ref="scrollRef">
            <div v-if="selectedPrimaryDept" class="right-content">
              <!-- 二级科室列表 -->
              <div v-for="secondaryDept in filteredSecondaryDepts" :key="secondaryDept.id" class="secondary-dept-item">
                <!-- 二级科室标题 -->
                <div 
                  class="px-15px py-12px cursor-pointer hover:bg-gray-50 transition-colors flex items-center justify-between right-item"
                  @click="handleSecondaryDeptClick(secondaryDept)"
                >
                  <div class="flex items-center">
                    <span 
                      v-if="secondaryDept.children && secondaryDept.children.length"
                      class="secondary-arrow mr-8px"
                    >
                      {{ secondaryDept.expanded ? '⯆' : '⯈' }}
                    </span>
                    <div class="text-sm font-semibold">{{ secondaryDept.name }}</div>
                  </div>
                  <!-- 如果没有三级科室，显示进入按钮 -->
                  <div v-if="!secondaryDept.children || secondaryDept.children.length === 0" class="flex-shrink-0 text-blue-600">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                
                <!-- 三级科室列表（可展开） -->
                <div v-if="secondaryDept.expanded && secondaryDept.children && secondaryDept.children.length" class="bg-gray-50">
                  <div 
                    v-for="tertiaryDept in secondaryDept.children"
                    :key="tertiaryDept.id"
                    class="px-30px py-12px tertiary-dept-item cursor-pointer hover:bg-blue-50 transition-colors flex items-center justify-between right-item"
                    @click="selectTertiaryDept(tertiaryDept)"
                  >
                    <div class="flex-grow">
                      <div class="flex items-center">
                        <span class="l-shape mr-8px"></span>
                        <div class="text-sm">{{ tertiaryDept.name }}</div>
                      </div>
                    </div>
                    <div class="flex-shrink-0 text-blue-600">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 没有子科室时的提示 -->
              <div v-if="filteredSecondaryDepts.length === 0" class="px-15px py-20px text-center text-gray-500">
                <div class="text-sm">该科室暂无子科室</div>
              </div>
            </div>
            
            <!-- 未选择一级科室时的提示 -->
            <div v-else class="px-15px py-20px text-center text-gray-500">
              <div class="text-sm">请选择左侧一级科室查看详情</div>
            </div>
          </el-scrollbar>
        </div>
      </div>
      
      <!-- 无数据状态 -->
      <div v-else h-full w-full flex items-center justify-center>
        <empty-data :top="0" title="暂无科室信息"></empty-data>
      </div>
    </layout-main>
    
    <!-- 底部取消按钮 -->
    <layout-item>
      <div class="py-12px px-15px" common-shadow--r>
        <el-button class="block w-full" @click="cancel()">取消</el-button>
      </div>
    </layout-item>
  </my-layout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { Search } from '@element-plus/icons-vue'
import MyLayout from '@/components/layout'
import LayoutItem from '@/components/layout/item.vue'
import LayoutMain from '@/components/layout/main.vue'
import EmptyData from '@/components/empty-data.vue'
import { getAppointmentRecommendList } from '@/api/internet-dept-manage'
import { ElMessage } from 'element-plus'

const props = defineProps({
  customer: { type: Object, default: () => ({}) },
  teams: { type: Array, default: () => [] },
  unionId: { type: String, default: '' }
})

const emits = defineEmits(['close', 'search', 'selectTertiary'])

const loading = ref(false)
const searchKeyword = ref('')
const selectedArea = ref(null)
const selectedPrimaryDept = ref(null)
const areaExpanded = ref(false)
const showAreaPopup = ref(false)

// 院区数据
const hospitalAreas = ref([])
// 科室数据
const departmentData = ref([])
// 原始数据
const originalData = ref(null)
// 处理后的科室数据
const processedDepartmentData = ref(null)

// 一级科室列表（根据院区筛选）
const primaryDepts = computed(() => {
  if (!selectedArea.value || !processedDepartmentData.value) return []
  
  const areaData = processedDepartmentData.value.find(area => area.id === selectedArea.value)
  return areaData ? areaData.children : []
})

// 二级科室列表（当前选中的一级科室的子科室）
const filteredSecondaryDepts = computed(() => {
  if (!selectedPrimaryDept.value) return []
  return selectedPrimaryDept.value.children || []
})

onMounted(() => {
  loadDepartments()
})

async function loadDepartments() {
  loading.value = true
  try {
    const result = await getAppointmentRecommendList()
    if (result.success) {
      originalData.value = result.data
      
      // 设置院区数据
      hospitalAreas.value = result.data.areas.map(area => ({
        key: area.id,
        name: area.name,
        description: area.description
      }))
      console.log('原始数据:', result.data.departments)
      
      // 直接使用API返回的数据结构
      // API中只为一级科室创建虚拟二级科室，二级、三级科室作为末级科室时不创建虚拟科室
      processedDepartmentData.value = result.data.departments.map(area => {
        return {
          ...area,
          children: area.children ? area.children.map(primaryDept => {
            return {
              ...primaryDept,
              children: primaryDept.children ? primaryDept.children.map(secondaryDept => {
                return {
                  ...secondaryDept,
                  expanded: false // 默认不展开
                }
              }) : []
            }
          }) : []
        }
      })
          console.log('处理后的数据:', processedDepartmentData.value)
      // 默认选择第一个院区
      if (hospitalAreas.value.length > 0) {
        selectedArea.value = hospitalAreas.value[0].key
        
        // 选择第一个一级科室
        if (primaryDepts.value.length > 0) {
          selectedPrimaryDept.value = primaryDepts.value[0]
        }
      }
    } else {
      ElMessage.error(result.message || '加载科室数据失败')
    }
  } catch (error) {
    ElMessage.error('加载科室数据失败')
    console.error('加载科室数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 选择院区
function selectArea(areaId) {
  selectedArea.value = areaId
  selectedPrimaryDept.value = null
  
  // 切换院区时重新选择第一个一级科室
  if (primaryDepts.value.length > 0) {
    selectedPrimaryDept.value = primaryDepts.value[0]
  }
}

// 选择一级科室
function selectPrimaryDept(dept) {
  selectedPrimaryDept.value = dept
}

// 处理二级科室点击
function handleSecondaryDeptClick(dept) {
  // 如果有三级科室，展开/折叠
  if (dept.children && dept.children.length > 0) {
    dept.expanded = !dept.expanded
  } else {
    // 如果没有三级科室，直接使用二级科室进入医生列表
    // 二级、三级科室作为末级科室时，不需要创建虚拟科室
    const deptForDoctorList = {
      id: dept.id,
      name: dept.name,
      type: 'department',
      level: dept.level || 2, // 保持原有层级
      parentId: dept.parentId,
      deptId: dept.deptId || dept.hlwDeptId || dept.id,
      hlwDeptId: dept.hlwDeptId || dept.deptId,
      description: dept.description,
      isVirtual: dept.isVirtual || false, // 保持原有的虚拟科室标记
      realDeptId: dept.realDeptId // 如果是虚拟科室，保留真实科室ID
    }
    selectTertiaryDept(deptForDoctorList)
  }
}

// 选择三级科室（进入医生列表）
function selectTertiaryDept(dept) {
  emits('selectTertiary', dept)
}

// 搜索处理（点击搜索框时直接进入搜索页面）
function handleSearchClick() {
  emits('search', searchKeyword.value || '')
}

// 搜索输入处理
function handleSearch() {
  if (searchKeyword.value.trim()) {
    emits('search', searchKeyword.value)
  }
}

// 取消
function cancel() {
  emits('close')
}

// 从弹出层选择院区
function selectAreaFromPopup(areaId) {
  selectArea(areaId)
  showAreaPopup.value = false
}
</script>

<style scoped>
/* 确保页面在最顶层 */
.appointment-recommend-page {
  z-index: 9999;
}

/* 院区选择样式 */
.area-selector {
  position: relative;
}

.area-grid {
  max-height: 88px; /* 增加高度以适应两行显示 */
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.area-grid-expanded {
  max-height: none;
}

/* 院区按钮样式 */
.area-button {
  font-size: 13px; /* 减小字体大小 */
  line-height: 1.2;
  min-height: 36px; /* 设置最小高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  word-break: break-all; /* 允许单词内换行 */
  white-space: normal; /* 允许换行 */
}

/* 在小屏幕下进一步优化 */
@media (max-width: 400px) {
  .area-button {
    font-size: 12px; /* 更小的字体 */
    padding: 4px 6px; /* 减小内边距 */
    min-height: 32px; /* 减小最小高度 */
  }

  .area-grid {
    max-height: 76px; /* 调整小屏幕下的最大高度 */
    gap: 6px; /* 减小间距 */
  }
}

.area-arrow {
  font-size: 12px;
  transition: transform 0.2s ease;
  display: inline-block;
}

.area-arrow-expanded {
  transform: rotate(180deg);
}

/* 左侧面板样式 */
.left-panel {
  border-right: 1px solid #e5e7eb;
  background: #fff;
}

/* 右侧面板样式 */
.right-panel {
  background: #fff;
}

/* 左侧内容容器 */
.left-content {
  min-height: 100%;
  display: flex;
  flex-direction: column;
}

/* 右侧内容容器 */
.right-content {
  min-height: 100%;
  display: flex;
  flex-direction: column;
}

/* 统一的分割线样式 */
.primary-dept-item,
.right-item {
  border-bottom: 1px solid #e5e7eb;
}

/* 移除重复的分割线样式定义 */

/* 二级科室箭头样式 */
.secondary-arrow {
  font-size: 14px;
  color: #6b7280;
  font-weight: bold;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

/* L形状装饰 */
.l-shape {
  width: 12px;
  height: 12px;
  border-left: 2px solid #9ca3af;
  border-bottom: 2px solid #9ca3af;
  border-bottom-left-radius: 2px;
  position: relative;
  display: inline-block;
}

.l-shape::before {
  content: '';
  position: absolute;
  top: -6px;
  left: -2px;
  width: 2px;
  height: 6px;
  background: #9ca3af;
}

/* 滚动条样式 */
.overflow-y-auto::-webkit-scrollbar,
.el-scrollbar :deep(.el-scrollbar__bar) {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track,
.el-scrollbar :deep(.el-scrollbar__track) {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb,
.el-scrollbar :deep(.el-scrollbar__thumb) {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover,
.el-scrollbar :deep(.el-scrollbar__thumb:hover) {
  background: #a8a8a8;
}

/* 旋转动画 */
.rotate-90 {
  transform: rotate(90deg);
}

.transition-transform {
  transition: transform 0.2s ease-in-out;
}

/* 一级科室选中状态 */
.bg-blue-50 {
  background-color: #eff6ff;
}

.text-blue-600 {
  color: #2563eb;
}

/* 弹出层样式 */
.area-popover-popper {
  z-index: 10000; /* 确保在其他弹出层之上 */
}
</style>
