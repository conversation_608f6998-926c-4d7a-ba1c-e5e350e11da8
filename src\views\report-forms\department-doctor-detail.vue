<template>
  <div class="report-container">
    <div class="report-header">
      <div class="report-actions">
        <div class="filter-section">
          <div class="filter-row">
            <!-- 执行日期筛选 -->
            <div class="filter-item">
              <span class="filter-label">日期：</span>
              <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :shortcuts="dateShortcuts" :disabled-date="disabledDate" @change="handleDateChange" class="date-range" />
            </div>
            <!-- 执行医生筛选 -->
            <div class="filter-item">
              <check-box-filter v-model="doctorFilter" label="医生" :list="doctorList">
                <template #item="{ item }">
                  <ww-user :openid="item.value"></ww-user>
                </template>
              </check-box-filter>
            </div>
            <!-- 配台医生筛选 -->
            <div class="filter-item">
              <check-box-filter v-model="assistantDoctorFilter" label="配台" :list="assistantDoctorList">
                <template #item="{ item }">
                  <ww-user :openid="item.value"></ww-user>
                </template>
              </check-box-filter>
            </div>
            <!-- 执行科室筛选 -->
            <div class="filter-item">
              <base-filter-item :clearable="deptFilter.length > 0" class="cursor-pointer" label="治疗科室" :text="deptText" @clear="clearDepts" @onClick="showDeptPicker()" />
            </div>
            <!-- 客户ID筛选 -->
            <div class="filter-item">
              <span class="filter-label">客户ID：</span>
              <el-input v-model="clientIdFilter" placeholder="客户ID" clearable />
            </div>
            <!-- 客户姓名筛选 -->
            <div class="filter-item">
              <span class="filter-label">客户姓名：</span>
              <el-input v-model="clientNameFilter" placeholder="客户姓名" clearable />
            </div>
            <!-- 项目分类筛选 -->
            <div class="filter-item">
              <base-filter-item :clearable="projectCategories.length > 0" class="cursor-pointer" label="项目分类" :text="categoryText" @clear="clearCategories" @onClick="showCategoryPicker()" />
            </div>
            <!-- 咨询项目（原执行项目）筛选 -->
            <div class="filter-item">
              <base-filter-item :clearable="projectFilter.length > 0" class="cursor-pointer" label="治疗项目" :text="projectText" @clear="clearProjects" @onClick="showProjectModal()" />
            </div>
            <!-- 咨询师筛选 -->
            <div class="filter-item">
              <check-box-filter v-model="consultantFilter" label="咨询师" :list="consultantList">
                <template #item="{ item }">
                  <ww-user :openid="item.value"></ww-user>
                </template>
              </check-box-filter>
            </div>
            <!-- 开发渠道筛选 -->
            <div class="filter-item">
              <filter-customer-source v-model="devPathFilter" label="开发渠道" />
            </div>
            <!-- 查询/导出/重置按钮 -->
            <div class="filter-actions">
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button type="success" @click="exportToExcel">导出Excel</el-button>
              <el-button @click="resetFilters">重置</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="table-wrapper">
      <div class="table-container">
        <table class="medical-table">
          <colgroup>
            <col style="width: 100px" />
            <!-- 医生 -->
            <col style="width: 90px" />
            <!-- 配台 -->
            <col style="width: 90px" />
            <!-- 治疗科室 -->
            <col style="width: 100px" />
            <!-- 客户门诊ID -->
            <col style="width: 100px" />
            <!-- 客户姓名 -->
            <col style="width: 90px" />
            <!-- 项目类别1 -->
            <col style="width: 90px" />
            <!-- 项目类别2 -->
            <col style="width: 90px" />
            <!-- 项目类别3 -->
            <col style="width: 90px" />
            <!-- 项目类别4 -->
            <col style="width: 120px" />
            <!-- 项目名称 -->
            <col style="width: 120px" />
            <!-- 套餐名称 -->
            <col style="width: 100px" />
            <!-- 本次消费次数 -->
            <col style="width: 100px" />
            <!-- 单次应收金额 -->
            <col style="width: 100px" />
            <!-- 本次划扣业绩 -->
            <col style="width: 100px" />
            <!-- 治疗时间 -->
            <col style="width: 100px" />
            <!-- 咨询师 -->
            <col style="width: 90px" />
            <!-- 开发渠道1 -->
            <col style="width: 90px" />
            <!-- 开发渠道2 -->
            <col style="width: 90px" />
            <!-- 开发渠道3 -->
          </colgroup>
          <thead>
            <tr>
              <th>医生</th>
              <th>配台</th>
              <th>治疗科室</th>
              <th>客户门诊ID</th>
              <th>客户姓名</th>
              <th>项目类别1</th>
              <th>项目类别2</th>
              <th>项目类别3</th>
              <th>项目类别4</th>
              <th>项目名称</th>
              <th>套餐名称</th>
              <th>本次消费次数</th>
              <th>单次应收金额</th>
              <th>本次划扣业绩</th>
              <th>治疗时间</th>
              <th>咨询师</th>
              <th>开发渠道1</th>
              <th>开发渠道2</th>
              <th>开发渠道3</th>
            </tr>
          </thead>
          <tbody>
            <template v-for="(group, groupIndex) in groupedData" :key="groupIndex">
              <template v-for="(item, itemIndex) in group.items" :key="itemIndex">
                <tr>
                  <td>{{ item.doctorName }}</td>
                  <td>{{ item.assistantDoctors }}</td>
                  <td v-if="itemIndex === 0" :rowspan="group.items.length">{{ group.departmentName }}</td>
                  <td>{{ item.clientId }}</td>
                  <td>{{ item.clientName }}</td>
                  <td>{{ item.category1 }}</td>
                  <td>{{ item.category2 }}</td>
                  <td>{{ item.category3 }}</td>
                  <td>{{ item.category4 }}</td>
                  <td>{{ item.itemName }}</td>
                  <td>{{ item.packageName || "-" }}</td>
                  <td>{{ item.quantity }}</td>
                  <td class="amount">{{ formatCurrency(item.unitPrice) }}</td>
                  <td class="amount">{{ formatCurrency(item.deductedAmount) }}</td>
                  <td>{{ item.treatmentDate }}</td>
                  <td>{{ item.consultantName }}</td>
                  <td>{{ item.source1 || "-" }}</td>
                  <td>{{ item.source2 || "-" }}</td>
                  <td>{{ item.source3 || "-" }}</td>
                </tr>
              </template>
            </template>
          </tbody>
        </table>
      </div>
    </div>
    <div class="footer-info">
      <span>共 {{ totalItems }} 条记录</span>
      <span class="date-limit-info">* 查询限制为一个月内的数据</span>
    </div>
  </div>
  <project-list-picker :value="projectFilter" :visible="projectModalVisible" :width="projectModalWidth" :current-category="projectCategories" @close="closeProjectModal()" @change="changeProjects" />
  <dept-picker-modal :onlyStaffDepts="!isManager" ref="deptPickerRef" :value="deptIds" :visible="deptVisible" :width="deptPickerWidth" @change="deptChange" @close="closeDeptPicker" />
  <category-picker ref="categoryPickerRef" :value="projectCategories" :visible="categoryPickerVisible" :width="categoryPickerWidth" @close="closeCategoryPicker" @change="changeCategories" @categoryChange="handleCategoryChange" />
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { ElDatePicker, ElSelect, ElOption, ElButton, ElMessage, ElInput } from "element-plus";
import { getDoctorDeductRecords } from "@/api/consult";
import { ProjectType } from "@/baseData";
import useModal from "@/hooks/useModal";
import { CheckBoxFilter, baseFilterItem, filterCustomerSource } from "@/components/filter-bar";
import { storeToRefs } from "pinia";
import projectListPicker from "@/components/project-picker/project-list-picker.vue";
import deptPickerModal from "@/components/dept-picker-modal/dept-picker-modal.vue";
import categoryPicker from "@/components/project-picker/category-picker.vue";
import { staffStore } from "@/store/staff";
import { teamStore } from "@/store/team";
import { memberStore } from "@/store/member";

const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());
const { currentTeamId, managerList } = storeToRefs(teamStore());
const { isAdmin } = storeToRefs(memberStore());
const { judgmentIsAdmin } = memberStore();
const belongDeptIds = ref([]);

const isManager = computed(() => {
  return managerList.value.includes(90012) || isAdmin.value;
});

// 日期筛选
const dateRange = ref([]);
const clientIdFilter = ref("");
const clientNameFilter = ref("");
const phoneFilter = ref("");
const itemTypeFilter = ref("");
const deductStatusFilter = ref("");
const projectCategories = ref([]);
const doctorFilter = ref([]);
const categoryPickerRef = ref(null);
// 项目筛选相关变量
const projectFilter = ref([]);
const currentCategory = ref({});
const { close: closeProjectModal, show: showProjectModal, visible: projectModalVisible, width: projectModalWidth } = useModal(800);
const { close: closeDeptPicker, show: showDeptPicker, visible: deptVisible, width: deptPickerWidth } = useModal(640);
const { close: closeCategoryPicker, show: showCategoryPicker, visible: categoryPickerVisible, width: categoryPickerWidth } = useModal(600);

// 新增咨询师筛选变量
const consultantFilter = ref([]);

// 新增配台医生筛选变量
const assistantDoctorFilter = ref([]);

// 配台医生列表
const assistantDoctorList = computed(() => {
  const allDoctors = staffList.value.filter((i) => {
    const roles = ["doctor", "nurse", "therapist", "assistant", "expertDoctor"];
    return Array.isArray(i.job) && i.job.some((job) => roles.includes(job));
  });
  return allDoctors.map((i) => ({ value: i.userid }));
});

// 开发渠道筛选变量
const devPathFilter = ref([]);

const projectText = computed(() => {
  if (projectFilter.value.length === 0) return "";
  if (projectFilter.value.length > 1) return `已选择${projectFilter.value.length}个项目`;
  return projectFilter.value[0].projectName;
});

const categoryText = computed(() => {
  if (projectCategories.value.length === 0) return "";
  if (projectCategories.value.length > 1) return `已选择${projectCategories.value.length}个分类`;
  return projectCategories.value[0].name;
});

function clearProjects() {
  projectFilter.value = [];
}

function clearCategories() {
  projectCategories.value = [];
  categoryPickerRef.value.clear();
}

function changeCategories(data) {
  projectCategories.value = data.categories;
  projectFilter.value = [];
}

function handleCategoryChange(category) {
  currentCategory.value = category;
  projectFilter.value = [];
}

function changeProjects(data) {
  projectFilter.value = data.projects;
  closeProjectModal();
}

// 处理手机号输入，限制只能输入数字
const handlePhoneFilterInput = (val) => {
  phoneFilter.value = val.replace(/[^\d]/g, "");
};
// 医生列表计算属性

const doctorList = computed(() => {
  const allDoctors = staffList.value.filter((i) => {
    const roles = ["doctor", "expertDoctor"];
    return Array.isArray(i.job) && i.job.some((job) => roles.includes(job));
  });
  return allDoctors.map((i) => ({ value: i.userid }));
});

// 咨询师列表计算属性
const consultantList = computed(() => {
  const allConsultants = staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("consultant"));
  return allConsultants.map((i) => ({ value: i.userid }));
});

// 监听团队ID变化
watch(
  () => currentTeamId.value,
  () => {
    if (dateRange.value && dateRange.value.length === 2) {
      handleGetTreatmentRecords();
    }
  }
);

/* ===============  科室筛选  start ================= */
const deptFilter = ref([]);
const deptIds = computed(() => deptFilter.value.map((i) => i._id));
const deptText = computed(() => {
  if (deptFilter.value.length === 0) return "";
  if (deptFilter.value.length > 1) return `已选择${deptFilter.value.length}个科室`;
  return deptFilter.value[0].deptName;
});
function deptChange(val) {
  deptFilter.value = val;
  closeDeptPicker();
}
function clearDepts() {
  deptFilter.value = [];
}

// 日期快捷选项
const dateShortcuts = [
  {
    text: "今天",
    value: () => {
      const today = new Date();
      return [today, today];
    },
  },
  {
    text: "昨天",
    value: () => {
      const today = new Date();
      const yesterday = new Date();
      yesterday.setDate(today.getDate() - 1);
      return [yesterday, yesterday];
    },
  },
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近半个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
];

// 原始数据
const rawData = ref([]);

// 获取治疗记录
async function handleGetTreatmentRecords() {
  // 构建请求参数
  const params = {
    startDate: dateRange.value?.[0] || formatDate(oneMonthAgo),
    endDate: dateRange.value?.[1] || formatDate(today),
  };

  // 医生筛选逻辑
  params.doctorFilter = doctorFilter.value.length > 0 ? doctorFilter.value : undefined;

  // 配台医生筛选
  if (assistantDoctorFilter.value && assistantDoctorFilter.value.length > 0) {
    params.assistantDoctorFilter = assistantDoctorFilter.value;
  }

  // 咨询师筛选
  if (consultantFilter.value && consultantFilter.value.length > 0) {
    params.consultantFilter = consultantFilter.value;
  }

  // 科室筛选
  if (isManager.value && deptFilter.value && deptFilter.value.length > 0) {
    params.deptFilter = deptFilter.value.map((dept) => dept._id);
  } else {
    params.deptFilter = belongDeptIds.value;
  }

  // 执行项目筛选
  if (projectFilter.value && projectFilter.value.length > 0) {
    params.projectIds = projectFilter.value.map((project) => project._id);
  }

  // 开发渠道筛选
  if (devPathFilter.value && devPathFilter.value.length > 0) {
    params.devPaths = devPathFilter.value;
  }

  let { data, success, message } = await getDoctorDeductRecords(params);
  if (success) {
    rawData.value = transformTreatmentRecords(data.data);
  } else {
    ElMessage.error(message);
  }
}

// 转换治疗记录数据为表格需要的格式
function transformTreatmentRecords(records) {
  if (!records || !Array.isArray(records)) {
    return [];
  }

  return records.map((record) => {
    // 获取执行科室名称
    const treatmentDeptName = record.treatmentDeptName || "-";

    // 获取执行医生信息
    const treatmentDoctorId = record.treatmentDoctorUserId || "-";
    const treatmentDoctorName = staffList.value.find((staff) => staff.userid === treatmentDoctorId)?.anotherName || treatmentDoctorId;

    // 获取咨询师信息
    const counselorId = record.counselorUserId || "-";
    const counselorName = staffList.value.find((staff) => staff.userid === counselorId)?.anotherName || counselorId;

    // 获取助手医生信息
    let assistantDoctors = "-";
    if (record.assistantDoctors && record.assistantDoctors.length > 0) {
      assistantDoctors = record.assistantDoctors.map((id) => staffList.value.find((staff) => staff.userid === id)?.anotherName || id).join(", ");
    }

    // 获取项目类别（根据 level 分别获取 1-4 级类别的 label）
    let category1 = "-";
    let category2 = "-";
    let category3 = "-";
    let category4 = "-";
    // 如果存在项目类别数据
    if (record.projectCates && Array.isArray(record.projectCates)) {
      // 根据 level 找到对应级别的类别标签
      const cat1 = record.projectCates.find((cat) => cat.level === 1);
      const cat2 = record.projectCates.find((cat) => cat.level === 2);
      const cat3 = record.projectCates.find((cat) => cat.level === 3);
      const cat4 = record.projectCates.find((cat) => cat.level === 4);
      category1 = cat1 ? cat1.label : "-";
      category2 = cat2 ? cat2.label : "-";
      category3 = cat3 ? cat3.label : "-";
      category4 = cat4 ? cat4.label : "-";
    }

    // 处理开发渠道
    let source1 = "-";
    let source2 = "-";
    let source3 = "-";
    if (record.customerSource && Array.isArray(record.customerSource)) {
      if (record.customerSource.length > 0) source1 = record.customerSource[0] || "-";
      if (record.customerSource.length > 1) source2 = record.customerSource[1] || "-";
      if (record.customerSource.length > 2) source3 = record.customerSource[2] || "-";
    }

    return {
      department: treatmentDeptName,
      doctorId: treatmentDoctorId,
      doctorName: treatmentDoctorName,
      clientId: record.customerNumber || "-",
      clientName: record.customerName || "-",
      sex: record.customerSex || "-",
      itemType: record.projectType || "-",
      category1,
      category2,
      category3,
      category4,
      itemName: record.projectName || "-",
      packageName: record.packageName || "-",
      quantity: record.deductUsageCount || record.usageCount || 1,
      unitPrice: record.unitPrice || record.deductedPrice || 0,
      deductedAmount: record.deductedPrice || 0,
      treatmentDate: formatDateFromTimestamp(record.treatmentTime),
      consultantId: counselorId,
      consultantName: counselorName,
      assistantDoctors: assistantDoctors,
      treatmentArea: record.treatmentArea || "-",
      deductStatus: record.deductStatus || "pending",
      deductType: record.deductType || "-",
      billType: record.billType || "-",
      treatmentId: record._id,
      clientPhone: record.customerPhone,
      treatmentRemark: record.treatmentRemark || "-",
      source1,
      source2,
      source3,
    };
  });
}

// 格式化时间戳为日期字符串 YYYY-MM-DD
function formatDateFromTimestamp(timestamp) {
  if (!timestamp) return "-";

  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");

  return `${year}-${month}-${day}`;
}

// 禁用超过一个月范围的日期
const disabledDate = (time) => {
  if (!dateRange.value || dateRange.value.length === 0) {
    return false;
  }

  const startDate = new Date(dateRange.value[0]);
  const endDate = new Date(dateRange.value[1]);
  const currentDate = new Date(time);

  // 如果已选择了开始日期，限制结束日期不能超过开始日期后的31天
  if (dateRange.value.length === 1) {
    const oneMonthLater = new Date(startDate);
    oneMonthLater.setDate(startDate.getDate() + 31);
    return currentDate > oneMonthLater;
  }

  return false;
};

// 监听日期变化，确保不超过一个月
watch(dateRange, (newValue) => {
  if (newValue && newValue.length === 2) {
    const start = new Date(newValue[0]);
    const end = new Date(newValue[1]);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays > 31) {
      // 如果超过31天，将结束日期设置为开始日期后的31天
      const maxEnd = new Date(start);
      maxEnd.setDate(start.getDate() + 31);
      const formattedMaxEnd = formatDate(maxEnd);

      dateRange.value = [newValue[0], formattedMaxEnd];
      ElMessage.warning("查询范围已限制为一个月内");
    }
  }
});

// 格式化日期为YYYY-MM-DD
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  return `${year}-${month}-${day}`;
};

// 过滤数据
const filteredData = computed(() => {
  let result = [...rawData.value];

  // 按客户ID筛选
  if (clientIdFilter.value) {
    result = result.filter((item) => item.clientId.includes(clientIdFilter.value));
  }

  // 按客户姓名筛选
  if (clientNameFilter.value) {
    result = result.filter((item) => item.clientName.includes(clientNameFilter.value));
  }

  // 按手机号筛选
  if (phoneFilter.value) {
    result = result.filter((item) => {
      if (phoneFilter.value.length === 4) {
        return item.clientPhone && item.clientPhone.endsWith(phoneFilter.value);
      } else if (phoneFilter.value.length === 11) {
        return item.clientPhone && item.clientPhone.includes(phoneFilter.value);
      }
      return false;
    });
  }

  if (projectCategories.value && projectCategories.value.length > 0) {
    let categoryObj = groupLabelsByLevel(projectCategories.value);
    result = result.filter((item) => {
      let matchesAllConditions = true;
      if (categoryObj[1] && categoryObj[1].length > 0) {
        if (!item.category1 || item.category1 === "-" || !categoryObj[1].includes(item.category1)) {
          matchesAllConditions = false;
        }
      }

      if (categoryObj[2] && categoryObj[2].length > 0) {
        if (!item.category2 || item.category2 === "-" || !categoryObj[2].includes(item.category2)) {
          matchesAllConditions = false;
        }
      }

      if (categoryObj[3] && categoryObj[3].length > 0) {
        if (!item.category3 || item.category3 === "-" || !categoryObj[3].includes(item.category3)) {
          matchesAllConditions = false;
        }
      }

      if (categoryObj[4] && categoryObj[4].length > 0) {
        if (!item.category4 || item.category4 === "-" || !categoryObj[4].includes(item.category4)) {
          matchesAllConditions = false;
        }
      }

      return matchesAllConditions;
    });
  }
  // 按咨询师筛选
  if (consultantFilter.value && consultantFilter.value.length > 0) {
    result = result.filter((item) => consultantFilter.value.includes(item.consultantId));
  }

  // 按开发渠道筛选
  if (devPathFilter.value && devPathFilter.value.length > 0) {
    result = result.filter((item) => {
      const paths = [item.source1, item.source2, item.source3].filter(Boolean);
      return paths.some((path) => devPathFilter.value.includes(path));
    });
  }

  return result;
});

function groupLabelsByLevel(data) {
  return data.reduce((acc, item) => {
    const level = item.level;
    if (!acc[level]) {
      acc[level] = [];
    }
    acc[level].push(item.label);
    return acc;
  }, {});
}

// 分组数据，按科室分组
const groupedData = computed(() => {
  const groups = {};

  // 按执行科室分组
  filteredData.value.forEach((item) => {
    if (!groups[item.department]) {
      groups[item.department] = {
        department: item.department,
        departmentName: item.department,
        items: [],
      };
    }

    // 直接添加项目到科室组
    groups[item.department].items.push(item);
  });

  // 转换为数组格式
  const result = Object.values(groups).map((group) => {
    return {
      department: group.department,
      departmentName: group.departmentName,
      items: group.items,
    };
  });

  return result;
});

// 计算总记录数
const totalItems = computed(() => {
  return filteredData.value.length;
});

// 格式化货币
const formatCurrency = (value) => {
  if (value === undefined || value === null || isNaN(value)) {
    return "¥0.00";
  }
  return new Intl.NumberFormat("zh-CN", {
    style: "currency",
    currency: "CNY",
    minimumFractionDigits: 2,
  }).format(value);
};

// 处理日期变化
const handleDateChange = (val) => {
  if (val && val.length === 2) {
    const start = new Date(val[0]);
    const end = new Date(val[1]);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays > 31) {
      ElMessage.warning("查询范围已限制为一个月内");
      return;
    }
    // 调用接口获取数据
    handleGetTreatmentRecords();
  }
};

// 处理查询
const handleSearch = () => {
  if (dateRange.value && dateRange.value.length === 2) {
    const start = new Date(dateRange.value[0]);
    const end = new Date(dateRange.value[1]);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    if (diffDays > 31) {
      ElMessage.warning("查询范围已限制为一个月内");
      return;
    }
    handleGetTreatmentRecords();
  } else {
    ElMessage.warning("请选择日期范围");
  }
};

// 重置筛选条件
const resetFilters = () => {
  // 设置为当天日期
  const today = new Date();
  const formattedToday = formatDate(today);
  dateRange.value = [formattedToday, formattedToday];

  clientIdFilter.value = "";
  clientNameFilter.value = "";
  phoneFilter.value = "";
  itemTypeFilter.value = "";
  deductStatusFilter.value = "";
  projectCategories.value = [];
  projectFilter.value = [];
  deptFilter.value = [];
  consultantFilter.value = []; // 重置咨询师筛选
  devPathFilter.value = []; // 重置开发渠道筛选
  assistantDoctorFilter.value = []; // 重置配台医生筛选

  doctorFilter.value = [];
  categoryPickerRef.value.clear();

  ElMessage.success("筛选条件已重置");
};

// 导出Excel
const exportToExcel = () => {
  // 准备导出数据
  const exportData = [];

  // 添加表头
  const headers = ["医生", "配台", "治疗科室", "客户门诊ID", "客户姓名", "项目类别1", "项目类别2", "项目类别3", "项目类别4", "项目名称", "套餐名称", "本次消费次数", "单次应收金额", "本次划扣业绩", "治疗时间", "咨询师", "开发渠道1", "开发渠道2", "开发渠道3"];

  exportData.push(headers);

  // 添加数据行
  groupedData.value.forEach((group) => {
    group.items.forEach((item) => {
      const rowData = [item.doctorName, item.assistantDoctors, group.departmentName, item.clientId, item.clientName, item.category1, item.category2, item.category3, item.category4, item.itemName, item.packageName || "-", item.quantity, item.unitPrice, item.deductedAmount, item.treatmentDate, item.consultantName, item.source1, item.source2, item.source3];

      exportData.push(rowData);
    });
  });

  // 创建工作簿和工作表
  const wb = XLSX.utils.book_new();
  const ws = XLSX.utils.aoa_to_sheet(exportData);

  // 设置列宽
  const colWidths = [
    { wch: 12 }, // 医生
    { wch: 12 }, // 配台
    { wch: 12 }, // 治疗科室
    { wch: 10 }, // 客户门诊ID
    { wch: 10 }, // 客户姓名
    { wch: 12 }, // 项目类别1
    { wch: 12 }, // 项目类别2
    { wch: 12 }, // 项目类别3
    { wch: 12 }, // 项目类别4
    { wch: 20 }, // 项目名称
    { wch: 20 }, // 套餐名称
    { wch: 10 }, // 本次消费次数
    { wch: 12 }, // 单次应收金额
    { wch: 12 }, // 本次划扣业绩
    { wch: 12 }, // 治疗时间
    { wch: 10 }, // 咨询师
    { wch: 12 }, // 开发渠道1
    { wch: 12 }, // 开发渠道2
    { wch: 12 }, // 开发渠道3
  ];

  ws["!cols"] = colWidths;

  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(wb, ws, "科室医生执行明细表");

  // 生成Excel文件并下载
  const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
  const blob = new Blob([excelBuffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });

  // 使用当前日期作为文件名的一部分
  const now = new Date();
  const fileName = `科室医生执行明细表_${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}.xlsx`;

  saveAs(blob, fileName);
  ElMessage.success("导出成功");
};

// 获取员工所在科室
function getStaffBelongDept() {
  belongDeptIds.value = memberStore().memberInfo?.deptIds || [];
}

onMounted(async () => {
  judgmentIsAdmin();
  // 初始化数据
  if (staffList.value.length === 0) await getStaffList();
  const end = new Date();
  const start = new Date(end.getFullYear(), end.getMonth(), 1);

  dateRange.value = [formatDate(start), formatDate(end)];
  getStaffBelongDept();
  handleGetTreatmentRecords();
});

watch(
  [doctorFilter, projectFilter, deptFilter, projectCategories, consultantFilter, assistantDoctorFilter, devPathFilter],
  () => {
    // 当筛选条件变化时自动触发查询
    handleGetTreatmentRecords();
  },
  { deep: true }
);
</script>
  
<style scoped>
.report-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
  background-color: #f5f7fa;
  overflow: hidden;
  min-width: 1200px;
}

.report-header {
  background-color: white;
  padding: 15px 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
}

.filter-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-label {
  white-space: nowrap;
  margin-right: 5px;
  font-size: 14px;
  color: #606266;
}

.filter-actions {
  margin-left: auto;
  display: flex;
  gap: 10px;
}

.date-range {
  min-width: 320px;
}

.table-wrapper {
  flex: 1;
  margin: 0 15px;
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 300px;
}

.table-container {
  flex: 1;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: auto;
  height: 100%;
  position: relative;
}

.medical-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  table-layout: fixed;
}

.medical-table th,
.medical-table td {
  border: 1px solid #ebeef5;
  padding: 8px 4px;
  text-align: center;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.medical-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 10;
}

.medical-table .amount {
  text-align: right;
  padding-right: 10px;
  min-width: 100px;
  max-width: 120px;
  white-space: nowrap;
  overflow: visible;
}

.subtotal-row {
  background-color: #f5f7fa;
  font-weight: bold;
}

.department-total-row {
  background-color: #e6f1fc;
  font-weight: bold;
}

.total-row {
  background-color: #ebeef5;
  font-weight: bold;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.subtotal-label,
.total-label {
  text-align: right !important;
  padding-right: 10px;
}

.footer-info {
  background-color: white;
  padding: 10px 15px;
  margin: 0 15px 10px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  color: #606266;
  font-size: 14px;
}

.date-limit-info {
  color: #e6a23c;
  font-style: italic;
  font-size: 14px;
}

@media print {
  .report-container {
    background-color: white;
  }

  .report-header {
    box-shadow: none;
  }

  .report-actions,
  .footer-info {
    display: none;
  }

  .table-container {
    margin: 0;
    box-shadow: none;
    overflow: visible;
  }

  .medical-table th,
  .medical-table td {
    font-size: 12px;
    padding: 4px 2px;
  }
}

@media (max-width: 768px) {
  .filter-section {
    gap: 15px;
  }

  .filter-row {
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
  }

  .filter-item {
    width: 100%;
  }

  .filter-actions {
    margin-left: 0;
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
