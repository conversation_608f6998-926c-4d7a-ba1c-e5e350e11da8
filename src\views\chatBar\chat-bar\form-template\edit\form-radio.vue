<template>
  <el-form-item class="px-15px py-6px el-form-item--autoheight" :class="required ? 'is-required' : ''" :label="name"
    border-bottom>
    <el-radio-group :model-value="value" @update:model-value="change($event)">
      <el-radio v-for="item in range" :label="item">{{ item }}</el-radio>
    </el-radio-group>
  </el-form-item>
</template>
<script setup>
import { computed } from 'vue';

const emits = defineEmits(['change'])
const props = defineProps({
  form: { type: Object, default: () => ({}) },
  name: { type: String, default: '' },
  range: { type: Array, default: () => [] },
  required: { type: Boolean, default: false },
  title: { type: String, default: '' },
})
const value = computed(() => props.form[props.title] || '');

function change(value) { emits('change', { title: props.title, value }) }

</script>
<style lang="scss" scoped></style>
