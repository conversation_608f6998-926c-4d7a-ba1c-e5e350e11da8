<template>
  <div>
    <el-drawer v-model="showdisease" title="新增疾病史" width="400px">
      <div class="dease-drawer">
        <div class="header">
          <div class="left">疾病史:</div>
          <el-select class="right" v-model="searchDiseases" multiple filterable allow-create default-first-option :reserve-keyword="false" placeholder="请填写疾病史" @input="debounceFunc" :remote-show-suffix="true" @change="getDiseaseAction">
            <el-option v-for="item in diseaseList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="defaultTitle">常见疾病:</div>
        <div class="defaultDiseases">
          <el-tag v-for="item in defaultDiseases" :key="item" size="large" class="pointer tag__item" :type="selections[item] ? 'primary' : 'info'" :effect="selections[item] ? 'light' : 'plain'" @click="toggle(item)">
            {{ item }}
          </el-tag>
          <!-- <el-checkbox-group v-model="checkedDisease" @change="handleCheckedDiseaseChange">
            <div class="box">
              <el-checkbox class="item" :label="item" v-for="item in defaultDiseases" />
            </div>
          </el-checkbox-group> -->
        </div>
      </div>
      <template #footer>
        <div common-shadow--r class="button_view">
          <el-button class="l w-100px" type="primary" @click="save">保存</el-button>
          <el-button class="r w-100px" @click="cancel">取消</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>
<script setup>
import { ref } from "vue";
import { debounce } from "../../../../utils/common";
import { memberStore } from "@/store/member";
let { corpInfo } = memberStore();
let defaultDiseases = corpInfo.diseases || [];
let $emit = defineEmits(["get-select-disease"]);
let showdisease = ref(false);
let diseaseList = ref(""); // 获取疾病列表
let searchDiseases = ref([]); // 搜索出的疾病名
let checkedDisease = ref([]);
const selections = ref({});
function openDialog(list = []) {
  diseaseList.value = [];
  searchDiseases.value = [];
  checkedDisease.value = [...list];
  selections.value = list.reduce((val, item) => {
    val[item] = true;
    return val;
  }, {});
  showdisease.value = true;
}
function toggle(tag) {
  const selected = Boolean(selections.value[tag]);
  if (selected) {
    checkedDisease.value = checkedDisease.value.filter((i) => i !== tag);
  } else {
    checkedDisease.value.push(tag);
  }
  selections.value[tag] = !selections.value[tag];
}

function save() {
  showdisease.value = false;
  if (!checkedDisease.value) {
    checkedDisease.value = [];
  }
  let array = [...new Set([...searchDiseases.value, ...checkedDisease.value])];
  $emit("get-select-disease", array);
}
function cancel() {
  showdisease.value = false;
}
const debounceFunc = debounce((e) => {
  if (e.data) {
    diseaseList.value = [];
    getDieaseList(e);
  }
}, 500);
// 获取疾病列表
async function getDieaseList(e) {
  const { data } = await getDisease(e.data);
  diseaseList.value = data.data.map((item) => {
    return {
      value: item.diseaseName,
      label: item.diseaseName,
    };
  });
}
// 获取搜索的疾病
function getDiseaseAction(e) {
  //   searchDiseases = e;
  console.log(searchDiseases.value);
}
defineExpose({
  openDialog,
});
</script>

<style lang="scss" scoped>
.dease-drawer {
  .header {
    display: flex;
    align-items: center;

    .left {
      margin-right: 20px;
    }

    .right {
      width: 250px;
    }
  }
}

.defaultTitle {
  margin: 15px 0 15px;
  padding-top: 10px;
  border-top: 1px dashed #ddd;
}

.defaultDiseases {
  width: 100%;
  overflow: hidden;

  .box {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
  }

  .item {
    min-width: 100px;
  }
}

.button_view {
  display: flex;
  padding: 15px;
  justify-content: center;
  border-top: 1px solid #eee;

  .l {
    margin-right: 20px;
  }
}

.tag__item {
  margin-right: 15px;
  margin-bottom: 12px;
  padding: 6px 10px;
  min-width: 80px;
}

:deep(.el-drawer__footer) {
  --el-drawer-padding-primary: 0;
  padding-top: 0;
}
</style>
