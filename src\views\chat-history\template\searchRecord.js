let template = `
<scroll-view class="scrollarea" scroll-y type="list" bindscrolltolower="bindscrolltolower">
    <view class="chat-record">
        <view wx:for="{{data.migList}}">
            <view class="chat">
                <view class="chat-left">
                    <view>
                        <view class="flex" wx:if="{{item.senderType === 2}}">
                        <ww-open-data  openid="{{item.customerUserId}}" type="externalUserName"></ww-open-data>
                            <view class="pl-10 chat-color">@微信</view>
                        </view>
                        <view class="flex" wx:if="{{item.senderType === 1}}">
                            <ww-open-data type="userName" openid="{{item.memberUserId}}"></ww-open-data>
                            <view class="pl-10 member-color">@员工</view>
                        </view>
                    </view>
                    <view class="send-time">
                        {{item.sendTime}}
                    </view>
                </view>
                <view class="chat-left">
                    <view class="chat-content">
                       <ww-open-message message-id="{{item.msgid}}" secret-key="{{item.secretKey}}"  open-type="viewMessage"/>
                    </view>
                    <view class="read-chat" bindclick="chat" data-info="{{item}}">查看聊天</view>
                </view>
            </view>
        </view>
    </view>
</scroll-view>
`;

const style = `
.scrollarea {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    font-size: 14px;
}

.chat-record {
    height: 100%;
    width: 100%;
}

.chat {
    padding: 15px;
    border-bottom: 1px solid rgba(229, 231, 235);
}

.chat-left {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.flex {
    display: flex;
    align-items: center;
}

.chat-content {
    flex-grow: 0;
    max-width: 90%;
}

.font-semibold {
    font-weight: 600;
}

.pl-10 {
    padding-left: 10px;
}

.pr-10 {
    padding-right: 10px;
}

.send-time {
    font-size: 12px;
    margin-bottom: 20px;
    width: 120px;
    text-align: right;
}

.chat-color {
    color: #66CB70;
    font-size: 12px;
}

.member-color {
    color: rgba(249, 115, 22);
    font-size: 12px;
}

.read-chat {
    color: #006EF8;
    font-size: 12px;
}
`;

export { template, style };
