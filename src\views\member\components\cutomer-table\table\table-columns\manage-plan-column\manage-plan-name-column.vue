<template>
  <el-table-column label="回访计划" prop="managePlanName" :width="200">
    <template #default="{row}">
      <td-wrapper classnames="w-full">
        <plan-wrapper :plans="row.plans">
          <template #default="{plan}">
            {{ plan && plan.planName ? plan.planName:'' }}
          </template>
        </plan-wrapper>
      </td-wrapper>
    </template>
  </el-table-column>
</template>
<script setup>
import tdWrapper from '../td-wrapper.vue';
import planWrapper from './plan-wrapper.vue';


</script>
<style lang="scss" scoped></style>
