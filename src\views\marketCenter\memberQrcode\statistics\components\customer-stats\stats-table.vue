<template>
  <div p-15>
    <div font-semibold font-16 flex-shrink-0 active-title-bar>统计明细表</div>
    <div flex-shrink-0 class="my-10px">
      <template v-for="(type, idx) in sortType" :key="type.key">
        <el-text pointer :type="currentSort === type.key ? 'primary' : ''" @click="changeSort(type.key)">
          {{ type.label }}
        </el-text>
        <el-divider v-if="idx < sortType.length - 1" direction="vertical" />
      </template>
    </div>
    <date-table v-if="currentSort === 'date'" :addCustomerList="addCustomerList"></date-table>
    <user-table v-else-if="currentSort === 'user'"  :addCustomerList="addCustomerList"></user-table>
    <customer-table v-else  :addCustomerList="addCustomerList"></customer-table>
  </div>
</template>
<script setup>
import { ref, inject, watch } from "vue";
import WwUser from "@/components/ww-user/index.vue";
import dateTable from "./date-table.vue";
import userTable from "./user-table.vue";
import customerTable from "./customer-table.vue";
const props = defineProps({
  addCustomerList: { type: Array, default: () => [] },
});
const list = ref([{}]);
const currentSort = ref("date");
const sortType = [
  { label: "按日期统计", key: "date" },
  { label: "按员工统计", key: "user" },
  { label: "按客户统计", key: "customer" },
];
function changeSort(key) {
  if (currentSort.value == key) return;
  currentSort.value = key;
}
</script>
