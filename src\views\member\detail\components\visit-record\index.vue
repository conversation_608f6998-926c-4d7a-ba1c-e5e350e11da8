<template>
  <div class="h-full relative">
    <my-layout>
      <layout-main>
        <health-indicator :healthIndicators="healthIndicators" :customerId="props.customer._id" />
        <div flex h-full bg-fff>
          <my-layout class="w-360px border-r border-gray-200" flex-shrink-0>
            <layout-item>
              <div class="flex items-center px-10px py-12px" style="--el-font-size-base: 13px" @click.stop>
                <el-select v-model="medicalType" class="mr-10px" @change="changeType()">
                  <el-option label="全部" value="ALL"></el-option>
                  <el-option v-for="item in templateList" :key="item.templateType" :label="item.name"
                    :value="item.templateType"></el-option>
                </el-select>
                <el-date-picker v-model="dates" type="daterange"  :unlink-panels="true"
 class="flex-grow" value-format="YYYY-MM-DD"
                  @change="getList()"></el-date-picker>
              </div>
            </layout-item>
            <layout-main v-if="recordList.length">
              <div px-15>
                <div v-for="record in recordList" px-15 font-14 pointer py-10 :key="record._id"
                  :class="currentRecord && currentRecord._id === record._id ? 'is-active' : ''"
                  class="current-visit-record" @click="toggle(record, record.medicalType)">
                  <div flex items-center justify-between class="time-line-time">
                    <span>{{ record.sortTime }}</span>
                    <span>{{ getmedicalTypeName(record.medicalType) }}</span>
                  </div>
                </div>
              </div>
            </layout-main>
            <layout-main v-else :scroll="false">
              <div h-full flex flex-col items-center justify-center>
                <empty-data :top="0" title="暂无记录"></empty-data>
              </div>
            </layout-main>
            <layout-item>
              <div class="pt-10px px-15px">
                <el-dropdown :disabled="true" class="w-full opacity-0">
                  <el-button ref="bottomBtnRef" class="w-full" :icon="Plus" type="primary">新增</el-button>
                </el-dropdown>
              </div>
            </layout-item>
          </my-layout>
          <div class="flex-grow h-full">
            <right-record-view v-if="currentRecord && currentRecord._id" :visible="showRecordVisible"
              @close="showRecordVisible = false" :currentRecord="currentRecord" :operationType="operationType"
              :templateType="templateType" :customer="customer" @reload="reload" @onRemove="onRemove" />
          </div>
          <record-show-view :visible="showRecordVisible" @close="showRecordVisible = false"
            :currentRecord="newRecord.record" :operationType="newRecord.operationType"
            :templateType="newRecord.templateType" :customer="customer" @reload="reload"
            @onRemove="onRemove"></record-show-view>
        </div>
      </layout-main>
    </my-layout>
    <el-dropdown v-if="customerType !== 'corpCustomer'" class="absolute z-5" :style="fixedStyle">
      <el-button class="w-full" :icon="Plus" type="primary">新增</el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item v-for="item in templateList" :key="item.templateType" @click="add(item.templateType)">{{
            item.name }}</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>
<script setup>
import { ref, computed, onMounted } from "vue";
import { useElementBounding } from "@vueuse/core";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import { getCustomerMedicalRecord, mergeMedicalHisRecord, getHealthIndicators } from "@/api/member";
import { memberStore } from "@/store/member";
import { templateStore } from "@/store/template";

import rightRecordView from "./right-record-view.vue";
import EmptyData from "@/components/empty-data.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import healthIndicator from "./health-indicator.vue";
import RecordShowView from "./record-show-view.vue";

const { corpInfo } = memberStore();
const store = templateStore();
const { corpTemplateList } = storeToRefs(store);
const { getCorpTemplate } = store;
const templateType = ref("");
const currentRecord = ref({});
const dates = ref([dayjs().startOf("year").format("YYYY-MM-DD"), dayjs().endOf("year").format("YYYY-MM-DD")]);
const medicalType = ref("ALL");
const recordList = ref([]);
const showRecordVisible = ref(false);
// 定义操作类型
const operationType = ref("edit");
const props = defineProps({
  customer: { type: Object, default: () => ({}) },
  customerType: { type: String, default: "customer" },
});
const templateList = ref([]);
const healthIndicators = ref([]);
onMounted(async () => {
  if (!Array.isArray(corpTemplateList.value) || corpTemplateList.value.length === 0) {
    await getCorpTemplate();
  }
  // 获取 corpTemplateList parentTemplateType 类型是 medicalRecord 的模板
  templateList.value = corpTemplateList.value.filter((i) => i.parentTemplateType === "medicalRecord" && i.templateStatus === "enable");
  await mergeHisRecord();
  getList();
  getHealthIndicatorsApi();
});
async function mergeHisRecord() {
  if (!corpInfo.isConnectHis || !props.customer.customerNumber) return;
  return await mergeMedicalHisRecord({
    memberId: props.customer._id,
    customerNumber: props.customer.customerNumber,
    recordDates: dates.value,
  });
}

async function getHealthIndicatorsApi() {
  const { data, success } = await getHealthIndicators({ memberId: props.customer._id });
  if (success) {
    healthIndicators.value = data.list;
  } else {
    healthIndicators.value = [];
  }
}
const getmedicalTypeName = (type) => {
  const item = templateList.value.find((i) => i.templateType === type);
  return item ? item.name : "";
};

const updateRecord = computed(() => {
  if (!currentRecord.value || Object.keys(currentRecord.value).length === 0) return null;
  const { creator, createTime, updateBy, updateTime } = currentRecord.value;
  const record = {};
  if (updateBy && updateTime) {
    record.update = {
      openId: updateBy,
      time: dayjs(updateTime).format("YYYY年MM月DD HH:mm"),
    };
  }
  if (creator && createTime) {
    record.create = {
      openId: creator,
      time: dayjs(createTime).format("YYYY年MM月DD HH:mm"),
    };
  }
  return record;
});
function changeType(type) {
  currentRecord.value = {};
  operationType.value = "edit";
  // if (medicalType.value !== type) {
  //   medicalType.value = type;
  getList();
  // }
}
function toggle(record, type = "") {
  templateType.value = type;
  if (!currentRecord.value || currentRecord.value._id !== record._id) {
    currentRecord.value = record;
  }
  // showRecordVisible.value = true;
}
function reload(id) {
  // medicalType.value = templateType.value;
  showRecordVisible.value = false;
  getList(id);
  operationType.value = "edit";
  getHealthIndicatorsApi();
}

const newRecord = ref({});
function add(type) {
  newRecord.value = {
    templateType: type,
    operationType: "add",
    record: {},
  };
  // templateType.value = type;
  // operationType.value = "add";
  // currentRecord.value = {};
  showRecordVisible.value = true;
}
async function getList(id) {
  const params = {
    memberId: props.customer._id,
    corpId: localStorage.getItem("corpId"),
    startTime: dates.value && dates.value[0] ? dayjs(dates.value[0]).valueOf() : "",
    endTime: dates.value && dates.value[1] ? dayjs(dates.value[1]).endOf("day").valueOf() : "",
  };
  if (medicalType.value !== "ALL") {
    params.medicalType = medicalType.value;
  } else {
    // 如果是全部类型 则获取所有模板
    let medicalTypeList = templateList.value.map((i) => i.templateType);
    params.medicalType = medicalTypeList;
  }
  const {
    data: { list = [] },
  } = await getCustomerMedicalRecord(params);
  recordList.value = list
    .map((i) => {
      return {
        ...i,
        sortTime: i.sortTime ? dayjs(i.sortTime).format("YYYY-MM-DD") : "",
      };
    })
    .sort((a, b) => {
      const pre = a.date ? dayjs(a.date).valueOf() : 0;
      const next = b.date ? dayjs(b.date).valueOf() : 0;
      return next - pre;
    });
  // 如果有id 则默认选中
  let record = {};
  if (id) {
    record = recordList.value.find((i) => id === i._id);
  } else {
    record = recordList.value.find((i) => currentRecord.value && currentRecord.value._id === i._id) || recordList.value[0] || {};
  }
  currentRecord.value = record;
  templateType.value = record && record.medicalType ? record.medicalType : '';

}

function onRemove() {
  reload();
  currentRecord.value = {};
}

const bottomBtnRef = ref();
const { width, left, right } = useElementBounding(bottomBtnRef);
const fixedStyle = computed(() => `width:${width.value}px;left:15px;bottom:0px`);
</script>
<style scoped>
.update-record {
  position: absolute;
  right: 5px;
  bottom: 5px;
}

.yc-tab:hover,
.yc-tab.is-active {
  color: #006eff;
}

.yc-tab {
  border-right: 1px solid #eee;
}

:deep(.current-visit-record .el-timeline-item__node) {
  z-index: 2;
  left: 4px;
  display: none;
}

:deep(.current-visit-record.el-timeline-item) {
  /* padding-left: 10px; */
  padding-top: 10px;
  padding-bottom: 10px;
}

.current-visit-record.is-active {
  /* background: #eef4ff; */
  background: #006eff;
  border-radius: 8px;
}

.current-visit-record {
  background: #f2f2f2;
  margin-bottom: 10px;
  border-radius: 8px;
}

:deep(.current-visit-record .el-timeline-item__timestamp) {
  color: rgba(0, 0, 0, 0.9);
}

.current-visit-record.is-active .time-line-content,
.current-visit-record.is-active .time-line-time,
.current-visit-record.is-active .time-line-label {
  color: white;
}

:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
  width: 100%;
}

:deep(.el-timeline) {
  padding-inline-start: 0;
}

:deep(.el-timeline-item__node) {
  background-color: white;
}
</style>