<template>
  <filter-item label="分组" :text="text" :width="100">
    <radio-group v-model="hasGroup" v-model:text="text" :list="list" />
  </filter-item>
</template>
<script setup>
import { ref } from 'vue';
import FilterItem from './filter-item.vue';
import radioGroup from './radio-group.vue';

const list = [{ label: '已分组', value: 'YES' }, { label: '未分组', value: 'NO' }];
const hasGroup = ref('');
const text = ref('');

function getParam() {
  return hasGroup.value ? { hasGroup: hasGroup.value } : {}
}

function reset() {
  hasGroup.value = ''
}

defineExpose({
  getParam,
  reset
})
</script>
<style lang="scss" scoped></style>
