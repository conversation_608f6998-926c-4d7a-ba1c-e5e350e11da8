import { createApp } from "vue";
import { createPinia } from "pinia";
import piniaPersist from "pinia-plugin-persist";
import App from "./App.vue";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import "virtual:windi.css";
import * as ElementPlusIconsVue from "@element-plus/icons-vue";
import WwUser from "@/components/ww-user/index.vue";
import "virtual:svg-icons-register";
import locale from "element-plus/es/locale/lang/zh-cn";
import router from "./router";
import elButtonAutoBlur from "./plugins/el-button-auto-blur";
import "./assets/font_4415501_7idtq96oak6/iconfont.css";

const app = createApp(App);
const pinia = createPinia();
pinia.use(piniaPersist);


app.use(router);
app.use(pinia);
app.use(ElementPlus, { locale });
app.use(elButtonAutoBlur);
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}
app.component("WwUser", WwUser);
app.config.warnHandler = () => null;
app.mount("#app");
