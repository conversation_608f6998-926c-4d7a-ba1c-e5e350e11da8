import { getTeamsServiceAndMemberCount, getStaffServiceAndMemberCount, getCustomerSourceCountSort, getCustomerGroupCountSort, getCusomterAndServiceCount, getStaffRate, getScoreRateTrend } from "@/api/member";
import { getQrcodeStaticsticsSort, getArticleListReadStats } from "@/api/knowledgeBase";
import { getCorpToDolist } from "@/api/todo";
import { getChatCount } from "@/api/sessionArchive";
import { getOpenedAccount } from "@/api/corp.js";
import { getNumber as statisticsEvents } from "@/api/todo";
import { memberStore } from "@/store/member";
import { teamStore } from "@/store/team";
import dayjs from "dayjs";
import { ref } from "vue";
import { pageSize } from "../../chat-history/api/chatCustomer";
let customerSourceCountSort = ref([]);
let customerGroupCountSort = ref([]);
let staffServiceAndMemberCount = ref([]);
let teamsServiceAndMemberCount = ref([]);
let qrcodeStaticsticsSort = ref([]);
let corpToDolist = ref([]);
// 获取团队服务人数和成员人数
async function getTeamsServiceAndMemberCountApi(timeType) {
  const dates = getTime(timeType);
  const { allTeams = [] } = teamStore();
  const teamList = allTeams.map((item) => {
    return {
      teamId: item.teamId,
      name: item.name,
    };
  });

  const { data, success, message } = await getTeamsServiceAndMemberCount({ dates });
  if (success) {
    data.list.sort((a, b) => b.memberCount - a.memberCount);

    teamsServiceAndMemberCount.value = data.list.map((item, index) => {
      return {
        ...item,
        rank: index + 1,
      };
    });
  } else {
    teamsServiceAndMemberCount.value = [];
  }
}
// 获取员工服务人数和成员人数
async function getStaffServiceAndMemberCountApi(timeType) {
  const dates = getTime(timeType);
  const { data, success } = await getStaffServiceAndMemberCount({ dates });
  if (success) {
    data.list.sort((a, b) => b.memberCount - a.memberCount);
    staffServiceAndMemberCount.value = data.list.map((item, index) => {
      return {
        ...item,
        rank: index + 1,
      };
    });
  } else {
    staffServiceAndMemberCount.value = [];
  }
}

function getTime(timeType) {
  if (timeType === "thisMonth") {
    return [dayjs().startOf("month").format("YYYY-MM-DD"), dayjs().endOf("month").format("YYYY-MM-DD")];
  } else if (timeType === "lastMonth") {
    return [dayjs().subtract(1, "month").startOf("month").format("YYYY-MM-DD"), dayjs().subtract(1, "month").endOf("month").format("YYYY-MM-DD")];
  } else if (timeType === "thisWeek") {
    return [dayjs().startOf("isoWeek").format("YYYY-MM-DD"), dayjs().endOf("isoWeek").format("YYYY-MM-DD")];
  } else if (timeType === "lastWeek") {
    return [dayjs().subtract(1, "week").startOf("isoWeek").format("YYYY-MM-DD"), dayjs().subtract(1, "week").endOf("isoWeek").format("YYYY-MM-DD")];
  } else if (timeType === "thisDay") {
    return [dayjs().startOf("day").format("YYYY-MM-DD"), dayjs().endOf("day").format("YYYY-MM-DD")];
  } else {
    return [];
  }
}

async function getScoreRateTrendApi(timeType) {
  const dates = getTime(timeType);
  const { data, success } = await getScoreRateTrend({ dates });
  if (success) return data.count;
  else return [];
}
// 获取客户来源统计
async function getCustomerSourceCountSortApi() {
  let { corpInfo } = memberStore();
  let sources = corpInfo.customerSourceList ? corpInfo.customerSourceList.map((i) => i.sourceName) : [];
  const { data, success } = await getCustomerSourceCountSort(sources);
  if (success) {
    customerSourceCountSort.value = data.list.map((item) => {
      return {
        name: item.sourceName,
        value: item.count,
      };
    });
  } else {
    customerSourceCountSort.value = [];
  }
}

// 获取客户分组统计
async function getCustomerGroupCountSortApi() {
  const { data, success, message } = await getCustomerGroupCountSort();
  if (success) {
    customerGroupCountSort.value = data.list.map((item, index) => {
      return {
        name: item.groupName,
        value: item.count,
      };
    });
  } else {
    customerGroupCountSort.value = [];
  }
}

// 获取二维码统计
async function getQrcodeStaticsticsSortApi() {
  const { data, success } = await getQrcodeStaticsticsSort();
  if (success) {
    qrcodeStaticsticsSort.value = data.list.map((item, index) => {
      return {
        name: item.qrCodeName,
        value: item.count,
      };
    });
  } else {
    qrcodeStaticsticsSort.value = [];
  }
}
// 获取待办列表
async function getCorpToDolistApi() {
  const { data, success } = await getCorpToDolist({
    page: 1,
    pageSize: 100,
  });
  if (success) {
    corpToDolist.value = data.data;
  } else {
    corpToDolist.value = [];
  }
}
// 获取会话统计
async function getChatCountApi() {
  const { data, success } = await getChatCount();
  if (success) return data;
  return {};
}

// 获取客户和服务人数
async function getCustomerAndServiceCountApi() {
  const { data, success } = await getCusomterAndServiceCount();
  if (success) {
    return data;
  }
  return {};
}

async function getOpenedAccountApi() {
  const { data, success } = await getOpenedAccount();
  if (success && Array.isArray(data.data)) return data.data.length;
  return 0;
}

async function getStatisticsEventsApi() {
  const { data, success } = await statisticsEvents();
  if (success) {
    return data.data;
  }
  return [];
}

async function getStaffRateApi() {
  const { data, success } = await getStaffRate();
  if (success) {
    return data.averageScore;
  }
  return [];
}

async function getArticleListReadStatsApi() {
  const { data, success } = await getArticleListReadStats();
  if (success) {
    return data.list.map((item, index) => {
      return {
        ...item,
        rank: index + 1,
      };
    });
  }
  return [];
}

export { customerSourceCountSort, customerGroupCountSort, staffServiceAndMemberCount, teamsServiceAndMemberCount, qrcodeStaticsticsSort, corpToDolist, getTeamsServiceAndMemberCountApi, getStaffServiceAndMemberCountApi, getCustomerSourceCountSortApi, getCustomerGroupCountSortApi, getQrcodeStaticsticsSortApi, getCorpToDolistApi, getChatCountApi, getCustomerAndServiceCountApi, getOpenedAccountApi, getStatisticsEventsApi, getStaffRateApi, getScoreRateTrendApi, getArticleListReadStatsApi };
