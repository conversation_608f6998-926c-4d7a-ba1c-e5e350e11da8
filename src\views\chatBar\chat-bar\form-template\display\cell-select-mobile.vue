<template>
  <div>{{ text }}</div>
</template>
<script setup>
import { computed } from "vue";

const props = defineProps({
  title: { default: '' },
  form: { type: Object, default: () => ({}) }
})
const noteTitle = computed(() => `${props.title}Note`);
const value = computed(() => props.title ? props.form[props.title] : '');

const text = computed(() => {
  const mobile = typeof value.value === 'string' ? value.value : '';
  const note = props.form && typeof props.form[noteTitle.value] === 'string' ? props.form[noteTitle.value] : '';
  return `${mobile} ${note}`;
})

</script>