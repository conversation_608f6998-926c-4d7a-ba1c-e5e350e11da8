<template>
  <my-layout bg-fff v-loading="loading">
    <layout-main>
      <div px-15>
        <div active-title-bar font-semibold class="my-15px text-20px py-2px">{{ viewTitle }}</div>
        <div v-if="form.userid" flex items-center pb-15 border-bottom>
          <div flex-shrink-0 class="mr-20px">
            <upload-file v-if="isEdit" ref="coverRef" classnames="h-80px w-80px" :showFileList="false" listType="" @hand-file-change="onHandFileChange" fileType="image/*">
              <el-avatar v-loading="uploadLoading" element-loading-text="正在上传..." shape="square" :size="80" fit="fill" :src="form.avatar">
                <div w-full h-full flex flex-col justify-center bg-fff items-center>
                  <el-button class="w-80px h-80px is-hover" plain>上传头像</el-button>
                </div>
              </el-avatar>
            </upload-file>
            <img v-else-if="form.avatar" :src="form.avatar" class="w-80px h-80px is-hover" />
            <img v-else src="@/assets/icons/default-avatar.png" class="w-80px h-80px is-hover" />
          </div>
          <div flex-grow>
            <div font-16 font-semibold style="line-height: 40px" class="flex">
              <ww-user :openid="form.userid"></ww-user>
              <div class="pl-10px flex align-center" style="color: #cdcdcd" v-if="props.staff.accountState === 'disable'">
                <img src="@/assets/stop.svg" class="w-20px h-20px" />
                已禁用
              </div>
            </div>
            <div font-14 style="line-height: 40px" class="flex">
              <!-- <div class="mr-4px">部门： </div>
              <div v-if="props.staff.department && props.staff.department.length > 0">
                <span v-for="(item, index) in props.staff.department" class="h-20px">
                  <ww-user :openid="item" type="departmentName"></ww-user>
                  <span v-if="index !== props.staff.department.length - 1">、</span>
                </span>
              </div> -->
              <div>ID: {{ form.userid }}</div>
            </div>
          </div>
          <el-button v-if="!isEdit" flex-shrink-0 type="primary" @click="editMember">修改</el-button>
        </div>
        <div v-else pb-15 border-bottom>
          <el-form-item class="is-required items-center" label="选择员工">
            <el-button class="w-80px h-80px is-hover" :icon="Plus" plain @click="selectForCorp">新增</el-button>
          </el-form-item>
        </div>
        <div font-semibold font-16 class="my-15px">基础信息</div>
        <div pb-15 border-bottom>
          <el-form class="staff-info" :label-width="100" label-position="right" label-suffix="：" :disabled="!isEdit">
            <el-row>
              <el-col :lg="8" :md="12" :sm="24">
                <el-form-item class="is-required" label="对外名称">
                  <el-input v-model="form.anotherName" placeholder="请输入员工对外名称"></el-input>
                </el-form-item>
              </el-col>
              <el-col :lg="8" :md="12" :sm="24">
                <el-form-item label="性别">
                  <el-radio-group v-model="form.gender">
                    <el-radio label="0">男</el-radio>
                    <el-radio label="1">女</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :lg="8" :md="12" :sm="24">
                <el-form-item class="pr-10px" label="手机号">
                  <el-input v-model="form.mobile" placeholder="请输入员手机号"></el-input>
                </el-form-item>
              </el-col>
              <el-col :lg="8" :md="12" :sm="24">
                <el-form-item label="岗位">
                  <el-select multiple :collapse-tags="isEdit" v-model="form.job" class="w-full" placeholder="请输入岗位" filterable allow-create>
                    <el-option v-for="item in jobOptions" :key="item._id" :label="item.name" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :lg="16">
                <el-form-item label="所属科室">
                  <div class="px-10px py-5px flex items-center w-full border border-gray-200 min-h-32px cursor-pointer rounded hover:border-gray-300" :class="isEdit ? '' : 'bg-[#f5f7fa]'" @click="isEdit ? showDeptPicker() : ''">
                    <div v-if="depts.length == 0 && isEdit" class="flex-shrink-0 text-gray-400 text-14px">请选择</div>
                    <div class="flex-grow flex flex-wrap -mb-5px">
                      <div class="flex items-center rounded border border-gray-200 px-10px py-3px mr-5px mb-5px group bg-gray-50" v-for="(dept, i) in depts" :key="i" @click.stop="">
                        <div class="text-12px">{{ dept.deptName }}</div>
                        <el-icon v-if="isEdit" class="ml-5px text-gray-500 hover:text-red-500" :size="14" @click.stop="removeDept(i)">
                          <CloseBold />
                        </el-icon>
                      </div>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-col>
              <el-form-item label="门诊时间">
                <el-input v-model="form.outpatientTime" type="textarea" :autosize="{ minRows: 4, maxRows: 8 }" />
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="对外电话">
                <div class="flex-grow">
                  <el-input v-model="form.callNumber" class="w-full" />
                  <div v-if="isEdit" class="mt-10px flex items-center text-13px text-red-500">
                    <el-icon class="text-16px mr-5px">
                      <Warning />
                    </el-icon>
                    <span>对外电话会在患者端页面展示并支持患者直接拨打，请慎重填写</span>
                  </div>
                </div>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="便民服务">
                <div class="flex-grow -mb-10px">
                  <div v-if="isEdit" class="flex items-center text-blue-500 mb-10px">
                    <el-icon class="text-14px mr-5px cursor-pointer" @click="addService()">
                      <CirclePlusFilled />
                    </el-icon>
                    <span class="cursor-pointer" @click="addService()">添加</span>
                  </div>
                  <div v-for="i in form.convenienceService" :key="i.key" class="flex items-center mb-10px text-14px">
                    <div class="flex-shrink-0 text-gray-500 mr-10px">页面名称：</div>
                    <el-input v-model="i.name" placeholder="请输入页面名称" class="w-200px mr-10px" />
                    <div class="flex-shrink-0 text-gray-500 mr-10px">页面地址：</div>
                    <el-input v-model="i.href" placeholder="以http://或https://开头" class="flex-grow" />
                    <el-icon v-if="isEdit" class="cursor-pointer text-16px text-red-500 ml-10px" @click="removeService(i.key)">
                      <Delete />
                    </el-icon>
                  </div>
                </div>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="员工介绍">
                <el-input v-model="form.memberTroduce" type="textarea" :autosize="{ minRows: 4, maxRows: 8 }" :maxlength="200" show-word-limit placeholder="请输入员工介绍"></el-input>
              </el-form-item>
            </el-col>
          </el-form>
        </div>
        <div font-semibold font-16 class="my-15px flex align-center">
          <div>账户权限</div>
          <el-button v-if="isOpenAccount && !props.staff.userid" type="primary" text @click="removeOpenAccount">取消开通账户权限</el-button>
        </div>
        <div pb-15>
          <div v-if="!isOpenAccount">
            <div v-if="isEdit" class="no-open-account-cell">
              <el-button @click="openAccount" class="mb-20px" type="primary">开通账户权限</el-button>
              <div>开通账户权限，员工进入团队，为员工设置平台角色后，员工可登陆“医客通平台。</div>
            </div>
            <div class="no-open-account-cell" v-else>暂未开通平台账户</div>
          </div>
          <el-form v-else class="staff-info" :label-width="100" label-position="left" label-suffix="：">
            <el-row>
              <el-col :span="24">
                <el-form-item class="is-required" label="所属团队">
                  <div w-full>
                    <span items-center v-for="(item, idx) in belongUserTeam" :key="item._id" class="text-14px leading-24px inline-block">
                      <span font-semibold>{{ item.name }}</span>
                      <span v-if="item.isTeamLeader" class="border-1 inline-block px-6px mx-4px leading-20px">负责人</span>
                      <span v-if="idx < teamList.length - 1">、</span>
                    </span>
                    <el-text v-if="isEdit" class="ml-12px" pointer type="primary" text @click="teamVisible = true">修改</el-text>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item class="is-required" label="平台角色">
                  <div w-full class="flex">
                    <div>
                      <el-button :type="item.type" v-for="item in showDefaultRoles" class="w-110px">
                        {{ item.roleName }}
                      </el-button>
                      <el-button type="primary" v-for="item in selectedRoles" class="w-110px">
                        {{ item.roleName }}
                      </el-button>
                      <!-- <span class="text-14px leading-24px inline-block ml-10px" v-for="(item, idx) in selectedRoles"
                        :key="item._id">
                        <span font-semibold>{{ item.roleName }}</span>
                        <span v-if="idx < selectedRoles.length - 1" class="mr-10px">、</span>
                      </span> -->
                    </div>
                    <el-text v-if="isEdit" class="ml-12px" pointer type="primary" text @click="changeRole()">修改</el-text>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
    </layout-main>
    <layout-item v-if="isEdit">
      <div text-center common-shadow--r py-10>
        <el-button class="w-100px" @click="onClose()" plain>
          {{ props.addType === "transfer" ? "取消转让" : "取消" }}
        </el-button>
        <el-button class="w-100px" type="primary" @click="saveCorpMemberUpdate">
          {{ props.addType === "transfer" ? "确认转让" : "保存" }}
        </el-button>
      </div>
    </layout-item>
    <belong-team :visible="teamVisible" @close="teamVisible = false" :belongTeamList="teamList" :userId="form.userid" @addTeam="onAddTeam"></belong-team>
    <staff-role :visible="roleVisible" @close="roleVisible = false" :roleIds="roles" @addRole="onAddRole" :member="props.staff"></staff-role>
    <dept-picker-modal ref="deptPickerRef" :value="deptIds" :visible="deptVisible" :width="deptPickerWidth" @change="deptChange" @close="closeDeptPicker" />
  </my-layout>
</template>

<script setup>
import { computed, nextTick, ref, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { 
  getRolesList, 
  getTeamsByUserId, 
  removeAccount, 
  upDatetransferTime, 
  updateCorpMember, 
  addCorpMember, 
  updateTeam, 
  getCorpMember, 
  getCorpMemberJob as getCorpMemberJobUrl 
} from "@/api/corp";
import { getCorpMemberInfoByUserId, transferLicense } from "@/api/wecom.js";
import { updateFile } from "@/api/uploadFIle.js";
import { memberStore } from "@/store/member";
import { teamStore } from "@/store/team";
import { selectEnterpriseContact } from "@/utils/jssdk";
import { getRandomStr } from "@/utils";

import useModal from "@/hooks/useModal";

import { Plus } from "@element-plus/icons-vue";
import BelongTeam from "./belong-team.vue";
import StaffRole from "./staff-role.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import WwUser from "@/components/ww-user/index.vue";
import uploadFile from "@/components/upload-file/index.vue";
import deptPickerModal from "@/components/dept-picker-modal/dept-picker-modal.vue";

// ------------------- 常量定义 -------------------
const defaultConvenienceService = [
  { name: "预约挂号", href: "" },
  { name: "在线咨询", href: "" },
];

// ------------------- Props & Emits -------------------
const props = defineProps({
  staff: { type: Object, default: () => ({}) },
  addType: { type: String, default: "" },
  transferUserId: { type: String, default: "" },
  corpMemberAndcustomtorCount: { type: Object, default: {} },
});

const $emits = defineEmits(["get-corp-member-info", "update-success", "back-frist-member"]);

// ------------------- 响应式状态 -------------------
// 表单数据
const form = ref([]);
const loading = ref(false);
const isEdit = ref(false);
const uploadLoading = ref(false);
const coverRef = ref();

// 部门相关
const depts = ref([]);
const deptPickerRef = ref();
const { close: closeDeptPicker, show: showDeptPicker, visible: deptVisible, width: deptPickerWidth } = useModal(640);

// 团队相关
const teamList = ref([]);
const teamVisible = ref(false);
let oldTeams = [];

// 角色相关
const roles = ref([]);
const roleList = ref([]);
const roleVisible = ref(false);
const defaultRoles = ref([]);

// 账户相关
const isOpenAccount = ref(false);
const corpMemberJobList = ref([]);

// ------------------- 计算属性 -------------------
const viewTitle = computed(() => {
  if (props.addType === "transfer") {
    return "账户转让";
  }
  return isEdit.value ? (props.staff._id ? "编辑员工信息" : "新增员工") : "员工详情";
});

const deptIds = computed(() => depts.value.map((item) => item._id));
const propsDeptIds = computed(() => (props.staff && Array.isArray(props.staff.deptIds) ? props.staff.deptIds : []));

const belongUserTeam = computed(() => {
  return teamList.value.filter((item) => item.memberList.some((i) => i === form.value.userid));
});

const jobOptions = computed(() => {
  const jobList = Array.isArray(form.value.job) ? form.value.job : [];
  const customJobList = jobList.filter((i) => !corpMemberJobList.value.some((b) => b.value === i));
  return [...corpMemberJobList.value, ...customJobList.filter(Boolean).map((i) => ({ label: i, value: i, _id: i }))];
});

// 角色相关计算属性
const roleNameList = computed(() => {
  if (roleList.value.length == 0) return [];
  return roleList.value.filter((item) => defaultRoles.value.some((i) => i.roleType === item.roleId)).sort((a, b) => a.sort - b.sort);
});

const selectedRoles = computed(() => {
  if (roleList.value.length === 0 || roles.value.length === 0) return [];
  return roleList.value.filter((item) => !defaultRoles.value.some((i) => i.roleType === item.roleId) && roles.value.some((i) => i === item._id));
});

const showDefaultRoles = computed(() => {
  if (roleNameList.value.length === 0) return [];
  return roleNameList.value.map((item) => {
    if (roles.value.some((i) => i === item._id)) {
      item.type = "primary";
    } else {
      item.type = "info";
    }
    return item;
  });
});

// ------------------- 监听器 -------------------
watch(
  props,
  (newItem) => {
    initFormData(newItem);
  },
  { immediate: true }
);

watch(
  propsDeptIds,
  async (val) => {
    await nextTick();
    depts.value = await deptPickerRef.value.getDeptsByIds(val);
  },
  { immediate: true }
);

// ------------------- 初始化方法 -------------------
// 初始化页面数据
function initFormData(newItem) {
  const { staff } = newItem;
  isEdit.value = false;
  isOpenAccount.value = staff.open_userid ? true : false;
  const services = Array.isArray(staff.convenienceService) ? staff.convenienceService : defaultConvenienceService;
  const convenienceService = services.map((i) => ({
    name: typeof i.name === "string" ? i.name : "",
    href: typeof i.href === "string" ? i.href : "",
    key: getRandomStr(),
  }));
  
  if (staff._id) {
    const { roleIds, userid, memberTroduce, job, mobile, gender, anotherName, avatar, callNumber, outpatientTime } = staff;
    form.value = {
      memberTroduce,
      job,
      mobile,
      gender,
      anotherName,
      avatar,
      userid,
      callNumber: typeof callNumber === "string" ? callNumber : "",
      outpatientTime: typeof outpatientTime === "string" ? outpatientTime : "",
      convenienceService,
    };
    roles.value = JSON.parse(JSON.stringify(roleIds));
    getTeamData(userid);
  } else {
    form.value = {
      userid: "",
      avatar: "",
      job: [],
      mobile: "",
      gender: "",
      anotherName: "",
      callNumber: "",
      outpatientTime: "",
      convenienceService,
    };
    roles.value = [];
    teamList.value = [];
  }
  
  if (newItem.addType === "transfer") {
    form.value.userid = staff.userid;
    isOpenAccount.value = true;
    isEdit.value = true;
  }
}

// 初始化数据
getRoleList();
getCorpMemberJob();

// ------------------- 数据获取方法 -------------------
// 获取角色列表
async function getRoleList() {
  let { data, success } = await getRolesList();
  if (success) {
    roleList.value = data.data;
  }
}

// 获取岗位列表
async function getCorpMemberJob() {
  const { data, success } = await getCorpMemberJobUrl();
  if (success) corpMemberJobList.value = data.data;
}

// 获取成员所属团队
async function getTeamData(userId) {
  let res = await getTeamsByUserId(userId);
  const { data, success } = res;
  if (success && data.data.length > 0) {
    oldTeams = data.data;
    teamList.value = data.data.map((item) => {
      return {
        teamId: item.teamId,
        name: item.name,
        memberList: item.memberList,
        memberLeaderList: item.memberLeaderList,
        isTeamLeader: item.memberLeaderList.some((i) => i === userId),
        isBelongTeam: true,
      };
    });
  } else {
    teamList.value = [];
  }
}

// 获取成员信息
async function getCorpInfoByuseId() {
  const userId = form.value.userid;
  let { corpInfo } = memberStore();
  let params = {
    userId,
    corpId: localStorage.getItem("corpId"),
    permanentCode: corpInfo.permanent_code,
  };
  let { data } = await getCorpMemberInfoByUserId(params);
  if (data.data.errcode === 0) {
    const { errcode, errmsg, ...reat } = data.data;
    return {
      errcode: errcode,
      errmsg: errmsg,
      data: reat,
    };
  } else {
    return {
      errcode: data.data.errcode,
      errmsg: data.data.errmsg,
    };
  }
}

// 获取企业成员列表
async function getCorpMemberList() {
  let params = {
    corpId: localStorage.getItem("corpId"),
  };
  let { data } = await getCorpMember(params, 999, 1);
  return data.data;
}

// ------------------- 表单操作方法 -------------------
// 修改员工
function editMember() {
  isEdit.value = true;
}

// 选择从机构中选择员工
async function selectForCorp() {
  if (import.meta.env.VITE_NEED_LOGIN === "true") {
    try {
      const { value: userid } = await ElMessageBox.prompt("请输入userId", "手动输入", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        inputValidator: (value) => {
          if (!value) {
            return "请输入userId";
          }
          return true;
        },
      });
      isEdit.value = true;
      getTeamData(userid);
      form.value.userid = userid;
    } catch (e) {
      console.log("用户取消输入");
    }
    return;
  }

  const res = await selectEnterpriseContact();
  const { selectedUserList } = res;
  if (selectedUserList.length > 1) {
    ElMessage.error("只能选择一名员工!");
    return;
  }
  let user = selectedUserList && selectedUserList[0];
  const userid = user && user.id;
  if (!userid) {
    ElMessage.error("员工还未添加应用!");
    return;
  }
  
  // 判断此员工是否在机构成员列表中
  let memberList = await getCorpMemberList();
  if (memberList.some((item) => item.userid === userid)) {
    ElMessage.error("该员工已存在!");
    return;
  }
  isEdit.value = true;
  getTeamData(userid);
  form.value.userid = userid;
}

// 保存成员信息
async function saveCorpMemberUpdate() {
  let params = form.value;
  let isAdminRole = props.staff.roleType && props.staff.roleType === "superAdmin";
  let res = {};
  
  // 表单验证
  if (typeof form.value.anotherName !== "string" || form.value.anotherName.trim() === "") {
    ElMessage.info("请输入对外名称");
    return;
  }
  if (isOpenAccount.value && Array.isArray(roles.value) && roles.value.length === 0) {
    ElMessage.error("平台角色不能为空!");
    return;
  }
  const myTeams = teamList.value.filter((item) => item.memberList.some((e) => e === form.value.userid));
  if (isOpenAccount.value && Array.isArray(myTeams) && myTeams.length === 0 && !isAdminRole) {
    ElMessage.error("所属团队不能为空!");
    return;
  }
  
  // 准备提交数据
  params["roleIds"] = roles.value;
  params["deptIds"] = deptIds.value;
  params["convenienceService"] = params.convenienceService.map((i) => ({ name: i.name, href: i.href }));
  loading.value = true;
  
  // 开通账户处理
  if (isOpenAccount.value && !props.staff.open_userid && import.meta.env.VITE_NEED_LOGIN !== "true") {
    // 获取成员信息
    const { errcode, data: corpInfo } = await getCorpInfoByuseId();
    if (errcode !== 0) {
      loading.value = false;
      await ElMessageBox.alert("<div>添加失败,可能有以下原因: </div><div>1、成员已退出企业</div> <div>2、成员被企业删除</div> <div>3、成员实际不在通讯录内</div> <div>4、成员不在应用可见范围内</div> ", "提示", { confirmButtonText: "知道了", dangerouslyUseHTMLString: true });
      return;
    }
    Object.assign(params, corpInfo);
  }
  
  // 保存数据
  if (props.staff._id) {
    res = await updateCorpMember(props.staff._id, params);
  } else {
    if (import.meta.env.VITE_NEED_LOGIN === "true" && !params.open_userid) {
      params.open_userid = form.value.userid;
    }
    params["corpId"] = localStorage.getItem("corpId");
    res = await addCorpMember(params);
  }
  
  // 账户转让处理
  if (props.addType === "transfer" && props.transferUserId) {
    await removeAccount(props.transferUserId);
    await upDatetransferTime(params.userid);
    await transferLicense({ takeoverUserId: params.userid, handoverUserId: props.transferUserId });
  }
  
  // 处理结果
  if (res.success) {
    if (isOpenAccount.value) {
      await updateTeamForUserId();
      setTimeout(() => {
        teamStore().getTeams();
      }, 500);
    }
    $emits("update-success", params.userid, isOpenAccount.value);
    isEdit.value = false;
    ElMessage.success("更新成功");
  } else {
    ElMessage.error(res.message);
  }
  loading.value = false;
}

// 取消操作
async function onClose() {
  depts.value = await deptPickerRef.value.getDeptsByIds(propsDeptIds.value);
  if (!props.staff._id) {
    $emits("back-frist-member");
  } else {
    const services = Array.isArray(props.staff.convenienceService) ? props.staff.convenienceService : defaultConvenienceService;
    form.value = JSON.parse(JSON.stringify(props.staff));
    form.value.convenienceService = services.map((i) => ({
      name: typeof i.name === "string" ? i.name : "",
      href: typeof i.href === "string" ? i.href : "",
      key: getRandomStr(),
    }));
  }
  isEdit.value = false;
}

// ------------------- 团队/角色相关方法 -------------------
// 开通账号
async function openAccount() {
  const { corpInfo } = memberStore();
  const { package: packAge } = corpInfo;
  const count = packAge.accountCount + packAge.giveAccountCount - props.corpMemberAndcustomtorCount.corpMemberCount;
  if (count <= 0) {
    await ElMessageBox.confirm("账户剩余数为0, 如需增加账户数, 请联系医客通客服!", "提示", {
      confirmButtonText: "去联系",
      cancelButtonText: "取消",
    });
    wx.invoke("openThirdAppServiceChat");
    return;
  }
  if (!form.value.userid) {
    ElMessage.warning("请先选择员工");
  }
  await ElMessageBox.confirm("是否开通账户?");
  isOpenAccount.value = true;
}

// 取消开通账户
function removeOpenAccount() {
  isOpenAccount.value = false;
  roles.value = [];
}

// 更新团队
async function updateTeamForUserId() {
  if (teamList.value.length === 0) {
    return;
  }
  let needUpdateTeams = teamList.value.filter((item) => {
    let flag = true;
    let index = oldTeams.findIndex((i) => i.teamId === item.teamId);
    const oldMemberList = index >= 0 && oldTeams[index].memberLeaderList;
    const memberList = index >= 0 && oldTeams[index].memberList;
    if (index >= 0 && JSON.stringify(oldMemberList) === JSON.stringify(item.memberLeaderList) && JSON.stringify(memberList) === JSON.stringify(item.memberList)) {
      flag = false;
    }
    return flag;
  });
  
  let updateTeamFunc = needUpdateTeams.map((item) => {
    return updateTeamMemberList(item);
  });
  await Promise.all(updateTeamFunc);
}

// 更新团队成员
async function updateTeamMemberList(item) {
  let { teamId, memberList, memberLeaderList } = item;
  memberList = [...new Set(memberList)];
  memberLeaderList = [...new Set(memberLeaderList)];
  let params = {
    teamId,
    memberList,
    memberLeaderList,
  };
  await updateTeam(params);
}

// 选择角色
function changeRole() {
  roleVisible.value = true;
}

// 选择角色回调
function selectedRolesAction(item) {
  if (!isEdit.value) return;
  if (item.type === "info") {
    roles.value.push(item._id);
  } else {
    roles.value = roles.value.filter((i) => i !== item._id);
  }
}

// 添加角色回调
async function onAddRole(item, list) {
  roleList.value = list;
  roleVisible.value = false;
  roles.value = item;
}

// 添加团队回调
function onAddTeam(list) {
  teamVisible.value = false;
  teamList.value = list;
}

// ------------------- 部门相关方法 -------------------
// 部门选择回调
function deptChange(val) {
  depts.value = val;
  closeDeptPicker();
}

// 移除部门
function removeDept(i) {
  depts.value.splice(i, 1);
}

// ------------------- 便民服务相关方法 -------------------
// 添加服务
function addService() {
  form.value.convenienceService.push({ key: getRandomStr(), name: "", href: "" });
}

// 移除服务
function removeService(key) {
  form.value.convenienceService = form.value.convenienceService.filter((item) => item.key !== key);
}

// ------------------- 文件上传方法 -------------------
// 头像上传处理
async function onHandFileChange(e) {
  coverRef.value && coverRef.value.clearFiles();
  const file = Array.isArray(e) ? e[e.length - 1] : null;
  if (!file) return;
  
  uploadLoading.value = true;
  try {
    let { download_url } = await updateFile(`${file.name}_${Date.now()}_${parseInt(Math.random() * 1000000)}`, file.raw, "head");
    form.value.avatar = download_url;
  } catch (e) {
    ElMessage.error(e && e.message ? e.message : "上传头像失败");
  } finally {
    uploadLoading.value = false;
  }
}
</script>

<style scoped>
.staff-info .el-form-item {
  margin-bottom: 20px;
}

.no-open-account-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>
