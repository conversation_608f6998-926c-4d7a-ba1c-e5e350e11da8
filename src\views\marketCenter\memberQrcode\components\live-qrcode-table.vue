<template>
  <my-layout>
    <layout-item>
      <div bg-fff class="pt-10px mb-10px">
        <el-form inline :label-width="90" label-suffix="：">
          <div class="flex flex-wrap px-15px query">
            <el-form-item class="query__item">
              <el-input v-model="searchForm.qrCodeName" placeholder="活码名称" clearable />
            </el-form-item>
            <el-form-item class="query__item" label-width="auto">
              <el-select v-model="searchForm.qrCodetype" placeholder="活码类型" clearable>
                <el-option v-for="item in qrcodeTypes" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item class="query__item" label-width="auto">
              <el-select v-model="searchForm.qrCodeStatus" placeholder="活码状态" clearable>
                <el-option v-for="item in qrcodeStatus" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
            <el-form-item class="query__item">
              <el-button class="ml-20px" type="primary" @click="search()">查询</el-button>
              <el-button type="primary" @click="reset()" plain>重置</el-button>
            </el-form-item>
            <el-form-item class="query__item">
              <el-button type="primary" @click="add()">新增员工活码</el-button>
              <el-text class="cursor-pointer ml-15px" type="primary" @click="liveVisible = true">了解员工活码</el-text>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <el-table stripe height="100%" :data="tableData" v-loading="loading">
        <el-table-column property="qrCodeName" label="活码名称" :min-width="150">
          <template #default="{ row }">
            <div v-if="row.staffQrcodeType === 'personal'">
              <ww-user v-if="row.creator" :openid="row.creator" />
              个人码
            </div>
            <div v-else> {{ row.qrCodeName }}</div>
          </template>
        </el-table-column>
        <el-table-column property="allAddCustomerCount" label="总添加客户数" :min-width="150" />
        <el-table-column property="todayAddCustomerCount" label="今日添加客户数" :min-width="200" />
        <el-table-column property="taskContent" label="使用成员" :min-width="200">
          <template #default="{ row: { corpUserId } }">
            <el-popover v-if="corpUserId && corpUserId.length" placement="top-start" :width="200" trigger="hover">
              <template #reference>
                <div text-ellipsis>
                  <span v-for="item in corpUserId" :key="item" class="mr-4px">
                    <ww-user :openid="item"></ww-user>
                  </span>
                </div>
              </template>
              <span v-for="item in corpUserId" :key="item" class="mr-4px">
                <ww-user :openid="item"></ww-user>
              </span>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column property="qrCodetypeName" label="类型" :min-width="100" />
        <el-table-column property="createor" label="创建人" :min-width="120">
          <template #default="{ row }">
            <ww-user :openid="row.creator" />
          </template>
        </el-table-column>
        <el-table-column property="createTime" label="创建时间 " :min-width="120" />
        <el-table-column property="qrCodeStatusName" label="状态 " :min-width="100" />
        <el-table-column fixed="right" class-name="el-table-fixed-column--shadow" label="操作" width="180">
          <template #default="{ row }">
            <el-text v-if="row.qrCodeStatus === 'enable'" class="cursor-pointer mr-10px" type="primary"
              @click="edit(row)">编辑</el-text>
            <el-text v-if="row.qrCodeStatus === 'enable'" class="cursor-pointer mr-10px" type="primary"
              @click="downloadImage(row)">下载</el-text>
            <el-text class="cursor-pointer mr-10px" type="primary" @click="toStatisticsView(row)">统计</el-text>
          </template>
        </el-table-column>
      </el-table>
    </layout-main>
    <layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize"
        @handle-current-change="changePage" />
    </layout-item>
  </my-layout>
  <live-qrcode-modal :visible="liveVisible" @close="liveVisible = false" @submit="getList" />

</template>
<script setup>
import { computed, onMounted, ref, watch } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import dayjs from "dayjs";
import { getStaffQrcode } from "@/api/knowledgeBase";
import useElPagination from "@/hooks/useElPagination";

import liveQrcodeModal from "./live-qrcode-modal.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";

const emits = defineEmits(['add', 'edit', 'download']);
const props = defineProps({
  cateIds: { type: Array, default: () => [] },
  qrcodeStatus: { type: Array, default: () => [] },
  statusMap: { type: Object, default: () => ({}) }
});
const router = useRouter();

const liveVisible = ref(false);
const loading = ref(false);
const searchForm = ref({});
const qrcodeTypes = [{ label: "单人码", value: 1 }, { label: "多人码", value: 2 }]
const typeMap = qrcodeTypes.reduce((acc, item) => (acc[item.value] = item.label, acc), {})
const { page, pageSize, changePage, changeSize } = useElPagination(getList);
const list = ref([]);
const total = ref(0);
const tableData = computed(() => {
  return list.value.map((item, index) => {
    item.createTime = item.createTime ? dayjs(item.createTime).format("YYYY-MM-DD") : "";
    item.qrCodetypeName = typeMap[item.qrCodetype];
    item.qrCodeStatusName = props.statusMap[item.qrCodeStatus];
    return { ...item, index: index + 1 }
  });
});

function add() {
  emits('add')
}

function downloadImage(row) {
  emits('download', row)
}

function edit(row) {
  emits('edit', row)
}


function search() {
  changePage(1)
}

function reset() {
  searchForm.value = {};
  changePage(1)
}

function toStatisticsView(item) {
  router.push({ name: "MARKETCQRCODEDETAIL", params: { id: item._id } });
}

async function getList() {
  loading.value = true;
  const params = { corpId: localStorage.getItem("corpId"), cateIds: props.cateIds };
  Object.keys(searchForm.value).forEach(key => {
    if (searchForm.value[key]) params[key] = searchForm.value[key];
  })
  const { data, success, message } = await getStaffQrcode(page.value, pageSize.value, params);
  loading.value = false;
  if (!success) {
    ElMessage.error(message);
    return;
  }
  list.value = data.data;
  total.value = data.total;
}
onMounted(getList)
watch(() => props.cateIds, () => changePage(1))

defineExpose({ refresh: getList })
</script>
<style lang="scss" scoped>
.query {
  .query__item {
    margin-bottom: 15px;
    margin-right: 10px;

    @at-root &:last-of-type {
      margin-left: auto;
      margin-right: 0;
    }
  }
}
</style>
