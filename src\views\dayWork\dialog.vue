<template>
  <el-dialog :model-value="props.visible" :title="form._id ? '编辑' : '新增'" :width="600" @close="close">
    <el-scrollbar wrap-style="max-height: calc(100vh - 360px)">
      <div>
        <el-form class="pr-10px pt-15px" :label-width="100" label-suffix="：">
          <el-row>
            <el-col :span="24">
              <el-form-item class="is-required" label="标题">
                <el-input v-model="form.title" placeholder="请输入标题" :maxlength="10">
                  <template #append>
                    <el-select v-model="form.workType" class="w-100px" placeholder="请选择" @change="changeWorkType">
                      <el-option v-for="item in WorkType" :key="item.value" :label="item.label" :value="item.value" />
                    </el-select>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item class="is-required" label="执行时间">
                <el-row class="w-full">
                  <el-col :span="12">
                    <el-date-picker v-model="workDate" class="workDate" format="YYYY-MM-DD" placeholder="请选择执行日期"
                      type="date" value-format="YYYY-MM-DD" :disabled-date="judgeDisabled" />
                  </el-col>
                  <el-col :span="12">
                    <el-time-picker v-model="workTime" format="HH:mm" class="workTime" placeholder="请选择执行时间"
                      value-format="HH:mm" />
                  </el-col>
                </el-row>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :class="form.workType === 'visit-reg' ? 'is-required' : ''"
                :label="form.workType === 'visit-reg' ? '就诊人' : '服务对象'">
                <el-select v-model="form.memberId" class="w-full" popper-class="auto-el-option-height" filterable remote
                  reserve-keyword :placeholder="form.workType === 'visit-reg' ? '请搜索就诊人' : '请搜索服务对象'" :disabled="disabled"
                  :remote-method="searchCustomer" :loading="searching" @change="changeCustomer">
                  <el-option-group :label="`找到${customerList.length}个客户`">
                    <el-option v-for="item in customerList" :key="item.value" :label="item.label" :value="item.value">
                      <div flex items-center>
                        <user-avatar flex-shrink-0 :sex="item.sex" />
                        <div flex-grow class="mx-10px customer-info">
                          <div>
                            <span font-16>{{ item.name }}</span>
                            <span color-666 font-12> {{ item.sex || "" }}</span>
                          </div>
                          <div font-14 color-666>
                            <el-icon class="mr-4px">
                              <CreditCard />
                            </el-icon>{{ maskIdNo(item.idCard) }}
                          </div>
                          <div font-14 color-666>
                            <el-icon class="mr-4px">
                              <Iphone />
                            </el-icon>{{ maskPhone(item.mobile) }}
                          </div>
                        </div>
                        <div flex-shrink-0>
                          <el-radio-group :model-value="form.memberId">
                            <el-radio :label="item._id">{{ " " }}</el-radio>
                          </el-radio-group>
                        </div>
                      </div>
                    </el-option>
                  </el-option-group>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- <template v-if="form.workType === 'schedule'">
              <el-col :span="12">
                <el-form-item label="执行人">
                  <ww-user v-if="form.userId" :openid="form.userId"></ww-user>
                </el-form-item>
              </el-col>
              
            </template> -->
            <template v-if="form.workType === 'visit-reg'">
              <!-- <el-col :span="12">
                <el-form-item class="is-required" label="就诊团队">
                  <el-select v-model="form.teamId" filterable placeholder="请选择就诊团队" :disabled="disabled"
                    @change="changeTeam">
                    <el-option v-for="item in showTeams" :key="item.teamId" :label="item.name" :value="item.teamId" />
                  </el-select>
                </el-form-item>
              </el-col> -->
              <el-col :span="24">
                <el-form-item class="is-required" label="就诊医生">
                  <el-select v-model="form.doctorUserId" class="el-select--hiddenValue" style="width: 100%" placeholder=""
                    :disabled="disabled" filterable>
                    <template #prefix>
                      <div class="h-30px" color-666>
                        <ww-user :openid="form.doctorUserId"></ww-user>
                      </div>
                    </template>
                    <el-option v-for="userId in teamMemberList" :key="userId" :value="userId">
                      <ww-user :openid="userId"></ww-user>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </template>
            <el-col :span="24">
              <el-form-item label="内容">
                <el-input v-model="form.workContent" type="textarea" placeholder="请输入内容"
                  :autosize="{ minRows: 4, maxRows: 8 }" :maxlength="200" />
              </el-form-item>
            </el-col>
            <el-col class="hidden" :span="24">
              <el-form-item label="转待办单">
                <div flex items-center>
                  <el-radio-group v-model="form.isTodo" :disabled="disabled">
                    <el-radio label="NO">否</el-radio>
                    <el-radio label="YES">是</el-radio>
                  </el-radio-group>
                  <el-select v-if="form.isTodo === 'YES'" v-model="form.advanceDay" class="ml-20px flex-grow"
                    :disabled="disabled" @change="changeAdvanceDay">
                    <el-option v-for="item in advanceDayList" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button class="w-100px" @click="close()">取消</el-button>
        <el-button class="w-100px" :loading="loading" type="primary" @click="save()">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
  <add-file ref="addFileRefs" @onGetSelectFile="getSelectFile"> </add-file>
  <survery-dialog :data="currentSurvery" :visible="visible" @close="visible = false" />
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { watchDebounced } from "@vueuse/core";
import dayjs from "dayjs";
import { getCustomerList } from "@/api/member";
import { setWork, getCorpMemberByUserId } from "@/api/corp";
import { WorkType, WeekDay } from "@/baseData";
import { maskIdNo, maskPhone } from "@/utils";
import { createServiceRecord } from "@/utils/service"
import WwUser from "@/components/ww-user/index.vue";
import UserAvatar from "@/components/user-avatar";
import addFile from "@/views/pannedEvent/components/addFile.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { memberStore } from "@/store/member";
import { teamStore } from "@/store/team";

const props = defineProps({
  data: { type: Object, default: () => ({}) },
  teams: { type: Array, default: () => [] },
  visible: { type: Boolean, default: false },
});

const emits = defineEmits(["close", "success", "show-survery"]);

function close() {
  emits("close");
}

const form = ref({});
const workDate = ref('');
const workTime = ref('');
const addFileRefs = ref("");
const pannedEventSendFile = ref({});
const disabled = computed(() => {
  //  有待办的日程 只允许编辑标题和内容
  if (form.value._id && form.value.isTodo === 'YES' && form.value.todoId) {
    return true
  }
  return false
})
function changeWorkType(value) {
  if (value === 'visit-reg' && (typeof form.value.title !== 'string' || form.value.title.trim() === '')) {
    form.value.title = '复诊预约'
  } else if (value === 'schedule' && form.value.title === '复诊预约') {
    form.value.title = ''
  }
}

function getSelectFile(file) {
  pannedEventSendFile.value = file;
}

async function readFile(item) {
  if (item.type === "questionnaire") {
    emits('show-survery', item.serveryId)
  }
}
// 删除附件
function removeDetele() {
  ElMessageBox.confirm("是否删除?").then(async () => {
    pannedEventSendFile.value = {};
  });
}
function choose(type) {
  addFileRefs.value.open(type, {});
}

const advanceDayList = [
  { label: "发生当天", value: 0 },
  { label: "提前一天", value: 1 },
  { label: "提前两天", value: 2 },
  { label: "提前一周", value: 7 },
];
function changeAdvanceDay(val) {
  const item = advanceDayList.find((i) => i.value === val);
  form.value.advanceDayStr = item ? item.label : "";
}

const customerList = ref([]);
const searchText = ref("");
const searching = ref(false);
const memberTeamIds = ref([]);

function changeCustomer(value) {
  const customer = customerList.value.find((i) => i.value === value);
  // const teamIds = customer && customer.teamId ? customer.teamId : "";
  // memberTeamIds.value = Array.isArray(teamIds)
  //   ? teamIds
  //   : teamIds
  //     ? [teamIds]
  //     : [];
  // form.value.teamId = memberTeamIds.value.some((i) => i === form.value.teamId)
  //   ? form.value.teamId
  //   : "";
  form.value.customerName = customer ? customer.label : "";
  form.value.externalUserId = customer ? customer.externalUserId : "";
  if (customer && form.value.workType === 'visit-reg' && form.value.title.includes("复诊预约")) {
    form.value.title = `${customer.label} 复诊预约`
  }
}

function searchCustomer(val) {
  if (val) {
    searching.value = true;
    searchText.value = val;
  }
}

async function getMembers(name) {
  const { data } = await getCustomerList({
    name
  }, 1, 9999);
  customerList.value = Array.isArray(data.data)
    ? data.data.map((i) => ({
      ...i,
      value: i._id,
      label: i.name,
    }))
    : [];
  return customerList.value;
}
watchDebounced(
  searchText,
  async () => {
    if (searchText.value) {
      await getMembers(searchText.value);
    }
    searching.value = false;
  },
  { debounce: 1000 }
);

const showTeams = computed(() => {
  let teams = props.teams.map((t) => ({ ...t }));
  if (form.value.workType === "schedule") {
    teams = teams.filter((i) =>
      i.memberList.includes(localStorage.getItem("userId"))
    );
  }
  if (memberTeamIds.value.length) {
    return teams.filter((i) =>
      memberTeamIds.value.some((id) => id == i.teamId)
    );
  }
  return teams;
});
const teamMemberList = computed(() => {
  return Object.keys(teamStore().currentTeam).length ? teamStore().currentTeam.memberList : []
});
function verify() {
  let message = "";
  if (!form.value.title || form.value.title.trim() === "") {
    message = "请输入标题";
  } else if (!form.value.workType) {
    message = "请选择日程类型";
  } else if (!workTime.value || !workDate.value) {
    message = "请选择执行时间";
  } else if (dayjs(`${workDate.value} ${workTime.value}`).isBefore(dayjs())) {
    message = "执行时间不能为过去时间";
  } else if (form.value.workType === 'visit-reg' && !form.value.memberId) {
    message = "请选择就诊人";
  }
  // else if (form.value.workType === "schedule" && !form.value.teamId) {
  //   message = "请选择执行团队";
  // } else if (form.value.workType === "visit-reg" && !form.value.teamId) {
  //   message = "请选择就诊团队";
  // } 
  else if (form.value.workType === "visit-reg" && !form.value.doctorUserId) {
    message = "请选择就诊医生";
  } else if (!form.value.isTodo) {
    message = "请选择是否转待办单";
  } else if (
    form.value.isTodo === "YES" &&
    !form.value.advanceDay &&
    form.value.advanceDay !== 0
  ) {
    message = "请选择待办生效时间";
  } else if (
    form.value.isTodo === "YES" &&
    dayjs(`${workDate.value} ${workTime.value}`).subtract(form.value.advanceDay, 'day').isBefore(dayjs())
  ) {
    message = "待办生效时间不能为过去时间";
  }
  message && ElMessage.info(message);
  return message ? false : true;
}

const loading = ref(false);
async function save() {
  if (verify()) {
    loading.value = true;
    form.value.workTime = `${workDate.value} ${workTime.value}`;
    if (form.value.isTodo === "YES") {
      const item = advanceDayList.find(
        (i) => i.value === form.value.advanceDay
      );
      form.value.advanceDayStr = item ? item.label : "";
      if (form.value.advanceDay > 0) {
        form.value.todoTime = dayjs(form.value.workTime)
          .subtract(form.value.advanceDay, "day")
          .set('hour', 7).set('minute', 0).set('second', 0)
          .valueOf();
      } else {
        form.value.todoTime = dayjs(form.value.workTime)
          .subtract(form.value.advanceDay, "day")
          .valueOf();
      }
    }
    form.value.workTimeStamp = dayjs(form.value.workTime).valueOf();
    const weekdayIndex = dayjs(form.value.workTime).day();
    const dateStr = dayjs(form.value.workTime).format(`YYYY年MM月DD日 ${WeekDay[weekdayIndex]} HH:mm`);
    form.value.taskContent = `${form.value.title}(日程时间：${dateStr}) \r\n${form.value.workContent ||''}`
    form.value.teamId = teamStore().currentTeamId;
    const { success, message } = await setWork(form.value);
    if (form.value.workType === 'visit-reg') {
      createServiceRecordAction();
    }
    if (success) {
      ElMessage.success(message);
      emits("success");
    } else {
      ElMessage.error(message);
    }
    loading.value = false;
  }
}

async function createServiceRecordAction() {
  const { memberInfo } = memberStore();
  const { anotherName } = memberInfo;

  const { data } = await getCorpMemberByUserId(form.value.doctorUserId);
  const doctorName = data.data[0].anotherName || ''
  let taskContent = `${anotherName}新增了一条就诊登记日程给${doctorName}医生`;
  const { currentTeam } = teamStore();
  const { teamId, name: teamName } = currentTeam;
  let item = {
    taskContent,
    executionTime: new Date().getTime(),
    customerId: form.value.memberId,
    executeTeamId: teamId,
    teamName,
    eventType: "visitRegistration",
    customerName: form.value.customerName
  }
  await createServiceRecord(item);
}

function judgeDisabled(date) {
  return dayjs(date).endOf('day').isBefore(dayjs())
}

watch(
  () => props.data,
  async (newData) => {
    const data = { ...newData, isTodo: newData.isTodo || "NO" };
    const file = { ...(newData.pannedEventSendFile || {}) }
    if (data.workTime) {
      workDate.value = dayjs(data.workTime).format('YYYY-MM-DD');
      workTime.value = dayjs(data.workTime).format('HH:mm:ss');
    } else {
      workDate.value = '';
      workTime.value = '';
    }
    if (data.memberId) {
      let member = customerList.value.find((i) => i._id === newData.memberId);
      if (!member) {
        await getMembers(newData.customerName);
        member = customerList.value.find((i) => i._id === newData.memberId);
      }
      data.memberId = member ? member._id : "";
      data.externalUserId = member ? member.externalUserId : "";
      data.customerName = member ? member.label : "";
    }
    form.value = data;
    pannedEventSendFile.value = file
  },
  { immediate: true }
);
</script>
<style>
.el-date-editor.workDate {
  width: calc(100% - 10px) !important;
}

.el-date-editor.workTime {
  width: 100%;
}

.el-select.el-select--hiddenValue .el-input__inner {
  display: none;
}

.el-select.el-select--hiddenValue .el-input__prefix {
  flex-grow: 1;
}

.auto-el-option-height .el-select-dropdown__item {
  height: auto;
}

.auto-el-option-height .customer-info {
  padding: 6px 0;
  line-height: 1.5em;
  color: rgba(0, 0, 0, 0.9);
  font-weight: normal;
}
</style>
