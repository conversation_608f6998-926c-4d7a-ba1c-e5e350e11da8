<template>
  <div bg-fff common-shadow>
    <el-tabs v-model="activeName" class="h-full doc-manage-tabs">
      <el-tab-pane v-for="tab in tabData" :key="tab.name" :label="tab.label" :name="tab.name">
        <el-scrollbar wrap-style="height:calc(100vh - 195px)">
          <div class="p-15px pt-0 select-none">
            <info-table :name="tab.name" :label="tab.label" :templates="tab.templateList"></info-table>
          </div>
        </el-scrollbar>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script setup>
import { ref, provide, computed, onMounted } from "vue";
import infoTable from "./components/info-table.vue";
import { templateStore } from "@/store/template";
import { storeToRefs } from "pinia";
const { getCorpTemplate, getPlatFormTemplate } = templateStore();
const { needChangeCorpTemplate, platformTemplateList } = storeToRefs(templateStore());
const activeName = ref("baseTemplate");
const tabs = [
  { label: "基础信息字段", name: "baseTemplate" },
  { label: "健康档案字段", name: "medicalRecord" },
  { label: "内部信息字段", name: "internalTemplate" },
];
onMounted(async () => {
  if (needChangeCorpTemplate.value.length === 0) {
    getCorpTemplate();
  }
  if (platformTemplateList.value.length === 0) {
    getPlatFormTemplate();
  }
});
// const templateList = computed(() => needChangeCorpTemplate.value.filter((item) => (item.parentTemplateType ? item.parentTemplateType === activeName.value : item.templateType === activeName.value)));
const tabData = computed(() => tabs.map(tab => {
  const list = needChangeCorpTemplate.value.filter(tpl => {
    if (tpl.parentTemplateType) {
      return tpl.parentTemplateType === tab.name
    }
    return tpl.templateType === tab.name
  })
  return { ...tab, templateList: list }

}))
provide("tips", ["客户档案模版字段，机构可根据需求自定义配置，特殊字段不允许修改。", "字段设置为启用，在系统中显示此字段和字段对应的数据。如果未启用则不显示。", "字段设置为必填，在系统中新增编辑时显示“*”，必须填写或选择后才能保存。", "您可以修改字段的显示名称。"]);

</script>

<style lang="scss" scoped>
:deep(.doc-manage-tabs .el-tabs__nav-scroll) {
  padding: 0 15px;
}

.doc-manage-tabs .tab-pane-height {
  height: calc(100vh - 195px);
  padding: 0 15px 15px;
}
</style>
