<template>
  <my-layout bg-fff>
    <layout-main>
      <div p-15>
        <el-form label-position="top">
          <el-form-item class="is-required" label="计划执行日期">
            <el-date-picker v-model="date" format="YYYY-MM-DD" placeholder="请选择执行日期" type="date"
              value-format="YYYY-MM-DD" :disabled-date="disabledPastDate" style="width: 100%;" />
          </el-form-item>
          <el-form-item class="is-required" label="所属团队">
            <el-select v-model="form.executeTeamId" filterable style="width: 100%;" placeholder="请选择所属团队">
              <el-option v-for="item in teams" :key="item.teamId" :label="item.name" :value="item.teamId" />
            </el-select>
          </el-form-item>
          <el-form-item class="is-required" label="待办类型">
            <el-select v-model="form.eventType" filterable style="width: 100%;" placeholder="请选择待办类型">
              <el-option v-for="item in eventTypeList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item class="is-required" label="跟进人">
            <ww-user-select :list="memberList" placeholder="请选择跟进人" :value="form.executorUserId"
              @change="change($event)" />
          </el-form-item>
          <el-form-item class="is-required" label="待跟进事项">
            <el-input v-model="form.taskContent" maxlength="100" :autosize="{ minRows: 5, maxRows: 8 }" resize="none"
              show-word-limit type="textarea" placeholder="请输入待跟进事项" />
          </el-form-item>
        </el-form>
      </div>
    </layout-main>
    <layout-item>
      <div class="py-12px px-15px text-center flex" common-shadow--r>
        <el-button class="w-100px flex-grow" plain @click="close()">取消</el-button>
        <el-button class="w-100px flex-grow" type="primary" :loading="loading" @click="confirm()">确定</el-button>
      </div>
    </layout-item>
  </my-layout>
</template>
<script setup>
import { computed, inject, onMounted, ref, toRefs, watch } from "vue";
import { storeToRefs } from "pinia";
import { ElMessage } from "element-plus";
import dayjs from 'dayjs';
import { createTodo } from '@/api/todo';
import { ToDoEventType } from '@/baseData';
import { memberStore } from "@/store/member";
import MyLayout, { LayoutMain } from "@/components/layout";
import WwUserSelect from "@/components/ww-user-select";
const emits = defineEmits(["change", "close"]);
const trigger = inject("side-bar-event-trigger");
const { memberInfo } = storeToRefs(memberStore());
const props = defineProps({
  customer: { type: Object, default: () => ({}) },
  teams: { type: Array, default: () => [] },
});
const { customer, teams } = toRefs(props);
const eventTypeList = Object.keys(ToDoEventType).map(key => ({ label: ToDoEventType[key], value: key }))

const form = ref({});
const date = ref("");
const memberList = computed(() => {
  const team = teams.value.find((i) => i.teamId === form.value.executeTeamId);
  return team && Array.isArray(team.memberList) ? team.memberList : [];
});

function disabledPastDate(e) {
  return dayjs().startOf("day").isAfter(dayjs(e));
}

function change(value) {
  form.value.executorUserId = value;
}

function close() {
  emits("close");
}
const loading = ref(false);
function confirm() {
  if (date.value === "") {
    ElMessage.info("请选择执行日期");
  } else if (!form.value.executeTeamId) {
    ElMessage.info('请选择所属团队')
  } else if (!form.value.eventType || !ToDoEventType[form.value.eventType]) {
    ElMessage.info('请选择待办类型')
  } else if (!form.value.executorUserId) {
    ElMessage.info("请选择跟进人");
  } else if (
    typeof form.value.taskContent !== "string" ||
    !form.value.taskContent.trim() === ""
  ) {
    ElMessage.info("请输入待跟进事项");
  } else {
    add();
  }
}

async function add() {
  if (loading.value) return;
  loading.value = true;
  const time = dayjs(date.value).isAfter(dayjs().endOf("day"))
    ? dayjs(dayjs(date.value).format("YYYY-MM-DD 07:00:00"))
    : dayjs();
  const todo = {
    corpId: localStorage.getItem("corpId"),
    customerId: customer.value._id,
    customerName: customer.value.name,
    expireTime: dayjs(date.value).add(7, "day").valueOf(),
    plannedExecutionTime: time.valueOf(),
    externalUserId: customer.value.externalUserId,
    taskContent: form.value.taskContent.trim(),
    creatorUserId: localStorage.getItem('userId'),
    eventStatus: 'untreated',
    eventType: form.value.eventType,
    executorUserId: form.value.executorUserId,
    executeTeamId: form.value.executeTeamId,
  };
  const { success, message } = await createTodo(todo);
  if (success) {
    ElMessage.success("新增跟进事项成功");
    setTimeout(
      () => trigger.$invoke("update-follow-up", form.value.executeTeamId),
      1000
    );
    close();
  } else {
    ElMessage.error(message);
  }
  loading.value = false;
}

onMounted(() => {
  if (teams.value.length === 1) {
    form.value.executeTeamId = teams.value[0].teamId;
    if (memberList.value.some((i) => i === memberInfo.value.userid)) {
      form.value.executorUserId = memberInfo.value.userid;
    } else if (memberList.value.length === 1)
      form.value.executorUserId = memberList.value[0];
  }
});
watch(
  () => form.value.executeTeamId,
  () => {
    if (
      form.value.executorUserId &&
      !memberList.value.some((i) => i === form.value.executorUserId)
    ) {
      form.value.executorUserId = "";
    }
  }
);
</script>
<style lang="scss" scoped></style>
