<template>
  <el-dialog v-model="dialogVisible" :title="operateType === 'add' ? '新增服务记录' : '编辑服务记录'" draggable :width="600">
    <el-form class="pt-20px" :rules="rules" :model="form" ref="addServiceRef" :label-width="100">
      <template v-if="form.eventType === 'serviceSummary' || operateType === 'add'">
        <el-form-item label="处理时间" prop="executionTime">
          <div class="flex">
            <el-date-picker style="width: 190px" v-model="workDate" class="workDate mr-20px" format="YYYY-MM-DD"
              placeholder="请选择执行日期" type="date" value-format="YYYY-MM-DD" :disabled-date="judgeDisabled" disabled />
            <el-time-picker style="width: 190px" v-model="workTime" format="HH:mm" class="workTime"
              placeholder="请选择执行时间" value-format="HH:mm" :disabled="operateType === 'edit'" />
          </div>
        </el-form-item>
        <el-form-item v-if="!member" label="服务对象" prop="memberId">
          <el-select class="w-400px" v-model="form.customerName" popper-class="auto-el-option-height" filterable remote
            reserve-keyword placeholder="请搜索服务对象" :remote-method="searchCustomer" :loading="searching"
            @change="changeCustomer" :disabled="operateType === 'edit'">
            <el-option-group :label="`找到${customerList.length}个客户`">
              <el-option v-for="item in customerList" :key="item.value" :label="item.label" :value="item.value">
                <div flex items-center>
                  <user-avatar flex-shrink-0 :sex="item.sex" />
                  <div flex-grow class="mx-10px customer-info">
                    <div>
                      <span font-16>{{ item.name }}</span>
                      <span color-666 font-12>{{ item.sex || "" }}</span>
                    </div>
                    <div font-14 color-666>
                      <el-icon class="mr-4px">
                        <CreditCard />
                      </el-icon>
                      {{ maskIdNo(item.idCard) }}
                    </div>
                    <div font-14 color-666>
                      <el-icon class="mr-4px">
                        <Iphone />
                      </el-icon>
                      {{ maskPhone(item.mobile) }}
                    </div>
                  </div>
                  <div flex-shrink-0>
                    <el-radio-group :model-value="form.memberId">
                      <el-radio :label="item._id">{{ " " }}</el-radio>
                    </el-radio-group>
                  </div>
                </div>
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item class="is-required" label="服务类型">
          <el-select v-model="form.eventType" filterable placeholder="请选择服务类型" class="w-400px"
            :disabled="operateType === 'edit'">
            <el-option v-for="item in serviceTypeList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </template>
      <el-form-item label="服务内容" prop="taskContent">
        <el-input class="w-400px" v-model="form.taskContent" maxlength="200" :autosize="{ minRows: 5, maxRows: 8 }"
          resize="none" show-word-limit type="textarea" placeholder="请输入服务记录内容" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer text-center">
        <el-button class="w-100px" @click="dialogVisible = false">取消</el-button>
        <!-- <el-button v-else-if="operateType === 'edit'" class="w-100px" @click="remove()">删除</el-button> -->
        <el-button class="w-100px" type="primary" :loading="loading" @click="dialogSubmit(addServiceRef)">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, inject } from "vue";
import { watchDebounced } from "@vueuse/core";
import { getCorpTeams } from "@/api/corp";
import { getCustomerList } from "@/api/member";
import { addServiceRecord, updateServiceRecord, removeRecord } from "@/api/todo";
import { maskIdNo, maskPhone } from "@/utils";
import dayjs from "dayjs";
import { teamStore } from "@/store/team";
import { ElMessage, ElMessageBox } from "element-plus";
import { ToDoEventType } from "@/baseData";
let addServiceRef = ref("");
let form = ref({});
let dialogVisible = ref(false);
let workDate = ref("");
let workTime = ref("");
let member = ref({});
let operateType = ref("");
const loading = ref(false)

const notifyCustomerServiceTime = inject("notifyCustomerServiceTime");
const serviceTypeList = Object.keys(ToDoEventType).map((key) => ({ label: ToDoEventType[key], value: key }));
watch(
  () => workDate.value,
  () => {
    if (workDate.value && workTime.value) form.value.executionTime = `${workDate.value}  ${workTime.value}`;
  },
  { immediate: false }
);

watch(
  () => workTime.value,
  () => {
    if (workDate.value && workTime.value) form.value.executionTime = `${workDate.value}  ${workTime.value}`;
  },
  { immediate: false }
);

let rules = reactive({
  taskContent: [{ required: true, message: "请输入服务记录内容", trigger: "blur" }],
  executionTime: [{ required: true, message: "请选择执行时间", trigger: "blur" }],
  // executeTeamId: [
  //     { required: true, message: "请选择服务团队", trigger: "blur" }
  // ],
  memberId: [{ required: true, message: "请选择执行人", trigger: "blur" }],
});
const emits = defineEmits(["save", "remove"]);
function open(type, customer, item) {
  dialogVisible.value = true;
  member.value = customer;
  if (type === "add") {
    operateType.value = "add";
    workDate.value = dayjs().format("YYYY-MM-DD");
    workTime.value = "";
    form.value = {
      taskContent: "",
      executionTime: "",
      customerName: customer ? customer.name : "",
      teamName: "",
      memberId: customer ? customer._id : "",
      eventType: "",
      customerUserId: customer ? customer.externalUserId : "",
    };
  } else {
    const [date, time] = item.executionTime && item.executionTime.split(/\s+/);
    workDate.value = date;
    workTime.value = time;
    operateType.value = "edit";
    form.value = item;
    form.value.memberId = item.customerId;
  }
}
function judgeDisabled(date) {
  return dayjs(date).isAfter(dayjs().endOf("day"));
}
async function dialogSubmit(formEl) {
  if (!formEl || loading.value) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      loading.value = true;
      await addRecord();
      loading.value = false;
    }
  });
}
async function addRecord() {
  const { taskContent, executionTime, customerName, memberId, customerUserId, eventType } = form.value;
  const { currentTeam } = teamStore();
  const { teamId, name: teamName } = currentTeam;
  let params = {
    taskContent,
    customerUserId,
    eventType,
    customerName,
  };
  if (operateType.value === "add") {
    params.corpId = localStorage.getItem("corpId");
    params.creatorUserId = localStorage.getItem("userId"); // 创建人
    params.executorUserId = localStorage.getItem("userId"); // 执行人
    params.executionTime = dayjs(executionTime).valueOf();
    params.customerId = memberId;
    params.executeTeamId = teamId;
    params.teamName = teamName;
  }
  let { success } = form.value._id ? await updateServiceRecord(form.value._id, params) : await addServiceRecord(params);
  if (!success) return;
  if (success && operateType.value === "add" && typeof notifyCustomerServiceTime === "function") {
    notifyCustomerServiceTime(memberId, dayjs(executionTime).valueOf());
  }
  dialogVisible.value = false;
  emits("save");
}
/************ start 选择服务人 ************/
const customerList = ref([]);
const searchText = ref("");
const searching = ref(false);
const memberTeamIds = ref([]);

function searchCustomer(val) {
  if (val) {
    searching.value = true;
    searchText.value = val;
  }
}
watchDebounced(
  searchText,
  async () => {
    if (searchText.value) {
      await getMembers(searchText.value);
    }
    searching.value = false;
  },
  { debounce: 1000 }
);

async function getMembers(name) {
  const { data } = await getCustomerList(
    {
      name,
    },
    1,
    9999
  );
  customerList.value = Array.isArray(data.data)
    ? data.data.map((i) => ({
      ...i,
      value: i._id,
      label: i.name,
    }))
    : [];
  return customerList.value;
}
function changeCustomer(value) {
  const customer = customerList.value.find((i) => i.value === value);
  const teamIds = customer && customer.teamId ? customer.teamId : "";
  memberTeamIds.value = Array.isArray(teamIds) ? teamIds : teamIds ? [teamIds] : [];
  form.value.memberId = value;
  form.value.customerName = customer ? customer.label : "";
  form.value.externalUserId = customer ? customer.externalUserId : "";
}
/************ end 选择服务人 ************/

/****************start 服务团队 *****************/
const teamList = ref([]);
getTeams();
const showTeams = computed(() => {
  let teams = teamList.value.map((t) => ({ ...t }));
  return teams;
});
// function changeTeam(val) {
//     const team = showTeams.value.find((i) => i.teamId === val);
//     form.value.teamName = team ? team.name : "";
//     form.value.executeTeamId = team ? team.teamId : "";
// }

async function getTeams() {
  const { data } = await getCorpTeams(1, 9999, localStorage.getItem("corpId"), "", localStorage.getItem("userId"));
  teamList.value = Array.isArray(data.list) ? data.list : [];
}
/****************end 服务团队 *****************/

// function remove() {
async function remove(id) {
  if (!form.value._id) return;
  ElMessageBox.confirm("是否删除该服务记录?", "提示", { type: "warning" }).then(async () => {
    let { success, message } = await removeRecord(form.value._id);
    if (success) {
      ElMessage.success("删除成功");
      dialogVisible.value = false;
      emits("remove");
    } else {
      ElMessage.error(message);
    }
  });
}
// }

defineExpose({
  open,
});
</script>

<style></style>