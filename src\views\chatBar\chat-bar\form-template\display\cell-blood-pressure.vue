<template>
  <div class="w-full">
    <div v-if="data.bloodPressureDate" class="py-6px border-b px-10px border-gray-200">
      <div class="mb-5px text-12px font-semibold leading-18px">{{ data.bloodPressureDate }}</div>
      <div class="text-12px text-gray-500">测量日期</div>
    </div>
    <div v-if="data.systolicPressure" class="py-6px border-b px-10px border-gray-200">
      <div class="mb-6px text-16px font-semibold leading-18px">
        {{ data.systolicPressure }}
        <span v-if="data.systolicPressure" class="text-12px">(mmHg)</span>
      </div>
      <div class="text-12px text-gray-500">收缩压</div>
    </div>
    <div v-if="data.diastolicPressure" class="pt-6px px-10px">
      <div class="mb-6px text-16px font-semibold leading-18px">
        {{ data.diastolicPressure }}
        <span v-if="data.diastolicPressure" class="text-12px">(mmHg)</span>
      </div>
      <div class="text-12px text-gray-500">舒张压</div>
    </div>
  </div>

</template>
<script setup>
import { computed } from "vue";
import dayjs from "dayjs";

const props = defineProps({
  form: {
    type: Object,
    default: {}
  }
})

const data = computed(() => {
  const systolicPressure = checkInteger(props.form['systolicPressure'], 40, 250) ? props.form['systolicPressure'] : '';
  const diastolicPressure = checkInteger(props.form['diastolicPressure'], 30, 200) ? props.form['diastolicPressure'] : '';
  const bloodPressureDate = props.form['bloodPressureDate'] && dayjs(props.form['bloodPressureDate']).isValid() ? props.form['bloodPressureDate'] : '';
  return { systolicPressure, diastolicPressure, bloodPressureDate }
})

function checkInteger(val) {
  return val > 0 && val % 1 === 0;
}

</script>