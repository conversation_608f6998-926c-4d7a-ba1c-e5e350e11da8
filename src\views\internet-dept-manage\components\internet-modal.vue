<template>
  <el-dialog 
    :model-value="visible" 
    :width="width" 
    :title="getTitle()" 
    @close="close()"
  >
    <el-scrollbar wrap-class="el-dialog-body-full-scroll">
      <el-form class="p-15px" label-suffix="：" :label-width="100" ref="formRef" :model="form" :rules="rules">
        <!-- 院区表单 -->
        <template v-if="item.type === 'district'">
          <el-form-item class="is-required" label="院区名称" prop="districtName">
            <el-input v-model.trim="form.districtName" :maxlength="20" placeholder="请输入院区名称" />
          </el-form-item>
          <el-form-item class="is-required" label="院区ID" prop="districtId">
            <div class="flex-grow">
              <el-input v-model="form.districtId" placeholder="请输入院区ID，保持唯一性" :disabled="!!item._id" />
              <!-- <div class="text-12px text-gray-500 mt-12px">院区ID可以为数字或者字母组合，请勿重复</div> -->
            </div>
          </el-form-item>
          <el-form-item label="院区介绍" prop="districtDesc">
            <el-input 
              type="textarea" 
              v-model="form.districtDesc" 
              placeholder="请输入院区介绍" 
              maxlength="500"
              :autosize="{ minRows: 6, maxRows: 10 }" 
              resize="none" 
              show-word-limit
            />
          </el-form-item>
        </template>
        
        <!-- 科室表单 -->
        <template v-else>
          <el-form-item class="is-required" label="科室名称" prop="deptName">
            <el-input v-model.trim="form.deptName" :maxlength="20" placeholder="请输入科室名称" />
          </el-form-item>
          <el-form-item class="is-required" label="上一级节点" prop="parentId">
            <el-cascader
              v-model="form.parentPath"
              :options="cascaderOptions"
              :props="cascaderProps"
              placeholder="请选择上一级节点"
              clearable
              :disabled="isParentSelectDisabled"
              style="width: 100%"
              @change="handleParentNodeChange"
              :show-all-levels="false"
              :collapse-tags="false"
              filterable
              popper-class="custom-cascader-popper"
            />
          </el-form-item>
          <el-form-item class="is-required" label="科室ID" prop="deptId">
            <div class="flex-grow">
              <el-input v-model="form.deptId" placeholder="请输入科室ID" :disabled="!!item._id" />
              <div class="text-12px text-gray-500 mt-12px">科室ID可以为数字或者字母组合，请勿重复</div>
            </div>
          </el-form-item>
          <el-form-item label="科室介绍" prop="deptDesc">
            <el-input 
              type="textarea" 
              v-model="form.deptDesc" 
              placeholder="请输入科室介绍" 
              maxlength="500"
              :autosize="{ minRows: 6, maxRows: 10 }" 
              resize="none" 
              show-word-limit
            />
          </el-form-item>
        </template>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button plain class="w-100px" @click="close()">取消</el-button>
        <el-button class="w-100px" :loading="loading" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { computed, ref, watch, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { 
  addDistrict, 
  addDept, 
  updateDistrict, 
  updateDept,
  getInternetDeptList,
  checkAreaIdExists,
  checkHlwDeptIdExists
} from "@/api/internet-dept-manage";

const emits = defineEmits(["close", "change"]);
const props = defineProps({
  item: { type: Object, default: () => ({}) },
  visible: { type: Boolean, default: false },
  width: { type: [String, Number], default: "600px" },
});

const formRef = ref();
const form = ref({
  type: '',
  districtName: '',
  districtId: '',
  districtDesc: '',
  deptName: '',
  parentId: '',
  parentPath: [], // 级联选择器的路径
  deptId: '',
  deptDesc: '',
  areaId: '' // 院区ID字段
});
const loading = ref(false);

// 级联选择器选项
const cascaderOptions = ref([]);

// 动态计算级联选择器配置
const cascaderProps = computed(() => {
  // 如果是编辑操作且当前节点存在，使用简单模式（不需要级联）
  if (props.item._id && props.item.level) {
    return {
      checkStrictly: true,
      value: '_id', // 级联选择器内部仍使用_id作为value
      label: 'name',
      children: 'children',
      emitPath: false,
      // 编辑时禁用级联，直接显示平铺选项
      multiple: false,
      leaf: true
    };
  }

  // 新增操作使用完整级联模式
  return {
    checkStrictly: true,
    value: '_id', // 级联选择器内部仍使用_id作为value
    label: 'name',
    children: 'children',
    emitPath: true // 为了获取完整路径，设置为true
  };
});

// 父节点选项（院区和科室）- 保留用于构建级联数据
const parentNodeOptions = ref([]);

// 检查选择框是否应该禁用
const isParentSelectDisabled = computed(() => {
  // 在新的需求下，编辑时也允许选择同级的父节点，所以不禁用
  return false;
});

// 获取所有可能的父节点选项并构建级联结构
async function fetchParentNodeOptions() {
  try {
    const { data, success } = await getInternetDeptList();
    if (success) {
      // 构建树形结构用于级联选择器
      const districts = data.list.filter(item => item.type === 'district');
      const depts = data.list.filter(item => item.type === 'dept');
      
      // 保存原始数据用于构建同级选择逻辑
      parentNodeOptions.value = [...districts, ...depts];
      
      // 根据当前编辑的节点层级，构建相应的级联选择器选项
      buildCascaderOptions(districts, depts);
    }
  } catch (error) {
    ElMessage.error('获取父节点列表失败');
  }
}

// 构建父节点的完整路径（用于级联选择器显示）
function buildParentPath(parentId) {
  if (!parentId || !parentNodeOptions.value.length) {
    return [];
  }

  const path = [];
  let currentId = parentId;



  // 向上递归查找父节点，构建完整路径
  while (currentId) {
    // 查找当前节点，需要兼容不同的ID字段
    let currentNode = parentNodeOptions.value.find(item => item._id === currentId);

    // 如果通过_id没找到，尝试通过业务ID查找（针对科室）
    if (!currentNode) {
      currentNode = parentNodeOptions.value.find(item =>
        item.deptId === currentId ||
        item.hlwDeptId === currentId ||
        item.districtId === currentId ||
        item.areaId === currentId
      );
    }

    if (!currentNode) {
      break;
    }

    // 将当前节点的_id添加到路径的开头（级联选择器使用_id作为value）
    path.unshift(currentNode._id);

    // 继续查找上一级父节点
    // 对于科室，parentId可能是业务ID，需要转换为MongoDB ID
    if (currentNode.parentId) {
      // 如果parentId是业务ID，需要找到对应的MongoDB ID
      const parentByBusinessId = parentNodeOptions.value.find(item =>
        item.deptId === currentNode.parentId ||
        item.hlwDeptId === currentNode.parentId ||
        item.districtId === currentNode.parentId ||
        item.areaId === currentNode.parentId
      );

      if (parentByBusinessId) {
        currentId = parentByBusinessId._id;
      } else {
        // 如果找不到，可能parentId就是MongoDB ID
        currentId = currentNode.parentId;
      }
    } else {
      currentId = null;
    }
  }


  return path;
}

// 构建级联选择器选项（根据当前节点层级限制选择范围）
function buildCascaderOptions(districts, depts) {
  // 如果是新增操作，显示完整的级联结构
  if (!props.item._id) {
    cascaderOptions.value = buildFullCascaderOptions(districts, depts);
    return;
  }
  
  // 如果是编辑操作，根据当前节点的层级限制选择范围
  const currentLevel = props.item.level;

  if (currentLevel === 1) {
    // 一级科室只能选择院区作为父节点
    cascaderOptions.value = districts.map(district => ({
      _id: district._id,
      name: district.districtName,
      type: 'district',
      level: 0, // 院区是0级
      // 保留业务ID信息
      districtId: district.districtId,
      areaId: district.areaId
    }));
  } else if (currentLevel === 2) {
    // 二级科室只能选择一级科室作为父节点
    const level1Depts = depts.filter(dept => dept.level === 1);
    cascaderOptions.value = level1Depts.map(dept => ({
      _id: dept._id,
      name: dept.deptName,
      type: 'dept',
      level: 1, // 一级科室是1级
      // 保留业务ID信息
      deptId: dept.deptId,
      hlwDeptId: dept.hlwDeptId,
      parentId: dept.parentId
    }));
  } else if (currentLevel === 3) {
    // 三级科室只能选择二级科室作为父节点
    const level2Depts = depts.filter(dept => dept.level === 2);
    cascaderOptions.value = level2Depts.map(dept => ({
      _id: dept._id,
      name: dept.deptName,
      type: 'dept',
      level: 2, // 二级科室是2级
      // 保留业务ID信息
      deptId: dept.deptId,
      hlwDeptId: dept.hlwDeptId,
      parentId: dept.parentId
    }));
  } else {
    // 默认情况显示完整级联结构
    cascaderOptions.value = buildFullCascaderOptions(districts, depts);
  }
}

// 查找正确的areaId（院区ID）
function findCorrectAreaId(parentId) {
  if (!parentId || !parentNodeOptions.value.length) {
    return null;
  }

  // 查找父节点
  let parentNode = parentNodeOptions.value.find(item => item._id === parentId);

  // 如果通过_id没找到，尝试通过业务ID查找
  if (!parentNode) {
    parentNode = parentNodeOptions.value.find(item =>
      item.deptId === parentId ||
      item.hlwDeptId === parentId
    );
  }

  if (!parentNode) {
    return null;
  }

  // 如果父节点是院区，直接返回院区的ID
  if (parentNode.type === 'district') {
    return parentNode.districtId || parentNode.areaId || parentNode._id;
  }

  // 如果父节点是科室，需要向上查找院区
  let currentNode = parentNode;
  while (currentNode && currentNode.type === 'dept') {
    // 查找当前科室的父节点
    let parentOfCurrent = parentNodeOptions.value.find(item => item._id === currentNode.parentId);

    // 如果通过_id没找到，尝试通过业务ID查找
    if (!parentOfCurrent && currentNode.parentId) {
      parentOfCurrent = parentNodeOptions.value.find(item =>
        item.deptId === currentNode.parentId ||
        item.hlwDeptId === currentNode.parentId
      );
    }

    if (!parentOfCurrent) {
      break;
    }

    // 如果找到院区，返回院区ID
    if (parentOfCurrent.type === 'district') {
      return parentOfCurrent.districtId || parentOfCurrent.areaId || parentOfCurrent._id;
    }

    // 继续向上查找
    currentNode = parentOfCurrent;
  }

  return null;
}

// 构建完整的级联选择器选项（用于新增操作）
function buildFullCascaderOptions(districts, depts) {
  return districts.map(district => {
    const districtNode = {
      _id: district._id,
      name: district.districtName,
      type: 'district',
      level: 0, // 院区是0级
      // 保留业务ID信息
      districtId: district.districtId,
      areaId: district.areaId,
      children: []
    };
    
    // 找到该院区下的一级科室（level 1）
    const level1Depts = depts.filter(dept => {
      if (dept.level !== 1) return false;

      // 支持多种关联方式：业务ID优先，兼容MongoDB ID
      return dept.parentId === district.districtId ||
             dept.parentId === district.areaId ||
             dept.parentId === district._id ||
             dept.areaId === district.districtId ||
             dept.areaId === district.areaId;
    });
    
    districtNode.children = level1Depts.map(dept1 => {
      const dept1Node = {
        _id: dept1._id,
        name: dept1.deptName,
        type: 'dept',
        level: 1, // 一级科室是1级
        // 保留业务ID信息
        deptId: dept1.deptId,
        hlwDeptId: dept1.hlwDeptId,
        parentId: dept1.parentId,
        children: []
      };
      
      // 找到该一级科室下的二级科室（level 2）
      // 需要通过父科室的业务ID来查找子科室
      const level2Depts = depts.filter(dept => {
        if (dept.level !== 2) return false;

        // 子科室的parentId应该匹配父科室的业务ID
        return dept.parentId === dept1.deptId ||
               dept.parentId === dept1.hlwDeptId ||
               dept.parentId === dept1._id; // 兼容旧的_id方式
      });


      
      // 显示二级科室
      dept1Node.children = level2Depts.map(dept2 => {
        const dept2Node = {
          _id: dept2._id,
          name: dept2.deptName,
          type: 'dept',
          level: 2, // 二级科室是2级
          // 保留业务ID信息
          deptId: dept2.deptId,
          hlwDeptId: dept2.hlwDeptId,
          parentId: dept2.parentId,
          children: []
        };

        // 找到该二级科室下的三级科室（level 3）
        const level3Depts = depts.filter(dept => {
          if (dept.level !== 3) return false;

          // 三级科室的parentId应该匹配二级科室的业务ID
          return dept.parentId === dept2.deptId ||
                 dept.parentId === dept2.hlwDeptId ||
                 dept.parentId === dept2._id; // 兼容旧的_id方式
        });

        // 添加三级科室
        dept2Node.children = level3Depts.map(dept3 => ({
          _id: dept3._id,
          name: dept3.deptName,
          type: 'dept',
          level: 3, // 三级科室是3级
          // 保留业务ID信息
          deptId: dept3.deptId,
          hlwDeptId: dept3.hlwDeptId,
          parentId: dept3.parentId
          // 三级科室作为最后一级，不再添加children
        }));

        return dept2Node;
      });
      
      return dept1Node;
    });
    
    return districtNode;
  });
}

// 处理级联选择器变化
function handleParentNodeChange(value) {
  // value 可能是单个ID或者路径数组，取最后一个作为父节点ID
  let selectedNodeId = '';
  if (Array.isArray(value) && value.length > 0) {
    selectedNodeId = value[value.length - 1];
  } else if (typeof value === 'string') {
    selectedNodeId = value;
  }

  if (!selectedNodeId) {
    form.value.parentId = '';
    return;
  }

  // 查找选中的节点，确定正确的parentId
  const selectedNode = parentNodeOptions.value.find(item => item._id === selectedNodeId);
  if (selectedNode) {
    // 根据节点类型选择正确的ID作为parentId
    if (selectedNode.type === 'district') {
      // 院区：使用_id作为parentId
      form.value.parentId = selectedNode._id;
    } else if (selectedNode.type === 'dept') {
      // 科室：使用MongoDB _id作为parentId，确保树形结构能正确匹配父子关系
      form.value.parentId = selectedNode._id;
    } else {
      // 默认使用_id
      form.value.parentId = selectedNode._id;
    }
  } else {
    form.value.parentId = selectedNodeId;
  }
}

// 验证院区ID唯一性
const validateAreaId = async (rule, value, callback) => {
  if (!value) {
    callback();
    return;
  }
  
  try {
    const result = await checkAreaIdExists(value, props.item._id);
    if (result.success && result.data?.exists) {
      callback(new Error('院区ID已存在，请使用其他ID'));
    } else {
      callback();
    }
  } catch (error) {
    callback();
  }
};

// 验证科室ID唯一性
const validateDeptId = async (rule, value, callback) => {
  if (!value) {
    callback();
    return;
  }
  
  try {
    const result = await checkHlwDeptIdExists(value, props.item._id);
    if (result.success && result.data?.exists) {
      callback(new Error('科室ID已存在，请使用其他ID'));
    } else {
      callback();
    }
  } catch (error) {
    callback();
  }
};

// 表单验证规则
const rules = computed(() => {
  const baseRules = {};
  
  if (props.item.type === 'district') {
    baseRules.districtName = [
      { required: true, message: "请输入院区名称", trigger: "blur" },
      { min: 1, max: 20, message: "院区名称长度在 1 到 20 个字符", trigger: "blur" }
    ];
    baseRules.districtId = [
      { required: true, message: "请输入院区ID", trigger: "blur" },
      { pattern: /^[a-zA-Z0-9]+$/, message: "院区ID只能包含字母和数字", trigger: "blur" },
      { validator: validateAreaId, trigger: "blur" }
    ];
  } else {
    baseRules.deptName = [
      { required: true, message: "请输入科室名称", trigger: "blur" },
      { min: 1, max: 20, message: "科室名称长度在 1 到 20 个字符", trigger: "blur" }
    ];
    baseRules.parentId = [
      { required: true, message: "请选择上一级节点", trigger: "change" }
    ];
    baseRules.deptId = [
      { required: true, message: "请输入科室ID", trigger: "blur" },
      { pattern: /^[a-zA-Z0-9]+$/, message: "科室ID只能包含字母和数字", trigger: "blur" },
      { validator: validateDeptId, trigger: "blur" }
    ];
  }
  
  return baseRules;
});

// 获取标题
function getTitle() {
  if (props.item.type === 'district') {
    return props.item._id ? '编辑院区' : '新增院区';
  } else {
    return props.item._id ? '编辑科室' : '新增科室';
  }
}

// 获取父节点名称（用于新增科室时的提示）
function getParentNodeName() {
  if (props.item.parentName) {
    return props.item.parentName;
  }
  // 如果没有parentName，尝试从parentNodeOptions中查找
  const parentNode = parentNodeOptions.value.find(node => node._id === props.item.parentId);
  return parentNode ? parentNode.name : '上级节点';
}

// 关闭弹窗
function close() {
  emits("close");
}

// 确认提交
async function confirm() {
  if (loading.value) return;

  try {
    await formRef.value.validate();
  } catch (error) {
    console.log('表单验证失败:', error);
    return;
  }
  
  loading.value = true;
  
  try {
    let result;
    
    if (props.item._id) {
      // 更新操作
      if (form.value.type === 'district') {
        result = await updateDistrict(props.item._id, form.value);
      } else {
        result = await updateDept(props.item._id, form.value);
      }
    } else {
      // 新增操作
      if (form.value.type === 'district') {
        result = await addDistrict(form.value);
      } else {
        // 构建科室提交数据，只包含科室相关字段
        const deptData = {
          hlwDeptName: form.value.deptName || '',
          hlwDeptId: form.value.deptId || '',
          description: form.value.deptDesc || '',
          status: 1
        };

        // 处理parentId：如果父节点是科室，需要转换为业务ID
        if (form.value.parentId) {
          const parentNode = parentNodeOptions.value.find(item => item._id === form.value.parentId);
          if (parentNode) {
            if (parentNode.type === 'district') {
              // 父节点是院区，不设置parentId（科室直接在院区下）
              // parentId留空，表示这是一级科室
            } else if (parentNode.type === 'dept') {
              // 父节点是科室，使用父科室的业务ID
              deptData.parentId = parentNode.deptId || parentNode.hlwDeptId;
            }
          }
        }

        // 查找正确的areaId
        const correctAreaId = findCorrectAreaId(form.value.parentId);
        if (correctAreaId) {
          deptData.areaId = correctAreaId;
        } else {
          console.error('未找到正确的areaId，这可能导致"指定的院区不存在"错误');
        }

        // 验证：确保areaId不等于parentId
        if (deptData.areaId === deptData.parentId) {
          console.error('检测到areaId等于parentId，这是错误的！');
          delete deptData.areaId;
        }

        // 验证必填字段
        if (!deptData.hlwDeptName) {
          ElMessage.error('科室名称不能为空');
          return;
        }
        if (!deptData.hlwDeptId) {
          ElMessage.error('科室ID不能为空');
          return;
        }

        result = await addDept(deptData);
      }
    }
    
    if (result.success) {
      ElMessage.success(result.message);
      emits("change");
      close();
    } else {
      ElMessage.error(result.message || "操作失败");
    }
  } catch (error) {
    ElMessage.error("操作失败");
  } finally {
    loading.value = false;
  }
}

// 监听props变化，重置表单
watch(() => props.item, async (newItem) => {
  if (newItem) {


    // 使用nextTick确保在下一个DOM更新周期安全地更新表单数据
    await nextTick();

    if (newItem.type === 'district') {
      // 使用Object.assign或逐个赋值，避免直接替换响应式对象
      Object.assign(form.value, {
        type: 'district',
        districtName: newItem.districtName || '',
        districtId: newItem.districtId || '',
        districtDesc: newItem.districtDesc || ''
      });
    } else {
      // 使用Object.assign或逐个赋值，避免直接替换响应式对象
      Object.assign(form.value, {
        type: 'dept',
        deptName: newItem.deptName || '',
        parentId: newItem.parentId || '', // 默认带出上一级节点ID
        parentPath: [], // 级联选择器路径，稍后构建
        deptId: newItem.deptId || '',
        deptDesc: newItem.deptDesc || '',
        // 确保areaId字段存在但为空，避免意外的值
        areaId: newItem.areaId || ''
      });

      // 如果有父节点ID，构建完整的级联路径
      if (newItem.parentId && parentNodeOptions.value.length > 0) {
        const parentPath = buildParentPath(newItem.parentId);
        if (parentPath.length > 0) {
          form.value.parentPath = parentPath;
        }
      }
    }
    
    // 重新构建级联选择器选项（根据当前节点层级）
    if (parentNodeOptions.value.length > 0) {
      const districts = parentNodeOptions.value.filter(item => item.type === 'district');
      const depts = parentNodeOptions.value.filter(item => item.type === 'dept');
      buildCascaderOptions(districts, depts);
    }
  }
}, { immediate: true, deep: true });

// 监听visible变化，重置验证和表单
watch(() => props.visible, async (newVal) => {
  if (newVal) {
    await fetchParentNodeOptions(); // 获取父节点选项
    
    // 数据加载完成后，如果是科室且有父节点信息，重新构建级联路径
    if (props.item && props.item.type === 'dept' && props.item.parentId) {
      await nextTick();
      const parentPath = buildParentPath(props.item.parentId);
      if (parentPath.length > 0) {
        form.value.parentPath = parentPath;
      }
    }
    
    if (formRef.value) {
      formRef.value.clearValidate();
    }
  } else {
    // 弹窗关闭时重置表单数据，确保下次打开时是干净的状态
    await nextTick();
    Object.assign(form.value, {
      type: '',
      districtName: '',
      districtId: '',
      districtDesc: '',
      deptName: '',
      parentId: '',
      parentPath: [], // 重置级联选择器路径
      deptId: '',
      deptDesc: '',
      areaId: '' // 重置areaId字段
    });
  }
});
</script>

<style scoped lang="scss">
.w-full {
  width: 100%;
}

.text-gray-500 {
  color: #6b7280;
}

// 选择框样式优化
:deep(.el-select) {
  .el-input__inner {
    text-align: left;
  }
}

:deep(.el-select-dropdown__item) {
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 12px;
  line-height: 34px;
}

// 确保表单项一行显示完整
.el-form-item {
  white-space: nowrap;
}

.el-form-item__label {
  text-align: left;
  white-space: nowrap;
  display: inline-block;
}

// 级联选择器样式
:deep(.el-cascader) {
  width: 100%;
  
  .el-input__inner {
    text-align: left;
  }
}

:deep(.el-cascader-panel) {
  .el-cascader-node {
    position: relative;
    
    // 隐藏原有的选中按钮
    .el-cascader-node__prefix {
      display: none !important;
    }
    
    .el-cascader-node__label {
      text-align: left;
      cursor: pointer;
      padding: 8px 12px;
      width: 100%;
      box-sizing: border-box;
      display: block;
      position: relative;
      transition: background-color 0.3s ease;
      
      &:hover {
        background-color: #f5f7fa;
      }
    }
    
    // 整个节点区域可点击
    &:hover {
      background-color: #f5f7fa;
    }
    
    // 选中状态样式
    &.is-active {
      background-color: #ecf5ff;
      
      .el-cascader-node__label {
        color: #409eff;
        font-weight: 500;
      }
    }
    
    // 让整个节点区域都可以点击
    &.is-selectable {
      cursor: pointer;
      
      .el-cascader-node__label {
        cursor: pointer;
      }
    }
  }
  
  // 隐藏Radio按钮，但保留其功能
  .el-radio {
    position: absolute !important;
    opacity: 0 !important;
    z-index: -1 !important;
    width: 0 !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  
  // 隐藏Checkbox
  .el-checkbox {
    position: absolute !important;
    opacity: 0 !important;
    z-index: -1 !important;
    width: 0 !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  
  // 移除默认的选中图标
  .el-icon-check {
    display: none !important;
  }
  
  // 优化展开箭头
  .el-cascader-node__postfix {
    .el-icon-arrow-right {
      color: #c0c4cc;
      transition: transform 0.3s ease;
    }
    
    &:hover .el-icon-arrow-right {
      color: #409eff;
    }
  }
  
  // 确保点击区域覆盖整个节点
  .el-cascader-node__content {
    width: 100%;
    display: flex;
    align-items: center;
    cursor: pointer;
    
    &:hover {
      background-color: #f5f7fa;
    }
  }
  
  // 去除选中按钮，整个区域可点击
  .el-cascader-node.is-leaf .el-cascader-node__postfix {
    display: none;
  }

  // 优化叶子节点的点击体验
  .el-cascader-node.is-leaf {
    .el-cascader-node__content {
      padding-right: 12px;
    }
  }

  // 优化级联面板的整体样式
  .el-cascader-panel {
    min-width: 180px;
  }
}

/* 优化表单样式 */
.el-form-item.is-required .el-form-item__label::before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}
</style>
