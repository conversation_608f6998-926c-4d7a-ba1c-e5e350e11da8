<template>
  <el-dialog v-model="props.visible" title="绑定档案" draggable :width="520" @close="cancel">
    <div v-loading="loading">
      <div class="box-content">
        <div p-15 border-bottom flex>
          <el-input v-model="filterName" class="mr-10px" flex-grow placeholder="请输入患者身份证号、手机号、档案编号查询" @blur="searchBlur" clearable></el-input>
          <el-button flex-shrink-0 type="primary" @click="search">查询</el-button>
        </div>
        <div class="text-center" v-if="!searchName">请在上方搜索患者，患者如果在院内已有建档信息，可直接绑定院内档案。</div>
        <template v-else>
          <div v-if="list.length > 0">
            <div class="box-cell" v-for="item in list" :key="item.idCard">
              <div class="box-cell-name">
                <div class="flex algin-center">
                  <div class="pr-10px t_1">{{ item.name }}</div>
                  <div v-if="item.mobile">|</div>
                  <div class="pl-10px">{{ item.mobile }}</div>
                </div>
                <div class="mt-10px">
                  <span>身份证号:</span>
                  <span class="pl-5px">{{ item.idCard }}</span>
                </div>
                <div class="mt-10px">
                  <span>档案编号:</span>
                  <span class="pl-5px">{{ item.customerNumber }}</span>
                </div>
              </div>
              <el-button type="primary" class="ml-10px" @click="bindMember(item)">绑定</el-button>
            </div>
          </div>
          <empty-data v-else :top="20" :title="noDataTitle" padding="0 0 15px" text-top="10px" :image-width="120"></empty-data>
        </template>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <div>
          未找到患者可以:
          <el-button type="primary" class="ml-10px" @click="addCustomer">新增患者</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
  <customer-repeat :visible="repeatVisible" :customer="customer" @acceptCustomer="acceptCustomer" @close="repeatVisible = false"></customer-repeat>
</template>
  <script setup>
import { computed, ref } from "vue";
import { getHisCustomerArchive } from "@/api/customerHisSync";
import { addMember } from "@/api/member";
import EmptyData from "@/components/empty-data.vue";
import { teamStore } from "@/store/team";
import { ElMessage } from "element-plus";
import customerRepeat from "./customer-repeat/index";
import { useRouter } from "vue-router";
import validate from "@/utils/validate";
import repeatComponentApi from "@/views/member/components/customer-repeat/api";
const props = defineProps({
  visible: { type: Boolean, defautl: false },
});
const { repeatVisible, customer, getCustomer } = repeatComponentApi();
const filterName = ref("");
const searchName = ref("");
const list = ref([]);
const loading = ref(false);
const emit = defineEmits(["cancel", "addCustomer", "success", "acceptCustomer"]);
function defineData() {
  filterName.value = "";
  searchName.value = "";
  list.value = [];
}
function cancel() {
  defineData();
  emit("cancel");
}
async function search() {
  searchName.value = filterName.value;
  if (!filterName.value) {
    return;
  }
  loading.value = true;
  let query = {
    idNo: searchName.value,
  };
  if (validate.isMobile(filterName.value)) {
    query = {
      mobile: filterName.value,
    };
  } else if (validate.isChinaId(filterName.value)[0]) {
    query = {
      idCard: filterName.value,
    };
  }
  let { success, data } = await getHisCustomerArchive(query);
  loading.value = false;
  if (success) {
    list.value = data.list;
  } else {
    list.value = [];
  }
}
function searchBlur() {
  if (!filterName.value) {
    searchName.value = "";
    list.value = [];
  }
}
const noDataTitle = computed(() => {
  return `没有找到“${searchName.value}”在院内的建档信息`;
});
function addCustomer() {
  repeatVisible.value = false;
  emit("addCustomer");
}
async function acceptCustomer() {
  repeatVisible.value = false;
  emit("acceptCustomer", customer.value);
}
async function bindMember(params) {
  if (loading.value) return;
  loading.value = true;
  try {
    const payLoad = {
      ...params,
      corpId: localStorage.getItem("corpId"),
      teamId: teamStore().currentTeamId ? [teamStore().currentTeamId] : [],
      creator: localStorage.getItem("userId"),
      relationship: "本人",
      isConnectHis: true,
      personResponsibles: [
        {
          corpUserId: localStorage.getItem("userId"),
          teamId: teamStore().currentTeamId,
        },
      ],
    };
    const { success, message } = await addMember(payLoad);
    if (success) {
      emit("cancel");
      emit("success");
      defineData();
    } else {
      let item = await getCustomer({ value: params.idCard, title: "idCard" });
      customer.value = Object.assign(params, item);
      if (customer.value["idCard"]) {
        repeatVisible.value = true;
        emit("cancel");
        return;
      }
      ElMessage.error(message);
    }
  } catch (error) {
    ElMessage.error("绑定失败，请稍后重试");
  } finally {
    loading.value = false;
  }
}
</script>
  <style scoped lang="scss">
.box-content {
  height: 300px;
  overflow-y: auto;
  position: relative;
  .box-cell {
    display: flex;
    justify-content: space-between;
    padding: 20px 20px 10px 20px;
    align-items: center;
    border-bottom: 1px solid #eee;
    .box-cell-name {
      .t_1 {
        color: #000;
        font-weight: 600;
        align-items: center;
      }
      .t_2 {
        font-size: 14px;
      }
    }
    .box-cell-radio {
      width: 18px;
      height: 18px;
      cursor: pointer;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}
</style>