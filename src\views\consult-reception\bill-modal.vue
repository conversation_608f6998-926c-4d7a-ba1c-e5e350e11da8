<template>
  <el-drawer v-model="visible" title="新建开单" size="80%" style="--el-drawer-padding-primary: 0">
    <my-layout class="bg-[#f2f3f4]">
      <layout-main>
        <customer-detail canRename :visible="visible" :customer="customer" @showCustomerDetail="handleDetail" @changeName="changeName" customerType="corpCustomer" :request="true" @reload="customerReload" />
        <div class="bg-white mt-10px">
          <div class="flex align-center">
            <div class="flex-shrink-0 px-10px text-16px font-semibold">关联缴费单</div>
            <div class="w-0 flex-grow truncate text-14px text-red-500" title="红色字体项目需要发往科室治疗，开单的时候请务必确认项目、数量、科室、金额的准确性">红色字体项目需要发往科室治疗，开单的时候请务必确认项目、数量、科室、金额的准确性</div>
            <el-button class="m-10px h-25px" type="primary" @click="drawerVisible = true" plain>选择</el-button>
          </div>
          <el-table stripe border :height="300" :data="groupTableList" :span-method="mergeMethod">
            <el-table-column property="type" :min-width="120" label="类型">
              <template #default="{ row: { doctorAdvice, rowType } }">
                <span v-if="rowType === 'countRow'"></span>
                <span v-else>{{ doctorAdvice }}</span>
              </template>
            </el-table-column>
            <el-table-column property="orderName" :min-width="160" label="项目名称">
              <template #default="{ row: { orderName, createTreatmentOrder } }">
                <div :class="haveProjectWithTreatment ? 'text-red-500' : ''">
                  <el-icon v-if="orderName && isSupported" class="mr-5px text-gray-500 cursor-pointer translate-y-2px transform hover:text-blue-500" @click="copyText(orderName)">
                    <DocumentCopy />
                  </el-icon>
                  <span :class="createTreatmentOrder ? 'text-red-500' : ''">{{ orderName }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column property="billDept" :min-width="120" label="开单科室" />
            <el-table-column property="billDoctor" :min-width="120" label="开单医生" />
            <el-table-column property="projectPrice" :min-width="120" label="单价" />
            <el-table-column property="projectCount" :min-width="120" label="数量" />
            <el-table-column property="settlementTime" :min-width="120" label="结算时间" />
            <el-table-column property="itemFee" :min-width="120" label="总金额">
              <template #default="{ row: { itemFee, rowType, typeFee } }">
                <span v-if="rowType === 'countRow'" class="font-semibold">小计： ￥{{ typeFee.toFixed(2) }}</span>
                <span>{{ itemFee >= 0 ? `￥${itemFee.toFixed(2)}` : "" }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="flex px-15px bg-white">
          <div class="w-0 flex-grow flex flex-wrap py-5px">
            <template v-if="matchRes.showTypeFee">
              <div v-for="i in typeStats" :key="i.key" class="flex items-center py-10px text-14px mr-10px">
                <div>{{ i.name }}：</div>
                <div :class="i.matched ? '' : 'text-red-500'">￥{{ i.fee }}</div>
                <el-checkbox class="ml-5px" :model-value="i.matched" />
              </div>
            </template>
          </div>
          <!-- <div class="ml-10px flex-shrink-0 font-semibold text-14px py-15px">
            <span>类型：{{ currentBill.billType || " -- " }}</span>
            <span class="ml-15px">日期：{{ currentBill.settlementTime || " -- " }}</span>
            <span class="ml-15px">合计总额：</span>
            <span class="text-orange-500">￥{{ currentBill.totalFee || " -- " }}</span>
            <el-checkbox class="ml-5px" :model-value="matchRes.totalFeeMatched" />
          </div> -->
          <div class="ml-10px flex-shrink-0 flex items-center text-14px py-15px">
            <div class="flex-shrink-0 flex items-center mr-10px">
              <div class="flex-shrink-0">开单科室：</div>
              <el-select filterable v-model="deptId" @change="selectDept($event)">
                <el-option v-for="i in depts" :key="i.value" :label="i.label" :value="i.value" />
              </el-select>
            </div>
            <div class="flex-shrink-0 flex items-center mr-10px">
              <div class="flex-shrink-0">开单医生：</div>
              <div class="flex-shrink-0 w-200px">
                <ww-user-select class="max-w-300px" :list="staffIds" placeholder="请选择" :value="staffId" @change="selectStaff($event)" />
              </div>
            </div>
            <div class="flex-shrink-0 items-center">
              <span>合计总额：</span>
              <span class="text-orange-500">￥{{ currentBill.totalFee || " -- " }}</span>
              <el-checkbox class="ml-5px" :model-value="matchRes.totalFeeMatched" />
            </div>
          </div>
        </div>

        <bill-project :dept="dept" :staff="staff" :customer="customer" :consultRecord="consultRecord" @toBill="toBill" @close="close" :feeList="feeList" :projectTypeAmountList="projectTypeAmountList" ref="billProjectRef" :getMatchRes="getMatchRes" @onProjectsChange="handleProjectChange" />
      </layout-main>
      <payment-drawer :visible="drawerVisible" @change="selectBill($event)" @close="drawerVisible = false" :customer="customer" :consultRecord="consultRecord" />
    </my-layout>
  </el-drawer>
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { useVModel } from "@vueuse/core";
import { useClipboard } from "@vueuse/core";
import { ElMessage } from "element-plus";
import useBillMatch from "./hooks/useBillMatch.js";
import MyLayout, { LayoutMain } from "@/components/layout";
import customerDetail from "@/components/customer-detail";
import billProject from "./bill-components/bill-project.vue";
import paymentDrawer from "./bill-components/payment-drawer.vue";
import usePayment from "./bill-components/usePayment";
import { getDeptList, getDeptStaff } from "@/api/dept-manage";
import WwUserSelect from "@/components/ww-user-select";

const emit = defineEmits(["update:modelValue", "success", "showCustomerDetail", "changeName"]);
const props = defineProps({
  customer: { type: Object, default: () => ({}) },
  consultRecord: { type: Object, default: () => ({}) },
  modelValue: {
    type: Object,
    default: () => ({}),
  },
});

const visible = useVModel(props, "modelValue", emit);
const source = ref("");
const { copy, isSupported } = useClipboard({ source });
const currentBill = ref([]);
const dept = ref({});
const depts = ref([]);
const deptId = ref("");
const staff = ref({});
const staffId = ref("");
const staffList = ref([]);
const drawerVisible = ref(false);
const selectedProjects = ref([]);
const selectedPackageProjects = ref([]);
const billProjectRef = ref(null);

const staffIds = computed(() => {
  // 过滤出岗位为"医生、专家医生"的人员
  return staffList.value
    .filter((staff) => {
      // 检查staff的job数组中是否包含"doctor"或"expertDoctor"
      return Array.isArray(staff.job) && (staff.job.includes("doctor") || staff.job.includes("expertDoctor"));
    })
    .map((i) => i.value);
});
const projectTypeAmountList = computed(() => {
  return groupTableList.value.filter((item) => item.rowType === "countRow");
});
const feeList = computed(() => {
  return groupTableList.value.filter((item) => item.rowType !== "countRow");
});
const { groupTableList, mergeMethod, list: tableList } = usePayment(8);
const { matchRes, typeStats } = useBillMatch(currentBill, selectedProjects, selectedPackageProjects);

function changeName(name) {
  emit("changeName", name);
}

function close() {
  visible.value = false;
}

async function customerReload(item) {
  emit("success", item);
}

function copyText(name) {
  if (typeof name === "string" && name.trim()) {
    copy(name);
    ElMessage.success("复制成功");
  }
}

function handleDetail() {
  emit("showCustomerDetail", props.customer._id);
}

function handleProjectChange(val) {
  selectedProjects.value = val && Array.isArray(val[0]) ? val[0] : [];
  selectedPackageProjects.value = val && Array.isArray(val[1]) ? val[1] : [];
}

function selectBill(bill) {
  currentBill.value = bill;
  tableList.value = bill.itemList;
}

function selectDept(val) {
  dept.value = depts.value.find((i) => i.value === val) || {};
  if (dept.value._id) {
    getStaffList(dept.value._id);
  }
}

function selectStaff(val) {
  staffId.value = val;
  staff.value = staffList.value.find((i) => i.value === val) || {};
}

function getMatchRes() {
  if (matchRes.value.result) {
    return true;
  } else {
    ElMessage.info(matchRes.value.msg);
    return false;
  }
}

async function getDepts() {
  const { data } = await getDeptList();
  depts.value = data && Array.isArray(data.list) ? data.list.map((i) => ({ label: i.deptName, value: i.deptId, sort: i.sort, _id: i._id })).sort((a, b) => a.sort - b.sort) : [];
}

async function getStaffList(deptId) {
  const { data } = await getDeptStaff({ deptId });
  staffList.value = data && Array.isArray(data.list) ? data.list.map((i) => ({ label: i.name, value: i.userId, _id: i._id, job: i.job })) : [];
  if (staffList.value.length === 1) {
    selectStaff(staffList.value[0].value);
  } else if (!staffList.value.some((i) => i.value === staffId.value)) {
    selectStaff();
  }
}

async function toBill() {
  visible.value = false;
  emit("success");
}

watch(
  () => visible.value,
  (n) => {
    if (n) {
      tableList.value = [];
      currentBill.value = [];
      dept.value = {};
      deptId.value = "";
      staff.value = {};
      staffId.value = "";
      getDepts();
      setTimeout(() => {
        billProjectRef.value.close();
      }, 0);
    }
  }
);
</script>
<style lang="scss" scoped>
:deep(.hidden-inner-content > .select-trigger > .el-input > .el-input__wrapper > .el-input__inner) {
  opacity: 0;
}
</style>