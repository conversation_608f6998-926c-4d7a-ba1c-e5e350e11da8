import { templateStore } from "@/store/template";
import { configStore } from "@/store/config";
import { storeToRefs } from "pinia";
const { getGroupTag } = configStore();
const { tags } = storeToRefs(configStore());

async function getImportTable(type) {
  const { corpTemplate } = templateStore();
  //浅拷贝 确保不改变原数据
  const newCorpTemplate = JSON.parse(JSON.stringify(corpTemplate));
  const corpTemplateList = getTemplateList(type, newCorpTemplate);
  let tagIdIndex = corpTemplateList.findIndex((item) => item.title === "tagIds");
  if (tags.value.length === 0) {
    await getGroupTag();
  }
  if (tagIdIndex !== -1) {
    let tagObj = tags.value.map((item) => {
      return {
        name: `客户标签(${item.groupName})`,
      };
    });
    corpTemplateList.splice(tagIdIndex, 1, ...tagObj);
  }
  let bloodPressureIndex = corpTemplateList.findIndex((item) => item.title === "bloodType");
  if (bloodPressureIndex !== -1) {
    let bloodPressureObj = [
      {
        name: "测量日期",
      },
      {
        name: "收缩压",
      },
      {
        name: "舒张压",
      },
    ];
    corpTemplateList.splice(bloodPressureIndex, 1, ...bloodPressureObj);
  }

  // 阳性发现及处理意见
  let positiveFindIndex = corpTemplateList.findIndex((item) => item.title === "positiveFind");
  if (positiveFindIndex !== -1) {
    let positiveFindObj = [
      {
        name: "阳性发现",
      },
      {
        name: "阳性发现处理意见",
      },
    ];
    corpTemplateList.splice(positiveFindIndex, 1, ...positiveFindObj);
  }

  let list = corpTemplateList.filter((item) => item.title !== "relationship").map((item) => item.name);
  let mustList = list.filter((item) => item.includes("必填"));
  let noMustList = list.filter((item) => !item.includes("必填"));
  let baseFeiled = [],
    inhospital = [],
    outpatient = [],
    physicalExamination = [];
  corpTemplate.forEach((item) => {
    let templateListTitle = item.templateList.filter((item) => item.operateType !== "onlyRead").map((item) => item.title);
    if (["baseTemplate", "internalTemplate"].includes(item.templateType)) {
      if (baseFeiled.includes("bloodPressure")) baseFeiled = [...baseFeiled, "bloodPressureDate", "diastolicPressure", "systolicPressure"];
      baseFeiled = [...baseFeiled, ...templateListTitle];
    } else if (item.templateType === "inhospital") {
      inhospital = templateListTitle;
    } else if (item.templateType === "outpatient") {
      outpatient = templateListTitle;
    } else if (item.templateType === "physicalExaminationTemplate") {
      physicalExamination = templateListTitle;
    }
  });
  return {
    list: [...mustList, ...noMustList],
    corpTemplateList,
    baseFeiled,
    inhospital,
    outpatient,
    physicalExamination,
  };
}
function getTemplateList(type, templateList) {
  let list = [];
  templateList.forEach((item) => {
    let tempList = item.templateList;
    if (["baseTemplate", "internalTemplate"].includes(item.templateType) || (item.templateType === "inhospital" && type === "inhospital") || (item.templateType === "outpatient" && type === "outpatient") || (item.templateType === "physicalExaminationTemplate" && type === "physicalExaminationTemplate")) {
      list = [...list, ...getFiled(tempList)];
    }
  });
  return list;
}

function getFiled(tempList) {
  return tempList
    .map((item) => {
      if ((item.isMustEnable || item.required) && !item.name.includes("必填")) {
        item.name = item.name + "(必填)";
      }
      return item;
    })
    .filter((item) => item.operateType !== "onlyRead" && !["corp", "corpName", "BMI", "cardType", "files", "customerSource", "relationship"].includes(item.title) && (item.fieldStatus === "enable" || !item.fieldStatus));
}

export { tags, getImportTable };
