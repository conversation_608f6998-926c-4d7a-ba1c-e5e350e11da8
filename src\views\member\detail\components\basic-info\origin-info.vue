<template>
  <info-card title="来源信息">
    <el-form :label-width="labelWidth" :size="formSize" label-position="left">
      <el-row>
        <el-col v-for="item in formatData" :key="item.key" v-bind="colAttr">
          <el-form-item :label="item.label">
            <template v-if="item.key === 'creator'">
              <ww-user v-if="customer.creator" :openid="customer.creator" type="userName"></ww-user>
              <el-text v-if="customer.creator" size="default">（内部员工）</el-text>
              <el-text v-else size="default">客户本人</el-text>
            </template>
            <template v-else-if="item.key === 'referrer'">
              {{ customer.referenceUserId }}
              <el-text v-if="customer.referenceType" size="default" class="mr-6px">{{ customer.referenceType }}</el-text>
              <ww-user v-if="customer.referenceUserId" :openid="customer.referenceUserId" type="userName"></ww-user>
              <el-text v-else size="default">{{ customer.reference }}</el-text>
            </template>
            <el-text v-else size="default">{{ item.value }}</el-text>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </info-card>
</template>
<script setup>
import { ref, computed, inject, watch } from "vue";
import dayjs from "dayjs";
import { getNameByexternalUserId } from "@/utils/common";
import InfoCard from "../info-card";
import WwUser from "@/components/ww-user/index.vue";
const colAttr = { span: 24, md: 12, lg: 8 };
const cols = [
  { value: "", label: "客户来源：", key: "origin" },
  { value: "", label: "推荐人：", key: "referrer" },
  { value: "", label: "意向服务项目：", key: "intentedProject" },
  // { value: "", label: "客户主诉：", key: "complaint" },
  { value: "", label: "建档人员：", key: "creator" },
  { value: "", label: "建档时间：", key: "createTime" },
  { value: "", label: "我的推荐：", key: "myRecommend" },
];
const labelWidth = inject("labelWidth");
const formSize = inject("formSize");
const customer = inject("customer");

const formatData = computed(() => {
  return cols.map((i) => {
    const item = { ...i };
    if (i.key === "origin") {
      item.value = Array.isArray(customer.value.customerSource) ? customer.value.customerSource.join("、") : "";
    } else if (i.key === "createTime") {
      item.value = customer.value.createTime ? dayjs(customer.value.createTime).format("YYYY-MM-DD HH:mm") : "";
    } else {
      item.value = customer.value[i.key] || "";
    }
    return item;
  });
});

// const creator = ref('');
// async function setReferrer(data) {
//   if (data.unionid && data.externalUserId) {
//     const res = await getNameByexternalUserId(data.externalUserId);
//     creator.value = res && res.name ? res.name : ''
//   } else {
//     creator.value = ''
//   }
// }
// watch(customer, (n) => {
//   setReferrer(n)
// }, { deep: true, immediate: true })
</script>