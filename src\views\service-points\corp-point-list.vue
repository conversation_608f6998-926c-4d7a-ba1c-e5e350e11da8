<template>
  <my-layout bg-fff>
    <layout-item>
      <div class="pt-15px pr-15px pb-5px pl-25px text-14px">
        <span class="cursor-pointer text-gray-500 hover:text-blue-500" @click="back()">返回</span>
        <el-divider direction="vertical" />
        <span color-normal class="text-15px font-semibold">积分管理明细</span>
      </div>
      <div class="px-15px pb-15px flex">
        <date-range-filter v-model:dates="dates" label="日期" :text="datesText" :width="320" />
        <trigger-condition-filter v-model:serviceTypes="serviceTypes" v-model:serviceRates="serviceRates" />
        <check-box-filter v-model="teamIds" label="服务团队" :list="teamList" />
        <check-box-filter v-model="userIds" label="员工" :list="peopleList">
          <template #item="{ item }">
            <ww-user :openid="item.value"></ww-user>
          </template>
        </check-box-filter>
        <el-button type="primary" class="ml-auto" @click="search">搜索</el-button>
      </div>
    </layout-item>
    <Layout-main :scroll="false">
      <el-table height="100%" :data="list">
        <el-table-column width="150" label="时间" prop="time">
          <template #default="{ row: { createTime } }">
            {{ dayjs(createTime).format("YYYY-MM-DD HH:mm") }}
          </template>
        </el-table-column>
        <el-table-column prop="condition" width="160" label="类型">
          <template #default="{ row: { eventType } }">
            {{ triggerConditionTitle(eventType) }}
          </template>
        </el-table-column>
        <el-table-column prop="detail" width="340" label="详情">
          <template #default="{ row }">
            <div>{{ serviceTypeTitle(row) }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="rewardPoints" width="100" label="奖励积分">
          <template #default="{ row: { points } }">
            <div class="truncate">{{ points }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="teams" width="200" label="服务团队">
          <template #default="{ row: { executeTeamId } }">
            <div class="truncate">{{ getTeamName(executeTeamId) }}</div>
          </template>
        </el-table-column>
        <el-table-column width="120" prop="user" label="员工">
          <template #default="{ row: { executorUserId } }">
            <ww-user :openid="executorUserId"></ww-user>
          </template>
        </el-table-column>
        <el-table-column width="120" prop="customer" label="客户">
          <template #default="{ row }">
            <div class="truncate cursor-pointer text-blue-500" @click="show(row)">{{ row.customerName }}</div>
          </template>
        </el-table-column>
      </el-table>
    </Layout-main>
    <Layout-item>
      <div class="flex justify-between align-center flex-wrap">
        <div class="ml-10px w-140px text-14px">累计积分：{{ allPoints }}分</div>
        <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize" @handle-current-change="changePage" />
      </div>
    </Layout-item>
  </my-layout>
  <customer-detail :visible="customerDetailVisible" :customerId="customerId" title="客户详情" customerType="corpCustomer" @close="customerDetailVisible = false" />
</template>
<script setup>
import { computed, onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import { storeToRefs } from "pinia";
import { teamStore } from "@/store/team";
import useElPagination from "@/hooks/useElPagination";
import { CheckBoxFilter, DateRangeFilter, RadioFilter } from "@/components/filter-bar";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import triggerConditionFilter from "./trigger-condition-filter.vue";
import WwUser from "@/components/ww-user/index.vue";
import { getCorpServicePointsList } from "@/api/todo";
import { ServiceType } from "@/baseData";
import { staffStore } from "@/store/staff";
import { tagsStore } from "@/store/tags";
import customerDetail from "@/views/member/detail/index.vue";

import dayjs from "dayjs";
import { getServicePointsStatistics } from "@/api/todo";

const route = useRoute();
const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());
const dates = ref([]);
const datesText = computed(() => dates.value.join(" ~ "));
const serviceTypes = ref([]);
const serviceRates = ref([]);
const list = ref([]);
const total = ref(0);
const customerId = ref("");
const customerDetailVisible = ref(false);

const rateList = [
  { label: "一星", value: 1 },
  { label: "二星", value: 2 },
  { label: "三星", value: 3 },
  { label: "四星", value: 4 },
  { label: "五星", value: 5 },
];
const { allTeams } = storeToRefs(teamStore());
const { removeTag: removeRouteTag } = tagsStore();

const peopleList = computed(() => staffList.value.map((i) => ({ value: i.userid })));
const teamList = computed(() => allTeams.value.map((i) => ({ label: i.name, value: i.teamId })));
const teamIds = ref([]);
const userIds = ref([]);
const memberList = computed(() => {
  const teams = teamIds.value.length ? allTeams.value.filter((i) => teamIds.value.includes(i.teamId)) : allTeams.value;
  const userids = teams.reduce((list, item) => {
    if (Array.isArray(item.memberLeaderList)) {
      list = [...list, ...item.memberLeaderList];
    }
    if (Array.isArray(item.memberList)) {
      list = [...list, ...item.memberList];
    }
    return list;
  }, []);
  return Array.from(new Set(userids)).map((userid) => ({ value: userid }));
});

function getTeamName(teamId) {
  return allTeams.value.find((i) => i.teamId === teamId)?.name || "";
}

const { page, pageSize, changePage, changeSize } = useElPagination(getList);
function search() {
  page.value = 1;
  getList();
  getStatistics();
}

function serviceTypeTitle(row) {
  if (row.serviceRate) return `服务评价获得一次"${rateList.find((i) => i.value === row.serviceRate).label}"`;
  else return ServiceType[row.eventType] ? `完成一条类型为"${ServiceType[row.eventType]}"服务` : "";
}
function triggerConditionTitle(type) {
  if (!type) return "";
  else if (type === "serviceRate") return "获得服务评价";
  else return "提供患者服务";
}

const allPoints = ref(0);
async function getStatistics() {
  const { data } = await getServicePointsStatistics({
    dates: dates.value,
    serviceTypes: serviceTypes.value,
    serviceRates: serviceRates.value,
    executeTeamIds: teamIds.value,
    executorUserIds: userIds.value,
  });
  allPoints.value = data ? data.allPoints : 0;
}

async function getList() {
  const { data } = await getCorpServicePointsList({
    page: page.value,
    pageSize: pageSize.value,
    dates: dates.value,
    serviceTypes: serviceTypes.value,
    serviceRates: serviceRates.value,
    executeTeamIds: teamIds.value,
    executorUserIds: userIds.value,
  });
  list.value = data.list;
  total.value = data.total;
}

function back() {
  removeRouteTag(route.fullPath);
}

function show(data) {
  if (data.customerId) {
    customerId.value = data.customerId;
    customerDetailVisible.value = true;
  }
}

onMounted(async () => {
  if (!staffList.value.length) await getStaffList();
  getList();
  getStatistics();
});
</script>