<template>
  <el-dialog :model-value="visible" :width="width" title="联系方式" @close="close">
    <div color-normal>
      <form-template ref="mobileRef" :items="mobileTemp" :form="forms" @change="change($event)"></form-template>
      <template v-if="temp && temp.tempList.length">
        <div class="px-15px pt-10px text-14px font-semibold">内部</div>
        <form-template ref="mobileGroupRef" :items="temp.tempList" :form="forms" @change="change($event)"></form-template>
      </template>
    </div>
    <template #footer>
      <div class="text-center">
        <el-button plain class="w-100px" @click="close()">取消</el-button>
        <el-button type="primary" :loading="loading" class="w-100px" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <customer-repeat :width="300" :visible="repeatVisible" :customer="customer" @acceptCustomer="acceptCustomer" @close="repeatVisible = false" :operateType="operateType"></customer-repeat>
  <mobile-repeat :width="300" :visible="mobileRepeatVisible" :customers="mobileReatCustomers" @close="mobileRepeatVisible = false"></mobile-repeat>
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { storeToRefs } from "pinia";
import { templateStore } from "@/store/template";
import customerRepeat from "@/views/member/components/customer-repeat/index.vue";
import repeatComponentApi from "@/views/member/components/customer-repeat/api";
import formTemplate from "../form-template/edit/index.vue";
import useCustomerApi from "../../chat-bar/useCustomerApi";
import validate from "@/utils/validate";
import mobileRepeat from "@/components/mobile-repeat/index.vue";
import mobileRepeatApi from "@/components/mobile-repeat/api";

const props = defineProps({
  customer: { type: Object, default: () => ({}) },
  visible: { type: Boolean, default: false },
  value: { type: String, default: "" },
  width: { type: String, default: "500px" },
});
const { corpTemplateList } = storeToRefs(templateStore());
const { repeatVisible, customer } = repeatComponentApi();
const { mobileReatCustomers, mobileRepeatVisible, getMobileReatCustomer } = mobileRepeatApi();
const form = ref({});
const mobileList = ["phone1", "phone2", "phone3"];
const forms = computed(() => ({ ...props.customer, ...form.value }));
const temp = computed(() => {
  const temp = Array.isArray(corpTemplateList.value) ? corpTemplateList.value.find((i) => i.templateType === "internalTemplate") : null;
  const tempList = temp && Array.isArray(temp.templateList) ? temp.templateList.filter((i) => mobileList.includes(i.title)).map((i) => ({ ...i, noteRight: true })) : [];
  return { ...(temp || {}), tempList };
});

const emits = defineEmits(["change", "close"]);
const { update } = useCustomerApi();
const mobileTemp = [{ title: "mobile", name: "客户自留", operateType: "formCell", type: "input" }];

const mobileRef = ref();
const mobileGroupRef = ref();
const loading = ref(false);
async function confirm() {
  if (Object.keys(form.value).length === 0) {
    close();
    return;
  }
  if (loading.value || !mobileRef.value.verify() || (mobileGroupRef.value && !mobileGroupRef.value.verify())) return;

  const { success, message } = await update(props.customer._id, form.value);
  if (success) {
    emits("change", { ...form.value });
    close();
  } else ElMessage.error(message);
}
function close() {
  emits("close");
}

function change({ title, value }) {
  if (title === "mobile" && validate.isMobile(value)) getMobileReatCustomer(value, props.customer._id);
  form.value[title] = value;
}

watch(
  () => props.visible,
  (n) => {
    if (n) {
      form.value = {};
    }
  }
);
</script>
<style lang="scss" scoped>
.tag-item {
  border: 1px solid #eee;
  margin-bottom: 12px;
}

.tag-group {
  margin-bottom: -12px;
}

.tag-item + .tag-item {
  margin-left: 12px;
}

.tag-item.active {
  background-color: var(--yc-primary-color);
  border-color: var(--yc-primary-color);
  color: #fff;
}
</style>