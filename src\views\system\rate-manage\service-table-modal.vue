<template>
  <el-dialog :model-value="visible" :width="width" :title="title" @close="close">
    <el-table :data="tableData" :max-height="maxHeight">
      <el-table-column prop="time" label="服务时间" />
      <el-table-column prop="type" label="服务类型" />
      <el-table-column prop="content" label="服务内容" />
    </el-table>
    <template #footer>
      <div class="text-center">
        <el-button plain class="w-100px" @click="close()">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed } from 'vue'
import { useWindowSize } from '@vueuse/core';
import dayjs from 'dayjs';
import { ServiceType } from "@/baseData";


const props = defineProps({
  list: { type: Array, default: () => [] },
  width: { type: Number },
  visible: { type: Boolean, default: false },
})
const emits = defineEmits(['close']);
const { height } = useWindowSize();
const maxHeight = computed(() => Math.floor(height.value * 0.7 - 200));
const title = computed(() => `服务明细：${props.list.length}条`)
const tableData = computed(() => props.list.map(i => {
  return {
    time: dayjs(i.executionTime).format('YYYY-MM-DD HH:mm'),
    type: ServiceType[i.eventType] || '',
    content: i.taskContent,
  }
}))
function close() {
  emits('close');
}
</script>