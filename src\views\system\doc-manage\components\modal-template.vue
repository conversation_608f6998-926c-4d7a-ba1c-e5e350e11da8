<template>
  <el-dialog :model-value="visible" :width="width" :title="data._id ? '编辑模板' : '新增模板'" @close="close">
    <el-scrollbar wrap-style="max-height: calc(100vh - 360px)">
      <div px-15>
        <div class="text-14px text-black py-12px">模版名称</div>
        <el-input v-model="name" placeholder="请输入模版名称" :maxlength="20"/>
        <div class="text-14px text-black py-12px">已选字段</div>
        <div border-bottom class="min-h-30px ">
          <div class="min-h-30px flex flex-wrap">
            <el-tag v-for="i in 8" :key="i" type="info" class="mr-10px mb-10px">
              <span class="inline-block py-6px px-16px w-80px text-center">{{ i }}</span>
            </el-tag>
            <!-- <div v-for="item in selections" :key="`selection_${item.prop}`" class="mr-10px mb-10px border-1 rounded-4px">
            {{ item.title }}
          </div> -->
          </div>
        </div>
        <div class="text-14px text-black py-12px">机构基础模版字段 <span class="ml-10px text-gray-400">请点击选择</span></div>
        <div class="min-h-30px flex flex-wrap">
          <el-tag v-for="i in 8" :key="i" type="info" class="mr-10px mb-10px pointer">
            <span class="inline-block py-6px px-16px w-80px text-center">{{ i }}</span>
          </el-tag>
          <!-- <div v-for="item in selections" :key="`selection_${item.prop}`" class="mr-10px mb-10px border-1 rounded-4px">
            {{ item.title }}
          </div> -->
        </div>
      </div>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button class="w-100px" @click="close()">取消</el-button>
        <el-button class="w-100px" type="primary" @click="confirm()"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref, toRefs, watch } from 'vue';
import { ElMessage } from 'element-plus';

const emits = defineEmits(['close']);
const props = defineProps({
  baseFields: { type: Array, default: () => [] },
  data: { type: Object, default: () => ({}) },
  visible: { type: Boolean, default: false },
  width: { type: Number, default: 520 }
})
const { data, baseFields, visible, width } = toRefs(props);
const name = ref('');
const fields = ref([]);
const selections = computed(() => baseFields.value.filter(i => fields.value.some(f => f.prop === i.prop)))

function close() {
  emits('close')
}
function confirm() {
  if (name.value.trim() === '') ElMessage.info('请输入模版名称')

}
watch(visible, n => {
  if (n) {
    name.value = typeof data.value.name === 'string' ? data.value.name : '';
    fields.value = Array.isArray(data.value.fields) ? [...data.data.value.fields] : [];
  }
})
</script>
<style lang="scss" scoped>
.customer-item:hover {
  background: #FFFAFA;
}
</style>
