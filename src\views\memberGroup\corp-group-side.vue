<template>
  <my-layout bg-fff h-full :radius="8">
    <layout-item>
      <div p-15 border-bottom>
        <div flex items-center justify-between>
          <div flex-shrink-0 font-semibold class="text-20px">分组管理</div>
        </div>
      </div>
    </layout-item>
    <layout-main v-loading="loading">
      <el-tree accordion ref="treeRef" :default-expanded-keys="defaultExpandedKeys" :draggable="true" :style="style"
        :data="treeData" :allow-drag="allowDrag" :allow-drop="allowDrop" node-key="_id"
        :props="{ class: 'group-manage-classify-list-tree' }" :expand-on-click-node="false"
        @node-drag-start="handleDragStart" @node-drop="nodeDrop" @node-click="onNodeClick">
        <template #default="{ node, data }">
          <div class="w-0 flex-grow flex items-center h-full px-15px border-b border-gray-200">
            <div class="flex-shrink-0 mr-6px text-14px" :class="data.level === 0 ? '' : 'opacity-0'">
              <el-icon v-if="node.expanded">
                <CaretBottom />
              </el-icon>
              <el-icon v-else>
                <CaretRight />
              </el-icon>
            </div>
            <div class="flex-shrink-0 text-yellow-500 text-16px mr-6px">
              <el-icon>
                <Folder />
              </el-icon>
            </div>
            <div class="w-0 flex-grow truncate" :class="current && current._id === data._id ? 'text-blue-500' : ''">
              {{ node.label }} {{ data.showCorpTag ? '（机构）':'' }}
            </div>
            <div v-if="data.level === 0" class="flex-shrink-0 flex items-center hover:text-blue-500"
              @click.stop="addNewGroup(data)">
              <el-icon v-if="loadingMap[data._id]" class="animate-spin text-blue-500">
                <Loading />
              </el-icon>
              <el-icon v-else class="outline-none">
                <Plus />
              </el-icon>
            </div>
            <template v-else-if="data.level === 1 && !data.noGroup">
              <div v-if="data.editable" class="flex-shrink-0 flex items-center" @click.stop="() => {}">
                <el-dropdown flex-shrink-0>
                  <el-icon class="outline-none">
                    <MoreFilled />
                  </el-icon>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click.stop="edit(data)">
                        <div class="px-12px text-14px py-4px" color-primary>编辑</div>
                      </el-dropdown-item>
                      <el-dropdown-item v-if="data.allowDelete" @click.stop="remove(data)">
                        <div class="px-12px text-14px py-4px" color-danger>刪除</div>
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
              <span v-if="data.moveable" class="ml-8px flex-shrink-0" title="拖拽可进行排序">
                <svg-icon style="color: #999" size="16" name="drag"></svg-icon>
              </span>
            </template>
          </div>
        </template>
      </el-tree>
    </layout-main>
  </my-layout>
  <corp-group-modal :group="group" :visible="corpVisible" :width="corpWidth" @close="corpClose"
    @change="onCorpGroupChange" />
  <team-group-modal :group="group" :visible="teamVisible" :width="teamWidth" @close="teamClose"
    @change="reloadTeamGroup" />
</template>
<script setup>
import { computed, nextTick, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { ElMessage, ElMessageBox } from "element-plus";
import { getGroups, removeGroup, orderTeamGroups } from "@/api/group";
import useModal from "@/hooks/useModal";
import { teamStore } from "@/store/team";

import corpGroupModal from './corp-group-modal.vue';
import teamGroupModal from './team-group-modal.vue';

import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import SvgIcon from "@/components/svg-icon";


const emits = defineEmits(['change'])
const props = defineProps({
  isAdmin: { type: Boolean, default: false },
  current: { type: Object, default: () => ({}) },
})
const { allTeams, teams } = storeToRefs(teamStore());
const style = {
  "--el-tree-node-content-height": "40px",
};

const corpGroup = ref(null); // 机构分组
const defaultExpandedKeys = ref([]) // 默认展开的节点
const treeRef = ref(); // 树形控件
const groupMap = ref({}); // 分组映射
const loadingMap = ref({}); // 加载状态
const group = ref({}); // 分组
const treeData = computed(() => {
  const corpGroups = { label: '机构', type: 'corp', level: 0, _id: 'corp', children: corpGroup.value };
  const teamGroups = (props.isAdmin ? allTeams.value : teams.value).map(item => ({
    label: item.name,
    type: 'team',
    level: 0,
    _id: item.teamId,
    teamId: item.teamId,
    teamData: { ...item },
    children: groupMap.value[item.teamId] || null,
    memberList: Array.isArray(item.memberList) ? item.memberList : []
  }))
  return props.isAdmin ? [corpGroups, ...teamGroups] : teamGroups;
})

const { close: corpClose, show: corpShow, visible: corpVisible, width: corpWidth } = useModal(800); // 机构分组弹窗
const { close: teamClose, show: teamShow, visible: teamVisible, width: teamWidth } = useModal(800); // 团队分组弹窗

function addNewGroup(data) {
  if (loadingMap.value[data._id]) return;
  if (data.type === 'corp') {
    group.value = {}
    corpShow()
  } else {
    group.value = { teamId: data.teamId }
    teamShow()
  }
}

function edit(data) {
  group.value = { ...data };
  if (data.groupType === 'team') {
    const team = (props.isAdmin ? allTeams.value : teams.value).find(item => item.teamId === data.teamId);
    group.value.teamData = { ...team };
  }
  data.groupType === 'corp' ? corpShow() : teamShow();
}

async function remove(data) {
  await ElMessageBox.confirm(`确定删除分组 ${data.groupName} 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
  const params = data.groupType === 'corp' ? { id: data._id, corpId: data.corpId, groupType: data.groupType } : { id: data._id, corpId: data.corpId, teamId: data.teamId }
  const { success, message } = await removeGroup(params);
  if (success) {
    ElMessage.success('删除成功');
    if (data.groupType === 'corp') {
      getCorpGroup()
    } else {
      reloadTeamGroup(data.teamId)
    }
  } else {
    ElMessage.error(message);
  }
}

async function onCorpGroupChange() {
  await getCorpGroup()
  await nextTick()
  const root = treeRef.value.store.root;
  const childNodes = Array.isArray(root.childNodes) ? root.childNodes : [];
  childNodes.forEach(node => {
    if (node.data._id !== 'corp') {
      node.expanded = false;
    }
  })
  await nextTick();
  groupMap.value = {};
}

/**
 * 节点点击事件
 * @param data 
 * @param node 
 */
async function onNodeClick(data, node) {
  // 一级节点点击时候 加载子节点
  if (data.level === 0 && data.children === null) {
    if (!loadingMap.value[data._id]) loadingMap.value[data._id] = true;
    await loadNode(data)
    await nextTick()
    treeRef.value.store.nodesMap[data._id].expanded = true;
    defaultExpandedKeys.value = [data._id];
    return
  }
  if (data.children && data.children.length) {
    node.expanded = !node.expanded;
    if (node.expanded) {
      defaultExpandedKeys.value = [data._id]
    }
  }
  if (data.level === 1 && data._id !== props.current._id) {
    const treeNode = data.groupType === 'team' ? treeData.value.find(i => i._id === data.teamId) : null;
    const options = treeNode ? treeNode.children.map(i => ({ ...i })) : [];
    emits("change", {
      group: {
        _id: data._id,
        managementPlan: data.managementPlan,
        groupType: data.groupType,
        groupName: data.groupName,
        teamId: data.teamId,
        noGroup: data.noGroup,
        description: data.description
      },
      options
    });
  }
}

/**
 * 加载团队分组数据并进行排序
 * 排序顺序为 未分组，机构分组，自定义分组
 * @param data 
 */
async function loadNode(data) {
  const res = await getTeamGroup(data._id);
  const group = [
    { label: '未分组', level: 1, _id: `noGroup_${data._id}`, groupType: 'team', teamId: data._id, noGroup: true, editable: false }
  ]
  const list = res.map(i => ({ ...i, groupType: 'team' })).sort((a, b) => {
    if (typeof a.order === 'number' && typeof b.order === 'number') {
      return a.order - b.order
    }
    if (typeof a.order === 'number' && typeof b.order !== 'number') {
      return -1
    }
    if (typeof a.order !== 'number' && typeof b.order === 'number') {
      return 1
    }
    return 0
  })
  groupMap.value[data._id] = [...group, ...list];
  loadingMap.value[data._id] = false;
}

/**
 * 获取机构分组
 */
async function getCorpGroup() {
  loadingMap.value['corp'] = true;
  const { success, data } = await getGroups({ page: 1, pageSize: 10000, groupName: '', groupType: "corp" });
  loadingMap.value['corp'] = false;
  if (success) {
    corpGroup.value = Array.isArray(data.data) ? data.data.map(i => ({
      ...i,
      label: i.groupName,
      level: 1,
      editable: true,
      allowDelete: true,
      showCorpTag: true
    })) : [];
  } else {
    ElMessage.error("获取分组列表失败");
    return;
  }
}

/**
 * 获取团队分组
 * @param teamId 
 */
async function getTeamGroup(teamId) {
  let { success, data, message } = await getGroups({ teamId, page: 1, pageSize: 10000 });
  if (success) {
    return Array.isArray(data.data) ? data.data.map(i => ({
      ...i,
      label: i.groupName,
      level: 1,
      editable: !Boolean(i.parentGroupId),
      moveable: true,
      allowDelete: !Boolean(i.parentGroupId),
      showCorpTag: Boolean(i.parentGroupId)
    })) : [];
  } else {
    ElMessage.error("获取分组列表失败");
    return message;
  }
}
/**
 * 重新加载团队分组
 * @param teamId 
 */
async function reloadTeamGroup(teamId) {
  await loadNode({ _id: teamId })
}

/**
 * 判断当前节点是否可以拖拽排序
 * 只有
 * @returns {boolean}
 * @param node 节点
 */
function allowDrag(node) {
  return node.data.moveable;
}

/**
 * 判断是否可以放置
 * @param draggingNode 拖拽节点
 * @param dropNode 放置节点
 * @param type 放置类型
 * @returns {boolean}
 */
function allowDrop(draggingNode, dropNode, type) {
  if (['next', 'prev'].includes(type) && draggingNode.data.teamId === dropNode.data.teamId) {
    if (type === 'prev' && !dropNode.data.moveable) return false;
    return true
  }
  return false
}

/**
 * 释放拖拽节点事件
 * @param node 当前节点
 * @param enterNode 拖拽进入的节点
 */
async function nodeDrop(node, enterNode) {
  const teamId = node.data.teamId;
  const item = treeData.value.find(item => item._id === teamId);
  // 排除未分组 以及 机构分组 （parentGroupId有值表示 机构分组）
  const children = Array.isArray(item.children) ? item.children.filter(i => !i.noGroup) : [];
  const order = children.reduce((acc, item, idx) => {
    acc[item._id] = (idx + 1);
    return acc
  }, {})
  const { success, message } = await orderTeamGroups({ teamId, order });
  if (success) {
    ElMessage.success('操作成功')
  } else {
    loadNode({ _id: teamId })
    ElMessage.error(message)
  }
  return true
}

watch(() => props.isAdmin, n => {
  if (n && !corpGroup.value) {
    getCorpGroup();
  }
}, { immediate: true })


</script>
<style lang="scss" scoped>
:deep(.group-manage-classify-list-tree > .el-tree-node__content > .el-tree-node__expand-icon) {
  display: none;
}
</style>
