<template>
  <div class="report-container">
    <div class="report-header">
      <div class="report-actions">
        <div class="filter-section">
          <div class="filter-row">
            <div class="filter-item">
              <span class="filter-label">日期：</span>
              <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" :shortcuts="dateShortcuts" :disabledDate="disabledDate" @change="handleDateChange" />
            </div>
            <div class="filter-item">
              <check-box-filter v-model="consultantFilter" label="咨询师" :list="consultantList" v-if="isManager" @clear="handleConsultantFilterClear">
                <template #item="{ item }">
                  <ww-user :openid="item.value"></ww-user>
                </template>
              </check-box-filter>
            </div>
            <div class="filter-item">
              <span class="filter-label">咨询项目：</span>
              <project-intent-select v-model="projectFilter" placeholder="请选择或搜索咨询项目" width="220px" @change="handleProjectChange" />
            </div>
            <div class="filter-item">
              <filter-info-source v-model="infoSourceFilter" label="信息来源" />
            </div>
            <div class="filter-item">
              <filter-customer-source v-model="devPathFilter" label="开发渠道" />
            </div>
            <div class="filter-actions">
              <el-button type="primary" @click="handleSearch" :loading="loading" :disabled="loading">查询</el-button>
              <el-button type="success" @click="exportToExcel" :disabled="loading">导出Excel</el-button>
              <el-button @click="resetFilters" :disabled="loading">重置</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="table-container" v-loading="loading" element-loading-text="数据加载中...">
      <table class="performance-table">        <colgroup>
          <col style="width: 120px" />
          <col style="width: 90px" />
          <col style="width: 90px" />
          <col style="width: 120px" />
          <col style="width: 120px" />
          <col style="width: 90px" />
          <col style="width: 90px" />
          <col style="width: 90px" />
          <col style="width: 120px" />
          <col style="width: 120px" />
          <col style="width: 90px" />
          <col style="width: 90px" />
          <col style="width: 90px" />
          <col style="width: 120px" />
          <col style="width: 120px" />
          <col style="width: 90px" />
          <col style="width: 90px" />
          <col style="width: 90px" />
          <col style="width: 120px" />
          <col style="width: 120px" />
        </colgroup>
        <thead>
          <tr>
            <th rowspan="2">咨询师</th>
            <th colspan="4">合计</th>
            <th colspan="5">初诊情况</th>
            <th colspan="5">复诊情况</th>
            <th colspan="5">再消费情况</th>
          </tr>
          <tr>
            <th>咨询人次</th>
            <th>成交人次</th>
            <th>成交金额</th>
            <th>客单价</th>
            <th>咨询人次</th>
            <th>成交人次</th>
            <th>成交率</th>
            <th>成交金额</th>
            <th>客单价</th>
            <th>咨询人次</th>
            <th>成交人次</th>
            <th>成交率</th>
            <th>成交金额</th>
            <th>客单价</th>
            <th>咨询人次</th>
            <th>成交人次</th>
            <th>成交率</th>
            <th>成交金额</th>
            <th>客单价</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, index) in tableData" :key="index">
            <td>{{ item.consultantName }}</td>
            <td>{{ item.consultCount }}</td>
            <td>{{ item.successCount }}</td>
            <td class="amount">{{ formatCurrency(item.successAmount) }}</td>
            <td class="amount">{{ formatCurrency(item.avgOrderValue) }}</td>
            <td>{{ item.firstVisitCount }}</td>
            <td>{{ item.firstVisitSuccessCount }}</td>
            <td>{{ formatPercentage(item.firstVisitSuccessRate) }}</td>
            <td class="amount">{{ formatCurrency(item.firstVisitAmount) }}</td>
            <td class="amount">{{ formatCurrency(item.firstVisitAvgOrderValue) }}</td>
            <td>{{ item.returnVisitCount }}</td>
            <td>{{ item.returnVisitSuccessCount }}</td>
            <td>{{ formatPercentage(item.returnVisitSuccessRate) }}</td>
            <td class="amount">{{ formatCurrency(item.returnVisitAmount) }}</td>
            <td class="amount">{{ formatCurrency(item.returnVisitAvgOrderValue) }}</td>
            <td>{{ item.moreConsumedCount }}</td>
            <td>{{ item.moreConsumedSuccessCount }}</td>
            <td>{{ formatPercentage(item.moreConsumedSuccessRate) }}</td>
            <td class="amount">{{ formatCurrency(item.moreConsumedAmount) }}</td>
            <td class="amount">{{ formatCurrency(item.moreConsumedAvgOrderValue) }}</td>
          </tr>
          <tr class="total-row">
            <td class="total-label">合计：</td>
            <td>{{ totalConsultCount }}</td>
            <td>{{ totalSuccessCount }}</td>
            <td class="amount">{{ formatCurrency(totalSuccessAmount) }}</td>
            <td class="amount">{{ formatCurrency(totalAvgOrderValue) }}</td>
            <td>{{ totalFirstVisitCount }}</td>
            <td>{{ totalFirstVisitSuccessCount }}</td>
            <td>{{ formatPercentage(totalFirstVisitSuccessRate) }}</td>
            <td class="amount">{{ formatCurrency(totalFirstVisitAmount) }}</td>
            <td class="amount">{{ formatCurrency(totalFirstVisitAvgOrderValue) }}</td>
            <td>{{ totalReturnVisitCount }}</td>
            <td>{{ totalReturnVisitSuccessCount }}</td>
            <td>{{ formatPercentage(totalReturnVisitSuccessRate) }}</td>
            <td class="amount">{{ formatCurrency(totalReturnVisitAmount) }}</td>
            <td class="amount">{{ formatCurrency(totalReturnVisitAvgOrderValue) }}</td>
            <td>{{ totalMoreConsumedCount }}</td>
            <td>{{ totalMoreConsumedSuccessCount }}</td>
            <td>{{ formatPercentage(totalMoreConsumedSuccessRate) }}</td>
            <td class="amount">{{ formatCurrency(totalMoreConsumedAmount) }}</td>
            <td class="amount">{{ formatCurrency(totalMoreConsumedAvgOrderValue) }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="footer-info">
      <span>共 {{ tableData.length }} 条记录</span>
      <span class="date-limit-info">* 查询限制为一个月内的数据</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { storeToRefs } from "pinia";
import { staffStore } from "@/store/staff";
import { teamStore } from "@/store/team";
import { memberStore } from "@/store/member";
import { ElDatePicker, ElButton, ElMessage, ElLoading } from "element-plus";
import { getConsultantStatistics } from "@/api/bill-record";
import projectIntentSelect from "@/components/benefit-management/project-intent-select.vue";
import { CheckBoxFilter, baseFilterItem, filterCustomerSource, filterInfoSource } from "@/components/filter-bar";

const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());
const { currentTeamId, managerList } = storeToRefs(teamStore());
const { isAdmin } = storeToRefs(memberStore());
const { judgmentIsAdmin } = memberStore();

const isManager = computed(() => {
  return managerList.value.includes(90004) || isAdmin.value;
});

const selectedConsultants = ref([]);
const hasExplicitlyClearedConsultants = ref(false);

// consultantFilter 默认值逻辑调整为 computed
const consultantFilter = computed({
  get() {
    if (selectedConsultants.value.length > 0) {
      return selectedConsultants.value;
    }
    if (hasExplicitlyClearedConsultants.value) {
      return [];
    }
    const currentUserId = localStorage.getItem("userId");
    if (!isManager.value) {
      // 不是主管，只能看到自己
      return [currentUserId];
    } else {
      // 主管
      const inList = consultantList.value.some((i) => i.value === currentUserId);
      if (inList) {
        return [currentUserId];
      } else {
        // 展示全部
        return [];
      }
    }
  },
  set(val) {
    selectedConsultants.value = val;
    hasExplicitlyClearedConsultants.value = false;
  },
});

// 处理咨询师筛选器的清除事件
function handleConsultantFilterClear() {
  hasExplicitlyClearedConsultants.value = true;
  selectedConsultants.value = [];
}

const infoSourceFilter = ref([]);
const devPathFilter = ref([]);

const consultantList = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("consultant")).map((i) => ({ value: i.userid }));
});

const projectFilter = ref([]);

const handleProjectChange = (selectedProjects) => {
  handleSearch();
};

const dateRange = ref([]);

const consultDepartmentFilter = ref("");

const departmentOptions = ref([
  { value: "口腔科", label: "口腔科" },
  { value: "美容科", label: "美容科" },
  { value: "皮肤科", label: "皮肤科" },
  { value: "内科", label: "内科" },
]);

const projectOptions = ref([
  { value: "种植牙", label: "种植牙" },
  { value: "牙齿矫正", label: "牙齿矫正" },
  { value: "烤瓷牙", label: "烤瓷牙" },
  { value: "全口检查", label: "全口检查" },
]);

const consultantOptions = ref([
  { value: "23090101", label: "张医生" },
  { value: "luoying", label: "罗医生" },
  { value: "wangwu", label: "王医生" },
]);

const dateShortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近半个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "本月",
    value: () => {
      const end = new Date();
      const start = new Date(end.getFullYear(), end.getMonth(), 1);
      return [start, end];
    },
  },
];

const disabledDate = (time) => {
  if (!dateRange.value || dateRange.value.length === 0) {
    return false;
  }

  const startDate = new Date(dateRange.value[0]);
  const endDate = new Date(dateRange.value[1]);
  const currentDate = new Date(time);

  if (dateRange.value.length === 1) {
    const oneMonthLater = new Date(startDate);
    oneMonthLater.setDate(startDate.getDate() + 31);
    return currentDate > oneMonthLater;
  }

  return false;
};

watch(dateRange, (newValue) => {
  if (newValue && newValue.length === 2) {
    const start = new Date(newValue[0]);
    const end = new Date(newValue[1]);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays > 31) {
      const maxEnd = new Date(start);
      maxEnd.setDate(start.getDate() + 31);
      const formattedMaxEnd = formatDate(maxEnd);

      dateRange.value = [newValue[0], formattedMaxEnd];
      ElMessage.warning("查询范围已限制为一个月内");
    }
  }
});

const formatDateRange = computed(() => {
  if (dateRange.value && dateRange.value.length === 2) {
    return `${dateRange.value[0]} 至 ${dateRange.value[1]}`;
  }
  return "全部日期";
});

const formatDate = (date) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  return `${year}-${month}-${day}`;
};

const tableData = ref([]);
const rawData = ref([]);
const loading = ref(false);

const formatPercentage = (value) => {
  if (value === undefined || value === null || isNaN(value)) {
    return "0.00%";
  }
  return `${(value * 100).toFixed(2)}%`;
};

async function handleGetConsultantStatistics() {
  loading.value = true;
  try {
    const params = {
      startDate: dateRange.value && dateRange.value.length > 0 ? dateRange.value[0] : undefined,
      endDate: dateRange.value && dateRange.value.length > 0 ? dateRange.value[1] : undefined,
      projectIds: projectFilter.value?.length > 0 ? projectFilter.value : undefined,
      infoSource: infoSourceFilter.value,
      devPath: devPathFilter.value,
    };

    if (isManager.value) {
      params.consultantFilter = consultantFilter.value.length > 0 ? consultantFilter.value : undefined;
    } else {
      params.consultantFilter = [localStorage.getItem("userId")];
    }

    const { data, success, message } = await getConsultantStatistics(params);

    if (!success) {
      ElMessage.error(message || "获取数据失败");
      return;
    }

    tableData.value = data.data.map((i) => {
      const consultantName = staffList.value.find((staff) => staff.userid === i.consultant)?.anotherName || "未知医生";

      // 初诊数据计算
      const firstVisitSuccessCount = i.firstVisitSuccessCount || 0;
      const firstVisitSuccessRate = i.firstVisitCount > 0 ? firstVisitSuccessCount / i.firstVisitCount : 0;
      const firstVisitAmount = i.firstVisitAmount || 0;
      const firstVisitAvgOrderValue = firstVisitSuccessCount > 0 ? firstVisitAmount / firstVisitSuccessCount : 0;

      // 复诊数据计算
      const returnVisitSuccessCount = i.returnVisitSuccessCount || 0;
      const returnVisitSuccessRate = i.returnVisitCount > 0 ? returnVisitSuccessCount / i.returnVisitCount : 0;
      const returnVisitAmount = i.returnVisitAmount || 0;
      const returnVisitAvgOrderValue = returnVisitSuccessCount > 0 ? returnVisitAmount / returnVisitSuccessCount : 0;

      // 再消费数据计算
      const moreConsumedSuccessCount = i.moreConsumedSuccessCount || 0;
      const moreConsumedSuccessRate = i.moreConsumedCount > 0 ? moreConsumedSuccessCount / i.moreConsumedCount : 0;
      const moreConsumedAmount = i.moreConsumedAmount || 0;
      const moreConsumedAvgOrderValue = moreConsumedSuccessCount > 0 ? moreConsumedAmount / moreConsumedSuccessCount : 0;

      return {
        ...i,
        consultantName,
        firstVisitSuccessRate,
        firstVisitAvgOrderValue,
        returnVisitSuccessRate,
        returnVisitAvgOrderValue,
        moreConsumedSuccessCount,
        moreConsumedSuccessRate,
        moreConsumedAmount,
        moreConsumedAvgOrderValue,
      };
    });
  } catch (error) {
    console.error("获取数据异常:", error);
    ElMessage.error("获取数据异常");
  } finally {
    loading.value = false;
  }
}

const handleDateChange = () => {
  if (dateRange.value && dateRange.value.length === 2) {
    handleSearch();
  }
};

const handleSearch = () => {
  if (!dateRange.value || dateRange.value.length !== 2) {
    ElMessage.warning("请选择日期范围");
    return;
  }

  const start = new Date(dateRange.value[0]);
  const end = new Date(dateRange.value[1]);
  const diffTime = Math.abs(end - start);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays > 31) {
    ElMessage.warning("查询范围已限制为一个月内");
    return;
  }

  handleGetConsultantStatistics();
};

const totalConsultCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + item.consultCount, 0);
});

const totalSuccessCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + item.successCount, 0);
});

const totalSuccessAmount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + item.successAmount, 0);
});

const totalRefundAmount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + item.refundAmount, 0);
});

const totalNetAmount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + item.netAmount, 0);
});

// 初诊数据统计
const totalFirstVisitCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + item.firstVisitCount, 0);
});

const totalFirstVisitSuccessCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (item.firstVisitSuccessCount || 0), 0);
});

const totalFirstVisitSuccessRate = computed(() => {
  return totalFirstVisitCount.value > 0 ? totalFirstVisitSuccessCount.value / totalFirstVisitCount.value : 0;
});

const totalFirstVisitAmount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (item.firstVisitAmount || 0), 0);
});

const totalFirstVisitAvgOrderValue = computed(() => {
  return totalFirstVisitSuccessCount.value > 0 ? totalFirstVisitAmount.value / totalFirstVisitSuccessCount.value : 0;
});

// 复诊数据统计
const totalReturnVisitCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + item.returnVisitCount, 0);
});

const totalReturnVisitSuccessCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (item.returnVisitSuccessCount || 0), 0);
});

const totalReturnVisitSuccessRate = computed(() => {
  return totalReturnVisitCount.value > 0 ? totalReturnVisitSuccessCount.value / totalReturnVisitCount.value : 0;
});

const totalReturnVisitAmount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (item.returnVisitAmount || 0), 0);
});

const totalReturnVisitAvgOrderValue = computed(() => {
  return totalReturnVisitSuccessCount.value > 0 ? totalReturnVisitAmount.value / totalReturnVisitSuccessCount.value : 0;
});

const totalMoreConsumedCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + item.moreConsumedCount, 0);
});

const totalAvgOrderValue = computed(() => {
  if (totalSuccessCount.value === 0) return 0;
  return totalSuccessAmount.value / totalSuccessCount.value;
});

const totalNewCustomerSuccessCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (item.newCustomerSuccessCount || 0), 0);
});

const totalNewCustomerSuccessRate = computed(() => {
  const totalNewCustomerConsult = totalFirstVisitCount.value + totalReturnVisitCount.value;
  return totalNewCustomerConsult > 0 ? totalNewCustomerSuccessCount.value / totalNewCustomerConsult : 0;
});

const totalNewCustomerSuccessAmount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (item.newCustomerSuccessAmount || 0), 0);
});

const totalNewCustomerAvgOrderValue = computed(() => {
  return totalNewCustomerSuccessCount.value > 0 ? totalNewCustomerSuccessAmount.value / totalNewCustomerSuccessCount.value : 0;
});

const totalMoreConsumedAmount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (item.moreConsumedAmount || 0), 0);
});

const totalMoreConsumedAvgOrderValue = computed(() => {
  return totalMoreConsumedCount.value > 0 ? totalMoreConsumedAmount.value / totalMoreConsumedCount.value : 0;
});

const totalMoreConsumedSuccessCount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + (item.moreConsumedSuccessCount || 0), 0);
});

const totalMoreConsumedSuccessRate = computed(() => {
  return totalMoreConsumedCount.value > 0 ? totalMoreConsumedSuccessCount.value / totalMoreConsumedCount.value : 0;
});

const formatCurrency = (value) => {
  if (value === undefined || value === null || isNaN(value)) {
    return "¥0.00";
  }
  return new Intl.NumberFormat("zh-CN", {
    style: "currency",
    currency: "CNY",
    minimumFractionDigits: 2,
  }).format(value);
};

const exportToExcel = () => {
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '正在导出Excel...',
    background: 'rgba(0, 0, 0, 0.7)'
  });
  
  try {
    const exportData = [];

  exportData.push(["现场咨询面诊情况统计报表"]);
  exportData.push([`统计日期：${formatDateRange.value}`]);

  exportData.push([
    `咨询师：${
      consultantFilter.value.length > 0
        ? consultantFilter.value
            .map((id) => {
              const staff = staffList.value.find((s) => s.userid === id);
              return staff ? staff.anotherName || staff.name : id;
            })
            .join(", ")
        : "全部"
    }`,
  ]);

  exportData.push([`咨询项目：${projectFilter.value.length > 0 ? projectFilter.value.map((p) => p.projectName).join(", ") : "全部"}`]);

  exportData.push([]);

  exportData.push(["咨询师", "合计", "", "", "", "初诊情况", "", "", "", "", "复诊情况", "", "", "", "", "再消费情况", "", "", "", ""]);

  exportData.push(["", "咨询人次", "成交人次", "成交金额", "客单价", "咨询人次", "成交人次", "成交率", "成交金额", "客单价", "咨询人次", "成交人次", "成交率", "成交金额", "客单价", "咨询人次", "成交人次", "成交率", "成交金额", "客单价"]);

  const formatNumber = (num) => {
    return Number((num || 0).toFixed(2));
  };

  tableData.value.forEach((item) => {
    exportData.push([
      item.consultantName, 
      item.consultCount, 
      item.successCount, 
      formatNumber(item.successAmount), 
      formatNumber(item.avgOrderValue), 
      item.firstVisitCount, 
      item.firstVisitSuccessCount, 
      `${(item.firstVisitSuccessRate * 100).toFixed(2)}%`, 
      formatNumber(item.firstVisitAmount), 
      formatNumber(item.firstVisitAvgOrderValue),
      item.returnVisitCount, 
      item.returnVisitSuccessCount, 
      `${(item.returnVisitSuccessRate * 100).toFixed(2)}%`, 
      formatNumber(item.returnVisitAmount), 
      formatNumber(item.returnVisitAvgOrderValue),
      item.moreConsumedCount, 
      item.moreConsumedSuccessCount, 
      `${(item.moreConsumedSuccessRate * 100).toFixed(2)}%`, 
      formatNumber(item.moreConsumedAmount), 
      formatNumber(item.moreConsumedAvgOrderValue)
    ]);
  });

  exportData.push([
    "合计", 
    totalConsultCount.value, 
    totalSuccessCount.value, 
    formatNumber(totalSuccessAmount.value), 
    formatNumber(totalAvgOrderValue.value), 
    totalFirstVisitCount.value, 
    totalFirstVisitSuccessCount.value, 
    `${(totalFirstVisitSuccessRate.value * 100).toFixed(2)}%`, 
    formatNumber(totalFirstVisitAmount.value), 
    formatNumber(totalFirstVisitAvgOrderValue.value),
    totalReturnVisitCount.value, 
    totalReturnVisitSuccessCount.value, 
    `${(totalReturnVisitSuccessRate.value * 100).toFixed(2)}%`, 
    formatNumber(totalReturnVisitAmount.value), 
    formatNumber(totalReturnVisitAvgOrderValue.value),
    totalMoreConsumedCount.value, 
    totalMoreConsumedSuccessCount.value, 
    `${(totalMoreConsumedSuccessRate.value * 100).toFixed(2)}%`, 
    formatNumber(totalMoreConsumedAmount.value), 
    formatNumber(totalMoreConsumedAvgOrderValue.value)
  ]);

  const wb = XLSX.utils.book_new();
  const ws = XLSX.utils.aoa_to_sheet(exportData);

  const colWidths = [
    { wch: 15 }, 
    { wch: 10 }, { wch: 10 }, { wch: 12 }, { wch: 10 }, 
    { wch: 10 }, { wch: 10 }, { wch: 10 }, { wch: 12 }, { wch: 10 }, 
    { wch: 10 }, { wch: 10 }, { wch: 10 }, { wch: 12 }, { wch: 10 }, 
    { wch: 10 }, { wch: 10 }, { wch: 10 }, { wch: 12 }, { wch: 10 }
  ];

  ws["!cols"] = colWidths;

  ws["!merges"] = [
    { s: { r: 0, c: 0 }, e: { r: 0, c: 19 } },
    { s: { r: 1, c: 0 }, e: { r: 1, c: 19 } },
    { s: { r: 2, c: 0 }, e: { r: 2, c: 19 } },
    { s: { r: 3, c: 0 }, e: { r: 3, c: 19 } },
    { s: { r: 5, c: 0 }, e: { r: 6, c: 0 } },
    { s: { r: 5, c: 1 }, e: { r: 5, c: 4 } },
    { s: { r: 5, c: 5 }, e: { r: 5, c: 9 } },
    { s: { r: 5, c: 10 }, e: { r: 5, c: 14 } },
    { s: { r: 5, c: 15 }, e: { r: 5, c: 19 } },
  ];

  XLSX.utils.book_append_sheet(wb, ws, "咨询面诊表");

  const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
  const blob = new Blob([excelBuffer], {
    type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  });

  const now = new Date();
  const fileName = `咨询面诊表_${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}.xlsx`;

  saveAs(blob, fileName);

  ElMessage.success("导出Excel成功");
  } catch (error) {
    console.error("导出Excel失败:", error);
    ElMessage.error("导出Excel失败");
  } finally {
    loadingInstance.close();
  }
};

onMounted(async () => {
  await judgmentIsAdmin();
  if (staffList.value.length === 0) await getStaffList();
  const end = new Date();
  const start = new Date(end.getFullYear(), end.getMonth(), 1);

  dateRange.value = [formatDate(start), formatDate(end)];

  handleGetConsultantStatistics();
});

const resetFilters = () => {
  dateRange.value = [];
  consultDepartmentFilter.value = "";
  projectFilter.value = [];
  infoSourceFilter.value = [];
  devPathFilter.value = [];
  selectedConsultants.value = [];
  handleSearch();
};

watch(
  () => currentTeamId.value,
  () => {
    if (dateRange.value && dateRange.value.length === 2) {
      handleGetConsultantStatistics();
    }
  }
);

watch(consultantFilter, () => {
  handleSearch();
});

watch(projectFilter, () => {
  handleSearch();
});

watch(infoSourceFilter, () => {
  handleSearch();
});

watch(devPathFilter, () => {
  handleSearch();
});
</script>

<style scoped>
.report-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
  background-color: #f5f7fa;
  overflow: hidden;
  min-width: 1200px;
}

.report-header {
  background-color: white;
  padding: 15px 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
}

.report-title {
  text-align: center;
  font-weight: bold;
  font-size: 20px;
  margin-bottom: 5px;
  color: #303133;
}

.report-date {
  text-align: center;
  font-size: 14px;
  margin-bottom: 15px;
  color: #606266;
}

.filter-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-label {
  white-space: nowrap;
  margin-right: 5px;
  font-size: 14px;
  color: #606266;
}

.filter-actions {
  margin-left: auto;
  display: flex;
  gap: 10px;
}

.date-range {
  min-width: 320px;
}

:deep(.el-date-editor.el-input__wrapper) {
  width: 350px;
}

.filter-controls {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.table-container {
  flex: 1;
  margin: 15px 0 0 0;
  background-color: white;
  overflow: auto;
  position: relative;
  border-top: 1px solid #ebeef5;
  border-bottom: 1px solid #ebeef5;
  max-height: 70vh; 
}

.performance-table {
  width: 100%;
  min-width: 2080px; 
  border-collapse: collapse;
  font-size: 14px;
  table-layout: fixed;
}

.performance-table th,
.performance-table td {
  border: 1px solid #ebeef5;
  padding: 12px 8px;
  text-align: center;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.performance-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 10;
}

.performance-table .amount {
  text-align: right;
  padding-right: 10px;
  min-width: 100px;
  max-width: 120px;
  white-space: nowrap;
  overflow: visible;
}

.total-row {
  background-color: #ebeef5;
  font-weight: bold;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.total-label {
  text-align: right;
  padding-right: 10px;
}

.footer-info {
  background-color: white;
  padding: 10px 20px;
  margin: 0;
  display: flex;
  justify-content: space-between;
  color: #606266;
  font-size: 14px;
  flex-shrink: 0;
  border-top: 1px solid #ebeef5;
}

.date-limit-info {
  color: #e6a23c;
  font-style: italic;
}

@media print {
  .report-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 0;
    background-color: #f5f7fa;
    overflow: hidden;
    min-width: 1200px;
  }

  .report-header {
    background-color: white;
    padding: 15px 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    margin-bottom: 15px;
  }

  .report-title {
    text-align: center;
    font-weight: bold;
    font-size: 20px;
    margin-bottom: 5px;
    color: #303133;
  }

  .report-date {
    text-align: center;
    font-size: 14px;
    margin-bottom: 15px;
    color: #606266;
  }

  .filter-section {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
  }

  .filter-item {
    display: flex;
    align-items: center;
  }

  .filter-label {
    white-space: nowrap;
    margin-right: 5px;
    font-size: 14px;
    color: #606266;
  }

  .filter-actions {
    margin-left: auto;
    display: flex;
    gap: 10px;
  }

  .date-range {
    min-width: 320px;
  }
  .table-container {
    margin: 0;
    border: none;
    overflow: auto;
    height: auto;
    max-height: 60vh;
  }

  .performance-table th,
  .performance-table td {
    font-size: 12px;
    padding: 8px 4px;
  }
}

@media (max-width: 768px) {
  .report-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .date-range {
    width: 100%;
  }

  :deep(.el-date-editor.el-input__wrapper) {
    width: 100%;
  }

  .filter-controls {
    flex-direction: column;
    align-items: stretch;
  }
}

.performance-table th[rowspan="2"] {
  vertical-align: middle;
}

.performance-table th {
  border: 1px solid #ebeef5;
  padding: 12px 8px;
  text-align: center;
  height: 40px;
  background-color: #f5f7fa;
  color: #606266;
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 10;
}

@media (min-width: 1400px) {
  .performance-table .amount {
    min-width: 120px;
    max-width: 150px;
  }
}
</style>