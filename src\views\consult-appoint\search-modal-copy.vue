<template>
  <el-dialog :model-value="props.visible" :width="width" title="报备详情" @close="close()">
    <div class="flex items-center p-10px border-b border-gray-200">
      <el-popover :visible="popoverVisible" placement="bottom" :width="popoverWidth">
        <template #reference>
          <el-input v-model="keyword" ref="inputRef" placeholder="请输入客户姓名、身份证号、手机号查询" class="flex-grow"></el-input>
        </template>
        <el-scrollbar ref="scrollRef" wrap-style="max-height:60vh">
          <div class="h-100vh bg-red-200">
            
          </div>
        </el-scrollbar>
      </el-popover>
      <el-button class="ml-10px" type="primary" @click="popoverVisible = true">搜索</el-button>
    </div>
    <div color-normal class="text-15px font-semibold px-10px mt-15px mb-10px">今日预约“咨询”未到院客户</div>
    <el-table stripe :max-height="tableHeight" :data="list" v-loading="loading">
      <el-table-column prop="name" label="姓名" :min-width="100">
        <template #default="{ row: { name } }">
          <el-popover :key="_id" placement="top-start" :width="320" trigger="hover">
            <template #reference>
              <div class="truncate">{{ name }}</div>
            </template>
            <el-text>{{ name }}</el-text>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="age" label="性别/年龄" :min-width="100">
        <template #default="{ row: { name } }">
          <div>性别 / 年龄</div>
        </template>
      </el-table-column>
      <el-table-column prop="mobile" label="联系方式" :min-width="100"></el-table-column>
      <el-table-column prop="reportPeople" label="预约类型" :min-width="100"></el-table-column>
      <el-table-column prop="stage" label="预约咨询师" :min-width="100"></el-table-column>
      <el-table-column prop="firstArriveTime" label="预约日期" :min-width="100"></el-table-column>
      <el-table-column prop="consume" label="预约时段" :min-width="100"></el-table-column>
      <el-table-column prop="recentPayTime" label="意向项目" :min-width="100"></el-table-column>
      <el-table-column prop="plan" label="备注" :min-width="100"></el-table-column>
      <el-table-column prop="customerSource" label="登记人" :min-width="100"></el-table-column>
      <el-table-column fixed="right" class-name="el-table-fixed-column--shadow" label="操作" width="100">
        <template #default="{ row }">
          <el-text class="cursor-pointer mr-5px" type="primary">新增咨询</el-text>
        </template>
      </el-table-column>
    </el-table>
    <div class="px-10px">
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize" @handle-current-change="changePage" />
    </div>
  </el-dialog>
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { useElementBounding, onClickOutside, useWindowSize } from "@vueuse/core";
import { ElMessage } from "element-plus";
import useElPagination from "@/hooks/useElPagination";
import pagination from "@/components/pagination/pagination.vue";

const emits = defineEmits(["close", "change"]);
const props = defineProps({
  visible: { type: Boolean, default: false },
  width: { type: String, default: "500px" },
});

const loading = ref(false);
const inputRef = ref(null);
const keyword = ref("");
const scrollRef = ref(null);
const total = ref(0);

const { width: inputWidth } = useElementBounding(inputRef);
const { height: winHeight } = useWindowSize();
const tableHeight = computed(() => winHeight.value * 0.85 - 360);
const popoverWidth = computed(() => Math.max(400, inputWidth.value));
const popoverVisible = ref(false);
const { page, pageSize, changePage, changeSize } = useElPagination(getList);

onClickOutside(scrollRef, () => {
  if (popoverVisible.value) popoverVisible.value = false;
});

function close() {
  emits("close");
}

async function getList(params) {}
async function confirm() {
  close();
}

watch(
  () => props.visible,
  (n) => {
    if (n) {
      cateId.value = props.id;
    }
  }
);
</script>