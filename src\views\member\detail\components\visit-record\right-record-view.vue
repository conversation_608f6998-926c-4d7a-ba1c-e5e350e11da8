<template>
  <customer-view ref="customerviewRef" :canRemove="canRemove" :removeSymbol="removeSymbol" class="pt-10px w-full pb-80px" :operateType="operationType" :templateList="selfTemplateList" @onClose="close" @onSuccess="success" :headerHeight="-10" :formData="selfCurrentRecord" :col="col" @onRemove="onRemove"></customer-view>
</template>
<script setup>
import { watch, ref, computed } from "vue";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import { ElMessage, ElMessageBox } from "element-plus";
import { addMedicalRecord, updateMedicalRecord, removeMedicalRecord } from "@/api/member.js";
import { updateFile } from "@/api/uploadFIle.js";
import { memberStore } from "@/store/member";
import { teamStore } from "@/store/team";
import { templateStore } from "@/store/template";
import { createServiceRecord } from "@/utils/service";
import customerView from "@/views/member/customer/drawer-customer-view.vue";

const { currentTeam } = storeToRefs(teamStore());
const removeSymbol = ref("");
const close = (operateType) => {
  if (operateType === "add") emit("reload");
  getInitData();
  emit("close");
};
const emit = defineEmits(["reload", "onRemove", "close"]);

const store = templateStore();
const { corpTemplateList } = storeToRefs(store);
const { getCorpTemplate } = store;
const props = defineProps({
  templateType: {
    type: String,
    default: "",
  },
  currentRecord: {
    type: Object,
    default: () => ({}),
  },
  operationType: {
    type: String,
    default: "edit",
  },
  customer: {
    type: Object,
    default: () => ({}),
  },
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "",
  },
});
const col = { lg: 24, md: 24, span: 24 };
const canRemove = computed(() => Boolean(props.currentRecord._id));
const selfCurrentRecord = computed(() => {
  const { currentRecord } = props;
  if (currentRecord["corp"] === "current corp") currentRecord["corp"] = "本院";
  if (currentRecord["corp"] === "other") currentRecord["corp"] = "其他";
  return JSON.parse(JSON.stringify(currentRecord));
});
const selfTemplateList = ref([]);
// 监听模板列表和当前记录的变化
watch(
  () => [props.currentRecord, props.templateType],
  ([record, templateType]) => {
    // 如果当前记录和模板列表都存在 则获取初始化数据
    if (record && templateType) {
      getInitData();
    }
  },
  { immediate: true }
);
async function getInitData() {
  if (!Array.isArray(corpTemplateList.value) || corpTemplateList.value.length === 0) {
    await getCorpTemplate();
  }
  let templateList = corpTemplateList.value.filter((i) => i.templateType === props.templateType);
  if (props.operationType === "edit") {
    templateList = templateList.map((item) => {
      item.templateList &&
        item.templateList.forEach((e) => {
          if (props.currentRecord[e.title]) {
            e.value = props.currentRecord[e.title];
            if (e.title === "corp" && e.value === "current corp") {
              e.value = "本院";
            }
            if (e.title === "corp" && e.value === "other") {
              e.value = "其他";
            }
          }
        });
      return item;
    });
  }
  selfTemplateList.value = templateList;
}
const success = async (params, callback) => {
  if (!params) return;
  // 兼容老数据
  let { templateType, customer, currentRecord } = props;
  if (params.files) {
    const files = await uploadImages(params.files);
    if (!files) {
      ElMessage.error("文件上传失败");
      return;
    }
    params.files = files;
  }

  if (params.plyFile && params.plyFile.length) {
    const plyFile = await uploadImages(params.plyFile);
    if (!plyFile) {
      ElMessage.error("文件上传失败");
      callback && callback("fail");
      return;
    }
    params.plyFile = plyFile;
  }
  if (params.dcmFile && params.dcmFile.length) {
    const dcmFile = await uploadImages(params.dcmFile);
    if (!dcmFile) {
      ElMessage.error("文件上传失败");
      callback && callback("fail");
      return;
    }
    params.dcmFile = dcmFile;
  }

  params.corpId = localStorage.getItem("corpId");
  params.memberId = customer._id;
  params.customerName = customer.name;
  params.medicalType = templateType;
  let template = corpTemplateList.value.find((i) => i.templateType === props.templateType);

  if (currentRecord._id) {
    params._id = currentRecord._id;
    params.userId = localStorage.getItem("userId");
  } else {
    params.creator = localStorage.getItem("userId");
    params.createTime = dayjs().valueOf();
    const { timeTitle } = template.service;
    if (timeTitle && params[timeTitle]) params["sortTime"] = dayjs(params[timeTitle]).valueOf();
  }
  const { data, success, message } = await (currentRecord && currentRecord._id ? updateMedicalRecord : addMedicalRecord)(params);
  if (success) {
    //更新列表
    emit("reload", data.data);
    customerviewRef.value && typeof customerviewRef.value.closeDrawer === "function" && customerviewRef.value.closeDrawer();
    callback && callback("success");
    ElMessage.success(message);
  } else {
    callback && callback("fail");
    ElMessage.error(message);
  }
};

async function uploadImages(images) {
  let fileList = images.filter((i) => i.base64);
  let otherFileList = images.filter((i) => !i.base64);

  if (fileList.length) {
    try {
      const corpId = localStorage.getItem("corpId");
      const res = await Promise.all(fileList.map((i) => updateFile(i.name, i.file, corpId)));
      fileList = fileList.map((item, idx) => {
        const url = res[idx].download_url;
        return {
          url,
          type: item.type,
          size: item.size,
          name: item.name,
        };
      });
      // otherFileList  fileList 合并
      return [...otherFileList, ...fileList];
    } catch (e) {
      return false;
    }
  }
}

async function createServiceRecordAction(params, template) {
  let { customer } = props;
  const { timeName, timeTitle } = template.service;
  const time = params[timeTitle];
  const { memberInfo } = memberStore();
  const { anotherName } = memberInfo;
  let taskContent = `${anotherName}为客户${customer.name}新增一份${template.name}`;
  if (time && dayjs(time).isValid()) {
    taskContent += `，${timeName}为${dayjs(time).format("YYYY-MM-DD")}`;
  }
  let item = {
    taskContent,
    executionTime: new Date().getTime(),
    customerId: customer._id,
    executeTeamId: currentTeam.value ? currentTeam.value.teamId : "",
    teamName: currentTeam.value ? currentTeam.value.name : "",
    eventType: "remindFiling",
    customerName: customer.name,
  };
  await createServiceRecord(item);
}
const customerviewRef = ref();
async function onRemove() {
  try {
    await ElMessageBox.confirm("确定删除该记录吗？", "提示", {
      type: "warning",
    });
    const { memberInfo } = memberStore();
    const { success, message } = await removeMedicalRecord({
      corpId: memberInfo.corpId,
      memberId: props.customer._id,
      medicalType: props.templateType,
      _id: props.currentRecord._id,
    });
    if (success) {
      ElMessage.success(message);
      removeSymbol.value = Date.now();
      emit("onRemove");
    } else {
      ElMessage.error(message);
    }
  } catch (e) {
    console.log(e);
  }
}
</script>

<style lang="scss">
.group-title {
  padding: 0 12px 16px;
  color: var(--yc-main-text-color);
  font-weight: 600;
  font-size: 14px;
}

.no-header .el-drawer__header {
  display: none;
}
</style>
