<template>
  <div v-loading="loading" class="shadow-xl my-10px">
    <div class="flex items-center justify-between px-15px py-10px ">
      <div class="font-semibold text-14px font-semibold">待办事项</div>
      <div class="flex-shrink-0 cursor-pointer pl-40px transform translate-y-2px text-center" @click="refresh">
        <el-icon :class="refreshing ? 'animate-spin' : ''">
          <Refresh />
        </el-icon>
      </div>
    </div>
    <div class="px-15px pb-10px">
      <div v-for="(i, idx) in records" :key="i._id" :class="idx > 0 ? 'mt-10px' : ''"
        class="py-12px px-15px bg-gray-100 rounded-6px">
        <div class="flex items-center justify-between mb-10px">
          <div class="font-semibold text-14px">{{ i.typeStr }}</div>
          <div class="text-12px">{{ i.planTime }} </div>
        </div>
        <div class="mb-10px text-14px text-gray-500 leading-20px">
          {{ i.taskContent }}
        </div>
        <div class="flex justify-end text-12px" color-primary>
          <div v-if="i.canSend" class="send-btn py-4px px-8px bg-fff rounded-4px" @click="sendMessage(i)">
            点击发送
          </div>
          <div class="send-btn py-4px px-8px bg-fff rounded-4px" @click="changeTodo('closed', i)">取消任务</div>
          <div class="send-btn py-4px px-8px bg-fff rounded-4px" @click="changeTodo('treated', i)">完成任务</div>
        </div>
      </div>
      <div v-if="more"
        class="py-6px mt-10px rounded-4px cursor-pointer flex items-center justify-center text-gray-400 hover:text-blue-500"
        @click="loadmore()">
        <div class="text-14px">加载更多</div>
        <el-icon>
          <ArrowDownBold />
        </el-icon>
      </div>
      <div v-if="records.length === 0" class="text-center px-10px py-15px bg-gray-50 rounded text-14px text-gray-400">
        暂无待跟进事项
      </div>
      <!-- bg-[f2f3f4] -->
    </div>
  </div>
  <follow-up-modal :status="status" :todo="currentTodo" :visible="visible" :width="width" @change="reload()"
    @close="close" />
</template>
<script setup>
import { ref, computed, inject, onMounted, onUnmounted } from 'vue';
import { watchDebounced } from '@vueuse/core'
import { storeToRefs } from 'pinia';
import dayjs from 'dayjs';
import { ElMessage } from 'element-plus';
import { getToDoList, updateTodo } from "@/api/todo";
import { createRecord } from '@/api/survery';
import { addArticleSendRecord } from '@/api/knowledgeBase';
import { formatTodoFiles } from '@/utils/todo';

import { ToDoEventType } from "@/baseData";
import useModal from '@/hooks/useModal';
import { configStore } from "@/store/config";
import { memberStore } from "@/store/member";
import * as ww from "@/libs/@wecom/jssdk/wecom.esm.js";

import followUpModal from './follow-up-modal'

const emits = defineEmits(['change'])
const props = defineProps({
  customer: { type: Object, default: () => ({}) },
  maxHeight: { type: Number },
  team: { type: Object, default: () => ({}) },
  unionId: { type: String, default: '' }
})
const { memberInfo } = storeToRefs(memberStore());
const payload = computed(() => `${props.customer._id}_${props.team ? props.team.teamId : ''}`)

const trigger = inject('side-bar-event-trigger')

const loading = ref(false);
const records = ref([]);
const more = ref(false);
const page = ref(1);
async function getList() {
  if (loading.value) return;
  if (memberInfo.value.corpId && props.customer._id && props.team && props.team.teamId) {
    loading.value = true;
    const params = { corpId: memberInfo.value.corpId, executorUserId: memberInfo.value.userid, customerId: props.customer._id, eventStatus: "untreated", plannedExecutionEndTime: dayjs().format('YYYY-MM-DD'), onlyTodo: true };
    const { data } = await getToDoList(page.value, 10, params, { orderBy: 'plannedExecutionTime', orderType: 'asc' }, props.team.teamId);
    const list = data && Array.isArray(data.data) ? data.data.map(i => {
      const timestamp = i.expireTime ? dayjs(i.expireTime).valueOf() : 0;
      const days = Math.floor((timestamp - dayjs().valueOf()) / (1000 * 60 * 60 * 24));
      const canSendContent = typeof i.sendContent === 'string' && i.sendContent.trim() !== '';
      const { files } = formatTodoFiles(i);
      const canSendAttachment = files.length > 0;
      return {
        ...i,
        files,
        canSend: canSendContent || canSendAttachment,
        planTime: i.plannedExecutionTime ? dayjs(i.plannedExecutionTime).format('YYYY年MM月DD日HH:mm') : '',
        expireTimeStr: i.expireTime ? dayjs(i.expireTime).format('YYYY-MM-DD HH:mm') : '',
        typeStr: ToDoEventType[i.eventType] || '',
        remainDays: days >= 0 ? days : ''
      }
    }) : [];
    more.value = data && data.pages > page.value;
    records.value = page.value === 1 ? list : [...records.value, ...list];
    loading.value = false;
  }
}
function reload() {
  emits('change', { name: 'follow-up-change' })
  page.value = 1;
  getList()
}

function loadmore() {
  if (more.value && !loading.value) {
    page.value = page.value + 1;
    getList()
  }
}

onMounted(() => {
  trigger.$on('update-follow-up', teamId => {
    if (teamId && teamId === props.team.teamId) reload()
  })
})

onUnmounted(() => trigger.$off('update-follow-up'))

watchDebounced(
  payload,
  () => {
    page.value = 1;
    if (props.customer._id && props.team && props.team.teamId) getList();
    else records.value = [];
  },
  { debounce: 500, maxWait: 1000, immediate: true },
)

const { close, show, visible, width } = useModal(); //  选择客户弹窗
const currentTodo = ref({});
const status = ref('closed');
function changeTodo(s, todo) {
  status.value = s;
  currentTodo.value = todo;
  show()
}

async function sendMessage(item) {
  let { _id, sendContent, eventType, pannedEventSendFile, files } = item;
  if (sendContent) {
    ww.sendChatMessage({
      msgtype: "text",
      enterChat: true,
      text: { content: sendContent }
    });
    // sendChatMessage({ type: "text", desc: sendContent });
  }
  files.forEach(async i => {
    console.log(i)
    if (i._type == 'image' && i.URL) {
      sendLinkMessage(i.file.name || '图片', pannedEventSendFile.name || '', i.URL)
    }
    if (i._type === 'questionnaire' && i.file && i.file.surveryId) {
      const answerId = await send(i.file.surveryId);
      const url = `${import.meta.env.VITE_PATIENT_PAGE_BASE_URL}pages/survery/fill?corpId=${memberInfo.value.corpId}&surveryId=${i.file.surveryId
        }&answerId=${answerId}&memberId=${props.customer._id}&unionid=${props.customer.unionid || props.unionId || ""}&name=${encodeURIComponent(props.customer.name || '')}`
      sendLinkMessage('请填写问卷', i.file && i.file.name ? i.file.name : '', url)
    }
    if (i._type === 'article' && i.file && i.file.url) {
      sendLinkMessage('宣教文章', i.file.name || '', i.file.url)
    }
  })
  // if (pannedEventSendFile && pannedEventSendFile.type === "article" && pannedEventSendFile.url) {
  //   if (pannedEventSendFile && pannedEventSendFile.articleId) createSendRecord(pannedEventSendFile.articleId)
  //   sendLinkMessage('宣教文章', i.file && i.file.name ? i.file.name : '', pannedEventSendFile.url)
  // }
  // if (pannedEventSendFile && pannedEventSendFile.type === "questionnaire" && pannedEventSendFile.surveryId) {
  //   const answerId = await send(pannedEventSendFile.surveryId);
  //   if (!answerId) return;
  //   const url = `${import.meta.env.VITE_PATIENT_PAGE_BASE_URL}pages/survery/fill?corpId=${memberInfo.value.corpId}&surveryId=${pannedEventSendFile.surveryId
  //     }&memberId=${props.customer._id}&unionid=${props.customer.unionid || props.unionId || ""}&answerId=${answerId}&name=${encodeURIComponent(props.customer.name || '')}`
  //   sendLinkMessage('请填写问卷', pannedEventSendFile.name || '', url)
  // }
}

const sendLoading = ref(false);
async function send(surveryId) {
  if (sendLoading.value) return;
  sendLoading.value = true;
  const { success, data, message } = await createRecord(surveryId, props.customer._id, props.customer.name);
  sendLoading.value = false;
  if (success) return data.id;
  else ElMessage.error(message);
  return ''
}

function sendLinkMessage(desc, title, url) {
  console.log({desc, title, url})
  ww.sendChatMessage({
    msgtype: "news",
    enterChat: true,
    news: {
      link: url, //H5消息页面url 必填
      title,
      desc, //H5消息摘要
      imgUrl: "https://796f-youcan-clouddev-1-8ewcqf31dbb2b5-1317294507.tcb.qcloud.la/other/%E6%A1%A3%E6%A1%88.png?sign=ad25044f43b599712bb290e486bd5038&t=1701328162",
    }
  });
}

async function updateEvent(id) {
  await updateTodo(id, { eventStatus: "treated" });
  reload();
}

function createSendRecord(articleId) {
  addArticleSendRecord({ corpId: memberInfo.value.corpId, userId: memberInfo.value.userid, articleId, customerId: props.customer._id })
}

function refresh() {
  page.value = 1;
  getList()
}
</script>
<style scoped>
.bg-light-blue {
  background: #f6f9fe;
}


.send-btn {
  border: 1px solid;
}

.send-btn+.send-btn {
  margin-left: 6px;
}
</style>
