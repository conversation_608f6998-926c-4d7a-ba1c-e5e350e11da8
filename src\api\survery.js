import { post } from "./axios";

async function useSurvery(data) {
  const res = await post("survery", data);
  return res;
}
export async function createRecord(surveryId, memberId, customer) {
  const corpId = localStorage.getItem("corpId");
  const userId = localStorage.getItem("userId");
  const res = await useSurvery({
    type: "createRecord",
    corpId,
    userId,
    surveryId,
    memberId,
    customer,
  });
  return res;
}

export async function getAnswerCount(surveryId) {
  const corpId = localStorage.getItem("corpId");
  const res = await useSurvery({ type: "getAnswerCount", corpId, surveryId });
  return res;
}

export async function getAnswerList(data) {
  const corpId = localStorage.getItem("corpId");
  const res = await useSurvery({
    type: "getAnswerList",
    corpId,
    ...data,
  });
  return res;
}

export async function getList(page, pageSize, name, status, showCount = false, cateIds) {
  const corpId = localStorage.getItem("corpId");
  const res = await useSurvery({
    type: "getList",
    corpId,
    page,
    pageSize,
    name,
    status,
    showCount,
    cateIds,
  });
  return res;
}

export async function getDetail(corpId, id) {
  const res = await useSurvery({ type: "getDetail", corpId, id });
  return res;
}

export async function setSurvery(survery) {
  const res = await useSurvery({ type: "setSurvery", ...survery });
  return res;
}

export async function setSurveryStatus(_id, status) {
  const res = await useSurvery({ type: "setSurveryStatus", _id, status });
  return res;
}

export async function removeSurvery(_id, corpId) {
  const res = await useSurvery({ type: "removeSurvery", _id, corpId });
  return res;
}

export async function getAnswer(surveryId, memberId, answerId) {
  const res = await useSurvery({
    type: "getAnswer",
    corpId: localStorage.getItem("corpId"),
    surveryId,
    memberId,
    answerId,
  });
  return res;
}

export async function addSurveryCate(data) {
  const res = await useSurvery({ type: "addSurveryCate", ...data });
  return res;
}

export async function updateSurveryCate(data) {
  const res = await useSurvery({ type: "updateSurveryCate", ...data });
  return res;
}

export async function sortSurveryCate(data) {
  const res = await useSurvery({ type: "sortSurveryCate", ...data });
  return res;
}

export async function deleteSurveryCate(data) {
  const res = await useSurvery({ type: "deleteSurveryCate", ...data });
  return res;
}

export async function getSurveryCateList(data) {
  const res = await useSurvery({ type: "getSurveryCateList", ...data });
  return res;
}

export async function getSurveryCount(data) {
  const res = await useSurvery({ type: "getSurveryCount", ...data });
  return res;
}

export async function setSurveryCate(data) {
  const res = await useSurvery({ type: "setSurveryCate", ...data });
  return res;
}
