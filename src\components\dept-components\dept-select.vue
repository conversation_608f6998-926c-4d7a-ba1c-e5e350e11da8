<template>
  <el-select v-model="selectedValue" :placeholder="placeholder" :clearable="clearable" :filterable="filterable" :style="{ width: width }" :disabled="disabled" @change="handleChange">
    <el-option v-for="item in deptOptions" :key="item.value" :label="item.label" :value="item.value" />
  </el-select>
</template>
  
  <script setup>
import { ref, watch, onMounted, defineProps, defineEmits } from "vue";
import { getDeptList } from "@/api/dept-manage";
import { ElMessage } from "element-plus";

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: "",
  },
  placeholder: {
    type: String,
    default: "全部",
  },
  clearable: {
    type: Boolean,
    default: true,
  },
  filterable: {
    type: Boolean,
    default: true,
  },
  width: {
    type: String,
    default: "150px",
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["update:modelValue", "change", "deptName"]);

const selectedValue = ref(props.modelValue);
const deptOptions = ref([]);

// 监听modelValue的变化
watch(
  () => props.modelValue,
  (newVal) => {
    selectedValue.value = newVal;
  }
);

// 监听选中值的变化
watch(
  () => selectedValue.value,
  (newVal) => {
    emit("update:modelValue", newVal);
    // 找到对应的科室名称
    const selectedDept = deptOptions.value.find((dept) => dept.value === newVal);
    if (selectedDept) {
      emit("deptName", selectedDept.label);
    } else {
      emit("deptName", "");
    }
  }
);

// 选择变化处理
const handleChange = (value) => {
  const selectedDept = deptOptions.value.find((dept) => dept.value === value);
  if (selectedDept) {
    emit("change", { value, label: selectedDept.label, dept: selectedDept });
  } else {
    emit("change", { value, label: "", dept: null });
  }
};

// 获取科室列表
const fetchDeptList = async () => {
  try {
    const { data, success } = await getDeptList();
    if (success && data && Array.isArray(data.list)) {
      deptOptions.value = data.list
        .map((i) => ({
          label: i.deptName,
          value: i.deptId,
          sort: i.sort,
          _id: i._id,
        }))
        .sort((a, b) => a.sort - b.sort);
    } else {
      deptOptions.value = [];
    }
  } catch (error) {
    console.error("获取科室列表失败:", error);
    ElMessage.error("获取科室列表失败");
    deptOptions.value = [];
  }
};

// 页面加载时获取数据
onMounted(() => {
  fetchDeptList();
});

// 暴露方法给父组件
defineExpose({
  fetchDeptList,
  deptOptions,
});
</script>
  