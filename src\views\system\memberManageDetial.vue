<template>
  <my-layout bg-fff common-shadow>
    <layout-main>
      <div class="manage-detial">
        <div title-bar class="title">员工详情</div>
        <el-form :rules="rules" :model="ruleForm">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="ID:">{{ corpMember.userid }}</el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="姓名:"><ww-user :openid="corpMember.name"></ww-user></el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="性别:">{{ corpMember.gender === "0" ? "男" : "女" }}</el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="部门">
                <ww-user v-for="item in corpMember.department" :openid="item" type="departmentName"></ww-user>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="别名:" prop="otherName">
                <el-input placeholder="请输入别名" v-model="ruleForm.anotherName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="手机号:">
                <el-input placeholder="请输入手机号" v-model="ruleForm.mobile"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col>
              <el-form-item label="头像:">
                <upload-file @hand-file-change="onHandFileChange" :fileList="fileList" fileType="image/*"></upload-file>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="平台角色:">
                <div class="corpRolesClass">
                  <el-tag class="role-tag" v-for="tag in roleNameList" :key="tag._id" closable @close="tagClose(tag)">
                    {{ tag.roleName }}
                  </el-tag>
                  <el-button class="item" type="primary" size="small" :icon="Plus" plain @click="addRole">新增</el-button>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="所属团队:">
                <el-row>
                  <el-col :span="24">
                    <div v-for="item in teamList" :key="item._id" @click="changeMainTeam(item.teamId)">
                      <div class="team">
                        <div class="name">{{ item.name }}</div>
                        <img v-if="selectTeamId !== item.teamId" src="@/assets/check.png" class="select-image" />
                        <img v-else src="@/assets/check-active.png" class="select-image" />
                        <div class="teamName">设置为主团队</div>
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="24">
                    <div class="addTeam" @click="addTeam">
                      <el-icon>
                        <Plus color="#006eff" />
                      </el-icon>
                      <div class="title">加入团队</div>
                    </div>
                  </el-col>
                </el-row>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col>
              <el-form-item label="员工介绍:">
                <el-input type="textarea" :autosize="{ minRows: 6 }" placeholder="请输入员工介绍" v-model="ruleForm.memberTroduce"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </layout-main>
    <layout-item>
      <div text-center p-15 common-shadow>
        <el-button class="w-100px" @click="cancel">取消</el-button>
        <el-button class="w-100px" @click="save" type="primary">保存</el-button>
      </div>
    </layout-item>
  </my-layout>
  <el-dialog v-model="addRoledialogVisible" title="新增角色" draggable :width="600">
    <div class="role_view">
      <el-checkbox-group v-model="checkRolesList">
        <el-checkbox :label="item.roleName" v-for="item in roles" :key="item._id" />
      </el-checkbox-group>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="roleDialogCancel">取消</el-button>
        <el-button type="primary" @click="roleDialogSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup>
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import WwUser from "@/components/ww-user/index.vue";
import uploadFile from "@/components/upload-file/index.vue";
import { ref, reactive, inject } from "vue";
import { useRoute, useRouter } from "vue-router";
import { updateFile } from "@/api/uploadFIle.js";
import { ElMessage } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
const router = useRouter();
let fileList = [];
let rules = reactive({
  otherName: [{ required: true, message: "请输入别名", trigger: "blur" }],
});
let corpMember = ref({});
let checkRolesList = ref([]);
let addRoledialogVisible = ref(false);
let detial = localStorage.getItem("MEMBERDETIAL") && JSON.parse(localStorage.getItem("MEMBERDETIAL"));
let { member, teams, roles } = detial;
const { anotherName, mobile, memberTroduce, avatar } = member;
let ruleForm = reactive({
  anotherName,
  mobile,
  memberTroduce,
  avatar,
});
if (avatar) {
  fileList = [
    {
      name: "头像",
      url: avatar,
    },
  ];
}
corpMember.value = member;
let selectTeamId = ref(member.teamId);
// 获取团队列表
let teamList = ref(getBelongToTeamName(corpMember.value.userid));
// 获取角色列表
let roleNameList = ref(getRolesById(corpMember.value.roleIds));
function getBelongToTeamName(userId) {
  let belongTeams = teams.filter((item) => {
    return item.memberList.indexOf(userId) !== -1;
  });
  return belongTeams;
}
function getRolesById(list) {
  if (Array.isArray(list) && list.length > 0) {
    let roleList = roles.filter((item) => {
      return list.indexOf(item._id) !== -1;
    });
    return roleList;
  }
}
function getRolesByName(list) {
  let roleList = roles.filter((item) => {
    return list.indexOf(item.roleName) !== -1;
  });
  return roleList;
}

function changeMainTeam(teamId) {
  selectTeamId.value = teamId;
}

function addRole() {
  addRoledialogVisible.value = true;
  if (Array.isArray(roleNameList.value) && roleNameList.value.length > 0) {
    let roleList = roleNameList.value.map((item) => item.roleName);
    checkRolesList.value = roleList;
  }
}

// 删除角色
function tagClose(tag) {
  roleNameList.value = roleNameList.value.filter((item) => item._id !== tag._id);
}
//新增角色dialog
function roleDialogCancel() {
  addRoledialogVisible.value = false;
}
function roleDialogSubmit() {
  roleNameList.value = getRolesByName(checkRolesList.value);
  addRoledialogVisible.value = false;
}
function onHandFileChange(e) {
  fileList = e;
}
function cancel() {}
// 更新成员信息
async function save() {
  const { anotherName, mobile = "", memberTroduce = "" } = ruleForm;
  if (typeof anotherName !== "string" || anotherName.trim() === "") {
    ElMessage.info("请输入对外名称");
    return;
  }
  let roleIds = roleNameList.value.map((item) => item._id);
  let params = {
    anotherName,
    mobile,
    memberTroduce,
    teamId: selectTeamId.value,
    roleIds,
  };
  if (fileList.length > 0) {
    let item = fileList[0];
    if (item.raw) {
      let res = await updateFile(item.name, item.raw, "head");
      params["avatar"] = res.download_url;
    }
  } else {
    params["avatar"] = "";
  }
  let { success } = await updateCorpMember(member._id, params);
  if (success) {
    ElMessage.success("更新成功");
    let newDetial = Object.assign({}, detial, params);
    localStorage.setItem("MEMBERDETIAL", JSON.stringify(newDetial));
  }
}
function addTeam() {
  router.push({ name: "TEAM" });
}
</script>
<style lang="scss" scoped>
.manage-detial {
  padding: 15px;

  .head-image {
    margin-top: 10px;
    margin-left: 10px;
    width: 100px;
    height: 100px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    color: #006eff;
    border: 1px solid #006eff;
    text-align: center;
    border-radius: 8px;
    cursor: pointer;
    box-sizing: border-box;
  }

  .team {
    display: flex;
    align-items: center;

    .name {
      margin-right: 20px;
    }
  }

  .select-image {
    width: 16px;
    height: 16px;
    margin-right: 10px;
    cursor: pointer;
  }

  .teamName {
    cursor: pointer;
  }

  .corpRolesClass {
    display: flex;

    .role-tag {
      margin-right: 20px;
    }
  }

  .role_view {
    padding: 30px;
  }

  .addTeam {
    display: flex;
    align-items: center;

    .title {
      color: #006eff;
      padding-left: 2px;
    }
  }
}
</style>
