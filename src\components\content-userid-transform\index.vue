<template>
  <div>
    <span v-for="item in contentArr">
      <ww-user v-if="useridIndex.some((i) => i === item)" :openid="item" />
      <span v-else>{{ item }}</span>
    </span>
  </div>
</template>
<script setup>
import WwUser from "@/components/ww-user/index";
import { computed } from "vue";
const props = defineProps({
  content: { type: String, default: "" },
});
const contentArr = computed(() => {
  return props.content ? props.content.split("&&&") : [];
});
const useridIndex = computed(() => {
  if (!props.content) return [];
  let regex = /&&([^&]+)&&/g;
  let matches = [];
  let match;
  while ((match = regex.exec(props.content)) !== null) {
    matches.push(match[1]);
  }
  return matches;
});
</script>
  
  
  