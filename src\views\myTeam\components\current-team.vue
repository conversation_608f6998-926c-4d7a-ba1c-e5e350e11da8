<template>
  <div flex-grow bg-fff common-shadow rounded-8>
    <div flex items-center justify-between px-15 class="pt-15px">
      <div font-16 font-semibold active-title-bar>当前团队</div>
      <!-- <el-popover placement="bottom" trigger="hover">
        <template #reference>
          <el-text type="primary" pointer>
            <el-icon>
              <Switch />
            </el-icon>
            切换团队
          </el-text>
        </template>
        <div v-for="item in teamList" class="team" @click="selectTeam(item)">
          {{ item.name }}
        </div>
      </el-popover> -->
    </div>
    <div flex items-center px-15 pt-10 font-14>
      <div class="min-w-80px text-between">团队名称：</div>
      <div>{{ props.currentTeam.name }}</div>
    </div>
    <div flex items-center px-15 pt-10 font-14>
      <div class="min-w-80px text-between">团队人数：</div>
      <div>{{ memberCount }}人</div>
    </div>
    <div flex items-center px-15 pt-10 font-14>
      <div class="min-w-80px text-between">团队负责人：</div>
      <div class="flex">
        <div v-for="(item, index) in props.currentTeam.memberLeaderList" :key="item._id" class="flex">
          <span v-if="index != 0">、</span>
          <ww-user :openid="item"></ww-user>
        </div>
      </div>
    </div>
    <div flex items-center px-15 pt-10 font-14 pb-10>
      <div class="min-w-80px text-between">团员：</div>
      <div class="flex">
        <div v-for="(item, index) in ordinaryMember" :key="item._id" class="flex">
          <span v-if="index != 0">、</span>
          <ww-user :openid="item"></ww-user>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, inject } from "vue";
import WwUser from "@/components/ww-user/index.vue";
const props = defineProps({
  currentTeam: {
    type: Object,
    default: {}
  }
})
const memberCount = computed(() => {
  const memberCounts =
    props.currentTeam && Array.isArray(props.currentTeam.memberList)
      ? props.currentTeam.memberList.length
      : 0;
  return memberCounts;
});
const ordinaryMember = computed(() => {
  if (!props.currentTeam) return [];
  let { memberList = [], memberLeaderList = [] } = props.currentTeam;
  return memberList.filter((item) => {
    return memberLeaderList.findIndex((i) => i === item) === -1;
  });
});
</script>
<style scoped>
.text-between {
  text-align: justify;
  text-align-last: justify;
  /*兼容ie*/
  text-justify: distribute-all-lines;
}

.team {
  width: 100%;
  text-align: center;
  height: 30px;
  line-height: 30px;
  cursor: pointer;
}

.team:hover {
  color: #006eff;
}
</style>
