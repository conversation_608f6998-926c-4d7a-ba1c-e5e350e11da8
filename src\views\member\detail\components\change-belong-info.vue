<template>
  <el-dialog :model-value="visible" :title="title" :width="500" @close="close">
    <el-form class="p-10px">
      <el-form-item class="is-required mb-0" :label="lable">
        <div class="flex-grow">
          <ww-user-select :list="memberList" :placeholder="placeholderTitle" :value="userId" @change="change($event)" />
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div text-center>
        <el-button type="primary" plain class="w-100px" @click="close()">取消</el-button>
        <el-button :loading="loading" class="w-100px" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
  <script setup>
import { computed, ref, watch } from "vue";
import WwUserSelect from "@/components/ww-user-select";
import { storeToRefs } from "pinia";
import { staffStore } from "@/store/staff";
import { updateCustomerIntroducer, updateCustomerCounselor } from "@/api/member";
const { staffList } = storeToRefs(staffStore());
const emits = defineEmits(["close", "change"]);
const props = defineProps({
  customer: { type: Object, default: () => ({}) },
  title: { type: String, default: "" },
  visible: { type: Boolean, default: false },
  type: { type: String, default: "" },
});
const loading = ref(false);
const userId = ref("");
const peopleList = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("onlineCustomerService")).map((i) => i.userid);
});
const counselorList = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("consultant")).map((i) => i.userid);
});

const memberList = computed(() => {
  return props.type === "counselor" ? counselorList.value : peopleList.value;
});

const lable = computed(() => {
  return props.type === "counselor" ? "所属咨询" : "所属开发";
});

const placeholderTitle = computed(() => {
  return props.type === "counselor" ? "请选择新咨询" : "请选择新开发";
});
async function change(item) {
  userId.value = item;
}

async function confirm() {
  loading.value = true;
  if (props.type === "counselor") {
    await updateCustomerCounselor({
      customerId: props.customer._id,
      counselorRecord: props.customer.counselorRecord,
      creator: localStorage.getItem("userId"),
      counselor: userId.value,
    });
  } else {
    await updateCustomerIntroducer({
      customerId: props.customer._id,
      introducerRecord: props.customer.introducerRecord,
      creator: localStorage.getItem("userId"),
      introducer: userId.value,
    });
  }
  loading.value = false;
  emits("change");
}

function close() {
  emits("close");
}
watch(
  () => props.visible,
  (n) => {
    if (n) {
      userId.value = "";
    }
  }
);
</script>