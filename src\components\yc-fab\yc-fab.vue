<template>
  <div class="yc-fabs fixed flex items-center justify-center rounded-full shadow-xl" :style="styles" @touchstart="handleTouchStart" @touchmove="handleTouchMove">
    <img :style="iconStyle" :src="props.icon" v-if="props.icon" />
    <div v-else-if="props.text" class="flex items-center justify-center text-16px text-white">{{ props.text }}</div>
  </div>
</template>


<script setup>
import { ref, computed } from "vue";

const props = defineProps({
  icon: { type: String, default: "" },
  iconSize: { type: Number, default: 30 },
  text: { default: "" },
  size: { type: Number, default: 55 },
  zIndex: { type: Number, default: 11 },
  right: { type: [Number, String], default: "15" },
  bottom: { type: [Number, String], default: "30" },
});

let startX = 0;
let startY = 0;

// 将 right 和 bottom 的值转换为像素
const x = ref(parseInt(props.right));
const y = ref(parseInt(props.bottom));

const handleTouchStart = (event) => {
  startX = event.touches[0].clientX;
  startY = event.touches[0].clientY;
};

const handleTouchMove = (event) => {
  event.preventDefault();

  const moveX = event.touches[0].clientX;
  const moveY = event.touches[0].clientY;

  // 获取屏幕的大小
  // const { windowWidth, windowHeight } = uni.getSystemInfoSync();

  // 计算新的 x 和 y 的值
  const newX = x.value - moveX + startX;
  const newY = y.value - moveY + startY;

  // 确保 x 和 y 的值在边界内
  x.value = Math.max(0, Math.min(newX, window.innerWidth - props.size));
  y.value = Math.max(0, Math.min(newY, window.innerHeight - props.size));

  startX = moveX;
  startY = moveY;
};

const styles = computed(() => ({
  width: props.size + "px",
  height: props.size + "px",
  right: x.value + "px",
  bottom: y.value + "px",
  zIndex: props.zIndex,
}));

const iconStyle = computed(() => (props.iconSize > 0 ? `width: ${props.iconSize}px;height: ${props.iconSize}px;` : ""));
</script>

<style lang="scss" scoped>
.yc-fabs {
  background-color: #007aff;
}
</style>