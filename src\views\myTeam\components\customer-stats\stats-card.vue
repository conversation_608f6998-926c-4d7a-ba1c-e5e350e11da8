<template>
  <div class="pt-15px" border-bottom>
    <el-row>
      <el-col :lg="8" :span="8" class="relative pointer" @click="selectCard('increaseCount')">
        <div rounded-8 class="count">
          <div>新增客户总数</div>
          <div font-26 font-semibold class="count__number">
            {{ increaseCount }}
            <span font-14>人</span>
          </div>
          <img class="count__icon" src="@/assets/icons/icon-trend-white.svg" />
        </div>
        <div class="triangle_box" v-if="cardType === 'increaseCount'">
          <span class="triangle"></span>
          <span class="triangle-shadow"></span>
        </div>
      </el-col>
      <el-col class="relative pointer" :lg="8" :span="8" @click="selectCard('visitCount')">
        <div rounded-8 class="count">
          <div>累计报到人次</div>
          <div font-26 font-semibold class="count__number">
            {{ visitCount }}
            <span font-14>次</span>
          </div>
          <img class="count__icon" src="@/assets/icons/icon-visit.svg" />
        </div>
        <div class="triangle_box" v-if="cardType === 'visitCount'">
          <span class="triangle"></span>
          <span class="triangle-shadow"></span>
        </div>
      </el-col>
      <el-col :lg="8" :span="8" class="relative pointer" @click="selectCard('peopleCount')">
        <div rounded-8 class="count">
          <div>累计服务人次</div>
          <div font-26 font-semibold class="count__number">
            {{ peopleCount }}
            <span font-14>次</span>
          </div>
          <img class="count__icon" src="@/assets/icons/icon-heart.svg" />
        </div>
        <div class="triangle_box" v-if="cardType === 'peopleCount'">
          <span class="triangle"></span>
          <span class="triangle-shadow"></span>
        </div>
      </el-col>
      <!-- <el-col class="hidden-md-and-down" :md="6" :span="12">
        <div rounded-8 class="count">
          <div>客户满意度</div>
          <div font-26 font-semibold class="count__number">
            <five-star color="#fff" :score="4">
              <template #append>
                <span class="count__rate">{{ satisfactionRate }}</span>
              </template>
            </five-star>
          </div>
          <img class="count__icon" src="@/assets/icons/icon-smile.svg" />
        </div>
      </el-col> -->
    </el-row>
  </div>
</template>
<script setup>
import { ref, inject, watch } from "vue";
import FiveStar from "@/components/five-star";
import { dates } from "./useDateTag";
import { getServiceAndmemberTotalByTeamId } from "@/api/member.js";

const props = defineProps({
  currentTeam: {
    type: Object,
    default: {},
  },
  cardType: {
    type: String,
    default: "increaseCount",
  },
});
const emit = defineEmits(["onSelectCard"]);
const visitCount = ref(0); // 累计报到人次
const satisfactionRate = ref(4.8); // 客户满意度
const increaseCount = ref(0); // 新增客户总数
const peopleCount = ref(0); // 累计服务次数
const selectCard = (type) => {
  emit("onSelectCard", type);
};
watch(
  () => props.currentTeam,
  () => {
    getData();
  },
  { immediate: true }
);
watch(dates, () => {
  getData();
});
async function getData() {
  let { data, success } = await getServiceAndmemberTotalByTeamId(dates.value);
  if (success) {
    const { memberCount, serviceCount, visitCount: visit_count } = data.data;
    increaseCount.value = memberCount;
    peopleCount.value = serviceCount;
    visitCount.value = visit_count;
  }
}
</script>
<style lang="scss" scoped>
[font-26] {
  font-size: 26px;
}

.count {
  position: relative;
  padding: 10px 15px 12px;
  min-height: 60px;
  line-height: 30px;
  margin: 0 20px 15px;
  font-size: 16px;
  color: #fff;
  background-image: linear-gradient(180deg, #418ff5 0%, #6cc0e8 100%);

  @at-root &__number {
    padding-top: 10px;
    position: relative;
    z-index: 2;
    height: 45px;
  }

  @at-root &__star {
    display: inline-block;
    width: 18px;
    height: 18px;
    margin-right: 5px;
  }

  @at-root &__rate {
    font-size: 16px;
  }

  @at-root &__icon {
    position: absolute;
    right: 20px;
    top: calc(50% - 20px);
    height: 40px;
    width: 40px;
  }
}

.triangle_box {
  margin-top: -25px;
  margin-bottom: 35px;
}
.triangle {
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-bottom: 20px solid white;
  position: absolute;
  bottom: -15px;
  left: 30%;
  margin-left: -10px;
  z-index: 2;
}

.triangle-shadow {
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-bottom: 20px solid rgba(0, 0, 0, 0.1);
  position: absolute;
  bottom: -14px;
  left: 30%;
  margin-left: -10px;
  z-index: 1;
}
</style>
