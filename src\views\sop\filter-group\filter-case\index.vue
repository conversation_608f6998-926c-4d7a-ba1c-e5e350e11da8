<template>
  <div v-if="config" class="flex items-center mt-10px">
    <el-select :model-value="theme" class="w-140px flex-shrink-0" placeholder="请选择">
      <el-option :label="config.name" :value="theme" />
    </el-select>
    <div class="flex-shrink-0 flex-shrink-0 mx-5px">：</div>
    <template v-if="config.type === 'select'">
      <el-select :model-value="condition" class="w-140px flex-shrink-0" placeholder="请选择"
        @update:model-value="changeCondition($event)">
        <el-option v-for="item in sub" :key="item.value" :label="item.name" :value="item.value" />
      </el-select>
    </template>
    <number-input v-if="conditionType === 'age'" v-model="data" :max="120" type="range" unit="岁" />
    <tag-select v-else-if="conditionType === 'tag'" v-model="data" :condition="condition" />
    <select-days v-else-if="conditionType === 'selectBeforeDay'" v-model:days="data" value-type="before" />
    <select-days v-else-if="conditionType === 'selectAfterDay'" v-model:days="data" :types="['after','sameDay']"
      value-type="after" />
    <project-select v-else-if="conditionType === 'intentedProject'" v-model="data" />
    <customer-source v-else-if="conditionType === 'customerSource'" v-model="data" />
    <remote-disease v-else-if="conditionType === 'multiSelectDiagnosis'" v-model="data" />
    <mult-input v-else-if="conditionType === 'multiSelectInput'" v-model="data" />
    <health-add v-else-if="conditionType === 'healthIndexAbnormal'" v-model="data" />
    <el-button text type="danger" size="small" class="flex-shrink-0" style="margin-left: 5px;" @click="remove()">
      <el-icon class="mr-3px">
        <Delete />
      </el-icon>
    </el-button>
  </div>
  <health-index-list v-if="conditionType === 'healthIndexAbnormal'" v-model="data" />
</template>
<script setup>
import { computed, defineAsyncComponent, onMounted, watch } from 'vue'
import { useVModel } from '@vueuse/core';
import { ElMessage } from 'element-plus';

const customerSource = defineAsyncComponent(() => import('./customer-source.vue'));
const healthAdd = defineAsyncComponent(() => import('./health-index-list/health-add.vue'));
const healthIndexList = defineAsyncComponent(() => import('./health-index-list/index-list.vue'));
const multInput = defineAsyncComponent(() => import('./mult-input.vue'));
const numberInput = defineAsyncComponent(() => import('./number-input.vue'));
const projectSelect = defineAsyncComponent(() => import('./project-select.vue'));
const remoteDisease = defineAsyncComponent(() => import('./remote-disease.vue'));
const selectDays = defineAsyncComponent(() => import('./select-days.vue'));
const tagSelect = defineAsyncComponent(() => import('./tag-select.vue'));

const emit = defineEmits(['update:modelValue', 'update:condition', 'update:isDayFilter', 'remove'])
const props = defineProps({
  caseId: { type: String, default: '' },
  condition: { type: String, default: '' },
  dayFilters: { type: Array, default: () => [] },
  isDayFilter: { type: Boolean, default: false },
  conditions: {
    type: Array,
    default: () => []
  },
  modelValue: {
    type: Object,
    default: () => ({})
  },
  name: { type: String, default: '' },
  theme: { type: String, default: '' },
})

const data = useVModel(props, 'modelValue', emit);
const condition = useVModel(props, 'condition', emit);
const isDayFilter = useVModel(props, 'isDayFilter', emit);
const config = computed(() => {
  const arr = Array.isArray(props.conditions) ? props.conditions : []
  return arr.find(i => i.value === props.theme)
})

onMounted(() => {
  if (props.theme === 'age') condition.value = 'age';
})

const sub = computed(() => {
  if (config.value && config.value.type === 'select') return Array.isArray(config.value.sub) ? config.value.sub : []
  return config.value && config.value.sub ? config.value.sub : ''
})

const conditionType = computed(() => {
  if (props.theme === 'age') return 'age';
  if (config.value && config.value.type === 'select') {
    const opt = sub.value.find(item => item.value === condition.value)
    return opt && opt.type ? opt.type : ''
  }
  return ''
})

function changeCondition(val) {
  const opt = sub.value.find(i => i.value === val);
  const isDayFilter = opt ? ['selectAfterDay', 'selectBeforeDay'].includes(opt.type) : false;
  const filters = props.dayFilters.filter(i => i.key !== props.caseId)
  if (isDayFilter && filters.length > 0) {
    ElMessage.info('目标客户筛选条件中，具有时间属性的筛选条件只能选择1个！')
  } else {
    condition.value = val
  }
}

function remove() {
  emit('remove')
}

watch(conditionType, (n, o) => {
  if (n && o) {
    data.value = {}
  }
  isDayFilter.value = ['selectBeforeDay', 'selectAfterDay'].includes(n)
}, { immediate: true })
</script>
<style lang="scss" scoped></style>
