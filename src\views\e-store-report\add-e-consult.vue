<template>
  <el-drawer :close-on-click-modal="false" :model-value="visible" :title="drawerTitle" size="60%" style="--el-drawer-padding-primary: 0" @close="close()">
    <my-layout class="bg-[#f2f3f4]">
      <layout-main>
        <div class="mx-15px my-10px p-10px bg-white rounded">
          <el-form label-position="top">
            <el-form-item class="is-required" label="信息来源">
              <remote-select-customer-source v-model="form.source" placeholder="请选择客户来源" :disabled="isViewMode" />
            </el-form-item>
            <el-form-item class="is-required" label="意向项目">
              <project-intent-select v-model="form.projectIds" @change="handleProjectChange" :disabled="isViewMode" />
            </el-form-item>
            <el-form-item label="报备说明">
              <el-input type="textarea" v-model="form.reportDesc" placeholder="请输入报备说明" maxlength="500" :autosize="{ minRows: 6, maxRows: 10 }" resize="none" show-word-limit :disabled="isViewMode"></el-input>
            </el-form-item>
          </el-form>
          <el-form>
            <el-row>
              <!-- <el-col :span="24" :md="12">
                <el-form-item label-position="left" label="咨询时间:">
                  <div class="flex-grow flex items-center flex-wrap">
                    <el-date-picker class="w-1/2 mr-10px mt-5px" v-model="date" type="date" placeholder="请选择咨询日期" value-format="YYYY-MM-DD" :disabled="true" />
                    <el-time-picker class="w-1/2 mt-5px" v-model="time" format="HH:mm" placeholder="请选择咨询时间" value-format="HH:mm" :disabled="true" />
                  </div>
                </el-form-item>
              </el-col> -->
              <el-col :span="24" :md="12">
                <el-form-item label-position="left" label="登记人:">
                  <ww-user v-if="register" :openid="register"></ww-user>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </layout-main>
      <layout-item>
        <div text-center class="py-10px mt-1px bg-white" common-shadow--r>
          <el-button plain class="w-100px" @click="close()">{{ isViewMode ? "关闭" : "取消" }}</el-button>
          <el-button v-if="!isViewMode" class="w-100px" type="primary" :loading="loading" @click="confirm()">保存</el-button>
        </div>
      </layout-item>
    </my-layout>
  </el-drawer>
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { ElMessage } from "element-plus";
import dayjs from "dayjs";
import { addEConsuleRecord, updateEConsuleRecord } from "@/api/consult";
import { updateMember } from "@/api/member";
import { ReportRemarkTemp } from "@/baseData";
import { memberStore } from "@/store/member";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import ProjectIntentSelect from "@/components/benefit-management/project-intent-select.vue";
import RemoteSelectCustomerSource from "@/components/benefit-management/remote-select-customer-source.vue";

const emits = defineEmits(["close", "change", "success"]);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  record: {
    type: Object,
    default: () => ({}),
  },
  customer: {
    type: Object,
    default: () => ({}),
  },
  mode: {
    type: String,
    default: "edit",
  },
});

// 计算属性判断是否为查看模式
const isViewMode = computed(() => props.mode === "view");
// 计算抽屉标题
const drawerTitle = computed(() => (isViewMode.value ? "报备详情查看" : "报备详情"));

const { memberInfo } = storeToRefs(memberStore());
const form = ref({});
const register = ref("");
const selectedProjects = ref([]);
const loading = ref(false);
const date = ref();
const time = ref();

function handleProjectChange(projects) {
  selectedProjects.value = projects;
}

function close() {
  emits("close");
}

async function confirm() {
  if (!form.value.source) {
    ElMessage.info("请选择信息来源");
    return;
  }
  if (!form.value.projectIds || form.value.projectIds.length === 0) {
    ElMessage.info("请选择项目");
    return;
  }
  if (!date.value || !time.value) {
    ElMessage.info("请选择咨询时间");
    return;
  }
  if (dayjs().isBefore(dayjs(form.value.date))) {
    ElMessage.info("日期不能大于今天");
    return;
  }
  props.record._id ? update() : add();
}

async function add() {
  const introducerRecord = props.customer.introducerRecord;
  const introducer = introducerRecord ? introducerRecord[introducerRecord.length - 1]?.introducer : localStorage.getItem("userId");
  const params = {
    ...form.value,
    corpId: memberInfo.value.corpId,
    userId: introducer,
    registrantUserId: memberInfo.value.userid,
    customerId: props.customer._id,
    reportDesc: form.value.reportDesc === ReportRemarkTemp ? "" : form.value.reportDesc,
  };
  loading.value = true;
  const { success, message } = await addEConsuleRecord(params);

  if (success) {
    // 新增记录成功后，更新客户档案
    try {
      const customerUpdateParams = {
        projectIds: form.value.projectIds,
        infoSource: form.value.source,
        reportDesc: form.value.reportDesc === ReportRemarkTemp ? "" : form.value.reportDesc,
      };
      await updateMember(props.customer._id, customerUpdateParams);
    } catch (error) {
      console.error("更新客户档案失败:", error);
      // 即使更新客户档案失败，也不影响主流程
    }

    ElMessage.success(message);
    emits("change");
    close();
  } else {
    ElMessage.error(message);
  }
  loading.value = false;
}
async function update() {
  const params = {
    _id: props.record._id,
    customerId: props.record.customerId,
    corpId: memberInfo.value.corpId,
    reportDesc: form.value.reportDesc === ReportRemarkTemp ? "" : form.value.reportDesc,
    source: form.value.source,
    date: form.value.date,
    projectIds: form.value.projectIds,
  };
  loading.value = true;
  const { success, message } = await updateEConsuleRecord(params);
  loading.value = false;
  if (success) {
    ElMessage.success(message);
    emits("change");
    close();
  } else {
    ElMessage.error(message);
  }
}

watch([date, time], ([d, t]) => {
  if (d && t) {
    form.value.date = `${d} ${t}`;
  }
});

watch(
  () => props.visible,
  (val) => {
    if (val) {
      if (props.record._id) {
        form.value = {
          source: props.record.source,
          date: props.record.date,
          reportDesc: props.record.reportDesc || ReportRemarkTemp,
          projectIds: props.record.projectIds,
        };

        register.value = props.record.registrantUserId || props.record.userId;

        if (props.record.date && dayjs(props.record.date).isValid()) {
          date.value = dayjs(props.record.date).format("YYYY-MM-DD");
          time.value = dayjs(props.record.date).format("HH:mm");
        }
      } else {
        register.value = memberInfo.value.userid;
        selectedProjects.value = [];
        form.value = {
          source: "",
          projectIds: [],
          date: dayjs().format("YYYY-MM-DD"),
          reportDesc: ReportRemarkTemp,
        };
        date.value = dayjs().format("YYYY-MM-DD");
        time.value = dayjs().format("HH:mm");
      }
    }
  }
);
</script>