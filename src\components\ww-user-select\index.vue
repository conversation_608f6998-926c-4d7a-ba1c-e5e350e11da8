<template>
  <el-select :model-value="value" class="el-select--hiddenValue" style="width: 100%" placeholder="" :disabled="disabled" filterable clearable @update:model-value="change($event)">
    <template #prefix>
      <div class="h-30px" color-666>
        <ww-user v-if="value" :openid="value"></ww-user>
        <span v-if="placeholder && !value" style="color: #c0c4cc">{{ placeholder }}</span>
      </div>
    </template>
    <el-option v-for="userId in list" :key="userId" :value="userId">
      <ww-user :openid="userId"></ww-user>
      <!-- <span>{{ userId }}</span> -->
    </el-option>
  </el-select>
</template>
<script setup>
import WwUser from "@/components/ww-user/index.vue";

defineProps({
  list: { type: Array, default: () => [] },
  placeholder: { type: String, default: "" },
  value: { type: [String, Number], default: "" },
});
const emits = defineEmits(["change"]);
function change(value) {
  emits("change", value);
}
</script>
<style lang="scss">
.el-select.el-select--hiddenValue .el-input__inner {
  display: none;
}

.el-select.el-select--hiddenValue .el-input__prefix {
  flex-grow: 1;
}
</style>
