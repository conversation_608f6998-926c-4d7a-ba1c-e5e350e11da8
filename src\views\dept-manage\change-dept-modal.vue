<template>
  <el-dialog :model-value="visible" :width="width" title="修改科室" @close="close()">
    <div class="flex h-50vh">
      <el-scrollbar class="flex-shrink-0 w-240px border-r border-gray-200">
        <classify-list v-model="deptIds" enableSelect :data="cateList" />
      </el-scrollbar>
      <el-scrollbar class="flex-grow">
        <div v-for="i in depts" :key="i.value" class="flex items-center py-10px px-15px border-b border-gray-200">
          <div class="w-0 flex-grow truncate">{{ i.label }}</div>
          <div class="text-gray-500 hover:text-red-500 cursor-pointer" @click="remove(i.value)"> <el-icon
              class="flex-shrink-0" :size="14">
              <CloseBold />
            </el-icon></div>
        </div>
      </el-scrollbar>
    </div>
    <template #footer>
      <div text-center>
        <el-button plain class="w-100px" type="primary" :icon="Plus" @click="close()">取消</el-button>
        <el-button class="w-100px" :loading="loading" type="primary" :icon="Plus" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { updateStaffDept } from "@/api/dept-manage";
import classifyList from '@/components/classify-list/classify-list-side.vue';

const emits = defineEmits(["close", "change"]);
const props = defineProps({
  cateList: { type: Array, default: () => ([]) },
  dept: { type: Object, default: () => ({}) },
  staff: { type: Object, default: () => ({}) },
  visible: { type: Boolean, default: false },
  width: { type: [String, Number], default: "500px" },
});
const deptIds = ref([]);
const depts = computed(() => props.cateList.filter(item => deptIds.value.includes(item.value)))
const loading = ref(false);
function close() {
  emits("close");
}

async function confirm() {
  if (loading.value) return;
  if (deptIds.value.length === 0) {
    ElMessage.error('请选择科室');
  } else {
    loading.value = true;
    const { success, message } = await updateStaffDept({ deptIds: deptIds.value, corpId: localStorage.getItem('corpId'), userId: props.staff.userId });
    if (success) {
      ElMessage.success('修改成功');
      emits('change')
      close()
    } else {
      ElMessage.error(message);
    }
    loading.value = false;
  }
}

function remove(value) {
  deptIds.value = deptIds.value.filter(item => item !== value)
}


watch(() => props.visible, (n) => {
  if (n) {
    deptIds.value = props.staff && Array.isArray(props.staff.deptIds) ? [...props.staff.deptIds] : [];
  }
}
);
</script>