<template>
  <el-table-column v-bind="$attrs">
    <template #default="{ row }">
      <div class="flex items-center relative h-44px w-full">
        <div class="text-14px leading-22px line-clamp-2" :class="classnames">
          <slot :row="row">
            <span>{{ row[$attrs.prop] }}</span>
          </slot>
        </div>
      </div>
    </template>
  </el-table-column>
</template>
<script setup>
const props = defineProps({
  classnames: {
    type: [String, Array, Object],
    default: "",
  },
});
</script>
<style lang="scss" scoped></style>
