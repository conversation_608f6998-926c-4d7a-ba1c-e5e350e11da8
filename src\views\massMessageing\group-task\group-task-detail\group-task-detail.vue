<template>

  <div bg-fff common-shadow class="p-15px rounded-8px mb-10px">
    <div class="flex items-center">
      <div class="mr-10px text-16px font-semibold">
        {{sendSource[groupmsg.createSounrce]||''}}【{{ sendType[groupmsg.sendType] }}】
      </div>
      <div class="text-yellow-500 border border-yellow-500 rounded-4px px-10px py-5px text-14px">
        {{ sendStatus }}
      </div>
    </div>
    <el-form label-suffix="：" :label-width="90" label-position="left">
      <el-row>
        <el-col :md="6" :sm="12" :span="24">
          <el-form-item label="发送时间">
            <span>{{ sendTime }}</span>
          </el-form-item>
        </el-col>
        <el-col :md="6" :sm="12" :span="24">
          <el-form-item label="创建人">
            <span v-if="groupmsg.creator"><ww-user :openid="groupmsg.creator"></ww-user></span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="发送内容">
            <div class="flex-grow pr-10px">
              <div class="text-14px leading-24px whitespace-pre-line">{{ groupmsg.content }}</div>
              <div v-for="item in groupmsg.attachments"
                class="my-10px py-10px flex items-center pt-10px border-t border-gray-200">
                <div class="w-0 flex-grow flex">
                  <div class="w-0 flex-grow text-14px flex" align-center>
                    <div class="leading-28px truncate" v-if="item.msgtype">[{{ templateType[item.msgtype] }}]</div>
                    <div class="text-gray-500 leading-20px truncate pl-10px">{{ item.title }}</div>
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
  <send-table :isNoSend="isNoSend" :sendSuccessList="sendSuccessList" :sendFailList="sendFailList"
    :customers="groupmsg.customers" :groupmsg="groupmsg" />
</template>
<script setup>
import MyLayout, { LayoutMain } from "@/components/layout";
import sendTable from "./send-status-table.vue";
import { useRoute } from "vue-router";
import WwUser from "@/components/ww-user/index.vue";
import { ref, computed } from "vue";
import dayjs from "dayjs";
import { getGroupmesgById, getCusomterGroupmsgSendResult } from "../../api/groupmsg.js";
const route = useRoute();
const id = route.params.id;
const loading = ref(false);
const templateType = {
  link: "网页",
  image: "图片",
  video: "视频",
};
const sendType = {
  MESSAGE: "群发消息",
  FRIEND: "群发朋友圈",
};
const sendSource = {
  CORP: '机构',
  MINE: '我的',
  TEAM: '团队',
}
const groupmsg = ref({});
const sendResultList = ref([]);
const sendSuccessList = computed(() => sendResultList.value.filter((item) => item.status === 1));
const sendFailList = computed(() => sendResultList.value.filter((item) => item.status == 2 || item.status == 3));
const isNoSend = computed(() => {
  return sendResultList.value.filter((item) => item.status === 0).length > 0;
});
const sendStatus = computed(() => {
  return groupmsg.value.executeStatus === "end" ? "已结束" : isNoSend.value ? "未发送" : "已发送";
});
const sendTime = computed(() => {
  let time = "";
  sendSuccessList.value.forEach((item) => {
    if (item.send_time) {
      time = item.send_time;
      return;
    }
  });
  return time ? dayjs.unix(time).format("YYYY-MM-DD HH:mm") : "";
});
getMesgDetial();
async function getMesgDetial() {
  loading.value = true;
  let res = await getGroupmesgById(id);
  groupmsg.value = res.data;
  const { executor, msgid, customers } = res.data;
  const { data } = await getCusomterGroupmsgSendResult(executor, msgid, customers);
  loading.value = false;
  sendResultList.value = data || [];
}
</script>
<style scoped>
:deep(.el-form-item) {
  margin-bottom: 0;
}
</style>
