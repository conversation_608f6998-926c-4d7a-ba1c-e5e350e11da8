<template>
  <my-layout>
    <layout-item>
      <div class="flex flex-wrap bg-white p-15px mb-10px rounded">
        <el-badge v-for="tab in tabs" :key="tab.key" :value="count[tab.countKey]" :hidden="!(count[tab.countKey] > 0)"
          :max="99" class="mr-12px">
          <div class="py-6px px-16px text-sm rounded-full cursor-pointer bg-[#f5f7fb] border border-[#ddd]"
            :class="currentTab && currentTab.key === tab.key ? 'bg-[#1261ff] border-[#1261ff] text-white font-semibold' : ''"
            @click="changeTab(tab)">
            {{ tab.label }}
          </div>
        </el-badge>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <component v-if="currentTab && currentTab.com" :is="currentTab.com" :init-value="initValue[currentTab.key]" />
    </layout-main>
    <layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="currentPage" @handle-size-change="onSizeChange"
        @handle-current-change="onCurrentChange" />
    </layout-item>
    <detail-modal :visible="visible" :data="currentRow" @close="visible = false" @updated="updated()"></detail-modal>
  </my-layout>
</template>
<script setup>
import { defineAsyncComponent, ref, onMounted, onBeforeMount, nextTick } from "vue";
import { getNumber } from "@/api/todo";
import useTeamId from "./components/useTeamId";
import { teamStore } from "@/store/team";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";

const myTodoTable = defineAsyncComponent(() => import("./components/my-todo-table.vue"));
const myTodoRecordTable = defineAsyncComponent(() => import("./components/my-todo-record-table.vue"));
const teamTodoTable = defineAsyncComponent(() => import("./components/team-todo-table.vue"));
const myServiceRecord = defineAsyncComponent(() => import("./components/my-service-record/index.vue"));
const teamServiceRecordTable = defineAsyncComponent(() => import("./components/team-service-record-table.vue"));

const tabs = [
  { label: "我的待办", key: "myTodo", countKey: "untreatedCount", com: myTodoTable },
  { label: "我的待办处理记录", key: "myTodoRecord", countKey: "", com: myTodoRecordTable },
  { label: "团队未认领待办", key: "teamTodo", countKey: "commonEventsCount", com: teamTodoTable },
  { label: "我的服务记录", key: "myServiceRecord", countKey: "", com: myServiceRecord },
  { label: "团队服务记录", key: "teamServiceRecord", countKey: "", com: teamServiceRecordTable },
];
const currentTab = ref(tabs[0]);
const initValue = ref({});
const changeTab = async (tab) => {
  currentTab.value = tab;
  await nextTick();
  initValue.value[tab.key] = null;
};

useTeamId(getCountNumber);
const count = ref({});
async function getCountNumber() {
  const { data } = await getNumber({
    executorUserId: localStorage.getItem("userId"),
    corpId: localStorage.getItem("corpId"),
    teamId: teamStore().currentTeamId,
  });
  count.value = data && data.data ? data.data : {};
}

onBeforeMount(() => {
  currentTab.value = tabs.find((tab) => tab.key === history.state.taskTab) || tabs[0];
  if (["myServiceRecord", "teamServiceRecord"].includes(history.state.taskTab) && history.state.dates && typeof history.state.dates === "string") {
    const dates = history.state.dates.split(",");
    initValue.value[history.state.taskTab] = dates;
  }
});
</script>
