<template>
  <el-drawer v-loading="loading" :close-on-click-modal="false" :model-value="visible" :title="data._id ? '编辑任务' : '新增任务'" size="60%" style="--el-drawer-padding-primary: 0" @close="close">
    <my-layout>
      <layout-main>
        <el-form :label-width="140" :model="taskForm" label-suffix="：" class="p-15px">
          <el-form-item class="is-required" label="回访日期" prop="date">
            <el-date-picker class="w-full" :disabled-date="disabledDate" v-model="taskForm.planExecutionTime" type="date" placeholder="请选择回访日期" value-format="YYYY-MM-DD" />
          </el-form-item>
          <el-form-item class="is-required" label="处理人" prop="executorUserId">
            <div v-if="type === 'addBatchTask'">所属开发</div>
            <el-select v-else v-model="selectedExecutor" ref="executorSelectRef" popper-class="task-drawer-executor-select-popover" placeholder="请选择处理人" class="w-full task-drawer-executor-select" clearable @change="changeExecutor" @visible-change="visibleChange">
              <template #prefix>
                <div class="truncate h-30px leading-30px text-left" :style="{ width: executorPrefixWidth, color: 'rgb(96, 98, 102)' }">
                  <ww-user v-if="taskForm.executorUserId" :openid="taskForm.executorUserId" />
                  <span class="ml-10px">{{ executorTeamName }}</span>
                </div>
              </template>
              <el-option-group v-for="group in filterExecutorOptions" :key="group.teamId" :label="group.name">
                <el-option v-for="item in group.list" :key="item.value" :value="item.value">
                  <div class="truncate">
                    <ww-user :openid="item.userId"></ww-user>
                  </div>
                </el-option>
              </el-option-group>
            </el-select>
          </el-form-item>
          <el-form-item label="类型" class="is-required mt-20px" prop="eventType">
            <el-select v-model="taskForm.eventType" filterable style="width: 100%" placeholder="请选择类型">
              <el-option v-for="item in eventTypeList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="目的" prop="taskContent" class="mt-20px">
            <el-input v-model="taskForm.taskContent" maxlength="500" resize="none" :autosize="{ minRows: 6, maxRows: 12 }" show-word-limit type="textarea" placeholder="请输入文字提醒" />
          </el-form-item>
          <el-form-item label="跟进方式" prop="executeMethod" class="mt-20px is-required">
            <el-radio-group v-model="taskForm.executeMethod" @change="changeMethod">
              <el-radio label="todo">生成待办单（员工手动处理）</el-radio>
              <el-radio label="groupTask">生成群发单（员工批量发送）</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="taskForm.executeMethod === 'todo'" label="向联系人发送" prop="enableSend" class="mt-20px">
            <el-switch v-model="taskForm.enableSend" inline-prompt active-text="是" inactive-text="否" />
            <el-tooltip effect="light" content="可提前预设想给联系人发送的标准话术及附件" placement="top">
              <el-icon class="text-20px text-gray-500 ml-10px">
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </el-form-item>
          <el-form-item v-if="taskForm.enableSend" :class="taskForm.executeMethod === 'groupTask' ? 'is-required' : ''" label="发送内容" prop="sendContent" class="mt-20px">
            <send-content-set v-model="taskForm" class="w-full" :executeMethod="taskForm.executeMethod" />
          </el-form-item>
        </el-form>
      </layout-main>
      <layout-item>
        <div common-shadow--r class="text-center py-10px">
          <el-button class="w-100px" @click="close()">取消</el-button>
          <el-button v-if="data._id && data.creatorUserId === memberInfo.userid" type="danger" class="w-100px" @click="remove()">删除</el-button>
          <el-button class="w-100px" type="primary" @click="save()">保存</el-button>
        </div>
      </layout-item>
    </my-layout>
  </el-drawer>
  <teleport v-if="showExecutorSearchInput" to=".task-drawer-executor-select-popover .teleport-container">
    <div class="px-20px pt-20px pb-5px">
      <el-input clearable v-model="executorKeyword" placeholder="搜索..." />
    </div>
  </teleport>
</template>
<script setup>
import { computed, ref, watch, nextTick } from "vue";
import { useElementBounding } from "@vueuse/core";
import { storeToRefs } from "pinia";
import { ElMessageBox, ElMessage } from "element-plus";
import dayjs from "dayjs";
import { executeManagementPlanTodo, updateTaskTodo, removeTodo } from "@/api/todo";
import { pushCustomerTeamId } from "@/api/member";
import { ToDoEventType } from "@/baseData";
import { teamStore } from "@/store/team";
import { memberStore } from "@/store/member";
import { staffStore } from "@/store/staff";
import { getRandomStr } from "@/utils";

import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import WwUserSelect from "@/components/ww-user-select";
import sendContentSet from "@/components/send-content-set/send-content-set.vue";
const emits = defineEmits(["close", "change"]);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: "",
  },
  teamId: {
    type: String,
    default: "",
  },
  data: {
    type: Object,
    default: () => ({}),
  },
  customer: {
    type: Object,
    default: () => ({}),
  },
  selectCustomers: {
    type: Array,
    default: () => [],
  },
  memberList: { type: Array, default: () => [] },
});
const eventTypeList = Object.keys(ToDoEventType).map((key) => ({ label: ToDoEventType[key], value: key }));

const taskForm = ref({});
const loading = ref(false);
const { memberInfo } = storeToRefs(memberStore());
const { allTeams } = storeToRefs(teamStore());
const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());
const team = computed(() => allTeams.value.find((i) => i.teamId === props.teamId));

// 处理人选择相关
const selectedExecutor = ref("");
const executorSelectRef = ref(null);
const { width: executorSelectWidth } = useElementBounding(executorSelectRef);
const executorPrefixWidth = computed(() => `${executorSelectWidth.value - 40}px`);
const showExecutorSearchInput = ref(false);
const executorKeyword = ref("");

const staffMap = computed(() =>
  staffList.value.reduce((acc, cur) => {
    acc[cur.userid] = cur;
    return acc;
  }, {})
);

const executorOptions = computed(() => {
  const data = allTeams.value.reduce((acc, cur) => {
    const memberList = Array.isArray(cur.memberList) ? cur.memberList : [];
    const group = { teamId: cur.teamId, name: cur.name, list: [] };
    group.list = memberList
      .map((i) => ({
        value: `${cur.teamId}||${i}`,
        userId: i,
        teamId: cur.teamId,
        name: cur.name,
        staff: staffMap.value[i],
      }))
      .filter((i) => i.staff); // 移除角色过滤，允许所有员工
    if (group.list.length) return [...acc, group];
    return acc;
  }, []);
  return data;
});

const filterExecutorOptions = computed(() => {
  const name = executorKeyword.value.trim();
  if (name === "") return executorOptions.value;
  return executorOptions.value
    .map((i) => {
      const { list, ...item } = i;
      return { ...item, list: list.filter((j) => j.staff && typeof j.staff.anotherName === "string" && j.staff.anotherName.includes(name)) };
    })
    .filter((i) => i.list.length);
});

const executorTeamName = computed(() => {
  const team = allTeams.value.find((i) => i.teamId === taskForm.value.executorTeamId && i.teamId);
  return team ? team.name : "";
});

function change(userid) {
  taskForm.value.executorUserId = userid;
}

function changeExecutor(val) {
  const [teamId = "", userId = ""] = val.split("||");
  taskForm.value.executorUserId = userId;
  taskForm.value.executorTeamId = teamId;
}

async function visibleChange(val) {
  if (!val) return;
  await nextTick();
  const popover = document.querySelector(".task-drawer-executor-select-popover");
  const el = document.querySelector(".task-drawer-executor-select-popover .teleport-container");
  if (popover && !el) {
    const el = document.createElement("div");
    el.classList.add("teleport-container");
    popover.prepend(el);
  }
  await nextTick();
  showExecutorSearchInput.value = val;
}

function changeMethod(val) {
  if (val === "groupTask") {
    taskForm.value.enableSend = true;
  }
}

function close() {
  emits("close");
}

function disabledDate(date) {
  const startDate = dayjs().subtract(6, "days").startOf("day");
  return dayjs(date).isBefore(startDate, "day");
}

function save() {
  if (!taskForm.value.planExecutionTime || !dayjs(taskForm.value.planExecutionTime).isValid() || dayjs(taskForm.value.planExecutionTime).isBefore(dayjs().subtract(7, "day"), "day")) {
    ElMessage.info("请选择正确的回访日期");
    return;
  }

  if (!ToDoEventType[taskForm.value.eventType]) {
    ElMessage.info("请选择正确的任务类型");
    return;
  }

  if (!["todo", "groupTask"].includes(taskForm.value.executeMethod)) {
    ElMessage.info("请选择正确的执行方式");
    return;
  }
  if (taskForm.value.executeMethod === "groupTask" && !taskForm.value.sendContent) {
    ElMessage.info("请填写发送内容");
    return;
  }

  if (props.type === "editManagementPlanTask") {
    if (!taskForm.value.executorUserId) {
      ElMessage.info("请选择处理人");
      return;
    }
    emits("change", { ...taskForm.value });
    close();
  } else if (props.type === "editTask") {
    props.data._id ? updateTask() : addTask();
  } else if (props.type === "addBatchTask") {
    addBatchTasks();
  }
}

async function addBatchTasks() {
  if (loading.value) return;
  if (props.selectCustomers.length === 0) {
    return ElMessage.warning("请选择客户");
  }

  loading.value = true;
  let successCount = 0;
  let failCount = 0;

  for (const customer of props.selectCustomers) {
    // 跳过没有微信联系人的客户（群发任务情况下）
    if (taskForm.value.executeMethod === "groupTask" && !customer.externalUserId) {
      failCount++;
      continue;
    }
    const introducerRecord = customer.introducerRecord;
    const customerUserId = props.customer.externalUserId;
    // 使用选择的处理人和团队
    const userId = taskForm.value.executorUserId || memberInfo.value.userid;
    const executeTeamId = taskForm.value.executorTeamId || team.value.teamId;
    const selectedTeam = allTeams.value.find((i) => i.teamId === executeTeamId);
    const executeTeamName = selectedTeam ? selectedTeam.name : "";
    const params = {
      corpId: memberInfo.value.corpId,
      customerId: customer._id,
      customerName: customer.name,
      customerUserId,
      executeTeamId,
      executeTeamName,
      userId,
      taskList: [{ ...taskForm.value, taskId: getRandomStr() }],
    };
    try {
      const { success } = await executeManagementPlanTodo(params);
      if (success) {
        // 更新客户档案的团队授权
        await updateCustomerTeamAuth(executeTeamId);
        successCount++;
      } else {
        failCount++;
      }
    } catch (error) {
      failCount++;
    }
  }

  if (successCount > 0) {
    ElMessage.success(`成功添加 ${successCount} 个任务`);
    emits("change");
    close();
  }

  if (failCount > 0) {
    ElMessage.warning(`${failCount} 个任务添加失败`);
  }

  loading.value = false;
}

async function addTask() {
  if (loading.value) return;
  if (hasGroupTask()) {
    return ElMessage.warning("该档案还未绑定微信联系人，无法执行群发任务!");
  }
  loading.value = true;
  const params = {
    corpId: memberInfo.value.corpId,
    customerId: props.customer._id,
    customerName: props.customer.name,
    customerUserId: props.customer.externalUserId || "",
    executeTeamId: taskForm.value.executorTeamId || team.value.teamId,
    executeTeamName: taskForm.value.executorTeamId ? executorTeamName.value : team.value.name,
    userId: taskForm.value.executorUserId || memberInfo.value.userid,
    taskList: [{ ...taskForm.value, taskId: getRandomStr() }],
  };
  const { success, message } = await executeManagementPlanTodo(params);
  if (success) {
    // 更新客户档案的团队授权
    await updateCustomerTeamAuth(taskForm.value.executorTeamId || team.value.teamId);
    
    ElMessage.success(message || "操作成功");
    emits("change");
    close();
  } else {
    ElMessage.error(message);
  }
  loading.value = false;
}

async function updateTask() {
  if (loading.value) return;
  if (hasGroupTask()) {
    return ElMessage.warning("该档案还未绑定微信联系人，无法执行群发任务!");
  }

  loading.value = true;
  const { _id: id, ...task } = taskForm.value;
  const { success, message } = await updateTaskTodo({ id, task, corpId: memberInfo.value.corpId });
  if (success) {
    ElMessage.success(message || "操作成功");
    emits("change");
    close();
  } else {
    ElMessage.error(message);
  }
  loading.value = false;
}

// 更新客户档案的团队授权
async function updateCustomerTeamAuth(executorTeamId) {
  if (!props.customer._id || !executorTeamId) return;
  
  try {
    // 直接调用 pushCustomerTeamId 接口授权团队
    await pushCustomerTeamId({ 
      customerId: props.customer._id, 
      teamId: executorTeamId 
    });
    console.log(`客户档案已授权给团队: ${executorTeamId}`);
  } catch (error) {
    console.error("更新客户档案团队授权失败:", error);
  }
}

// 判断 是否有微信联系人 且存在群发任务
function hasGroupTask() {
  return taskForm.value.executeMethod === "groupTask" && !props.customer.externalUserId;
}
async function remove() {
  if (loading.value) return;
  await ElMessageBox.confirm("确定要删除吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  });
  loading.value = true;
  const { success, message } = await removeTodo({ id: props.data._id, corpId: memberInfo.value.corpId, userId: memberInfo.value.userid });
  if (success) {
    ElMessage.success(message || "操作成功");
    emits("change");
    close();
  } else {
    ElMessage.error(message);
  }
  loading.value = false;
}

watch(
  () => props.visible,
  (val) => {
    if (!val) return;
    taskForm.value = JSON.parse(JSON.stringify(props.data));
    if (taskForm.value.planExecutionTime && dayjs(taskForm.value.planExecutionTime).isValid()) {
      taskForm.value.planExecutionTime = dayjs(taskForm.value.planExecutionTime).format("YYYY-MM-DD");
    }
    // 新增任务时，默认设置executeMethod为todo
    if (!taskForm.value._id) {
      taskForm.value.executeMethod = "todo";
    }
    
    // 初始化处理人选择
    if (staffList.value.length === 0) getStaffList();
    selectedExecutor.value = taskForm.value.executorTeamId && taskForm.value.executorUserId ? `${taskForm.value.executorTeamId}||${taskForm.value.executorUserId}` : "";
    executorKeyword.value = "";
  }
);
</script>
<style scoped>
:deep(.el-textarea__inner) {
  padding-bottom: 20px;
}

:deep(.task-drawer-executor-select > .select-trigger > .el-input > .el-input__wrapper > .el-input__inner) {
  display: none;
}

:deep(.task-drawer-executor-select > .select-trigger > .el-input > .el-input__wrapper > .el-input__prefix) {
  flex-grow: 1;
}
</style>