<template>
  <my-layout>
    <layout-item>
      <div class="py-5px flex bg-white">
        <div class="flex-grow">
          <div class="search-form">
            <div class="text-gray-500 text-14px flex items-center">
              <div class="filter-label pr-5px">姓名:</div>
              <el-input
                v-model="searchForm.name"
                placeholder="请输入姓名"
                clearable
                style="width: 200px"
              />
            </div>
            <div class="text-gray-500 text-14px flex items-center">
              <div class="filter-label pr-5px">联系方式:</div>
              <el-input
                v-model="searchForm.contact"
                placeholder="请输入联系方式"
                clearable
                style="width: 200px"
              />
            </div>
            <div class="text-gray-500 text-14px flex items-center">
              <div class="filter-label pr-5px">所属部门:</div>
              <el-cascader
                v-model="searchForm.department"
                :options="departmentOptions"
                :props="{ 
                  expandTrigger: 'hover',
                  emitPath: false,
                  checkStrictly: true,
                  multiple: true
                }"
                placeholder="请选择所属部门"
                clearable
                style="width: 200px"
              />
            </div>
            <div class="text-gray-500 text-14px flex items-center">
              <div class="filter-label pr-5px">岗位:</div>
              <el-select
                v-model="searchForm.position"
                placeholder="请选择岗位"
                clearable
                filterable
                multiple
                collapse-tags
                style="width: 200px"
              >
                <el-option
                  v-for="job in jobOptions"
                  :key="job.value"
                  :label="job.label || job.name"
                  :value="job.value"
                />
              </el-select>
            </div>
          </div>
        </div>
        <div class="flex-shrink-0 flex items-center h-40px pr-15px">
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button type="primary" @click="handleAdd" class="ml-10px">+ 新增</el-button>
        </div>
      </div>
    </layout-item>

    <layout-main :scroll="false">
      <el-table
        stripe
        border
        height="100%"
        :data="tableData"
        empty-text="暂无数据"
        v-loading="loading"
      >
        <el-table-column prop="name" label="姓名" align="center" min-width="120" />
        <el-table-column prop="gender" label="性别" align="center" width="80" />
        <el-table-column label="岗位" align="center" min-width="120">
          <template #default="scope">
            <div class="job-cell">{{ scope.row.job }}</div>
          </template>
        </el-table-column>
        <el-table-column label="所属部门" align="center" min-width="150">
          <template #default="scope">
            <div class="department-cell">{{ scope.row.department }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="联系电话" align="center" min-width="120" />
        <el-table-column label="操作" align="center" width="280">
          <template #default="scope">
            <el-button type="primary" link @click="handleView(scope.row)">查看</el-button>
            <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </layout-main>

    <layout-item>
      <pagination
        :totalRow="total"
        :pageSize="pageSize"
        :currentPage="currentPage"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handlePageChange"
      />
    </layout-item>
  </my-layout>

  <!-- 员工详情弹窗 -->
  <StaffDetailModal
    :visible="detailModalVisible"
    :staff="currentStaff"
    :readonly="modalReadonly"
    @close="detailModalVisible = false"
    @update="handleStaffUpdate"
  />
</template>

<script>
import { ElMessage, ElMessageBox } from 'element-plus';
import { getCorpMember } from "@/api/corp.js";
import StaffDetailModal from '@/views/internet-dept-manage/components/staff-detail-modal.vue';
import pagination from '@/components/pagination/pagination.vue';
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";

export default {
  name: 'StaffInfo',
  components: {
    StaffDetailModal,
    pagination,
    MyLayout,
    LayoutMain,
    LayoutItem
  },
  data() {
    return {
      searchForm: {
        name: '',
        contact: '',
        department: [], // 改为数组，支持多选
        position: [] // 改为数组，支持多选
      },
      tableData: [],
      loading: false,
      total: 0,
      currentPage: 1,
      pageSize: 20,
      detailModalVisible: false,
      currentStaff: {},
      modalReadonly: true,
      departmentOptions: [],
      jobOptions: []
    }
  },
  methods: {
    async loadStaffList() {
      this.loading = true;
      try {
        const page=this.currentPage
        const pageSize=this.pageSize
        const params = {
          corpId: localStorage.getItem("corpId"),

        };

        // 添加搜索条件
        if (this.searchForm.name) {
          params.keyword = this.searchForm.name;
        }

        if (this.searchForm.contact) {
          params.mobile = this.searchForm.contact;
        }
        if (this.searchForm.department && this.searchForm.department.length > 0) {
          params.deptIds = this.searchForm.department;
        }

        if (this.searchForm.position && this.searchForm.position.length > 0) {
          params.job = this.searchForm.position;
        }

        // 添加超时处理
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('员工数据加载超时')), 15000) // 15秒超时
        });

        const apiPromise = getCorpMember(
          params,
          this.pageSize,
          this.currentPage
        );

        const response = await Promise.race([apiPromise, timeoutPromise]);

        console.log('API Response:', response);
        const { success, data, total } = response;
        if (success) {
          const actualData = Array.isArray(data) ? data : (data?.data || data?.list || []);
          console.log('Actual data:', actualData);
          
          this.tableData = Array.isArray(actualData) ? actualData.map(member => ({
            id: member._id || member.userid,
            name: member.anotherName || member.name,
            gender: member.gender === '0' || member.gender === 0 ? '男' : member.gender === '1' || member.gender === 1 ? '女' : '-',
            job: this.getJobNames(this.normalizeJobArray(member.job)),
            department: this.getDepartmentNames(this.normalizeDeptArray(member.deptIds)),
            phone: member.mobile || '-',
            // 保存原始数据用于弹窗
            originalStaff: member
          })) : [];
          this.total = total || (data?.total || 0);
        } else {
          this.tableData = [];
          this.total = 0;
        }
      } catch (error) {
        console.error('Error loading staff:', error);
        this.tableData = [];
        this.total = 0;

        // 如果是超时错误，给用户友好提示
        if (error.message && error.message.includes('超时')) {
          ElMessage.warning('员工数据加载超时，请稍后重试');
        } else {
          ElMessage.error('加载员工信息失败');
        }
      } finally {
        this.loading = false;
      }
    },
    
    handleSearch() {
      this.currentPage = 1;
      this.loadStaffList();
    },
    
    handleView(row) {
      this.currentStaff = row.originalStaff || row;
      this.modalReadonly = true;
      this.detailModalVisible = true;
    },

    handleEdit(row) {
      this.currentStaff = row.originalStaff || row;
      this.modalReadonly = false;
      this.detailModalVisible = true;
    },

    handleAdd() {
      this.currentStaff = {};
      this.modalReadonly = false;
      this.detailModalVisible = true;
    },

    async handleDelete(row) {
      try {
        // 检查人员是否关联账号
        const relationCheck = await this.checkRelatedData(row);

        if (relationCheck.hasRelation) {
          // 显示关联账号提示弹窗
          const message = `该人员关联了账号"${relationCheck.accountName}"，请解绑后再删除！`;
          await ElMessageBox.alert(
            message,
            '提示',
            {
              confirmButtonText: '知道了',
              type: 'warning'
            }
          );
          return;
        }

        // 如果没有关联账号，显示删除确认弹窗
        await ElMessageBox.confirm(
          '确定要删除该人员吗？',
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        );

        // 执行删除操作
        await this.deleteStaff(row);

      } catch (error) {
        if (error === 'cancel') {
          // 用户取消操作，不显示错误
          return;
        }
        console.error('删除人员失败:', error);
        ElMessage.error('删除人员失败');
      }
    },

    // 检查人员是否关联账号
    async checkRelatedData(staff) {
      try {
        const staffId = staff.id || staff._id || staff.userid;
        if (!staffId) {
          console.warn('无法获取人员ID');
          return { hasRelation: false, accountName: null };
        }

        // 检查是否有关联的账号
        const relatedAccount = await this.checkStaffAccount(staffId);

        if (relatedAccount) {
          console.log(`人员 ${staff.name} 关联了账号: ${relatedAccount.accountName}`);
          return {
            hasRelation: true,
            accountName: relatedAccount.accountName || relatedAccount.anotherName || '未知账号'
          };
        }

        console.log(`人员 ${staff.name} 无关联账号，可以删除`);
        return { hasRelation: false, accountName: null };

      } catch (error) {
        console.error('检查关联账号失败:', error);
        // 检查失败时，为了安全起见，假设有关联数据
        return { hasRelation: true, accountName: '检查失败' };
      }
    },

    // 检查员工是否有关联的账号
    async checkStaffAccount(staffId) {
      try {
        // 调用获取已开通账号列表的API
        const { getOpenedAccount } = await import('@/api/corp.js');
        const response = await getOpenedAccount();

        console.log('账号API响应:', response);

        if (response.success && response.data) {
          // 处理不同的数据结构
          let accounts = [];

          if (Array.isArray(response.data)) {
            // 如果 response.data 直接是数组
            accounts = response.data;
          } else if (response.data.data && Array.isArray(response.data.data)) {
            // 如果数据在 response.data.data 中
            accounts = response.data.data;
          } else if (response.data.list && Array.isArray(response.data.list)) {
            // 如果数据在 response.data.list 中
            accounts = response.data.list;
          } else {
            console.warn('账号数据格式不正确:', response.data);
            return null;
          }

          console.log('处理后的账号列表:', accounts);

          // 查找是否有匹配的账号
          const relatedAccount = accounts.find(account =>
            account.userid === staffId ||
            account.accountId === staffId ||
            account._id === staffId
          );

          if (relatedAccount) {
            console.log('找到关联账号:', relatedAccount);
          }

          return relatedAccount || null;
        }

        return null;
      } catch (error) {
        console.error('获取账号列表失败:', error);
        return null;
      }
    },

    // 执行删除操作
    async deleteStaff(staff) {
      try {
        const staffId = staff.id || staff._id || staff.userid;

        if (!staffId) {
          throw new Error('无法获取人员ID');
        }

        // 调用删除API
        const { removeCorpMember } = await import('@/api/corp.js');
        const result = await removeCorpMember(staffId);

        if (result.success) {
          ElMessage.success('删除成功');
          this.loadStaffList(); // 刷新列表
        } else {
          throw new Error(result.message || '删除失败');
        }

      } catch (error) {
        console.error('删除操作失败:', error);
        throw error;
      }
    },
    
    handlePageChange(page) {
      this.currentPage = page;
      this.loadStaffList();
    },

    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
      this.loadStaffList();
    },
    
    handleStaffUpdate() {
      this.loadStaffList();
    },
    
    async loadDepartmentOptions() {
      try {
        const { getDeptList } = await import('@/api/dept-manage');
        const corpId = localStorage.getItem('corpId');

        if (!corpId) {
          console.warn('未找到corpId');
          this.departmentOptions = [];
          this.jobOptions = [];
          return;
        }

        // 添加超时处理
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('部门和岗位数据加载超时')), 15000) // 15秒超时
        });

        const dataPromise = Promise.all([
          getDeptList({ corpId }),
          this.loadJobOptions()
        ]);

        const [deptResult] = await Promise.race([dataPromise, timeoutPromise]);

        if (deptResult.success && deptResult.data && deptResult.data.list) {
          this.departmentOptions = this.buildDepartmentTree(deptResult.data.list);
          console.log('部门数据加载成功:', this.departmentOptions.length, '个部门');
        } else {
          console.warn('获取部门数据失败:', deptResult);
          this.departmentOptions = [];
        }
      } catch (error) {
        console.error('加载部门选项失败:', error);
        this.departmentOptions = [];
        this.jobOptions = [];

        // 如果是超时错误，给用户友好提示
        if (error.message && error.message.includes('超时')) {
          ElMessage.warning('部门和岗位数据加载超时，可能显示不完整');
        }
      }
    },
    
    async loadJobOptions() {
      try {
        const { getCorpMemberJob } = await import('@/api/corp.js');

        // 添加超时处理
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('岗位数据加载超时')), 10000) // 10秒超时
        });

        const apiPromise = getCorpMemberJob();

        const result = await Promise.race([apiPromise, timeoutPromise]);

        if (result.success && result.data) {
          // 兼容不同的数据结构：data.data 或 data.list 或直接 data
          let jobData = [];
          if (Array.isArray(result.data)) {
            jobData = result.data;
          } else if (result.data.data && Array.isArray(result.data.data)) {
            jobData = result.data.data;
          } else if (result.data.list && Array.isArray(result.data.list)) {
            jobData = result.data.list;
          }

          this.jobOptions = JSON.parse(JSON.stringify(jobData));
          console.log('岗位数据加载成功:', this.jobOptions.length, '个岗位');
          console.log('岗位数据结构示例:', this.jobOptions[0]);
        } else {
          this.jobOptions = [];
          console.warn('获取岗位数据失败:', result);
        }
      } catch (error) {
        console.error('加载岗位选项失败:', error);
        this.jobOptions = [];

        // 如果是超时错误，给用户友好提示
        if (error.message && error.message.includes('超时')) {
          ElMessage.warning('岗位数据加载超时，可能显示不完整');
        }
      }
    },
    
    buildDepartmentTree(deptList) {
      const deptMap = new Map();
      const rootDepts = [];

      // 创建部门映射
      deptList.forEach(dept => {
        const deptNode = {
          _id: dept._id,
          deptId: dept.deptId,
          label: dept.deptName || dept.label,
          deptName: dept.deptName || dept.label,
          parentId: dept.parentId,
          children: []
        };
        deptMap.set(dept._id, deptNode);
        // 同时用deptId作为key，方便查找
        if (dept.deptId) {
          deptMap.set(dept.deptId, deptNode);
        }
      });

      // 构建树形结构
      deptList.forEach(dept => {
        const deptNode = deptMap.get(dept._id);
        if (dept.parentId && deptMap.has(dept.parentId)) {
          const parent = deptMap.get(dept.parentId);
          parent.children.push(deptNode);
        } else {
          rootDepts.push(deptNode);
        }
      });

      return rootDepts;
    },
    
    getDepartmentNames(deptIds) {
      if (!deptIds || !Array.isArray(deptIds) || deptIds.length === 0) {
        return '-';
      }

      const names = deptIds.map(id => this.getDepartmentName(id)).filter(name => name && name !== '-');
      return names.length > 0 ? names.join(', ') : '-';
    },
    
    getDepartmentName(deptId) {
      if (!deptId) return '-';

      // 如果部门选项还没有加载，返回原始ID
      if (!this.departmentOptions || this.departmentOptions.length === 0) {
        console.warn('部门选项未加载，显示原始ID:', deptId);
        return deptId;
      }

      function findName(options, targetId) {
        if (!Array.isArray(options)) return null;

        for (const option of options) {
          // 匹配 _id 或 deptId 或 value
          if (
            (option._id && String(option._id) === String(targetId)) ||
            (option.deptId && String(option.deptId) === String(targetId)) ||
            (option.value && String(option.value) === String(targetId))
          ) {
            return option.label || option.deptName || option.name;
          }

          if (option.children && option.children.length > 0) {
            const childName = findName(option.children, targetId);
            if (childName) return childName;
          }
        }
        return null;
      }

      const name = findName(this.departmentOptions, deptId);
      if (name) {
        return name;
      } else {
        console.warn('未找到部门名称，显示原始ID:', deptId);
        return deptId;
      }
    },
    
    normalizeJobArray(job) {
      if (!job) return [];
      if (Array.isArray(job)) return job.filter(Boolean);
      return [job].filter(Boolean);
    },

    normalizeDeptArray(deptIds) {
      if (!deptIds) return [];
      if (Array.isArray(deptIds)) return deptIds.filter(Boolean);
      return [deptIds].filter(Boolean);
    },

    getJobNames(jobArray) {
      if (!jobArray || !Array.isArray(jobArray) || jobArray.length === 0) {
        return '-';
      }

      // 如果岗位选项还没有加载，返回原始值
      if (!Array.isArray(this.jobOptions) || this.jobOptions.length === 0) {
        console.warn('岗位选项未加载，显示原始值:', jobArray);
        return jobArray.join(', ');
      }

      const jobMap = {};
      this.jobOptions.forEach(job => {
        if (job && job.value && (job.label || job.name)) {
          jobMap[job.value] = job.label || job.name;
        }
      });

      const mappedJobs = jobArray.map(job => {
        const mappedName = jobMap[job];
        if (!mappedName) {
          console.warn('未找到岗位名称，显示原始值:', job);
        }
        return mappedName || job;
      }).filter(Boolean);

      return mappedJobs.join(', ') || '-';
    }
  },
  
  async mounted() {
    try {
      // 先加载部门和岗位数据，再加载员工数据
      await this.loadDepartmentOptions();

      // 部门和岗位数据加载完成后再加载员工数据
      await this.loadStaffList();
    } catch (error) {
      console.error('初始化数据加载失败:', error);
      // 即使部门岗位数据加载失败，也要尝试加载员工数据
      this.loadStaffList();
    }
  }
}
</script>

<style lang="scss" scoped>
.search-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 15px;
  padding: 10px 15px;
}

.filter-label {
  white-space: nowrap;
}

.job-cell,
.department-cell {
  word-break: break-word;
  line-height: 1.4;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>