<template>
  <div class="flex flex-col h-full rect-border-box">
    <div class="flex-shrink-0 relative mb-[1rem]">
      <title-bar title="团队服务概况" />
      <div class="absolute top-0 right-0 text-13px flex h-full items-center">
        <div v-for="(item, idx) in typeList" :key="item.value" class="text-white cursor-pointer mr-10px hover:text-[#f9b008]" :class="[type === item.value ? 'text-[#f9b008]' : '']" @click="changeTime(item.value)">
          {{ item.label }}
        </div>
        <!-- <div class="text-white cursor-pointer mr-10px hover:text-[#f9b008]" :class="type==='thisMonth'">本月</div>
        <div class="text-white cursor-pointer mr-10px hover:text-[#f9b008]">上月</div>
        <div class="text-white cursor-pointer hover:text-[#f9b008]">全部</div> -->
      </div>
    </div>
    <div class="flex-grow relative">
      <div class="absolute inset-0">
        <el-table ref="tableRef" class="screen-el-table" :data="teamsServiceAndMemberCount" height="100%" v-bind="tableConfig">
          <el-table-column align="center" prop="taskContent" label="排名" width="70">
            <template #default="{ row: { rank } }">
              <div class="truncate text-white">{{ rank }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="团队名称">
            <template #default="{ row: { teamName } }">
              <div class="truncate text-white">{{ teamName }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="customerName" label="新增患者" width="80">
            <template #default="{ row: { memberCount } }">
              <div class="truncate text-white">{{ memberCount }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="userName" label="服务记录" width="80">
            <template #default="{ row: { serviceCount } }">
              <div class="truncate text-white">{{ serviceCount }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="satisfaction" label="服务满意率" width="100">
            <template #default="{ row: { satisfaction } }">
              <div class="truncate text-white">{{ satisfaction }}%</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script setup>
import { computed, onMounted, ref } from "vue";
import useBigScreenTable from "./use-big-screen-table";
import titleBar from "./title-bar.vue";
import { teamsServiceAndMemberCount, getTeamsServiceAndMemberCountApi } from "../api";

const tableRef = ref();
const { tableConfig } = useBigScreenTable(tableRef);
const typeList = [
  { label: "全部", value: "all" },
  { label: "今日", value: "thisDay" },
  { label: "本周", value: "thisWeek" },
  { label: "上周", value: "lastWeek" },
  { label: "本月", value: "thisMonth" },
  { label: "上月", value: "lastMonth" },
];
const type = ref("all");
onMounted(() => {
  getTeamsServiceAndMemberCountApi(type.value);
});
function changeTime(value) {
  type.value = value;
  getTeamsServiceAndMemberCountApi(value);
}
</script>
<style lang="scss" scoped>
:deep(.el-table__header .el-table__cell .cell) {
  white-space: nowrap;
}

:global(.el-table.screen-el-table .el-table__header tr) {
  background-color: rgba(13, 93, 171, 0.5);
}

:global(.el-table.screen-el-table .el-table__inner-wrapper::before) {
  display: none;
}

:deep(.el-table__cell) {
  background-color: transparent !important;
}
</style>
