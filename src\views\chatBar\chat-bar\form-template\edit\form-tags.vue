<template>
  <el-form-item class="px-15px py-6px el-form-item--autoheight" bg-fff :class="required ? 'is-required' : ''"
    :label="name" border-bottom>
    <div flex items-center class="w-full" @click="show()">
      <div flex flex-grow class="flex-wrap">
        <customer-tag v-for="tagId in value" :key="tagId" :tag-id="tagId" />
      </div>
      <div v-if="value.length" flex-shrink-0 class="text-14px ml-10px mr-11px" style="color: #a8abb2;" @click.stop="clear()">
        <el-icon>
          <CircleClose /> 
        </el-icon>
      </div>
      <div v-else flex-shrink-0 class="text-14px ml-10px mr-11px" style="color: #a8abb2;">
        <el-icon>
          <ArrowDown />
        </el-icon>
      </div>
    </div>
  </el-form-item>
  <tag-modal customer-handle :visible="visible" :value="value" :width="width" @change="change" @close="close" />
</template>
<script setup>
import { computed } from 'vue';
import customerTag from '@/components/customer-tag/customer-tag';
import tagModal from '../../character-info/tag-modal';
import useModal from '@/hooks/useModal';

const emits = defineEmits(['change'])
const props = defineProps({
  form: { type: Object, default: () => ({}) },
  name: { type: String, default: '' },
  required: { type: Boolean, default: false },
  title: { type: String, default: '' },
  wordLimit: { type: Number, default: 20 },
})
const value = computed(() => Array.isArray(props.form[props.title]) ? props.form[props.title] : []);

const { close, show, visible, width } = useModal();

function change(value) {
  close()
  emits('change', { title: props.title, value })
}

function clear() {
  emits('change', { title: props.title, value: '' })
}
</script>
<style lang="scss" scoped></style>
