<template>
  <el-dialog :model-value="visible" :width="width" title="修改员工科室" @close="close()">
    <el-scrollbar wrap-class="el-dialog-body-full-scroll">
      <el-form class="p-15px" label-suffix="：" :label-width="100" ref="formRef" :model="form" :rules="rules">
        <div v-if="staff" color-normal class="mb-15px pl-10px text-14px font-semibold">
          修改员工【{{ staff.name }}】的科室分配
        </div>
        
        <el-form-item label="员工信息">
          <div class="flex items-center">
            <span class="mr-10px">{{ staff?.name }}</span>
            <span class="text-gray-500">({{ staff?.userId }})</span>
          </div>
        </el-form-item>
        
        <el-form-item class="is-required" label="当前科室">
          <div class="text-gray-600">
            {{ staff?.deptNames?.join('、') || '无' }}
          </div>
        </el-form-item>
        
        <el-form-item class="is-required" label="目标科室" prop="deptIds">
          <el-select 
            v-model="form.deptIds" 
            placeholder="请选择科室" 
            class="w-full"
            multiple
            collapse-tags
            collapse-tags-tooltip
          >
            <el-option-group 
              v-for="district in districtOptions" 
              :key="district._id" 
              :label="district.districtName"
            >
              <el-option 
                v-for="dept in district.depts" 
                :key="dept._id" 
                :label="dept.deptName" 
                :value="dept._id"
              />
            </el-option-group>
          </el-select>
        </el-form-item>
        
        <el-form-item class="is-required" label="岗位" prop="jobName">
          <el-select v-model="form.jobName" placeholder="请选择岗位" class="w-full">
            <el-option label="主任医师" value="主任医师" />
            <el-option label="副主任医师" value="副主任医师" />
            <el-option label="主治医师" value="主治医师" />
            <el-option label="住院医师" value="住院医师" />
            <el-option label="护士长" value="护士长" />
            <el-option label="主管护师" value="主管护师" />
            <el-option label="护师" value="护师" />
            <el-option label="护士" value="护士" />
            <el-option label="技师" value="技师" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="备注" prop="remark">
          <el-input 
            type="textarea" 
            v-model="form.remark" 
            placeholder="请输入变更原因或备注信息" 
            maxlength="200"
            :autosize="{ minRows: 3, maxRows: 6 }" 
            resize="none" 
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button plain class="w-100px" @click="close()">取消</el-button>
        <el-button class="w-100px" :loading="loading" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, computed } from "vue";
import { ElMessage } from "element-plus";

const emits = defineEmits(["close", "change"]);
const props = defineProps({
  staff: { type: Object, default: null },
  cateList: { type: Array, default: () => [] },
  visible: { type: Boolean, default: false },
  width: { type: [String, Number], default: "600px" },
});

const formRef = ref();
const form = ref({
  deptIds: [],
  jobName: '',
  remark: ''
});
const loading = ref(false);

// 按院区分组的科室选项
const districtOptions = computed(() => {
  const districts = props.cateList.filter(item => item.type === 'district');
  return districts.map(district => ({
    ...district,
    depts: props.cateList.filter(item => item.type === 'dept' && item.parentId === district._id)
  })).filter(district => district.depts.length > 0);
});

// 表单验证规则
const rules = {
  deptIds: [
    { required: true, message: "请选择至少一个科室", trigger: "change" }
  ],
  jobName: [
    { required: true, message: "请选择岗位", trigger: "change" }
  ]
};

// 关闭弹窗
function close() {
  emits("close");
}

// 确认提交
async function confirm() {
  if (loading.value) return;
  
  try {
    await formRef.value.validate();
  } catch (error) {
    return;
  }
  
  loading.value = true;
  
}

// 监听visible变化，重置表单
watch(() => props.visible, (newVal) => {
  if (newVal && props.staff) {
    form.value = {
      deptIds: [],
      jobName: props.staff.jobName || '',
      remark: ''
    };
    if (formRef.value) {
      formRef.value.clearValidate();
    }
  }
});
</script>

<style scoped lang="scss">
.w-full {
  width: 100%;
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-600 {
  color: #4b5563;
}
</style>
