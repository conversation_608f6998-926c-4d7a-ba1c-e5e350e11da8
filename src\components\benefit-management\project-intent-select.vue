<template>
  <el-select
    v-model="selectedValue"
    :placeholder="placeholder"
    :disabled="disabled"
    clearable
    filterable
    remote
    :remote-method="remoteSearch"
    multiple
    collapse-tags
    collapse-tags-tooltip
    :loading="loading"
    @change="handleChange"
    @clear="handleClear"
    :style="{ width }"
  >
    <el-option
      v-for="item in projectIntentList"
      :key="item._id"
      :label="item.projectName"
      :value="item._id"
    />
  </el-select>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { getProjectIntentList } from "@/api/benefitManagement";

const props = defineProps({
  modelValue: {
    type: [String, Number, Array],
    default: () => []
  },
  placeholder: {
    type: String,
    default: '请搜索意向项目'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  deptId: {
    type: [String, Number],
    default: ''
  },
  width: {
    type: String,
    default: '100%'
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

const selectedValue = ref([]);
const projectIntentList = ref([]);
const loading = ref(false);

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  if (Array.isArray(newVal)) {
    selectedValue.value = newVal;
  } else if (newVal) {
    // 兼容单值传入的情况
    selectedValue.value = [newVal];
  } else {
    selectedValue.value = [];
  }
}, { immediate: true });

// 监听选中值变化
watch(() => selectedValue.value, (newVal) => {
  emit('update:modelValue', newVal);
});

// 监听deptId变化，重新获取项目列表
watch(() => props.deptId, (newVal) => {
  if (newVal) {
    fetchProjectIntent();
  }
});


// 获取项目意向列表
const fetchProjectIntent = async (keyword = '') => {
  loading.value = true;
  try {
    const params = {
      pageSize: 999,
      pageNum: 1,
      status: 'active',
    };
    
    if (keyword) {
      params.keyword = keyword;
    }
    
    if (props.deptId) {
      params.deptId = props.deptId;
    }
    
    const { success, data } = await getProjectIntentList(params);
    
    if (success && data && data.data) {
      projectIntentList.value = data.data.list || [];
    } else {
      projectIntentList.value = [];
      ElMessage.error('获取意向项目列表失败');
    }
  } catch (error) {
    console.error('获取意向项目列表失败:', error);
    ElMessage.error('获取意向项目列表失败');
    projectIntentList.value = [];
  } finally {
    loading.value = false;
  }
};

// 远程搜索方法
const remoteSearch = (query) => {
  if (query !== '') {
    fetchProjectIntent(query);
  } else {
    fetchProjectIntent();
  }
};

// 处理选择变化
const handleChange = (values) => {
  const selectedProjects = values.map(value => {
    const project = projectIntentList.value.find(item => item._id === value);
    return {
      id: project?._id || '',
      name: project?.projectName || ''
    };
  });
  emit('change', selectedProjects);
};

// 处理清除
const handleClear = () => {
  emit('change', []);
};

// 组件挂载时获取数据
onMounted(() => {
  fetchProjectIntent();
});

// 暴露方法给父组件
defineExpose({
  fetchProjectIntent
});
</script>
