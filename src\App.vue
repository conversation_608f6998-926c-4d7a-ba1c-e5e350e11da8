<template>
  <!-- vue3.0配置 -->
  <div h-full>
    <loading v-if="isLoading" />
    <router-view :class="isLoading ? 'hidden' : ''"></router-view>
  </div>
</template>
<script setup>
import { storeToRefs } from "pinia";
import { useScriptTag } from "@vueuse/core";

import { computed, defineAsyncComponent, onMounted } from "vue";
import { memberStore } from "@/store/member";
import { isMobile } from "@/utils/common";
const loading = defineAsyncComponent(() => import("@/views/loading/index.vue"));
const { logined } = storeToRefs(memberStore());
// const isLoading = computed(() => {
//   return isMobile() ? false : logined.value === "unlogin";
// });
const isLoading  = false;
onMounted(async () => {
  if (!import.meta.env.DEV && import.meta.env.VITE_TCB_PATH === "yktPCDeploy") {
    const { scriptTag, load, unload } = useScriptTag("https://cdn.bootcdn.net/ajax/libs/vConsole/3.15.1/vconsole.min.js", () => new VConsole(), { manual: true });
    load();
  }
});
</script>
<style lang="scss">
@import "./styles/attr.css";
@import "./styles/common.scss";
@import "./styles/cover-element.css";
@import "./styles/var.css";

// * {
//   -webkit-user-select: none;
//   /* chrome、safari、opera */
//   -khtml-user-select: none;
//   /* khtml browsers */
//   -moz-user-select: none;
//   /* Firefox */
//   -ms-user-select: none;
//   /* Internet Explorer/Edge */
//   user-select: none;
// }

[p-15] {
  padding: 15px;
}

.w-100px {
  width: 100px;
}

.w-full {
  width: 100%;
}

.el-dialog__body {
  padding: 10px !important;
}

// .el-input__inner{
//   width: 120px ;
// }

.selectMember .el-dialog__body {
  padding: 0px !important;
}



html,
body,
#app {
  margin: 0;
  height: auto;
}

#appView {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  height: 0;
  overflow: hidden;
  background-color: #f3f4f7;
  font-family: PingFang SC, Lantinghei SC, Microsoft YaHei, HanHei SC, Helvetica Neue, Open Sans, Arial, Hiragino Sans GB, 微软雅黑, STHeiti, WenQuanYi Micro Hei, SimSun, sans-serif;
  -webkit-tap-highlight-color: transparent;
  --el-text-color-regular: #666;

  .el-text--default {
    color: rgba(0, 0, 0, 0.9);
  }
}

[active-title-bar] {
  position: relative;
  margin-left: 10px;

  @at-root &::before {
    content: "";
    position: absolute;
    left: -10px;
    width: 4px;
    top: 5px;
    bottom: 3px;
    border-radius: 2px;
    background-color: #006eff;
  }
}
</style>
<style>
@import url(./assets/iconfont/icon.css);

.el-dialog-body-full-scroll {
  max-height: calc(85vh - 200px);
}

span.el-tag.el-tag--info {
  --el-tag-border-color: #bbb;
}

span.el-tag--large {
  padding: 0 20px;
  --el-tag-border-color: #006eff;
}

span.el-dialog__title {
  font-size: 18px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.9);
}

.el-dialog__header {
  border-bottom: 1px solid #eee;
}

footer.el-dialog__footer {
  padding-top: 15px;
  padding-bottom: 15px;
  box-shadow: 0 -1px 4px 0 rgba(54, 58, 80, 0.2);
}

.el-dialog {
  --el-dialog-border-radius: 8px !important;
}

.el-drawer__header {
  /* --el-drawer-padding-primary: 15px; */
  font-size: 16px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.9) !important;
  padding: 5px 15px !important;
  margin-bottom: 0 !important;
  border-bottom: 1px solid #eee;
}
.el-drawer__header .el-drawer__title{
  font-size: 15px;
}

body .el-table th.gutter {
  display: table-cell !important;
}

body .el-table colgroup.gutter {
  display: table-cell !important;
}

table {
  width: 100% !important;
}

/* table {
  width: 100% !important;
} */

.el-button.is-text:active {
  color: #fff;
}

svg {
  outline: none;
}

.el-form-item--autoheight .el-form-item__label {
  height: auto;
  font-size: 14px;
  padding-top: 9px;
  padding-bottom: 9px;
  line-height: 18px;
}

.el-table .el-table__cell {
  padding: 5px !important;
}
</style>
