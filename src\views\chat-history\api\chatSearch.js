import { searchChatRecord as searchChatRecordUrl, getCorpMemberChatRecord } from "@/api/sessionArchive.js";
import { ElMessage } from "element-plus";
import JSEncrypt from "jsencrypt";
import { publicKeys } from "./authMember.js";
import { ref, watch } from "vue";
import dayjs from "dayjs";
const seatchFrame = ref(null);
const searchChatRecordList = ref([]);
const searchLoading = ref(false);
const pageSize = ref(20);
const currentPage = ref(1);
const total = ref(0);
const keyword = ref("");
let nextCursor = "";
let hasMore = false;
const showSearchResult = ref(false);
async function searchChatRecord() {
  searchLoading.value = true;
  const query = {
    keyword: keyword.value,
    cursor: nextCursor,
    limit: 50,
  };
  const { success, data, message } = await searchChatRecordUrl(query);
  if (success) {
    let msgIds = data.data.map((item) => item.msgid);
    nextCursor = data.next_cursor;
    hasMore = data.has_more;
    getChatUserRecord(msgIds);
  } else {
    searchLoading.value = false;
    ElMessage.error(message);
  }
}
async function getChatUserRecord(msgIds) {
  if (msgIds.length === 0) return (searchLoading.value = false);
  const { success, data, message } = await getCorpMemberChatRecord({ pageSize: pageSize.value, page: currentPage.value, msgIds });
  searchLoading.value = false;
  if (success) {
    total.value = data.total;
    let list = data.data.map((item) => {
      item.secretKey = RSA_Decrypt(item);
      item.sendTime = dayjs(item.sendTime * 1000).format("MM-DD HH:mm");
      return item;
    });
    searchChatRecordList.value = searchChatRecordList.value.concat(list);
  } else {
    total.value = 0;
    searchChatRecordList.value = [];
    ElMessage.error(message);
  }
}
function handleScroll() {
  console.log("到底了");
  if (hasMore === 1) {
    searchLoading.value = true;
    searchChatRecord();
  }
}
watch(
  () => searchChatRecordList.value,
  async (val) => {
    if (val.length === 0) return;
    if (seatchFrame.value) {
      seatchFrame.value.setData({
        migList: searchChatRecordList.value,
      });
    }
  },
  { immediate: true }
);

// 解密操作：先 Base64 解码，再用私钥解密
function RSA_Decrypt(item) {
  // Base64 解码
  let key = publicKeys.value.find((i) => i.publicKeyVersion === item.publicKeyVersion);
  const privateKey = key ? key.privateKey : "";
  // 使用 RSA 私钥解密
  let decrypt = new JSEncrypt();
  decrypt.setPrivateKey(privateKey);
  console.log(privateKey);
  return decrypt.decrypt(item.encryptedSecretKey);
}

async function search() {
  if (!keyword.value) return;
  searchChatRecordList.value = [];
  nextCursor = "";
  hasMore = 0;
  showSearchResult.value = true;
  await searchChatRecord();
}

export { searchChatRecord, searchChatRecordList, searchLoading, seatchFrame, handleScroll, keyword, search, showSearchResult };
