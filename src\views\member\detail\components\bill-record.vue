<template>
  <my-layout bg-fff common-shadow>
    <layout-item>
      <div class="flex items-center p-15px pb-10px">
        <div class="flex-grow flex flex-wrap">
          <date-range-filter v-model:dates="createrDates" label="开单日期" :text="createrDatesText" :width="320" />
          <radio-filter v-model="billType" label="类别" :list="billTypeList" :width="200" />
          <base-filter-item :clearable="projects.length > 0" class="cursor-pointer" label="项目" :text="projectText" @clear="changeProjects([])" @onClick="showProjectModal()" />
          <base-filter-item :clearable="packageList.length > 0" class="cursor-pointer" label="套餐" :text="packageText" @clear="changePackage([])" @onClick="showPackageModal()" />
          <!-- <base-filter-item class="cursor-pointer" label="折扣" /> -->
        </div>
        <div class="flex-shrink-0">
          <el-button type="primary" @click="search">查询</el-button>
          <el-button plain type="primary" @click="reset()">重置</el-button>
        </div>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <el-table stripe v-loading="loading" border height="100%" class="mx-15px" :data="list" :span-method="arraySpanMethod">
        <el-table-column property="billTime" label="开单时间" width="160" />
        <el-table-column property="packageName" label="类别" width="160" />
        <el-table-column property="projectName" label="项目名称" min-width="160" />
        <el-table-column property="treatmentDeptName" label="治疗科室" min-width="160" />
        <el-table-column property="usageCount" label="项目数量" min-width="80" />
        <el-table-column property="price" label="单价" :min-width="110" />
        <el-table-column property="discount" label="折扣" width="80">
          <template #default="{ row: { discount, isFree } }">
            {{ isFree ? "赠送" : discount }}
          </template>
        </el-table-column>
        <el-table-column property="totalPrice" label="开单总价" width="120" />
        <el-table-column property="billdCreator" label="开单人" width="120">
          <template #default="{ row: { billdCreator } }">
            <ww-user :openid="billdCreator" />
          </template>
        </el-table-column>
        <!-- <el-table-column property="billStatus" label="收费状态" width="120" />
        <el-table-column property="billDate" label="收费日期" width="120" /> -->
      </el-table>
    </layout-main>
    <layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize" @handle-current-change="changePage" />
    </layout-item>
  </my-layout>
  <project-picker :value="projects" :visible="projectModalVisible" :width="projectModalWidth" @close="closeProjectModal()" @change="changeProjects" />
  <project-package-picker :data="packageList" :visible="packageModalVisible" :width="packageModalWidth" @close="closePackageModal()" @change="changePackage" />
</template>
<script setup>
import { computed, ref } from "vue";
import useElPagination from "@/hooks/useElPagination";
import useModal from "@/hooks/useModal";
import dayjs from "dayjs";
import pagination from "@/components/pagination/pagination.vue";
import projectPicker from "@/components/project-picker/project-picker.vue";
import projectPackagePicker from "@/components/project-package-picker/project-package-picker.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import { DateRangeFilter, baseFilterItem, RadioFilter } from "@/components/filter-bar";
import { getBillRecord } from "@/api/bill-record";

const billTypeList = [
  { label: "单项目", value: "project" },
  { label: "套餐", value: "package" },
];
const { memberId } = defineProps(["memberId"]);

const billType = ref("");
const createrDates = ref([]);
const packageList = ref([]);
const projects = ref([]);
const total = ref(0);
const arraySpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  const rows = list.value;
  const billTime = row.billTime;
  // 获取当前行的合并信息
  const currentRowSpan = rows.filter((item) => item.billTime === billTime).length;
  // 只在 item1 和 item2 列进行合并
  if (columnIndex === 0) {
    if (rowIndex === rows.findIndex((item) => item.billTime === billTime)) {
      return {
        rowspan: currentRowSpan,
        colspan: 1,
      };
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      };
    }
  }
};

const createrDatesText = computed(() => createrDates.value.join(" ~ "));
const packageText = computed(() => {
  if (packageList.value.length === 0) return "";
  if (packageList.value.length === 1) return packageList.value[0].name;
  return `已选择${packageList.value.length}个套餐`;
});
const projectText = computed(() => {
  if (projects.value.length === 0) return "";
  if (projects.value.length > 1) return `已选择${projects.value.length}个项目`;
  return projects.value[0].projectName;
});
const { list, page, pageSize, changePage, changeSize } = useElPagination(getList);
const { close: closeProjectModal, show: showProjectModal, visible: projectModalVisible, width: projectModalWidth } = useModal(800);
const { close: closePackageModal, show: showPackageModal, visible: packageModalVisible, width: packageModalWidth } = useModal(800);
getList();
function changePackage(data) {
  packageList.value = data;
}

function changeProjects(data) {
  projects.value = data;
}

function reset() {
  projects.value = [];
  packageList.value = [];
  billType.value = "";
  createrDates.value = [];
  search();
}

function search() {
  changePage(1);
}

async function getList() {
  let query = {
    customerId: memberId,
    page: page.value,
    pageSize: pageSize.value,
    billType: billType.value,
    billDates: createrDates.value,
    projects: projects.value.map((i) => i._id),
    packageList: packageList.value.map((i) => i._id),
  };
  let { success, data } = await getBillRecord(query);
  if (success) {
    list.value = data.list.map((i) => {
      return {
        ...i,
        billTime: i.billTime ? dayjs(i.billTime).format("YYYY-MM-DD HH:mm") : "",
        packageName: i.packageName || "单项目",
        price: i.price?.toFixed(2),
        totalPrice: i.totalPrice?.toFixed(2),
        discount: i.discount?.toFixed(2) || "",
        projectName: i.projectName || i.hmprojectName,
      };
    });
    total.value = data.total;
  }
}
</script>
<style lang="scss" scoped></style>
