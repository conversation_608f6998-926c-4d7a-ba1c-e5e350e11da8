<template>
  <my-layout bg-fff>
    <layout-item>
      <div class="p-15px">
        <div class="flex items-center justify-between">
          <div active-title-bar font-16 font-semibold>积分统计</div>
          <el-button type="primary" @click="toPointsDetail()">积分发放明细</el-button>
        </div>
        <div class="mt-15px overflow-hidden">
          <el-row :gutter="10">
            <el-col v-for="i in count" :key="i.key" :span="6">
              <div class="flex flex-col justify-between rounded bg-gray-100 rounded px-20px py-24px h-100px">
                <div class="text-15px text-gray-500">
                  {{ i.label }}
                </div>
                <div class="text-primary font-semibold text-28px">{{ i.value }}</div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="flex items-center justify-between mt-15px">
          <div active-title-bar font-16 font-semibold>积分规则列表</div>
          <el-button type="primary" :icon="Plus" @click="createPointsTask">新增积分规则</el-button>
        </div>
      </div>
    </layout-item>
    <Layout-main :scroll="false">
      <el-table height="100%" :data="list">
        <!-- <el-table-column type="index" width="80" label="序号" align="center" /> -->
        <el-table-column prop="condition" width="160" label="类型">
          <template #default="{ row: { triggerCondition } }">
            <div class="truncate">{{ triggerConditionTitle(triggerCondition) }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="detail" width="240" label="详情">
          <template #default="{ row }">
            <div class="truncate">{{ serviceTypeTitle(row) }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="rewardPoints" width="100" label="奖励积分">
          <template #default="{ row: { points } }">
            <div class="truncate">{{ points }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="times" width="130" label="剩余奖励/总奖励">
          
          <template #default="{ row: { taskTriggerTotal, serviceRecordCount } }">
            <div class="truncate">{{ taskTriggerTotal === -1 ? "无限次" : taskTriggerTotal - serviceRecordCount + "/" + taskTriggerTotal }}</div>
          </template>
        </el-table-column>
        <el-table-column width="140" prop="limitCount" label="每人每日上限奖励">
    
          <template #default="{ row: { userTriggerTodayLimitCount } }">
            <div class="truncate">{{ userTriggerTodayLimitCount === -1 ? "无限次" : userTriggerTodayLimitCount }}</div>
          </template>
        </el-table-column>
        <el-table-column width="120" prop="maxLimitCount" label="每人总上限">
          <template #default="{ row: { userTriggerlimitTotal } }">
            <div class="truncate">{{ userTriggerlimitTotal === -1 ? "无限次" : userTriggerlimitTotal }}</div>
          </template>
        </el-table-column>
        <el-table-column width="150" prop="status" label="创建时间">
          <template #default="{ row: { createTime } }">
            <div class="truncate">{{ dayjs(createTime).format("YYYY-MM-DD") }}</div>
          </template>
        </el-table-column>

        <el-table-column width="120" prop="status" label="启用状态">
          <template #default="{ row: { taskStatus } }">
            <div class="truncate" :class="taskStatus === 'enable' ? 'text-blue-500' : 'text-gray-500'">
              {{ taskStatus === "enable" ? "启用" : "停用" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column fixed="right" class-name="el-table-fixed-column--shadow" label="操作" width="100">
          <template #default="{ row }">
            <el-text type="primary" class="cursor-pointer" @click="toDetial(row)">详情</el-text>
            <el-text type="danger" class="ml-10px cursor-pointer" @click="changeStatus(row)" v-if="row.taskStatus === 'enable'">停用</el-text>
            <el-text type="primary" class="ml-10px cursor-pointer" @click="changeStatus(row)" v-else>启用</el-text>
          </template>
        </el-table-column>
      </el-table>
    </Layout-main>
    <Layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize" @handle-current-change="changePage" />
    </Layout-item>
  </my-layout>
  <rule-modal :visible="visible" :width="width" @close="close" @success="refresh" :data="taskDetial" />
</template>
<script setup>
import { onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import useElPagination from "@/hooks/useElPagination";
import useModal from "@/hooks/useModal";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import { Plus } from "@element-plus/icons-vue";
import ruleModal from "./rule-modal.vue";
import { getPointsTaskList, updatePointsTask } from "@/api/points";
import { getServicePointsStatistics } from "@/api/todo";
import { ServiceType } from "@/baseData";
import { ElMessage, ElMessageBox } from "element-plus";
import dayjs from "dayjs";
const count = ref([
  { label: "累计发放积分", value: 0, key: "allPoints" },
  { label: "今日发放积分", value: 0, key: "todayPoints" },
]);
const taskDetial = ref({});
const router = useRouter();
const list = ref([]);
const total = ref(0);
const rateList = [
  { label: "一星", value: 1 },
  { label: "二星", value: 2 },
  { label: "三星", value: 3 },
  { label: "四星", value: 4 },
  { label: "五星", value: 5 },
];
const { page, pageSize, changePage, changeSize } = useElPagination(getList);
const { close, show, visible, width } = useModal(640);
function toPointsDetail() {
  router.push({ name: "CORPSERVICEPOINTSLIST" });
}
function serviceTypeTitle(row) {
  if (row.serviceRate) return `服务评价获得一次"${rateList.find((i) => i.value === row.serviceRate).label}"`;
  else return ServiceType[row.serviceType] ? `完成一条类型为"${ServiceType[row.serviceType]}"服务` : "";
}
function triggerConditionTitle(type) {
  if (!type) return "";
  else if (type === "service") return "提供患者服务";
  else if (type === "rate") return "获得服务评价";
  else return "";
}
async function getList() {
  const params = {
    page: page.value,
    pageSize: pageSize.value,
  };
  const { data } = await getPointsTaskList(params);
  list.value = data.list;
  total.value = data.total;
}
async function changeStatus(item) {
  const { taskStatus, _id, serviceRate, serviceType } = item;
  await ElMessageBox.confirm(`确定要${taskStatus === "enable" ? "停用" : "启用"}吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  });
  const { success, message } = await updatePointsTask({ id: _id, taskStatus: taskStatus === "enable" ? "disable" : "enable", serviceRate, serviceType });
  if (success) {
    getList();
  } else {
    ElMessage.error(message);
    console.log(message);
  }
  console.log("停用");
}

async function getStatistics() {
  const { data: allData } = await getServicePointsStatistics();
  const { data: todayData } = await getServicePointsStatistics({
    dates: [dayjs().format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")],
  });
  count.value = [
    { label: "累计发放积分", value: allData.allPoints, key: "allPoints" },
    { label: "今日发放积分", value: todayData.allPoints, key: "todayPoints" },
  ];
}

function createPointsTask() {
  taskDetial.value = {};
  show();
}

function toDetial(row) {
  taskDetial.value = row;
  visible.value = true;
}

onMounted(async () => {
  getList();
  getStatistics();
});

function refresh() {
  page.value = 1;
  getList();
}
</script>