<template>
  <el-dialog v-model="props.visible" title="关联患者" draggable :width="width" @close="cancel">
    <div v-loading="loading">
      <div class="box-content">
        <div p-15 border-bottom flex>
          <el-input v-model="filterName" class="mr-10px" flex-grow placeholder="请输入患者身份证号、手机号查询" @blur="searchBlur" clearable></el-input>
          <el-button flex-shrink-0 type="primary" @click="search">查询</el-button>
        </div>
        <div v-if="list.length > 0">
          <div class="box-cell" v-for="item in list" :key="item.idCard">
            <div class="box-cell-name">
              <div class="flex algin-center">
                <div class="pr-10px t_1">{{ item.name }}</div>
                <div class="t_2">
                  {{ item.age }}
                  <span v-if="item.age && item.sex">/</span>
                  {{ item.sex }}
                </div>
              </div>
              <div class="mt-10px flex">
                <div class="w-80px">身份证号:</div>
                <div class="pl-5px">{{ item.idCard }}</div>
              </div>
              <div class="mt-10px flex">
                <div class="w-80px">手机号:</div>
                <div class="pl-5px">{{ item.mobile }}</div>
              </div>
              <div class="flex pt-10px">
                <div class="w-80px">服务团队：</div>
                <div>{{ getTeamNames(item.teamId) }}</div>
              </div>
            </div>
            <el-button type="primary" class="ml-10px" @click="bindMember(item)">绑定</el-button>
          </div>
        </div>
        <empty-data v-else :top="20" :title="noDataTitle" padding="0 0 15px" text-top="10px" :image-width="120"></empty-data>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <div>
          未找到患者可以:
          <el-button type="primary" class="ml-10px" @click="addCustomer">新增患者</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>
    <script setup>
import { computed, ref } from "vue";
import { updateMember, getCustomers } from "@/api/member";
import EmptyData from "@/components/empty-data.vue";
import { teamStore } from "@/store/team";
import validate from "@/utils/validate";
import { ElMessageBox } from "element-plus";
import { bindWechat } from "@/views/member/components/cutomer-table/utils";
const { allTeams } = teamStore();
const props = defineProps({
  visible: { type: Boolean, defautl: false },
  externalUserId: { type: String, default: "" },
  width: { type: Number, default: 520 },
});
const filterName = ref("");
const searchName = ref("");
const list = ref([]);
const loading = ref(false);
const emit = defineEmits(["close", "addCustomer", "success", "acceptCustomer"]);
function defineData() {
  filterName.value = "";
  searchName.value = "";
  list.value = [];
}
function cancel() {
  defineData();
  emit("close");
}
async function search() {
  searchName.value = filterName.value;
  if (!searchName.value) return;

  let query = {};
  if (validate.isMobile(filterName.value)) {
    query = {
      mobile: filterName.value,
    };
  } else if (validate.isChinaId(filterName.value)[0]) {
    query = {
      idCard: filterName.value,
    };
  }
  // 判断query 是否为空
  if (Object.keys(query).length === 0) return;
  loading.value = true;
  let { success, data } = await getCustomers(query);
  loading.value = false;
  list.value = success ? data.data : [];
}
function searchBlur() {
  if (!filterName.value) {
    searchName.value = "";
    list.value = [];
  }
}
const noDataTitle = computed(() => {
  return searchName.value ? `没有找到“${searchName.value}”在的建档信息` : "";
});
function addCustomer() {
  emit("addCustomer");
}
function getTeamNames(teamId) {
  if (!teamId) return "";
  if (typeof teamId === "string") teamId = [teamId];
  return allTeams
    .filter((team) => teamId.includes(team.teamId))
    .map((team) => team.name)
    .join("、");
}
async function bindMember(item) {
  if (loading.value) return;
  let externalUserId = props.externalUserId;
  loading.value = true;
  const currentTeamId = teamStore().currentTeamId;
  if (typeof item.teamId === "string") item.teamId = [item.teamId];
  if (!item.teamId.includes(currentTeamId)) item.teamId.push(currentTeamId);
  let contains = item.personResponsibles.some((i) => i.corpUserId === localStorage.getItem("userId") && i.teamId === currentTeamId);
  if (!contains) {
    item.personResponsibles = [
      ...item.personResponsibles,
      {
        corpUserId: localStorage.getItem("userId"),
        teamId: currentTeamId,
      },
    ];
  }
  let payLoad = {
    teamId: item.teamId,
    personResponsibles: item.personResponsibles,
  };
  if (externalUserId) payLoad.externalUserId = externalUserId;
  // 更新档案
  const { success } = await updateMember(item._id, payLoad);
  loading.value = false;
  if (success) {
    emit("close");
    emit("success");
    if (!externalUserId) await bindCustomerWeChat(item, item._id);
    defineData();
  }
}

async function bindCustomerWeChat(params, id) {
  if (params.externalUserId) return;
  await ElMessageBox.confirm(`${params.name}新建档案成功,是否绑定客户微信?`, "提示", {
    confirmButtonText: "立即绑定",
    cancelButtonText: "关闭",
    type: "warning",
  });
  await bindWechat({ _id: id });
  emit("success");
}
</script>
    <style scoped lang="scss">
.box-content {
  height: 300px;
  overflow-y: auto;
  position: relative;
  .box-cell {
    display: flex;
    justify-content: space-between;
    padding: 10px 0px 10px 20px;
    align-items: center;
    border-bottom: 1px solid #eee;
    .box-cell-name {
      .t_1 {
        color: #000;
        font-weight: 600;
        align-items: center;
      }
      .t_2 {
        font-size: 14px;
      }
    }
    .box-cell-radio {
      width: 18px;
      height: 18px;
      cursor: pointer;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}
</style>