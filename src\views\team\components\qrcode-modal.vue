<template>
  <el-dialog :model-value="visible" :title="isEdit ? '编辑团队二维码' : '创建团队二维码'" :width="width" @close="close()">
    <el-scrollbar ref="scrollbarRef" wrap-style="max-height: calc(85vh - 200px)">
      <el-form class="px-10px" label-position="top">
        <el-form-item class="is-required">
          <template #label><span color-normal>二维码名称</span></template>
          <el-input v-model="form.name" placeholder="请输入二维码名称" />
        </el-form-item>
        <el-form-item>
          <template #label><span color-normal>页面引导语</span></template>
          <el-input v-model="form.guide" type="textarea" :rows="5" resize="none" maxlength="80" show-word-limit
            placeholder="请输入页面引导语" />
        </el-form-item>
        <el-form-item class="is-required">
          <template #label><span color-normal>客户建档模版</span></template>
          <el-button type="primary" :icon="Plus" @click="setField('base', '客户建档模版字段')">{{ form.teamFileds &&
            form.teamFileds.baseInfo && form.teamFileds.baseInfo.length ? "修改" : "配置" }}建档信息字段</el-button>
          <el-table stripe v-if="link" :data="[1]" class="mt-10px">
            <el-table-column label="链接名称" width="80">
              <template #default="{ $index }">
                <span class="text-gray-500">建档链接</span>
              </template>
            </el-table-column>
            <el-table-column label="链接名称" min-width="120">
              <template #default>
                <div class="">{{ link }}</div>
              </template>
            </el-table-column>
            <el-table-column align="center" fixed="right" label="操作" width="120">
              <template #default>
                <el-text type="primary" class="cursor-pointer" @click="showCard">消息卡片配置</el-text>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-form-item v-if="healthTemplateList.length">
          <template #label><span color-normal>健康档案模版</span></template>
          <el-button type="primary" :icon="Plus" @click="setHealthField()">配置健康档案字段</el-button>
        </el-form-item>
        <el-form-item>
          <template #label><span color-normal>是否支持报到</span></template>
          <div class="flex-grow">
            <div class="flex items-center">
              <el-checkbox :model-value="form.enableReport === 'YES'" class="h-16px"
                @update:model-value="change($event, 'enableReport')">启用</el-checkbox>
              <span class="ml-20px leading-16px text-gray-500">
                <el-icon class="translate-y-2px transform mx-4px">
                  <BellFilled />
                </el-icon>
                <span>启用后，客户到院扫码后可点击报道，系统记录客户到院时间</span>
              </span>
            </div>
          </div>
        </el-form-item>
        <el-form-item>
          <template #label><span color-normal>是否展示宣教文章</span></template>
          <div class="flex-grow">
            <el-checkbox :model-value="form.enableAnnounce === 'YES'" class="h-16px"
              @update:model-value="change($event, 'enableAnnounce')">启用</el-checkbox>
            <div v-if="form.enableAnnounce === 'YES'" class="cursor-pointer" @click="showArticle">
              <el-select class="pointer-events-none" w-full :model-value="'checked'" placeholder="">
                <el-option :label="`已选择${form.articles ? form.articles.length : 0}篇`" value="checked" />
              </el-select>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </el-scrollbar>

    <template #footer>
      <div text-center>
        <el-button class="w-100px" @click="close">取消</el-button>
        <el-button v-if="isEdit" type="danger" class="w-100px" @click="remvoe()">删除</el-button>
        <el-button class="w-100px" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <fieldSetModal :data="field.data" :list="field.list" :title="field.title" :visible="fieldVisible" :width="fieldWidth"
    @close="closeField" @confirm="changeField($event)" />
  <message-card-modal :data="form.card" :visible="cardVisible" :width="cardWidth" @close="closeCard"
    @confirm="changeCard" />
  <article-modal :articles="form.articles" :width="articleWidth" :visible="articleVisible" @close="closeArticle"
    @change="changeTeamArticle"></article-modal>
  <health-template-modal :data="form.healthTempList" :tempList="healthTemplateList" :visible="healthVisible"
    :width="healthWidth" @close="healthClose" @change="changeHealth" />
</template>
<script setup>
import { computed, nextTick, ref, watch } from "vue";
import { storeToRefs } from "pinia";
import useModal from "@/hooks/useModal";
import { templateStore } from "@/store/template";
import { getRandomStr } from "@/utils";

import { Plus } from "@element-plus/icons-vue";
import ArticleModal from "./article-modal.vue";
import fieldSetModal from "./field-set-modal.vue";
import healthTemplateModal from "./health-template-modal.vue";
import messageCardModal from "./message-card-modal/message-card-modal.vue";
import { ElMessage, ElMessageBox } from "element-plus";

const { corpTemplateList } = storeToRefs(templateStore());
const baseTeamList = computed(() => getTemplate("baseTemplate"));
const healthTemplateList = computed(() => {
  return corpTemplateList.value
    .filter((i) => i.parentTemplateType === "medicalRecord" && i.templateStatus === "enable" && i.templateType === 'inhospital')
    .map(i => ({
      ...i,
      templateList: getTemplate(i.templateType)
    }));
})

const emits = defineEmits(["close", "confirm", "remove"]);
const props = defineProps({
  data: { type: Object, default: () => ({}) },
  teamId: { type: String, default: "" },
  visible: { type: Boolean, default: false },
  width: { type: Number },
});
const { close: closeField, show: showField, visible: fieldVisible, width: fieldWidth } = useModal(560);
const { close: closeCard, show: showCard, visible: cardVisible, width: cardWidth } = useModal();
const { close: closeArticle, show: showArticle, visible: articleVisible, width: articleWidth } = useModal(640);
const { close: healthClose, show: healthShow, visible: healthVisible, width: healthWidth } = useModal(800);

const form = ref({
  name: "",
  guide: "",
  teamFileds: {
    baseInfo: [],
    healthInfo: [],
  },
  enableHealth: "",
  card: {},
  enableReport: "NO",
  enableAnnounce: "NO",
  articles: [],
});
function change(val, key) {
  form.value[key] = val ? "YES" : "NO";
}

const field = ref({}); // health
function setField(type, title) {
  field.value.title = title;
  field.value.type = type;
  field.value.list = type === "base" ? baseTeamList.value : [];
  field.value.data = type === "base" ? form.value.teamFileds.baseInfo : [];
  showField();
}
const link = computed(() => {
  return form.value.teamFileds.baseInfo && form.value.teamFileds.baseInfo.length ? `${import.meta.env.VITE_PATIENT_PAGE_BASE_URL}pages/index/index?teamId=${props.teamId || ""}&qrid=${form.value.id}&corpId=${localStorage.getItem("corpId")}` : "";
});

const isEdit = ref(false);
const scrollbarRef = ref();
watch(
  () => props.visible,
  async (n) => {
    if (n) {
      const qrcode = props.data;
      const teamFileds = qrcode.teamFileds || {};
      isEdit.value = Boolean(qrcode.id);
      form.value.id = qrcode.id || getRandomStr(3);
      form.value.name = typeof qrcode.name === "string" ? qrcode.name : "";
      form.value.guide = typeof qrcode.guide === "string" ? qrcode.guide : "";
      form.value.card = Object.prototype.toString.call(qrcode.card) === "[object Object]" ? qrcode.card : {};
      form.value.enableReport = ["YES", "NO"].includes(qrcode.enableReport) ? qrcode.enableReport : "NO";
      form.value.enableAnnounce = ["YES", "NO"].includes(qrcode.enableAnnounce) ? qrcode.enableAnnounce : "NO";
      form.value.articles = Array.isArray(qrcode.articles) ? qrcode.articles : [];
      form.value.teamFileds.baseInfo = teamFileds && Array.isArray(teamFileds.baseInfo) ? teamFileds.baseInfo : [];
      form.value.healthTempList = Array.isArray(qrcode.healthTempList) ? qrcode.healthTempList : [];
      await nextTick();
      scrollbarRef.value && scrollbarRef.value.setScrollTop(0);
    }
  }
);
function close() {
  emits("close");
}
function confirm() {
  if (form.value.name.trim() === "") {
    ElMessage.info("请输入二维码名称");
  } else if (form.value.teamFileds.baseInfo.length === 0) {
    ElMessage.info("请选择客户基本信息字段");
  } else if (form.value.enableAnnounce === "YES" && form.value.articles.length === 0) {
    ElMessage.info("请选择宣教文章");
  } else {
    emits("confirm", { ...form.value, qrcode: link.value });
  }
}

function getTemplate(templateType) {
  const temp = corpTemplateList.value.find((i) => i.templateType === templateType);
  return temp && Array.isArray(temp.templateList) ? temp.templateList.filter((i) => i.fieldStatus !== "disable") : [];
}

function changeField(list) {
  form.value.teamFileds.baseInfo = list;
}
function changeCard(card) {
  form.value.card = card;
}

function changeTeamArticle(articles) {
  form.value.articles = articles;
}

function changeHealth(data) {
  form.value.healthTempList = data;
}

function remvoe() {
  ElMessageBox.confirm("确定要删除该二维码吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      emits("remove", form.value.id);
      close();
    })
    .catch(() => {
      // catch error
    });
}

function setHealthField() {
  healthShow()
}
</script>
<style scoped>
.append-select {
  width: 80px;
}

.title-bar {
  position: relative;
  padding-left: 10px;
  margin-bottom: 15px;
  font-size: 14px;
  font-weight: bold;
  color: var(--yc-main-text-color);
}

.modal-footer {
  /* padding-top: 15px; */
  text-align: center;
}

.mr-20 {
  margin-right: 20px;
}
</style>