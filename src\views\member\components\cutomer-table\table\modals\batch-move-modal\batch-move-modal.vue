<template>
  <el-dialog :model-value="visible" :title="title" :width="width" @close="close">
    <el-form class="p-15px" label-suffix="：" :label-width="120">
      <el-form-item v-if="canSelectType" label="转移类型">
        <el-select v-model="type" class="w-full" clearable placeholder="请选择转移类型">
          <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <div v-if="type && canSelectType" class="border-t border-gray-200 mb-15px"></div>
      <el-form-item v-if="type === 'transferToSameTeam'" class="is-required mb-0" label="新责任人">
        <div class="flex-grow">
          <ww-user-select :list="memberList" placeholder="请选择新责任人" :value="teammate" @change="change($event)" />
          <div class="flex items-center pt-10px" v-if="customers.length === 1 && myFriendUserIds.length > 0">
            <el-checkbox v-model="transferFriend" :label="transferFriend" size="large" class="mr-10px">同时转让好友关系</el-checkbox>
            <el-icon class="cursor-pointer text-gray-400 hover:text-blue-500" @click="show = true">
              <QuestionFilled />
            </el-icon>
          </div>
        </div>
      </el-form-item>
      <template v-else-if="type === 'transferToOtherTeam'">
        <el-text type="danger">客户将与本团队解除服务关系，本团队成员将没有权限查询到客户档案。</el-text>
        <el-form-item class="is-required mt-15px" label="新负责团队">
          <el-select v-model="newTeamId" class="w-full h-full" clearable placeholder="请选择负责团队">
            <el-option v-for="item in allTeams" :key="item.teamId" :label="item.name" :value="item.teamId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="mb-0" label="责任人">
          <div class="flex-grow">
            <ww-user-select :list="newTeamMemberList" placeholder="请选择责任人" :value="newTeamLeader" @change="changeLeader($event)" />
            <div class="flex items-center pt-10px" v-if="customers.length === 1 && myFriendUserIds.length > 0">
              <el-checkbox v-model="transferFriend" :label="transferFriend" size="large" class="mr-10px">同时转让好友关系</el-checkbox>
              <el-icon class="cursor-pointer text-gray-400 hover:text-blue-500" @click="show = true">
                <QuestionFilled />
              </el-icon>
            </div>
          </div>
        </el-form-item>
      </template>
      <el-form-item v-if="transferFriend" class="is-required mb-0" label="被转移成员">
        <div class="flex-grow">
          <ww-user-select :list="myFriendUserIds" placeholder="被转移成员" :value="handoverUserId" @change="changeHandoverUserId($event)" />
        </div>
      </el-form-item>
      <div v-else-if="type === 'transferToCustomerPool'" class="pt-15px px-10px text-15px leading-24px text-gray-500">客户将与本团队解除服务关系，本团队成员将没有权限查询到客户档案。</div>
    </el-form>
    <template #footer>
      <div text-center>
        <el-button type="primary" plain class="w-100px" @click="close()">取消</el-button>
        <el-button :loading="loading" class="w-100px" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <transfer-friend-modal :visible="show" :width="width" @close="show = false" />
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { ElMessage } from "element-plus";
import { transferCustomers } from "@/api/member";
import { teamStore } from "@/store/team";
import WwUserSelect from "@/components/ww-user-select";
import transferFriendModal from "./transfer-friend-modal.vue";
import { batchUpdateToDoAndManagePlan } from "@/api/todo";
import { transferExternalcontact } from "@/api/wecom";
import wxContact from "@/store/wxContact";
import { memberStore } from "@/store/member";

const { contactFollowUsers } = storeToRefs(wxContact());
const { corpInfo } = memberStore();
const { currentTeam, allTeams } = storeToRefs(teamStore());
const emits = defineEmits(["close", "change"]);
const props = defineProps({
  currentType: { type: String, default: "" },
  customers: { type: Object, default: () => ({}) },
  title: { type: String, default: "批量转移客户" },
  visible: { type: Boolean, default: false },
  width: { type: Number },
});
const handoverUserId = ref("");
const myFriendUserIds = computed(() => {
  if (props.customers.length === 1) {
    let externalUserId = props.customers[0].externalUserId;
    return contactFollowUsers.value[externalUserId] || [];
  }
  return [];
});

const canSelectType = ref(false);
const show = ref(false);
const loading = ref(false);
const type = ref("");
const typeList = [
  { label: "转移客户给本团队其他人", value: "transferToSameTeam" },
  { label: "转移客户给其他团队", value: "transferToOtherTeam" },
  { label: "把客户转移至公共客户池", value: "transferToCustomerPool" },
];

const transferFriend = ref(false);
const teammate = ref("");

watch(
  () => type.value,
  (n) => {
    if (n) {
      transferFriend.value = false;
    }
  }
);

const memberList = computed(() => (currentTeam.value && Array.isArray(currentTeam.value.memberList) ? currentTeam.value.memberList : []));
function change(data) {
  teammate.value = data;
}
function changeHandoverUserId(data) {
  handoverUserId.value = data;
}
const newTeamId = ref("");
const newTeam = computed(() => allTeams.value.find((item) => item.teamId === newTeamId.value));
const newTeamLeader = ref("");
const newTeamMemberList = computed(() => (newTeam.value && Array.isArray(newTeam.value.memberList) ? newTeam.value.memberList : []));
function changeTeam() {
  newTeamLeader.value = "";
}
function changeLeader(val) {
  newTeamLeader.value = val;
}

async function confirm() {
  if (!type.value) {
    ElMessage.error("请选择转移类型");
    return;
  }

  if (type.value === "transferToSameTeam" && !teammate.value) {
    ElMessage.error("请选择新责任人");
    return;
  }
  if (type.value === "transferToOtherTeam" && !newTeamId.value) {
    ElMessage.error("请选择新负责团队");
    return;
  }
  if (type.value === "transferToOtherTeam" && !newTeamLeader.value) {
    ElMessage.error("请选择责任人");
    return;
  }
  if (transferFriend.value && !handoverUserId.value) {
    ElMessage.error("请选择被转移成员");
    return;
  }
  if (transferFriend.value && (type.value === "transferToSameTeam" || type.value === "transferToOtherTeam") && handoverUserId.value === teammate.value) {
    ElMessage.error("被转移成员不能是新责任人");
    return;
  }
  let query = {
    currentTeamId: currentTeam.value.teamId,
    operationType: type.value,
  };
  if (type.value === "transferToSameTeam") {
    query["targetUserId"] = teammate.value;
    query["targetTeamId"] = currentTeam.value.teamId;
  } else if (type.value === "transferToOtherTeam") {
    query["targetTeamId"] = newTeamId.value;
    query["targetUserId"] = newTeamLeader.value;
  }
  await save(query);
}
async function save(data) {
  loading.value = true;
  const params = {
    customerIds: props.customers.map((item) => item._id),
    ...data,
  };
  let flag = await transferFriendAction();
  if (!flag) {
    loading.value = false;
    return;
  }
  await transferCustomers(params);
  let query = {
    customerIds: params.customerIds,
    currentTeamId: currentTeam.value.teamId,
    currentTeamName: currentTeam.value.name,
    executorUserId: teammate.value,
  };
  if (type.value === "transferToSameTeam") {
    query["operationType"] = "updateExecutor";
  } else {
    query["operationType"] = "closed";
  }
  await batchUpdateToDoAndManagePlan(query);
  emits("change");
  ElMessage.success("批量修改成功");
  loading.value = false;
}
async function transferFriendAction() {
  if (!transferFriend.value) return true;
  let externalUserIds = props.customers.map((item) => item.externalUserId);
  // 去重
  externalUserIds = Array.from(new Set(externalUserIds));
  if (externalUserIds.length === 0) return true;
  const params = {
    externalUserIds,
    handoverUserId: handoverUserId.value,
    permanentCode: corpInfo.permanent_code,
    takeoverUserId: type.value === "transferToSameTeam" ? teammate.value : newTeamLeader.value,
  };
  let { success, message } = await transferExternalcontact(params);
  if (!success) {
    ElMessage.error(message);
    return false;
  }
  return true;
}
function close() {
  emits("close");
}
watch(
  () => props.visible,
  (n) => {
    if (n) {
      type.value = "";
      transferFriend.value = false;
      teammate.value = "";
      newTeamId.value = "";
      newTeamLeader.value = "";
      handoverUserId.value = "";
      canSelectType.value = !typeList.some((i) => i.value === props.currentType);
      if (!canSelectType.value) {
        type.value = props.currentType;
      }
      // tagIds.value = [];
      // tagType.value = 'add'
      // customerStage.value = '';
      // customerSource.value.customerSource = [];
    }
  }
);
</script>