<template>
  <div class="information-source-container b-fff">
    <!-- 搜索区域 -->
    <div class="search-area">
      <div class="left-area">
        <span class="text-14px">所属科室：</span>
        <!-- 使用自定义科室选择器组件 -->
        <dept-select v-model="searchForm.deptId" @deptName="(value) => (searchForm.deptName = value)" />

        <el-input v-model="searchForm.keyword" placeholder="请输入名称搜索或拼音码" style="width: 240px; margin-left: 10px" />

        <el-button type="primary" @click="handleSearch" style="margin-left: 10px">搜索</el-button>
      </div>

      <div class="right-area">
        <el-button type="primary" @click="handleAdd">+ 新增</el-button>
      </div>
    </div>
    <!-- 表格区域 -->
    <el-table :data="tableData" border style="width: 100%" v-loading="tableLoading" height="calc(100vh - 260px)" :empty-text="'暂无数据'">
      <el-table-column label="序号" width="80" align="center">
        <template #default="scope">
          {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="状态" width="100" align="center">
        <template #default="scope">
          <span :class="scope.row.status === 'active' ? 'status-active' : 'status-inactive'">
            {{ scope.row.status === "active" ? "启用" : "停用" }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="projectName" label="意向项目名称" />
      <el-table-column prop="deptName" label="所属科室" />
      <el-table-column prop="pinyinCode" label="拼音码" align="center" />
      <el-table-column label="操作" width="160" align="center">
        <template #default="scope">
          <el-button type="primary" text @click="handleEdit(scope.row)">编辑</el-button>
          <el-button :type="scope.row.status === 'active' ? 'danger' : 'success'" text @click="handleToggleStatus(scope.row)">
            {{ scope.row.status === "active" ? "停用" : "启用" }}
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <div class="pagination-area">
      <span>共{{ total }}条数据</span>
      <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 30, 50, 100]" layout="prev, pager, next, jumper, sizes, total" :total="total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>

    <!-- 新增/编辑表单 -->
    <el-dialog v-model="drawerVisible" :title="isEdit ? '编辑项目' : '新增项目'" :width="500" center append-to-body destroy-on-close>
      <div class="form-container">
        <el-form :model="form" label-width="100px" :rules="rules" ref="formRef">
          <el-form-item label="项目名称：" prop="projectName">
            <el-input v-model="form.projectName" placeholder="请输入" />
          </el-form-item>

          <el-form-item label="所属科室" prop="deptId">
            <!-- 使用自定义科室选择器组件 -->
            <dept-select v-model="form.deptId" @deptName="(value) => (form.deptName = value)" width="100%" />
          </el-form-item>

          <el-form-item label="拼音码：" prop="pinyinCode">
            <el-input v-model="form.pinyinCode" placeholder="请输入" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="drawerVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
  
<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getProjectIntentList, addProjectIntent, updateProjectIntent, updateProjectIntentStatus } from "@/api/benefitManagement";
// 导入自定义科室选择器组件
import DeptSelect from "@/components/dept-components/dept-select.vue";

// 搜索表单
const searchForm = reactive({
  deptId: "",
  deptName: "",
  keyword: "",
});

// 表单数据
const form = reactive({
  projectName: "",
  deptId: "",
  deptName: "",
  pinyinCode: "",
});

// 表单规则
const rules = {
  projectName: [{ required: true, message: "请输入项目名称", trigger: "blur" }],
  deptId: [{ required: true, message: "请选择所属科室", trigger: "change" }],
};

// 表格数据
const tableData = ref([]);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 其他状态
const tableLoading = ref(false);
const drawerVisible = ref(false);
const isEdit = ref(false);
const formRef = ref(null);
const currentEditItem = ref(null);

// 页面加载时获取数据
onMounted(() => {
  fetchData();
});

// 获取表格数据
const fetchData = async () => {
  tableLoading.value = true;
  try {
    // 更新参数
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchForm.keyword,
    };

    // 如果选择了科室，添加科室参数
    if (searchForm.deptId) {
      params.deptId = searchForm.deptId;
      params.deptName = searchForm.deptName;
    }

    const { success, data } = await getProjectIntentList(params);

    if (success) {
      tableData.value = data.data.list || [];
      total.value = data.data.total || 0;
    } else {
      tableData.value = [];
      total.value = 0;
    }
  } catch (error) {
    console.error("获取项目意向列表失败:", error);
    ElMessage.error("获取数据失败");
  } finally {
    tableLoading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  currentPage.value = 1;
  fetchData();
};

// 新增
const handleAdd = () => {
  isEdit.value = false;
  resetForm();
  drawerVisible.value = true;
};

// 编辑
const handleEdit = (row) => {
  isEdit.value = true;
  currentEditItem.value = row;
  form.projectName = row.projectName;
  form.deptId = row.deptId;
  form.deptName = row.deptName || row.department; // 兼容旧数据
  form.pinyinCode = row.pinyinCode;
  drawerVisible.value = true;
};

// 切换状态
const handleToggleStatus = async (row) => {
  const action = row.status === "active" ? "停用" : "启用";
  try {
    await ElMessageBox.confirm(`确定要${action}该项目吗？`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    const { success, message } = await updateProjectIntentStatus({
      id: row._id,
      status: row.status === "active" ? "inactive" : "active", // 使用英文状态值
    });

    if (success) {
      ElMessage.success(`${action}成功`);
      fetchData(); // 重新获取数据以确保状态正确
    } else {
      ElMessage.error(message || `${action}失败`);
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error(`${action}项目失败:`, error);
      ElMessage.error(`${action}失败`);
    }
  }
};

// 提交表单
const handleSubmit = async () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        let res;
        const formData = {
          projectName: form.projectName,
          deptId: form.deptId,
          deptName: form.deptName,
          pinyinCode: form.pinyinCode,
        };

        if (isEdit.value) {
          res = await updateProjectIntent({
            id: currentEditItem.value._id,
            ...formData,
          });
        } else {
          res = await addProjectIntent(formData);
        }
        if (res.success) {
          ElMessage.success(isEdit.value ? "编辑成功" : "新增成功");
          drawerVisible.value = false;
          fetchData();
        } else {
          ElMessage.error(res.message || (isEdit.value ? "编辑失败" : "新增失败"));
        }
      } catch (error) {
        console.error(isEdit.value ? "编辑项目失败:" : "新增项目失败:", error);
        ElMessage.error(isEdit.value ? "编辑失败" : "新增失败");
      }
    }
  });
};

// 重置表单
const resetForm = () => {
  form.projectName = "";
  form.deptId = "";
  form.deptName = "";
  form.pinyinCode = "";
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 分页大小改变
const handleSizeChange = (val) => {
  pageSize.value = val;
  fetchData();
};

// 当前页改变
const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchData();
};
</script>
  
<style scoped>
.information-source-container {
  padding: 20px;
}

.search-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.status-active {
  color: #409eff;
}

.status-inactive {
  color: #f56c6c;
}

.tips-area {
  margin-top: 20px;
  font-size: 14px;
}

.tips-title {
  color: #f56c6c;
  font-weight: bold;
  margin-bottom: 5px;
}

.tips-content {
  color: #f56c6c;
  margin-bottom: 10px;
}

.tips-rules {
  color: #f56c6c;
}

.pagination-area {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 20px;
}

.pagination-area span {
  margin-right: 20px;
  color: #606266;
  font-size: 14px;
}

.form-container {
  padding: 10px 20px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  width: 100%;
  gap: 20px;
}

:deep(.el-dialog__header) {
  margin-right: 0;
  text-align: center;
  font-weight: bold;
}

:deep(.el-dialog__body) {
  padding: 10px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px;
}

/* 添加表格高度样式 */
:deep(.el-table) {
  min-height: 400px;
}

:deep(.el-table__empty-block) {
  min-height: 400px;
  height: calc(100vh - 300px);
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 科室选择器相关样式 */
.cursor-pointer {
  cursor: pointer;
}

/* 确保 base-filter-item 样式正确 */
:deep(.filter-item-container) {
  display: inline-flex;
  align-items: center;
  height: 32px;
}

:deep(.filter-item-content) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
