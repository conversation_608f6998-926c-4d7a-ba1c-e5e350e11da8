<template>
  <div v-if="visible" v-loading="loading" class="h-screen w-screen fixed inset-0 z-99 bg-white">
    <page-wrapper v-if="healthTempList.length" @back="close()">
      <template #header>
        <div font-16 font-semibold px-15 border-bottom class="py-12px">健康指标</div>
      </template>
      <form-template ref="healthRef" :items="healthTempList" :rule="rule" :form="forms" @change="change($event)" />
      <template #footer>
        <div class="relative z-2" bg-fff flex items-center px-15 py-10 common-shadow--r>
          <el-button plain class="flex-grow mr-15px" @click="close()">取消</el-button>
          <el-button :loading="loading" class="flex-grow" type="primary" @click="confirm()">保存</el-button>
        </div>
      </template>
    </page-wrapper>
  </div>
</template>
<script setup>
import { ref, onMounted, toRefs, watch, computed, inject } from 'vue';
import { storeToRefs } from 'pinia';
import { ElMessage } from 'element-plus';
import { updateMember } from "@/api/member";
import { templateStore } from "@/store/template";
import { checkNumber } from "../../form-template/edit/verify";
import formTemplate from "../../form-template/edit/index.vue";
import PageWrapper from '../../../components/page-wrapper.vue';
const refresh = inject('refresh-customer');
const emits = defineEmits(['close']);
const props = defineProps({
  visible: { type: Boolean, default: false },
  customer: { type: Object, default: () => ({}) }
})
const { visible } = toRefs(props);
const store = templateStore();
const { corpTemplateList } = storeToRefs(store);
const { getCorpTemplate, getTempateByType } = store;
const healthTempList = computed(() => getTemplate('healthTemplate'));
const form = ref({});
const healthRef = ref();
const forms = computed(() => ({ ...props.customer, ...form.value }));
const rule = {
  height(value, name) {
    const res = checkNumber(value, 20, 250);
    if (res === true) return true;
    else if (typeof res === 'string') return `${name}: ${res}`
    return `请输入有效的${name}`
  },
  weight(value, name) {
    const res = checkNumber(value, 5, 200);
    if (res === true) return true;
    else if (typeof res === 'string') return `${name}: ${res}`
    return `请输入有效的${name}`
  }
}

function getTemplate(templateType) {
  const temp = Array.isArray(corpTemplateList.value) ? corpTemplateList.value.find(i => i.templateType === templateType) : [];
  const tempList = temp && Array.isArray(temp.templateList) ? temp.templateList.filter(i => i && i.fieldStatus !== "disable" && i.operateType !== 'onlyRead') : [];
  return tempList;
}

// onMounted(() => {
//   if (!Array.isArray(corpTemplateList.value) || corpTemplateList.value.length === 0) getCorpTemplate()
// })


const tempLoading = ref(false);
async function getLatestTemp() {
  tempLoading.value = true
  await getTempateByType('healthTemplate');
  tempLoading.value = false

}
watch(visible, n => {
  if (n) {
    form.value = {};
    getLatestTemp()
  }
})

function change({ title, value }) {
  form.value[title] = value;
}

function close() {
  emits('close')
}

const loading = ref(false);
async function confirm() {
  if (Object.keys(form.value).length === 0) {
    emits('close')
    return
  };
  if (loading.value || !props.customer._id || !healthRef.value.verify()) return;
  loading.value = true;
  const { success, message = '更新客户信息失败' } = await updateMember(props.customer._id, { ...form.value });
  if (success) {
    ElMessage.success(message || '更新客户信息成功');
    refresh()
    emits('close');
  } else {
    ElMessage.error(message)
  }
  loading.value = false;

}

</script>
<style>
.create-customer .el-form-item__label {
  height: auto;
  font-size: 14px;
  padding-top: 9px;
  padding-bottom: 9px;
  line-height: 18px;
}
</style>
