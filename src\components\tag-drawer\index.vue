<template>
  <div>
    <el-drawer v-model="showTag" :title="props.title">
      <div class="tag-drawer">
        <div class="tag-box" v-for="tagGroup in tags">
          <div class="title">
            {{ tagGroup.groupName }}
            <span class="pl-5px text-12px" main-color v-if="tagGroup.createType == 'corpAsync'">(企微标签)</span>
          </div>
          <div>
            <el-tag v-for="item in tagGroup.tag" :key="item.id" size="large" class="pointer tag__item"
              :type="selections[item.id] ? 'primary' : 'info'" :effect="selections[item.id] ? 'light' : 'plain'"
              @click="toggle(item, tagGroup.tag)">
              {{ item.name }}
            </el-tag>
          </div>
        </div>
      </div>
      <template #footer>
        <div common-shadow--r class="button_view">
          <el-button class="l w-100px" type="primary" @click="save">保存</el-button>
          <el-button class="r w-100px" @click="cancel">取消</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>
<script setup>
import { ref, computed, onMounted } from "vue";
import { configStore } from "@/store/config";
import { storeToRefs } from "pinia";
const { getGroupTag } = configStore();
const { tags } = storeToRefs(configStore());
let $emit = defineEmits(["get-select-tag"]);

onMounted(() => {
  if (tags.value.length === 0) {
    getGroupTag();
  }
});
const props = defineProps({
  mult: { type: Boolean, default: false },
  title: {
    type: String,
    default: "新增标签",
  },
});
let showTag = ref(false);
const flatTags = computed(() => {
  return tags.value.reduce((arr, { tag }) => {
    return [...arr, ...(Array.isArray(tag) ? tag : [])];
  }, []);
});
const selections = ref({});

function openDialog(list = []) {
  selections.value = list.reduce((val, item) => {
    const opt = flatTags.value.find((i) => i.id == item);
    if (opt) val[item] = opt.id;
    return val;
  }, {});
  showTag.value = true;
}

function toggle(tag, opts) {
  if (props.mult) {
    selections.value[tag.id] = selections.value[tag.id] ? "" : tag.id;
  } else {
    opts.forEach(({ id }) => {
      selections.value[id] = id == tag.id ? (selections.value[id] ? false : tag.id) : false;
    });
  }
}

function save() {
  const tagIds = Object.values(selections.value).filter(Boolean)
  $emit("get-select-tag", tagIds);
  showTag.value = false;
}
function cancel() {
  showTag.value = false;
}

defineExpose({
  openDialog,
});
</script>

<style lang="scss" scoped>
.tag-box {
  padding-top: 12px;
  border-bottom: 1px dashed #ddd;

  @at-root &:last-of-type {
    border-bottom: 0;
  }

  @at-root &:first-of-type {
    padding-top: 0;
  }

  .title {
    font-size: 14px;
    font-weight: 600;
    color: rgba($color: #000000, $alpha: 0.9);
    margin-bottom: 12px;
  }

  .tag__item {
    margin-right: 15px;
    margin-bottom: 15px;
    padding: 6px 10px;
    min-width: 80px;
  }
}

.button_view {
  display: flex;
  padding: 15px;
  justify-content: center;
  border-top: 1px solid #eee;

  .l {
    margin-right: 20px;
  }
}

:deep(.el-drawer__footer) {
  --el-drawer-padding-primary: 0;
  padding-top: 0;
}
</style>
