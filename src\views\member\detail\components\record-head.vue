<!-- 页面顶部客户详情 -->
<template>
  <div color-normal class="px-15px py-10px bg-[#ecf4ff] bg-opacity-30 border-b border-gray-100">
    <div class="flex justify-between flex-wrap w-full pt-20px">
      <div class="mr-10px">
        <div class="flex items-center">
          <div class="text-16px font-semibold leading-30px" @click="log()">{{ customer.name }}</div>
          <div class="ml-10px text-14px leading-30px">{{ customer.sex }} {{ customer.sex && age ? " | " : "" }} {{ age }}</div>
          <div v-if="birthdayStr" :title="birthdayStr">
            <svg-icon class="ml-10px flex-shrink-0" title="" name="cake" size="24"></svg-icon>
          </div>
        </div>
        <div class="flex flex-nowrap items-center mt-4px">
          <template v-if="customer.tagIds && customer.tagIds.length">
            <customer-tag v-for="tagId in customer.tagIds.slice(0, 6)" :key="tagId" :tag-id="tagId" class="mb-0" />
            <el-popover :disabled="customer.tagIds.length <= 6" placement="top-start" :width="300" trigger="click">
              <template #reference>
                <span v-if="customer.tagIds.length > 6" class="ml-10px text-gray-400 cursor-pointer">...</span>
              </template>
              <div class="flex flex-wrap">
                <customer-tag v-for="tagId in customer.tagIds" :key="`${tagId}_popover`" :tag-id="tagId" class="flex-shrink-0" />
              </div>
            </el-popover>
            <svg-icon class="ml-5px text-[#007aff] cursor-pointer" size="18" name="pencil" @click="changeTag"></svg-icon>
          </template>
          <el-button v-else plain type="primary" size="small" @click="changeTag">新增标签</el-button>
        </div>
      </div>
      <div class="flex-shrink-0">
        <div class="flex items-center">
          <el-button plain class="ml-10px" size="small" type="primary" @click="moveCustomer()">转移</el-button>
          <el-button plain class="ml-10px" size="small" type="primary" @click="bindInhosCustomer" v-if="corpInfo.isConnectHis && !customer.isConnectHis">绑定院内档案</el-button>
          <el-button plain disabled size="small" class="ml-10px cursor-default pointer-events-none" v-if="corpInfo.isConnectHis && customer.isConnectHis">已绑定院内档案</el-button>
          <div v-if="contactName[customer.externalUserId] && hasWechatContact" class="flex items-center ml-10px cursor-pointer" @click="chat">
            <el-tooltip placement="top-end" effect="light">
              <svg-icon class="text-[#00c800]" name="wechat-rect" size="26"></svg-icon>
              <template #content>
                点击发起微信聊天
                <br />
                微信联系人：{{ contactName[customer.externalUserId] }}
              </template>
            </el-tooltip>
          </div>
          <div v-else-if="customer.externalUserId" class="flex items-center ml-10px p-5px text-gray-500 cursor-pointer" @click="noFriend()">
            <svg-icon name="wechat-rect" size="26"></svg-icon>
          </div>
          <div v-else class="flex items-center ml-10px text-gray-500 cursor-pointer" @click="bindWechat()">
            <el-tooltip placement="bottom" effect="light" content="点击绑定微信联系人">
              <svg-icon class="hover:text-[#00c800]" name="bind-wechat" size="30"></svg-icon>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>

    <el-row class="mt-5px text-14px">
      <el-col :lg="4" :md="8" :span="12">
        <div class="flex items-center pr-10px">
          <div class="flex-shrink-0 leading-26px text-gray-500">备注：</div>
          <div class="truncate text-[#FC8352]" :title="customer.notes || ''">{{ customer.notes || "--" }}</div>
          <svg-icon class="ml-5px text-[#007aff] cursor-pointer" size="18" name="pencil" @click="show"></svg-icon>
        </div>
      </el-col>

      <!-- <el-col :lg="5" :md="8" :span="12">
        <div class="flex items-center pr-10px">
          <div class="flex-shrink-0 leading-26px text-gray-500">当前团队责任人：</div>
          <div class="truncate">
            <ww-user v-if="person && person.corpUserId" :openid="person.corpUserId"></ww-user>
            <span v-else>--</span>
          </div>
          <el-icon class="flex-shrink-0 ml-5px text-18px cursor-pointer text-blue-500" @click="moveCustomer('transferToSameTeam', '变更责任人')">
            <Setting />
          </el-icon>
        </div>
      </el-col> -->

      <el-col :lg="5" :md="8" :span="12" v-if="isBenefitManagement">
        <div class="flex items-center pr-10px">
          <div class="flex-shrink-0 leading-26px text-gray-500">所属咨询：</div>
          <div class="truncate">
            <ww-user v-if="customer && customer.counselorRecord && Array.isArray(customer.counselorRecord)" :openid="customer.counselorRecord[customer.counselorRecord.length - 1]?.counselor"></ww-user>
            <span v-else>--</span>
          </div>
          <el-icon class="flex-shrink-0 ml-5px text-18px cursor-pointer text-blue-500" @click="changeConsultInfo('counselor')" v-if="customerType === 'corpCustomer' && corpType === 'main'">
            <Setting />
          </el-icon>
        </div>
      </el-col>

      <el-col :lg="5" :md="8" :span="12">
        <div class="flex items-center pr-10px">
          <div class="flex-shrink-0 leading-26px text-gray-500">最近服务：</div>
          <div class="truncate flex-shrink-0 min-w-0">{{ serviceTime || "--" }}</div>
        </div>
      </el-col>

      <el-col :lg="5" :md="8" :span="12">
        <div class="flex items-center pr-10px">
          <div class="flex-shrink-0 leading-26px text-gray-500">建档时间：</div>
          <div class="truncate flex-shrink-0 min-w-0">{{ createTime || "--" }}</div>
        </div>
      </el-col>

      <el-col :lg="5" :md="8" :span="12" v-if="isBenefitManagement">
        <div class="flex items-center pr-10px">
          <div class="flex-shrink-0 leading-26px text-gray-500">最近下单时间：</div>
          <div class="truncate">{{ lastOrderTime || "--" }}</div>
        </div>
      </el-col>

      <el-col :lg="4" :md="8" :span="12">
        <div class="flex items-center pr-10px">
          <div class="flex-shrink-0 leading-26px text-gray-500">共享团队：</div>
          <div v-if="serviceTeams.length" class="flex items-center">
            <el-popover placement="bottom-end" :width="350" trigger="click">
              <template #reference>
                <div class="truncate mr-4px cursor-pointer hover:text-blue-500">{{ serviceTeams.length }}个团队服务中</div>
              </template>
              <el-scrollbar wrap-style="max-height:40vh">
                <div v-for="i in serviceTeams" :key="i.teamId" class="py-10px flex border-b border-gray-100">
                  <div class="w-160px flex-shrink-0 truncate" :title="i.name || ''">{{ i.name }}</div>
                  <div class="w-160px flex-shrink-0 truncate">
                    <span class="mx-5px">|</span>
                    责任人：
                    <ww-user v-if="i.corpUserId" :openid="i.corpUserId"></ww-user>
                  </div>
                </div>
              </el-scrollbar>
            </el-popover>
            <el-icon class="flex-shrink-0 text-18px cursor-pointer text-[#0074ff]" @click="showShareModal">
              <CirclePlusFilled />
            </el-icon>
          </div>
        </div>
      </el-col>

      <el-col :lg="5" :md="8" :span="12" v-if="isBenefitManagement">
        <div class="flex items-center pr-10px">
          <div class="flex-shrink-0 leading-26px text-gray-500">所属开发：</div>
          <div class="truncate">
            <ww-user v-if="customer && customer.introducerRecord && Array.isArray(customer.introducerRecord)" :openid="customer.introducerRecord[customer.introducerRecord.length - 1]?.introducer"></ww-user>
            <span v-else>--</span>
          </div>
          <el-icon class="flex-shrink-0 ml-5px text-18px cursor-pointer text-blue-500" @click="changeConsultInfo('introducer')" v-if="customerType === 'corpCustomer' && corpType === 'main'">
            <Setting />
          </el-icon>
        </div>
      </el-col>

      <el-col :lg="5" :md="8" :span="12">
        <div class="flex items-center pr-10px">
          <div class="flex-shrink-0 leading-26px text-gray-500">添加人：</div>
          <div>
            <el-popover placement="bottom-end" :width="420" trigger="click">
              <template #reference>
                <el-text type="primary cursor-pointer">{{ serviceTimes.length }}人</el-text>
              </template>
              <el-scrollbar wrap-style="max-height:40vh">
                <div v-for="i in serviceTimes" :key="i.id" class="py-8px flex items-center border-b border-gray-100">
                  <svg-icon class="mr-4px flex-shrink-0" name="wx-work" size="24"></svg-icon>
                  <div class="flex-shrink-0 w-150px">
                    <ww-user v-if="i.id" :openid="i.id"></ww-user>
                  </div>
                  <div class="flex-shrink-0" v-if="i.executionTime">
                    <span class="mx-10px">|</span>
                    最近服务时间：{{ i.executionTime }}
                  </div>
                </div>
              </el-scrollbar>
            </el-popover>
          </div>
        </div>
      </el-col>

      <el-col :lg="5" :md="8" :span="12">
        <div class="flex items-center pr-10px">
          <div class="flex-shrink-0 leading-26px text-gray-500">最近到院：</div>
          <div class="truncate">{{ lastVisitTime || "--" }}</div>
        </div>
      </el-col>

      <el-col :lg="5" :md="8" :span="12" v-if="isBenefitManagement">
        <div class="flex items-center pr-10px">
          <div class="flex-shrink-0 leading-26px text-gray-500">累计消费：</div>
          <div class="truncate">{{ totalAmount || "--" }}</div>
        </div>
      </el-col>
    </el-row>
  </div>
  <bind-member :visible="bindCustomerVisible" :list="hisCustomers" :memberId="customer._id" @bindCustomerSuccess="bindCustomer" @cancel="bindCustomerVisible = false"></bind-member>
  <tag-drawer ref="tagDrawerRef" :mult="true" @get-select-tag="onTagChanged($event)"></tag-drawer>
  <edit-notes-modal :customer="customer" :visible="visible" :width="width" @change="changeNotes($event)" @close="close" />
  <batch-share-modal :customers="[customer]" title="共享客户" :visible="shareVisible" :width="shareWidth" @close="closeShareModal" @change="handleShare" />
  <batch-move-modal :current-type="moveType" :title="moveTitle" :customers="[customer]" :visible="moveVisible" :width="moveWidth" @close="closeMoveModal" @change="handleMove" />
  <introducer-modal :type="belongType" :customer="customer" :visible="personVisible" :width="personWidth" @close="closePersonModal" @change="handleBelong" />
</template>
<script setup>
import { computed, ref, toRefs, watch } from "vue";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import { ElMessage, ElMessageBox } from "element-plus";
import { getHisCustomerArchive } from "@/api/customerHisSync";
import { updateMember } from "@/api/member";
import useModal from "@/hooks/useModal";
import { getCustomerAge } from "@/helpers/customer";
import { getLastServiceTime } from "@/api/todo";
import { teamStore } from "@/store/team";
import wxContact from "@/store/wxContact";
import { openChatWindow } from "@/utils/common";
import { selectExternalContact } from "@/utils/jssdk";
import SvgIcon from "@/components/svg-icon";
import WwUser from "@/components/ww-user/index.vue";
import customerTag from "@/components/customer-tag/customer-tag";
import TagDrawer from "@/components/tag-drawer/index.vue";
import EditNotesModal from "../../components/cutomer-table/table/modals/edit-notes-modal/edit-notes-modal.vue";
import { memberStore } from "@/store/member";
import bindMember from "../../components/bind-his-customer.vue";
import batchShareModal from "../../components/cutomer-table/table/modals/batch-share-modal/batch-share-modal.vue";
import batchMoveModal from "../../components/cutomer-table/table/modals/batch-move-modal/batch-move-modal.vue";
import introducerModal from "./introducer-modal.vue";

const { corpInfo } = storeToRefs(memberStore());

const isBenefitManagement = computed(() => {
  return corpInfo.value && corpInfo.value.isBenefitManagement;
});
const emits = defineEmits(["change", "reload", "update-customer"]);
const { close: closeShareModal, show: showShareModal, visible: shareVisible, width: shareWidth } = useModal(); //  批量转移客户弹窗
const { close: closeMoveModal, show: showMoveModal, visible: moveVisible, width: moveWidth } = useModal(); //  批量转移客户弹窗
const { close: closePersonModal, show: showPersonModal, visible: personVisible, width: personWidth } = useModal(640);
const bindCustomerVisible = ref(false);
const hisCustomers = ref([]);
const props = defineProps({ customer: { type: Object, default: () => ({}) }, customerType: { type: String, default: "" }, corpType: { type: String, default: "" } });
const { customer } = toRefs(props);
const { currentTeam, teams, allTeams } = storeToRefs(teamStore());
const contactStore = wxContact();
const { getContacts } = contactStore;
const { contactName, contactFollowUsers } = storeToRefs(contactStore);
const followUser = computed(() => contactFollowUsers.value[customer.value.externalUserId]);
const hasWechatContact = computed(() => Array.isArray(followUser.value) && followUser.value.some((i) => i && i === localStorage.getItem("userId")) && customer.value.externalUserId);
const age = computed(() => getCustomerAge(customer.value));
const createTime = computed(() => (customer.value.createTime ? dayjs(customer.value.createTime).format("YYYY-MM-DD HH:mm") : ""));
const belongType = ref("");
const lastVisitTime = computed(() => {
  const inHospitalTimes = customer.value.inHospitalTimes;
  const lastTime = Array.isArray(inHospitalTimes) && inHospitalTimes.length > 0 ? inHospitalTimes[inHospitalTimes.length - 1] : "";
  return lastTime ? dayjs(lastTime).format("YYYY-MM-DD HH:mm") : "";
});
const totalAmount = computed(() => {
  if (!customer.value.consumeRecord || !Array.isArray(customer.value.consumeRecord)) {
    return "0.00";
  }
  const total = customer.value.consumeRecord.reduce((sum, bill) => {
    return sum + (bill.consumeAmount || 0);
  }, 0);
  return total.toFixed(2);
});

const lastOrderTime = computed(() => {
  const consumeRecord = customer.value.consumeRecord;
  const lastTime = Array.isArray(consumeRecord) && consumeRecord.length > 0 ? consumeRecord[consumeRecord.length - 1]?.createTime : "";
  return lastTime ? dayjs(lastTime).format("YYYY-MM-DD HH:mm") : "";
});

const personResponsibles = computed(() => (customer.value && Array.isArray(customer.value.personResponsibles) ? customer.value.personResponsibles : []));
const person = computed(() => personResponsibles.value.find((i) => i.teamId === currentTeam.value.teamId));
const serviceTime = computed(() => (customer.value && customer.value.serviceTime ? dayjs(customer.value.serviceTime).format("YYYY-MM-DD HH:mm") : "--"));
const serviceTimes = ref([]);
const birthdayStr = computed(() => {
  if (customer.value.birthdayStamp && dayjs(customer.value.birthdayStamp).isValid()) {
    return dayjs(customer.value.birthdayStamp).format("YYYY年MM月DD日");
  }
  if (customer.value.birthday && dayjs(customer.value.birthday).isValid()) {
    return dayjs(customer.value.birthday).format("YYYY年MM月DD日");
  }
  return "";
});

async function bindInhosCustomer() {
  if (!customer.value.idCard && !customer.value.customerNumber) {
    ElMessageBox.alert("请补全客户身份证号或档案编号!", "温馨提示", { type: "warning", confirmButtonText: "知道了" });
    return;
  }
  await getCustomerArchiveInhos();
  if (hisCustomers.value.length > 0) {
    bindCustomerVisible.value = true;
  } else {
    ElMessageBox.alert("his系统内未查询到客户档案!", "温馨提示", { type: "warning", confirmButtonText: "知道了" });
  }
}

function bindCustomer() {
  bindCustomerVisible.value = false;
  emits("reload");
}
async function getCustomerArchiveInhos() {
  let query = {};
  if (customer.value.idCard) {
    query = {
      idCard: customer.value.idCard,
    };
  } else if (customer.value.customerNumber) {
    query = {
      idNo: customer.value.customerNumber,
    };
  }
  let { success, data } = await getHisCustomerArchive(query);
  hisCustomers.value = success ? data.list : [];
}

async function bindWechat() {
  let { _id } = customer.value;
  let res = await selectExternalContact();
  const { err_msg, userIds } = res;
  if (err_msg !== "selectExternalContact:ok" && err_msg !== "selectExternalContact:cancel") {
    return ElMessage({
      message: err_msg,
      type: "error",
    });
  }
  if (userIds.length > 1) {
    return ElMessage({
      message: "只能选择一个用户!",
      type: "warning",
    });
  }
  let { success } = await updateMember(_id, { externalUserId: userIds[0] });
  if (success) {
    ElMessage({
      message: "更新成功",
      type: "success",
    });
    // 页面刷新
    emits("change", userIds[0]);
  }
}

function changeConsultInfo(type) {
  belongType.value = type;
  showPersonModal();
}

function chat() {
  hasWechatContact.value && openChatWindow(customer.value._id, customer.value.externalUserId);
}

async function getServiceTimes() {
  const res = await getLastServiceTime(customer.value._id, followUser.value);
  const list = res && Array.isArray(res.data.data) ? res.data.data : [];
  serviceTimes.value = followUser.value
    ? followUser.value.map((i) => {
        const item = list.find((item) => item.id === i);
        const executionTime = item && item.executionTime ? dayjs(item.executionTime).format("YYYY-MM-DD") : "";
        return { id: i, executionTime };
      })
    : [];
}
const serviceTeams = computed(() => {
  const teamIds = customer.value.teamId ? (typeof customer.value.teamId === "string" ? [customer.value.teamId] : customer.value.teamId) : [];
  return teamIds.map((i) => {
    const item = personResponsibles.value.find((j) => j.teamId === i);
    const team = allTeams.value.find((j) => j.teamId === i);
    return { name: team ? team.name : "", teamId: i, corpUserId: item ? item.corpUserId : "" };
  });
});

function handleShare() {
  closeShareModal();
  emits("reload");
}
function handleMove() {
  closeMoveModal();
  emits("reload");
}

function handleBelong() {
  closePersonModal();
  emits("reload");
}

watch(
  () => customer.value.externalUserId,
  async (n) => {
    if (n) {
      await getContacts([n]);
      getServiceTimes();
    }
  },
  { immediate: true }
);
// 监听customer.value 的变化
watch(
  customer.value,
  (newItem, oldItem) => {
    emits("update-customer");
  },
  { deep: true }
);

const tagDrawerRef = ref();

function changeTag() {
  tagDrawerRef.value.openDialog(Array.isArray(customer.value.tagIds) ? customer.value.tagIds : []);
}
async function onTagChanged(tagIds) {
  let { success, message } = await updateMember(customer.value._id, { tagIds });
  if (success) {
    ElMessage.success("更新成功");
    customer.value.tagIds = tagIds;
    emits("update-customer");
  } else {
    ElMessage.error(message);
  }
}
const { close, show, visible, width } = useModal(); //  编辑客户备注弹窗
function changeNotes(notes) {
  customer.value.notes = notes;
}

function noFriend() {
  ElMessage.info("您与客户不是好友关系，无法发起聊天；");
}
const moveType = ref("");
const moveTitle = ref("");
function moveCustomer(type = "", title = "转移客户") {
  moveType.value = type;
  moveTitle.value = title;
  showMoveModal();
}

function log() {
  console.log("log");
  console.log(person.value);
  console.log(customer.value);
}
</script>
