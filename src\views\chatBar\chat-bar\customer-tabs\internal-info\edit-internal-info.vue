<template>
  <div v-if="visible" v-loading="loading || tempLoading" class="h-screen w-screen fixed inset-0 z-99 bg-white">
    <page-wrapper v-if="infoTempList.length || true" @back="close()">
      <template #header>
        <div font-16 font-semibold px-15 border-bottom class="py-12px">内部信息</div>
      </template>
      <!-- 卡号字段编辑 -->
      <div class="px-15px py-10px border-b border-gray-100">
        <div class="text-14px text-gray-700 mb-8px font-medium">卡号</div>
        <el-input 
          v-model="cardNo" 
          placeholder="请输入卡号" 
          :maxlength="50"
          show-word-limit
          clearable
        />
      </div>
      <form-template ref="formRef" :items="infoTempList" :rule="rule" :form="forms" @change="change($event)" />
      <template #footer>
        <div class="relative z-2" bg-fff flex items-center px-15 py-10 common-shadow--r>
          <el-button plain class="flex-grow mr-15px" @click="close()">取消</el-button>
          <el-button :loading="loading" class="flex-grow" type="primary" @click="confirm()">保存</el-button>
        </div>
      </template>
    </page-wrapper>
  </div>
</template>
<script setup>
import { ref, onMounted, toRefs, watch, computed, inject } from 'vue';
import { storeToRefs } from 'pinia';
import { ElMessage } from 'element-plus';
import { updateMember } from "@/api/member";
import { templateStore } from "@/store/template";
import { checkNumber } from "../../form-template/edit/verify";
import { getCorpTemplate as getCorpTemplateUrl, getCurrentTemplate } from "@/api/corp.js";


import formTemplate from "../../form-template/edit/index.vue";
import PageWrapper from '../../../components/page-wrapper.vue';


const refresh = inject('refresh-customer');
const emits = defineEmits(['close']);
const props = defineProps({
  visible: { type: Boolean, default: false },
  customer: { type: Object, default: () => ({}) }
})

const { visible } = toRefs(props);
const store = templateStore();
const { corpTemplateList } = storeToRefs(store);
const { getCorpTemplate, getTempateByType } = store;
const infoTempList = computed(() => getTemplate('internalTemplate'));
const form = ref({});
const formRef = ref();
const forms = computed(() => ({ ...props.customer, ...form.value }));

// 卡号字段
const cardNo = ref('');

function getTemplate(templateType) {
  const temp = Array.isArray(corpTemplateList.value) ? corpTemplateList.value.find(i => i.templateType === templateType) : [];
  const tempList = temp && Array.isArray(temp.templateList) ? temp.templateList.filter(i => i && i.fieldStatus !== "disable" && i.operateType !== 'onlyRead') : [];
  return tempList;
}

const tempLoading = ref(false);
async function getLatestTemp() {
  tempLoading.value = true
  await getTempateByType('internalTemplate');
  tempLoading.value = false

}
watch(visible, n => {
  if (n) {
    form.value = {};
    // 初始化卡号字段
    cardNo.value = props.customer.cardNo || '';
    getLatestTemp()
  }
})

function change({ title, value }) {
  form.value[title] = value;
}

function close() {
  emits('close')
}

const loading = ref(false);
async function confirm() {
  // 准备提交数据，包含卡号
  const submitData = { ...form.value };
  if (cardNo.value !== props.customer.cardNo) {
    submitData.cardNo = cardNo.value;
  }
  
  if (Object.keys(submitData).length === 0) {
    emits('close')
    return
  };
  if (loading.value || !props.customer._id || (infoTempList.value.length > 0 && !formRef.value.verify())) return;
  loading.value = true;
  const { success, message = '更新客户信息失败' } = await updateMember(props.customer._id, submitData);
  if (success) {
    ElMessage.success(message || '更新客户信息成功');
    refresh()
    emits('close');
  } else {
    ElMessage.error(message)
  }
  loading.value = false;

}

</script>
<style>
.create-customer .el-form-item__label {
  height: auto;
  font-size: 14px;
  padding-top: 9px;
  padding-bottom: 9px;
  line-height: 18px;
}
</style>
