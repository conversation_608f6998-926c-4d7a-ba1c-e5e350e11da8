<template>
  <my-layout>
    <layout-item>
      <div class="py-5px flex bg-white">
        <expand-filter-box v-model:can-expand="canExpand" :enable-expand="true" class="flex-grow" :expand="expand">
          <radio-filter v-model="appointmentStatus" label="到院状态" :list="arrivalStatusOptions"></radio-filter>
          <date-range-filter v-model:dates="appointmentDates" label="预约日期" :text="appointmentDatesText" :width="320" />
          <filter-info-source v-model="infoSource" />
          <div class="text-gray-500 text-14px flex items-center">
            <div class="filter-label pr-5px">意向项目:</div>
            <project-intent-select v-model="projectIds" @change="handleProjectChange" placeholder="请选择意向项目" type="filter" style="width: 200px" multiple />
          </div>
          <check-box-filter v-model="introducers" label="所属开发" :list="peopleList" @clear="clearIntroducers" v-if="isManager">
            <template #item="{ item }">
              <ww-user :openid="item.value"></ww-user>
            </template>
          </check-box-filter>
          <input-filter v-model="name" label="客户姓名" placeholder="请输入" :width="200" />
          <input-filter v-model="mobile" label="客户手机号" placeholder="请输入" :width="200" />
        </expand-filter-box>
        <div class="flex-shrink-0 flex items-center h-40px pr-15px">
          <el-button v-if="canExpand" text plain @click="expand = !expand">
            <span class="mr-5px">{{ expand ? "收起筛选" : "更多筛选" }}</span>
            <el-icon v-if="expand">
              <ArrowUpBold />
            </el-icon>
            <el-icon v-else>
              <ArrowDownBold />
            </el-icon>
          </el-button>
          <el-button type="primary" @click="search">查询</el-button>
        </div>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <el-table stripe border height="100%" :data="tableData" v-loading="loading" ref="tableRef">
        <el-common-column prop="" label="到院状态" :min-width="100">
          <template #default="{ row }">
            <div :style="{ color: getArrivalStatusColor(row) }">
              {{ getArrivalStatusLabel(row) }}
            </div>
          </template>
        </el-common-column>
        <el-common-column prop="appointmentDate" label="预约时间" :min-width="120">
          <template #default="{ row }">
            <td-wrapper>{{ row.appointmentDate }}</td-wrapper>
          </template>
        </el-common-column>
        <el-common-column prop="name" label="姓名" :min-width="120">
          <template #default="{ row }">
            <span @click.stop="toCustomerDetail(row.customerId)" main-color class="pointer hover:underline underline-offset-2">
              {{ row.name }}
            </span>
          </template>
        </el-common-column>
        <el-common-column prop="mobile" label="联系方式" :min-width="120">
          <template #default="{ row }">
            <span>{{ maskPhone(row.mobile, 3, 4) }}</span>
          </template>
        </el-common-column>
        <el-common-column prop="consultTime" label="咨询时间" :min-width="160">
          <template #default="{ row }">
            <td-wrapper>{{ row.eConusltDate }}</td-wrapper>
          </template>
        </el-common-column>
        <el-common-column prop="projectName" label="咨询项目" :min-width="160">
          <template #default="{ row }">
            {{ row.projectName }}
          </template>
        </el-common-column>
        <el-common-column prop="consultSituation" label="咨询情况" :min-width="200">
          <template #default="{ row }">
            <td-wrapper>{{ row.reportDesc }}</td-wrapper>
          </template>
        </el-common-column>
        <el-common-column prop="infoSource" label="信息来源" :min-width="180">
          <template #default="{ row }">
            <td-wrapper>{{ row.infoSource }}</td-wrapper>
          </template>
        </el-common-column>
        <el-common-column prop="introducer" label="所属开发" :min-width="120">
          <template #default="{ row }">
            <ww-user v-if="row.introducer" :openid="row.introducer" />
          </template>
        </el-common-column>
      </el-table>
    </layout-main>
    <layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize" @handle-current-change="changePage" />
    </layout-item>
  </my-layout>
  <customer-detail :enableAction="['managementPlan']" :visible="customerDetailVisible" @close="customerDetailVisible = false" :customerId="customerId" title="客户详情" @update="changeSuccess" customerType="corpCustomer" />
</template>
<script setup>
import { computed, onMounted, ref, watch } from "vue";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import useElPagination from "@/hooks/useElPagination";
import { memberStore } from "@/store/member";
import { staffStore } from "@/store/staff";
import { teamStore } from "@/store/team";
import { maskPhone } from "@/utils";
import { ElMessage } from "element-plus";
import { CheckBoxFilter, DateRangeFilter, InputFilter, expandFilterBox, RadioFilter, filterInfoSource } from "@/components/filter-bar";
import elCommonColumn from "@/components/el-common-column/el-common-column.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import WwUser from "@/components/ww-user/index.vue";
import { getAppointmentRecord } from "@/api/consult";
import projectIntentSelect from "@/components/benefit-management/project-intent-select.vue";
import customerDetail from "@/views/member/detail/index.vue";

const appointmentDates = ref([
  dayjs().format("YYYY-MM-DD"), // 今天
  dayjs().add(7, "day").format("YYYY-MM-DD"), // 未来7天
]);
const appointmentDatesText = computed(() => appointmentDates.value.join(" ~ "));
const appointmentStatus = ref("");
const infoSource = ref([]); // 添加信息来源筛选变量
const projectIds = ref([]);
const selectedProjects = ref([]);
const customerDetailVisible = ref(false);

const customerId = ref("");
// 统一管理到院状态的映射
const arrivalStatusMap = {
  "": { label: "全部", color: "" },
  notInHospital: { label: "未到院", color: "red" },
  inHospital: { label: "已到院", color: "green" },
  failToHospital: { label: "爽约", color: "orange" },
};

// 从映射生成选项列表
const arrivalStatusOptions = Object.entries(arrivalStatusMap).map(([value, info]) => ({
  label: info.label,
  value: value,
}));

// 获取状态颜色
function getArrivalStatusColor(row) {
  return arrivalStatusMap[row.appointmentStatus || "notInHospital"]?.color;
}

// 获取状态标签
function getArrivalStatusLabel(row) {
  return arrivalStatusMap[row.appointmentStatus || "notInHospital"]?.label;
}

const list = ref([]);
const total = ref(0);
const name = ref("");
const mobile = ref("");
const canExpand = ref(false);
const expand = ref(false);
const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());
const { isAdmin } = storeToRefs(memberStore());
const { currentTeamId, managerList } = storeToRefs(teamStore());
const { judgmentIsAdmin } = memberStore();

const selectedIntroducers = ref([]);
const hasExplicitlyClearedIntroducers = ref(false);
const introducers = computed({
  get() {
    if (selectedIntroducers.value.length > 0) {
      return selectedIntroducers.value;
    }
    if (hasExplicitlyClearedIntroducers.value) {
      return [];
    }
    return managerList.value.includes(10005) || isAdmin.value ? [localStorage.getItem("userId")] : [];
  },
  set(newVal) {
    selectedIntroducers.value = newVal;
    hasExplicitlyClearedIntroducers.value = false;
  },
});

const loading = ref(false);
const tableRef = ref(null);
const { page, pageSize, changePage, changeSize } = useElPagination(getList);

const peopleList = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("onlineCustomerService")).map((i) => ({ value: i.userid }));
});

const isManager = computed(() => {
  return managerList.value.includes(10005) || isAdmin.value;
});

function clearIntroducers() {
  selectedIntroducers.value = [];
  hasExplicitlyClearedIntroducers.value = true;
}

function handleProjectChange(projects) {
  selectedProjects.value = projects;
}

function toCustomerDetail(id) {
  customerId.value = id;
  customerDetailVisible.value = true;
}

watch(
  () => currentTeamId.value,
  () => {
    getList();
  }
);

async function getList() {
  if (loading.value) return;
  loading.value = true;
  let params = {
    corpId: localStorage.getItem("corpId"),
    page: page.value,
    pageSize: pageSize.value,
    appointmentStatus: appointmentStatus.value,
  };
  if (name.value) params.name = name.value;
  if (mobile.value) params.mobile = mobile.value;
  if (appointmentDates.value.length) {
    params.startAppointmentTime = appointmentDates.value[0];
    params.endAppointmentTime = appointmentDates.value[1];
  }
  if (infoSource.value.length) params.infoSource = infoSource.value; // 添加信息来源筛选条件
  if (projectIds.value && projectIds.value.length > 0) params.projectIds = projectIds.value; // 添加项目筛选条件
  if (isManager.value) {
    const userIds = peopleList.value.map((i) => i.value);
    params.introducers = !introducers.value.length ? userIds : introducers.value;
  } else {
    params.introducers = [localStorage.getItem("userId")];
  }
  try {
    const { data, success } = await getAppointmentRecord(params);
    if (!success) {
      loading.value = false;
      ElMessage.error("获取预约记录失败");
      return;
    }
    loading.value = false;
    list.value = data.data.list;
    total.value = data.data.total;
  } catch (error) {
    loading.value = false;
    ElMessage.error("获取预约记录失败");
    console.error(error);
  }
}

const tableData = computed(() => {
  return list.value.map((i) => {
    return { appointmentDate: dayjs(i.appointmentTime).format("YYYY-MM-DD HH:mm"), eConusltDate: dayjs(i.eConusltTime).format("YYYY-MM-DD HH:mm"), projectName: Array.isArray(i.projectNames) ? i.projectNames.join("、") : "", infoSource: Array.isArray(i.source) ? i.source.join("-") : "", ...i };
  });
});

function search() {
  page.value = 1;
  getList();
}

onMounted(async () => {
  await getStaffList(true);
  await judgmentIsAdmin();
  getList();
});
</script>
<style lang="scss" scoped></style>
