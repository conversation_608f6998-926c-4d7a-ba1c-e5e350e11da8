<template>
  <el-table-column label="联系方式" prop="mobile" :width="120">
    <template #default="{row:{mobile , phone1 , phone2, phone3}}">
      <td-wrapper>{{ maskPhone(mobile || phone1 || phone2 || phone3,4,3) }}</td-wrapper>
    </template>
  </el-table-column>
</template>
<script setup>
import { maskPhone } from '@/utils';
import tdWrapper from './td-wrapper.vue';


</script>
<style lang="scss" scoped></style>
