<template>
  <div v-if="isRead">
    {{ value }}
  </div>
  <el-input v-else :model-value="value" :placeholder="item.placeholder || ''" :maxlength="item.wordLimit" type="textarea" show-word-limit @update:model-value="change($event)" :rows="4" resize="none" class="w-400px"></el-input>
</template>
<script setup>
import { ref, watch } from "vue";
const $emit = defineEmits(["change"]);
const props = defineProps({
  item: {
    type: Object,
    default: {},
  },
  value: {
    type: String,
    default: "",
  },
  isRead: {
    type: Boolean,
    default: false,
  },
});
function change(value) {
  $emit("change", {
    title: props.item.title,
    value,
  });
}
</script>

<style></style>