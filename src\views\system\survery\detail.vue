<template>
  <my-layout v-loading="loading" bg-fff>
    <div v-if="current && form._id !== current" flex flex-col h-full items-center justify-center>
      <empty-data title="未查询到问卷信息"></empty-data>
    </div>
    <div v-else flex h-full>
      <!-- <el-icon><QuestionFilled /></el-icon> -->
      <my-layout flex-grow flex-shrink-0>
        <layout-item>
          <div border-bottom p-15 font-16 font-semibold mr-10>{{ id ? '编辑问卷' : '新增问卷' }}</div>
        </layout-item>
        <layout-main ref="mainRef">
          <div p-15 border-bottom>
            <el-input class="name-input hidden-border-input" font-semibold text-center placeholder="点击输入问卷标题"
              v-model="form.name" size="large" :maxlength="20" />
          </div>
          <div class="px-15px py-10px" border-bottom>
            <div class="flex items-center">
              <div class="text-15px font-semibold w-120px">问卷分类:</div>
              <el-cascader v-model="cateId" :show-all-levels="false" :options="cateTree"
                :props="{checkStrictly:true,value:'_id'}" clearable />
            </div>
          </div>
          <div class="px-15px py-10px" border-bottom>
            <div class="flex items-center">
              <div class="text-15px font-semibold w-120px">
                问卷得分:
                <el-popover placement="top-start" title="" :width="200" trigger="hover" content="暂支持单选题">
                  <template #reference>
                    <el-icon class="text-16px text-gray-400 transform translate-y-2px">
                      <QuestionFilled />
                    </el-icon>
                  </template>
                </el-popover>

              </div>
              <el-switch v-model="form.enableScore" size="large" class="ml-2" inline-prompt active-text="启用得分"
                inactive-text="未启用得分" />
            </div>
          </div>
          <div p-15 border-bottom>
            <div class="text-15px font-semibold mb-10px">问卷说明： </div>
            <el-input type="textarea" placeholder="点击输入问卷说明" :autosize="{ minRows: 4, maxRows: 8 }"
              v-model="form.description" size="large" />
          </div>
          <transition-group name="list">
            <div v-for="(item, idx) in list" :key="item.id" p-15 border-bottom>
              <div pb-10 font-14 relative flex items-center>
                <div flex-shrink-0 relative :class="item.require ? 'require-title' : ''"> {{ idx + 1 }}、</div>
                <el-input class="hidden-border-input flex-grow title-input" placeholder="点击输入问卷题目" v-model="item.title">
                </el-input>
              </div>
              <div v-if="item.type === 'input'" pb-10>
                <el-input readonly />
              </div>
              <div v-if="item.type === 'radio'" pb-10 px-15>
                <el-radio-group>
                  <transition-group name="list">
                    <div v-for="(opt, index) in item.options" :key="opt.value" class="w-full group flex items-center"
                      :class="index>0 ?'border-t border-gray-200':''">
                      <el-radio :label="opt.value">
                        <div class="flex items-center group">
                          <div
                            class="flex items-center px-15px border rounded border-transparent focus-within:border-blue-500 group-hover:border-blue-500">
                            <el-input v-model="opt.label" class="hidden-border-input title-input w-240px"
                              placeholder="点击输入选项内容"></el-input>
                            <template v-if="form.enableScore">
                              <div class="mx-10px h-16px w-1px bg-gray-400"></div>
                              <el-input v-model="opt.score" class="hidden-border-input title-input w-60px text-right"
                                placeholder="得分">
                              </el-input>
                              <div class="text-14px text-gray-500">分</div>
                            </template>
                          </div>
                          <span class="inline-flex opacity-0 mx-10px transform translate-y-2px group-hover:opacity-100"
                            items-center>
                            <el-icon pointer size="18" :class="index === 0 ? 'cursor-not-allowed' : ''"
                              @click="moveOption('up', opt, item.options)">
                              <Top />
                            </el-icon>
                            <el-icon pointer size="18" class="ml-10px"
                              :class="index === item.options.length - 1 ? 'cursor-not-allowed' : ''"
                              @click="moveOption('down', opt, item.options)">
                              <Bottom />
                            </el-icon>
                            <el-icon pointer size="18" class="ml-10px" color="#F56C6C"
                              @click="removeOption(opt, item.options)">
                              <CloseBold />
                            </el-icon>
                          </span>
                        </div>
                      </el-radio>

                    </div>
                  </transition-group>
                </el-radio-group>
                <el-text pointer type="primary" @click="addOption(item.options)"><el-icon class="mr-5px">
                    <Plus />
                  </el-icon>添加选项</el-text>
              </div>
              <div flex items-center justify-between>
                <div class="move" :class="item.type === 'input' ? '' : 'invisible '">
                  <span font-14 mr-10>最大输入长度：</span>
                  <el-input-number v-model="item.maxlength" :step="10" :max="200" />
                </div>
                <div flex items-center>
                  <el-checkbox v-model="item.require" label="必填" />
                  <!-- <el-text pointer type="warning" class="ml-15px"><el-icon>
                    <Connection />
                  </el-icon>关联</el-text> -->
                  <el-text pointer class="ml-15px" :class="idx === 0 ? 'cursor-not-allowed' : ''"
                    @click="move('up', item)"><el-icon>
                      <ArrowUpBold />
                    </el-icon>上移</el-text>
                  <el-text pointer class="ml-15px" :class="idx === list.length - 1 ? 'cursor-not-allowed' : ''"
                    @click="move('down', item)">
                    <el-icon>
                      <ArrowDownBold />
                    </el-icon>下移</el-text>
                  <el-text pointer class="ml-15px" :class="idx === 0 ? 'cursor-not-allowed' : ''"
                    @click="move('top', item)">置顶</el-text>
                  <el-text pointer class="ml-15px" :class="idx === list.length - 1 ? 'cursor-not-allowed' : ''"
                    @click="move('bottom', item)">底部</el-text>
                  <el-text pointer type="danger" class="ml-15px" @click="remove(item)"><el-icon>
                      <Delete />
                    </el-icon>删除</el-text>

                </div>
              </div>

            </div>
          </transition-group>
        </layout-main>
        <layout-item>
          <div p-15 font-16 font-semibold text-center common-shadow--r>
            <el-dropdown>
              <el-button type="primary" plain style="margin-right:15px">
                添加题目<el-icon class="el-icon--right"><arrow-down /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>
                    <el-button :icon="EditPen" type="primary" plain @click="addQuestion('input')">填空题</el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-button :icon="Select" type="primary" plain @click="addQuestion('radio')">选择题</el-button>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <el-button class="w-100px" type="primary" plain @click="save(SurveryStatusKey.enable)">发布</el-button>
            <el-button v-if="id" class="w-100px" type="primary" plain @click="save(form.status)">保存</el-button>
            <el-button v-else class="w-100px" type="primary" plain @click="save(SurveryStatusKey.init)">保存</el-button>
            <el-button class="w-100px" type="primary" plain @click="preview()">预览</el-button>
          </div>
        </layout-item>
      </my-layout>
    </div>
  </my-layout>
  <preview-dialog :visible="visible" :data="data" @close="visible = false"></preview-dialog>
</template>
<script setup>
import { computed, nextTick, onActivated, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { SurveryStatusKey } from '@/baseData';
import useAlivePage from '@/hooks/useAlivePage';
import { getDetail, setSurvery, getSurveryCateList } from "@/api/survery";
import { getCateTreeData, getRandomStr } from '@/utils'

import EmptyData from "@/components/empty-data.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import PreviewDialog from './dialog.vue';
import { Select, EditPen, ArrowUpBold, ArrowDownBold } from "@element-plus/icons-vue";


const options = { cb: handleIdChange, reset }
const form = ref({});
const cateId = ref([]);
const cateList = ref([]);
const list = ref([]);
const loading = ref(false)
const mainRef = ref()
const visible = ref(false);
const { current, closePage } = useAlivePage(options);

const cateTree = computed(() => getCateTreeData(cateList.value))
const cateIdStr = computed(() => {
  if (typeof cateId.value === 'string') return cateId.value;
  return Array.isArray(cateId.value) ? cateId.value[cateId.value.length - 1] || '' : '';
})
const data = computed(() => {
  return { ...form.value, list: [...list.value] }
})

function addOption(options) {
  options.push({ label: `选项${options.length + 1}`, value: getRandomStr(5, 5) })
}

function getBaseItem(type) {
  const options = type === 'input' ? [] : [
    { label: '选项1', value: getRandomStr(5, 5) },
    { label: '选项2', value: getRandomStr(5, 5) }
  ]
  return { type, options, extraCase: [], extraValue: "", id: getRandomStr(), maxlength: 200, require: true, title: '', value: '' }
}

function handleIdChange(id) {
  if (id) getSurvery(id)
  else {
    cateId.value = history.state.cateId || cateId.value;
    reset()
  }
}

function preview() { if (verify()) visible.value = true }

function reset() {
  form.value = {};
  list.value = []
}

function verify() {
  let message = '';
  if (!form.value.name || form.value.name.trim() === '') {
    message = '请输入问卷标题';
  } else if (!cateList.value.some(i => i._id === cateIdStr.value && cateIdStr.value)) {
    message = '请选择问卷分类';
  } else if (list.value.length === 0) {
    message = '请添加问卷题目';
  } else {
    list.value.find((item, idx) => {
      if (!item.title || item.title.trim() === '') {
        message = `问题${idx + 1}的标题不能为空`;
        return true
      }
      if (item.type !== 'radio') return;
      const options = Array.isArray(item.options) ? item.options : [];
      if (options.length === 0) {
        message = `问题${idx + 1}的选项不能为空`;
        return true
      }
      const index = options.findIndex(i => !i.label || i.label.trim() === '');
      if (index >= 0) {
        message = `请填写题目${idx + 1} 第${index}项的选项内容`;
        return true
      }
      if (!form.value.enableScore) return false;
      const optIndex = options.findIndex((opt, index) => {
        if (typeof opt.score === 'string' && opt.score.trim() !== '') {
          if (!/^\d+$/.test(opt.score)) {
            message = `题目${idx + 1} 第${index + 1}项的得分应为正整数`;
            return true
          }
          if (typeof item.score === 'number' && !(item.score > 0 && item.score % 1 === 0)) {
            message = `题目${idx + 1} 第${index + 1}项的分数应为正整数`;
            return true
          }
        }
      })
      return optIndex > 0
    })
  }
  if (message) ElMessage.info(message);
  return !message
}

function moveOption(type, opt, options) {
  const index = options.findIndex(i => i.value === opt.value);
  if (index === 0 && type === 'up') return;
  if (index === options.length - 1 && 'down' === type) return;
  const [item] = options.splice(index, 1);
  if (type === 'up') {
    options.splice(index - 1, 0, item);
  } else if (type === 'down') {
    options.splice(index + 1, 0, item);
  }
}

async function addQuestion(type) {
  list.value.push(getBaseItem(type))
  await nextTick();
  mainRef.value && mainRef.value.scrollToBottom()
}

async function getCateList() {
  const { success, data, message } = await getSurveryCateList(localStorage.getItem('corpId'));
  if (success) {
    cateList.value = data && Array.isArray(data.list) ? data.list : []
  } else {
    ElMessage.error(message);
    cateList.value = []
  }
}

async function getSurvery(id) {
  const corpId = localStorage.getItem("corpId");
  const { success, data, message } = await getDetail(corpId, id);
  const { list: questions = [], ...rest } = data && data.data ? data.data : {};
  form.value = rest;
  cateId.value = rest.cateId || [];
  list.value = questions;
  if (!success) ElMessage.error(message);
}

async function move(type, item) {
  const index = list.value.findIndex(i => i.id === item.id);
  if (index === 0 && ['up', 'top'].includes(type)) return;
  if (index === list.value.length - 1 && ['down', 'bottom'].includes(type)) return;
  const [data] = list.value.splice(index, 1);
  if (type === 'up') {
    list.value.splice(index - 1, 0, data);
  } else if (type === 'down') {
    list.value.splice(index + 1, 0, data);
  } else if (type === 'top') {
    list.value.unshift(data);
  } else if (type === 'bottom') {
    list.value.push(data);
  }
}

async function remove(item) {
  await ElMessageBox.confirm('确定该题目吗', '提示');
  list.value = list.value.filter(i => i.id !== item.id);
}

async function removeOption(opt, options) {
  await ElMessageBox.confirm('确定该选项吗', '提示');
  const index = options.findIndex(i => i.value === opt.value);
  options.splice(index, 1)
}

async function save(status) {
  if (loading.value) return
  if (verify()) {
    loading.value = true
    const params = {
      corpId: localStorage.getItem('corpId'),
      userId: localStorage.getItem('userId'),
      name: form.value.name,
      description: form.value.description || '',
      enableScore: Boolean(form.value.enableScore),
      list: list.value,
      status,
      cateId: cateIdStr.value
    }
    if (form.value._id) params._id = form.value._id;
    const { success, message } = await setSurvery(params);
    if (success) {
      ElMessage.success(message);
      closePage()
    } else {
      ElMessage.error(message)
    }
    loading.value = false
  }
}

onActivated(() => getCateList())

</script>
<style scoped lang="scss">
.move {
  color: rgb(102, 102, 102);

  @at-root &:hover {
    color: #006eff;
  }
}

.name-input {
  font-size: 16px;
  font-weight: 600;
  color: rgba(0, 0, 0, .9);
}

.require-title::before {
  content: "*";
  position: absolute;
  color: #F56C6C;
  font-size: 12px;
  left: -6px;
  top: 0;
}

:deep(.hidden-border-input .el-input__wrapper) {
  box-shadow: none
}

:deep(.title-input .el-input__wrapper) {
  padding-left: 0;
}

:deep(.el-radio-group) {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

:deep(.el-radio) {
  display: flex;
  height: auto;
  padding-top: 5px;
  padding-bottom: 5px;

}
</style>