<template>
  <div class="report-container">
    <div class="report-header">
      <!-- <h2 class="report-title">电话网络客户消费明细表（按咨询人员）</h2>
      <div class="report-date">{{ formatDateRange }}</div> -->
      <div class="report-actions">
        <div class="filter-section">
          <div class="filter-row">
            <div class="filter-item">
              <span class="filter-label">购买日期：</span>
              <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :shortcuts="dateShortcuts" :disabled-date="disabledDate" @change="handleDateChange" class="date-range" />
            </div>
            <div class="filter-item">
              <span class="filter-label">客户ID：</span>
              <el-input v-model="clientIdFilter" placeholder="客户ID" clearable />
            </div>

            <div class="filter-item">
              <span class="filter-label">客户姓名：</span>
              <el-input v-model="clientNameFilter" placeholder="客户姓名" clearable />
            </div>

            <!-- 添加手机号筛选 -->
            <div class="filter-item">
              <span class="filter-label">手机号：</span>
              <el-input v-model="phoneFilter" placeholder="全号或后四位" clearable @input="handlePhoneFilterInput" />
            </div>

            <div class="filter-item">
              <check-box-filter v-model="consultantFilter" label="咨询师" :list="consultantList" v-if="isManager" @clear="handleConsultantFilterClear">
                <template #item="{ item }">
                  <ww-user :openid="item.value"></ww-user>
                </template>
              </check-box-filter>
            </div>
            <!-- <div class="filter-item">
              <span class="filter-label">接诊状态：</span>
              <el-select v-model="receptionStatusFilter" placeholder="接诊状态" clearable>
                <el-option v-for="item in receptionStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div> -->

            <div class="filter-item">
              <base-filter-item :clearable="projectCategories.length > 0" class="cursor-pointer" label="项目分类" :text="categoryText" @clear="clearCategories" @onClick="showCategoryPicker()" />
            </div>
            <div class="filter-item">
              <base-filter-item :clearable="projectFilter.length > 0" class="cursor-pointer" label="开单项目" :text="projectText" @clear="clearProjects" @onClick="showProjectModal()" />
            </div>

            <div class="filter-item">
              <base-filter-item :clearable="deptFilter.length > 0" class="cursor-pointer" label="开单科室" :text="deptText" @clear="clearDepts" @onClick="showDeptPicker()" />
            </div>

            <!-- 添加信息来源筛选 -->
            <div class="filter-item">
              <filter-info-source v-model="infoSourceFilter" label="信息来源" />
            </div>

            <!-- 添加开发渠道筛选 -->
            <div class="filter-item">
              <filter-customer-source v-model="devPathFilter" label="开发渠道" />
            </div>

            <!-- 添加接诊状态筛选 -->
            <div class="filter-item">
              <check-box-filter v-model="consultStageFilter" label="接诊类型" :list="ConsultStage" />
            </div>

            <div class="filter-item">
              <span class="filter-label">项目类型：</span>
              <el-select v-model="itemTypeFilter" placeholder="项目类型" clearable>
                <el-option v-for="item in ProjectType" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
            <div class="filter-actions">
              <el-button type="primary" @click="handleSearch" :loading="loading" :disabled="loading">查询</el-button>
              <el-button type="success" @click="exportToExcel" :disabled="loading">导出Excel</el-button>
              <el-button @click="resetFilters" :disabled="loading">重置</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="table-wrapper">
      <div class="table-container" v-loading="loading" element-loading-text="正在获取数据...">
        <table class="medical-table">
          <colgroup>
            <col style="width: 80px" />
            <!-- 咨询人员 -->
            <col style="width: 100px" />
            <!-- 客户门诊ID -->
            <col style="width: 80px" />
            <!-- 客户姓名 -->
            <col style="width: 90px" />
            <!-- 开单科室 -->
            <col style="width: 80px" />
            <!-- 开单医生 -->
            <col style="width: 90px" />
            <!-- 项目类型 -->
            <col style="width: 80px" />
            <!-- 项目类别1 -->
            <col style="width: 80px" />
            <!-- 项目类别2 -->
            <col style="width: 80px" />
            <!-- 项目类别3 -->
            <col style="width: 80px" />
            <!-- 项目类别4 -->
            <col style="width: 100px" />
            <!-- 项目名称 -->
            <col style="width: 120px" />
            <!-- 购买数量 -->
            <col style="width: 70px" />
            <!-- 金额 -->
            <col style="width: 100px" />
            <!-- 开发人员 -->
            <col style="width: 80px" />
            <!-- 消费日期 -->
            <col style="width: 120px" />
            <!-- 订单类型 -->
            <col style="width: 80px" />
            <!-- 接诊类型 -->
            <col style="width: 100px" />
            <!-- 信息来源 -->
            <col style="width: 100px" />
            <!-- 开发渠道1 -->
            <col style="width: 80px" />
            <!-- 开发渠道2 -->
            <col style="width: 80px" />
            <!-- 开发渠道3 -->
            <col style="width: 80px" />
            <!-- 初诊日期 -->
            <col style="width: 120px" />
          </colgroup>
          <thead>
            <tr>
              <th>咨询师</th>
              <th>客户门诊ID</th>
              <th>客户姓名</th>
              <th>开单科室</th>
              <th>开单医生</th>
              <th>项目类型</th>
              <th>项目类别1</th>
              <th>项目类别2</th>
              <th>项目类别3</th>
              <th>项目类别4</th>
              <th>项目名称</th>
              <th>购买数量</th>
              <th>金额</th>
              <th>开发人员</th>
              <th>消费日期</th>
              <th>订单类型</th>
              <th>接诊类型</th>
              <th>信息来源</th>
              <th>开发渠道1</th>
              <th>开发渠道2</th>
              <th>开发渠道3</th>
              <th>初诊日期</th>
            </tr>
          </thead>
          <tbody>
            <template v-for="(group, groupIndex) in groupedData" :key="groupIndex">
              <template v-for="(client, clientIndex) in group.clients" :key="clientIndex">
                <template v-for="(item, itemIndex) in client.items" :key="itemIndex">
                  <tr>
                    <td v-if="clientIndex === 0 && itemIndex === 0" :rowspan="group.rowspan">{{ group.consultantName }}</td>
                    <td v-if="itemIndex === 0" :rowspan="client.items.length">{{ client.clientId }}</td>
                    <td v-if="itemIndex === 0" :rowspan="client.items.length">{{ client.clientName }}</td>
                    <td>{{ item.department }}</td>
                    <td>{{ item.doctor }}</td>
                    <td>{{ item.itemType }}</td>
                    <td>
                      <span v-if="shouldShowCategories">{{ item.category1 }}</span>
                    </td>
                    <td>
                      <span v-if="shouldShowCategories">{{ item.category2 }}</span>
                    </td>
                    <td>
                      <span v-if="shouldShowCategories">{{ item.category3 }}</span>
                    </td>
                    <td>{{ item.category4 }}</td>
                    <td>{{ item.itemName }}</td>
                    <td>{{ item.quantity }}</td>
                    <td class="amount">{{ formatCurrency(item.amount) }}</td>
                    <td>{{ item.developerName }}</td>
                    <td>{{ item.consumeDate }}</td>
                    <td>{{ item.orderType }}</td>
                    <td>{{ item.receptionType }}</td>
                    <td>{{ item.infoSource1 }}</td>
                    <td>{{ item.devPath1 }}</td>
                    <td>{{ item.devPath2 }}</td>
                    <td>{{ item.devPath3 }}</td>
                    <td>{{ item.firstVisitDate }}</td>
                  </tr>
                </template>
              </template>
              <tr class="subtotal-row">
                <td :colspan="11" class="subtotal-label">小计：</td>
                <td>{{ group.totalQuantity }}</td>
                <td class="amount">{{ formatCurrency(group.totalAmount) }}</td>
                <td :colspan="9"></td>
              </tr>
            </template>
            <tr class="total-row">
              <td :colspan="11" class="total-label">合计：</td>
              <td>{{ totalQuantity }}</td>
              <td class="amount">{{ formatCurrency(totalAmount) }}</td>
              <td :colspan="9"></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="footer-info">
      <span>共 {{ loading ? '...' : totalItems }} 条记录</span>
      <span class="date-limit-info">* 查询限制为一个月内的数据</span>
    </div>
  </div>
  <project-list-picker :value="projectFilter" :visible="projectModalVisible" :width="projectModalWidth" :current-category="projectCategories" @close="closeProjectModal()" @change="changeProjects" />
  <dept-picker-modal ref="deptPickerRef" :value="deptIds" :visible="deptVisible" :width="deptPickerWidth" @change="deptChange" @close="closeDeptPicker" />
  <category-picker ref="categoryPickerRef" :value="projectCategories" :visible="categoryPickerVisible" :width="categoryPickerWidth" @close="closeCategoryPicker" @change="changeCategories" @categoryChange="handleCategoryChange" />
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { ElDatePicker, ElSelect, ElOption, ElButton, ElMessage, ElInput, ElLoading } from "element-plus";
import { getBillRecordsWithMemberDetails } from "@/api/bill-record";
import { ProjectType, ConsultStage } from "@/baseData";
import useModal from "@/hooks/useModal";
import { CheckBoxFilter, baseFilterItem, filterCustomerSource, filterInfoSource } from "@/components/filter-bar";
import { storeToRefs } from "pinia";
import projectListPicker from "@/components/project-picker/project-list-picker.vue";
import deptPickerModal from "@/components/dept-picker-modal/dept-picker-modal.vue";
import categoryPicker from "@/components/project-picker/category-picker.vue";
import { staffStore } from "@/store/staff";
import { teamStore } from "@/store/team"; // 新增导入 teamStore
import { memberStore } from "@/store/member"; // 新增导入 memberStore

const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());
const { currentTeamId, managerList } = storeToRefs(teamStore()); // 获取团队ID和主管列表
const { isAdmin } = storeToRefs(memberStore()); // 获取管理员状态
const { judgmentIsAdmin } = memberStore();
// 格式化显示的日期范围
const formatDateRange = computed(() => {
  if (dateRange.value && dateRange.value.length === 2) {
    return `${dateRange.value[0]} 至 ${dateRange.value[1]}`;
  }
  return "全部日期";
});

// 日期类型（消费日期/咨询日期）
const dateType = ref("consume");
// 日期筛选
const dateRange = ref([]);
const clientIdFilter = ref("");
const clientNameFilter = ref("");
const phoneFilter = ref(""); // 添加手机号筛选变量
const receptionStatusFilter = ref("");
const itemNameFilter = ref("");
const departmentFilter = ref("");
const itemTypeFilter = ref("");
const infoSourceFilter = ref([]); // 添加信息来源筛选变量
const devPathFilter = ref([]); // 添加开发渠道筛选变量
const projectCategories = ref([]); // 咨询师筛选变量
const consultStageFilter = ref([]); // 添加接诊状态筛选变量
const categoryPickerRef = ref(null);
// 项目筛选相关变量
const projectFilter = ref([]);
const currentCategory = ref({}); // 添加当前分类变量
const { close: closeProjectModal, show: showProjectModal, visible: projectModalVisible, width: projectModalWidth } = useModal(800);
const { close: closeDeptPicker, show: showDeptPicker, visible: deptVisible, width: deptPickerWidth } = useModal(640);
const { close: closeCategoryPicker, show: showCategoryPicker, visible: categoryPickerVisible, width: categoryPickerWidth } = useModal(600);
const projectText = computed(() => {
  if (projectFilter.value.length === 0) return "";
  if (projectFilter.value.length > 1) return `已选择${projectFilter.value.length}个项目`;
  return projectFilter.value[0].projectName;
});
const categoryText = computed(() => {
  if (projectCategories.value.length === 0) return "";
  if (projectCategories.value.length > 1) return `已选择${projectCategories.value.length}个分类`;
  return projectCategories.value[0].name;
});
function clearProjects() {
  projectFilter.value = [];
}

// 修改清除分类函数，增加关联逻辑
function clearCategories() {
  projectCategories.value = [];
  categoryPickerRef.value.clear();
}

// 修改处理项目分类变更函数，增加关联逻辑
function changeCategories(data) {
  // 将分类数据处理为包含级别信息的数组
  projectCategories.value = data.categories;
  console.log("项目分类数据", projectCategories.value);
  // 当分类变更时，清空已选项目
  projectFilter.value = [];
}

// 修改处理分类变化的函数
function handleCategoryChange(category) {
  currentCategory.value = category;

  // 当前分类变化时，清空已选项目
  projectFilter.value = [];
}

function changeProjects(data) {
  projectFilter.value = data.projects;
  closeProjectModal();
}

// 处理手机号输入，限制只能输入数字
const handlePhoneFilterInput = (val) => {
  // 只保留数字
  phoneFilter.value = val.replace(/[^\d]/g, "");
};

const isManager = computed(() => {
  return managerList.value.includes(90001) || isAdmin.value;
});

// 优化咨询师列表计算属性，根据权限筛选可见咨询师
const consultantList = computed(() => {
  const allConsultants = staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("consultant"));
  return allConsultants.map((i) => ({ value: i.userid }));
});

const selectedConsultants = ref([]);
const hasExplicitlyClearedConsultants = ref(false);

// consultantFilter 默认值逻辑调整为 computed
const consultantFilter = computed({
  get() {
    if (selectedConsultants.value.length > 0) {
      return selectedConsultants.value;
    }
    if (hasExplicitlyClearedConsultants.value) {
      return [];
    }
    const currentUserId = localStorage.getItem("userId");
    if (!isManager.value) {
      // 不是主管，只能看到自己
      return [currentUserId];
    } else {
      // 主管
      const inList = consultantList.value.some((i) => i.value === currentUserId);
      if (inList) {
        return [currentUserId];
      } else {
        // 展示全部
        return [];
      }
    }
  },
  set(val) {
    selectedConsultants.value = val;
    hasExplicitlyClearedConsultants.value = false;
  },
});

// 处理咨询师筛选器的清除事件
function handleConsultantFilterClear() {
  hasExplicitlyClearedConsultants.value = true;
  selectedConsultants.value = [];
}

// 监听团队ID变化，重新获取数据
watch(
  () => currentTeamId.value,
  () => {
    if (dateRange.value && dateRange.value.length === 2) {
      handleGetBillRecordsWithMemberInfo();
    }
  }
);

/* ===============  科室筛选  start ================= */
const deptFilter = ref([]);
const deptIds = computed(() => deptFilter.value.map((i) => i._id));
const deptText = computed(() => {
  if (deptFilter.value.length === 0) return "";
  if (deptFilter.value.length > 1) return `已选择${deptFilter.value.length}个项目`;
  return deptFilter.value[0].deptName;
});
function deptChange(val) {
  deptFilter.value = val;
  closeDeptPicker();
}
function clearDepts() {
  deptFilter.value = [];
}

// 日期快捷选项
const dateShortcuts = [
  {
    text: "今天",
    value: () => {
      const today = new Date();
      return [today, today];
    },
  },
  {
    text: "昨天",
    value: () => {
      const today = new Date();
      const yesterday = new Date();
      yesterday.setDate(today.getDate() - 1);
      return [yesterday, yesterday];
    },
  },
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近半个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
];

// 原始数据
const rawData = ref([]);
// 添加 loading 状态
const loading = ref(false);

async function handleGetBillRecordsWithMemberInfo() {
  loading.value = true;
  try {
    // 构建请求参数，根据权限添加不同的筛选条件
    const params = {
      payDate: dateRange.value,
    };
    // 咨询师筛选逻辑
    if (isManager.value) {
      params.consultantFilter = consultantFilter.value.length > 0 ? consultantFilter.value : undefined;
    } else {
      params.consultantFilter = [localStorage.getItem("userId")];
    }
    if (consultStageFilter.value.length > 0) {
      params.consultStages = consultStageFilter.value;
    }

    let { data, success, message } = await getBillRecordsWithMemberDetails(params);
    if (success) {
      rawData.value = transformBillRecords(data.data);
    } else {
      ElMessage.error(message);
    }
  } catch (error) {
    ElMessage.error('获取数据失败');
    console.error('获取数据失败:', error);
  } finally {
    loading.value = false;
  }
}

// 转换账单记录数据为表格需要的格式
function transformBillRecords(records) {
  if (!records || !Array.isArray(records)) {
    return [];
  }
  return records.map((record) => {
    // 从customerSource数组中获取信息来源
    const devPath1 = record.customerSource && record.customerSource.length > 0 ? record.customerSource[0] : "-";
    const devPath2 = record.customerSource && record.customerSource.length > 1 ? record.customerSource[1] : "-";
    const devPath3 = record.customerSource && record.customerSource.length > 2 ? record.customerSource[2] : "-";
    // 使用新的信息来源处理方式
    const infoSource1 = record.consultSource && Array.isArray(record.consultSource) ? record.consultSource.join("、") : record.consultSource || "-";
    // 使用billdCreator作为咨询人员
    const consultant = record.counselorUserId || "-";
    // 使用billDoctorId作为开单医生
    const doctor = record.billDoctorId || "-";
    // 根据医生ID查找对应的中文名称
    const doctorName = staffList.value.find((staff) => staff.userid === doctor)?.anotherName || doctor;
    // 从introducerRecord获取开发人员信息（最后一个记录的introducer）
    let developer = record.introducerUserId;
    // 获取项目类别（根据 level 分别获取 1-4 级类别的 label）
    let category1 = "-";
    let category2 = "-";
    let category3 = "-";
    let category4 = "-";
    // 如果存在项目类别数据
    if (record.projectCates && Array.isArray(record.projectCates)) {
      // 根据 level 找到对应级别的类别标签
      const cat1 = record.projectCates.find((cat) => cat.level === 1);
      const cat2 = record.projectCates.find((cat) => cat.level === 2);
      const cat3 = record.projectCates.find((cat) => cat.level === 3);
      const cat4 = record.projectCates.find((cat) => cat.level === 4);
      category1 = cat1 ? cat1.label : "-";
      category2 = cat2 ? cat2.label : "-";
      category3 = cat3 ? cat3.label : "-";
      category4 = cat4 ? cat4.label : "-";
    }
    // 将 consultStage 值转换为对应的标签
    const consultStageLabel = record.consultStage ? ConsultStage.find((stage) => stage.value === record.consultStage)?.label || record.consultStage : "普通";
    
    // 获取初诊日期，从inHospitalTimes[0]获取
    const firstVisitDate = record.inHospitalTimes && Array.isArray(record.inHospitalTimes) && record.inHospitalTimes.length > 0 
      ? formatDateFromTimestamp(record.inHospitalTimes[0]) 
      : "-";
    
    // 创建基础项目信息
    return {
      consultant,
      consultantName: staffList.value.find((staff) => staff.userid === consultant)?.anotherName || "",
      clientId: record.customerNumber || "-",
      clientName: record.customerName || "-",
      clientPhone: record.customerPhone,
      clientPhone1: record.customerPhone1,
      clientPhone2: record.clientPhone2,
      clientPhone3: record.clientPhone3, // 实际项目中应从记录中获取
      department: record.billDeptName, //
      doctor: doctor,
      doctorName: doctorName, // 添加医生中文名称
      itemType: record.projectType || "-",
      // 使用上面获取的项目类别标签
      category1: category1,
      category2: category2,
      category3: category3,
      category4: category4,
      itemName: record.projectName || "-",
      quantity: record.usageCount || 0, 
      amount: record.totalPrice || 0,
      developer: developer,
      developerName: staffList.value.find((staff) => staff.userid === developer)?.anotherName || "",
      consumeDate: formatDateFromTimestamp(record.consumeTime || record.createTime),
      consultDate: formatDateFromTimestamp(record.createTime),
      orderType: record.billType || "-", // 确保使用billType字段作为订单类型
      receptionType: consultStageLabel, // 使用转换后的咨询阶段标
      infoSource1: infoSource1,
      devPath1: devPath1,
      devPath2: devPath2,
      devPath3: devPath3,
      firstVisitDate: firstVisitDate,
    };
  });
}

// 格式化时间戳为日期字符串 YYYY-MM-DD
function formatDateFromTimestamp(timestamp) {
  if (!timestamp) return "-";

  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");

  return `${year}-${month}-${day}`;
}

// 禁用超过一个月范围的日期
const disabledDate = (time) => {
  if (!dateRange.value || dateRange.value.length === 0) {
    return false;
  }

  const startDate = new Date(dateRange.value[0]);
  const endDate = new Date(dateRange.value[1]);
  const currentDate = new Date(time);

  // 如果已选择了开始日期，限制结束日期不能超过开始日期后的31天
  if (dateRange.value.length === 1) {
    const oneMonthLater = new Date(startDate);
    oneMonthLater.setDate(startDate.getDate() + 31);
    return currentDate > oneMonthLater;
  }

  return false;
};

// 监听日期变化，确保不超过一个月
watch(dateRange, (newValue) => {
  if (newValue && newValue.length === 2) {
    const start = new Date(newValue[0]);
    const end = new Date(newValue[1]);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays > 31) {
      // 如果超过31天，将结束日期设置为开始日期后的31天
      const maxEnd = new Date(start);
      maxEnd.setDate(start.getDate() + 31);
      const formattedMaxEnd = formatDate(maxEnd);

      dateRange.value = [newValue[0], formattedMaxEnd];
      ElMessage.warning("查询范围已限制为一个月内");
    }
  }
});

// 咨询师接诊状态选项
const receptionStatusOptions = [
  { value: "全部", label: "" },
  { value: "已接诊", label: "已接诊" },
  { value: "未接诊", label: "未接诊" },
];

// 格式化日期为YYYY-MM-DD
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  return `${year}-${month}-${day}`;
};

// 过滤数据
const filteredData = computed(() => {
  let result = [...rawData.value];

  // 按客户ID筛选
  if (clientIdFilter.value) {
    result = result.filter((item) => item.clientId.includes(clientIdFilter.value));
  }

  // 按客户姓名筛选
  if (clientNameFilter.value) {
    result = result.filter((item) => item.clientName.includes(clientNameFilter.value));
  }
  // 按手机号筛选
  if (phoneFilter.value) {
    result = result.filter((item) => {
      // 获取待匹配的手机号数组
      const phones = [item.clientPhone, item.clientPhone1, item.clientPhone2, item.clientPhone3].filter(Boolean); // 过滤掉空值
      console.log("phones:", phones);
      // 如果输入是后四位（长度小于等于4）
      if (phoneFilter.value.length === 4) {
        return phones.some((phone) => phone && phone.endsWith(phoneFilter.value));
      } else if (phoneFilter.value.length === 11) {
        return phones.some((phone) => phone && phone.includes(phoneFilter.value));
      }
    });
  }

  // 按咨询师接诊状态筛选
  if (receptionStatusFilter.value && receptionStatusFilter.value !== "全部") {
    result = result.filter((item) => item.receptionType === receptionStatusFilter.value);
  }

  // 按开单项目名称筛选
  if (itemNameFilter.value) {
    result = result.filter((item) => item.itemName.includes(itemNameFilter.value));
  }

  // 按开单科室筛选
  if (departmentFilter.value && departmentFilter.value !== "全部") {
    result = result.filter((item) => item.department === departmentFilter.value);
  }

  // 按项目类型筛选
  if (itemTypeFilter.value && itemTypeFilter.value !== "全部") {
    result = result.filter((item) => item.itemType === itemTypeFilter.value);
  }

  // 按日期筛选
  if (dateRange.value && dateRange.value.length === 2) {
    const targetDateField = "consumeDate"; // 使用购买日期进行筛选
    // 实际项目中这里需要根据日期格式进行筛选
    // 这里只是示例，没有实际筛选
    console.log("日期范围:", dateRange.value, "筛选字段:", targetDateField);
  }

  // 按开单项目筛选
  if (projectFilter.value && projectFilter.value.length > 0) {
    result = result.filter((item) => {
      return projectFilter.value.some((p) => p.projectName === item.itemName);
    });
  }

  // 按开单科室筛选
  if (deptFilter.value && deptFilter.value.length > 0) {
    result = result.filter((item) => {
      return deptFilter.value.some((d) => d.deptName === item.department);
    });
  }

  // 按信息来源筛选
  if (infoSourceFilter.value && infoSourceFilter.value.length > 0) {
    result = result.filter((item) => {
      // 检查合并后的信息来源字符串是否包含任一筛选值
      return infoSourceFilter.value.some((filter) => item.infoSource1 && item.infoSource1.includes(filter));
    });
  }

  // 按开发渠道筛选
  if (devPathFilter.value && devPathFilter.value.length > 0) {
    result = result.filter((item) => {
      const paths = [item.devPath1, item.devPath2, item.devPath3].filter(Boolean);
      return paths.some((path) => devPathFilter.value.some((filter) => path === filter));
    });
  }

  // 按项目分类筛选
  if (projectCategories.value && projectCategories.value.length > 0) {
    let categoryObj = groupLabelsByLevel(projectCategories.value);
    result = result.filter((item) => {
      // 如果所有分类都是"-"，则不匹配
      if (item.category1 === "-" && item.category2 === "-" && item.category3 === "-" && item.category4 === "-") {
        return false;
      }

      // 只要任一选中的分类级别匹配即可
      let hasMatch = false;

      if (categoryObj[1] && categoryObj[1].length > 0) {
        if (item.category1 && item.category1 !== "-" && categoryObj[1].includes(item.category1)) {
          hasMatch = true;
        }
      }

      if (categoryObj[2] && categoryObj[2].length > 0) {
        if (item.category2 && item.category2 !== "-" && categoryObj[2].includes(item.category2)) {
          hasMatch = true;
        }
      }

      if (categoryObj[3] && categoryObj[3].length > 0) {
        if (item.category3 && item.category3 !== "-" && categoryObj[3].includes(item.category3)) {
          hasMatch = true;
        }
      }

      if (categoryObj[4] && categoryObj[4].length > 0) {
        if (item.category4 && item.category4 !== "-" && categoryObj[4].includes(item.category4)) {
          hasMatch = true;
        }
      }

      return hasMatch;
    });
  }

  return result;
});

function groupLabelsByLevel(data) {
  return data.reduce((acc, item) => {
    const level = item.level;
    if (!acc[level]) {
      acc[level] = [];
    }
    acc[level].push(item.label);
    return acc;
  }, {});
}

// 分组数据，现在按咨询人员分组
const groupedData = computed(() => {
  const groups = {};
  // 按咨询人员分组
  filteredData.value.forEach((item) => {
    if (!groups[item.consultant]) {
      groups[item.consultant] = {
        consultant: item.consultant,
        consultantName: item.consultantName || item.consultant,
        clients: {},
        totalQuantity: 0,
        totalAmount: 0,
      };
    }
    // 按客户ID分组
    if (!groups[item.consultant].clients[item.clientId]) {
      groups[item.consultant].clients[item.clientId] = {
        clientId: item.clientId,
        clientName: item.clientName,
        items: [],
      };
    }
    // 添加项目
    groups[item.consultant].clients[item.clientId].items.push({
      department: item.department,
      doctor: item.doctorName,
      itemType: item.itemType,
      category1: item.category1,
      category2: item.category2,
      category3: item.category3,
      category4: item.category4,
      itemName: item.itemName,
      quantity: item.quantity,
      amount: item.amount,
      consultant: item.consultant,
      consultantName: item.consultantName || item.consultant,
      consumeDate: item.consumeDate,
      consultDate: item.consultDate,
      orderType: item.orderType,
      receptionType: item.receptionType,
      infoSource1: item.infoSource1,
      devPath1: item.devPath1,
      devPath2: item.devPath2,
      devPath3: item.devPath3,
      firstVisitDate: item.firstVisitDate,
      clientPhone: item.clientPhone,
      developer: item.developer,
      developerName: item.developerName || item.developer,
    });
    // 累加总数量和总金额
    groups[item.consultant].totalQuantity += item.quantity;
    groups[item.consultant].totalAmount += item.amount;
  });

  // 转换为数组格式，并计算每个咨询人员的行数
  const result = Object.values(groups).map((group) => {
    const clients = Object.values(group.clients);
    let rowspan = 0;

    clients.forEach((client) => {
      rowspan += client.items.length;
    });

    return {
      consultant: group.consultant,
      consultantName: group.consultantName,
      clients: clients,
      totalQuantity: group.totalQuantity,
      totalAmount: group.totalAmount,
      rowspan: rowspan,
    };
  });

  return result;
});

// 计算总数量和总金额
const totalQuantity = computed(() => {
  return groupedData.value.reduce((sum, group) => sum + group.totalQuantity, 0);
});

const totalAmount = computed(() => {
  return groupedData.value.reduce((sum, group) => sum + group.totalAmount, 0);
});

// 计算总记录数
const totalItems = computed(() => {
  return filteredData.value.length;
});

// 格式化金额
// 格式化货币
const formatCurrency = (value) => {
  if (value === undefined || value === null || isNaN(value)) {
    return "¥0.00";
  }
  return new Intl.NumberFormat("zh-CN", {
    style: "currency",
    currency: "CNY",
    minimumFractionDigits: 2,
  }).format(value);
};
// 处理日期类型变化
const handleDateTypeChange = () => {
  console.log("日期类型变更为:", dateType.value);
  // 可以根据需要在这里执行其他操作
};

// 处理日期变化
const handleDateChange = (val) => {
  if (val && val.length === 2) {
    const start = new Date(val[0]);
    const end = new Date(val[1]);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays > 31) {
      ElMessage.warning("查询范围已限制为一个月内");
      return;
    }
    // 直接调用接口获取数据
    handleGetBillRecordsWithMemberInfo();
  }
};

// 处理查询
const handleSearch = () => {
  if (dateRange.value && dateRange.value.length === 2) {
    const start = new Date(dateRange.value[0]);
    const end = new Date(dateRange.value[1]);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    if (diffDays > 31) {
      ElMessage.warning("查询范围已限制为一个月内");
      return;
    }
    handleGetBillRecordsWithMemberInfo();
  } else {
    ElMessage.warning("请选择日期范围");
  }
};

// 重置筛选条件
const resetFilters = () => {
  // 重置所有筛选条件为默认值
  dateType.value = "consume";

  // 设置为当天日期
  const today = new Date();
  const formattedToday = formatDate(today);
  dateRange.value = [formattedToday, formattedToday];

  clientIdFilter.value = "";
  clientNameFilter.value = "";
  phoneFilter.value = ""; // 重置手机号筛选
  receptionStatusFilter.value = "";
  itemNameFilter.value = "";
  departmentFilter.value = "";
  itemTypeFilter.value = "";
  infoSourceFilter.value = []; // 重置信息来源筛选
  devPathFilter.value = []; // 重置开发渠道筛选
  consultStageFilter.value = []; // 重置接诊状态筛选

  // 根据权限设置咨询师筛选的默认值
  selectedConsultants.value = [];

  projectCategories.value = []; // 重置项目分类筛选
  projectFilter.value = [];
  deptFilter.value = [];
  categoryPickerRef.value.clear();

  ElMessage.success("筛选条件已重置");
};

// 导出Excel
const exportToExcel = () => {
  // 准备导出数据
  const exportData = [];

  // 添加表头 - 根据shouldShowCategories决定是否包括项目类别1、2、3
  const headers = ["咨询人员", "客户门诊ID", "客户姓名", "开单科室", "开单医生", "项目类型"];

  if (shouldShowCategories.value) {
    headers.push("项目类别1", "项目类别2", "项目类别3");
  }

  headers.push("项目类别4", "项目名称", "购买数量", "金额", "开发人员", "消费日期", "订单类型", "接诊类型", "信息来源", "开发渠道1", "开发渠道2", "开发渠道3", "初诊日期");

  exportData.push(headers);

  // 添加数据行
  groupedData.value.forEach((group) => {
    group.clients.forEach((client) => {
      client.items.forEach((item) => {
        const rowData = [group.consultantName, client.clientId, client.clientName, item.department, item.doctor, item.itemType];

        if (shouldShowCategories.value) {
          rowData.push(item.category1, item.category2, item.category3);
        }

        rowData.push(item.category4, item.itemName, item.quantity, item.amount, item.developerName, item.consumeDate, item.orderType, item.receptionType, item.infoSource1, item.devPath1, item.devPath2, item.devPath3, item.firstVisitDate);

        exportData.push(rowData);
      });
    });

    // 添加小计行 - 注意需要调整空单元格的数量
    const subtotalRow = ["小计:"];
    const emptyColsCount = 10; // 根据是否显示类别调整空列数
    for (let i = 0; i < emptyColsCount; i++) {
      subtotalRow.push("");
    }
    subtotalRow.push(group.totalQuantity, group.totalAmount);
    // 添加剩余的空列
    const remainingEmptyCols = headers.length - subtotalRow.length;
    for (let i = 0; i < remainingEmptyCols; i++) {
      subtotalRow.push("");
    }
    exportData.push(subtotalRow);
  });

  // 添加总计行 - 与小计行类似，需要调整空单元格
  const totalRow = ["合计:"];
  const emptyColsCount = 9;
  for (let i = 0; i < emptyColsCount; i++) {
    totalRow.push("");
  }
  totalRow.push("", totalQuantity.value, totalAmount.value); // 添加一个空白单元格，然后是数量和金额
  // 添加剩余的空列
  const remainingEmptyCols = headers.length - totalRow.length;
  for (let i = 0; i < remainingEmptyCols; i++) {
    totalRow.push("");
  }
  exportData.push(totalRow);

  // 创建工作簿和工作表
  const wb = XLSX.utils.book_new();
  const ws = XLSX.utils.aoa_to_sheet(exportData);

  // 设置列宽
  const colWidths = [
    { wch: 10 }, // 咨询人员
    { wch: 12 }, // 客户门诊ID
    { wch: 10 }, // 客户姓名
    { wch: 12 }, // 开单科室
    { wch: 10 }, // 开单医生
    { wch: 12 }, // 项目类型
    { wch: 12 }, // 项目类别1
    { wch: 12 }, // 项目类别2
    { wch: 12 }, // 项目类别3
    { wch: 15 }, // 项目类别4
    { wch: 20 }, // 项目名称
    { wch: 10 }, // 购买数量
    { wch: 12 }, // 金额
    { wch: 12 }, // 开发人员
    { wch: 12 }, // 消费日期
    { wch: 10 }, // 订单类型
    { wch: 10 }, // 接诊状态
    { wch: 12 }, // 信息来源
    { wch: 12 }, // 开发渠道1
    { wch: 12 }, // 开发渠道2
    { wch: 12 }, // 开发渠道3
    { wch: 12 }, // 初诊日期
  ];

  ws["!cols"] = colWidths;

  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(wb, ws, "咨询消费明细表");

  // 生成Excel文件并下载
  const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
  const blob = new Blob([excelBuffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });

  // 使用当前日期作为文件名的一部分
  const now = new Date();
  const fileName = `咨询消费明细表_${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}.xlsx`;

  saveAs(blob, fileName);
  ElMessage.success("导出成功");
};

// 打印表格功能
const printTable = () => {
  window.print();
};

onMounted(async () => {
  await judgmentIsAdmin();
  // 初始化数据
  if (staffList.value.length === 0) await getStaffList();

  const end = new Date();
  const start = new Date(end.getFullYear(), end.getMonth(), 1);

  dateRange.value = [formatDate(start), formatDate(end)];

  handleGetBillRecordsWithMemberInfo();
});

watch(
  [consultantFilter, projectFilter, deptFilter, projectCategories, consultStageFilter],
  () => {
    // 当筛选条件变化时自动触发查询
    handleGetBillRecordsWithMemberInfo();
  },
  { deep: true }
);

// 添加一个计算属性，决定是否显示项目类别1、2、3
const shouldShowCategories = computed(() => {
  return rawData.value.some((item) => item.category1 === item.department);
});
</script>
  
<style scoped>
.report-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
  background-color: #f5f7fa;
  overflow: hidden;
  min-width: 1400px; /* 增加最小宽度以适应更多列 */
}

.report-header {
  background-color: white;
  padding: 15px 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
}

.report-title {
  text-align: center;
  font-weight: bold;
  font-size: 20px;
  margin-bottom: 5px;
  color: #303133;
}

.report-date {
  text-align: center;
  font-size: 14px;
  margin-bottom: 15px;
  color: #606266;
}

.filter-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-label {
  white-space: nowrap;
  margin-right: 5px;
  font-size: 14px;
  color: #606266;
}

.filter-actions {
  margin-left: auto;
  display: flex;
  gap: 10px;
}

.date-range {
  min-width: 320px;
}

/* 保留原有样式 */
.table-wrapper {
  flex: 1;
  margin: 0 15px;
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 300px;
  overflow: hidden; /* 确保容器不会产生额外的滚动条 */
}

.table-container {
  flex: 1;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: auto;
  height: 100%;
  position: relative;
  min-width: 100%;
  /* 确保水平滚动条正确显示 */
  overflow-x: auto;
  overflow-y: auto;
}

.medical-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  table-layout: auto; /* 改为auto，让表格根据内容自动调整列宽 */
  min-width: 1700px;
}

.medical-table th,
.medical-table td {
  border: 1px solid #ebeef5;
  padding: 8px 4px;
  text-align: center;
  min-height: 40px; /* 改为最小高度，允许内容撑开 */
  height: auto; /* 允许自动调整高度 */
  overflow: hidden; /* 隐藏溢出内容 */
  text-overflow: ellipsis; /* 显示省略号 */
  white-space: nowrap; /* 防止换行 */
  box-sizing: border-box;
  vertical-align: middle; /* 垂直居中对齐 */
}

.medical-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 10;
  white-space: nowrap; /* 防止表头换行 */
  overflow: hidden; /* 隐藏溢出内容 */
  text-overflow: ellipsis; /* 显示省略号 */
}

.medical-table .amount {
  text-align: right;
  padding-right: 10px;
  min-width: 100px;
  max-width: none; /* 移除最大宽度限制 */
  white-space: nowrap; /* 防止换行 */
  overflow: hidden; /* 隐藏溢出内容 */
  text-overflow: ellipsis; /* 显示省略号 */
}

.subtotal-row {
  background-color: #f5f7fa;
  font-weight: bold;
}

.total-row {
  background-color: #ebeef5;
  font-weight: bold;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.subtotal-label,
.total-label {
  text-align: right !important;
  padding-right: 10px;
}

.footer-info {
  background-color: white;
  padding: 10px 15px;
  margin: 0 15px 10px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  color: #606266;
  font-size: 14px;
}

/* 移动日期限制信息到最底部的单独区域 */
.date-limit-notice {
  background-color: white;
  padding: 8px 15px;
  margin: 0 15px 15px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  text-align: center;
}

.date-limit-info {
  color: #e6a23c;
  font-style: italic;
  font-size: 14px;
}

@media print {
  .report-container {
    background-color: white;
  }

  .report-header {
    box-shadow: none;
  }

  .report-actions,
  .footer-info {
    display: none;
  }

  .table-container {
    margin: 0;
    box-shadow: none;
    overflow: visible;
  }

  .medical-table th,
  .medical-table td {
    font-size: 12px;
    padding: 4px 2px;
  }
}

@media (max-width: 768px) {
  .filter-section {
    gap: 15px;
  }

  .filter-row {
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
  }

  .filter-item {
    width: 100%;
  }

  .filter-actions {
    margin-left: 0;
    width: 100%;
    justify-content: flex-end;
  }
}

/* 添加一个媒体查询，在大屏幕上提供更多空间 */
@media (min-width: 1600px) {
  .medical-table .amount {
    min-width: 120px;
    max-width: 150px;
  }
}

/* 为特定列设置最小宽度，确保内容完整显示 */
.medical-table th:nth-child(11), /* 项目名称列 */
.medical-table td:nth-child(11) {
  min-width: 150px;
  max-width: none;
}

.medical-table th:nth-child(13), /* 金额列 */
.medical-table td:nth-child(13) {
  min-width: 120px;
  max-width: none;
}

.medical-table th:nth-child(18), /* 信息来源列 */
.medical-table td:nth-child(18) {
  min-width: 120px;
  max-width: none;
}

.medical-table th:nth-child(21), /* 初诊日期列 */
.medical-table td:nth-child(21) {
  min-width: 120px;
  max-width: none;
}

/* 添加媒体查询处理表格在小屏幕上的显示 */
@media (max-width: 1600px) {
  .report-container {
    min-width: 100%;
  }
  
  .table-container {
    overflow-x: auto;
  }
  
  .medical-table {
    min-width: 1700px; /* 保持表格最小宽度 */
  }
}

/* 确保表格在打印时也能正确显示 */
@media print {
  .medical-table {
    min-width: auto; /* 打印时允许表格自适应 */
    width: 100%;
  }
  
  .table-container {
    overflow: visible;
  }
}
</style>