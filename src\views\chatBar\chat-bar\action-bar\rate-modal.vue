<template>
  <el-dialog :model-value="visible" :width="width" title="提示" @close="close">
    <div class="p-15px">
      <span v-if="type === 'noService'">今日没有服务记录，无法发送服务评价。如果今日有输出服务，可填写服务记录后再发送评价邀请。</span>
      <span v-else>客户已对您今日服务做出评价，可前往“工作记录”查看。</span>
    </div>
    <template #footer>
      <div text-center>
        <el-button v-if="type === 'noService'" plain type="primary" class="w-100px" @click="add()">写服务记录</el-button>
        <el-button type="primary" class="w-100px" @click="close()">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>

const props = defineProps({
  visible: { type: Boolean, default: false },
  type: { type: String, default: 'noService' },
  width: { type: Number, default: 520 }
})

const emits = defineEmits(['close', 'addService']);
function close() {
  emits('close')
}

function add() {
  emits('addService')
  close()
}

</script>
<style lang="scss" scoped>
.customer-item:hover {
  background: #FFFAFA;
}
</style>
