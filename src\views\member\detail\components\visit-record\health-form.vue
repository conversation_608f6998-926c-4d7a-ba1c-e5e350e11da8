<template>
  <div v-if="list.length !== 0" style="border-bottom: 10px solid #eee">
    <div>
      <info-card title="健康指标" :border="false">
        <el-form label-position="left" label-suffix="：">
          <el-row>
            <el-col v-for="i in list" :key="i.title">
              <el-form-item class="el-form-item--autoheight" :label="i.name">
                {{ i.value }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </info-card>
    </div>
    <div class="flex items-center justify-center pb-10px text-center text-14px cursor-pointer" color-primary @click="expand = !expand">
      <el-icon v-if="expand">
        <arrow-up />
      </el-icon>
      <el-icon v-else>
        <arrow-down />
      </el-icon>
      <span class="ml-6px">{{ expand ? "收起" : "展开" }}</span>
    </div>
  </div>
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { templateStore } from "@/store/template";
import InfoCard from "../info-card";
import ShowCell from "@/components/form-cell/show-cell/index.vue";
import { getHealthIndicators } from "@/api/member";
const spanConfig = {
  bloodPressure: 24,
};
const props = defineProps({
  customer: { type: Object, default: () => ({}) },
});

const list = ref([]);
watch(
  () => props.customer,
  (val) => {
    if (!val) return;
    getHealthIndicatorsApi();
  },
  { immediate: true }
);
// const data = computed(() => {
//   const data = { ...props.customer };
//   if (Array.isArray(data.surgicalHistory)) {
//     data.surgicalHistory = data.surgicalHistory.filter((i) => i !== "有");
//   }
//   return data;
// });
// const { corpTemplateList } = storeToRefs(templateStore());
// const tempList = computed(() => {
//   const temp = corpTemplateList.value && Array.isArray(corpTemplateList.value) ? corpTemplateList.value.find((i) => i.templateType === "healthTemplate") : null;
//   return temp && Array.isArray(temp.templateList) ? temp.templateList.filter((item) => item.fieldStatus !== "disable") : [];
// });
// const expand = ref(false);

async function getHealthIndicatorsApi() {
  const { data } = await getHealthIndicators({ memberId: props.customer._id });
  list.value = data.list;
}
</script>
<style lang="scss" scoped></style>
