<template>
  <el-dialog :model-value="visible" :close-on-click-modal="false" :width="width" :height="height" title="手机号重复" @close="close">
    <div class="max-h-300px overflow-auto">
      <div class="py-10px" v-for="item in customers" :key="item._id" border-bottom>
        <div class="flex justify-between">
          <div>
            <div class="flex">
              <div>客户姓名：</div>
              <div>{{ item.name }}</div>
            </div>
            <div class="flex pt-5px">
              <div>性别/年龄：</div>
              <div v-if="item.idCard">{{ validate.getAge(item.idCard) }}/{{ validate.getGender(item.idCard) }}</div>
            </div>
          </div>
          <div></div>
        </div>
        <div class="flex pt-5px">
          <div>手机号码：</div>
          <div>{{ item.mobile }}</div>
        </div>
        <div class="flex pt-5px" v-if="item.phone1">
          <div>联系电话1：</div>
          <div>{{ item.phone1 }}</div>
        </div>
        <div class="flex pt-5px" v-if="item.phone2">
          <div>联系电话2：</div>
          <div>{{ item.phone2 }}</div>
        </div>
        <div class="flex pt-5px" v-if="item.phone3">
          <div>联系电话3：</div>
          <div>{{ item.phone3 }}</div>
        </div>
        <div class="flex pt-5px" v-if="getTeamNames(item)">
          <div>服务团队：</div>
          <div>{{ getTeamNames(item) }}</div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
<script setup>
import validate from "@/utils/validate";
import { teamStore } from "@/store/team";
const emits = defineEmits(["close"]);
const { allTeams } = teamStore();
const props = defineProps({
  visible: { type: Boolean, defautl: false },
  customers: { type: Object, default: {} },
  width: { type: Number, default: 420 },
  height: { type: Number, default: 200 },
});
function close() {
  emits("close");
}
function getTeamNames(item) {
  let teamId = item.teamId;
  if (!teamId) return "";
  if (typeof teamId === "string") teamId = [teamId];
  return allTeams
    .filter((team) => teamId.includes(team.teamId))
    .map((team) => team.name)
    .join("、");
}
</script>
