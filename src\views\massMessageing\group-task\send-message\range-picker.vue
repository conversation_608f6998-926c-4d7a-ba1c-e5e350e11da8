<template>
  <el-dialog :model-value="visible" :width="width" title="选择发送范围" @close="close">
    <el-form class="px-10px" label-width="110px" label-position="left" label-suffix="：">
      <el-form-item class="pt-10px border-gray-100" label="执行团队" v-if="sendTaskType === 'TEAM'">
        <div class="flex-grow w-0 truncate h-2em">{{ teamStr }}</div>
      </el-form-item>
      <el-form-item label="通知客户">
        <el-radio-group v-model="form.range">
          <el-radio label="ALL">全部客户</el-radio>
          <el-radio label="SOME">按条件筛选客户</el-radio>
        </el-radio-group>
      </el-form-item>
      <template v-if="form.range === 'SOME'">
        <el-form-item class="pt-10px border-t border-gray-100" label="执行团队" v-if="sendTaskType === 'CORP'">
          <div class="flex-grow flex justify-end items-center px-12px py-4px border cursor-pointer border-gray-200 hover:border-gray-300 rounded-8px" @click="selectTeam()">
            <div class="flex-grow w-0 truncate h-2em text-right">{{ teamStr }}</div>
            <el-icon size="18" class="ml-10px text-gray-400 hover:text-blue-500">
              <ArrowRight />
            </el-icon>
          </div>
        </el-form-item>
        <el-form-item class="pt-10px border-t border-gray-100" label="执行员工">
          <div class="flex-grow flex justify-end items-center px-12px py-4px border cursor-pointer border-gray-200 hover:border-gray-300 rounded-8px" @click="changeMember">
            <div class="flex-grow w-0 truncate h-2em text-right">{{ memberStr }}</div>
            <el-icon size="18" class="ml-10px text-gray-400 hover:text-blue-500">
              <ArrowRight />
            </el-icon>
          </div>
        </el-form-item>
        <el-form-item label="客户标签">
          <div class="flex-grow flex justify-end items-center px-12px py-4px border cursor-pointer border-gray-200 hover:border-gray-300 rounded-8px" @click="showTagModal">
            <div class="flex-grow w-0 truncate h-2em text-right">
              <tag-str v-if="tags.length" :tags="tags" />
            </div>
            <el-icon size="18" class="ml-10px text-gray-400 hover:text-blue-500">
              <ArrowRight />
            </el-icon>
          </div>
        </el-form-item>
      </template>
    </el-form>
    <template #footer>
      <div text-center>
        <el-button type="primary" plain class="w-100px" @click="close()">取消</el-button>
        <el-button class="w-100px" type="primary" @click="confirm()" :loading="loading">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <member-picker :visible="filterVisible" :width="filterWidth" @close="closeFilter" :teamIds="selfTeamIds" @confirm="confirmUser" :memberList="memberList" :sendTaskType="sendTaskType" />
  <tag-modal :value="tags" :visible="tagVisible" :width="tagWidth" @close="closeTag" @change="changeTag($event)" />
  <team-filter :visible="teamVisible" @close="teamVisible = false" :selectTeams="selfTeamIds" @change="changeTeam"></team-filter>
</template>
<script setup>
import { computed, ref, watch } from "vue";
import useModal from "@/hooks/useModal";
import memberPicker from "./member-picker.vue";
import tagModal from "../../components/tag-modal.vue";
import tagStr from "../../components/tag-str.vue";
import { teamStore } from "@/store/team";
import { storeToRefs } from "pinia";
import { getCorpCustomer } from "@/api/member";
import { memberStore } from "@/store/member";
import { ElMessage } from "element-plus";
import teamFilter from "./team-filter.vue";
const teamVisible = ref(false);
const { corpInfo } = memberStore();
const { allTeams, currentTeam } = storeToRefs(teamStore());
const selfTeamIds = ref([]);
const props = defineProps({
  visible: { type: Boolean, default: false },
  width: { type: String, default: "30%" },
  teamIds: { type: Array, default: () => [] },
  userIds: { type: Array, default: () => [] },
  sendTaskType: { type: String, default: "" },
});
const memberList = ref([]);
let customers = ref([]),
  more = false,
  loading = ref(false);
const emits = defineEmits(["close"]);
const form = ref({
  radio: 1,
  range: "ALL",
});
const teamStr = computed(() => {
  return props.sendTaskType === "CORP" ? `已选择${selfTeamIds.value.length}个团队` : currentTeam.value.name;
});
const memberStr = computed(() => {
  if (memberList.value.length) return `已选择${memberList.value.length}位员工`;
  return "";
});

watch(
  () => props.visible,
  () => {
    if (props.sendTaskType === "CORP") {
      selfTeamIds.value = props.teamIds.length > 0 ? props.teamIds : allTeams.value.map((item) => item.teamId);
    } else {
      selfTeamIds.value = [currentTeam.value.teamId];
    }
    memberList.value = props.userIds.length > 0 ? props.userIds : getMemberList();
  }
);
watch(
  () => form.value.range,
  (val) => {
    if (val === "ALL") {
      selfTeamIds.value = props.sendTaskType === "CORP" ? allTeams.value.map((item) => item.teamId) : [currentTeam.value.teamId];
      memberList.value = getMemberList();
    }
  }
);
function selectTeam() {
  if (props.sendTaskType === "TEAM") return;
  teamVisible.value = true;
}
function getMemberList() {
  let list = allTeams.value.filter((i) => selfTeamIds.value.includes(i.teamId));

  let arr = [];
  list.forEach((item) => {
    arr = [...arr, ...item.memberList];
  });
  return Array.from(new Set(arr)).filter((i) => i);
}

function changeTeam(teamIds) {
  // 判断数组内容是否相同
  teamVisible.value = false;
  selfTeamIds.value = teamIds;
  memberList.value = getMemberList();
}
function close() {
  emits("close");
}
const { close: closeFilter, show: showFilter, visible: filterVisible, width: filterWidth } = useModal(640); //  选择员工弹窗
const { close: closeTag, show: showTagModal, visible: tagVisible, width: tagWidth } = useModal(640); //  选择客户弹窗
function confirmUser(list) {
  memberList.value = list;
  closeFilter();
}
function changeMember() {
  if (selfTeamIds.value.length === 0) return ElMessage.info("请选择团队");
  showFilter();
}
const tags = ref([]);
function changeTag(val) {
  tags.value = [...val];
}
// 获取团队下所有客户的userid
async function getAllCustomers(page) {
  // 判断userIds 是在哪几个团队
  let teamIds = selfTeamIds.value;
  if (props.sendTaskType === "CORP") {
    const userTeams = allTeams.value.filter((i) => i.memberList.some((j) => memberList.value.includes(j))).map((i) => i.teamId);
    teamIds = selfTeamIds.value.filter((i) => userTeams.includes(i));
  }
  let params = {
    page,
    pageSize: 100,
    showExternalCustomer: true,
    teamId: teamIds,
    userIds: memberList.value.map((i) => i),
    corpId: localStorage.getItem("corpId"),
    permanent_code: corpInfo.permanent_code,
  };
  if (tags.value.length) params.tagIds = tags.value.map((i) => i.id);
  const { data, success } = await getCorpCustomer(params);
  if (!success) return;
  customers.value = page === 1 ? data.data : [...customers.value, ...data.data];
  more = data.total > customers.value.length;
  // 如果还有数据，继续获取
  if (more) {
    page += 1;
    await getAllCustomers(page);
  }
}
async function confirm() {
  if (selfTeamIds.value.length === 0) {
    return ElMessage.info("请选择团队");
  }
  if (form.value.range === "SOME" && !memberList.value.length) {
    return ElMessage.info("请选择员工");
  }
  loading.value = true;
  await getAllCustomers(1);
  loading.value = false;
  closeFilter();
  // 获取所有成员的userid
  emits("confirm", { customers: customers.value, teamIds: selfTeamIds.value, userIds: memberList.value });
}
</script>
<style lang="scss" scoped></style>
