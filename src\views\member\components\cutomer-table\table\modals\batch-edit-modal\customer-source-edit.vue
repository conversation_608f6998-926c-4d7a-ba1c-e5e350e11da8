<template>
  <el-form-item class="mb-0" label="来源">
    <el-cascader v-model="customerSource" :filter-method="filterMethod" filterable :props="{ value: 'sourceName', label: 'sourceName' }" :options="customerSourceList" placeholder="请选择来源" class="w-full" clearable />
  </el-form-item>
  <el-form-item v-if="referenceType === '客户'" class="mb-0 mt-15px" label="推荐客户">
    <remote-select-customer class="w-full" :value="referenceCustomerId" placeholder="请搜索客户姓名" @change="changeCustomer($event, '客户')" />
  </el-form-item>
  <el-form-item v-else-if="referenceType === '同事'" class="mb-0 mt-15px" label="推荐同事">
    <el-input :placeholder="referenceUserId ? '' : '请选择同事'" readonly class="w-full">
      <template #prefix>
        <span v-if="referenceUserId" class="text-black mr-10px">
          <ww-user :openid="referenceUserId"></ww-user>
        </span>
      </template>
      <template #append>
        <el-text pointer @click="chooseCoWorker()">选择</el-text>
      </template>
    </el-input>
  </el-form-item>
</template>
<script setup>
import { computed, watch } from "vue";
import { storeToRefs } from "pinia";
import { useVModels } from "@vueuse/core";
import { memberStore } from "@/store/member";
import { selectEnterpriseContact } from "@/utils/jssdk";
import RemoteSelectCustomer from "@/components/remote-select-customer/remote-select-customer";

const props = defineProps({
  customerSource: { type: Array, default: () => [] },
  referenceType: { type: String, default: "" },
  reference: { type: String, default: "" },
  referenceCustomerId: { type: String, default: "" },
  referenceUserId: { type: String, default: "" },
});
const emit = defineEmits(["update:customerSource", "update:referenceType", "update:reference", "update:referenceCustomerId", "update:referenceUserId"]);
const { customerSource, referenceType, reference, referenceCustomerId, referenceUserId } = useVModels(props, emit);

const store = memberStore();
const { corpInfo } = storeToRefs(store);
const customerSourceList = computed(() => (Array.from(corpInfo.value.customerSourceList) ? convertData(corpInfo.value.customerSourceList) : []));

function convertData(data) {
  return data.reduce((acc, item) => {
    if (!item.disable) {
      if (item.children && item.children.length > 0) {
        item.children = convertData(item.children);
      }
      acc.push(item);
    }
    return acc;
  }, []);
}

async function chooseCoWorker() {
  const res = await selectEnterpriseContact("single");
  const { selectedUserList = [] } = res || {};
  if (selectedUserList && selectedUserList[0])
    setOtherData({
      referenceType: "同事",
      referenceUserId: selectedUserList[0].id,
    });
}

function changeCustomer(customer = {}) {
  setOtherData({
    referenceType: "客户",
    reference: customer.name || "",
    referenceCustomerId: customer._id || "",
  });
}

function setOtherData(data) {
  referenceType.value = data.referenceType || "";
  reference.value = data.reference || "";
  referenceCustomerId.value = data.referenceCustomerId || "";
  referenceUserId.value = data.referenceUserId || "";
}

watch(
  customerSource,
  (val) => {
    if (val && val.length === 1 && val[0] === "同事推荐" && referenceType.value !== "同事") setOtherData({ referenceType: "同事" });
    else if (val && val.length === 1 && val[0] === "客户推荐" && referenceType.value !== "客户") setOtherData({ referenceType: "客户" });
    else setOtherData({});
  },
  { immediate: true }
);
</script>
<style lang="scss" scoped></style>
