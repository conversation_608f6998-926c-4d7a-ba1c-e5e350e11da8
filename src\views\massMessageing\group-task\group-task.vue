<template>
  <my-layout v-loading="getTaskLoading" bg-fff common-shadow>
    <!-- <div class="member"> -->
    <layout-item>
      <div class="flex flex-wrap pt-15px px-15px">
        <el-form-item class="w-180px mb-15px mr-10px">
          <el-select v-model="query.createSounrce" placeholder="发送来源" clearable>
            <el-option v-for="item in sourceList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <!-- <el-form-item class="w-180px mb-15px mr-10px">
          <el-select v-model="query.type" placeholder="发送类型" clearable>
            <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item> -->
        <!-- <el-form-item class="w-180px mb-15px mr-10px">
          <el-select v-model="query.status" placeholder="发送状态" clearable>
            <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item class="w-240px mb-15px mr-10px">
          <el-date-picker v-model="query.sendTime" type="daterange"  :unlink-panels="true"
 />
        </el-form-item> -->
        <el-form-item class="w-auto mb-15px mr-10px">
          <el-button type="primary" @click="search()">查询</el-button>
        </el-form-item>
        <el-form-item class="w-auto mb-15px ml-auto">
          <el-button type="primary" @click="add()">新建群发</el-button>
        </el-form-item>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <el-table stripe :align="center" :data="selfGroupmsgList" empty-text="暂无数据" height="100%">
        <el-table-column class-name="first-td" prop="createSounrce" label="发送来源" :width="120" />
        <el-table-column class-name="first-td" prop="taskName" label="任务名称" :min-width="120" />
        <el-table-column class-name="first-td" prop="createTime" label="任务发起时间" :min-width="160" />
        <!-- 任务名称 -->
        <!-- <el-table-column prop="sendType" label="发送类型" :min-width="80" /> -->
        <!-- <el-table-column prop="teamName" label="团队" :min-width="120" /> -->
        <el-table-column prop="content" label="发送内容" :min-width="200">
          <template #default="{ row }">
            <div>
              <el-popover v-if="row.content" placement="top-start" :width="300" trigger="hover" :content="row.content">
                <template #reference>
                  <div text-ellipsis>
                    {{ row.content }}
                  </div>
                </template>
              </el-popover>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="sendStatus" label="发送状态" :min-width="80" />
        <el-table-column prop="sendTime" label="送达时间" :min-width="160" />
        <el-table-column prop="sendCount" label="预计发送客户数" :min-width="130" />
        <el-table-column prop="sendSuccessCount" label="成功" :width="80" />
        <el-table-column prop="sendFileCount" label="失败" :width="80" />
        <el-table-column fixed="right" class-name="el-table-fixed-column--shadow last-td" label="操作" width="80">
          <template #default="scope">
            <div flex items-center>
              <el-button text type="primary" size="small" @click="viewDetail(scope.row)">详情</el-button>
              <!-- <el-button text type="primary" class="pl-0 !important" size="small">去发送</el-button> -->
            </div>
          </template>
        </el-table-column>
      </el-table>
    </layout-main>
    <layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="currentPage" @handle-size-change="onHandleSizeChange" @handle-current-change="onHandleCurrentChange" />
    </layout-item>
  </my-layout>
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { useRouter } from "vue-router";
import pagination from "@/components/pagination/pagination.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import dayjs from "dayjs";
import { currentPage, pageSize, groupmsgList, total, getTaskLoading, getWecomGroupmesgList } from "../api/groupmsg.js";
let sendTaskType = "MINE";
let params = {
  sendSource: sendTaskType,
  executor: localStorage.getItem("userId"),
};
getWecomGroupmesgList(params);
const router = useRouter();
const query = ref({
  source: "",
  type: "",
  status: "",
  sendTime: "",
});
function add() {
  router.push({ name: "SENDMESSAGE", params: { sendTaskType } });
}
function viewDetail(row) {
  router.push({ name: "GROUPTASKDETAIL", params: { id: row._id, sendTaskType } });
}
const selfGroupmsgList = computed((item) => {
  let list = JSON.parse(JSON.stringify(groupmsgList.value));
  console.log(list);
  list.forEach((item) => {
    item.createSounrce = sourceList.find((source) => source.value === item.createSounrce)?.label;
    item.sendType = typeList.find((type) => type.value === item.sendType)?.label;
    if (item.executeStatus === "end") {
      item.sendStatus = "已结束";
    } else {
      item.sendStatus = item.taskStatus === "closed" ? "已取消" : item.unexecutedCount > 0 ? "未发送" : "已发送";
    }

    item.sendCount = item.unexecutedCount + item.sendSuccessCount + item.sendFileCount;
    item.sendTime = item.sendTime ? dayjs.unix(item.sendTime).format("YYYY-MM-DD HH:mm") : "";
    item.createTime = item.createTime ? dayjs(item.createTime).format("YYYY-MM-DD HH:mm") : "";
    let str = item.attachments
      .map((item) => {
        if (item.msgtype === "link" && item.linkType === "article") return "[文章]";
        else if (item.msgtype === "link") return "[网页]";
        else if (item.msgtype === "image") return "[图片]";
        else if (item.msgtype === "video") return "[视频]";
        else return "";
      })
      .join("");
    item.content = item.content + str;
  });
  return list;
});
const sourceList = [
  { label: "我的群发", value: "MINE" },
  { label: "团队群发", value: "TEAM" },
  { label: "机构群发", value: "CORP" },
];
const typeList = [
  { label: "群发消息", value: "MESSAGE" },
  { label: "群发朋友圈", value: "FRIEND" },
];
const statusList = [
  { label: "已发送", value: "SEND" },
  { label: "未发送", value: "UNSEND" },
];
function search() {
  if (query.value.createSounrce) {
    params.createSounrce = query.value.createSounrce;
  } else {
    delete params.createSounrce;
  }
  getWecomGroupmesgList(params);
}
function onHandleSizeChange(e) {
  currentPage.value = 1;
  pageSize.value = e;
  getWecomGroupmesgList(params);
}
function onHandleCurrentChange(e) {
  currentPage.value = e;
  getWecomGroupmesgList(params);
}
</script>
