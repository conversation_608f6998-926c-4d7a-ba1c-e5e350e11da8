<template>
  <my-layout class="bg-white rounded">
    <layout-item>
      <div class="flex flex-wrap items-center px-15px py-10px border-b border-gray-100">
        <radio-filter v-model="eventType" label="服务类型" :list="eventTypeList" :width="200" />
        <date-range-filter v-model:dates="dates" label="服务时间" :text="datesText" :width="320" />
        <!-- <date-filter v-model="executionTime" label="服务时间" :text="executionTime" /> -->
        <el-button class="ml-30px" type="primary" @click="search()">查询</el-button>
        <el-button type="primary" @click="reset()" plain>重置</el-button>
        <span class="ml-auto">
          <el-button type="primary" @click="add()">新增服务记录</el-button>
        </span>
      </div>
    </layout-item>
    <layout-main v-loading="loading" :scroll="false">
      <el-table stripe height="100%" :data="list">
        <el-table-column property="executionTime" label="服务时间" :min-width="150" />
        <el-table-column property="taskType" label="服务类型" :min-width="150" />
        <el-table-column property="taskContent" label="服务内容" :min-width="200">
          <template #default="{ row }">
            <el-popover v-if="row.taskContent" placement="top-start" :width="300" trigger="hover">
              <template #reference>
                <el-text truncated class="pointer" style="transform: translateY(4px)">
                  <content-userid-transform :content="row.taskContent"></content-userid-transform>
                </el-text>
              </template>
              <content-userid-transform :content="row.taskContent"></content-userid-transform>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column property="customerName" label="客户" :min-width="100">
          <template #default="{ row: { customerName, eventType } }">
            <!-- 成员待建档不显示昵称-->
            {{ customerName }}
          </template>
        </el-table-column>
        <el-table-column property="executorUserId" label="处理人" :min-width="100">
          <template #default="{ row: { executorUserId } }">
            <span v-if="executorUserId === 'system'">系统自动</span>
            <ww-user v-else-if="executorUserId" :openid="executorUserId"></ww-user>
          </template>
        </el-table-column>
        <el-table-column property="teamName" label="所属团队" :min-width="160"></el-table-column>
        <el-table-column fixed="right" class-name="el-table-fixed-column--shadow" label="操作" width="150">
          <template #default="{ row }">
            <div flex items-center>
              <el-text type="primary" class="mr-2" pointer @click="serviceEditRecord(row)">编辑</el-text>
              <el-text v-if="row.hasArticle" type="primary" class="mr-2" pointer @click="viewArticle(row.pannedEventSendFile)">查看文章</el-text>
              <el-text v-else-if="row.hasSurvery" type="primary" class="mr-2" pointer @click="viewSurvery(row)">查看问卷</el-text>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </layout-main>
    <layout-item>
      <div class="flex justify-end p-15px">
        <el-pagination class="ml-auto" layout="total, prev, pager, next, sizes, jumper" :current-page="page" :page-size="pageSize" :page-sizes="[10, 20, 50]" :total="total" @current-change="changePage" @size-change="changeSize"></el-pagination>
      </div>
    </layout-item>
  </my-layout>
  <survery-dialog :data="currentSurvery" :visible="visible" @close="visible = false" />
  <create-service ref="createServiceRef" @remove="removeServiceRecord" @save="onSaveService"></create-service>
</template>
<script setup>
import { computed, onMounted, ref } from "vue";
import { ElMessage } from "element-plus";
import dayjs from "dayjs";
import { getServiceRecord } from "@/api/todo";
import { ServiceType } from "@/baseData";
import useElPagination from "@/hooks/useElPagination";
import useServiceTable from "../useServiceTable";
import useTeamId from "../useTeamId";

import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import { DateFilter, DateRangeFilter, RadioFilter } from "@/components/filter-bar";
import contentUseridTransform from "@/components/content-userid-transform/index.vue";
import SurveryDialog from "@/views/system/survery/dialog.vue";
import createService from "@/components/create-service/index.vue";

const { formatList, viewArticle, getSurvery } = useServiceTable();
const props = defineProps({
  initValue: { type: Array, default: () => [] },
});

const loading = ref(false);
const eventType = ref("");
const dates = ref([]);
const executionTime = ref("");
const eventTypeList = Object.keys(ServiceType).map((i) => ({
  label: ServiceType[i],
  value: i,
}));
const datesText = computed(() => dates.value.join(" ~ "));

const { page, pageSize, changePage, changeSize } = useElPagination(getList);
const list = ref([]);
const total = ref(0);
let payload = {};

function search() {
  const params = {};
  if (eventType.value) params.eventType = eventType.value;
  // if (executionTime.value) params.executionTime = executionTime.value;
  if (dates.value.length) params.executionTime = [...dates.value];
  payload = { ...params };
  changePage(1);
}

async function getList() {
  loading.value = true;
  const { success, data, message } = await getServiceRecord(
    page.value,
    pageSize.value,
    {
      ...payload,
      executorUserId: localStorage.getItem("userId"),
      corpId: localStorage.getItem("corpId"),
    },
    "",
    currentTeamId.value
  );
  if (success) {
    const { data: tableData = [], total: count } = data;
    list.value = Array.isArray(tableData) ? formatList(tableData) : [];
    total.value = count;
  } else {
    ElMessage.error(message);
  }
  loading.value = false;
}

const currentSurvery = ref({});
const visible = ref(false);
async function viewSurvery(row) {
  const survery = await getSurvery(row.pannedEventSendFile, row.customerId);
  if (survery) {
    currentSurvery.value = survery;
    visible.value = true;
  } else {
    ElMessage.error("未找到问卷");
  }
}

// 编辑服务记录
const createServiceRef = ref("");

async function serviceEditRecord(item) {
  createServiceRef.value.open("edit", null, item);
}
function onSaveService() {
  getList();
}
function add() {
  createServiceRef.value.open("add");
}

function reset() {
  executionTime.value = "";
  eventType.value = "";
  search();
}
const currentTeamId = useTeamId(search, false);
onMounted(() => {
  const [start, end] = (Array.isArray(props.initValue) ? props.initValue : []).filter((i) => i && dayjs(i).isValid());
  dates.value = [start, end].filter(Boolean);
  search();
});
</script>
