import { defineStore } from "pinia";
import { getAllTeamByCorp, getTeamList } from "@/api/corp.js";
import { getRolesByRoleId } from "@/api/corp";
import { memberStore } from "@/store/member";

const teamStore = defineStore("team", {
  state: () => ({
    currentTeam: {},
    teams: [],
    allTeams: [],
    roles: [],
    isAdmin: false,
    // 移除 managerList，避免与 getter 冲突
  }),

  getters: {
    currentTeamId: (state) => state.currentTeam?.teamId || "",
    currentTeamName: (state) => state.currentTeam?.name || "",
    managerList(state) {
      const mergedManagerList = state.roles.reduce((acc, role) => acc.concat(role.managerList || []), []);
      return Array.from(new Set(mergedManagerList));
    },
  },

  actions: {
    updateRole(list) {
      this.roles = list;
      this.isAdmin = list.some((i) => i.roleId === "admin");
    },
    getArchiveLink(teamId, qrid) {
      const corpId = localStorage.getItem("corpId");
      return `${import.meta.env.VITE_PATIENT_PAGE_BASE_URL}pages/external/memberManage/index?qrid=${qrid}&teamId=${teamId}&corpId=${corpId}`;
    },

    async getTeams() {
      try {
        const { data } = await getTeamList();
        const teams = Array.isArray(data.data) ? data.data : [];
        this.teams = teams;
        return teams;
      } catch (error) {
        console.error("获取团队列表失败:", error);
        return [];
      }
    },

    async getAllTeams() {
      try {
        const { success, data } = await getAllTeamByCorp();
        if (success) {
          this.allTeams = data.data;
          await this.setCurrentTeam();
        }
      } catch (error) {
        console.error("获取所有团队失败:", error);
      }
    },

    teamSubscribe(callback) {
      callback &&
        this.$subscribe((mutation, state) => {
          callback(state);
        });
    },

    async getUserRole() {
      try {
        const { memberInfo } = memberStore();
        const { roleIds } = memberInfo;

        if (!roleIds?.length) return;

        const { data } = await getRolesByRoleId(roleIds);
        this.roles = data.data;
        this.isAdmin = data.data.some((role) => role.roleId === "admin");
      } catch (error) {
        console.error("获取用户角色失败:", error);
      }
    },

    async setCurrentTeam(id) {
      try {
        let storeTeamId = localStorage.getItem("currentTeamId");
        const { memberInfo } = memberStore();

        await this.getUserRole();

        // 检查用户是否有权限访问存储的团队
        if (!memberInfo.teamId?.some((i) => i === storeTeamId) && !this.isAdmin) {
          storeTeamId = "";
          localStorage.removeItem("currentTeamId");
        }

        const teamId = id || this.currentTeamId || storeTeamId;
        const teamsToSearch = this.isAdmin ? this.allTeams : this.teams;
        const team = teamId ? teamsToSearch?.find((i) => teamId === i.teamId) : null;
        const currentTeam = team || teamsToSearch[0] || {};

        this.currentTeam = currentTeam;

        if (currentTeam.teamId) {
          localStorage.setItem("currentTeamId", currentTeam.teamId);
        } else {
          localStorage.removeItem("currentTeamId");
        }
      } catch (error) {
        console.error("设置当前团队失败:", error);
      }
    },
  },
});

export { teamStore };
