<template>
  <el-drawer v-model="visible" :title="`${customerName}的三方照片`" direction="rtl" size="60%" :before-close="handleClose">
    <div class="photo-selector-container">
      <!-- 术前照片 -->
      <div class="photo-group">
        <div class="group-header">
          <span class="group-title">术前</span>
          <el-checkbox v-model="beforeAllSelected" @change="toggleGroupSelect('before')"
            class="select-all-checkbox">全选</el-checkbox>
        </div>
        <div class="photo-grid">
          <div v-for="(photo, index) in beforeImgs" :key="photo.url" class="photo-item"
            :class="{ selected: photo.selected }">
            <div class="photo-wrapper cursor-pointer" @click="updateGroupSelectStatus('before',photo.url)">
              <img :src="photo.url" class="photo-image" />
              <div class="photo-overlay" v-if="photo.selected">
                <el-icon class="check-icon">
                  <Check />
                </el-icon>
              </div>
              <el-checkbox :model-value="photo.selected" class="photo-checkbox" />
            </div>
          </div>
        </div>
      </div>
      <!-- 术后照片 -->
      <div class="photo-group">
        <div class="group-header">
          <span class="group-title">术后</span>
          <el-checkbox v-model="afterAllSelected" @change="toggleGroupSelect('after')"
            class="select-all-checkbox">全选</el-checkbox>
        </div>
        <div class="photo-grid">
          <div v-for="(photo, index) in afterImgs" :key="photo.url" class="photo-item"
            :class="{ selected: photo.selected }">
            <div class="photo-wrapper cursor-pointer" @click="updateGroupSelectStatus('after',photo.url)">
              <img :src="photo.url" class="photo-image" />
              <div class="photo-overlay" v-if="photo.selected">
                <el-icon class="check-icon">
                  <Check />
                </el-icon>
              </div>
              <el-checkbox :model-value="photo.selected" class="photo-checkbox" />
            </div>
          </div>
        </div>
      </div>
      <!-- 术中照片 -->
      <!-- <div class="photo-group">
        <div class="group-header">
          <span class="group-title">术中</span>
          <el-checkbox v-model="duringAllSelected" @change="toggleGroupSelect('during')"
            class="select-all-checkbox">全选</el-checkbox>
        </div>
        <div class="photo-grid">
          <div v-for="(photo, index) in duringPhotos" :key="photo.url" class="photo-item"
            :class="{ selected: photo.selected }">
            <div class="photo-wrapper">
              <img :src="photo.url" class="photo-image" />
              <div class="photo-overlay" v-if="photo.selected">
                <el-icon class="check-icon">
                  <Check />
                </el-icon>
              </div>
              <el-checkbox v-model="photo.selected" class="photo-checkbox"
                @change="updateGroupSelectStatus('during')" />
            </div>
          </div>
        </div>
      </div> -->

      <!-- 复诊照片 -->
      <div class="photo-group">
        <div class="group-header">
          <span class="group-title">复诊</span>
          <el-checkbox v-model="reviewAllSelected" @change="toggleGroupSelect('review')"
            class="select-all-checkbox">全选</el-checkbox>
        </div>
        <div class="photo-grid">
          <div v-for="(photo, index) in reviewImgs" :key="photo.id" class="photo-item"
            :class="{ selected: photo.selected }">
            <div class="photo-wrapper cursor-pointer" @click="updateGroupSelectStatus('review',photo.url)">
              <img :src="photo.url" :alt="photo.name" class="photo-image" />
              <div class="photo-overlay" v-if="photo.selected">
                <el-icon class="check-icon">
                  <Check />
                </el-icon>
              </div>
              <el-checkbox :model-value="photo.selected" class="photo-checkbox" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="drawer-footer text-center">
      <el-button @click="handleCancel" class="cancel-btn">取消</el-button>
      <el-button type="primary" @click="handleConfirm" class="confirm-btn">确定</el-button>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import { Check } from "@element-plus/icons-vue";
import { getYizhipaiPhotos } from "@/api/yizhipai";

const emit = defineEmits(["update:modelValue", "confirm", "cancel"]);
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  customerName: {
    type: String,
    default: "",
  },
  customerId: {
    type: String,
    default: "",
  }
});

const visible = computed({
  get: () => props.modelValue,
  set: (val) => emit("update:modelValue", val),
});
const photos = ref([])
const beforeImgs = ref([])
const afterImgs = ref([])
const reviewImgs = ref([])

// 手术前照片
const beforePhotos = computed(() => photos.value.filter(i => i.timeInterval === 0))
// 手术后照片
const afterPhotos = computed(() => photos.value.filter(i => i.timeInterval === 1))
// 复诊照片
const reviewPhotos = computed(() => photos.value.filter(i => i.timeInterval === 2))

// 全选状态
const beforeAllSelected = computed({
  get: () => beforeImgs.value.length > 0 && beforeImgs.value.every(i => i.selected),
  set: (val) => {
    beforeImgs.value.forEach(i => i.selected = val)
  }
});
const afterAllSelected = computed({
  get: () => afterImgs.value.length > 0 && afterImgs.value.every(i => i.selected),
  set: (val) => {
    afterImgs.value.forEach(i => i.selected = val)
  }
});
const reviewAllSelected = computed({
  get: () => reviewImgs.value.length > 0 && reviewImgs.value.every(i => i.selected),
  set: (val) => {
    reviewImgs.value.forEach(i => i.selected = val)
  }
});

// 切换分组全选
function toggleGroupSelect(groupType) {
  let photos, allSelected;

  switch (groupType) {
    case "before":
      photos = beforePhotos.value;
      allSelected = beforeAllSelected.value;
      break;
    case "after":
      photos = afterPhotos.value;
      allSelected = afterAllSelected.value;
      break;
    case "during":
      photos = duringPhotos.value;
      allSelected = duringAllSelected.value;
      break;
    case "review":
      photos = reviewPhotos.value;
      allSelected = reviewAllSelected.value;
      break;
  }

  photos.forEach((photo) => {
    photo.selected = allSelected;
  });
}

// 更新分组全选状态
function updateGroupSelectStatus(groupType, url) {
  let targetImgs = [];
  if (groupType === 'before') {
    targetImgs = beforeImgs.value
  } else if (groupType === 'after') {
    targetImgs = afterImgs.value
  } else if (groupType === 'review') {
    targetImgs = reviewImgs.value
  }
  const index = targetImgs.findIndex(i => i.url === url)
  if (index !== -1) {
    targetImgs[index].selected = !targetImgs[index].selected
  }
}

// 处理确认
function handleConfirm() {
  const imgs = [...beforeImgs.value, ...afterImgs.value, ...reviewImgs.value].filter(i => i.selected);

  emit("confirm", imgs.map(i=>({url:i.url, name:i.name})));
  visible.value = false;
}

// 处理取消
function handleCancel() {
  emit("cancel");
  visible.value = false;
}

// 处理关闭
function handleClose() {
  visible.value = false;
}

async function getPhotos() {
  const { data } = await getYizhipaiPhotos({
    customerId: props.customerId,
  });
  photos.value = data && Array.isArray(data.data) ? data.data.map(i => ({
    timeInterval: i.timeInterval,
    photos: Array.isArray(i.files) ? i.files.map(j => ({ url: j.path, name: j.originalname })) : []
  })) : []
}
function setImgs() {
  beforeImgs.value = beforePhotos.value.reduce((acc, item) => {
    item.photos.forEach(({ url, name }) => {
      acc.push({ url, name, selected: false })
    })
    return acc
  }, [])
  afterImgs.value = afterPhotos.value.reduce((acc, item) => {
    item.photos.forEach(({ url, name }) => {
      acc.push({ url, name, selected: false })
    })
    return acc
  }, [])
  reviewImgs.value = reviewPhotos.value.reduce((acc, item) => {
    item.photos.forEach(({ url, name }) => {
      acc.push({ url, name, selected: false })
    })
    return acc
  }, [])

}

// 监听弹框打开，重置选择状态
watch(visible, async (newVal) => {
  if (newVal) {
    await getPhotos()
    setImgs()
    // 重置所有选择状态

  }
});
</script>

<style lang="scss" scoped>
.photo-selector-container {
  padding: 0 20px 80px 20px;
  height: calc(100vh - 120px);
  overflow-y: auto;
}

.photo-group {
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 0;
  }
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.group-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.select-all-checkbox {
  :deep(.el-checkbox__label) {
    font-size: 14px;
    color: #606266;
  }
}

.photo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.photo-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;

  &.selected {
    .photo-wrapper {
      border: 2px solid #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.photo-wrapper {
  position: relative;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.photo-image {
  width: 100%;
  height: 150px;
  object-fit: cover;
  display: block;
}

.photo-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(64, 158, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-icon {
  font-size: 24px;
  color: #409eff;
  background: white;
  border-radius: 50%;
  padding: 4px;
}

.photo-checkbox {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 2px;

  :deep(.el-checkbox__input) {
    .el-checkbox__inner {
      border-color: #409eff;
    }
  }
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px 20px;
  background: white;
  border-top: 1px solid #e4e7ed;
  display: flex;
  justify-content: center;
  gap: 12px;
}

.cancel-btn {
  min-width: 80px;
}

.confirm-btn {
  min-width: 80px;
}

:deep(.el-drawer__header) {
  margin-bottom: 0;
  padding: 20px;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-drawer__body) {
  padding: 0;
  position: relative;
}
</style>