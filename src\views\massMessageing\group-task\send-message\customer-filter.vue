<template>
  <el-dialog :model-value="visible" :width="width" title="按条件筛选我的客户" @close="close" height>
    <div v-loading="loading">
      <el-input v-model="name" placeholder="搜索" size="large" class="text-center" :suffix-icon="Search" />
      <div class="flex items-center my-15px">
        <div class="w-0 mr-10px flex-grow text-14px" @click="showTagModal">
          <div class="text-blue-500 font-semibold cursor-pointer truncate">根据标签筛选</div>
          <tag-str v-if="tags.length" class="mt-10px cursor-pointer" :tags="tags" />
        </div>
        <div class="mr-22px">
          <img src="@/assets/check-active.png" class="w-14px h-14px pointer" v-if="isAllCustomer" @click="removeAll()" />
          <img @click="changeAll()" src="@/assets/check.png" class="w-14px h-14px pointer" v-else />
        </div>
      </div>
      <div style="overflow: auto; height: 39vh">
        <div v-infinite-scroll="load" :infinite-scroll-disabled="disabled" :infinite-scroll-immediate="false" style="height: 40vh">
          <div class="h-70px" v-for="item in list" :key="item._id">
            <div class="flex h-60px items-center border border-gray-200 rounded-4px px-15px mb-10px cursor-pointer hover:border-gray-400">
              <div class="flex-grow flex items-center py-10px">
                <div class="mr-10px font-semibold text-16px">{{ item.name }}</div>
                <div class="mr-10px text-gray-500 text-14px">{{}}</div>
              </div>
              <el-checkbox :model-value="selfSelectCustomers.some((i) => i._id === item._id)" @change="selectCheckBox(item)" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="flex justify-between">
        <div class="text-14px text-gray-500">
          已选
          <span class="text-blue-500 mx-4px underline">{{ selfSelectCustomers.length }}</span>
          位客户
        </div>
        <el-button class="w-100px" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <tag-modal :value="tags" :visible="tagVisible" :width="tagWidth" @close="closeTag" @change="changeTag($event)" />
</template>
<script setup>
import { computed, nextTick, ref, watch } from "vue";
import { watchDebounced } from "@vueuse/core";
import useModal from "@/hooks/useModal";
import { memberStore } from "@/store/member";
import { Search } from "@element-plus/icons-vue";
import tagModal from "../../components/tag-modal.vue";
import tagStr from "../../components/tag-str.vue";
import { RecycleScroller } from "vue-virtual-scroller";
import "vue-virtual-scroller/dist/vue-virtual-scroller.css";
import { getCorpCustomer } from "@/api/member";
const { corpInfo } = memberStore();
const props = defineProps({
  visible: { type: Boolean, default: false },
  width: { type: [String, Number], default: 200 },
  teamIds: { type: String, default: "" },
  selectCustomers: { type: Array, default: [] },
  type: { type: String, default: "SOME" },
});
let selfSelectCustomers = ref([]);
let customerTotal = ref(0);
const emits = defineEmits(["close", "change"]);
function close() {
  emits("close");
}
const tags = ref([]);
watch(
  () => props.visible,
  () => {
    tags.value = [];
    selfSelectCustomers.value = props.selectCustomers;
    page.value = 1;
    getList(10);
  },
  { immediate: false, debounce: 750, maxWait: 5 * 1000 }
);
const isAllCustomer = computed(() => {
  return selfSelectCustomers.value.length === customerTotal.value;
});
function changeAll() {
  page.value = 1;
  getList(100);
}
function removeAll() {
  selfSelectCustomers.value = [];
}
const { close: closeTag, show: showTagModal, visible: tagVisible, width: tagWidth } = useModal(640); //  选择客户弹窗
function changeTag(val) {
  tags.value = val.map((i) => ({ ...i }));
  selfSelectCustomers.value = [];
  page.value = 1;
  getList(100);
}
const name = ref("");
const list = ref([]);
const page = ref(1);
const more = ref(true);
const loading = ref(false);
const queryStr = computed(
  () =>
    `${name.value}_${tags.value
      .filter((i) => i.name)
      .map((i) => i.name)
      .join()}`
);
watchDebounced(
  queryStr,
  () => {
    page.value = 1;
    getList(20);
  },
  { immediate: false, debounce: 750, maxWait: 5 * 1000 }
);
function selectCheckBox(e) {
  if (selfSelectCustomers.value.some((i) => i._id === e._id)) {
    selfSelectCustomers.value = selfSelectCustomers.value.filter((i) => i._id !== e._id);
  } else {
    selfSelectCustomers.value.push(e);
  }
}
function confirm() {
  emits("change", selfSelectCustomers.value);
  selfSelectCustomers.value = [];
}
async function getList(pageSize) {
  if (loading.value) return;
  loading.value = true;
  let params = {
    page: page.value,
    pageSize,
    showExternalCustomer: true,
    teamId: props.teamIds,
    userIds: [localStorage.getItem("userId")],
    corpId: localStorage.getItem("corpId"),
    permanent_code: corpInfo.permanent_code,
  };
  if (name.value) params.name = name.value;
  if (tags.value.length) params.tagIds = tags.value.map((i) => i.id);
  const { data, success } = await getCorpCustomer(params);
  loading.value = false;
  if (!success) return;
  list.value = page.value === 1 ? data.data : [...list.value, ...data.data];
  more.value = data.total > list.value.length;
  customerTotal.value = data.total;
  if (pageSize == 100) {
    if (more.value) {
      page.value += 1;
      getList(pageSize);
    }
    selfSelectCustomers.value = list.value;
  }
}
function load() {
  if (more.value && !loading.value) {
    page.value += 1;
    getList(10);
  }
}
</script>
<style lang="scss">
::-webkit-scrollbar {
  width: 6px;
  background-color: transparent;
}

::-webkit-scrollbar-track {
  border-radius: 4px;
  // background-color: white;
}

::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: #909399;
}
</style>
