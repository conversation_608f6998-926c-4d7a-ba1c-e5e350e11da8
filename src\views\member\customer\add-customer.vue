<template>
  <el-drawer :model-value="visible" :title="title" size="60%" :before-close="close">
    <customer-view ref="customerRef" operateType="add" :templateList="templateList" @onClose="close" @onSuccess="success"></customer-view>
  </el-drawer>
</template>
<script setup>
import customerView from "./customer-view.vue";
import dayjs from "dayjs";
import { templateStore } from "@/store/template";
import { watch, ref, onMounted, nextTick } from "vue";
import { storeToRefs } from "pinia";
import { addMember, updateMember } from "@/api/member";
import { createServiceRecord } from "@/utils/service";
import { teamStore } from "@/store/team";
import { memberStore } from "@/store/member";
import { ElMessage, ElMessageBox } from "element-plus";
import { bindWechat } from "../components/cutomer-table/utils";
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  visible: {
    type: Boolean,
    default: false,
  },
  viewType: {
    type: String,
    default: "customer",
  },
  customer: {
    type: Object,
    default: () => ({}),
  },
});
const emit = defineEmits(["close", "update"]);
const customerRef = ref(null);
watch(
  () => props.visible,
  async (newVal) => {
    if (newVal) {
      await nextTick();
      customerRef.value.getInitData(props.customer);
      customerRef.value.scollToTop();
    }
  }
);
const close = () => {
  emit("close");
};
const store = templateStore();
const { corpTemplateList } = storeToRefs(store);
const { getCorpTemplate } = store;
const templateList = ref([
  {
    name: "基础信息",
    type: "baseTemplate",
  },
  {
    name: "内部信息",
    type: "internalTemplate",
  },
]);
onMounted(async () => {
  if (!Array.isArray(corpTemplateList.value) || corpTemplateList.value.length === 0) {
    getCorpTemplate();
  }
});
watch(
  corpTemplateList,
  (newVal) => {
    if (Array.isArray(newVal) && newVal.length > 0) {
      templateList.value = templateList.value
        .map((item) => {
          const temp = newVal.find((i) => i.templateType === item.type);
          if (temp && temp.templateStatus !== "disable") {
            item.templateList = (temp && temp.templateList).filter((i) => i.operateType !== "onlyRead") || [];
          } else {
            return null;
          }

          return item;
        })
        .filter((i) => i);
    }
  },
  { immediate: true }
);
const success = async (item, callback) => {
  // 判断form.value.mobile是否为空 且判断手机号是否合法
  if (item.mobile && !/^1[3456789]\d{9}$/.test(item.mobile)) {
    ElMessage.warning("请输入正确的手机号码");
    callback("fail");
    return;
  }
  if (item.telphone && !/^\d{3,4}-\d{6,8}$/.test(item.telphone)) {
    ElMessage.warning("请输入正确的座机号码");
    callback("fail");
    return;
  }
  // 判断form.value.idCard是否为空 且判断身份证是否合法
  if (item.idCard && !isChinaIdCard(item.idCard)) {
    ElMessage.warning("请输入正确的身份证号码");
    callback("fail");
    return;
  }
  if (item.referenceType == "客户") {
    item.referenceUserId = "";
  }
  if (item.referenceType == "同事") {
    item.reference = "";
    item.referenceCustomerId = "";
  }
  const { currentTeam } = teamStore();
  const { teamId } = currentTeam;
  const userId = localStorage.getItem("userId");
  let params = {
    creator: localStorage.getItem("userId"),
    corpId: localStorage.getItem("corpId"),
  };
  if (props.viewType === "customer") {
    params["personResponsibles"] = [
      {
        corpUserId: userId,
        teamId,
      },
    ];
    params["teamId"] = teamId ? [teamId] : [];
  }
  // 把团队加到这这个团队中

  const memberId = item._id;
  if (!memberId) {
    Object.assign(params, item);
  } else {
    if (props.viewType === "customer") {
      if (typeof item.teamId === "string") item.teamId = [item.teamId];
      if (!item.teamId.includes(teamId)) item.teamId.push(teamId);
    }
    if (!item.personResponsibles) item.personResponsibles = [];
    let contains = item.personResponsibles.some((item) => item.corpUserId === userId && item.teamId === teamId);
    if (!contains) {
      item.personResponsibles.push({
        corpUserId: userId,
        teamId,
      });
    }
    delete item._id;
  }
  const { success, message, data } = !memberId ? await addMember(params) : await updateMember(memberId, item);
  if (success) {
    const id = data.data ? data.data.id : memberId;
    if ((!memberId || !item.externalUserId) && props.viewType === "customer") {
      bindCustomerWeChat(item, id, callback);
    }
    callback && callback("success");
    emit("close");
    emit("update", id);
  } else {
    callback && callback("fail");
    ElMessage.error(message);
  }
};
async function bindCustomerWeChat(params, id) {
  await ElMessageBox.confirm(`${params.name}新建档案成功,是否绑定客户微信?`, "提示", {
    confirmButtonText: "立即绑定",
    cancelButtonText: "关闭",
    type: "warning",
  });
  await bindWechat({ _id: id });
  emit("update");
}
function isChinaIdCard(str) {
  const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  return reg.test(str);
}
const { memberInfo } = storeToRefs(memberStore());
// function createServiceRecordAction(params, customerId) {
//   if (!customerId) return;
//   const { currentTeam } = teamStore();
//   const { teamId, name: teamName } = currentTeam;
//   const { anotherName } = memberInfo.value;
//   let time = dayjs().format("YYYY-MM-DD HH:mm");
//   let item = {
//     taskContent: `${anotherName ? anotherName : ""}为客户${params.name}新增一份档案，建档时间为${time}`,
//     executionTime: new Date().getTime(),
//     customerId,
//     executeTeamId: teamId,
//     teamName,
//     eventType: "addCustomerRrofile",
//     customerName: params.name,
//   };
//   createServiceRecord(item);
// }
</script>

<style lang="scss">
.group-title {
  padding: 0 12px 16px;
  color: var(--yc-main-text-color);
  font-weight: 600;
  font-size: 14px;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}
</style>
