<template>
  <slot :team="team">
    <span v-if="team">{{ team.name }}</span>
  </slot>
</template>
<script setup>
import { computed } from "vue";
import { storeToRefs } from 'pinia';
import { teamStore } from "@/store/team";

const props = defineProps({
  teamId: { type: String, default: '' },
})
const { allTeams } = storeToRefs(teamStore());
const team = computed(() => {
  return allTeams.value.find(team => team.teamId === props.teamId)
})

</script>
<style lang="scss" scoped></style>
