<template>
  <el-drawer v-model="visible" title="咨询预约列表" size="80%" style="--el-drawer-padding-primary:0;">
    <my-layout class="bg-[#f2f3f4]">
      <layout-item>
        <div class="overflow-hidden m-15px">
          <el-row :gutter="10">
            <el-col v-for="i in count" :key="i.key" :span="6">
              <div class="flex flex-col justify-between rounded bg-white rounded px-20px py-24px h-80px">
                <div class="text-15px text-gray-500"> {{ i.label }} </div>
                <div class="mt-8px text-primary font-semibold text-18px">{{ i.value }}</div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="py-5px mx-15px flex items-center bg-white">
          <div class="flex-grow flex flex-wrap">
            <date-filter v-model="date" label="预约日期" />
            <check-box-filter v-model="type" label="预约类型" :list="regTypes"></check-box-filter>
            <check-box-filter v-model="project" label="项目" :list="projectList"></check-box-filter>
            <check-box-filter v-model="consultant" label="预约咨询师" :list="consultantList"></check-box-filter>
            <check-box-filter v-model="status" label="预约状态" :list="statusList"></check-box-filter>
            <filter-customer-stage v-model="stages" />
            <check-box-filter v-model="receptionPeople" label="登记人" :list="peopleList"></check-box-filter>

            <input-filter v-model="name" label="客户姓名" placeholder="请输入" :width="200" />
            <input-filter v-model="mobile" label="客户手机号" placeholder="请输入" :width="200" />

          </div>
          <div class="flex-shrink-0 pr-15px">
            <el-button type="primary">查询</el-button>
          </div>
        </div>
      </layout-item>
      <layout-main :scroll="false">
        <div class="h-full px-15px">
          <el-table stripe height="100%" :data="[1,2,4,5,6]" v-loading="loading">
            <el-table-column prop="name" label="姓名" :min-width="100">
              <template #default="{ row: { name } }">
                <el-popover :key="_id" placement="top-start" :width="320" trigger="hover">
                  <template #reference>
                    <div class="truncate"> {{ name }}</div>
                  </template>
                  <el-text>{{ name }}</el-text>
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column prop="age" label="性别/年龄" :min-width="100">
              <template #default="{ row: { name } }">
                <div>性别 / 年龄</div>
              </template>
            </el-table-column>
            <el-table-column prop="wechat" label="微信联系人" :min-width="100"></el-table-column>
            <el-table-column prop="reportPeople" label="报备人" :min-width="100"></el-table-column>
            <el-table-column prop="stage" label="客户阶段" :min-width="100"></el-table-column>
            <el-table-column prop="firstArriveTime" label="首次到院时间" :min-width="100"></el-table-column>
            <el-table-column prop="consume" label="消费情况" :min-width="100"></el-table-column>
            <el-table-column prop="recentPayTime" label="最近消费时间" :min-width="100"></el-table-column>
            <el-table-column prop="plan" label="跟进计划" :min-width="100"></el-table-column>
            <el-table-column prop="customerSource" label="客户来源" :min-width="100"></el-table-column>
            <el-table-column prop="project" label="意向项目" :min-width="100"></el-table-column>
            <el-table-column prop="reportExplain" label="报备说明" :min-width="100"></el-table-column>
            <el-table-column prop="reportTime" label="报备时间" :min-width="100"></el-table-column>
            <el-table-column fixed="right" class-name="el-table-fixed-column--shadow" label="操作" width="200">
              <template #default="{ row }">
                <el-text class="cursor-pointer mr-5px" type="primary">报备详情</el-text>
                <el-text class="cursor-pointer mr-5px" type="primary" @click="planShow()">跟进计划</el-text>
                <el-text class="cursor-pointer" type="primary">预约</el-text>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </layout-main>
      <layout-item>
        <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize"
          @handle-current-change="changePage" />
      </layout-item>
    </my-layout>
  </el-drawer>
</template>
<script setup>
import { ref } from "vue";
import { useVModel } from "@vueuse/core";

import { CheckBoxFilter, CustomFilter, DateFilter, DateRangeFilter, InputFilter, RadioFilter, filterCustomerStage } from "@/components/filter-bar";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";

const emit = defineEmits(["update:modelValue"]);
const props = defineProps({
  conditions: { type: Array, default: [] },
  modelValue: {
    type: Object,
    default: () => ({}),
  },
});
const visible = useVModel(props, "modelValue", emit);
const count = ref([
  { key: 1, label: "今日预约", value: 100 },
  { key: 2, label: "已到院", value: 1000 },
  { key: 3, label: "未到院", value: 100 },
  { key: 4, label: "已取消", value: 1000 },
])
const consultant = ref([]);
const consultantList = ref([]);
const date = ref('');
const project = ref([]);
const projectList = ref([]);
const status = ref([]);
const statusList = ref([]);
const stage = ref([]);
const stageList = ref([]);
const type = ref('');
const regTypes = ref([]);

</script>
<style lang="scss" scoped></style>
