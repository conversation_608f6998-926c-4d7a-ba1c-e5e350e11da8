<template>
  <el-form-item class="px-15px py-6px el-form-item--autoheight" :class="required ? 'is-required' : ''" :label="name"
    border-bottom>
    <ww-user-select :list="memberList" placeholder="请选择跟进人" :value="value" @change="change($event)" />
  </el-form-item>
</template>
<script setup>
import { computed } from 'vue';
import WwUserSelect from "@/components/ww-user-select";

const emits = defineEmits(['change'])
const props = defineProps({
  form: { type: Object, default: () => ({}) },
  mult: { type: Boolean, default: false },
  name: { type: String, default: '' },
  memberList: { type: Array, default: () => [] },
  required: { type: Boolean, default: false },
  title: { type: String, default: '' },
})
const value = computed(() => props.form[props.title] || '');

function change(value) { emits('change', { title: props.title, value }) }

</script>
<style lang="scss" scoped></style>
