import { markRaw, ref, onMounted } from 'vue';
import { watchDebounced, useWindowSize } from "@vueuse/core";
import echarts from '@/utils/echarts';

function getServiceOptions(xAxisData = [], customerData = [], serviceData = []) {
  return {
    title: { text: '' },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: { backgroundColor: '#6a7985', Color: '#fff' }
      }
    },
    legend: {
      textStyle: {
        fontSize: 13,
        color: '#a7cdff'
      },
      bottom: 0,
      data: ['新增客户数', '服务记录']
    },

    grid: {
      top: '3%',
      left: '5%',
      right: '5%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: [{
      type: 'category',
      // boundaryGap: false,
      //刻度线
      axisTick: { show: false },
      data: xAxisData,
      axisLine: {
        lineStyle: {
          color: '#a7cdff',
          width: 0, //这里是为了突出显示加上的
        }
      },
    },
    ],
    yAxis: [{
      type: 'value',
      boundaryGap: [0.2, 0.2],
      min: 0,
      //刻度线
      axisTick: { //y轴刻度线
        show: false
      },
      //字体颜色
      axisLine: {
        lineStyle: {
          color: '#a7cdff',
          width: 0, //这里是为了突出显示加上的
        }
      },
      //网格线
      splitLine: {
        show: true,
        lineStyle: {
          color: ['#23569b'],
          width: 1,
          type: 'dashed'
        }
      }
    }
    ],
    color: ['#00c5d1', '#007dec'],
    series: [
      {
        name: '新增客户数',
        type: 'bar',
        barWidth: '15',
        barGap: 0,
        data: customerData,
        itemStyle: {
          normal: {
            borderRadius: [40, 40, 0, 0],
            color: new echarts.graphic.LinearGradient(
              1, 0, 1, 1,
              [
                { offset: 0, color: '#00c7d2' },
                { offset: 0.5, color: '#0389b2' },
                { offset: 1, color: '#045595' }
              ]
            )
          },
        }
      },
      {
        name: '服务记录',
        type: 'bar',
        barWidth: '15',
        barGap: 0,
        itemStyle: {
          normal: {
            barBorderRadius: [40, 40, 0, 0],
            color: new echarts.graphic.LinearGradient(
              1, 0, 1, 1,
              [
                { offset: 0, color: '#0083f5' },
                { offset: 0.5, color: '#0964c1' },
                { offset: 1, color: '#04479b' }
              ]
            )
          },
        },
        data: serviceData
      }
    ],
  }
}

export default function useServiceChart(chartRef) {
  const chart = ref(null);
  const customerData = ref([]);
  const serviceData = ref([]);
  const xAxisData = ref([]);
  const { width, height } = useWindowSize();

  function setChart() {
    chart.value = markRaw(echarts.init(chartRef.value));
  }

  function paint(repaint = false) {
    if (chartRef.value && !chart.value) {
      setChart();
      repaint = false;
    };
    if (!chart.value) return;
    const option = getServiceOptions(xAxisData.value, customerData.value, serviceData.value);
    chart.value.setOption(option, repaint);
  }
  onMounted(paint)
  watchDebounced([xAxisData, customerData, serviceData], n => {
    paint(true)
  }, { debounce: 500, maxWait: 1000 })
  watchDebounced([width, height], () => {
    chart.value && chart.value.resize();
  }, { debounce: 500, maxWait: 1500 })

  return { customerData, serviceData, xAxisData }
}