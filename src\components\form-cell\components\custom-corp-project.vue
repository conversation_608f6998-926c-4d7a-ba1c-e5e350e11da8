<template>
  <div v-if="isRead">
    {{ item.value && Array.isArray(item.value) ? item.value.join("、") : item.value }}
  </div>
  <el-select v-else clearable remote reserve-keyword :model-value="value" :multiple="item.mult" :filterable="true"
    placeholder="请选择" @update:model-value="change($event)" :remote-method="search" :loading="loading" class="w-200px">
    <el-option v-for="opt in options" :label="opt.label" :value="opt.label" />
  </el-select>
</template>
<script setup>
import { computed, ref } from 'vue';
import { storeToRefs } from "pinia";
import { watchDebounced } from "@vueuse/core";
import { memberStore } from "@/store/member";
import { getProjectList } from "@/api/project-manage";

const $emit = defineEmits(["change"]);
const props = defineProps({
  item: {
    type: Object,
    default: {},
  },
  value: {
    type: String,
    default: "",
  },
  isRead: {
    type: Boolean,
    default: false,
  },
});
const { memberInfo } = storeToRefs(memberStore());
const loading = ref(false);
const list = ref([]);
const text = ref('');
const arrValue = computed(() => (Array.isArray(props.value) ? props.value : [props.value]).filter(Boolean));
const valueOptions = computed(() => arrValue.value.map(item => ({ label: item, value: item })));
const options = computed(() => {
  const str = text.value.trim();
  const data = list.value.filter(i => !valueOptions.value.some(item => item.value === i.value))
  if (str) {
    return [...valueOptions.value.filter(item => item.label.includes(str)), ...data]
  }
  return [...valueOptions.value, ...data];
})


function search(val) {
  if (val) {
    loading.value = true;
    text.value = val;
  }
}
function change(value) {
  $emit("change", {
    title: props.item.title,
    value,
  });
}

async function getProjects() {
  const payload = {
    page: 1,
    pageSize: 15,
    corpId: memberInfo.value.corpId,
    projectStatus: 'enable',
    projectName: text.value.trim(),
  }
  const { data } = await getProjectList(payload);
  list.value = data && Array.isArray(data.list) ? data.list.map(i => ({ value: i.projectName, label: i.projectName })) : [];
  loading.value = false
}

watchDebounced(
  text,
  async () => {
    if (text.value) {
      await getProjects();
    }
    loading.value = false;
  },
  { debounce: 1000 }
);
</script>

<style></style>