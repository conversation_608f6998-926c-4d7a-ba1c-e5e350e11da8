<template>
  <div flex items-center :style="{ color }">
    <svg-icon name="star" v-for="i in fullStarCount" :key="`full_${i}`" class="five-star" :size="size"></svg-icon>
    <svg-icon name="star-empty" v-for="i in emptyStarCount" :key="`empty_${i}`" class="five-star" :size="size"></svg-icon>
    <slot name="append"></slot>
  </div>
</template>
<script setup>
import { computed, toRefs } from 'vue';
import SvgIcon from '../svg-icon';

const props = defineProps({
  color: { type: String, default: '#fff' },
  gap: { type: Number, default: 5 },
  score: { type: Number, default: 0 },
  size: { type: Number, default: 18 }
})
const { color, gap, score, size } = toRefs(props);

const fullStarCount = computed(() => {
  return score.value >= 0 && score.value <= 5 && score.value % 1 === 0 ? score.value : 0
})
const emptyStarCount = computed(() => {
  return 5 - fullStarCount.value
})
</script>
<style scoped>
.five-star {
  margin-right: v-bind(gap + "px");
}
</style>