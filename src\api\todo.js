import { teamStore } from "@/store/team";
import { post } from "./axios";

async function useToDo(data) {
  const res = await post("todo", data);
  return res;
}
export async function getToDoList(page, pageSize, params, sort, teamId) {
  const res = await useToDo({
    type: "getEventsRecords",
    teamId: teamId || teamStore().currentTeamId,
    page,
    pageSize,
    params,
    sort,
  });
  return res;
}

export async function getCustomerTodos(data) {
  const res = await useToDo({
    type: "getCustomerTodos",
    ...data,
  });
  return res;
}

export async function updateTaskTodo(data) {
  const res = await useToDo({
    type: "updateTaskTodo",
    ...data,
  });
  return res;
}

export async function removeTodo(data) {
  const res = await useToDo({
    type: "removeTodo",
    ...data,
  });
  return res;
}

export async function updateTaskTodoResult(data) {
  const res = await useToDo({
    type: "updateTaskTodoResult",
    ...data,
  });
  return res;
}

export async function executeManagementPlanTodo(data) {
  const res = await useToDo({
    type: "executeManagementPlanTodo",
    creatorUserId: localStorage.getItem("userId"),
    ...data,
  });
  return res;
}

export async function getCorpToDolist(data) {
  const res = await useToDo({
    type: "getCorpToDolist",
    ...data,
  });
  return res;
}
export async function getEventsCount(params, teamId) {
  const res = await useToDo({
    type: "getEventsCount",
    teamId,
    params,
  });
  return res;
}

export async function getTodoById(id, corpId) {
  const res = await useToDo({ type: "getTodoById", id, corpId });
  return res;
}
export async function setTodoExecutor(id, executorUserId, corpId) {
  const res = await useToDo({
    type: "setTodoExecutor",
    id,
    executorUserId,
    corpId,
  });
  return res;
}
export async function setTodoStatus(id, eventStatus, result, userId) {
  const res = await useToDo({
    type: "setTodoStatus",
    id,
    eventStatus,
    result,
    userId,
  });
  return res;
}
export async function getNumber(params) {
  const res = await useToDo({
    type: "statisticsEvents",
    params,
  });
  return res;
}

export async function updateTodo(id, params) {
  const res = await useToDo({ type: "updateEvent", id, params });
  return res;
}

export async function batchAddTodo(todos) {
  const res = await useToDo({ type: "batchAddEvent", todos });
  return res;
}

export async function createEvents(params) {
  const res = await useToDo({ type: "createEvents", params });
  return res;
}

export async function statisticsEventsByTeamId(teamId) {
  const res = await useToDo({
    type: "statisticsEventsByTeamId",
    corpId: localStorage.getItem("corpId"),
    teamId,
  });
  return res;
}
export async function statisticsUserEventsByTeamId(teamId) {
  const res = await useToDo({
    type: "statisticsUserEventsByTeamId",
    corpId: localStorage.getItem("corpId"),
    teamId,
  });
  return res;
}
// 获取服务记录
export async function getServiceRecord(page, pageSize, params, queryType = "", teamId) {
  const res = await useToDo({
    type: "getServiceRecord",
    page,
    pageSize,
    params,
    queryType,
    teamId,
  });
  return res;
}
// 获取服务记录
export async function getCustomerServiceRecord(data) {
  const res = await useToDo({
    type: "getCustomerServiceRecord",
    ...data,
  });
  return res;
}
// 获取成员最后一次服务时间
export async function getLastServiceTime(customerId, userIds) {
  const res = await useToDo({
    type: "getLastServiceTime",
    customerId,
    userIds,
  });
  return res;
}

//新增服务记录

export async function addServiceRecord(params) {
  const res = await useToDo({
    type: "addServiceRecord",
    ...params,
  });
  return res;
}

// 更新服务记录
export async function updateServiceRecord(id, params) {
  const res = await useToDo({
    type: "updateServiceRecord",
    id,
    params,
  });
  return res;
}

// 删除服务记录
export async function removeRecord(id) {
  const res = await useToDo({
    type: "removeServiceRecord",
    id,
  });
  return res;
}

export async function getServiceRecordCountToday(data = {}) {
  const res = await useToDo({
    type: "getServiceRecordCountToday",
    corpId: localStorage.getItem("corpId"),
    userId: localStorage.getItem("userId"),
    teamId: teamStore().currentTeamId,
    ...data,
  });
  return res;
}

export async function getTodayServiceCustomerCount(data = {}) {
  const res = await useToDo({
    type: "getTodayServiceCustomerCount",
    corpId: localStorage.getItem("corpId"),
    userId: localStorage.getItem("userId"),
    teamId: teamStore().currentTeamId,
    ...data,
  });
  return res;
}

export async function createTodo(params) {
  const res = await useToDo({
    type: "createTodo",
    params,
  });
  return res;
}

export async function getMemberIsTodoAndPlan(memberId, teamId) {
  const res = await useToDo({
    type: "getMemberIsTodoAndPlan",
    corpId: localStorage.getItem("corpId"),
    memberId,
    teamId,
  });
  return res;
}

// 批量修改待办
export async function batchUpdateTodo({ customerIds, params, currentTeamId }) {
  const res = await useToDo({
    type: "batchUpdateTodo",
    corpId: localStorage.getItem("corpId"),
    customerIds,
    currentTeamId,
    params,
  });
  return res;
}

// 批量更新管理任务
export async function batchUpdateManageTask({ customerIds, params, currentTeamId }) {
  const res = await useToDo({
    type: "batchUpdateManageTask",
    corpId: localStorage.getItem("corpId"),
    customerIds,
    currentTeamId,
    params,
  });
  return res;
}

// 批量更新任务和管理计划

export async function batchUpdateToDoAndManagePlan({ customerIds, operationType, currentTeamId, executorUserId }) {
  const res = await useToDo({
    type: "batchUpdateToDoAndManagePlan",
    corpId: localStorage.getItem("corpId"),
    customerIds,
    currentTeamId,
    operationType,
    executorUserId,
  });
  return res;
}

export async function getTodoEventsByPannedEventId(params) {
  const { pageSize, page, pannedEventId } = params;
  const res = await useToDo({
    type: "getTodoEventsByPannedEventId",
    pannedEventId,
    page,
    pageSize,
  });
  return res;
}

export async function getCustomerDayServiceCount(params) {
  const res = await useToDo({
    type: "getCustomerDayServiceCount",
    ...params,
  });
  return res;
}

export async function updatePlannedExecutionTime(params) {
  const res = await useToDo({
    type: "updatePlannedExecutionTime",
    ...params,
  });
  return res;
}

export async function getCorpServicePointsList(params) {
  const res = await useToDo({
    type: "getCorpServicePointsList",
    ...params,
  });
  return res;
}

export async function getServicePointsStatistics(params) {
  const res = await useToDo({
    type: "getServicePointsStatistics",
    corpId: localStorage.getItem("corpId"),
    ...params,
  });
  return res;
}

export async function customerTransferRecord(data) {
  const res = await useToDo({ type: "customerTransferRecord", ...data });
  return res;
}
