<template>
  <el-drawer :model-value="props.visible" :width="width" size="50%" title="治疗记录" style="--el-drawer-padding-primary: 0" @close="close()">
    <el-scrollbar>
      <el-form class="p-15px">
        <el-row>
          <el-col :span="12">
            <el-form-item class="mb-15px" label="治疗项目:" label-width="120">
              {{ form.projectName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item class="mb-15px" label="治疗数:" label-width="120">
              {{ form.deductUsageCount }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item class="mb-15px" label="治疗科室:" label-width="120">
              {{ form.treatmentDeptName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item class="mb-15px" label="治疗医生:" label-width="120">
              <ww-user :openid="form.treatmentDoctorUserId" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item class="mb-15px" label="划扣方式:" label-width="120">
              {{ form.deductType }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item class="mb-15px" label="划扣人:" label-width="120">
              <ww-user :openid="form.deductOperator" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item class="mb-15px" label="治疗时间:" label-width="120">
              <div>
                {{ form.treatmentTime ? dayjs(form.treatmentTime).format("YYYY-MM-DD") : "" }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item class="mb-15px" label="下次治疗时间:" label-width="120">
              <el-date-picker v-model="form.nextTreatmentTime" placeholder="请选择下次治疗时间" @change="selectNextTreatmentTime" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item class="mb-15px" label="配台:" label-width="120">
          <div v-if="operateType === 'read'" class="flex">
            <div v-for="(userId, index) in form.assistantDoctors" :key="userId" :label="userId" :value="userId">
              <ww-user :openid="userId"></ww-user>
              <span v-if="form.assistantDoctors.length - 1 !== index">、</span>
            </div>
          </div>
          <el-select v-else v-model="form.assistantDoctors" multiple class="w-full el-select--hiddenValue" placeholder="请选择配台" @change="editDoctors">
            <template #prefix>
              <div class="h-30px flex" color-666>
                <div v-for="(i, index) in form.assistantDoctors">
                  <ww-user :openid="i"></ww-user>
                  <span v-if="form.assistantDoctors.length - 1 !== index">、</span>
                </div>
              </div>
            </template>
            <el-option-group label="">
              <el-option v-for="userId in deptDoctors" :key="userId" :label="userId" :value="userId">
                <ww-user :openid="userId"></ww-user>
              </el-option>
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item class="mb-15px" label="治疗备注:" label-width="120">
          <div v-if="operateType === 'read'">{{ form.treatmentRemark }}</div>
          <el-input v-else type="textarea" v-model="form.treatmentRemark" :autosize="{ minRows: 5, maxRows: 8 }" resize="none" :maxlength="2000" show-word-limit placeholder="请输入治疗备注内容" @input="editTreatmentRemark" />
        </el-form-item>
        <el-form-item class="mb-15px" label="治疗照片:" label-width="120">
          <div class="flex-grow flex flex-wrap -mb-10px -mr-10px">
            <div v-for="image in imageList" :key="image.key" v-loading="uploadLoading[image.key]" element-loading-text="正在上传" class="relative w-100px mr-10px mb-10px text-center">
              <img class="h-80px mx-auto cursor-pointer" :src="image.href || image.url" @click="previewImage(image.href || image.url)" />
              <div class="text-14px truncate mt-10px pb-5px text-gray-500" :title="image.name">{{ image.name }}</div>
              <div v-if="image.error" class="text-12px text-red-500 mt-10px">上传失败, 请重试</div>
              <el-icon class="absolute -right-5px -top-5px text-red-500 bg-white rounded-full cursor-pointer" @click="removeImage(idx)" v-if="operateType !== 'read'">
                <CircleCloseFilled />
              </el-icon>
            </div>
            <el-upload multiple action="#" accept="image/*" :show-file-list="false" list-type="picture" :auto-upload="false" @change="changeFile" v-if="operateType !== 'read'">
              <template #trigger>
                <el-button plain size="small" type="primary" class="w-100px h-100px">
                  <el-icon class="text-20px">
                    <Plus />
                  </el-icon>
                </el-button>
              </template>
            </el-upload>
          </div>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div text-center common-shadow--r class="z-1 relative py-10px" v-if="operateType !== 'read'">
        <el-button plain class="w-100px" type="primary" @click="close()">取消</el-button>
        <el-button class="w-100px" :loading="loading" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-drawer>
  <el-image-viewer v-if="showViewer" :url-list="previewList" @close="cancelPreview" />
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import { ElMessage } from "element-plus";
import { updateTreatmentRecord } from "@/api/treatment-record";
import { updateFile } from "@/api/uploadFIle.js";
import { staffStore } from "@/store/staff";

import wwUser from "@/components/ww-user/index.vue";

const emits = defineEmits(["close", "success"]);
const props = defineProps({
  project: { type: Object, default: () => ({}) },
  visible: { type: Boolean, default: false },
  width: { type: Number, default: 600 },
  operateType: { type: String, default: "" },
});
const { staffList } = storeToRefs(staffStore());
const form = ref({});
const editForm = ref({});
const previewList = ref([]);
const imageList = ref([]);
const uploadLoading = ref({});
const showViewer = ref(false);

function changeFile({ raw: file, name, uid: key, url }) {
  imageList.value.push({ name, key, url, file });
}

function removeImage(idx) {
  imageList.value.splice(idx, 1);
}

function previewImage(url) {
  showViewer.value = true;
  previewList.value = [url];
}
function cancelPreview() {
  showViewer.value = false;
}

function close() {
  emits("close");
}
const loading = ref(false);
async function confirm() {
  loading.value = true;
  await uploadImages();
  if (imageList.value.some((item) => item.error || item.file)) return (loading.value = false);

  const query = {
    id: props.project._id,
    params: {
      ...editForm.value,
      pics: imageList.value.map((i) => ({ name: i.name, href: i.href })),
    },
  };
  let { success } = await updateTreatmentRecord(query);
  if (success) {
    ElMessage.success("更新成功");
    emits("success");
    emits("close");
  }
  loading.value = false;
}
const deptDoctors = computed(() => {
  return getDoctors(props.project);
});

function getDoctors(project) {
  if (!props.project.treatmentDept_id) return [];
  let userIds =
    staffList.value && Array.isArray(staffList.value)
      ? staffList.value
          .filter((item) => {
            if (item.deptIds && Array.isArray(item.deptIds)) {
              return item.deptIds.includes(project.treatmentDept_id);
            } else {
              return false;
            }
          })
          .map((item) => {
            return item.userid;
          })
      : [];
  return userIds;
}

function editDoctors(value) {
  editForm.value.assistantDoctors = value;
  console.log(editForm.value);
}

function selectNextTreatmentTime(value) {
  editForm.value.nextTreatmentTime = value;
}

function editTreatmentRemark(value) {
  editForm.value.treatmentRemark = value;
  console.log(editForm.value);
}

function editTreatementTime(value) {
  editForm.value.treatmentTime = value;
}

async function uploadImages() {
  const m = new Map();
  for (let item of imageList.value) {
    if (item.file) {
      uploadLoading.value[item.key] = true;
      try {
        const res = await updateFile(`${+new Date()}_${Math.ceil(Math.random() * 100000)}_${item.name}`, item.file, localStorage.getItem("corpId"));
        if (res.download_url) {
          m.set(item.key, { name: item.name, href: res.download_url, key: item.key });
        } else {
          m.set(item.key, { ...item, error: true });
        }
      } catch (e) {
        m.set(item.key, { ...item, error: true });
      }
      uploadLoading.value[item.key] = false;
    } else {
      m.set(item.key, { name: item.name, href: item.href, key: item.key });
    }
  }
  imageList.value = imageList.value.map((i) => m.get(i.key));
}

watch(
  () => props.visible,
  (n) => {
    if (n) {
      form.value = { ...props.project };
      editForm.value = {};
      imageList.value = Array.isArray(props.project.pics)
        ? props.project.pics.map((i, idx) => ({
            name: i.name,
            href: i.href,
            key: `pic_${Date.now()}_${idx}`,
          }))
        : [];
    }
  }
);
</script>
<style lang="scss">
.el-select.el-select--hiddenValue .el-select__tags > span {
  display: none;
}

.el-select.el-select--hiddenValue .el-input__prefix {
  flex-grow: 1;
}
</style>