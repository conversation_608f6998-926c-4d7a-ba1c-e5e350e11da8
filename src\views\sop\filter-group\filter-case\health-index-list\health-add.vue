<template>
  <div class="flex-shrink-0 mx-5px text-gray-500">包含任意</div>
  <el-button text type="primary" size="small" class="flex-shrink-0" style="margin-left: 5px;" @click="add()">
    <el-icon class="mr-3px">
      <Plus />
    </el-icon>
    新增筛选
  </el-button>
</template>
<script setup>
import { useVModel } from '@vueuse/core';

const emit = defineEmits(['update:modelValue'])
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})
const data = useVModel(props, 'modelValue', emit);

function add() {
  if (Array.isArray(data.value)) {
    data.value = [...data.value, { key: `${Date.now()}_${Math.floor(Math.random() * 1000)}` }]
  } else {
    data.value = [{ key: `${Date.now()}_${Math.floor(Math.random() * 1000)}` }]
  }
}

</script>
<style lang="scss" scoped></style>
