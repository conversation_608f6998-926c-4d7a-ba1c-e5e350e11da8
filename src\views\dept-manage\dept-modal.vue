<template>
  <el-dialog :model-value="visible" :width="width" :title="`${dept._id ? '编辑' : '新增'}科室`" @close="close()">
    <el-scrollbar wrap-class="el-dialog-body-full-scroll">
      <el-form class="p-15px" label-suffiix="：" :label-width="100" label-suffix="：">
        <div v-if="!dept._id && dept.parentId" color-normal class="mb-15px pl-10px text-14px font-semibold">
          在【{{dept.parentName}}】下方新增科室
        </div>
        <el-form-item class="is-required" label="科室名称">
          <el-input v-model.trim="form.deptName" :maxlength="10" placeholder="请输入科室名称" />
        </el-form-item>
        <el-form-item class="is-required" label="科室ID">
          <div class="flex-grow">
            <el-input v-model="form.deptId" placeholder="请输入科室ID" />
            <div class="text-12px text-gray-500 mt-12px">科室ID可以为数字或者字母组合，请勿重复</div>
          </div>
        </el-form-item>
        <el-form-item label="科室介绍">
          <el-input type="textarea" v-model="form.deptDesc" placeholder="请输入科室介绍" maxlength="500"
            :autosize="{ minRows: 6, maxRows: 10 }" resize="none" show-word-limit></el-input>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button plain class="w-100px" type="primary" :icon="Plus" @click="close()">取消</el-button>
        <el-button class="w-100px" :loading="loading" type="primary" :icon="Plus" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { addDept, updateDept } from "@/api/dept-manage";

const emits = defineEmits(["close", "change"]);
const props = defineProps({
  dept: { type: Object, default: () => ({}) },
  visible: { type: Boolean, default: false },
  width: { type: [String, Number], default: "500px" },
});
const form = ref({});
const loading = ref(false);
function close() {
  emits("close");
}

function confirm() {
  if (loading.value) return;
  if (form.value.deptName.trim() === '') {
    ElMessage.info("科室名称不能为空");
  } else if (form.value.deptId.trim() === '') {
    ElMessage.info("科室ID不能为空");
  } else if (!/^[a-zA-Z0-9]+$/.test(form.value.deptId.trim())) {
    ElMessage.info("科室ID只能由数字或者字母组合");
  }
  loading.value = true;
  if (props.dept._id) {
    update()
  } else {
    add()
  }
  loading.value = false;
}

async function add() {
  const payload = {
    corpId: localStorage.getItem('corpId'),
    params: { ...form.value },
  }
  if (props.dept.parentId) {
    payload.parentId = props.dept.parentId
  }
  const { success, message } = await addDept(payload);
  if (success) {
    ElMessage.success(message);
    emits('change')
    close()
  } else {
    ElMessage.error(message);
  }
}

async function update() {
  const payload = {
    corpId: localStorage.getItem('corpId'),
    id: props.dept._id,
    params: { ...form.value },
  }
  const { success, message } = await updateDept(payload);
  if (success) {
    ElMessage.success(message);
    emits('change', payload)
    close()
  } else {
    ElMessage.error(message);
  }
}

watch(
  () => props.visible,
  (n) => {
    if (n) {
      form.value.deptName = typeof props.dept.deptName === "string" ? props.dept.deptName.trim() : "";
      form.value.deptId = typeof props.dept.deptId === "string" ? props.dept.deptId.trim() : "";
      form.value.deptDesc = typeof props.dept.deptDesc === "string" ? props.dept.deptDesc.trim() : "";
    }
  }
);
</script>