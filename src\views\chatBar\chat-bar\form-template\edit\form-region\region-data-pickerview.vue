<template>
  <div class="uni-data-pickerview">
    <div class="h-50px">
      <el-scrollbar class="selected-area flex-grow-0">
        <div class="selected-list">
          <div class="selected-item" v-for="(item, index) in selected" :key="index" :class="{
            'selected-item-active': index == selectedIndex
          }" @click="handleSelect(index)">
            <text>{{ item.name || '请选择' }}</text>
          </div>
        </div>
      </el-scrollbar>
    </div>
    <div class="tab-c">
      <el-scrollbar class="list" :scroll-y="true">
        <div class="item" :class="{ 'is-disabled': !!item.disable }" v-for="item in dataList[selectedIndex]"
          :key="item.code" @click="handleNodeClick(item, selectedIndex)">
          <text class="item-text">{{ item.name }}</text>
          <div v-if="selectMap[item.code]" class="check"></div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { dbStore } from '@/store/db';

const db = dbStore();
const { province, area } = storeToRefs(db);
const { getArea } = db;

const emits = defineEmits(['change'])

const selected = ref([{}]);
const selectedIndex = ref(0);
const selectMap = computed(() => selected.value.reduce((val, i) => {
  val[i.code] = true;
  return val
}, {}))
const dataList = ref([])
onMounted(async () => {
  if (province.value.length === 0) await getArea();
  dataList.value[0] = [...province.value];
})

function handleSelect(index) {
  selectedIndex.value = index;
}

async function handleNodeClick(item, idx) {
  if (selected.value[idx].code === item.code) return;
  selected.value = selected.value.slice(0, idx + 1);
  dataList.value = dataList.value.slice(0, idx + 1);
  selected.value[idx] = { ...item };
  if (area.value[item.code] === undefined) await getArea(item.code);
  if (Array.isArray(area.value[item.code]) && area.value[item.code].length) {
    selected.value[idx + 1] = {};
    selectedIndex.value = idx + 1;
    dataList.value[idx + 1] = [...area.value[item.code]]
  }
  emits('change', { selected: selected.value.filter(i => i.name), current: { ...item } })
}
</script>

<style lang="scss">
$uni-primary: #007aff !default;

.uni-data-pickerview {
  flex: 1;
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-direction: column;
  overflow: hidden;
  height: 100%;
}

.error-text {
  color: #DD524D;
}

.loading-cover {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, .5);
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-direction: column;
  align-items: center;
  z-index: 1001;
}

.load-more {
  /* #ifndef APP-NVUE */
  margin: auto;
  /* #endif */
}

.error-message {
  background-color: #fff;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  padding: 15px;
  opacity: .9;
  z-index: 102;
}

/* #endif */

.selected-list {
  /* #ifndef APP-NVUE */
  display: flex;
  flex-wrap: nowrap;
  /* #endif */
  flex-direction: row;
  padding: 0 5px;
  border-bottom: 1px solid #f8f8f8;
}

.selected-item {
  margin-left: 10px;
  margin-right: 10px;
  padding: 12px 0;
  text-align: center;
  /* #ifndef APP-NVUE */
  white-space: nowrap;
  /* #endif */
}

.selected-item-text-overflow {
  width: 168px;
  /* fix nvue */
  overflow: hidden;
  /* #ifndef APP-NVUE */
  width: 6em;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  /* #endif */
}

.selected-item-active {
  border-bottom: 2px solid $uni-primary;
}

.selected-item-text {
  color: $uni-primary;
}

.tab-c {
  position: relative;
  flex: 1;
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-direction: row;
  overflow: hidden;
}

.list {
  flex: 1;
}

.item {
  padding: 12px 15px;
  /* border-bottom: 1px solid #f0f0f0; */
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-direction: row;
  justify-content: space-between;
}

.is-disabled {
  opacity: .5;
}

.item-text {
  /* flex: 1; */
  color: #333333;
}

.item-text-overflow {
  width: 280px;
  /* fix nvue */
  overflow: hidden;
  /* #ifndef APP-NVUE */
  width: 20em;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  /* #endif */
}

.check {
  margin-right: 5px;
  border: 2px solid $uni-primary;
  border-left: 0;
  border-top: 0;
  height: 12px;
  width: 6px;
  transform-origin: center;
  /* #ifndef APP-NVUE */
  transition: all 0.3s;
  /* #endif */
  transform: rotate(45deg);
}
</style>
