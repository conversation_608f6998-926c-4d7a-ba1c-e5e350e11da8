<template>
  <div class="report-container">
    <div class="report-header">
      <div class="report-actions">
        <div class="filter-section">
          <div class="filter-row">
            <div class="filter-item">
              <span class="filter-label">消费日期:</span>
              <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :shortcuts="dateShortcuts" :disabled-date="disabledDate" @change="handleDateChange" class="date-range" />
            </div>
            <div class="filter-item">
              <span class="filter-label">客户ID：</span>
              <el-input v-model="clientIdFilter" placeholder="客户ID" clearable />
            </div>

            <div class="filter-item">
              <span class="filter-label">客户姓名：</span>
              <el-input v-model="clientNameFilter" placeholder="客户姓名" clearable />
            </div>

            <div class="filter-item">
              <span class="filter-label">手机号：</span>
              <el-input v-model="phoneFilter" placeholder="全号或后四位" clearable @input="handlePhoneFilterInput" />
            </div>

            <div class="filter-item">
              <check-box-filter v-model="consultantFilter" label="咨询师" :list="consultantList">
                <template #item="{ item }">
                  <ww-user :openid="item.value"></ww-user>
                </template>
              </check-box-filter>
            </div>

            <div class="filter-item">
              <check-box-filter v-model="developerFilter" label="开发人员" :list="developerList" @clear="clearDevelopers" v-if="isManager">
                <template #item="{ item }">
                  <ww-user :openid="item.value"></ww-user>
                </template>
              </check-box-filter>
            </div>

            <div class="filter-item">
              <base-filter-item :clearable="projectCategories.length > 0" class="cursor-pointer" label="项目分类" :text="categoryText" @clear="clearCategories" @onClick="showCategoryPicker()" />
            </div>

            <div class="filter-item">
              <base-filter-item :clearable="projectFilter.length > 0" class="cursor-pointer" label="开单项目" :text="projectText" @clear="clearProjects" @onClick="showProjectModal()" />
            </div>

            <div class="filter-item">
              <base-filter-item :clearable="deptFilter.length > 0" class="cursor-pointer" label="开单科室" :text="deptText" @clear="clearDepts" @onClick="showDeptPicker()" />
            </div>

            <div class="filter-item">
              <filter-info-source v-model="infoSourceFilter" label="信息来源" />
            </div>

            <div class="filter-item">
              <filter-customer-source v-model="devPathFilter" label="开发渠道" />
            </div>

            <div class="filter-item">
              <check-box-filter v-model="consultStageFilter" label="接诊类型" :list="ConsultStage" />
            </div>

            <div class="filter-item">
              <span class="filter-label">项目类型：</span>
              <el-select v-model="itemTypeFilter" placeholder="项目类型" clearable>
                <el-option v-for="item in ProjectType" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
            <div class="filter-actions">
              <el-button type="primary" @click="handleSearch" :loading="loading" :disabled="loading">查询</el-button>
              <el-button type="success" @click="exportToExcel" :disabled="loading">导出Excel</el-button>
              <el-button @click="resetFilters" :disabled="loading">重置</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="table-wrapper">
      <div class="table-container" v-loading="loading" element-loading-text="正在获取数据...">
        <table class="medical-table">
          <colgroup>
            <col style="width: 80px" />
            <col style="width: 100px" />
            <col style="width: 70px" />
            <col style="width: 90px" />
            <col style="width: 80px" />
            <col style="width: 90px" />
            <col style="width: 90px" />
            <col style="width: 90px" />
            <col style="width: 90px" />
            <col style="width: 90px" />
            <col style="width: 120px" />
            <col style="width: 70px" />
            <col style="width: 150px" />
            <col style="width: 80px" />
            <col style="width: 100px" />
            <col style="width: 80px" />
            <col style="width: 80px" />
            <col style="width: 90px" />
            <col style="width: 90px" />
            <col style="width: 90px" />
            <col style="width: 90px" />
          </colgroup>
          <thead>
            <tr>
              <th>开发人员</th>
              <th>客户门诊ID</th>
              <th>客户姓名</th>
              <th>开单科室</th>
              <th>开单医生</th>
              <th>项目类型</th>
              <th>项目类别1</th>
              <th>项目类别2</th>
              <th>项目类别3</th>
              <th>项目类别4</th>
              <th>项目名称</th>
              <th>购买数量</th>
              <th>金额</th>
              <th>咨询师</th>
              <th>消费日期</th>
              <th>订单类型</th>
              <th>接诊类型</th>
              <th>信息来源</th>
              <th>开发渠道1</th>
              <th>开发渠道2</th>
              <th>开发渠道3</th>
            </tr>
          </thead>
          <tbody>
            <template v-for="(group, groupIndex) in groupedData" :key="groupIndex">
              <template v-for="(client, clientIndex) in group.clients" :key="clientIndex">
                <template v-for="(item, itemIndex) in client.items" :key="itemIndex">
                  <tr>
                    <td v-if="clientIndex === 0 && itemIndex === 0" :rowspan="group.rowspan">{{ group.developerName }}</td>
                    <td v-if="itemIndex === 0" :rowspan="client.items.length">{{ client.clientId }}</td>
                    <td v-if="itemIndex === 0" :rowspan="client.items.length">{{ client.clientName }}</td>
                    <td>{{ item.department }}</td>
                    <td>{{ item.doctor }}</td>
                    <td>{{ item.itemType }}</td>
                    <td>
                      <span v-if="shouldShowCategories">{{ item.category1 }}</span>
                    </td>
                    <td>
                      <span v-if="shouldShowCategories">{{ item.category2 }}</span>
                    </td>
                    <td>
                      <span v-if="shouldShowCategories">{{ item.category3 }}</span>
                    </td>
                    <td>{{ item.category4 }}</td>
                    <td>{{ item.itemName }}</td>
                    <td>{{ item.quantity }}</td>
                    <td class="amount">{{ formatCurrency(item.amount) }}</td>
                    <td>{{ item.consultantName }}</td>
                    <td>{{ item.consumeDate }}</td>
                    <td>{{ item.orderType }}</td>
                    <td>{{ item.receptionType }}</td>
                    <td>{{ item.infoSource1 }}</td>
                    <td>{{ item.devPath1 }}</td>
                    <td>{{ item.devPath2 }}</td>
                    <td>{{ item.devPath3 }}</td>
                  </tr>
                </template>
              </template>
              <tr class="subtotal-row">
                <td :colspan="11" class="subtotal-label">小计：</td>
                <td>{{ group.totalQuantity }}</td>
                <td class="amount">{{ formatCurrency(group.totalAmount) }}</td>
                <td :colspan="10"></td>
              </tr>
            </template>
            <tr class="total-row">
              <td :colspan="11" class="total-label">合计：</td>
              <td>{{ totalQuantity }}</td>
              <td class="amount">{{ formatCurrency(totalAmount) }}</td>
              <td :colspan="10"></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="footer-info">
      <span>共 {{ loading ? '...' : totalItems }} 条记录</span>
      <span class="date-limit-info">* 查询限制为一个月内的数据</span>
    </div>
  </div>
  <project-list-picker :value="projectFilter" :visible="projectModalVisible" :width="projectModalWidth" :current-category="projectCategories" @close="closeProjectModal()" @change="changeProjects" />
  <dept-picker-modal ref="deptPickerRef" :value="deptIds" :visible="deptVisible" :width="deptPickerWidth" @change="deptChange" @close="closeDeptPicker" />
  <category-picker ref="categoryPickerRef" :value="projectCategories" :visible="categoryPickerVisible" :width="categoryPickerWidth" @close="closeCategoryPicker" @change="changeCategories" @categoryChange="handleCategoryChange" />
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { ElDatePicker, ElSelect, ElOption, ElButton, ElMessage, ElInput } from "element-plus";
import { getBillRecordsWithMemberDetails } from "@/api/bill-record";
import { ProjectType, ConsultStage } from "@/baseData";
import useModal from "@/hooks/useModal";
import { CheckBoxFilter, baseFilterItem, filterCustomerSource, filterInfoSource } from "@/components/filter-bar";
import { storeToRefs } from "pinia";
import projectListPicker from "@/components/project-picker/project-list-picker.vue";
import deptPickerModal from "@/components/dept-picker-modal/dept-picker-modal.vue";
import categoryPicker from "@/components/project-picker/category-picker.vue";
import { staffStore } from "@/store/staff";
import { memberStore } from "@/store/member";
import { teamStore } from "@/store/team";

const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());
const { isAdmin } = storeToRefs(memberStore());
const { managerList } = storeToRefs(teamStore());
const { judgmentIsAdmin } = memberStore();
const categoryPickerRef = ref(null);

const isManager = computed(() => {
  return managerList.value.includes(90002) || isAdmin.value;
});

const dateType = ref("consume");
const dateRange = ref([]);
const clientIdFilter = ref("");
const clientNameFilter = ref("");
const phoneFilter = ref("");
const receptionStatusFilter = ref("");
const itemNameFilter = ref("");
const departmentFilter = ref("");
const itemTypeFilter = ref("");
const infoSourceFilter = ref([]);
const devPathFilter = ref([]);
const projectCategories = ref([]);
const { close: closeCategoryPicker, show: showCategoryPicker, visible: categoryPickerVisible, width: categoryPickerWidth } = useModal(600);
const currentCategory = ref({});

const categoryText = computed(() => {
  if (projectCategories.value.length === 0) return "";
  if (projectCategories.value.length > 1) return `已选择${projectCategories.value.length}个分类`;
  return projectCategories.value[0].name;
});

const projectFilter = ref([]);
const { close: closeProjectModal, show: showProjectModal, visible: projectModalVisible, width: projectModalWidth } = useModal(800);
const projectText = computed(() => {
  if (projectFilter.value.length === 0) return "";
  if (projectFilter.value.length > 1) return `已选择${projectFilter.value.length}个项目`;
  return projectFilter.value[0].projectName;
});

function clearCategories() {
  projectCategories.value = [];
  if (categoryPickerRef.value) {
    categoryPickerRef.value.clear();
  }
}

function changeCategories(data) {
  projectCategories.value = data.categories;
  console.log("当前分类:", projectCategories.value);
  projectFilter.value = [];
}

function handleCategoryChange(category) {
  currentCategory.value = category;
  projectFilter.value = [];
}

function changeProjects(data) {
  projectFilter.value = data.projects;
  closeProjectModal();
}

const selectedDevelopers = ref([]);
const hasExplicitlyClearedDevelopers = ref(false);

const developerFilter = computed({
  get() {
    if (selectedDevelopers.value.length > 0) {
      return selectedDevelopers.value;
    }
    if (hasExplicitlyClearedDevelopers.value) {
      return [];
    }
    const currentUserId = localStorage.getItem("userId");
    if (!isManager.value) {
      // 不是主管，只能看到自己
      return [currentUserId];
    } else {
      // 主管
      const inList = developerList.value.some((i) => i.value === currentUserId);
      if (inList) {
        return [currentUserId];
      } else {
        // 展示全部
        return [];
      }
    }
  },
  set(newVal) {
    selectedDevelopers.value = newVal;
    hasExplicitlyClearedDevelopers.value = false;
  },
});

function clearDevelopers() {
  selectedDevelopers.value = [];
  hasExplicitlyClearedDevelopers.value = true;
}

const consultantFilter = ref([]);
const consultantList = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("consultant")).map((i) => ({ value: i.userid }));
});
const developerList = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("onlineCustomerService")).map((i) => ({ value: i.userid }));
});

const { close: closeDeptPicker, show: showDeptPicker, visible: deptVisible, width: deptPickerWidth } = useModal(640);
const deptFilter = ref([]);
const deptIds = computed(() => deptFilter.value.map((i) => i._id));
const deptText = computed(() => {
  if (deptFilter.value.length === 0) return "";
  if (deptFilter.value.length > 1) return `已选择${deptFilter.value.length}个项目`;
  return deptFilter.value[0].deptName;
});
function deptChange(val) {
  deptFilter.value = val;
  closeDeptPicker();
}
function clearDepts() {
  deptFilter.value = [];
}

function clearProjects() {
  projectFilter.value = [];
  projectCategories.value = [];
}

const dateShortcuts = [
  {
    text: "今天",
    value: () => {
      const today = new Date();
      return [today, today];
    },
  },
  {
    text: "昨天",
    value: () => {
      const today = new Date();
      const yesterday = new Date();
      yesterday.setDate(today.getDate() - 1);
      return [yesterday, yesterday];
    },
  },
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近半个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
];

const rawData = ref([]);
const consultStageFilter = ref([]);
// 添加 loading 状态
const loading = ref(false);

async function handleGetBillRecordsWithMemberInfo() {
  loading.value = true;
  try {
    let params = {
      payDate: dateRange.value,
      consultantFilter: consultantFilter.value,
    };

    if (isManager.value) {
      params.developerFilter = developerFilter.value.length > 0 ? developerFilter.value : undefined;
    } else {
      params.developerFilter = [localStorage.getItem("userId")];
    }

    if (consultStageFilter.value && consultStageFilter.value.length > 0) {
      params.consultStages = consultStageFilter.value;
    }

    let { data, success, message } = await getBillRecordsWithMemberDetails(params);
    if (success) {
      rawData.value = transformBillRecords(data.data);
    } else {
      ElMessage.error(message);
    }
  } catch (error) {
    ElMessage.error('获取数据失败');
    console.error('获取数据失败:', error);
  } finally {
    loading.value = false;
  }
}

function transformBillRecords(records) {
  if (!records || !Array.isArray(records)) {
    return [];
  }
  return records.map((record) => {
    const devPath1 = record.customerSource && record.customerSource.length > 0 ? record.customerSource[0] : "-";
    const devPath2 = record.customerSource && record.customerSource.length > 1 ? record.customerSource[1] : "-";
    const devPath3 = record.customerSource && record.customerSource.length > 2 ? record.customerSource[2] : "-";

    const infoSource1 = record.consultSource && Array.isArray(record.consultSource) ? record.consultSource.join("、") : record.consultSource || "-";

    const consultant = record.counselorUserId || "-";

    const doctor = record.billDoctorId || "-";
    const doctorName = staffList.value.find((staff) => staff.userid === doctor)?.anotherName || doctor;
    let developer = record.introducerUserId || "-";

    let category1 = "-";
    let category2 = "-";
    let category3 = "-";
    let category4 = "-";

    if (record.projectCates && Array.isArray(record.projectCates)) {
      const cat1 = record.projectCates.find((cat) => cat.level === 1);
      const cat2 = record.projectCates.find((cat) => cat.level === 2);
      const cat3 = record.projectCates.find((cat) => cat.level === 3);
      const cat4 = record.projectCates.find((cat) => cat.level === 4);

      category1 = cat1 ? cat1.label : "-";
      category2 = cat2 ? cat2.label : "-";
      category3 = cat3 ? cat3.label : "-";
      category4 = cat4 ? cat4.label : "-";
    }

    const consultStageLabel = record.consultStage ? ConsultStage.find((stage) => stage.value === record.consultStage)?.label || record.consultStage : "普通";
    const developerName = staffList.value.find((staff) => staff.userid === developer)?.anotherName || "-";
    const consultantName = staffList.value.find((staff) => staff.userid === consultant)?.anotherName || "";

    return {
      developer,
      developerName,
      clientId: record.customerNumber || "-",
      clientName: record.customerName || "-",
      clientPhone: record.customerPhone,
      clientPhone1: record.customerPhone1,
      clientPhone2: record.clientPhone2,
      clientPhone3: record.clientPhone3,
      department: record.billDeptName,
      doctor: doctor,
      doctorName: doctorName,
      consultant: consultant,
      consultantName,
      itemType: record.projectType || "-",
      category1: category1,
      category2: category2,
      category3: category3,
      category4: category4,
      itemName: record.projectName || "-",
      quantity: 1,
      amount: record.totalPrice || 0,
      consumeDate: formatDateFromTimestamp(record.consumeTime || record.createTime),
      consultDate: formatDateFromTimestamp(record.createTime),
      orderType: record.billType || "-",
      receptionType: consultStageLabel,
      infoSource1: infoSource1,
      devPath1: devPath1,
      devPath2: devPath2,
      devPath3: devPath3,
    };
  });
}

function formatDateFromTimestamp(timestamp) {
  if (!timestamp) return "-";

  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");

  return `${year}-${month}-${day}`;
}

const disabledDate = (time) => {
  if (!dateRange.value || dateRange.value.length === 0) {
    return false;
  }

  const startDate = new Date(dateRange.value[0]);
  const endDate = new Date(dateRange.value[1]);
  const currentDate = new Date(time);

  if (dateRange.value.length === 1) {
    const oneMonthLater = new Date(startDate);
    oneMonthLater.setDate(startDate.getDate() + 31);
    return currentDate > oneMonthLater;
  }

  return false;
};

watch(dateRange, (newValue) => {
  if (newValue && newValue.length === 2) {
    const start = new Date(newValue[0]);
    const end = new Date(newValue[1]);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays > 31) {
      const maxEnd = new Date(start);
      maxEnd.setDate(start.getDate() + 31);
      const formattedMaxEnd = formatDate(maxEnd);

      dateRange.value = [newValue[0], formattedMaxEnd];
      ElMessage.warning("查询范围已限制为一个月内");
    }
  }
});

const receptionStatusOptions = [
  { value: "全部", label: "" },
  { value: "已接诊", label: "已接诊" },
  { value: "未接诊", label: "未接诊" },
];

const formatDate = (date) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  return `${year}-${month}-${day}`;
};

const filteredData = computed(() => {
  let result = [...rawData.value];

  if (clientIdFilter.value) {
    result = result.filter((item) => item.clientId.includes(clientIdFilter.value));
  }

  if (clientNameFilter.value) {
    result = result.filter((item) => item.clientName.includes(clientNameFilter.value));
  }

  if (phoneFilter.value) {
    result = result.filter((item) => {
      const phones = [item.clientPhone, item.clientPhone1, item.clientPhone2, item.clientPhone3].filter(Boolean);
      if (phoneFilter.value.length === 4) {
        return phones.some((phone) => phone && phone.endsWith(phoneFilter.value));
      } else if (phoneFilter.value.length === 11) {
        return phones.some((phone) => phone && phone.includes(phoneFilter.value));
      }
    });
  }

  if (receptionStatusFilter.value && receptionStatusFilter.value !== "全部") {
    result = result.filter((item) => item.receptionType === receptionStatusFilter.value);
  }

  if (itemNameFilter.value) {
    result = result.filter((item) => item.itemName.includes(itemNameFilter.value));
  }

  if (departmentFilter.value && departmentFilter.value !== "全部") {
    result = result.filter((item) => item.department === departmentFilter.value);
  }

  if (itemTypeFilter.value && itemTypeFilter.value !== "全部") {
    result = result.filter((item) => item.itemType === itemTypeFilter.value);
  }

  if (dateRange.value && dateRange.value.length === 2) {
    const targetDateField = "consumeDate";
    console.log("日期范围:", dateRange.value, "筛选字段:", targetDateField);
  }

  if (projectFilter.value && projectFilter.value.length > 0) {
    result = result.filter((item) => {
      return projectFilter.value.some((p) => p.projectName === item.itemName);
    });
  }

  if (deptFilter.value && deptFilter.value.length > 0) {
    result = result.filter((item) => {
      return deptFilter.value.some((d) => d.deptName === item.department);
    });
  }

  if (infoSourceFilter.value && infoSourceFilter.value.length > 0) {
    result = result.filter((item) => {
      return infoSourceFilter.value.some((filter) => item.infoSource1 === filter);
    });
  }

  if (devPathFilter.value && devPathFilter.value.length > 0) {
    result = result.filter((item) => {
      const paths = [item.devPath1, item.devPath2, item.devPath3].filter(Boolean);
      return paths.some((path) => devPathFilter.value.some((filter) => path === filter));
    });
  }

  if (projectCategories.value && projectCategories.value.length > 0) {
    let categoryObj = groupLabelsByLevel(projectCategories.value);
    result = result.filter((item) => {
      let matchesAllConditions = true;
      if (categoryObj[1] && categoryObj[1].length > 0) {
        if (!item.category1 || item.category1 === "-" || !categoryObj[1].includes(item.category1)) {
          matchesAllConditions = false;
        }
      }

      if (categoryObj[2] && categoryObj[2].length > 0) {
        if (!item.category2 || item.category2 === "-" || !categoryObj[2].includes(item.category2)) {
          matchesAllConditions = false;
        }
      }

      if (categoryObj[3] && categoryObj[3].length > 0) {
        if (!item.category3 || item.category3 === "-" || !categoryObj[3].includes(item.category3)) {
          matchesAllConditions = false;
        }
      }

      if (categoryObj[4] && categoryObj[4].length > 0) {
        if (!item.category4 || item.category4 === "-" || !categoryObj[4].includes(item.category4)) {
          matchesAllConditions = false;
        }
      }
      return matchesAllConditions;
    });
  }

  return result;
});
function groupLabelsByLevel(data) {
  return data.reduce((acc, item) => {
    const level = item.level;
    if (!acc[level]) {
      acc[level] = [];
    }
    acc[level].push(item.label);
    return acc;
  }, {});
}

const groupedData = computed(() => {
  const groups = {};
  filteredData.value.forEach((item) => {
    if (!groups[item.developer]) {
      groups[item.developer] = {
        developer: item.developer,
        developerName: item.developerName || item.developer,
        clients: {},
        totalQuantity: 0,
        totalAmount: 0,
      };
    }
    if (!groups[item.developer].clients[item.clientId]) {
      groups[item.developer].clients[item.clientId] = {
        clientId: item.clientId,
        clientName: item.clientName,
        items: [],
      };
    }
    groups[item.developer].clients[item.clientId].items.push({
      department: item.department,
      doctor: item.doctorName,
      itemType: item.itemType,
      category1: item.category1,
      category2: item.category2,
      category3: item.category3,
      category4: item.category4,
      itemName: item.itemName,
      quantity: item.quantity,
      amount: item.amount,
      consultant: item.consultant,
      consultantName: item.consultantName || item.consultant,
      consumeDate: item.consumeDate,
      consultDate: item.consultDate,
      orderType: item.orderType,
      receptionType: item.receptionType,
      infoSource1: item.infoSource1,
      devPath1: item.devPath1,
      devPath2: item.devPath2,
      devPath3: item.devPath3,
      clientPhone: item.clientPhone,
      developer: item.developer,
      developerName: item.developerName || item.developer,
    });
    groups[item.developer].totalQuantity += item.quantity;
    groups[item.developer].totalAmount += item.amount;
  });

  const result = Object.values(groups).map((group) => {
    const clients = Object.values(group.clients);
    let rowspan = 0;

    clients.forEach((client) => {
      rowspan += client.items.length;
    });

    return {
      developer: group.developer,
      developerName: group.developerName,
      clients: clients,
      totalQuantity: group.totalQuantity,
      totalAmount: group.totalAmount,
      rowspan: rowspan,
    };
  });

  return result;
});

const totalQuantity = computed(() => {
  return groupedData.value.reduce((sum, group) => sum + group.totalQuantity, 0);
});

const totalAmount = computed(() => {
  return groupedData.value.reduce((sum, group) => sum + group.totalAmount, 0);
});

const totalItems = computed(() => {
  return filteredData.value.length;
});

const formatCurrency = (value) => {
  if (value === undefined || value === null || isNaN(value)) {
    return "¥0.00";
  }
  return new Intl.NumberFormat("zh-CN", {
    style: "currency",
    currency: "CNY",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);
};

const handleDateTypeChange = () => {
  console.log("日期类型变更为:", dateType.value);
};

const handleDateChange = (val) => {
  if (val && val.length === 2) {
    const start = new Date(val[0]);
    const end = new Date(val[1]);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays > 31) {
      ElMessage.warning("查询范围已限制为一个月内");
    } else {
      handleGetBillRecordsWithMemberInfo();
    }
  }
};

const handleSearch = () => {
  if (dateRange.value && dateRange.value.length === 2) {
    const start = new Date(dateRange.value[0]);
    const end = new Date(dateRange.value[1]);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    if (diffDays > 31) {
      ElMessage.warning("查询范围已限制为一个月内");
      return;
    }
    handleGetBillRecordsWithMemberInfo();
  } else {
    ElMessage.warning("请选择日期范围");
  }
};

const resetFilters = () => {
  dateType.value = "consume";

  const today = new Date();
  const formattedToday = formatDate(today);
  dateRange.value = [formattedToday, formattedToday];

  clientIdFilter.value = "";
  clientNameFilter.value = "";
  phoneFilter.value = "";
  receptionStatusFilter.value = "";
  itemNameFilter.value = "";
  departmentFilter.value = "";
  itemTypeFilter.value = "";
  infoSourceFilter.value = [];
  devPathFilter.value = [];
  consultantFilter.value = [];
  selectedDevelopers.value = [];
  projectCategories.value = [];
  projectFilter.value = [];
  deptFilter.value = [];
  consultStageFilter.value = [];
  categoryPickerRef.value.clear();
};

const exportToExcel = () => {
  const exportData = [];

  const headers = ["开发人员", "客户门诊ID", "客户姓名", "开单科室", "开单医生", "项目类型"];

  if (shouldShowCategories.value) {
    headers.push("项目类别1", "项目类别2", "项目类别3");
  }

  headers.push("项目类别4", "项目名称", "购买数量", "金额", "咨询师", "消费日期", "咨询日期", "订单类型", "接诊类型", "信息来源", "开发渠道1", "开发渠道2", "开发渠道3");

  exportData.push(headers);

  groupedData.value.forEach((group) => {
    group.clients.forEach((client) => {
      client.items.forEach((item) => {
        const rowData = [item.developerName, client.clientId, client.clientName, item.department, item.doctor, item.itemType];

        if (shouldShowCategories.value) {
          rowData.push(item.category1, item.category2, item.category3);
        }

        rowData.push(item.category4, item.itemName, item.quantity, item.amount, item.consultantName, item.consumeDate, item.consultDate || "-", item.orderType, item.receptionType, item.infoSource1, item.devPath1, item.devPath2, item.devPath3);

        exportData.push(rowData);
      });
    });

    const subtotalRow = ["小计:"];
    const emptyColsCount = 10;
    for (let i = 0; i < emptyColsCount; i++) {
      subtotalRow.push("");
    }
    subtotalRow.push(group.totalQuantity, group.totalAmount);
    const remainingEmptyCols = headers.length - subtotalRow.length;
    for (let i = 0; i < remainingEmptyCols; i++) {
      subtotalRow.push("");
    }
    exportData.push(subtotalRow);
  });

  const totalRow = ["合计:"];
  const emptyColsCount = 9;
  for (let i = 0; i < emptyColsCount; i++) {
    totalRow.push("");
  }
  totalRow.push("", totalQuantity.value, totalAmount.value);
  const remainingEmptyCols = headers.length - totalRow.length;
  for (let i = 0; i < remainingEmptyCols; i++) {
    totalRow.push("");
  }
  exportData.push(totalRow);

  const wb = XLSX.utils.book_new();
  const ws = XLSX.utils.aoa_to_sheet(exportData);

  const colWidths = [{ wch: 10 }, { wch: 12 }, { wch: 10 }, { wch: 12 }, { wch: 10 }, { wch: 12 }, { wch: 12 }, { wch: 12 }, { wch: 12 }, { wch: 15 }, { wch: 20 }, { wch: 10 }, { wch: 12 }, { wch: 12 }, { wch: 12 }, { wch: 12 }, { wch: 10 }, { wch: 10 }, { wch: 12 }, { wch: 12 }];

  ws["!cols"] = colWidths;

  XLSX.utils.book_append_sheet(wb, ws, "开发消费明细表");

  const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
  const blob = new Blob([excelBuffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });

  const now = new Date();
  const fileName = `开发消费明细表_${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}.xlsx`;

  saveAs(blob, fileName);
  ElMessage.success("导出成功");
};

const printTable = () => {
  window.print();
};

onMounted(async () => {
  await judgmentIsAdmin();
  if (staffList.value.length === 0) await getStaffList();
  const end = new Date();
  const start = new Date(end.getFullYear(), end.getMonth(), 1);

  dateRange.value = [formatDate(start), formatDate(end)];

  handleGetBillRecordsWithMemberInfo();
});

watch(
  [consultantFilter, developerFilter, projectFilter, deptFilter, projectCategories, consultStageFilter],
  (newValue) => {
    if (dateRange.value && dateRange.value.length === 2) {
      handleGetBillRecordsWithMemberInfo();
    }
  },
  { deep: true }
);

watch(
  projectFilter,
  (newValue) => {
    if (dateRange.value && dateRange.value.length === 2) {
      console.log("开单项目筛选条件更新:", newValue);
    }
  },
  { deep: true }
);

watch(
  deptFilter,
  (newValue) => {
    if (dateRange.value && dateRange.value.length === 2) {
      console.log("开单科室筛选条件更新:", newValue);
    }
  },
  { deep: true }
);

watch(
  projectCategories,
  (newValue) => {
    if (dateRange.value && dateRange.value.length === 2) {
      console.log("项目分类筛选条件更新:", newValue);
    }
  },
  { deep: true }
);

const shouldShowCategories = computed(() => {
  return rawData.value.some((item) => item.category1 === item.department);
});
</script>
  
<style scoped>
.report-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
  background-color: #f5f7fa;
  overflow: hidden;
  min-width: 1200px;
}

.report-header {
  background-color: white;
  padding: 15px 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
}

.report-title {
  text-align: center;
  font-weight: bold;
  font-size: 20px;
  margin-bottom: 5px;
  color: #303133;
}

.report-date {
  text-align: center;
  font-size: 14px;
  margin-bottom: 15px;
  color: #606266;
}

.filter-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-label {
  white-space: nowrap;
  margin-right: 5px;
  font-size: 14px;
  color: #606266;
}

.filter-actions {
  margin-left: auto;
  display: flex;
  gap: 10px;
}

.date-range {
  min-width: 320px;
}

.table-wrapper {
  flex: 1;
  margin: 0 15px;
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 300px;
}

.table-container {
  flex: 1;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: auto;
  height: 100%;
  position: relative;
}

.medical-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  table-layout: auto;
}

.medical-table th,
.medical-table td {
  border: 1px solid #ebeef5;
  padding: 8px 4px;
  text-align: center;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.medical-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 10;
}

.medical-table .amount {
  text-align: right;
  padding-right: 10px;
  min-width: 130px;
  max-width: 150px;
  overflow: visible;
  white-space: nowrap;
}

.subtotal-row {
  background-color: #f5f7fa;
  font-weight: bold;
}

.total-row {
  background-color: #ebeef5;
  font-weight: bold;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.subtotal-label,
.total-label {
  text-align: right;
  padding-right: 10px;
  text-align: right !important;
}

.footer-info {
  background-color: white;
  padding: 10px 15px;
  margin: 0 15px 10px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  color: #606266;
  font-size: 14px;
}

.date-limit-notice {
  background-color: white;
  padding: 8px 15px;
  margin: 0 15px 15px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  text-align: center;
}

.date-limit-info {
  color: #e6a23c;
  font-style: italic;
  font-size: 14px;
}

@media print {
  .report-container {
    background-color: white;
  }

  .report-header {
    box-shadow: none;
  }

  .report-actions,
  .footer-info {
    display: none;
  }

  .table-container {
    margin: 0;
    box-shadow: none;
    overflow: visible;
  }

  .medical-table th,
  .medical-table td {
    font-size: 12px;
    padding: 4px 2px;
  }
}

@media (max-width: 768px) {
  .filter-section {
    gap: 15px;
  }

  .filter-row {
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
  }

  .filter-item {
    width: 100%;
  }

  .filter-actions {
    margin-left: 0;
    width: 100%;
    justify-content: flex-end;
  }
}
</style>