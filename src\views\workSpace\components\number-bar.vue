<template>
  <div class="flex-shrink-0 overflow-hidden">
    <el-row :gutter="10">
      <el-col :md="12" :span="24">
        <div class="pb-10px bg-white rounded">
          <div class="pt-15px h-35px px-15px text-16px font-semibold">今日</div>          <div class="h-48px flex flex-nowrap items-center">
            <div class="w-1/3 flex items-center justify-center h-full">
              <img class="flex-shrink-0 mr-6px w-32px h-32px bg-gray-100 rounded-full"
                src="@/assets/images/todo-count1.svg" />
              <div>
                <div class="leading-14px pt-2px text-11px">
                  <span>待办</span>
                  <el-tooltip content="员工在所属团队内所有待办任务、群发任务的总数统计" placement="top" effect="light">
                    <el-icon class="ml-2px text-gray-500 text-12px transform translate-y-1px">
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </div>
                <div class="text-13px font-semibold leading-20px h-20px">{{ count.untreatedCount || 0 }}条</div>
              </div>
            </div>
            <div class="w-1/3 flex items-center justify-center h-full cursor-pointer" @click="toCustomerContact">
              <img class="flex-shrink-0 mr-6px w-32px h-32px bg-gray-100 rounded-full"
                src="@/assets/images/todo-count1.svg" />
              <div class="pointer">
                <div class="leading-14px pt-2px text-11px">
                  <span>新加好友</span>
                </div>
                <div class="text-13px font-semibold leading-20px h-20px">{{ newWeChatCount }}人</div>
              </div>
            </div>
            <div class="w-1/3 flex items-center justify-center h-full">
              <img class="flex-shrink-0 mr-6px w-32px h-32px bg-gray-100 rounded-full"
                src="@/assets/images/todo-count1.svg" />
              <div>
                <div class="leading-14px pt-2px text-11px">
                  <span>我的预约</span>
                </div>
                <div class="text-13px font-semibold leading-20px h-20px">0</div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :md="12" :span="24">
        <div class="pb-10px bg-white rounded">
          <div class="pt-15px h-35px flex items-center justify-between flex-nowrap px-15px">
            <span class="text-16px font-semibold">我的</span>
            <div class="flex-shrink-0 flex items-center">
              <div v-for="i in list" :key="i.value" class="ml-10px text-14px cursor-pointer hover:text-blue-500"
                :class="active === i.value ? 'text-blue-500' : ''" @click="toggle(i.value)">{{ i.label }}</div>
            </div>
          </div>          <div class="h-48px flex flex-nowrap items-center">
            <div class="w-1/3 flex items-center justify-center h-full cursor-pointer" @click="toCustomer">
              <img class="flex-shrink-0 mr-6px w-32px h-32px bg-gray-100 rounded-full"
                src="@/assets/images/todo-count1.svg" />
              <div>
                <div class="leading-14px pt-2px text-11px">
                  <span>{{ active === "all" ? "全部" : "新增" }}客户</span>
                </div>
                <div class="text-13px font-semibold leading-20px h-20px">{{ serviceCount.memberTotal || 0 }}人</div>
              </div>
            </div>
            <div class="w-1/3 flex items-center justify-center h-full cursor-pointer" @click="toService">
              <img class="flex-shrink-0 mr-6px w-32px h-32px bg-gray-100 rounded-full"
                src="@/assets/images/todo-count2.svg" />
              <div>
                <div class="leading-14px pt-2px text-11px">
                  <span>服务记录</span>
                </div>
                <div class="text-13px font-semibold leading-20px h-20px">{{ serviceCount.serviceTotal || 0 }}次</div>
              </div>
            </div>
            <div class="w-1/3 flex items-center justify-center h-full">
              <img class="flex-shrink-0 mr-6px w-32px h-32px bg-gray-100 rounded-full"
                src="@/assets/images/todo-count4.svg" />
              <div>
                <div class="leading-14px pt-2px text-11px">
                  <span>服务满意率</span>
                </div>
                <div class="text-13px font-semibold leading-20px h-20px">{{ rate }}</div>
              </div>
            </div>
            <div class="w-1/3 flex items-center justify-center h-full pointer"
              :class="isHangzheng ? 'pointer-events-none opacity-0' : ''" @click="toPointsDetial">
              <img class="flex-shrink-0 mr-6px w-32px h-32px bg-gray-100 rounded-full"
                src="@/assets/images/todo-count4.svg" />
              <div>
                <div class="leading-14px pt-2px text-11px">
                  <span>服务积分</span>
                </div>
                <div class="text-13px font-semibold leading-20px h-20px">{{ points || 0 }}分</div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script setup>
import { computed, onMounted, ref, watch } from "vue";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import { getmemberAndserviceCountForUserId, getStaffRate } from "@/api/member.js";
import { getNumber } from "@/api/todo";
import useDateRange from "@/hooks/useDateRange";
import { memberStore } from "@/store/member";
import { teamStore } from "@/store/team";
import { useRouter } from "vue-router";
import { getWechatFriends } from "@/api/corp";
import { getServicePointsStatistics } from "@/api/todo";
const router = useRouter();
const { active, dates, list, toggle } = useDateRange(["all", "today", "yesterday", "thisWeek", "lastWeek"]);
const { teams } = storeToRefs(teamStore());
const { isHangzheng } = storeToRefs(memberStore());
const count = ref({});
const newWeChatCount = ref(0);
async function getCountNumber() {
  const { data } = await getNumber({
    executorUserId: localStorage.getItem("userId"),
    corpId: localStorage.getItem("corpId"),
    teamIds: teams.value.map((item) => item.teamId),
    endExecutionTime: dayjs().endOf("day").valueOf(),
  });
  count.value = data && data.data ? data.data : {};
}
const serviceCount = ref({});
async function getMemberCount() {
  const params = {
    userId: localStorage.getItem("userId"),
    corpId: localStorage.getItem("corpId"),
    startTime: dates.value[0] ? dayjs(dates.value[0]).valueOf() : "",
    endTime: dates.value[1] ? dayjs(dates.value[1]).valueOf() : "",
  };
  let { data } = await getmemberAndserviceCountForUserId(active.value.toUpperCase(), params);
  serviceCount.value = data && data.data ? data.data : {};
}

async function getTodayWechatFriendsCount() {
  const query = {
    dates: [dayjs().format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")],
    userId: localStorage.getItem("userId"),
  };
  const { data, success } = await getWechatFriends(query);
  if (success) newWeChatCount.value = data.total;
}
const rate = ref("-");
async function getRate() {
  let data = await getStaffRate({ dates: dates.value, userId: localStorage.getItem("userId") });
  const score = data.data && Number(data.data.averageScore) >= 0 ? Number(data.data.averageScore) : "";
  rate.value = score === "" ? "-" : `${score}%`;
}
const points = ref(0);
async function getPoints() {
  const { data } = await getServicePointsStatistics({
    dates: dates.value,
    executorUserIds: [localStorage.getItem("userId")],
  });
  points.value = data ? data.allPoints : 0;
}
function toCustomer() {
  router.push({ name: "MEMBER", state: { tab: "myCustomer", dates: dates.value.join(",") } });
}

function toCustomerContact() {
  router.push({ name: "CUSTOMERCONTACT" });
}

function toService() {
  router.push({ name: "SCHEDULE", state: { taskTab: "myServiceRecord", dates: dates.value.join(",") } });
}

function toPointsDetial() {
  if (isHangzheng.value) return;
  router.push({ name: "SERVICEPOINTSLIST", params: { dates: JSON.stringify(dates.value) } });
}

onMounted(() => {
  getCountNumber();
  getMemberCount();
  getRate();
  getTodayWechatFriendsCount();
  if (!isHangzheng.value) getPoints();
});
watch(active, () => {
  getMemberCount();
  getRate();
  if (!isHangzheng.value) getPoints();
});

defineExpose({
  reloadTodo: getCountNumber,
});
</script>
<style lang="scss" scoped></style>
