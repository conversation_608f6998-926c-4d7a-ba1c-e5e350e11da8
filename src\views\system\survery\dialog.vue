<template>
  <el-dialog :model-value="props.visible" :close-on-click-modal="false" :width="width" title="" @close="close">
    <template #header>
      <div v-bind="isMobile ? { 'font-16': true } : { 'font-18': true }" font-semibold text-center>
        {{ data.name || '问卷详情' }}
      </div>
    </template>
    <el-scrollbar :wrap-style="wrapStyle">
      <div px-15 font-16 style="color: rgba(0,0,0,.9);">
        <div v-if="data.description" font-14 pb-10 color-666> {{ data.description }}</div>
        <template v-for="(item, index) in list" :key="item.id">
          <div pb-10 font-14 relative :class="item.require ? 'require-question' : ''">{{ index + 1 }}、{{ item.title }}
          </div>
          <div v-if="item.type === 'radio'">
            <el-radio-group :model-value="item.value">
              <el-radio v-for="opt in item.options" :key="opt.value" :label="opt.value">
                <div class="whitespace-normal">{{ opt.label }}{{ data.enableScore && opt.score ? `（${opt.score}分）` : ''}}</div>
              </el-radio>
            </el-radio-group>
          </div>
          <div v-else-if="item.type === 'input'" pb-10>
            <el-input readonly :model-value="item.value" />
          </div>
        </template>
      </div>
    </el-scrollbar>
    <template #footer>
      <slot name="footer">
        <div text-center>
          <el-button type="primary" @click="close()"> 关闭 </el-button>
        </div>
      </slot>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref, computed, watch } from 'vue'
const props = defineProps({
  data: { type: Object, default: () => ({}) },
  isMobile: { type: Boolean, default: false },
  optional: { type: Boolean, default: false },
  visible: { type: Boolean, default: false },
  yPadding: { type: Number, default: 360 }
})
const list = computed(() => {
  const questions = Array.isArray(props.data.list) ? props.data.list : [];
  return questions.filter(item => {
    const showCase = Array.isArray(item.showCase) ? item.showCase : [];
    if (props.optional && showCase.length) {
      const res = showCase.every(({ type, id, value }) => {
        const question = questions.find(i => i.id === id);
        const val = question ? question.value : '';
        return type === 'in' ? value.includes(val) : val === value.join()
      })
      return res
    }
    return true
  })
});
const wrapStyle = computed(() => `max-height: calc(100vh - ${props.yPadding}px)`)
const emits = defineEmits(['close'])
function close() {
  emits('close')
}
const width = ref(520);
watch(props, n => {
  if (n.visible) {
    width.value = Math.min(window.innerWidth * .9, 520)
  }
})
</script>
<style scoped>
  .require-question::before {
    position: absolute;
    left: -10px;
    top: 0;
    content: '*';
    color: #f56c6c;
  }

  [border-bottom-dashed] {
    border-bottom: 1px dashed #eee;
  }

  :deep(.el-radio-group) {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }

  :deep(.el-radio) {
    display: flex;
    height: auto;
    padding-bottom: 10px;
  }
</style>