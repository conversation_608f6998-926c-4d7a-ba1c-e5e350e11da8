<template>
  <my-layout bg-fff>
    <layout-item>
      <div class="p-15px">
        <div class="flex items-center">
          <div class="text-16px font-semibold">{{ sop.sopName }}</div>
          <el-tag v-if="sop.statusName" class="ml-10px" :type="sop.status === 'executing' ? 'warning' : 'info'">
            {{ sop.statusName }}
          </el-tag>
        </div>
        <div class="mt-15px text-gray-500 text-14px truncate">任务详情：{{ taskDetial }}</div>
        <div class="mt-15px flex items-center overflow-hidden">
          <!-- <el-select v-model="status" class="w-160px mr-15px" placeholder="所有状态" clearable>
            <el-option v-for="i in statusList" :key="i.value" :label="i.label" :value="i.value" />
          </el-select> -->
          <el-date-picker v-model="dates" placeholder="入组日期筛选" type="date" value-format="YYYY-MM-DD" @change="change($event)" />
          <el-button class="ml-10px" @click="reset">重置</el-button>
        </div>
      </div>
    </layout-item>
    <Layout-main :scroll="false">
      <el-table height="100%" :data="list" @sort-change="handleSort($event)">
        <el-table-column prop="customerName" width="160" label="目标客户" />
        <el-table-column prop="executeTeamName" width="120" label="管理团队" />
        <el-table-column prop="groupName" width="120" label="加入分组" />
        <el-table-column prop="status"  label="处理状态" />
        <el-table-column prop="executeTimeStr" width="120" label="入组日期" />
        <el-table-column prop="planName" width="120" label="关联计划名称" />
        <el-table-column prop="executeUserId" width="120" label="计划跟进人">
          <template #default="{ row }">
            <ww-user :openid="row.executeUserId" />
          </template>
        </el-table-column>
      </el-table>
    </Layout-main>
    <Layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize" @handle-current-change="changePage" />
    </Layout-item>
  </my-layout>
</template>
<script setup>
import { computed, onMounted, ref, watch, toRefs } from "vue";
import useElPagination from "@/hooks/useElPagination";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import { getGroupTaskResultList, getGroupByIds } from "@/api/member";
import dayjs from "dayjs";
const props = defineProps({
  sop: { type: Object, default: () => ({}) },
});
const { sop } = toRefs(props);
const { page, pageSize, changePage, changeSize } = useElPagination(getList);
const list = ref([]);
const dates = ref([]);
async function change() {
  getList();
}

const taskDetial = computed(() => {
  const { groups } = sop.value;
  if (!groups || !Array.isArray(groups)) return "";
  return groups
    .map((i) => {
      return `分组【${i.groupName || ""}】，人数占比${i.percent}%`;
    })
    .join("；");
});

async function getGroupsName() {
  const ids = sop.value.groups.map((i) => i.groupId);
  const { data, success } = await getGroupByIds({ ids });
  if (!success) return;
  sop.value.groups = sop.value.groups.map((i) => {
    const group = data.data.find((j) => j._id === i.groupId);
    return { ...i, groupName: group?.groupName };
  });
}

onMounted(async () => {
  getList();
  getGroupsName();
});
async function getList() {
  let params = {
    sopTaskId: sop.value._id,
    dates: dates.value,
  };
  const { data, success } = await getGroupTaskResultList(params);
  if (!success) return;
  list.value = data.data.map((i) => {
    const group = i.group && Array.isArray(i.group) && i.group.length > 0 ? i.group[0] : {};
    return {
      ...i,
      executeTimeStr: i.executeTime ? dayjs(i.executeTime).format("YYYY-MM-DD") : "",
      status: i.failReason ? `处理失败：${i.failReason}` : "已处理",
      groupName: group?.groupName,
      planName: group?.managementPlan?.planName,
    };
  });
}
</script>
<style lang="scss" scoped>
.text-primary {
  color: var(--el-color-primary);
}
</style>
