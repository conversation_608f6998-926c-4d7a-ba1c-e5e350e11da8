import { selectedCustomer } from "./chatCustomer.js";
import { publicKeys } from "./authMember.js";
import { getCorpMemberChatRecord, getCorpSessionArchive, batchStorageSession } from "@/api/sessionArchive.js";
import { searchCorpCustomer } from "@/api/member.js";
import { nextTick, onMounted, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { watchDebounced } from "@vueuse/core";
import JSEncrypt from "jsencrypt";
import dayjs from "dayjs";
const chatCustomerRecord = ref([]);
const pageSize = ref(10);
const currentPage = ref(1);
const total = ref(0);
const recordLoading = ref(false);
const customerList = ref([]);
let dataFrame = ref(null);
async function getChatUserRecord(msgIds) {
  const { success, data, message } = await getCorpMemberChatRecord({ pageSize: pageSize.value, page: currentPage.value, customerUserId: selectedCustomer.value.customerUserId, memberUserId: selectedCustomer.value.memberUserId, msgIds });
  recordLoading.value = false;
  if (success) {
    total.value = data.total;
    let list = data.data.map((item) => {
      item.secretKey = RSA_Decrypt(item);
      item.sendTime = dayjs(item.sendTime * 1000).format("MM-DD HH:mm");
      return item;
    });
    if (currentPage.value === 1) chatCustomerRecord.value = list;
    else chatCustomerRecord.value = chatCustomerRecord.value.concat(list);
  } else {
    total.value = 0;
    chatCustomerRecord.value = [];
    ElMessage.error(message);
  }
}
function RSA_Decrypt(item) {
  // Base64 解码
  let key = publicKeys.value.find((i) => i.publicKeyVersion === item.publicKeyVersion);
  const privateKey = key ? key.privateKey : "";
  // 使用 RSA 私钥解密
  let decrypt = new JSEncrypt();
  decrypt.setPrivateKey(privateKey);
  return decrypt.decrypt(item.encryptedSecretKey);
}

watchDebounced(
  () => selectedCustomer.value.customerUserId,
  (val) => {
    chatCustomerRecord.value = [];
    customerList.value = [];
    if (val) {
      currentPage.value = 1;
      recordLoading.value = true;
      getChatUserRecord();
      getCustomerList();
    }
  },
  { immediate: true, debounce: 500, maxWait: 5 * 1000 }
);

watch(
  () => chatCustomerRecord.value,
  async (val) => {
    if (dataFrame.value) {
      dataFrame.value.setData({
        migList: val,
      });
    }
  },
  { immediate: true }
);

async function refreshRecord() {
  recordLoading.value = true;
  await batchStorageSession();
  currentPage.value = 1;
  getChatUserRecord();
}

// 获取用户信息
async function getCustomerList() {
  const params = {
    corpId: localStorage.getItem("corpId"),
    page: 1,
    pageSize: 1000,
    externalUserId: selectedCustomer.value.customerUserId,
  };
  const { data, success } = await searchCorpCustomer(params);
  if (success) customerList.value = data.list;
}
function handleScroll() {
  console.log("到底了");
  if (chatCustomerRecord.value.length >= total.value) return;
  recordLoading.value = true;
  currentPage.value++;
  getChatUserRecord();
}
export { handleScroll, chatCustomerRecord, refreshRecord, recordLoading, customerList, getChatUserRecord, dataFrame };
