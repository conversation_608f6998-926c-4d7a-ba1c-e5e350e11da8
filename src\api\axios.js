import axios from "axios";
import { getApiUrlByCorpId } from "../config/apiConfig";

// 创建 axios 实例
const api = axios.create({
  timeout: 30000, // 请求超时设置，单位是毫秒
  headers: {
    "Content-Type": "application/json",
    // 可以设置请求头，比如 Authorization Token
    // Authorization: "Bearer " + localStorage.getItem("token"),
  },
});

// 请求拦截器，可以在这里做一些请求前的处理，比如添加 token
api.interceptors.request.use(
  (config) => {
    // 例如：配置请求头或者修改请求参数
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器，可以在这里做一些响应后的处理
api.interceptors.response.use(
  (response) => {
    // 这里可以处理返回的数据
    return response.data;
  },
  (error) => {
    // 这里可以处理请求失败的情况
    console.error("请求失败", error);
    return Promise.reject(error);
  }
);

// 发送请求的具体方法
export const post = async (url, query) => {
  try {
    if (!query.corpId) query.corpId = localStorage.getItem("corpId");
    // 根据当前 corpId 动态设置 baseURL
    const baseURL = getApiUrlByCorpId(query.corpId);
    const result = await api.post(`${baseURL}getYoucanData/${url}`, query); // 发送 POST 请求
    const { success = false, message, ...data } = result;
    console.log(`api name: ${query.type}`);
    console.log(data);
    return { success, data, message: message || (success ? "" : "服务器开小差了, 请稍后再试~") };
  } catch (e) {
    return { success: false, message: e.message || "服务器开小差了, 请稍后再试~~" };
  }
};

export const postFile = async (formData) => {
  try {
    const corpId = localStorage.getItem("corpId");
    const baseURL = getApiUrlByCorpId(corpId);
    const result = await api.post(`${baseURL}upload`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return result;
  } catch {
    return { success: false, message: "服务器开小差了, 请稍后再试~" };
  }
};
