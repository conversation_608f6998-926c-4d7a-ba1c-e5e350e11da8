<template>
  <el-dialog :model-value="props.visible" :width="width" title="选择宣教文章" @close="close()">
    <div flex items-center>
      <my-layout class="w-240px" height="calc(100vh - 360px)" style="border-right:1px solid #eee">
        <layout-item>
          <div px-15>
            <el-input v-model="title" text-center class="block" placeholder="输入文章标题搜索"
              @keyup.enter.native="getList()"></el-input>
            <el-checkbox v-model="checkAll" class="mt-5px" :indeterminate="isIndeterminate"
              @change="handleCheckAllChange">全部文章</el-checkbox>
          </div>
        </layout-item>
        <layout-main>
          <div v-for="item in list" :key="item._id" px-15 py-10 flex items-center justify-between border-bottom>
            <div font-14 mr-10 flex-grow class="w-0px truncate" :title="item.title"> {{ item.title }} </div>
            <el-checkbox flex-shrink-0 :model-value="Boolean(articleIds[item._id])" label=""
              @change="change($event, item)" />
          </div>
        </layout-main>
      </my-layout>
      <my-layout class="flex-grow" height="calc(100vh - 360px)">
        <layout-item>
          <div p-15 color-666 class="h-69px">已选文章: ({{ selection.length }})</div>
        </layout-item>
        <layout-main>
          <div v-for="item in selection" :key="'selection_' + item._id" px-15 py-10 flex items-center justify-between
            border-bottom>
            <div v-if="articleStatus[item._id] &&!articleStatus[item._id].exist" class="text-gray-500 flex-shrink-0 font-semibold">
              【已失效】
            </div>
            <div v-else-if="articleStatus[item._id] &&!articleStatus[item._id].enable"
              class="text-gray-500 flex-shrink-0 font-semibold">
              【已停用】
            </div>
            <div font-14 mr-10 flex-grow class="w-0px truncate"
              :class="articleStatus[item._id] &&!articleStatus[item._id].exist ?'text-gray-500 line-through':'text-dark-500'"> {{
              item.title }}
            </div>
            <span class="leading-none text-20px" color-danger pointer @click="change(false, item)">
              <el-icon>
                <CloseBold />
              </el-icon>
            </span>
            <!-- <el-checkbox flex-shrink-0 :model-value="Boolean(articleIds[item._id])" label="" @change="change($event, item)" /> -->
          </div>
        </layout-main>
      </my-layout>
    </div>
    <template #footer>
      <div text-center>
        <el-button plain class="w-100px" type="primary" :icon="Plus" @click="close()">取消</el-button>
        <el-button class="w-100px" type="primary" :icon="Plus" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref, watch } from "vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import { getArticleList, getArticleStatus } from '@/api/knowledgeBase'

const props = defineProps({
  visible: { type: Boolean, default: false },
  articles: { type: Array, default: () => [] },
  width: { type: Number, default: 520 }
})


const emits = defineEmits(['close', 'change']);
function close() {
  emits('close')
}

const articleStatus = ref({})
const title = ref('');
const list = ref([]);
const selection = ref([]);
const articleIds = computed(() => {
  return selection.value.reduce((val, item) => {
    val[item._id] = item;
    return val
  }, {})
})

async function getList() {
  const { data, message, success } = await getArticleList({ title: title.value.trim(), corpId: localStorage.getItem('corpId'), page: 1, pageSize: 30, enable: true });
  const { list: articleList = [], total: count = 0 } = data;
  list.value = articleList;
  if (!success) ElMessage.error(message);
  articleList.forEach(item => {
    const article = selection.value.find(i => i._id == item._id);
    if (article && article.title !== item.title) {
      article.title = item.title
    }
  })
}

function change(val, item) {
  if (val) {
    selection.value.push({ title: item.title, _id: item._id })
  } else {
    selection.value = selection.value.filter(i => i._id !== item._id)
  }
}

const checkAll = computed({
  get() {
    return list.value.every(i => Boolean(articleIds.value[i._id]))
  },
  set(newValue) {
    if (newValue) {
      list.value.forEach(item => {
        if (!articleIds.value[item._id]) {
          selection.value.push({ title: item.title, _id: item._id })
        }
      })
    } else {
      list.value.forEach(item => {
        selection.value = selection.value.filter(i => i._id !== item._id)
      })
    }
  }
})
const isIndeterminate = computed(() => {
  const checked = list.value.filter(i => Boolean(articleIds.value[i._id]));
  return checked.length < list.value.length && selection.value.length > 0 && list.value.length > 0
})

function handleCheckAllChange(value) {
  checkAll.value = value
}

function confirm() {
  emits('change', selection.value);
  emits('close');
}

async function getArticleStatusList() {
  const { data, message, success } = await getArticleStatus({ corpId: localStorage.getItem('corpId'), ids: props.articles.map(i => i._id) });
  if (!success || !Array.isArray(data.list)) return;

  const status = props.articles.reduce((val, item) => {
    val[item._id] = { exist: false };
    return val
  }, {})
  data.list.forEach(item => {
    if (status[item._id]) {
      status[item._id] = { exist: true, enable: item.enable }
    }
  })
  articleStatus.value = status;
}

watch(() => props.visible, (n) => {
  if (n) {
    title.value = '';
    articleStatus.value = {}
    selection.value = props.articles.map(i => ({ ...i }));
    getList()
    if (props.articles.length) getArticleStatusList()

  }
})

</script>