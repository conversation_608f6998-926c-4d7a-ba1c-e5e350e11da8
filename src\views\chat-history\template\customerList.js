let template = `
<scroll-view class="scrollarea" scroll-y type="list" bindscrolltolower="bindscrolltolower">
    <view class="chat-record">
        <view wx:for="{{data.customerList}}" wx:key="msgid" data-index="{{index}}">
               <view class="flex">
                    <view class="pr-10 font-semibold ">
                    <ww-open-data  openid="{{item.customerUserId}}" type="externalUserName"></ww-open-data>
                    </view>
                    <view class="pr-10 chat-color">@微信</view>
                    <view class="send-time">{{item.sendTime}}</view>
                </view>
        </view>
    </view>
</scroll-view>
`;

const style = `
.scrollarea {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;

}
.chat-record {
  height: 100%;
  width: 100%;
  font-size: 14px;
}
.chat {
  padding: 15px;
}
.chat-left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.chat-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.flex {
  display: flex;
  align-items: center;
}
.chat-content {
  word-break: break-all;
  flex-grow: 0;
  padding: 10px;
  border-radius: 5px;
  max-width: 80%;
  margin-top: 10px;
}
.box-shadow {
  box-shadow: 0 2px 4px 0 rgba(54, 58, 80, 0.32);
}
.font-semibold {
  font-weight: 600;
}
.pl-10 {
  padding-left: 10px;
}
.pr-10 {
  padding-right: 10px;
}
.send-time {
  font-size: 12px;
}
.chat-color {
  color: #66CB70;
  font-size: 12px;
}
.member-color {
  color: rgba(249, 115, 22);
  font-size: 12px;
}
`;

export { template, style };
