<template>
  <div v-if="list.length" class="border-b bg-white border-gray-200 px-15px pt-5px mb-10px pb-10px">
    <div :style="expand ? '' : 'max-height: 96px; overflow:hidden'">
      <div class="flex flex items-center justify-between">
        <div>
          <span class="text-16px pr-5px font-bold">关键指标</span>
          <span class="text-12px pt-3px">(展示最近一次的测量数据)</span>
        </div>
        <div v-if="showExpand" class="text-center text-14px pr-5px text-blue-500 cursor-pointer"
          @click="expand = !expand">
          <el-icon v-if="expand" class="mr-5px transform translate-y-2px">
            <ArrowUpBold />
          </el-icon>
          <el-icon v-else class="mr-5px transform translate-y-2px">
            <ArrowDownBold />
          </el-icon>
          <span>{{ expand ? "收起" : "展开" }}</span>
        </div>
      </div>
      <el-form ref="formRef" label-suffix="：">
        <el-row>
          <el-col v-for="i in list" :key="i.title" :span="8">
            <el-form-item :label="i.name" class="mb-0">
              <div class="flex-grow flex items-center">
                <div>{{ i.value }} {{ i.unit }}</div>
                <svg-icon class="flex-shrink-0 cursor-pointer text-blue-500 ml-10px" size="16" name="linechart"
                  @click="toTrend(i)" v-if="i.value" />
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
  <trend-modal :visible="visible" :width="width" @close="close" :field="field" :customerId="customerId" />
</template>
<script setup>
import { computed, onMounted, ref } from "vue";
import { useElementBounding } from "@vueuse/core";
import { storeToRefs } from "pinia";
import { dbStore } from "@/store/db.js";
import SvgIcon from "@/components/svg-icon";
import trendModal from "./trend-modal.vue";
import useModal from "@/hooks/useModal";

const db = dbStore();
const { getHealthIndicatorTemplate } = db;
const { healthIndicatorsTemplate } = storeToRefs(db);
const { close, show, visible, width } = useModal(1200); // 选择客户弹窗
const formRef = ref();
const { height: formHeight } = useElementBounding(formRef);
const showExpand = computed(() => formHeight.value > 64);
const expand = ref(false);
onMounted(() => {
  if (healthIndicatorsTemplate.value.length === 0) getHealthIndicatorTemplate();
});
const props = defineProps({
  healthIndicators: {
    type: Array,
    default: [],
  },
  customerId: {
    type: String,
    default: "",
  },
});
const list = computed(() => {
  return healthIndicatorsTemplate.value.map((item) => {
    const data = props.healthIndicators ? props.healthIndicators.find((i) => i.type === item.title) : null;
    return {
      ...item,
      value: data ? data.value : item.default,
    };
  });
});

const field = ref("");
function toTrend(item) {
  field.value = item;
  show();
}
</script>
<style lang="scss" scoped></style>
