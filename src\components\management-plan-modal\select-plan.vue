<template>
  <el-dialog :model-value="props.visible" :close-on-click-modal="false" :width="400" title="" @close="close">
    <template #header>
      <div font-semibold text-center>选择回访计划</div>
    </template>
    <div v-if="planList.length > 0">
      <el-scrollbar max-height="300" v-loading="loading" height="200px">
        <div class="plan-cell" border-bottom v-for="item in planList" :key="item._id">
          <div>
            <div font-semibold>{{ item.planName }}</div>
            <div class="pt-10px plan-cell-detial">应用范围: {{ item.planDetial }}</div>
          </div>
          <img src="@/assets/check.png" class="cell-radio" v-if="item.planId !== selectId" @click="selectPlanAction(item)" />
          <img src="@/assets/check-active.png" class="cell-radio" v-else @click="selectPlanAction('')" />
        </div>
      </el-scrollbar>
    </div>
    <div v-else>
      <empty-data :top="0" title="暂无回访计划" padding="30px 15px" text-top="10px" :image-width="120"></empty-data>
    </div>
    <template #footer>
      <slot name="footer">
        <div class="text-center">
          <el-button class="w-100px" @click="close()">取消</el-button>
          <el-button class="w-100px" type="primary" @click="save()">确定</el-button>
        </div>
      </slot>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref, watch } from "vue";
import { getManagementPlan } from "@/api/managementPlan";
import EmptyData from "@/components/empty-data.vue";
import { storeToRefs } from "pinia";
import { teamStore } from "@/store/team";
const { currentTeam } = storeToRefs(teamStore());


watch(currentTeam, (newItem, oldItem) => {
  if (newItem && oldItem && newItem.teamId !== oldItem.teamId) {
    getList();
  }
});
const { teamId } = currentTeam
const props = defineProps({
  visible: { type: Boolean, default: false },
  planId: { type: String, default: "" },
  yPadding: { type: Number, default: 360 },
});
const planList = ref([]);
const selectId = ref("");
const loading = ref(false);
watch(
  () => props.planId,
  (n) => {
    selectId.value = n.planId;
  },
  { immediate: true }
);

getList();
const emits = defineEmits(["close", "success", "addCustomerTask"]);
function close() {
  emits("close");
}
async function getList() {
  loading.value = true;
  let { data, success } = await getManagementPlan(1, 999);
  loading.value = false;
  if (success) {
    planList.value = data.data.filter((e) => e.planStatus);
  }
}
function selectPlanAction(item) {
  selectId.value = item.planId;
}
function save() {
  const plan = planList.value.find((item) => item.planId === selectId.value);
  emits("success", plan);
}

// 新增回访计划
function addTask() {
  emits("addCustomerTask");
}
</script>
<style scoped lang="scss">
.footer-class {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.teamPlan {
  display: flex;
  width: 100%;
  overflow-x: auto;
}

.plan-cell {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 15px 15px 15px;

  &-detial {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 300px;
  }
}

.require-question::before {
  position: absolute;
  left: -10px;
  top: 0;
  content: "*";
  color: #f56c6c;
}

[border-bottom-dashed] {
  border-bottom: 1px dashed #eee;
}

:deep(.el-radio-group) {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

:deep(.el-radio) {
  display: flex;
  height: auto;
  padding-bottom: 10px;
}
</style>