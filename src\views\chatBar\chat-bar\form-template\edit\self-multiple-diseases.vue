<template>
  <form-diagnosis :form="form" :name="name" :title="title" :required="required" :mult="true" @change="change" />
</template>
<script setup>
import FormDiagnosis from './form-diagnosis.vue';

const emits = defineEmits(['change'])
const props = defineProps({
  form: { type: Object, default: () => ({}) },
  name: { type: String, default: '' },
  required: { type: Boolean, default: false },
  title: { type: String, default: '' },
})

function change(data) { emits('change', data) }
</script>
<style lang="scss" scoped></style>
