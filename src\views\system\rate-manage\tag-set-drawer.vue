<template>
  <el-drawer :model-value="visible" :close-on-click-modal="false" :close-on-press-escape="false">
    <template #header>
      <div>评价标签设置</div>
    </template>
    <template #default>
      <el-scrollbar>
        <template v-for="({label, key, tags}, idx) in rateTagGroup" :key="key">
          <div class="flex items-center justify-between" :class="idx > 0 ?'mt-15px':''">
            <div class="text-sm font-semibold">{{label}}</div>
            <el-button text type="primary" :icon="Plus" @click="addTag(key)">
              <el-icon class="mr-4px">
                <Plus />
              </el-icon>
              新增标签
            </el-button>
          </div>
          <div class="flex flex-wrap pb-15px border-b border-gray-200">
            <editable-tag v-for="({_id,text}) in tags" :key="_id" :id="_id" :rate-star="key"
              classnames="mt-10px mr-10px" :text="text" @updated="updated" @remove="remove" />
            <editable-tag v-if="currentRateStar == key" :key="`${key}-new`" :rate-star="key"
              classnames="mt-10px mr-10px" @cancelAdd="cancelAdd" @updated="updated" />
          </div>
        </template>
      </el-scrollbar>
    </template>
  </el-drawer>
</template>
<script setup>
import { computed, ref, nextTick } from 'vue';
import editableTag from './editable-tag.vue';

const props = defineProps({
  rateTags: { type: Array, default: () => [] },
  visible: { type: Boolean, default: false }
})

const emits = defineEmits(['updated', 'remove'])

const groupConfig = [
  { label: '设置星级评分为“非常满意”的标签', key: 'fiveStar' },
  { label: '设置星级评分为“满意”的标签', key: 'fourStar' },
  { label: '设置星级评分为“一般”的标签', key: 'threeStar' },
  { label: '设置星级评分为“不满意”的标签', key: 'twoStar' },
  { label: '设置星级评分为“非常不满意”的标签', key: 'oneStar' }
]

const rateTagGroup = computed(() => groupConfig.map(({ key, label }) => {
  const item = props.rateTags.find(i => i.rateStar === key)
  const tags = item && Array.isArray(item.rateTags) ? item.rateTags : []
  return { key, label, tags }
}))


const currentRateStar = ref('');
function addTag(key) {
  currentRateStar.value = key
}
function cancelAdd() {
  currentRateStar.value = ''
}

function updated(data) {
  cancelAdd()
  emits('updated', data)
}

function remove(data) {
  emits('remove', data)
}

</script>
<style lang="scss" scoped></style>
