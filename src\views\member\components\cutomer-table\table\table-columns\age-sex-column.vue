<template>
  <el-table-column label="性别 / 年龄" prop="sexAndAge" :width="110">
    <template #default="{row}">
      <td-wrapper classnames="w-full">{{ getSexAndAge(row) }}</td-wrapper>
    </template>
  </el-table-column>
</template>
<script setup>
import { getCustomerAge } from '@/helpers/customer'
import tdWrapper from './td-wrapper.vue';

function getSexAndAge(customer) {
  return [customer.sex, getCustomerAge(customer)].filter(Boolean).join(' / ');
}

</script>
<style lang="scss" scoped></style>
