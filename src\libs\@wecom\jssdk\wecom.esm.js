var win = typeof window !== "undefined" ? window : undefined;
var doc = typeof document !== "undefined" ? document : undefined;
var nav = typeof navigator !== "undefined" ? navigator : undefined;
var loc = typeof location !== "undefined" ? location : undefined;
function getHref() {
    return (loc == null ? void 0 : loc.href) || "";
}
function getTitle() {
    return (doc == null ? void 0 : doc.title) || "";
}

var _ua_match;
var ua = (nav == null ? void 0 : nav.userAgent) || "";
var platform = (nav == null ? void 0 : nav.platform) || "";
var wecomVersion = ua == null ? void 0 : (_ua_match = ua.match(/wxwork\/([\d.]+)/i)) == null ? void 0 : _ua_match[1];
var isWeCom = !!wecomVersion;
var isWeChat = !isWeCom && /micromessenger/i.test(ua);
var isAndroid = /\bAndroid\b/i.test(ua);
var isWindows = /Win/i.test(platform);
var _getAppleDeviceType = getAppleDeviceType();
var isIOS = _getAppleDeviceType.isIOS, isMac = _getAppleDeviceType.isMac;
function getAppleDeviceType() {
    var _nav_maxTouchPoints;
    var maxTouchPoints = (_nav_maxTouchPoints = nav == null ? void 0 : nav.maxTouchPoints) != null ? _nav_maxTouchPoints : 1;
    var isMac = /Mac/i.test(platform);
    // https://stackoverflow.com/questions/56934826/distinguish-between-ipad-and-mac-on-ipad-with-ipados
    if (isMac && maxTouchPoints > 2) {
        return {
            isMac: false,
            isIOS: true
        };
    }
    return {
        isMac: isMac,
        isIOS: /\b(iPhone|iPad|iPod)\b/i.test(ua)
    };
}

var registerOptions;
var corpConfigTask;
var agentConfigTask;
function setRegisterOptions(options) {
    if (!options.corpId) {
        throw new Error("Missing corpId");
    }
    registerOptions = options;
}
function getRegisterOptions() {
    return registerOptions;
}
function getCorpConfigTask() {
    return corpConfigTask;
}
function setCorpConfigTask(task) {
    corpConfigTask = task;
}
function getCorpConfigParams() {
    var _corpConfigTask_getResult;
    return corpConfigTask == null ? void 0 : (_corpConfigTask_getResult = corpConfigTask.getResult()) == null ? void 0 : _corpConfigTask_getResult.params;
}
function getAgentConfigTask() {
    return agentConfigTask;
}
function setAgentConfigTask(task) {
    agentConfigTask = task;
}
function getAgentConfigParams() {
    var _agentConfigTask_getResult;
    return agentConfigTask == null ? void 0 : (_agentConfigTask_getResult = agentConfigTask.getResult()) == null ? void 0 : _agentConfigTask_getResult.params;
}
function getConfigParams() {
    var registerOptions = getRegisterOptions();
    if (isWeCom && (registerOptions == null ? void 0 : registerOptions.getAgentConfigSignature)) {
        return getAgentConfigParams();
    }
    return getCorpConfigParams();
}
function getConfigCorpId() {
    var registerOptions = getRegisterOptions();
    if (isWeCom && (registerOptions == null ? void 0 : registerOptions.getAgentConfigSignature)) {
        var params = getAgentConfigParams();
        return params == null ? void 0 : params.corpid;
    }
    var params1 = getCorpConfigParams();
    return params1 == null ? void 0 : params1.appId;
}
/**
 * 获取 config 或 agentConfig 传入的相关参数
 *
 * 用于外部 sdk 调用私有方法
 */ function getVerifyParams() {
    var verifyParams = getConfigParams();
    if (!verifyParams) {
        return;
    }
    var corpId = getConfigCorpId();
    return {
        appId: corpId,
        verifyAppId: corpId,
        verifySignType: "sha1",
        verifyTimestamp: verifyParams.timestamp,
        verifyNonceStr: verifyParams.nonceStr,
        verifySignature: verifyParams.signature
    };
}

function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {
    try {
        var info = gen[key](arg);
        var value = info.value;
    } catch (error) {
        reject(error);
        return;
    }
    if (info.done) resolve(value);
    else Promise.resolve(value).then(_next, _throw);
}
function _async_to_generator(fn) {
    return function() {
        var self = this, args = arguments;
        return new Promise(function(resolve, reject) {
            var gen = fn.apply(self, args);
            function _next(value) {
                asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value);
            }
            function _throw(err) {
                asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err);
            }
            _next(undefined);
        });
    };
}

function __generator(thisArg, body) {
    var _ = {
        label: 0,
        sent: function sent() {
            if (t[0] & 1) throw t[1];
            return t[1];
        },
        trys: [],
        ops: []
    }, f, y, t, g;
    return g = {
        next: verb(0),
        "throw": verb(1),
        "return": verb(2)
    }, typeof Symbol === "function" && (g[Symbol.iterator] = function() {
        return this;
    }), g;
    function verb(n) {
        return function(v) {
            return step([
                n,
                v
            ]);
        };
    }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while(g && (g = 0, op[0] && (_ = 0)), _)try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [
                op[0] & 2,
                t.value
            ];
            switch(op[0]){
                case 0:
                case 1:
                    t = op;
                    break;
                case 4:
                    _.label++;
                    return {
                        value: op[1],
                        done: false
                    };
                case 5:
                    _.label++;
                    y = op[1];
                    op = [
                        0
                    ];
                    continue;
                case 7:
                    op = _.ops.pop();
                    _.trys.pop();
                    continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) {
                        _ = 0;
                        continue;
                    }
                    if (op[0] === 3 && (!t || op[1] > t[0] && op[1] < t[3])) {
                        _.label = op[1];
                        break;
                    }
                    if (op[0] === 6 && _.label < t[1]) {
                        _.label = t[1];
                        t = op;
                        break;
                    }
                    if (t && _.label < t[2]) {
                        _.label = t[2];
                        _.ops.push(op);
                        break;
                    }
                    if (t[2]) _.ops.pop();
                    _.trys.pop();
                    continue;
            }
            op = body.call(thisArg, _);
        } catch (e) {
            op = [
                6,
                e
            ];
            y = 0;
        } finally{
            f = t = 0;
        }
        if (op[0] & 5) throw op[1];
        return {
            value: op[0] ? op[1] : void 0,
            done: true
        };
    }
}
typeof SuppressedError === "function" ? SuppressedError : function _SuppressedError(error, suppressed, message) {
    var e = new Error(message);
    return e.name = "SuppressedError", e.error = error, e.suppressed = suppressed, e;
};

function _extends() {
    _extends = Object.assign || function assign(target) {
        for(var i = 1; i < arguments.length; i++){
            var source = arguments[i];
            for(var key in source)if (Object.prototype.hasOwnProperty.call(source, key)) target[key] = source[key];
        }
        return target;
    };
    return _extends.apply(this, arguments);
}

function _array_like_to_array(arr, len) {
    if (len == null || len > arr.length) len = arr.length;
    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];
    return arr2;
}

function _unsupported_iterable_to_array(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _array_like_to_array(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(n);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _array_like_to_array(o, minLen);
}

function _create_for_of_iterator_helper_loose(o, allowArrayLike) {
    var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"];
    if (it) return (it = it.call(o)).next.bind(it);
    // Fallback for engines without symbol support
    if (Array.isArray(o) || (it = _unsupported_iterable_to_array(o)) || allowArrayLike && o && typeof o.length === "number") {
        if (it) o = it;
        var i = 0;
        return function() {
            if (i >= o.length) return {
                done: true
            };
            return {
                done: false,
                value: o[i++]
            };
        };
    }
    throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}

var webToClientJsApiMap = {
    config: "preVerifyJsApi",
    onMenuShareTimeline: "menu:share:timeline",
    onMenuShareAppMessage: "menu:share:appmessage",
    onMenuShareWechat: "menu:share:wechat",
    onMenuShareQQ: "menu:share:qq",
    onMenuShareWeibo: "menu:share:weiboApp",
    onMenuShareQZone: "menu:share:QZone",
    previewImage: "imagePreview",
    getLocation: "geoLocation",
    openProductSpecificView: "openProductViewWithPid",
    addCard: "batchAddCard",
    openCard: "batchViewCard",
    chooseWXPay: "getBrandWCPayRequest",
    openEnterpriseRedPacket: "getRecevieBizHongBaoRequest",
    startSearchBeacons: "startMonitoringBeacons",
    stopSearchBeacons: "stopMonitoringBeacons",
    onSearchBeacons: "onBeaconsInRange",
    consumeAndShareCard: "consumedShareCard",
    openAddress: "editAddress",
    getBrandWCPayRequest: "getBrandWCPayRequest"
};
var clientToWebJsApiMap = {};
for(var _iterator = _create_for_of_iterator_helper_loose(Object.keys(webToClientJsApiMap)), _step; !(_step = _iterator()).done;){
    var key = _step.value;
    clientToWebJsApiMap[webToClientJsApiMap[key]] = key;
}
function mapJsApiNameToClient(name) {
    return webToClientJsApiMap[name] || name;
}
function mapJsApiNameToWeb(name) {
    return clientToWebJsApiMap[name] || name;
}
function mapJsApiListToClient(list) {
    return list.map(mapJsApiNameToClient);
}

function error() {
    for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
        args[_key] = arguments[_key];
    }
    var _console;
    (_console = console).error.apply(_console, [].concat([
        "[wwsdk]"
    ], args)); // eslint-disable-line no-console
}

var originalHref = getHref();
/**
 * 两个 SemVer 相减，取差的符号
 */ function semverSubtract(v1, v2) {
    var v1Seg = (v1 == null ? void 0 : v1.split(".")) || [];
    var v2Seg = (v2 == null ? void 0 : v2.split(".")) || [];
    for(var i = 0, ii = Math.max(v1Seg.length, v2Seg.length); i < ii; i++){
        var v1Num = parseInt(v1Seg[i]) || 0;
        var v2Num = parseInt(v2Seg[i]) || 0;
        if (v1Num > v2Num) {
            return 1;
        }
        if (v1Num < v2Num) {
            return -1;
        }
    }
    return 0;
}
function arrayBufferToBase64(buffer) {
    if (typeof Buffer !== "undefined") {
        return Buffer.from(buffer).toString("base64");
    }
    var binary = "";
    var bytes = new Uint8Array(buffer);
    var len = bytes.byteLength;
    for(var i = 0; i < len; i++){
        binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
}
function base64ToArrayBuffer(base64) {
    if (typeof Buffer !== "undefined") {
        return Buffer.from(base64, "base64").buffer;
    }
    var binary = atob(base64);
    var length = binary.length;
    var bytes = new Uint8Array(length);
    for(var i = 0; i < length; i++){
        bytes[i] = binary.charCodeAt(i);
    }
    return bytes.buffer;
}
function safeRun(fn, param, thisArg) {
    if (!isFunction(fn)) {
        return;
    }
    try {
        return fn.call(thisArg, param);
    } catch (error$1) {
        error(error$1);
    }
}
function startsWith(str, search) {
    return str.slice(0, search.length) === search;
}
function getSignURL(original) {
    if (original) {
        return originalHref.split("#")[0];
    }
    return getHref().split("#")[0];
}
function extractErrMsgKeyword(errMsg) {
    if (!isString(errMsg)) {
        return "";
    }
    return errMsg.slice(errMsg.indexOf(":") + 1);
}
function isFalsy(val) {
    return val === false || val === 0;
}
// eslint-disable-next-line @typescript-eslint/ban-types
function isObject$1(val) {
    if (!val) {
        return false;
    }
    return typeof val === "object";
}
function isFunction(val) {
    return typeof val === "function";
}
function isString(val) {
    return typeof val === "string";
}
function joinList(list) {
    if (!list) {
        return list;
    }
    if (isString(list)) {
        return list;
    }
    return list.join(";");
}

/**
 * WeixinJSBridge 是否已注入到 window
 */ var isWeixinJSBridgeReady = !!(win == null ? void 0 : win.WeixinJSBridge);
/**
 * 等待 WeixinJSBridge 注入到 window
 */ var onWeixinJSBridgeReady = Promise.resolve();
if (!isWeixinJSBridgeReady) {
    onWeixinJSBridgeReady = new Promise(function(resolve) {
        doc == null ? void 0 : doc.addEventListener("WeixinJSBridgeReady", function() {
            isWeixinJSBridgeReady = true;
            resolve();
        });
    });
}
/**
 * 监听 JSSDK 未定义的事件
 *
 * @example
 * ```ts
 * ww.on('onBeaconsInRange', res => {
 *   console.log(res)
 * })
 * ```
 *
 * @param name 事件名称
 * @param callback 监听回调
 */ function on(name, callback) {
    return _on.apply(this, arguments);
}
function _on() {
    _on = _async_to_generator(function(name, callback) {
        return __generator(this, function(_state) {
            switch(_state.label){
                case 0:
                    if (!!isWeixinJSBridgeReady) return [
                        3,
                        2
                    ];
                    return [
                        4,
                        onWeixinJSBridgeReady
                    ];
                case 1:
                    _state.sent();
                    _state.label = 2;
                case 2:
                    win.WeixinJSBridge.on(name, callback);
                    return [
                        2
                    ];
            }
        });
    });
    return _on.apply(this, arguments);
}
/**
 * 调用 JSSDK 未定义的 JSAPI
 *
 * @example
 * ```ts
 * ww.invoke('openEnterpriseChat', params, res => {
 *   console.log(res)
 * })
 * ```
 *
 * @param name JSAPI 名称
 * @param params JSAPI 参数
 * @param callback 回调函数
 * @returns JSAPI 返回值
 */ function invoke(name) {
    return _invoke.apply(this, arguments);
}
function _invoke() {
    _invoke = _async_to_generator(function(name, params, callback) {
        var result, keyword;
        return __generator(this, function(_state) {
            switch(_state.label){
                case 0:
                    if (params === void 0) params = {};
                    if (!!isWeixinJSBridgeReady) return [
                        3,
                        2
                    ];
                    return [
                        4,
                        onWeixinJSBridgeReady
                    ];
                case 1:
                    _state.sent();
                    _state.label = 2;
                case 2:
                    return [
                        4,
                        new Promise(function(resolve) {
                            var fullParams = _extends({}, params, getVerifyParams());
                            win.WeixinJSBridge.invoke(name, fullParams, resolve);
                        })
                    ];
                case 3:
                    result = _state.sent();
                    if (!result.errMsg) {
                        result.errMsg = formatErrMsg(name, result.err_msg || result.errmsg);
                    }
                    keyword = extractErrMsgKeyword(result.errMsg);
                    if (result.errCode == null) {
                        if (result.err_code != null) {
                            result.errCode = result.err_code;
                        } else {
                            result.errCode = keyword === "ok" ? 0 : -1;
                        }
                    }
                    safeRun(callback, result);
                    if (keyword !== "ok") {
                        throw result;
                    }
                    return [
                        2,
                        result
                    ];
            }
        });
    });
    return _invoke.apply(this, arguments);
}
function formatErrMsg(name, errMsg) {
    var apiName = mapJsApiNameToWeb(name);
    if (!errMsg) {
        return "" + apiName + ":ok";
    }
    var keyword = extractErrMsgKeyword(errMsg);
    if (keyword === "confirm") {
        keyword = "ok";
    }
    if (keyword === "failed") {
        keyword = "fail";
    }
    if (startsWith(keyword, "failed_")) {
        keyword = keyword.slice(7);
    }
    if (startsWith(keyword, "fail_")) {
        keyword = keyword.slice(5);
    }
    keyword = keyword.replace(/_/g, "").toLowerCase();
    if (keyword === "access denied" || keyword === "no permission to execute") {
        keyword = "permission denied";
    }
    if (apiName === "config" && keyword === "function not exist") {
        keyword = "ok";
    }
    if (!keyword) {
        keyword = "fail";
    }
    return apiName + ":" + keyword;
}

function _set_prototype_of(o, p) {
    _set_prototype_of = Object.setPrototypeOf || function setPrototypeOf(o, p) {
        o.__proto__ = p;
        return o;
    };
    return _set_prototype_of(o, p);
}

function _inherits(subClass, superClass) {
    if (typeof superClass !== "function" && superClass !== null) {
        throw new TypeError("Super expression must either be null or a function");
    }
    subClass.prototype = Object.create(superClass && superClass.prototype, {
        constructor: {
            value: subClass,
            writable: true,
            configurable: true
        }
    });
    if (superClass) _set_prototype_of(subClass, superClass);
}

function _is_native_reflect_construct() {
    if (typeof Reflect === "undefined" || !Reflect.construct) return false;
    if (Reflect.construct.sham) return false;
    if (typeof Proxy === "function") return true;
    try {
        Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {}));
        return true;
    } catch (e) {
        return false;
    }
}

function _construct(Parent, args, Class) {
    if (_is_native_reflect_construct()) _construct = Reflect.construct;
    else {
        _construct = function construct(Parent, args, Class) {
            var a = [
                null
            ];
            a.push.apply(a, args);
            var Constructor = Function.bind.apply(Parent, a);
            var instance = new Constructor();
            if (Class) _set_prototype_of(instance, Class.prototype);
            return instance;
        };
    }
    return _construct.apply(null, arguments);
}

function _get_prototype_of(o) {
    _get_prototype_of = Object.setPrototypeOf ? Object.getPrototypeOf : function getPrototypeOf(o) {
        return o.__proto__ || Object.getPrototypeOf(o);
    };
    return _get_prototype_of(o);
}

function _is_native_function(fn) {
    return Function.toString.call(fn).indexOf("[native code]") !== -1;
}

function _wrap_native_super(Class) {
    var _cache = typeof Map === "function" ? new Map() : undefined;
    _wrap_native_super = function _wrap_native_super(Class) {
        if (Class === null || !_is_native_function(Class)) return Class;
        if (typeof Class !== "function") throw new TypeError("Super expression must either be null or a function");
        if (typeof _cache !== "undefined") {
            if (_cache.has(Class)) return _cache.get(Class);
            _cache.set(Class, Wrapper);
        }
        function Wrapper() {
            return _construct(Class, arguments, _get_prototype_of(this).constructor);
        }
        Wrapper.prototype = Object.create(Class.prototype, {
            constructor: {
                value: Wrapper,
                enumerable: false,
                writable: true,
                configurable: true
            }
        });
        return _set_prototype_of(Wrapper, Class);
    };
    return _wrap_native_super(Class);
}

var SDKError = /*#__PURE__*/ function(Error1) {
    _inherits(SDKError, Error1);
    function SDKError(message) {
        return Error1.call(this, "SDK Error: " + message);
    }
    return SDKError;
}(_wrap_native_super(Error));
var AbortError = /*#__PURE__*/ function(SDKError) {
    _inherits(AbortError, SDKError);
    function AbortError() {
        return SDKError.call(this, "Aborted");
    }
    return AbortError;
}(SDKError);

var CONFIG_TYPE_CORP = 1;
var CONFIG_TYPE_AGENT = 2;
var ConfigTask = /*#__PURE__*/ function() {
    function ConfigTask(type, factory) {
        this.type = type;
        this.useOriginalURL = isWeChat && !isWindows && !isMac && type === CONFIG_TYPE_CORP;
        this.url = getSignURL(this.useOriginalURL);
        this.promise = this.awaitPromise(factory(this.url));
    }
    var _proto = ConfigTask.prototype;
    _proto.checkValid = function checkValid() {
        if (isWeCom && this.url !== getSignURL(this.useOriginalURL)) {
            this.error = new AbortError();
        }
        return !this.error;
    };
    _proto.getResult = function getResult() {
        if (this.checkValid()) {
            return this.result;
        }
    };
    _proto.awaitPromise = function awaitPromise(promise) {
        var _this = this;
        return _async_to_generator(function() {
            var error;
            return __generator(this, function(_state) {
                switch(_state.label){
                    case 0:
                        _state.trys.push([
                            0,
                            2,
                            ,
                            3
                        ]);
                        return [
                            4,
                            promise
                        ];
                    case 1:
                        _this.result = _state.sent();
                        return [
                            2,
                            _this.result
                        ];
                    case 2:
                        error = _state.sent();
                        _this.error = error;
                        throw error;
                    case 3:
                        return [
                            2
                        ];
                }
            });
        })();
    };
    return ConfigTask;
}();

/**
 * 触发或等待 config 返回
 */ function ensureCorpConfigReady() {
    return _ensureCorpConfigReady.apply(this, arguments);
}
function _ensureCorpConfigReady() {
    _ensureCorpConfigReady = _async_to_generator(function() {
        var originalTask, task;
        return __generator(this, function(_state) {
            originalTask = getCorpConfigTask();
            if (originalTask == null ? void 0 : originalTask.checkValid()) {
                return [
                    2,
                    originalTask.promise
                ];
            }
            task = new ConfigTask(CONFIG_TYPE_CORP, /*#__PURE__*/ _async_to_generator(function(url) {
                var registerOptions, data, params, result;
                return __generator(this, function(_state) {
                    switch(_state.label){
                        case 0:
                            registerOptions = getRegisterOptions();
                            if (!(registerOptions == null ? void 0 : registerOptions.getConfigSignature)) {
                                throw new SDKError("Missing getConfigSignature");
                            }
                            return [
                                4,
                                registerOptions.getConfigSignature(url)
                            ];
                        case 1:
                            data = _state.sent();
                            if (!task.checkValid()) {
                                throw new AbortError();
                            }
                            // 2. 构建参数
                            params = buildCorpConfigParams(registerOptions, data);
                            return [
                                4,
                                invoke("preVerifyJSAPI", {
                                    appId: params.appId,
                                    verifyJsApiList: params.jsApiList,
                                    verifyOpenTagList: params.openTagList,
                                    verifyAppId: params.appId,
                                    verifySignType: "sha1",
                                    verifyTimestamp: params.timestamp,
                                    verifyNonceStr: params.nonceStr,
                                    verifySignature: params.signature
                                })
                            ];
                        case 2:
                            result = _state.sent();
                            if (!task.checkValid()) {
                                throw new AbortError();
                            }
                            return [
                                2,
                                {
                                    params: params,
                                    result: result
                                }
                            ];
                    }
                });
            }));
            task.promise.then(function(res) {
                var registerOptions = getRegisterOptions();
                safeRun(registerOptions == null ? void 0 : registerOptions.onConfigSuccess, res.result);
                safeRun(registerOptions == null ? void 0 : registerOptions.onConfigComplete, res.result);
            }, function(error) {
                var registerOptions = getRegisterOptions();
                safeRun(registerOptions == null ? void 0 : registerOptions.onConfigFail, error);
                safeRun(registerOptions == null ? void 0 : registerOptions.onConfigComplete, error);
            });
            setCorpConfigTask(task);
            return [
                2,
                task.promise
            ];
        });
    });
    return _ensureCorpConfigReady.apply(this, arguments);
}
function buildCorpConfigParams(options, data) {
    return {
        appId: options.corpId,
        timestamp: "" + data.timestamp,
        nonceStr: data.nonceStr,
        signature: data.signature,
        jsApiList: mapJsApiListToClient(options.jsApiList || [
            "config"
        ]),
        openTagList: mapJsApiListToClient(options.openTagList || [])
    };
}

/**
 * 触发或等待 agentConfig 返回
 */ function ensureAgentConfigReady() {
    return _ensureAgentConfigReady.apply(this, arguments);
}
function _ensureAgentConfigReady() {
    _ensureAgentConfigReady = _async_to_generator(function() {
        var originalTask, task;
        return __generator(this, function(_state) {
            originalTask = getAgentConfigTask();
            if (originalTask == null ? void 0 : originalTask.checkValid()) {
                return [
                    2,
                    originalTask.promise
                ];
            }
            task = new ConfigTask(CONFIG_TYPE_AGENT, /*#__PURE__*/ _async_to_generator(function(url) {
                var configPromise, _ref, params, result;
                return __generator(this, function(_state) {
                    switch(_state.label){
                        case 0:
                            if (isWeCom && semverSubtract(wecomVersion, "3.0.24") < 0) {
                                configPromise = ensureCorpConfigReady();
                            }
                            return [
                                4,
                                Promise.all([
                                    resolveAgentConfigParams(url),
                                    configPromise
                                ])
                            ];
                        case 1:
                            _ref = _state.sent(), params = _ref[0];
                            if (!task.checkValid()) {
                                throw new AbortError();
                            }
                            return [
                                4,
                                invoke("agentConfig", params)
                            ];
                        case 2:
                            result = _state.sent();
                            if (!task.checkValid()) {
                                throw new AbortError();
                            }
                            return [
                                2,
                                {
                                    params: params,
                                    result: result
                                }
                            ];
                    }
                });
            }));
            task.promise.then(function(res) {
                var registerOptions = getRegisterOptions();
                safeRun(registerOptions == null ? void 0 : registerOptions.onAgentConfigSuccess, res.result);
                safeRun(registerOptions == null ? void 0 : registerOptions.onAgentConfigComplete, res.result);
            }, function(error) {
                var registerOptions = getRegisterOptions();
                safeRun(registerOptions == null ? void 0 : registerOptions.onAgentConfigFail, error);
                safeRun(registerOptions == null ? void 0 : registerOptions.onAgentConfigComplete, error);
            });
            setAgentConfigTask(task);
            return [
                2,
                task.promise
            ];
        });
    });
    return _ensureAgentConfigReady.apply(this, arguments);
}
function resolveAgentConfigParams(url) {
    return _resolveAgentConfigParams.apply(this, arguments);
}
function _resolveAgentConfigParams() {
    _resolveAgentConfigParams = _async_to_generator(function(url) {
        var registerOptions, data;
        return __generator(this, function(_state) {
            switch(_state.label){
                case 0:
                    registerOptions = getRegisterOptions();
                    if (!(registerOptions == null ? void 0 : registerOptions.getAgentConfigSignature)) {
                        throw new SDKError("Missing getAgentConfigSignature");
                    }
                    return [
                        4,
                        registerOptions.getAgentConfigSignature(url)
                    ];
                case 1:
                    data = _state.sent();
                    return [
                        2,
                        {
                            corpid: registerOptions.corpId,
                            agentid: "" + registerOptions.agentId,
                            timestamp: "" + data.timestamp,
                            nonceStr: data.nonceStr,
                            signature: data.signature,
                            jsApiList: mapJsApiListToClient(registerOptions.jsApiList || [
                                "agentConfig"
                            ])
                        }
                    ];
            }
        });
    });
    return _resolveAgentConfigParams.apply(this, arguments);
}

/**
 * 触发或等待 config、agentConfig 完成
 *
 * @example
 * ```ts
 * await ww.ensureConfigReady()
 * ```
 */ function ensureConfigReady() {
    var registerOptions = getRegisterOptions();
    if (isWeCom && (registerOptions == null ? void 0 : registerOptions.getAgentConfigSignature)) {
        return ensureAgentConfigReady();
    }
    return ensureCorpConfigReady();
}
/**
 * 在提供了 registerOptions 的情况下保证 config、agentConfig 完成
 *
 * @private
 */ function tryEnsureConfigReady() {
    var registerOptions = getRegisterOptions();
    if (registerOptions) {
        return ensureConfigReady();
    }
}

/**
 * 注册应用信息。
 *
 * @example
 * ```ts
 * ww.register({
 *   corpId: 'ww7ca4776b2a70000',
 *   jsApiList: ['getExternalContact'],
 *   getConfigSignature
 * })
 * ```
 */ function register(options) {
    setRegisterOptions(options);
    if (isWeChat || isWeCom) {
        ensureConfigReady();
    }
}

/**
 * 透传 JSAPI 调用
 */ function passthrough(name, params) {
    return _passthrough.apply(this, arguments);
}
function _passthrough() {
    _passthrough = _async_to_generator(function(name, params, jsapiParams) {
        return __generator(this, function(_state) {
            if (jsapiParams === void 0) jsapiParams = params;
            return [
                2,
                promiseToCallback(params, /*#__PURE__*/ _async_to_generator(function() {
                    return __generator(this, function(_state) {
                        switch(_state.label){
                            case 0:
                                return [
                                    4,
                                    tryEnsureConfigReady()
                                ];
                            case 1:
                                _state.sent();
                                return [
                                    2,
                                    invoke(name, jsapiParams)
                                ];
                        }
                    });
                }))
            ];
        });
    });
    return _passthrough.apply(this, arguments);
}
/**
 * 绑定事件
 */ function bindShare(name, params, handler) {
    return _bindShare.apply(this, arguments);
}
function _bindShare() {
    _bindShare = _async_to_generator(function(name, params, handler) {
        return __generator(this, function(_state) {
            switch(_state.label){
                case 0:
                    return [
                        4,
                        tryEnsureConfigReady()
                    ];
                case 1:
                    _state.sent();
                    on(name, function() {
                        promiseToCallback(params, handler);
                    });
                    return [
                        2
                    ];
            }
        });
    });
    return _bindShare.apply(this, arguments);
}
/**
 * 把 promise 转为 success/fail callbacak 回调
 */ function promiseToCallback(params, factory) {
    return factory(params).then(function(result) {
        safeRun(params == null ? void 0 : params.success, result);
        safeRun(params == null ? void 0 : params.complete, result);
        return result;
    }, function(error) {
        var keyword = extractErrMsgKeyword(error.errMsg);
        if (keyword === "cancel") {
            safeRun(params == null ? void 0 : params.cancel, error);
        } else {
            safeRun(params == null ? void 0 : params.fail, error);
        }
        safeRun(params == null ? void 0 : params.complete, error);
        throw error;
    });
}

var Proximity;
(function(Proximity) {
    /**
   * CLProximityUnknown
   */ Proximity["CLProximityUnknown"] = "0";
    /**
   * CLProximityImmediate
   */ Proximity["CLProximityImmediate"] = "1";
    /**
   * CLProximityNear
   */ Proximity["CLProximityNear"] = "2";
    /**
   * CLProximityFar
   */ Proximity["CLProximityFar"] = "3";
})(Proximity || (Proximity = {}));
/**
 * 开启查找周边 iBeacon 设备
 *
 * @see https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html
 * @compat WeChat
 */ function startSearchBeacons(params) {
    if (params === void 0) params = {};
    return passthrough("startMonitoringBeacons", params);
}
/**
 * 关闭查找周边 iBeacon 设备
 *
 * @see https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html
 * @compat WeChat
 */ function stopSearchBeacons(params) {
    if (params === void 0) params = {};
    return passthrough("stopMonitoringBeacons", params);
}
/**
 * 监听周边 iBeacon 设备接口
 *
 * @see https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html
 * @compat WeChat
 */ function onSearchBeacons(callback) {
    on("onBeaconsInRange", callback);
}

/**
 * 初始化蓝牙模块。
 *
 * @compat WeCom
 *
 * @example
 * ```ts
 * ww.openBluetoothAdapter()
 * ```
 */ function openBluetoothAdapter(params) {
    if (params === void 0) params = {};
    return passthrough("openBluetoothAdapter", params);
}
/**
 * 关闭蓝牙模块。
 *
 * 调用该方法将断开所有已建立的链接并释放系统资源。
 *
 * @compat WeCom
 *
 * @example
 * ```ts
 * ww.closeBluetoothAdapter()
 * ```
 */ function closeBluetoothAdapter(params) {
    if (params === void 0) params = {};
    return passthrough("closeBluetoothAdapter", params);
}
/**
 * 获取本机蓝牙适配器状态。
 *
 * @compat WeCom
 *
 * @example
 * ```ts
 * ww.getBluetoothAdapterState()
 * ```
 */ function getBluetoothAdapterState(params) {
    if (params === void 0) params = {};
    return passthrough("getBluetoothAdapterState", params);
}
/**
 * 监听蓝牙适配器状态变化。
 *
 * @compat WeCom
 *
 * @example
 * ```ts
 * ww.onBluetoothAdapterStateChange(function(event) {
 *   console.log(event)
 * })
 * ```
 */ function onBluetoothAdapterStateChange(callback) {
    on("onBluetoothAdapterStateChange", callback);
}
/**
 * 开始搜寻附近的蓝牙外围设备。
 *
 * @compat WeCom
 *
 * @example
 * ```ts
ww.startBluetoothDevicesDiscovery({
  services: ['FEE7']
})
 * ```
 */ function startBluetoothDevicesDiscovery(params) {
    if (params === void 0) params = {};
    return passthrough("startBluetoothDevicesDiscovery", params);
}
/**
 * 停止搜寻附近的蓝牙外围设备。
 *
 * 若已经找到需要的蓝牙设备并不需要继续搜索时，建议调用该接口停止蓝牙搜索。
 *
 * @compat WeCom
 *
 * @example
 * ```ts
 * ww.stopBluetoothDevicesDiscovery()
 * ```
 */ function stopBluetoothDevicesDiscovery(params) {
    if (params === void 0) params = {};
    return passthrough("stopBluetoothDevicesDiscovery", params);
}
/**
 * 获取在蓝牙模块生效期间所有已发现的蓝牙设备。
 *
 * @note
 * - 该接口获取到的设备列表为蓝牙模块生效期间所有搜索到的蓝牙设备，若在蓝牙模块使用流程结束后未及时调用 closeBluetoothAdapter 释放资源，
 *   调用该接口可能会返回之前蓝牙使用流程中搜索到的蓝牙设备，可能设备已经不在用户身边，无法连接
 * - 蓝牙设备在被搜索到时，系统返回的 name 字段一般为广播包中的 LocalName 字段中的设备名称，而如果与蓝牙设备建立连接，系统返回的 name
 *   字段会改为从蓝牙设备上获取到的 GattName。若需要动态改变设备名称并展示，建议使用 localName 字段
 *
 * @compat WeCom
 *
 * @example
 * ```ts
 * ww.getBluetoothDevices()
 * ```
 */ function getBluetoothDevices(params) {
    if (params === void 0) params = {};
    return promiseToCallback(params, /*#__PURE__*/ _async_to_generator(function() {
        var res, _iterator, _step, device;
        return __generator(this, function(_state) {
            switch(_state.label){
                case 0:
                    return [
                        4,
                        tryEnsureConfigReady()
                    ];
                case 1:
                    _state.sent();
                    return [
                        4,
                        invoke("getBluetoothDevices")
                    ];
                case 2:
                    res = _state.sent();
                    for(_iterator = _create_for_of_iterator_helper_loose(res.devices || []); !(_step = _iterator()).done;){
                        device = _step.value;
                        device.advertisData = base64ToArrayBuffer(device.advertisData);
                        device.serviceData = base64ToArrayBuffer(device.serviceData);
                    }
                    return [
                        2,
                        res
                    ];
            }
        });
    }));
}
/**
 * 监听寻找到新设备。
 *
 * @note
 * - 若在该接口中回调了某个设备，则此设备会添加到 getBluetoothDevices 接口返回的设备列表中
 *
 * @compat WeCom
 *
 * @example
 * ```ts
 * ww.onBluetoothDeviceFound(function(event) {
 *   console.log(event)
 * })
 * ```
 */ function onBluetoothDeviceFound(callback) {
    on("onBluetoothDeviceFound", callback);
}
/**
 * 根据 uuid 获取处于已连接状态的设备。
 *
 * @compat WeCom
 *
 * @example
 * ```ts
 * ww.getConnectedBluetoothDevices({
 *   services: ['FEE7']
 * })
 * ```
 */ function getConnectedBluetoothDevices(params) {
    return passthrough("getConnectedBluetoothDevices", params);
}
/**
 * 连接低功耗蓝牙设备。
 *
 * @note
 * - 安卓手机上如果多次调用 createBLEConnection 创建连接，有可能导致系统持有同一设备多个连接的实例，导致调用 closeBLEConnection 的时候并不能真正的断开与设备的连接。因此请保证尽量成对的调用 create 和 close 接口
 * - 蓝牙链接随时可能断开，建议监听 onBLEConnectionStateChange 回调事件，当蓝牙设备断开时按需执行重连操作
 * - 若对未连接的设备或已断开连接的设备调用数据读写操作的接口，会返回 10006 错误，建议进行重连操作
 *
 * @compat WeCom
 *
 * @example
 * ```ts
 * ww.createBLEConnection({
 *   deviceId: deviceId
 * })
 * ```
 */ function createBLEConnection(params) {
    return passthrough("createBLEConnection", params);
}
/**
 * 断开与低功耗蓝牙设备的连接。
 *
 * @compat WeCom
 *
 * @example
 * ```ts
 * ww.closeBLEConnection({
 *   deviceId: deviceId
 * })
 * ```
 */ function closeBLEConnection(params) {
    return passthrough("closeBLEConnection", params);
}
/**
 * 监听低功耗蓝牙连接状态的改变事件，包括开发者主动连接或断开连接，设备丢失，连接异常断开等等。
 *
 * @compat WeCom
 *
 * @example
 * ```ts
 * ww.onBLEConnectionStateChange(function(event) {
 *   console.log(event)
 * })
 * ```
 */ function onBLEConnectionStateChange(callback) {
    on("onBLEConnectionStateChange", callback);
}
/**
 * 获取蓝牙设备所有 service（服务）。
 *
 * @compat WeCom
 *
 * @example
 * ```ts
 * ww.getBLEDeviceServices({
 *   deviceId: deviceId
 * })
 * ```
 */ function getBLEDeviceServices(params) {
    return passthrough("getBLEDeviceServices", params);
}
/**
 * 获取蓝牙设备某个服务中的所有 characteristic（特征值）。
 *
 * @compat WeCom
 *
 * @example
 * ```ts
 * ww.getBLEDeviceCharacteristics({
 *   deviceId: deviceId,
 *   serviceId: serviceId
 * })
 * ```
 */ function getBLEDeviceCharacteristics(params) {
    return passthrough("getBLEDeviceCharacteristics", params);
}
/**
 * 读取低功耗蓝牙设备的特征值的二进制数据值。
 *
 * @note
 * - 设备的特征值必须支持 read 才可以成功调用，具体参照 characteristic 的 properties 属性
 * - 并行调用多次读写接口存在读写失败的可能性
 * - 接口读取到的信息需要在 onBLECharacteristicValueChange 的回调中获取
 *
 * @compat WeCom
 *
 * @example
 * ```ts
 * ww.readBLECharacteristicValue({
 *   deviceId: deviceId,
 *   serviceId: serviceId,
 *   characteristicId: characteristicId
 * })
 * ```
 */ function readBLECharacteristicValue(params) {
    return passthrough("readBLECharacteristicValue", params);
}
/**
 * 向低功耗蓝牙设备特征值中写入二进制数据。
 *
 * @note
 * - 设备的特征值必须支持 write 才可以成功调用，具体参照 characteristic 的 properties 属性
 * - 并行调用多次读写接口存在读写失败的可能性
 * - 接口不会对写入数据包大小做限制，但系统与蓝牙设备会确定蓝牙 4.0 单次传输的数据大小，超过最大字节数后会发生写入错误，建议每次写入不超过 20 字节
 * - 安卓平台上，在调用 notify 成功后立即调用 write 接口，在部分机型上会发生 10008 系统错误
 * - 若单次写入数据过长，iOS 平台上存在系统不会有任何回调的情况（包括错误回调）
 *
 * @compat WeCom
 *
 * @example
 * ```ts
 * ww.writeBLECharacteristicValue({
 *   deviceId: deviceId,
 *   serviceId: serviceId,
 *   characteristicId: characteristicId,
 *   value: arrayBufferValue
 * })
 * ```
 */ function writeBLECharacteristicValue(params) {
    return passthrough("writeBLECharacteristicValue", params, {
        deviceId: params.deviceId,
        serviceId: params.serviceId,
        characteristicId: params.characteristicId,
        value: arrayBufferToBase64(params.value)
    });
}
/**
 * 启用低功耗蓝牙设备特征值变化时的 notify 功能，订阅特征值。
 *
 * @note
 * - 设备的特征值必须支持 notify 或者 indicate 才可以成功调用，具体参照 characteristic 的 properties 属性
 * - 订阅操作成功后需要设备主动更新特征值的 value 才会触发 onBLECharacteristicValueChange 回调
 * - 安卓平台上，在调用 notify 成功后立即调用 write 接口，在部分机型上会发生 10008 系统错误
 *
 * @compat WeCom
 *
 * @example
 * ```ts
 * ww.notifyBLECharacteristicValueChange({
 *   deviceId: deviceId,
 *   serviceId: serviceId,
 *   characteristicId: characteristicId,
 *   state: true
 * })
 * ```
 */ function notifyBLECharacteristicValueChange(params) {
    return passthrough("notifyBLECharacteristicValueChange", params);
}
/**
 * 监听低功耗蓝牙设备的特征值变化。
 *
 * 必须先启用 notify 才能接收到设备推送的 notification。
 *
 * @compat WeCom
 *
 * @example
 * ```ts
 * ww.onBLECharacteristicValueChange(function(event) {
 *   console.log(event)
 * })
 * ```
 */ function onBLECharacteristicValueChange(callback) {
    on("onBLECharacteristicValueChange", function(event) {
        if (event == null ? void 0 : event.value) {
            event.value = base64ToArrayBuffer(event.value);
        }
        callback(event);
    });
}

/**
 * 设置系统剪贴板的内容。
 *
 * @compat WeCom iOS, Android >= 2.4.16; WeCom PC, Mac >= 3.1.2
 *
 * @example
 * ```ts
 * ww.setClipboardData({
 *   data: 'data'
 * })
 * ```
 */ function setClipboardData(params) {
    return passthrough("setClipboardData", params);
}
/**
 * 获取系统剪贴板内容。
 *
 * @compat WeCom >= 3.1.2
 *
 * @example
 * ```ts
 * ww.getClipboardData()
 * ```
 */ function getClipboardData(params) {
    if (params === void 0) params = {};
    return passthrough("getClipboardData", params);
}

/**
 * 开始搜索附近的 iBeacon 设备。
 *
 * @compat WeCom
 *
 * @example
 * ```ts
 * ww.startBeaconDiscovery({
 *   uuids: ['uuid']
 * })
 * ```
 */ function startBeaconDiscovery(params) {
    return passthrough("startBeaconDiscovery", params);
}
/**
 * 停止搜索附近的 iBeacon 设备。
 *
 * @compat WeCom
 *
 * @example
 * ```ts
 * ww.stopBeaconDiscovery()
 * ```
 */ function stopBeaconDiscovery(params) {
    if (params === void 0) params = {};
    return passthrough("stopBeaconDiscovery", params);
}
/**
 * 获取所有已搜索到的 iBeacon 设备。
 *
 * @compat WeCom
 *
 * @example
 * ```ts
 * ww.getBeacons()
 * ```
 */ function getBeacons(params) {
    if (params === void 0) params = {};
    return passthrough("getBeacons", params);
}
/**
 * 监听 iBeacon 设备的更新事件。
 *
 * @compat WeCom
 *
 * @example
 * ```ts
 * ww.onBeaconUpdate(function(event) {
 *   console.log(event)
 * })
 * ```
 */ function onBeaconUpdate(callback) {
    on("onBeaconUpdate", callback);
}
/**
 * 监听 iBeacon 服务的状态变化。
 *
 * @compat WeCom
 *
 * @example
 * ```ts
 * ww.onBeaconServiceChange(function(event) {
 *   console.log(event)
 * })
 * ```
 */ function onBeaconServiceChange(callback) {
    on("onBeaconServiceChange", callback);
}

var LocationType;
(function(LocationType) {
    /**
   * gps 坐标
   */ LocationType["wgs84"] = "wgs84";
    /**
   * 火星坐标
   */ LocationType["gcj02"] = "gcj02";
})(LocationType || (LocationType = {}));
/**
 * 使用企业微信内置地图查看位置。
 *
 * @example
 * ```ts
 * ww.openLocation({
 *   latitude: 0,
 *   longitude: 0,
 *   name: 'name',
 *   address: 'address',
 *   scale: 1
 * })
 * ```
 */ function openLocation(params) {
    return passthrough("openLocation", params, {
        latitude: params.latitude,
        longitude: params.longitude,
        name: params.name || "",
        address: params.address || "",
        scale: params.scale || 28,
        infoUrl: params.infoUrl || ""
    });
}
/**
 * 获取地理位置。
 *
 * @example
 * ```ts
 * ww.getLocation({
 *   type: 'wgs84'
 * })
 * ```
 */ function getLocation(params) {
    if (params === void 0) params = {};
    return promiseToCallback(params, /*#__PURE__*/ _async_to_generator(function() {
        var res;
        return __generator(this, function(_state) {
            switch(_state.label){
                case 0:
                    return [
                        4,
                        tryEnsureConfigReady()
                    ];
                case 1:
                    _state.sent();
                    return [
                        4,
                        invoke("geoLocation", {
                            type: params.type || "wgs84"
                        })
                    ];
                case 2:
                    res = _state.sent();
                    delete res.type;
                    return [
                        2,
                        res
                    ];
            }
        });
    }));
}
/**
 * 打开持续定位。
 *
 * @compat WeCom >= 2.4.20
 *
 * @example
 * ```ts
 * ww.startAutoLBS({
 *   type: 'gcj02'
 * })
 * ```
 */ function startAutoLBS(params) {
    return passthrough("startAutoLBS", params);
}
/**
 * 停止持续定位。
 *
 * @compat WeCom >= 2.4.20
 *
 * @example
 * ```ts
 * ww.stopAutoLBS()
 * ```
 */ function stopAutoLBS(params) {
    if (params === void 0) params = {};
    return passthrough("stopAutoLBS", params);
}
/**
 * 监听地理位置的变化。
 *
 * @limit
 * - 需要提前调用 startAutoLBS
 * - 需要用户停留在当前页面
 *
 * @compat WeCom >= 2.4.20
 *
 * @example
 * ```ts
 * ww.onLocationChange(function(event) {
 *   console.log(event)
 * })
 * ```
 */ function onLocationChange(callback) {
    on("auto:location:report", callback);
}

var NetworkType;
(function(NetworkType) {
    /**
   * wifi
   */ NetworkType["wifi"] = "wifi";
    /**
   * 2g
   */ NetworkType["network2g"] = "2g";
    /**
   * 3g
   */ NetworkType["network3g"] = "3g";
    /**
   * 4g
   */ NetworkType["network4g"] = "4g";
    /**
   * 无网络
   */ NetworkType["none"] = "none";
    /**
   * Android下不常见的网络类型
   */ NetworkType["unknown"] = "unknown";
})(NetworkType || (NetworkType = {}));
/**
 * 获取网络状态。
 *
 * @compat WeCom iOS, Android; WeChat
 *
 * @example
 * ```ts
 * ww.getNetworkType()
 * ```
 */ function getNetworkType(params) {
    if (params === void 0) params = {};
    return passthrough("getNetworkType", params);
}
/**
 * 监听网络状态变化。
 *
 * @compat WeCom iOS, Android
 *
 * @example
 * ```ts
 * ww.onNetworkStatusChange(function(event) {
 *   console.log(event)
 * })
 * ```
 */ function onNetworkStatusChange(callback) {
    on("onNetworkStatusChange", callback);
}

/**
 * 初始化 Wi-Fi 模块。
 *
 * @compat WeCom iOS, Android >= 2.4.16
 *
 * @example
 * ```ts
 * ww.startWifi()
 * ```
 */ function startWifi(params) {
    if (params === void 0) params = {};
    return passthrough("startWifi", params);
}
/**
 * 关闭 Wi-Fi 模块。
 *
 * @compat WeCom iOS, Android >= 2.4.16
 *
 * @example
 * ```ts
 * ww.stopWifi()
 * ```
 */ function stopWifi(params) {
    if (params === void 0) params = {};
    return passthrough("stopWifi", params);
}
/**
 * 连接 Wi-Fi。
 *
 * @compat WeCom iOS, Android >= 2.4.16
 *
 * @example
 * ```ts
 * ww.connectWifi({
 *   SSID: 'vincenthome',
 *   BSSID: '8c:a6:df:c8:f7:4b',
 *   password: 'test1234',
 * })
 * ```
 */ function connectWifi(params) {
    return passthrough("connectWifi", params);
}
/**
 * 获取 Wi-Fi 列表。
 *
 * @compat WeCom iOS, Android >= 2.4.16
 *
 * @example
 * ```ts
 * ww.getWifiList()
 * ```
 */ function getWifiList(params) {
    if (params === void 0) params = {};
    return passthrough("getWifiList", params);
}
/**
 * 监听 Wi-Fi 列表更新。
 *
 * @compat WeCom iOS, Android >= 2.4.16
 *
 * @example
 * ```ts
 * ww.onGetWifiList(function(event) {
 *   console.log(event)
 * })
 * ```
 */ function onGetWifiList(callback) {
    on("onGetWifiList", callback);
}
/**
 * 监听 Wi-Fi 连接成功。
 *
 * @compat WeCom iOS, Android >= 2.4.16
 *
 * @example
 * ```ts
 * ww.onWifiConnected(function(event) {
 *   console.log(event)
 * })
 * ```
 */ function onWifiConnected(callback) {
    on("onWifiConnected", callback);
}
/**
 * 获取已连接中的 Wi-Fi 信息。
 *
 * @compat WeCom iOS, Android >= 2.4.16
 *
 * @example
 * ```ts
 * ww.getConnectedWifi()
 * ```
 */ function getConnectedWifi(params) {
    if (params === void 0) params = {};
    return passthrough("getConnectedWifi", params);
}

/**
 * 预览文件。
 *
 * @compat WeCom iOS, Android
 *
 * @example
 * ```ts
 * wx.previewFile({
 *   url: 'http://open.work.weixin.qq.com/wwopen/downloadfile/wwapi.zip',
 *   name: 'Android开发工具包集合',
 *   size: 22189
 * })
 * ```
 */ function previewFile(params) {
    return passthrough("previewFile", params);
}

var SizeType;
(function(SizeType) {
    /**
   * 原图
   */ SizeType["original"] = "original";
    /**
   * 压缩后的图片
   */ SizeType["compressed"] = "compressed";
})(SizeType || (SizeType = {}));
var SourceType;
(function(SourceType) {
    /**
   * 相册
   */ SourceType["album"] = "album";
    /**
   * 相机
   */ SourceType["camera"] = "camera";
})(SourceType || (SourceType = {}));
var CameraMode;
(function(CameraMode) {
    /**
   * 单拍
   */ CameraMode["normal"] = "normal";
    /**
   * 连拍
   *
   * @compat WeCom >= 2.3.0
   */ CameraMode["batch"] = "batch";
    /**
   * 前置摄像头单拍
   *
   * @compat WeCom >= 3.0.26
   */ CameraMode["front"] = "front";
    /**
   * 前置摄像头连拍
   *
   * @compat WeCom >= 3.0.26
   */ CameraMode["batch_front"] = "batch_front";
})(CameraMode || (CameraMode = {}));
/**
 * 拍照或从手机相册中选图。
 *
 * @example
 * ```ts
 * ww.chooseImage({
 *   count: 1,
 *   sizeType: ['original', 'compressed'],
 *   sourceType: ['album', 'camera'],
 *   defaultCameraMode: 'batch',
 *   isSaveToAlbum: true
 * })
 * ```
 */ function chooseImage(params) {
    if (params === void 0) params = {};
    return promiseToCallback(params, /*#__PURE__*/ _async_to_generator(function() {
        var res;
        return __generator(this, function(_state) {
            switch(_state.label){
                case 0:
                    return [
                        4,
                        tryEnsureConfigReady()
                    ];
                case 1:
                    _state.sent();
                    return [
                        4,
                        invoke("chooseImage", {
                            scene: "1|2",
                            count: params.count || 9,
                            sizeType: params.sizeType || [
                                "original",
                                "compressed"
                            ],
                            sourceType: params.sourceType || [
                                "album",
                                "camera"
                            ],
                            defaultCameraMode: params.defaultCameraMode || "normal",
                            isSaveToAlbum: isFalsy(params.isSaveToAlbum) ? 0 : 1
                        })
                    ];
                case 2:
                    res = _state.sent();
                    if (isAndroid && res.localIds) {
                        res.localIds = res.localIds || "[]";
                        try {
                            res.localIds = JSON.parse(res.localIds);
                        } catch (error) {
                        // noop
                        }
                    }
                    return [
                        2,
                        res
                    ];
            }
        });
    }));
}
/**
 * 预览图片。
 *
 * @example
 * ```ts
 * ww.previewImage({
 *   current: imgURL,
 *   urls: [imgURL]
 * });
 * ```
 */ function previewImage(params) {
    return passthrough("imagePreview", params);
}
/**
 * 上传图片。
 *
 * 上传的图片有效期 3 天，可用[素材管理](#90253)接口下载图片到自己的服务器，此处获得的 serverId 即 media_id。
 *
 * @example
 * ```ts
 * ww.uploadImage({
 *   localId: localId,
 *   isShowProgressTips: true
 * })
 * ```
 */ function uploadImage(params) {
    return passthrough("uploadImage", params, {
        localId: params.localId,
        isShowProgressTips: isFalsy(params.isShowProgressTips) ? 0 : 1
    });
}
/**
 * 下载图片。
 *
 * @example
 * ```ts
 * ww.downloadImage({
 *   serverId: serverId,
 *   isShowProgressTips: true
 * })
 * ```
 */ function downloadImage(params) {
    return passthrough("downloadImage", params, {
        serverId: params.serverId,
        isShowProgressTips: isFalsy(params.isShowProgressTips) ? 0 : 1
    });
}
/**
 * 获取本地图片内容。
 *
 * 仅在 iOS WKWebView 下支持。
 *
 * @compat WeCom iOS >= 2.4.6
 *
 * @example
 * ```ts
 * ww.getLocalImgData({
 *   localId: localId
 * })
 * ```
 */ function getLocalImgData(params) {
    return passthrough("getLocalImgData", params);
}

/**
 * 开始录音。
 *
 * @example
 * ```ts
 * ww.startRecord()
 * ```
 */ function startRecord(params) {
    if (params === void 0) params = {};
    return passthrough("startRecord", params);
}
/**
 * 停止录音。
 *
 * @example
 * ```ts
 * ww.stopRecord()
 * ```
 */ function stopRecord(params) {
    if (params === void 0) params = {};
    return passthrough("stopRecord", params);
}
/**
 * 监听录音自动停止。
 *
 * @example
 * ```ts
 * ww.onVoiceRecordEnd({
 *   localId: localId
 * })
 * ```
 */ function onVoiceRecordEnd(callback) {
    on("onVoiceRecordEnd", callback);
}
/**
 * 播放语音。
 *
 * @example
 * ```ts
 * ww.playVoice({
 *   localId: localId
 * })
 * ```
 */ function playVoice(params) {
    return passthrough("playVoice", params);
}
/**
 * 暂停播放。
 *
 * @example
 * ```ts
 * ww.pauseVoice({
 *   localId: localId
 * })
 * ```
 */ function pauseVoice(params) {
    return passthrough("pauseVoice", params);
}
/**
 * 停止播放。
 *
 * @example
 * ```ts
 * ww.stopVoice({
 *   localId: localId
 * })
 * ```
 */ function stopVoice(params) {
    return passthrough("stopVoice", params);
}
/**
 * 监听语音播放完毕。
 *
 * @example
 * ```ts
 * ww.onVoicePlayEnd(function(event) {
 *   console.log(event)
 * })
 * ```
 */ function onVoicePlayEnd(callback) {
    on("onVoicePlayEnd", callback);
}
/**
 * 上传语音。
 *
 * 上传语音有效期 3 天，可以通过[素材管理](https://developer.work.weixin.qq.com/document/path/91054)接口下载语音到自己的服务器，接口返回的的 `serverId` 即 `media_id`。
 *
 * @example
 * ```ts
 * ww.uploadVoice({
 *   localId: localId,
 *   isShowProgressTips: true
 * })
 * ```
 */ function uploadVoice(params) {
    return passthrough("uploadVoice", params, {
        localId: params.localId,
        isShowProgressTips: isFalsy(params.isShowProgressTips) ? 0 : 1
    });
}
/**
 * 下载语音。
 *
 * @example
 * ```ts
 * ww.downloadVoice({
 *   serverId: serverId,
 *   isShowProgressTips: true
 * })
 * ```
 */ function downloadVoice(params) {
    return passthrough("downloadVoice", params, {
        serverId: params.serverId,
        isShowProgressTips: isFalsy(params.isShowProgressTips) ? 0 : 1
    });
}
/**
 * 语音转文字。
 *
 * @compat WeCom iOS, Android >= 2.7.5
 *
 * @example
 * ```ts
 * ww.translateVoice({
 *   localId: localId,
 *   isShowProgressTips: true
 * })
 * ```
 */ function translateVoice(params) {
    return passthrough("translateVoice", params, {
        localId: params.localId,
        isShowProgressTips: isFalsy(params.isShowProgressTips) ? 0 : 1
    });
}

var LiveType;
(function(LiveType) {
    /**
   * 通用直播
   */ LiveType[LiveType["common"] = 0] = "common";
    /**
   * 企业培训
   */ LiveType[LiveType["corp_training"] = 1] = "corp_training";
    /**
   * 大班课
   */ LiveType[LiveType["edu_normal_class"] = 2] = "edu_normal_class";
    /**
   * 小班课
   */ LiveType[LiveType["edu_small_class"] = 3] = "edu_small_class";
})(LiveType || (LiveType = {}));
function startLiving(params) {
    if (params === void 0) params = {};
    return passthrough("startLiving", params);
}
/**
 * 调起直播间回放页面。
 *
 * @limit
 * - 应用需具有直播使用权限，见[配置可使用直播的应用](#25967/配置可使用直播的应用)
 *
 * @compat WeCom >= 3.1.0
 *
 * @example
 * ```ts
 * ww.replayLiving({
 *   livingId: 'LIVINGID'
 * })
 * ```
 */ function replayLiving(params) {
    return passthrough("replayLiving", params);
}
/**
 * 调起直播回放下载页面。
 *
 * @limit
 * - 应用需具有直播使用权限，见[配置可使用直播的应用](#25967/配置可使用直播的应用)
 * - 只允许直播的发起人下载直播回放
 *
 * @compat WeCom PC >= 3.1.0
 *
 * @example
 * ```ts
 * ww.downloadLivingReplay({
 *   livingId: 'LIVINGID'
 * })
 * ```
 */ function downloadLivingReplay(params) {
    return passthrough("downloadLivingReplay", params);
}

function startMeeting(params) {
    if (params === void 0) params = {};
    return passthrough("startMeeting", params);
}

/**
 * 选择可分享的在线文档
 *
 * @compat WeCom >= 4.0.12
 */ function wedocSelectDoc(params) {
    if (params === void 0) params = {};
    return passthrough("wedocSelectDoc", params);
}

/**
 * 选择可上传的目录/空间
 *
 * 选中一个具有可上传权限的目录/空间，返回选中目录/空间对应的 selectedTicket
 *
 * @compat WeCom >= 4.0.12
 */ function wedriveSelectDir(params) {
    if (params === void 0) params = {};
    return passthrough("wedriveSelectDir", params);
}
/**
 * 选择可分享的文件
 *
 * 选中一个或多个具有可分享权限的微盘文件或在线文档，返回选中文件的 url
 *
 * @compat WeCom >= 4.0.12
 */ function wedriveSelectFileForShare(params) {
    return passthrough("wedriveSelectFileForShare", params);
}
/**
 * 选择可下载的文件
 *
 * 选中一个或多个具有下载权限的文件（只能是微盘文件，不支持在线文档），返回选中文件对应的 selectedTickets 列表
 *
 * @compat WeCom >= 4.0.12
 */ function wedriveSelectFileForDownload(params) {
    return passthrough("wedriveSelectFileForDownload", params);
}

/**
 * 监听页面返回事件。
 *
 * @param callback 回调函数，返回 false 则表示中断此次返回操作
 *
 * @limit
 * - 当页面左上角没有关闭按钮，不产生该事件
 * - iOS 系统下使用手势返回时，不产生该事件
 *
 * @compat WeCom iOS, Android >= 2.2.0; WeCom PC, Mac >= 2.4.5
 *
 * @example
 * ```ts
 * ww.onHistoryBack(function() {
 *   return confirm('确定放弃当前页面的修改？')
 * })
 * ```
 */ function onHistoryBack(callback) {
    on("historyBack", callback);
}
/**
 * 隐藏右上角菜单。
 *
 * @example
 * ```ts
 * ww.hideOptionMenu()
 * ```
 */ function hideOptionMenu(params) {
    if (params === void 0) params = {};
    return passthrough("hideOptionMenu", params);
}
/**
 * 显示右上角菜单。
 *
 * @example
 * ```ts
 * ww.showOptionMenu()
 * ```
 */ function showOptionMenu(params) {
    if (params === void 0) params = {};
    return passthrough("showOptionMenu", params);
}
/**
 * 关闭当前网页窗口。
 *
 * @example
 * ```ts
 * ww.closeWindow()
 * ```
 */ function closeWindow(params) {
    if (params === void 0) params = {};
    return passthrough("closeWindow", params);
}
/**
 * 批量隐藏功能按钮。
 *
 * 完整功能按钮列表请参考[所有菜单项列表](#90508)。
 *
 * @example
 * ```ts
 * ww.hideMenuItems({
 *   menuList: ['menuItem:setFont']
 * })
 * ```
 */ function hideMenuItems(params) {
    return passthrough("hideMenuItems", params);
}
/**
 * 批量显示功能按钮。
 *
 * 完整功能按钮列表请参考[所有菜单项列表](#90508)。
 *
 * @example
 * ```ts
 * ww.showMenuItems({
 *   menuList: ['menuItem:setFont']
 * })
 * ```
 */ function showMenuItems(params) {
    return passthrough("showMenuItems", params);
}
/**
 * 隐藏所有非基础按钮。
 *
 * @example
 * ```ts
 * ww.hideAllNonBaseMenuItem()
 * ```
 */ function hideAllNonBaseMenuItem(params) {
    if (params === void 0) params = {};
    return passthrough("hideAllNonBaseMenuItem", params);
}
/**
 * 显示所有功能按钮。
 *
 * @example
 * ```ts
 * ww.showAllNonBaseMenuItem()
 * ```
 */ function showAllNonBaseMenuItem(params) {
    if (params === void 0) params = {};
    return passthrough("showAllNonBaseMenuItem", params);
}
/**
 * 打开系统默认浏览器。
 *
 * @compat WeCom PC >= 2.3.0
 *
 * @example
 * ```ts
 * ww.openDefaultBrowser({
 *   url: 'https://work.weixin.qq.com/'
 * })
 * ```
 */ function openDefaultBrowser(params) {
    return passthrough("openDefaultBrowser", params);
}
/**
 * 监听用户截屏事件。
 *
 * @compat WeCom iOS, Android >= 2.5.0
 *
 * @example
 * ```ts
 * ww.onUserCaptureScreen(function() {
 *   console.log('用户截屏了')
 * })
 * ```
 */ function onUserCaptureScreen(callback) {
    on("onUserCaptureScreen", callback);
}

/**
 * 获取「转发」按钮点击状态并自定义分享内容。
 *
 * @note
 * - 微信客户端即将废弃该接口
 *
 * @limit
 * - 仅激活成员数超过 200 人且已经认证的企业才可在微信上调用
 *
 * @example
 * ```ts
 * ww.onMenuShareAppMessage({
 *   title: '企业微信',
 *   desc: '让每个企业都有自己的微信',
 *   link: 'https://work.weixin.qq.com/',
 *   imgUrl: 'https://res.mail.qq.com/node/ww/wwmng/style/images/index_share_logo$13c64306.png',
 *   success() {
 *     // 用户确认分享后回调
 *   },
 *   cancel() {
 *     // 用户取消分享后回调
 *   }
 * })
 * ```
 */ function onMenuShareAppMessage(params) {
    bindShare("menu:share:appmessage", params, function() {
        return invoke("sendAppMessage", {
            title: params.title || getTitle(),
            desc: params.desc || "",
            link: params.link || getHref(),
            img_url: params.imgUrl || "",
            type: params.type || "link",
            data_url: params.dataUrl || ""
        });
    });
}
/**
 * 获取「分享到朋友圈」按钮点击状态并自定义分享内容。
 *
 * @note
 * - 微信客户端即将废弃该接口
 *
 * @example
 * ```ts
 * ww.onMenuShareTimeline({
 *   title: '企业微信',
 *   link: 'https://work.weixin.qq.com/',
 *   imgUrl: 'https://res.mail.qq.com/node/ww/wwmng/style/images/index_share_logo$13c64306.png',
 *   success() {
 *     // 用户确认分享后回调
 *   },
 *   cancel() {
 *     // 用户取消分享后回调
 *   }
 * })
 * ```
 */ function onMenuShareTimeline(params) {
    bindShare("menu:share:timeline", params, function() {
        return invoke("shareTimeline", {
            title: params.title || getTitle(),
            desc: params.title || getTitle(),
            img_url: params.imgUrl || "",
            link: params.link || getHref(),
            type: params.type || "link",
            data_url: params.dataUrl || ""
        });
    });
}
/**
 * 获取「微信」按钮点击状态并自定义分享内容。
 *
 * @compat WeCom
 *
 * @example
 * ```ts
 * ww.onMenuShareWechat({
 *   title: '企业微信',
 *   desc: '让每个企业都有自己的微信',
 *   link: 'https://work.weixin.qq.com/',
 *   imgUrl: 'https://res.mail.qq.com/node/ww/wwmng/style/images/index_share_logo$13c64306.png',
 *   success() {
 *     // 用户确认分享后回调
 *   },
 *   cancel() {
 *     // 用户取消分享后回调
 *   }
 * })
 * ```
 */ function onMenuShareWechat(params) {
    bindShare("menu:share:wechat", params, function() {
        return invoke("shareWechat", {
            title: params.title || getTitle(),
            desc: params.desc || "",
            link: params.link || getHref(),
            img_url: params.imgUrl || "",
            type: params.type || "link",
            data_url: params.dataUrl || ""
        });
    });
}
/**
 * 获取「分享到QQ」按钮点击状态并自定义分享内容。
 *
 * @note
 * 微信客户端即将废弃该接口
 *
 * @compat WeChat
 */ function onMenuShareQQ(params) {
    bindShare("menu:share:qq", params, function() {
        return invoke("shareQQ", {
            title: params.title || getTitle(),
            desc: params.desc || "",
            img_url: params.imgUrl || "",
            link: params.link || getHref()
        });
    });
}
/**
 * 获取「分享到微博」按钮点击状态并自定义分享内容。
 *
 * @compat WeChat
 */ function onMenuShareWeibo(params) {
    bindShare("menu:share:weiboApp", params, function() {
        return invoke("shareWeiboApp", {
            title: params.title || getTitle(),
            desc: params.desc || "",
            img_url: params.imgUrl || "",
            link: params.link || getHref()
        });
    });
}
/**
 * 获取「分享到QQ空间」按钮点击状态并自定义分享内容。
 *
 * @note
 * 微信客户端即将废弃该接口
 *
 * @compat WeChat
 */ function onMenuShareQZone(params) {
    bindShare("menu:share:QZone", params, function() {
        return invoke("shareQZone", {
            title: params.title || getTitle(),
            desc: params.desc || "",
            img_url: params.imgUrl || "",
            link: params.link || getHref()
        });
    });
}
/**
 * 自定义转发到会话。
 *
 * @compat WeCom >= 2.4.5
 *
 * @example
 * ```ts
 * ww.shareAppMessage({
 *   title: '企业微信',
 *   desc: '让每个企业都有自己的微信',
 *   link: 'https://work.weixin.qq.com/',
 *   imgUrl: 'https://res.mail.qq.com/node/ww/wwmng/style/images/index_share_logo$13c64306.png',
 * })
 * ```
 */ function shareAppMessage(params) {
    return passthrough("shareAppMessage", params);
}
/**
 * 自定义转发到微信。
 *
 * @compat WeCom >= 2.4.5
 *
 * @example
 * ```ts
 * ww.shareWechatMessage({
 *   title: '企业微信',
 *   desc: '让每个企业都有自己的微信',
 *   link: 'https://work.weixin.qq.com/',
 *   imgUrl: 'https://res.mail.qq.com/node/ww/wwmng/style/images/index_share_logo$13c64306.png',
 * })
 * ```
 */ function shareWechatMessage(params) {
    return passthrough("shareWechatMessage", params);
}
/**
 * 自定义「分享到朋友圈」及「分享到QQ空间」按钮的分享内容。
 *
 * @compat WeChat
 */ function updateTimelineShareData(params) {
    if (params === void 0) params = {};
    return passthrough("updateTimelineShareData", params);
}
/**
 * 自定义「分享给朋友」及「分享到QQ」按钮的分享内容。
 *
 * @compat WeChat
 */ function updateAppMessageShareData(params) {
    if (params === void 0) params = {};
    return passthrough("updateAppMessageShareData", params);
}

/**
 * 批量添加卡券
 *
 * @see https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html#批量添加卡券接口
 */ function addCard(params) {
    return promiseToCallback(params, /*#__PURE__*/ _async_to_generator(function() {
        var res, _iterator, _step, card;
        return __generator(this, function(_state) {
            switch(_state.label){
                case 0:
                    return [
                        4,
                        tryEnsureConfigReady()
                    ];
                case 1:
                    _state.sent();
                    return [
                        4,
                        invoke("batchAddCard", {
                            card_list: params.cardList.map(function(card) {
                                return {
                                    card_id: card.cardId,
                                    card_ext: card.cardExt
                                };
                            })
                        })
                    ];
                case 2:
                    res = _state.sent();
                    if (!res.card_list) {
                        return [
                            2,
                            res
                        ];
                    }
                    for(_iterator = _create_for_of_iterator_helper_loose(res.card_list); !(_step = _iterator()).done;){
                        card = _step.value;
                        card.cardId = card.card_id;
                        delete card.card_id;
                        card.cardExt = card.card_ext;
                        delete card.card_ext;
                        card.isSuccess = !!card.is_succ;
                        delete card.is_succ;
                    }
                    res.cardList = res.card_list;
                    delete res.card_list;
                    return [
                        2,
                        res
                    ];
            }
        });
    }));
}
/**
 * 拉取适用卡券列表并获取用户选择信息
 *
 * @see https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html#拉取适用卡券列表并获取用户选择信息
 */ function chooseCard(params) {
    return promiseToCallback(params, /*#__PURE__*/ _async_to_generator(function() {
        var res;
        return __generator(this, function(_state) {
            switch(_state.label){
                case 0:
                    return [
                        4,
                        tryEnsureConfigReady()
                    ];
                case 1:
                    _state.sent();
                    return [
                        4,
                        invoke("chooseCard", {
                            app_id: getConfigCorpId(),
                            location_id: params.shopId || "",
                            sign_type: params.signType || "SHA1",
                            card_id: params.cardId || "",
                            card_type: params.cardType || "",
                            card_sign: params.cardSign,
                            time_stamp: "" + params.timestamp,
                            nonce_str: params.nonceStr
                        })
                    ];
                case 2:
                    res = _state.sent();
                    res.cardList = res.choose_card_info;
                    delete res.choose_card_info;
                    return [
                        2,
                        res
                    ];
            }
        });
    }));
}
/**
 * 查看微信卡包中的卡券
 *
 * @see https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html#查看微信卡包中的卡券接口
 */ function openCard(params) {
    return passthrough("batchViewCard", params, {
        card_list: params.cardList.map(function(card) {
            return {
                card_id: card.cardId,
                code: card.code
            };
        })
    });
}
/**
 * 核销并分享卡券
 *
 * @deprecated
 */ function consumeAndShareCard(params) {
    return passthrough("consumedShareCard", params, {
        consumedCardId: params.cardId,
        consumedCode: params.code
    });
}

var ProductViewType;
(function(ProductViewType) {
    ProductViewType[ProductViewType["normal"] = 0] = "normal";
    ProductViewType[ProductViewType["scan"] = 1] = "scan";
})(ProductViewType || (ProductViewType = {}));
/**
 * 跳转微信商品页
 *
 * @see https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html#跳转微信商品页接口
 */ function openProductSpecificView(params) {
    return passthrough("openProductViewWithPid", params, {
        pid: params.productId,
        view_type: params.viewType || 0,
        ext_info: params.extInfo
    });
}

/**
 * 发起一个微信支付请求
 *
 * @see https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html#发起一个微信支付请求
 */ function chooseWXPay(params) {
    return passthrough("getBrandWCPayRequest", params, normalizeParams(params));
}
/**
 * 领取企业红包
 */ function openEnterpriseRedPacket(params) {
    return passthrough("getRecevieBizHongBaoRequest", params, normalizeParams(params));
}
function normalizeParams(params) {
    return {
        timeStamp: "" + params.timestamp,
        nonceStr: params.nonceStr,
        package: params.package,
        paySign: params.paySign,
        signType: params.signType || "SHA1"
    };
}

/**
 * 添加设备。
 *
 * @limit
 * - 发起用户需要有设备添加权限（超级管理员/设备管理员）
 *
 * @compat WeCom iOS, Android >= 4.0.18
 *
 * @example
 * ```ts
 * ww.addDevice({
 *   type: 'qrcode',
 *   qrcode_url: 'https://open.work.weixin.qq.com/connect?xxx'
 * })
 * ```
 */ function addDevice(params) {
    return passthrough("addDevice", params);
}

/**
 * 判断当前客户端版本是否支持指定 JS 接口。
 *
 * @example
 * ```ts
 * ww.checkJsApi({
 *   jsApiList: ['chooseImage']
 * })
 * ```
 */ function checkJsApi(params) {
    return _checkJsApi.apply(this, arguments);
}
function _checkJsApi() {
    _checkJsApi = _async_to_generator(function(params) {
        return __generator(this, function(_state) {
            return [
                2,
                promiseToCallback(params, /*#__PURE__*/ _async_to_generator(function() {
                    var res;
                    return __generator(this, function(_state) {
                        switch(_state.label){
                            case 0:
                                return [
                                    4,
                                    tryEnsureConfigReady()
                                ];
                            case 1:
                                _state.sent();
                                return [
                                    4,
                                    invoke("checkJsApi", {
                                        jsApiList: mapJsApiListToClient(params.jsApiList)
                                    })
                                ];
                            case 2:
                                res = _state.sent();
                                res.checkResult = normalize(flatten(res.checkResult));
                                return [
                                    2,
                                    res
                                ];
                        }
                    });
                }))
            ];
        });
    });
    return _checkJsApi.apply(this, arguments);
}
function normalize(data) {
    var result = _extends({}, data);
    for(var _iterator = _create_for_of_iterator_helper_loose(Object.keys(data)), _step; !(_step = _iterator()).done;){
        var key = _step.value;
        result[mapJsApiNameToWeb(key)] = data[key];
    }
    return result;
}
function flatten(data) {
    var result = {};
    for(var _iterator = _create_for_of_iterator_helper_loose(Object.keys(data)), _step; !(_step = _iterator()).done;){
        var key = _step.value;
        if (!isObject$1(data[key])) {
            result[key] = data[key];
            continue;
        }
        var child = flatten(data[key]);
        for(var _iterator1 = _create_for_of_iterator_helper_loose(Object.keys(child)), _step1; !(_step1 = _iterator1()).done;){
            var childKey = _step1.value;
            result[key + "." + childKey] = child[childKey];
        }
    }
    return result;
}

/**
 * 查看日程闲忙状态。
 *
 * 查看其他成员某段时间内日程中的闲忙状态。
 *
 * @limit
 * - 当前成员必须在应用可见范围内
 * - 应用需具有日程使用权限
 *
 * @compat WeCom >= 4.0.20
 *
 * @example
 * ```ts
 * ww.checkSchedule({
 *   start_time: 1667232000,
 *   end_time: 1667318400,
 *   users: ['jack', 'jason']
 * })
 * ```
 */ function checkSchedule(params) {
    return passthrough("checkSchedule", params);
}

/**
 * 拉起电子发票列表。
 *
 * @compat WeCom iOS, Android >= 2.1.0
 *
 * @example
 * ```ts
 * ww.chooseInvoice({
 *   timestamp: timestamp,
 *   nonceStr: nonceStr,
 *   signType: signType,
 *   cardSign: cardSign
 * })
 * ```
 */ function chooseInvoice(params) {
    return passthrough("chooseInvoice", params);
}

/**
 * 认领老师班级
 *
 * 跳转到认领班级的界面
 *
 * @compat WeCom >= 3.1.8
 */ function claimClassAdmin(params) {
    if (params === void 0) params = {};
    return passthrough("claimClassAdmin", params);
}

/**
 * 创建群聊并发送消息
 *
 * 向用户申请给指定范围发送消息。调用接口后，用户可在选人界面对群聊范围进行修改，当创建群聊成功时会返回新建的群聊 ID
 *
 * @limit
 * - 仅第三方应用（非通讯录应用）可调用
 *
 * @compat WeCom >= 3.1.8
 *
 * @example
 * ```ts
 * ww.createChatWithMsg({
 *   selectedOpenUserIds: ['zhangsan','lisi'],
 *   selectedTickets: ['tick1','token2'],
 *   chatName: 'discussName',
 *   msg: {
 *     msgtype: 'link',
 *     link: {
 *       title: 'title1',
 *       desc: 'desc1',
 *       url: 'link1',
 *       imgUrl: 'imgurl1'
 *     }
 *   }
 * })
 * ```
 */ function createChatWithMsg(params) {
    return passthrough("createChatWithMsg", params);
}

/**
 * 创建企业互联/上下游会话
 *
 * @limit
 * - 企业必须开启互联群功能
 * - 仅局校互联和上下游企业可调用
 * - 当前成员必须在应用的可见范围
 * - 群成员人数不能超过 2000 人
 * - 如果创建的会话有外部联系人，群成员人数不能超过 40 人
 *
 * @compat WeCom >= 3.1.8
 *
 * @example
 * ```ts
 * ww.createCorpGroupChat({
 *   groupName: '讨论组',
 *   userIds: ['lisi', 'lisi2'],
 *   openUserIds: ['wabc3', 'wbcde'],
 *   externalUserIds: ['exid1', 'exid2'],
 *   corpGroupUserIds: [
 *     {
 *       corpId: 'ww3333',
 *       userId: 'userid123',
 *       openUserId: 'wx1111'
 *     },
 *     {
 *       corpId: 'ww4444',
 *       userId: 'userid123',
 *       openUserId: 'wx1111'
 *     }
 *   ]
 * })
 * ```
 */ function createCorpGroupChat(params) {
    return passthrough("createCorpGroupChat", params, {
        groupName: params.groupName || "",
        userIds: params.userIds,
        openUserIds: params.openUserIds,
        corpGroupUserIds: params.corpGroupUserIds
    });
}

var CreateExternalPaymentType;
(function(CreateExternalPaymentType) {
    /**
   * 在聊天中收款
   */ CreateExternalPaymentType[CreateExternalPaymentType["chat"] = 0] = "chat";
    /**
   * 收款码收款
   */ CreateExternalPaymentType[CreateExternalPaymentType["qrcode"] = 1] = "qrcode";
})(CreateExternalPaymentType || (CreateExternalPaymentType = {}));
/**
 * 发起对外收款
 *
 * @limit
 * - 应用需要具有对外收款权限
 * - 发起的用户必须在应用可见范围并实名
 * - 允许第三方应用、代开发应用和自建应用调用
 *
 * @compat WeCom >= 4.0.12
 *
 * @example
 * ```ts
 * ww.createExternalPayment({
 *   paymentType: 0,
 *   totalFee: 300,
 *   description: '可乐一罐'
 * })
 * ```
 */ function createExternalPayment(params) {
    if (params === void 0) params = {};
    return passthrough("createExternalPayment", params);
}

/**
 * 发起班级收款
 *
 * 用于老师对学生家长发起付款请求，接口调用成功后会通过家校通知发送付款小程序给家长
 *
 * @limit
 * - 仅支持配置在家长可使用范围内的应用
 * - 企业必须已验证或者已认证
 * - 发起的用户必须在应用可见范围并实名
 * - 发起的用户需在个人微信零钱账户的可用范围内
 *
 * @compat WeCom iOS, Android, PC >= 3.1.10
 *
 * @example
 * ```ts
 * ww.createSchoolPayment({
 *   projectName: '1班班费',
 *   amount: 100,
 *   payers: {
 *     students: ['zhagnshan', 'lisi'],
 *     departments: [1, 2]
 *   }
 * })
 * ```
 */ function createSchoolPayment(params) {
    return passthrough("createSchoolPayment", params);
}

/**
 * 添加设备。
 *
 * @deprecated 请使用 addDevice 接口
 *
 * @limit
 * - 调用者必须为企业超级管理员
 *
 * @compat WeCom iOS, Android >= 2.5.8
 *
 * @example
 * ```ts
 * ww.discoverDevice({
 *   type: 'qrcode',
 *   qrcode_url: 'https://open.work.weixin.qq.com/connect?xxx'
 * })
 * ```
 */ function discoverDevice(params) {
    return passthrough("discoverDevice", params);
}

/**
 * 加入视频会议。
 *
 * @limit
 * - 只能加入同企业硬件创建的视频会议
 *
 * @compat WeCom >= 2.5.0
 *
 * @example
 * ```ts
 * ww.enterHWOpenTalk({
 *   code: code,
 *   ticket: ticket
 * })
 * ```
 */ function enterHWOpenTalk(params) {
    return passthrough("enterHWOpenTalk", params);
}

/**
 * 跳转认证界面。
 *
 * @compat WeCom iOS, Android >= 2.8.7
 *
 * @example
 * ```ts
 * ww.enterpriseVerify()
 * ```
 */ function enterpriseVerify(params) {
    if (params === void 0) params = {};
    return passthrough("enterpriseVerify", params);
}

/**
 * 获取已选择的选项。
 *
 * 当用户打开网页后，应该先调用一次该接口获取用户已经选择的数据作为初始数据。获取到初始数据后，应该恢复已经选择的选项。
 *
 * @limit
 * - 网页应该做好深色模式适配
 * - 接口仅用于审批设置外部选项场景，请勿用作其他场景
 *
 * @compat WeCom >= 4.0.18
 *
 * @example
 * ```ts
 * ww.getApprovalSelectedItems({
 *   key: 'key'
 * })
 * ```
 */ function getApprovalSelectedItems(params) {
    return promiseToCallback(params, /*#__PURE__*/ _async_to_generator(function() {
        var res;
        return __generator(this, function(_state) {
            switch(_state.label){
                case 0:
                    return [
                        4,
                        tryEnsureConfigReady()
                    ];
                case 1:
                    _state.sent();
                    return [
                        4,
                        invoke("getApprovalSelectedItems", {
                            key: params.key
                        })
                    ];
                case 2:
                    res = _state.sent();
                    if (!res.selectedData) {
                        return [
                            2,
                            res
                        ];
                    }
                    try {
                        res.selectedData = JSON.parse(res.selectedData);
                    } catch (error) {
                        res.selectedData = [];
                    }
                    return [
                        2,
                        res
                    ];
            }
        });
    }));
}

var EntryType;
(function(EntryType) {
    /**
   * 从联系人详情进入
   */ EntryType["contact_profile"] = "contact_profile";
    /**
   * 从单聊会话的工具栏进入
   */ EntryType["single_chat_tools"] = "single_chat_tools";
    /**
   * 从群聊会话的工具栏进入
   */ EntryType["group_chat_tools"] = "group_chat_tools";
    /**
   * 从会话的聊天附件栏进入
   *
   * @compat WeCom >= 3.1.6
   */ EntryType["chat_attachment"] = "chat_attachment";
    /**
   * 从微信客服的工具栏进入
   *
   * @compat WeCom >= 3.1.10
   */ EntryType["single_kf_tools"] = "single_kf_tools";
    /**
   * 上下游单聊会话的工具栏
   *
   * @compat WeCom >= 4.0.8
   */ EntryType["chain_single_chat_tools"] = "chain_single_chat_tools";
    /**
   * 上下游群聊会话的工具栏
   *
   * @compat WeCom >= 4.0.8
   */ EntryType["chain_group_chat_tools"] = "chain_group_chat_tools";
    /**
   * 除以上场景之外进入，例如工作台，聊天会话等
   */ EntryType["normal"] = "normal";
})(EntryType || (EntryType = {}));
/**
 * 调用该接口可以判断用户是从哪个入口打开页面，从而决定是否可以调用客户联系相关的接口。
 *
 * @compat WeCom >= 3.0.24
 *
 * @example
 * ```ts
 * ww.getContext()
 * ```
 */ function getContext(params) {
    if (params === void 0) params = {};
    return passthrough("getContext", params);
}

/**
 * 获取当前上下游互联群的群 ID
 *
 * @compat WeCom >= 4.0.12
 */ function getCurCorpGroupChat(params) {
    if (params === void 0) params = {};
    return passthrough("getCurCorpGroupChat", params);
}

/**
 * 获取当前上下游联系人用户 ID
 *
 * @compat WeCom >= 4.0.8
 */ function getCurCorpGroupContact(params) {
    return passthrough("getCurCorpGroupContact", params);
}

/**
 * 获取当前客户群的群 ID。
 *
 * @limit
 * - 不同的入口对应用及用户有相应的限制
 *   | getContext 接口返回的 entry 值 | 自建应用 | 第三方应用 | 用户 | 支持的最低版本 |
 *   | --- | --- | --- | --- | --- |
 *   | group_chat_tools | 需有客户联系功能权限 | 需有“企业客户权限->客户基础信息”权限 | 配置了客户联系功能 | 2.8.17 |
 *   | group_chat_tools | 所有 | 需有「家校沟通」使用权限 | 所有 | 3.0.36 |
 *   | group_chat_tools | 所有 | 需有「家校沟通」使用权限 | 所有 | 4.0.8 |
 *
 * @compat WeCom >= 2.8.17
 *
 * @example
 * ```ts
 * ww.getCurExternalChat()
 * ```
 */ function getCurExternalChat(params) {
    if (params === void 0) params = {};
    return passthrough("getCurExternalChat", params);
}

/**
 * 获取当前外部联系人 userId。
 *
 * @limit
 * - 不同的入口对应用及用户有相应的限制
 *   | getContext 接口返回的 entry 值 | 自建应用 | 第三方应用 | 用户 | 支持的最低版本 |
 *   | --- | --- | --- | --- | --- |
 *   | contact_profile | 需有客户联系功能权限 | 需有“企业客户权限->客户基础信息”权限 | 配置了客户联系功能 | 2.5.8 |
 *   | single_chat_tools | 需有客户联系功能权限 | 需有“企业客户权限->客户基础信息”权限 | 配置了客户联系功能 | 2.8.10 |
 *
 * @compat WeCom >= 2.5.8
 *
 * @example
 * ```ts
 * ww.getCurExternalContact()
 * ```
 */ function getCurExternalContact(params) {
    if (params === void 0) params = {};
    return passthrough("getCurExternalContact", params);
}

/**
 * 获取私密消息信息
 *
 * @compat WeCom >= 3.1.8
 *
 * @example
 * ```ts
 * ww.getShareInfo({
 *   shareTicket: 'xxx'
 * })
 * ```
 */ function getShareInfo(params) {
    return passthrough("getShareInfo", params);
}

/**
 * 隐藏聊天附件栏的发送按钮
 *
 * 隐藏从聊天附件栏打开页面时固定显示的发送按钮，开发者可以通过[分享消息到当前会话](#sendChatMessage)接口灵活适配对页面或页面中具体内容的转发
 *
 * @limit
 * - 需要从附件栏进入才允许调用
 *
 * @compat WeCom >= 3.1.6
 */ function hideChatAttachmentMenu(params) {
    return passthrough("hideChatAttachmentMenu", params);
}

/**
 * 跳转到小程序。
 *
 * @note
 * - 打开小程序时如果需要关闭页面，需同步调用 closeWindow，不推荐用 setTimeout 延迟关闭
 *
 * @limit
 * - 跳转的小程序必须属于页面所属的企业
 * - 跳转的小程序必须已关联到工作台
 *
 * @compat WeCom >= 3.0.36
 *
 * @example
 * ```ts
 * ww.launchMiniprogram({
 *   appid: 'wx062f7a5507909000',
 *   path: 'pages/home/<USER>'
 * })
 * ```
 */ function launchMiniprogram(params) {
    return passthrough("launchMiniprogram", params, {
        appid: params.appid,
        path: addHTMLToPath(params.path),
        envVersion: params.envVersion
    });
}
function addHTMLToPath(url) {
    if (!url || !isString(url)) {
        return;
    }
    var _url_split = url.split("?"), path = _url_split[0], query = _url_split.slice(1);
    if (!query.length) {
        return "" + path + ".html";
    }
    return path + ".html?" + query.join("?");
}

/**
 * 在企业微信内快速跳转到添加客户的界面。
 *
 * @limit
 * - 当前成员必须配置了客户联系功能
 * - 第三方应用须配置客户联系功能权限
 *
 * @compat WeCom iOS, Android >= 3.0.36
 *
 * @example
 * ```ts
 * ww.navigateToAddCustomer()
 * ```
 */ function navigateToAddCustomer(params) {
    if (params === void 0) params = {};
    return passthrough("navigateToAddCustomer", params);
}

/**
 * 进入微信客服消息界面
 *
 * @limit
 * - 当前企业须已开启「微信客服」应用
 * - 当前成员须是指定客服账号的坐席
 *
 * @compat WeCom iOS, Android, PC >= 3.1.12
 *
 * @example
 * ```ts
 * ww.navigateToKfChat({
 *   openKfId: 'wkAJ2GCAAAZSfhHCt7IFSvLKtMPxyAAA',
 *   externalUserId: 'wmAJ2GCAAAZSfhHCt7IFSvLKtMPxyBBB'
 * })
 * ```
 */ function navigateToKfChat(params) {
    return passthrough("navigateToKfChat", params);
}

/**
 * 共享收货地址
 *
 * @see https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html#共享收货地址接口
 */ function openAddress(params) {
    if (params === void 0) params = {};
    return promiseToCallback(params, /*#__PURE__*/ _async_to_generator(function() {
        var res;
        return __generator(this, function(_state) {
            switch(_state.label){
                case 0:
                    return [
                        4,
                        tryEnsureConfigReady()
                    ];
                case 1:
                    _state.sent();
                    return [
                        4,
                        invoke("editAddress")
                    ];
                case 2:
                    res = _state.sent();
                    res.postalCode = res.addressPostalCode;
                    delete res.addressPostalCode;
                    res.provinceName = res.proviceFirstStageName;
                    delete res.proviceFirstStageName;
                    res.cityName = res.addressCitySecondStageName;
                    delete res.addressCitySecondStageName;
                    res.countryName = res.addressCountiesThirdStageName;
                    delete res.addressCountiesThirdStageName;
                    res.detailInfo = res.addressDetailInfo;
                    delete res.addressDetailInfo;
                    return [
                        2,
                        res
                    ];
            }
        });
    }));
}

/**
 * 打开应用评价页面
 *
 * 第三方应用可以使用该接口提供按钮，让用户快速打开应用评价页面。
 *
 * @compat WeCom iOS, Android, PC >= 4.0.2
 *
 * @limit
 * - 仅第三方应用可调用
 * - 当前用户可以进行应用评价
 * - 对成员授权的应用，当前用户需要在应用可见范围内
 * - 对管理员授权的应用，当前用户需要在应用可见范围内或具有应用管理权限
 */ function openAppComment(params) {
    if (params === void 0) params = {};
    return passthrough("openAppComment", params);
}

/**
 * 获取设备数据授权。
 *
 * 唤起设备选择列表，企业管理员选择设备后，应用可以通过云端接口获取到设备上报的数据。
 *
 * @limit
 * - 应用必须勾选了设备信息接口权限
 * - 仅第三方应用使用
 *
 * @compat WeCom >= 4.0.12
 *
 * @example
 * ```ts
 * ww.openAppDeviceDataAuth()
 * ```
 */ function openAppDeviceDataAuth(params) {
    if (params === void 0) params = {};
    return passthrough("openAppDeviceDataAuth", params);
}

/**
 * 打开应用管理页面
 *
 * 应用可以使用该接口提供按钮，让企业管理员快速打开应用的管理页面。
 *
 * @limit
 * - 当前用户必须具有应用管理权限
 *
 * @compat WeCom >= 4.0.2
 */ function openAppManage(params) {
    if (params === void 0) params = {};
    return passthrough("openAppManage", params);
}

var EnvVersion;
(function(EnvVersion) {
    EnvVersion["release"] = "release";
    EnvVersion["trial"] = "trial";
    EnvVersion["develop"] = "develop";
})(EnvVersion || (EnvVersion = {}));
/**
 * 商户小程序跳转微信支付分小程序
 *
 * @see https://pay.weixin.qq.com/wiki/doc/apiv3/payscore.php?chapter=29_3&index=3
 */ function openBusinessView(params) {
    return promiseToCallback(params, /*#__PURE__*/ _async_to_generator(function() {
        var res;
        return __generator(this, function(_state) {
            switch(_state.label){
                case 0:
                    return [
                        4,
                        tryEnsureConfigReady()
                    ];
                case 1:
                    _state.sent();
                    return [
                        4,
                        invoke("openBusinessView", {
                            businessType: params.businessType,
                            queryString: params.queryString || "",
                            envVersion: params.envVersion
                        })
                    ];
                case 2:
                    res = _state.sent();
                    if (!isAndroid || !res.extraData) {
                        return [
                            2,
                            res
                        ];
                    }
                    try {
                        res.extraData = JSON.parse(res.extraData);
                    } catch (error) {
                        res.extraData = {};
                    }
                    return [
                        2,
                        res
                    ];
            }
        });
    }));
}

/**
 * 查看设备。
 *
 * @limit
 * - 调用者必须拥有指定 deviceSn 的管理权限
 *
 * @note
 * - 若开发者需要在 web 端引导跳转设备管理，可以构造链接跳转：
 *   `https://work.weixin.qq.com/wework_admin/frame#hardware/device?sn=DEVICESN`
 *
 * @compat WeCom iOS, Android >= 2.8.2
 *
 * @example
 * ```ts
 * ww.openDeviceProfile({
 *   deviceSn: 'QYWX001'
 * })
 * ```
 */ function openDeviceProfile(params) {
    return passthrough("openDeviceProfile", params);
}

/**
 * 打开会话。
 *
 * @limit
 * - 内部群最多 2000 人，外部群最多 500 人
 * - 若创建的会话包含微信联系人，群成员人数不能超过 40 人
 *
 * @compat WeCom >= 2.0.0
 *
 * @example
 * ```ts
 * ww.openEnterpriseChat({
 *   groupName: '讨论组',
 *   userIds: [
 *     'zhangsan',
 *     'lisi'
 *   ],
 *   externalUserIds: [
 *     'wmEAlECwAAHrbWYDOK5u3Bf13xlYDAAA',
 *     'wmEAlECwAAHibWYDOK5u3Af13xlYDAAA'
 *   ]
 * })
 * ```
 */ function openEnterpriseChat(params) {
    if (params === void 0) params = {};
    return passthrough("openEnterpriseChat", params, {
        chatId: params.chatId || "",
        chatname: params.groupName || "",
        groupName: params.groupName || "",
        useridlist: joinList(params.userIds),
        userIds: joinList(params.userIds),
        openIds: joinList(params.openIds),
        externalUserIds: joinList(params.externalUserIds)
    });
}

/**
 * 打开已有群聊并发送消息
 *
 * 打开一个已有的群聊并可选发送一条链接消息。支持打开企业内部群、外部群、互联群
 *
 * @compat WeCom >= 3.1.8
 *
 * @example
 * ```ts
 * ww.openExistedChatWithMsg({
 *   chatId: 'chatId123',
 *   msg: {
 *     msgtype: 'link',
 *     link: {
 *       title: 'title1',
 *       desc: 'desc1',
 *       url: 'link1',
 *       imgUrl: 'imgurl1'
 *     }
 *   }
 * })
 * ```
 */ function openExistedChatWithMsg(params) {
    return passthrough("openExistedChatWithMsg", params);
}

/**
 * 进入应用客服会话
 *
 * 第三方应用可以使用该接口提供按钮，让用户快速打开应用客服的会话。
 *
 * @compat WeCom iOS, Android >= 3.1.18
 *
 * @limit
 * - 仅第三方应用可调用
 * - 第三方应用需要提前配置客服
 * - 当前用户需要有添加外部联系人权限
 */ function openThirdAppServiceChat(params) {
    if (params === void 0) params = {};
    return passthrough("openThirdAppServiceChat", params);
}

var OpenUserProfileType;
(function(OpenUserProfileType) {
    /**
   * 企业成员
   */ OpenUserProfileType[OpenUserProfileType["internal"] = 1] = "internal";
    /**
   * 外部联系人
   */ OpenUserProfileType[OpenUserProfileType["external"] = 2] = "external";
})(OpenUserProfileType || (OpenUserProfileType = {}));
/**
 * 唤起成员或外部联系人的个人信息页面。
 *
 * @compat WeCom >= 2.4.20
 *
 * @example
 * ```ts
 * ww.openUserProfile({
 *   type: 1,
 *   userid: 'zhangsan'
 * })
 * ```
 */ function openUserProfile(params) {
    return passthrough("openUserProfile", params);
}

var PrintFileIdType;
(function(PrintFileIdType) {
    /**
   * mediaid
   */ PrintFileIdType[PrintFileIdType["mediaid"] = 1] = "mediaid";
    /**
   * url
   */ PrintFileIdType[PrintFileIdType["url"] = 2] = "url";
    /**
   * localId
   */ PrintFileIdType[PrintFileIdType["localId"] = 4] = "localId";
})(PrintFileIdType || (PrintFileIdType = {}));
/**
 * 发起文件打印。
 *
 * @limit
 * - 应用需要已获取设备信息 - 打印扫描设备 - 发起文件打印权限授权
 * - 当前触发调用人员身份需要在应用的可见范围内
 * - 当前企业有安装企业微信打印设备
 * - 仅第三方应用使用
 *
 * @compat WeCom >= 4.0.12
 *
 * @example
 * ```ts
 * ww.printFile({
 *   fileId: 'fileId',
 *   fileIdType: 1,
 *   fileName: 'fileName.jpg'
 * })
 * ```
 */ function printFile(params) {
    return passthrough("printFile", params);
}

var InTalkType;
(function(InTalkType) {
    /**
   * 当前不在任何通话中
   */ InTalkType["None"] = "None";
    /**
   * 视频会议中
   */ InTalkType["HWOpenTalk"] = "HWOpenTalk";
    /**
   * voip通话中
   */ InTalkType["VoIP"] = "VoIP";
    /**
   * 系统通话中
   */ InTalkType["SystemCall"] = "SystemCall";
})(InTalkType || (InTalkType = {}));
/**
 * 查询当前是否在视频会议。
 *
 * @compat WeCom >= 2.5.0
 *
 * @example
 * ```ts
 * ww.queryCurrHWOpenTalk()
 * ```
 */ function queryCurrHWOpenTalk(params) {
    if (params === void 0) params = {};
    return passthrough("queryCurrHWOpenTalk", params);
}

/**
 * 发起退款
 *
 * @limit
 * - 应用需要具有对外收款权限
 * - 发起的用户必须在应用可见范围并实名
 * - 只允许退款由应用本身发起的收款
 * - 允许第三方应用、代开发应用和自建应用调用
 *
 * @compat WeCom >= 4.0.12
 *
 * @example
 * ```ts
 * ww.refundExternalPayment({
 *   paymentId: 'xxxx',
 *   outTradeNo: 'yyyy',
 *   refundFee: 100,
 *   refundComment: '7天无理由退货'
 * })
 * ```
 */ function refundExternalPayment(params) {
    return passthrough("refundExternalPayment", params);
}

/**
 * 保存选择的选项。
 *
 * 用户在网页中进行选择时，调用该接口保存用户的选择。
 *
 * @note
 * - 接口仅用于审批设置外部选项场景，请勿用作其他场景
 * - 网页应该做好深色模式适配
 *
 * @compat WeCom >= 4.0.18
 *
 * @example
 * ```ts
 * ww.saveApprovalSelectedItems({
 *   key: 'key',
 *   selectedData: [
 *     {
 *       key: 'item-1',
 *       value: '选项1'
 *     },
 *     {
 *       key: 'item-2',
 *       value: '选项2'
 *     }
 *   ]
 * })
 * ```
 */ function saveApprovalSelectedItems(params) {
    return passthrough("saveApprovalSelectedItems", params, {
        key: params.key,
        selectedData: typeof params.selectedData === "string" ? params.selectedData : JSON.stringify(params.selectedData)
    });
}

var ScanQRCodeType;
(function(ScanQRCodeType) {
    /**
   * 扫描二维码
   */ ScanQRCodeType["qrCode"] = "qrCode";
    /**
   * 扫描条形码
   */ ScanQRCodeType["barCode"] = "barCode";
})(ScanQRCodeType || (ScanQRCodeType = {}));
/**
 * 调起企业微信扫一扫。
 *
 * @example
 * ```ts
 * ww.scanQRCode({
 *   needResult: true,
 *   scanType: ['qrCode']
 * })
 * ```
 */ function scanQRCode(params) {
    if (params === void 0) params = {};
    return promiseToCallback(params, /*#__PURE__*/ _async_to_generator(function() {
        var _data_scan_code, res, resultStr, data;
        return __generator(this, function(_state) {
            switch(_state.label){
                case 0:
                    return [
                        4,
                        tryEnsureConfigReady()
                    ];
                case 1:
                    _state.sent();
                    return [
                        4,
                        invoke("scanQRCode", {
                            needResult: params.needResult ? 1 : 0,
                            scanType: params.scanType || [
                                "qrCode",
                                "barCode"
                            ]
                        })
                    ];
                case 2:
                    res = _state.sent();
                    if (!isIOS) {
                        return [
                            2,
                            res
                        ];
                    }
                    resultStr = res.resultStr;
                    if (!resultStr) {
                        return [
                            2,
                            res
                        ];
                    }
                    try {
                        data = JSON.parse(resultStr);
                    } catch (error) {
                    // noop
                    }
                    res.resultStr = data == null ? void 0 : (_data_scan_code = data.scan_code) == null ? void 0 : _data_scan_code.scan_result;
                    return [
                        2,
                        res
                    ];
            }
        });
    }));
}

var InputCorpGroupContactMode;
(function(InputCorpGroupContactMode) {
    /**
   * 单选
   */ InputCorpGroupContactMode["single"] = "single";
    /**
   * 多选
   */ InputCorpGroupContactMode["multi"] = "multi";
})(InputCorpGroupContactMode || (InputCorpGroupContactMode = {}));
var InputCorpGroupContactType;
(function(InputCorpGroupContactType) {
    InputCorpGroupContactType["department"] = "department";
    InputCorpGroupContactType["user"] = "user";
})(InputCorpGroupContactType || (InputCorpGroupContactType = {}));
/**
 * 企业互联选人
 *
 * @limit
 * - 该接口仅可选择应用可见范围内的成员和部门
 *
 * @compat WeCom iOS, Android, PC >= 3.1.6
 *
 * @example
 * ```ts
 * ww.selectCorpGroupContact({
 *   fromDepartmentId: -1,
 *   mode: 'single',
 *   type: ['department', 'user'],
 *   selectedDepartmentIds: ['2','3'],
 *   selectedUserIds: ['lisi','lisi2'],
 *   selectedOpenUserIds: ['wabc3','wbcde'],
 *   selectedChainDepartmentIds: [
 *     {
 *       corpId: 'ww3333',
 *       departmentId: '2'
 *     },
 *     {
 *       corpId: 'ww4444',
 *       departmentId: '3'
 *     }
 *   ],
 *   selectedChainUserIds: [
 *     {
 *       corpId: 'ww3333',
 *       userId: 'userid123',
 *       openUserId: 'wx1111'
 *     },
 *     {
 *       corpId: 'ww4444',
 *       userId: 'userid123',
 *       openUserId: 'wx1111'
 *     }
 *   ],
 *   selectedCorpGroupDepartmentIds: [
 *     {
 *       corpId: 'ww3333',
 *       departmentId: '2'
 *     },
 *     {
 *       corpId: 'ww4444',
 *       departmentId: '3'
 *     }
 *   ],
 *   selectedCorpGroupUserIds: [
 *     {
 *       corpId: 'ww3333',
 *       userId: 'userid123',
 *       openUserId: 'wx1111'
 *     },
 *     {
 *       corpId: 'ww4444',
 *       userId: 'userid123',
 *       openUserId: 'wx1111'
 *     }
 *   ]
 * })
 * ```
 */ function selectCorpGroupContact(params) {
    return promiseToCallback(params, /*#__PURE__*/ _async_to_generator(function() {
        var res;
        return __generator(this, function(_state) {
            switch(_state.label){
                case 0:
                    return [
                        4,
                        tryEnsureConfigReady()
                    ];
                case 1:
                    _state.sent();
                    return [
                        4,
                        invoke("selectCorpGroupContact", params)
                    ];
                case 2:
                    res = _state.sent();
                    if (!isString(res.result)) {
                        return [
                            2,
                            res
                        ];
                    }
                    try {
                        res.result = JSON.parse(res.result);
                    } catch (error) {
                    // noop
                    }
                    return [
                        2,
                        res
                    ];
            }
        });
    }));
}

var SelectEnterpriseContactMode;
(function(SelectEnterpriseContactMode) {
    /**
   * 单选
   */ SelectEnterpriseContactMode["single"] = "single";
    /**
   * 多选
   */ SelectEnterpriseContactMode["multi"] = "multi";
})(SelectEnterpriseContactMode || (SelectEnterpriseContactMode = {}));
var SelectEnterpriseContactType;
(function(SelectEnterpriseContactType) {
    SelectEnterpriseContactType["department"] = "department";
    SelectEnterpriseContactType["user"] = "user";
})(SelectEnterpriseContactType || (SelectEnterpriseContactType = {}));
/**
 * 选择通讯录成员。
 *
 * @compat WeCom >= 1.3.11; WeChat iOS, Android >= 6.5.10
 *
 * @example
 * ```ts
 * ww.selectEnterpriseContact({
 *   fromDepartmentId: -1,
 *   mode: 'multi',
 *   type: ['department', 'user'],
 *   selectedDepartmentIds: ['2', '3'],
 *   selectedUserIds: ['lisi', 'lisi2']
 * })
 * ```
 */ function selectEnterpriseContact(params) {
    return promiseToCallback(params, /*#__PURE__*/ _async_to_generator(function() {
        var res;
        return __generator(this, function(_state) {
            switch(_state.label){
                case 0:
                    return [
                        4,
                        tryEnsureConfigReady()
                    ];
                case 1:
                    _state.sent();
                    return [
                        4,
                        invoke("selectEnterpriseContact", params)
                    ];
                case 2:
                    res = _state.sent();
                    if (!isString(res.result)) {
                        return [
                            2,
                            res
                        ];
                    }
                    try {
                        res.result = JSON.parse(res.result);
                    } catch (error) {
                    // noop
                    }
                    return [
                        2,
                        res
                    ];
            }
        });
    }));
}

var SelectExternalContactType;
(function(SelectExternalContactType) {
    /**
   * 展示全部外部联系人列表
   */ SelectExternalContactType[SelectExternalContactType["all"] = 0] = "all";
    /**
   * 仅展示未曾选择过的外部联系人
   */ SelectExternalContactType[SelectExternalContactType["unselected"] = 1] = "unselected";
})(SelectExternalContactType || (SelectExternalContactType = {}));
/**
 * 唤起该成员的外部联系人列表，并返回员工选择的外部联系人的 userId。
 *
 * @limit
 * - 当前成员必须配置了客户联系功能
 * - 第三方应用须配置客户联系功能权限
 *
 * @compat WeCom >= 2.4.20
 *
 * @example
 * ```ts
 * ww.selectExternalContact({
 *   filterType: 0
 * })
 * ```
 */ function selectExternalContact(params) {
    if (params === void 0) params = {};
    return passthrough("selectExternalContact", params);
}

var SelectPrivilegedContactMode;
(function(SelectPrivilegedContactMode) {
    /**
   * 单选
   */ SelectPrivilegedContactMode["single"] = "single";
    /**
   * 多选
   */ SelectPrivilegedContactMode["multi"] = "multi";
})(SelectPrivilegedContactMode || (SelectPrivilegedContactMode = {}));
/**
 * 返回 ticket 的选人接口
 *
 * 用于第三方应用唤起选择企业通讯录成员，用户选择的范围区分成两部分回传给第三方应用：
 *
 * 1. 过滤应用可见范围后的 openUserId 列表
 * 2. 用户选择完整列表的 ticket，ticket 后续可用于调用 createChatWithMsg 接口或者[发送模板消息](#94515)
 *
 * @limit
 * - 仅第三方应用（非通讯录应用）可调用
 *
 * @compat WeCom >= 3.1.8
 *
 * @example
 * ```ts
 * ww.selectPrivilegedContact({
 *   fromDepartmentId: -1,
 *   mode: 'multi',
 *   selectedContextContact: 1
 *   selectedOpenUserIds: ['xxx', 'yyy'],
 *   selectedTickets: ['ticket1', 'ticket2']
 * })
 * ```
 */ function selectPrivilegedContact(params) {
    return passthrough("selectPrivilegedContact", params);
}

/**
 * 从聊天工具栏或附件栏打开的页面中向当前会话发送消息。
 *
 * @limit
 * - 仅从特定入口进入页面才可调用
 * - 不同的入口对应用及用户有相应的限制
 *   | getContext 接口返回的 entry 值 | 自建应用 | 第三方应用 | 用户 | 支持的最低版本 |
 *   | --- | --- | --- | --- | --- |
 *   | single_chat_tools | 需有客户联系功能权限 | 需有“企业客户权限->客户基础信息”权限 | 配置了客户联系功能 | 2.8.10 |
 *   | group_chat_tools | 需有客户联系功能权限 | 需有“企业客户权限->客户基础信息”权限 | 配置了客户联系功能 | 2.8.10 |
 *   | group_chat_tools | 所有 | 需有「家校沟通」使用权限 | 所有 | 3.0.36 |
 *   | group_chat_tools | 所有 | 需有「家校沟通」使用权限 | 所有 | 4.0.8 |
 *   | chat_attachment | 所有 | 所有 | 所有 | 3.1.6（mac端暂不支持） |
 *
 * @compat WeCom >= 2.8.10
 *
 * @example
 * ```ts
 * ww.sendChatMessage({
 *   msgtype: 'text',
 *   text: {
 *     content: '你好'
 *   }
 * })
 * ```
 */ function sendChatMessage(params) {
    return passthrough("sendChatMessage", params);
}

/**
 * 设置私密消息
 *
 * @compat WeCom >= 3.1.8
 *
 * @example
 * ```ts
 * ww.setShareAttr({
 *   withShareTicket: true,
 *   state: 'STATE'
 * })
 * ```
 */ function setShareAttr(params) {
    if (params === void 0) params = {};
    return passthrough("setShareAttr", params);
}

/**
 * 具有客户联系权限的企业成员，可通过该接口将文本内容和附件传递到客户群群发、发送到客户群。
 *
 * @limit
 * - 为防止滥用，同一个成员每日向一个客户最多可群发一条消息，每次群发最多可选 200 个最近活跃的客户群
 * - 当前成员必须配置了客户联系功能
 * - 第三方应用须配置客户联系功能权限
 *
 * @compat WeCom >= 2.8.7
 *
 * @example
 * ```ts
 * ww.shareToExternalChat({
 *   text: {
 *     content: '企业微信'
 *   },
 *   attachments: [
 *     {
 *       msgtype: 'image',
 *       image: {
 *         imgUrl: 'https://res.mail.qq.com/node/ww/wwmng/style/images/index_share_logo$13c64306.png'
 *       }
 *     }
 *   ]
 * })
 * ```
 */ function shareToExternalChat(params) {
    return passthrough("shareToExternalChat", params);
}

/**
 * 具有客户联系权限的企业成员，可通过该接口将文本内容和附件传递到群发助手、发送给客户。
 *
 * @limit
 * - 为防止滥用，同一个成员每日向一个客户最多可群发一条消息，每次群发最多可选 200 个客户
 * - 当前成员必须配置了客户联系功能
 * - 第三方应用须配置客户联系功能权限
 *
 * @compat WeCom >= 2.8.7
 *
 * @example
 * ```ts
 * ww.shareToExternalContact({
 *   text: {
 *     content: '企业微信'
 *   },
 *   attachments: [
 *     {
 *       msgtype: 'image',
 *       image: {
 *         imgUrl: 'https://res.mail.qq.com/node/ww/wwmng/style/images/index_share_logo$13c64306.png'
 *       }
 *     }
 *   ]
 * })
 * ```
 */ function shareToExternalContact(params) {
    return passthrough("shareToExternalContact", params);
}

/**
 * 发表内容到客户朋友圈
 *
 * @limit
 * - 当前成员必须配置了客户联系功能
 * - 第三方应用须配置客户联系功能权限
 * - 当前成员必须在客户朋友圈使用范围
 * - 当前成员必须具备外部沟通管理成员使用权限
 *
 * @compat WeCom iOS, Android >= 3.1.12
 *
 * @example
 * ```ts
 * ww.shareToExternalMoments({
 *   text: {
 *     content: '企业微信'
 *   },
 *   attachments: [
 *     {
 *       msgtype: 'image',
 *       image: {
 *         imgUrl: 'https://res.mail.qq.com/node/ww/wwmng/style/images/index_share_logo$13c64306.png'
 *       }
 *     }
 *   ]
 * })
 * ```
 */ function shareToExternalMoments(params) {
    return passthrough("shareToExternalMoments", params);
}

/**
 * 发起无线投屏。
 *
 * @compat WeCom
 *
 * @example
 * ```ts
 * ww.startWecast()
 * ```
 */ function startWecast(params) {
    if (params === void 0) params = {};
    return passthrough("startWecast", params);
}

var OAType;
(function(OAType) {
    /**
   * 发起审批
   */ OAType["create_approval"] = "10001";
    /**
   * 查看审批详情
   */ OAType["view_approval"] = "10002";
})(OAType || (OAType = {}));
var OaExtDataType;
(function(OaExtDataType) {
    /**
   * 链接
   */ OaExtDataType["link"] = "link";
    /**
   * 文本
   */ OaExtDataType["text"] = "text";
})(OaExtDataType || (OaExtDataType = {}));
/**
 * 应用发起审批。
 *
 * 在应用页面中发起审批。之后审批流程的每次状态变化都会通知开发者，开发者可按需进行拓展开发。具体参见[审批流程引擎](#93798)。
 *
 * @limit
 * - 应用需具有审批权限
 *
 * @compat WeCom >= 2.5.0
 *
 * @example
 * ```ts
 * ww.thirdPartyOpenPage({
 *   oaType: '10001',
 *   templateId: '46af67a118a6ebf000002',
 *   thirdNo: 'thirdNo',
 *   extData: {
 *     fieldList: [
 *       {
 *         type: 'text',
 *         title: '采购类型',
 *         value: '市场活动'
 *       },
 *       {
 *         type: 'link',
 *         title: '订单链接',
 *         value: 'https://work.weixin.qq.com'
 *       }
 *     ]
 *   }
 * })
 * ```
 */ function thirdPartyOpenPage(params) {
    return passthrough("thirdPartyOpenPage", params);
}

/**
 * 变更企业互联群成员
 *
 * @limit
 * - 当前成员必须在应用的可见范围
 * - 仅支持往群里添加企业内部成员/企业互联成员
 * - 仅局校互联企业可调用
 *
 * @compat WeCom >= 3.1.8
 *
 * @example
 * ```ts
 * ww.updateCorpGroupChat({
 *   chatId: 'CHATID',
 *   userIdsToAdd: ['lisi', 'lisi2'],
 *   openUserIdsToAdd: ['wabc3', 'wbcde'],
 *   corpGroupUserIdsToAdd: [
 *     {
 *       corpId: 'ww3333',
 *       userId: 'userid123',
 *       openUserId: 'wx1111'
 *     },
 *     {
 *       corpId: 'ww4444',
 *       userId: 'userid123',
 *       openUserId: 'wx1111'
 *     }
 *   ]
 * })
 * ```
 */ function updateCorpGroupChat(params) {
    return passthrough("updateCorpGroupChat", params);
}

/**
 * 变更群成员。
 *
 * @limit
 * - 目前仅支持添加企业内成员
 * - 仅支持客户群调用
 *
 * @compat WeCom iOS, Android, PC >= 3.0.36
 *
 * @example
 * ```ts
 * ww.updateEnterpriseChat({
 *   chatId: 'CHATID',
 *   userIdsToAdd: [
 *     'zhangsan',
 *     'lisi'
 *   ]
 * })
 * ```
 */ function updateEnterpriseChat(params) {
    return passthrough("updateEnterpriseChat", params, {
        chatId: params.chatId,
        userIdsToAdd: joinList(params.userIdsToAdd)
    });
}

/**
 * 设置朋友圈封面与签名
 *
 * @compat WeCom iOS, Android >= 3.1.12
 *
 * @example
 * ```ts
 * ww.updateMomentSetting({
 *   signature: '个性签名',
 *   imgUrl: 'https://work.weixin.qq.com/'
 * })
 * ```
 */ function updateMomentsSetting(params) {
    return passthrough("updateMomentsSetting", params);
}

/**
 * **注意：页面上需要提前引入 `jwxwork-1.0.0.js`：**
 *
 * ```html
 * <script src="https://open.work.weixin.qq.com/wwopen/js/jwxwork-1.0.0.js" referrerpolicy="origin"></script>
 * ```
 *
 * 初始化[通讯录展示组件](#91958)。
 *
 * 在该接口返回成功后，可以直接调用通讯录展示组件的相关方法。
 *
 * @example
 * ```ts
 * ww.initOpenData()
 * ```
 */ function initOpenData(params) {
    if (params === void 0) params = {};
    return promiseToCallback(params, /*#__PURE__*/ _async_to_generator(function() {
        var result;
        return __generator(this, function(_state) {
            switch(_state.label){
                case 0:
                    if (!isWeCom) {
                        return [
                            2,
                            invokeOpenDataAgentConfig()
                        ];
                    }
                    return [
                        4,
                        ensureAgentConfigReady()
                    ];
                case 1:
                    result = _state.sent().result;
                    if (!isWindows && !(win == null ? void 0 : win.WeixinSandBox)) {
                        throw new SDKError("Missing WeixinSandBox");
                    }
                    if (!!(win == null ? void 0 : win.WWOpenData)) return [
                        3,
                        3
                    ];
                    return [
                        4,
                        invoke("wwapp.initWwOpenData")
                    ];
                case 2:
                    _state.sent();
                    _state.label = 3;
                case 3:
                    if (!(win == null ? void 0 : win.WWOpenData)) {
                        throw new SDKError("Init WWOpenData failed");
                    }
                    if (win.WWOpenData.initJSSDK) {
                        win.WWOpenData.initJSSDK({
                            invoke: invoke,
                            ensureAgentConfigReady: ensureAgentConfigReady
                        });
                    }
                    return [
                        2,
                        result
                    ];
            }
        });
    }));
}
function invokeOpenDataAgentConfig() {
    return _invokeOpenDataAgentConfig.apply(this, arguments);
}
function _invokeOpenDataAgentConfig() {
    _invokeOpenDataAgentConfig = _async_to_generator(function() {
        var _win_WWOpenData, params;
        return __generator(this, function(_state) {
            switch(_state.label){
                case 0:
                    if (!(win == null ? void 0 : (_win_WWOpenData = win.WWOpenData) == null ? void 0 : _win_WWOpenData.agentConfig)) {
                        throw new SDKError("Missing WWOpenData.agentConfig");
                    }
                    return [
                        4,
                        resolveAgentConfigParams(getSignURL())
                    ];
                case 1:
                    params = _state.sent();
                    return [
                        2,
                        new Promise(function(success, fail) {
                            win.WWOpenData.agentConfig(_extends({}, params, {
                                success: success,
                                fail: fail
                            }));
                        })
                    ];
            }
        });
    });
    return _invokeOpenDataAgentConfig.apply(this, arguments);
}

function createTransparentIFrame(el) {
    var iframeEl = document.createElement("iframe");
    var style = iframeEl.style;
    style.display = "block";
    style.border = "none";
    style.background = "transparent";
    iframeEl.referrerPolicy = "origin";
    iframeEl.setAttribute("frameborder", "0");
    iframeEl.setAttribute("allowtransparency", "true");
    var containerEl = resolveEl(el);
    if (containerEl) {
        containerEl.appendChild(iframeEl);
    }
    return iframeEl;
}
function resolveEl(el) {
    if (typeof el === "string") {
        return document.querySelector(el);
    }
    return el;
}

function genUid() {
    var rand = Math.random().toString(36).slice(2);
    return Date.now().toString(36) + "." + rand;
}
function normalizeError(error) {
    if (!error || typeof error !== "object") {
        return error;
    }
    return _extends({}, error, {
        message: error.message,
        stack: error.stack
    });
}

function useRemoteInvoke(postMessage, callback) {
    var messageMap = new Map();
    function invoke(args) {
        return new Promise(function(resolve, reject) {
            var uid = genUid();
            messageMap.set(uid, {
                resolve: resolve,
                reject: reject
            });
            postMessage({
                type: "ww-iframe-handle:call",
                uid: uid,
                args: args
            });
        });
    }
    function handleCallMessage(msg) {
        return _handleCallMessage.apply(this, arguments);
    }
    function _handleCallMessage() {
        _handleCallMessage = _async_to_generator(function(msg) {
            var _tmp, error;
            return __generator(this, function(_state) {
                switch(_state.label){
                    case 0:
                        _state.trys.push([
                            0,
                            2,
                            ,
                            3
                        ]);
                        _tmp = {
                            type: "ww-iframe-handle:response",
                            uid: msg.uid,
                            kind: "resolve"
                        };
                        return [
                            4,
                            callback(msg.args)
                        ];
                    case 1:
                        postMessage.apply(void 0, [
                            (_tmp.args = _state.sent(), _tmp)
                        ]);
                        return [
                            3,
                            3
                        ];
                    case 2:
                        error = _state.sent();
                        postMessage({
                            type: "ww-iframe-handle:response",
                            uid: msg.uid,
                            kind: "reject",
                            args: normalizeError(error)
                        });
                        throw error;
                    case 3:
                        return [
                            2
                        ];
                }
            });
        });
        return _handleCallMessage.apply(this, arguments);
    }
    function handleResponseMessage(msg) {
        var handle = messageMap.get(msg.uid);
        if (!handle) {
            return;
        }
        handle[msg.kind](msg.args);
        messageMap.delete(msg.uid);
    }
    return {
        invoke: invoke,
        handleCallMessage: handleCallMessage,
        handleResponseMessage: handleResponseMessage
    };
}

var attached = false;
var listeners = [];
function sendRemoteMessage(target, message, origin) {
    var data = typeof message === "string" ? message : JSON.stringify(message);
    target == null ? void 0 : target.postMessage(data, origin);
}
function useRemoteMessage(listener) {
    if (!attached) {
        attached = true;
        window.addEventListener("message", onWindowMessage);
    }
    listeners.push(listener);
    return function() {
        var index = listeners.indexOf(listener);
        if (index >= 0) {
            listeners.splice(index, 1);
        }
    };
}
function onWindowMessage(event) {
    try {
        var msg = JSON.parse(event.data);
        if (!msg.type.startsWith("ww-iframe-handle:")) {
            return;
        }
        for(var _iterator = _create_for_of_iterator_helper_loose(listeners.slice()), _step; !(_step = _iterator()).done;){
            var listener = _step.value;
            listener(msg, event);
        }
    } catch (error) {
    // noop
    }
}

function useIframeClient(iframeEl, origin, callback) {
    var _useRemoteInvoke = useRemoteInvoke(enqueueMessage, callback), invoke = _useRemoteInvoke.invoke, handleCallMessage = _useRemoteInvoke.handleCallMessage, handleResponseMessage = _useRemoteInvoke.handleResponseMessage;
    var messageQueue = [];
    var iframeReady = false;
    var dispose = useRemoteMessage(function(msg, event) {
        if (event.origin !== origin || iframeEl.contentWindow !== event.source) {
            return;
        }
        switch(msg.type){
            case "ww-iframe-handle:ready":
                return handleReadyMessage();
            case "ww-iframe-handle:call":
                return handleCallMessage(msg);
            case "ww-iframe-handle:response":
                return handleResponseMessage(msg);
        }
    });
    function onIframeLoad() {
        if (iframeReady) {
            return;
        }
        postMessage({
            type: "ww-iframe-handle:init"
        });
    }
    function handleReadyMessage() {
        if (iframeReady) {
            return;
        }
        iframeReady = true;
        for(var _iterator = _create_for_of_iterator_helper_loose(messageQueue), _step; !(_step = _iterator()).done;){
            var message = _step.value;
            postMessage(message);
        }
    }
    function enqueueMessage(message) {
        var data = JSON.stringify(message);
        if (!iframeReady) {
            messageQueue.push(data);
        } else {
            postMessage(data);
        }
    }
    function postMessage(message) {
        sendRemoteMessage(iframeEl.contentWindow, message, origin);
    }
    iframeEl.addEventListener("load", onIframeLoad);
    return {
        el: iframeEl,
        invoke: invoke,
        dispose: dispose
    };
}

function resolveSuiteConfigParams(url) {
    return _resolveSuiteConfigParams.apply(this, arguments);
}
function _resolveSuiteConfigParams() {
    _resolveSuiteConfigParams = _async_to_generator(function(url) {
        var registerOptions, data;
        return __generator(this, function(_state) {
            switch(_state.label){
                case 0:
                    registerOptions = getRegisterOptions();
                    if (!(registerOptions == null ? void 0 : registerOptions.getSuiteConfigSignature)) {
                        throw new SDKError("Missing getSuiteConfigSignature");
                    }
                    return [
                        4,
                        registerOptions.getSuiteConfigSignature(url)
                    ];
                case 1:
                    data = _state.sent();
                    return [
                        2,
                        {
                            suiteid: registerOptions.suiteId,
                            timestamp: "" + data.timestamp,
                            nonceStr: data.nonceStr,
                            signature: data.signature,
                            jsApiList: mapJsApiListToClient(registerOptions.jsApiList || [
                                "agentConfig"
                            ])
                        }
                    ];
            }
        });
    });
    return _resolveSuiteConfigParams.apply(this, arguments);
}

function has$1(obj, key) {
    return Object.prototype.hasOwnProperty.call(obj, key);
}

function handleCallMsg(msg, options, thisArg) {
    safeRun(options[msg.name], msg.payload || msg.data, thisArg);
}

var disposeModalFrame;
function showModalFrame(url, callback) {
    disposeModalFrame == null ? void 0 : disposeModalFrame();
    var iframeEl = createTransparentIFrame(document.body);
    var style = iframeEl.style;
    style.position = "fixed";
    style.left = "0";
    style.top = "0";
    style.zIndex = "1000";
    style.width = "100vw";
    style.height = "100vh";
    iframeEl.classList.add("wecom-jssdk-modal");
    iframeEl.setAttribute("src", url);
    var origin = new URL(url).origin;
    var client = useIframeClient(iframeEl, origin, function(msg) {
        if (msg.type === "close") {
            handleCloseMsg();
        }
        return callback == null ? void 0 : callback(msg);
    });
    function handleCloseMsg() {
        var _iframeEl_parentNode;
        client.dispose();
        (_iframeEl_parentNode = iframeEl.parentNode) == null ? void 0 : _iframeEl_parentNode.removeChild(iframeEl);
    }
    disposeModalFrame = handleCloseMsg;
    return client;
}

/**
 * 创建 JSAPI 触发面板
 *
 * 在非企业微信内置浏览器环境下，开发者可以创建 JSAPI 触发面板。当用户点击面板时，内置的 iframe 将调起用户本地的企业微信客户端并调用指定的 JSAPI
 *
 * @param name 要调用的 JSAPI 名称
 *
 * @limit
 * - 应用必须经过 SSO 登录获取 web_token
 * - 用户必须登录了企业微信桌面端且当前用户身份和页面身份一致
 */ function createJSAPIPanel(name, options) {
    var iframeEl = createTransparentIFrame(options.el);
    var style = iframeEl.style;
    style.width = "100%";
    style.height = "100%";
    var jsapiParamsMap = new Map();
    var dispose = useIframeClient(iframeEl, "https://login.work.weixin.qq.com", function(msg) {
        switch(msg.type){
            case "getStaticOptions":
                return handleGetStaticOptionsMsg();
            case "getJSAPIParams":
                return handleGetJSAPIParams(msg);
            case "openModalFrame":
                return handleOpenModalFrameMsg(msg);
            case "jsapiCallback":
                return handleJsapiCallbackMsg(msg);
            case "call":
                return handleCallMsg(msg, options);
        }
    }).dispose;
    function handleGetStaticOptionsMsg() {
        return {
            name: name,
            options: _extends({}, options, {
                el: undefined,
                params: undefined
            })
        };
    }
    function handleJsapiCallbackMsg(msg) {
        if (!jsapiParamsMap.has(msg.seq)) {
            return;
        }
        var jsapiParams = jsapiParamsMap.get(msg.seq);
        jsapiParamsMap.delete(msg.seq);
        if (msg.kind === "success") {
            safeRun(jsapiParams == null ? void 0 : jsapiParams.success, msg.payload);
        } else {
            safeRun(jsapiParams == null ? void 0 : jsapiParams.fail, msg.payload);
        }
        safeRun(jsapiParams == null ? void 0 : jsapiParams.complete, msg.payload);
    }
    function handleGetJSAPIParams(msg) {
        return _handleGetJSAPIParams.apply(this, arguments);
    }
    function _handleGetJSAPIParams() {
        _handleGetJSAPIParams = _async_to_generator(function(msg) {
            var _msg_payload, _msg_payload1, jsapiParams, _tmp, signUrl, _tmp1, _tmp2;
            return __generator(this, function(_state) {
                switch(_state.label){
                    case 0:
                        if (!isFunction(options.params)) return [
                            3,
                            2
                        ];
                        return [
                            4,
                            options.params()
                        ];
                    case 1:
                        _tmp = _state.sent();
                        return [
                            3,
                            3
                        ];
                    case 2:
                        _tmp = options.params;
                        _state.label = 3;
                    case 3:
                        jsapiParams = _tmp;
                        signUrl = getSignURL();
                        jsapiParamsMap.set(msg.seq, jsapiParams);
                        _tmp1 = {
                            webToken: options.webToken,
                            url: signUrl
                        };
                        if (!((_msg_payload = msg.payload) == null ? void 0 : _msg_payload.skipSignature)) return [
                            3,
                            4
                        ];
                        _tmp2 = undefined;
                        return [
                            3,
                            6
                        ];
                    case 4:
                        return [
                            4,
                            resolveSignatureData(signUrl, ((_msg_payload1 = msg.payload) == null ? void 0 : _msg_payload1.preferSignatureTypeList) || [
                                "agentConfig"
                            ])
                        ];
                    case 5:
                        _tmp2 = _state.sent();
                        _state.label = 6;
                    case 6:
                        return [
                            2,
                            (_tmp1.configParams = _tmp2, _tmp1.jsapi = name, _tmp1.jsapiParams = jsapiParams, _tmp1)
                        ];
                }
            });
        });
        return _handleGetJSAPIParams.apply(this, arguments);
    }
    function handleOpenModalFrameMsg(msg) {
        showModalFrame(msg.payload.url);
    }
    iframeEl.style.opacity = "0";
    iframeEl.src = "https://login.work.weixin.qq.com/wwopen/ww-jsapi-transparent-frame";
    return {
        /**
     * JSAPI 触发面板的 iframe 元素
     */ el: iframeEl,
        /**
     * 卸载 JSAPI 触发面板
     */ unmount: function unmount() {
            var _iframeEl_parentNode;
            dispose();
            (_iframeEl_parentNode = iframeEl.parentNode) == null ? void 0 : _iframeEl_parentNode.removeChild(iframeEl);
        }
    };
}
var resolveSignatureFnMap = {
    agentConfig: resolveAgentConfigParams,
    suiteConfig: resolveSuiteConfigParams
};
function resolveSignatureData(url, typeList) {
    return _resolveSignatureData.apply(this, arguments);
}
function _resolveSignatureData() {
    _resolveSignatureData = _async_to_generator(function(url, typeList) {
        var lastError, _iterator, _step, type, _tmp, error;
        return __generator(this, function(_state) {
            switch(_state.label){
                case 0:
                    lastError = new Error("Missing signature handler");
                    _iterator = _create_for_of_iterator_helper_loose(typeList);
                    _state.label = 1;
                case 1:
                    if (!!(_step = _iterator()).done) return [
                        3,
                        6
                    ];
                    type = _step.value;
                    _state.label = 2;
                case 2:
                    _state.trys.push([
                        2,
                        4,
                        ,
                        5
                    ]);
                    if (!has$1(resolveSignatureFnMap, type)) {
                        return [
                            3,
                            5
                        ];
                    }
                    _tmp = {
                        type: type
                    };
                    return [
                        4,
                        resolveSignatureFnMap[type](url)
                    ];
                case 3:
                    return [
                        2,
                        (_tmp.params = _state.sent(), _tmp)
                    ];
                case 4:
                    error = _state.sent();
                    lastError = error;
                    return [
                        3,
                        5
                    ];
                case 5:
                    return [
                        3,
                        1
                    ];
                case 6:
                    throw lastError;
            }
        });
    });
    return _resolveSignatureData.apply(this, arguments);
}

function has(object, key) {
    return Object.prototype.hasOwnProperty.call(object, key);
}
function isObject(val) {
    return typeof val === "object" && val !== null;
}
function includes(list, value) {
    if (!list) {
        return false;
    }
    return list.indexOf(value) >= 0;
}
var createIdentifier = createBuilder("Identifier" /* Types.Identifier */ , "name");
var createLiteral = createBuilder("Literal" /* Types.Literal */ , "value");
function createBuilder(type, key) {
    return function(val) {
        var _a;
        return _a = {
            type: type
        }, _a[key] = val, _a;
    };
}
function isIdentifierStart(ch) {
    return isInRange(ch, 65, 90) // A-Z
     || isInRange(ch, 97, 122) // a-z
     || ch === 36 /* Code.dollarSign */  || ch === 95 /* Code.underscore */ ;
}
function isIdentifierPart(ch) {
    return isIdentifierStart(ch) || isDecimalDigit(ch);
}
function isDecimalDigit(ch) {
    return isInRange(ch, 48, 57); // 0-9
}
function isHexDigit(ch) {
    return isDecimalDigit(ch) || isInRange(ch, 65, 70) // A-F
     || isInRange(ch, 97, 102); // a-f
}
function isInRange(val, min, max) {
    return val >= min && val <= max;
}
/**
 * 12.6 Names and Keywords & 13.1 Identifiers
 */ var LITERAL_NAME = {
    "null": null,
    "true": true,
    "false": false,
    NaN: NaN,
    Infinity: Infinity
};
var reserveWords = ("await break case catch class const continue " + "  default delete do else enum export " + "extends false finally for function if import " + "in instanceof new null return super switch " + "this throw true try typeof var void while " + "with yield").split(" ");
var reserveWordMap = {};
for(var _i = 0, reserveWords_1 = reserveWords; _i < reserveWords_1.length; _i++){
    var word = reserveWords_1[_i];
    reserveWordMap[word] = true;
}
/**
 * IdentifierReference
 *
 * https://tc39.es/ecma262/#prod-IdentifierReference
 */ function parseIdentifierReference(ctx) {
    var name = parseIdentifierName(ctx);
    if (has(LITERAL_NAME, name)) {
        return ctx.build(createLiteral(LITERAL_NAME[name]));
    }
    if (has(reserveWordMap, name)) {
        ctx.unexpected(name);
    }
    return ctx.build(createIdentifier(name));
}
/**
 * Identifier
 *
 * https://tc39.es/ecma262/#prod-Identifier
 */ function parseIdentifier(ctx) {
    var name = parseIdentifierName(ctx);
    return ctx.build(createIdentifier(name));
}
/**
 * IdentifierName
 *
 * https://tc39.es/ecma262/#prod-IdentifierName
 */ function parseIdentifierName(ctx) {
    if (!isIdentifierStart(ctx.peek())) {
        ctx.unexpected();
    }
    var start = ctx.index;
    do {
        ctx.next();
    }while (isIdentifierPart(ctx.peek()));
    return ctx.expr.slice(start, ctx.index);
}
/**
 * 12.8.3 Numeric Literals
 */ /**
 * NumericLiteral
 *
 * https://tc39.es/ecma262/#prod-NumericLiteral
 */ function parseNumericLiteral(ctx) {
    var number = "";
    while(isDecimalDigit(ctx.peek())){
        number += ctx.nextCh();
    }
    if (number === "0") {
        // HexIntegerLiteral
        // https://tc39.es/ecma262/#prod-HexIntegerLiteral
        if (ctx.eat(120 /* Code.lowercaseX */ ) || ctx.eat(88 /* Code.uppercaseX */ )) {
            number = "";
            while(isHexDigit(ctx.peek())){
                number += ctx.nextCh();
            }
            if (!number) {
                ctx.unexpected();
            }
            return ctx.build(createLiteral(parseInt(number, 16)));
        }
        // BinaryIntegerLiteral
        // https://tc39.es/ecma262/#prod-BinaryIntegerLiteral
        if (ctx.eat(98 /* Code.lowercaseB */ ) || ctx.eat(66 /* Code.uppercaseB */ )) {
            number = "";
            while(ctx.peek() === 48 /* Code.digit0 */  || ctx.peek() === 49 /* Code.digit1 */ ){
                number += ctx.nextCh();
            }
            if (!number) {
                ctx.unexpected();
            }
            return ctx.build(createLiteral(parseInt(number, 2)));
        }
    }
    if (ctx.peek() === 46 /* Code.dot */ ) {
        number += ctx.nextCh();
        while(isDecimalDigit(ctx.peek())){
            number += ctx.nextCh();
        }
    }
    // ExponentPart
    // https://tc39.es/ecma262/#prod-ExponentPart
    if (ctx.peek() === 101 /* Code.lowercaseE */  || ctx.peek() === 69 /* Code.uppercaseE */ ) {
        number += ctx.nextCh();
        if (ctx.peek() === 43 /* Code.plusSign */  || ctx.peek() === 45 /* Code.dash */ ) {
            number += ctx.nextCh();
        }
        var hasDecimal = false;
        while(isDecimalDigit(ctx.peek())){
            hasDecimal = true;
            number += ctx.nextCh();
        }
        if (!hasDecimal) {
            ctx.unexpected();
        }
    }
    if (isIdentifierStart(ctx.peek())) {
        ctx["throw"]("Variable name cannot start with a number (".concat(number).concat(ctx.peekCh(), ")."));
    }
    if (ctx.peek() === 46 /* Code.dot */  || number === ".") {
        ctx.unexpected();
    }
    return ctx.build(createLiteral(parseFloat(number)));
}
/**
 * 12.8.4 String Literals
 */ var ESCAPE_CHARACTER = {
    n: "\n",
    r: "\r",
    t: "	"
};
/**
 * StringLiteral
 *
 * https://tc39.es/ecma262/#prod-StringLiteral
 */ function parseStringLiteral(ctx) {
    var quote = ctx.nextCh();
    var value = "";
    var ch;
    while(ch = ctx.nextCh()){
        if (ch === quote) {
            return ctx.build(createLiteral(value));
        }
        if (ch !== "\\") {
            value += ch;
            continue;
        }
        ch = ctx.nextCh();
        if (has(ESCAPE_CHARACTER, ch)) {
            value += ESCAPE_CHARACTER[ch];
        } else {
            value += ch;
        }
    }
    ctx.unexpected();
}
/**
 * 13.2 Primary Expression
 */ /**
 * PrimaryExpression
 *
 * https://tc39.es/ecma262/#prod-PrimaryExpression
 */ function parsePrimaryExpression(ctx) {
    var code = ctx.peek();
    if (isDecimalDigit(code) || code === 46 /* Code.dot */ ) {
        return parseNumericLiteral(ctx);
    }
    if (code === 39 /* Code.singleQuote */  || code === 34 /* Code.doubleQuote */ ) {
        return parseStringLiteral(ctx);
    }
    if (isIdentifierStart(code)) {
        return parseIdentifierReference(ctx);
    }
    ctx.unexpected();
}
/**
 * 13.3 Left-Hand-Side Expressions
 */ /**
 * LeftHandSideExpression
 *
 * https://tc39.es/ecma262/#prod-LeftHandSideExpression
 */ function parseLeftHandSideExpression(ctx) {
    var content = parsePrimaryExpression(ctx);
    var code;
    while(code = ctx.peek()){
        // base [ prop ]
        if (code === 91 /* Code.leftSquareBracket */ ) {
            content = buildMemberExpression(ctx, content, true);
            continue;
        }
        // base . prop
        if (ctx.eat(46 /* Code.dot */ )) {
            content = buildMemberExpression(ctx, content);
            continue;
        }
        break;
    }
    return content;
}
/**
 * MemberExpression
 *
 * https://tc39.es/ecma262/#prod-MemberExpression
 */ function buildMemberExpression(ctx, object, computed) {
    if (computed === void 0) {
        computed = false;
    }
    var property;
    if (computed) {
        ctx.expect(91 /* Code.leftSquareBracket */ );
        property = parseExpression(ctx);
        ctx.expect(93 /* Code.rightSquareBracket */ );
    } else {
        property = parseIdentifier(ctx);
    }
    return ctx.build({
        type: "MemberExpression" /* Types.MemberExpression */ ,
        object: object,
        property: property,
        computed: computed
    });
}
/**
 * 13.16 Comma Operator ( , )
 */ /**
 * Expression
 *
 * https://tc39.es/ecma262/#prod-Expression
 */ function parseExpression(ctx) {
    return parseLeftHandSideExpression(ctx);
}
function createParserContext(expr) {
    return {
        expr: expr,
        index: 0,
        peek: function peek() {
            return this.expr.charCodeAt(this.index);
        },
        peekCh: function peekCh() {
            return this.expr.charAt(this.index);
        },
        next: function next() {
            this.index += 1;
        },
        nextCh: function nextCh() {
            this.index += 1;
            return this.expr.charAt(this.index - 1);
        },
        eat: function eat(ch) {
            if (this.peek() !== ch) {
                return false;
            }
            this.next();
            this.skipWhitespace();
            return true;
        },
        expect: function expect(ch) {
            if (!this.eat(ch)) {
                this.unexpected();
            }
        },
        skip: function skip(length) {
            this.index += length;
            this.skipWhitespace();
        },
        skipWhitespace: function skipWhitespace() {
            var ch = expr.charCodeAt(this.index);
            while(ch === 32 /* Code.space */  || ch === 9 /* Code.tab */  || ch === 13 /* Code.carriageReturn */  || ch === 10 /* Code.lineFeed */ ){
                this.index += 1;
                ch = expr.charCodeAt(this.index);
            }
            return this.index;
        },
        build: function build(expr) {
            this.skipWhitespace();
            return expr;
        },
        unexpected: function unexpected(token) {
            if (!token && this.index >= expr.length) {
                throw this["throw"]("Unexpected end of input.");
            }
            throw this["throw"]("Unexpected token '".concat(token || this.peekCh(), "'."));
        },
        "throw": function(msg) {
            throw new SyntaxError("".concat(msg, " (1:").concat(this.index, ")"));
        }
    };
}
function parseDataPath(input) {
    var ctx = createParserContext(input.trim());
    var ast = parseExpression(ctx);
    if (ctx.index !== ctx.expr.length) {
        ctx.unexpected();
    }
    return ast;
}
var DEFAULT_PROTECTED_KEYS = [
    "constrcutor",
    "prototype",
    "__proto__"
];
function patch(data, update, options) {
    if (options === void 0) {
        options = {};
    }
    var protectedKeys = options.protectedKeys || DEFAULT_PROTECTED_KEYS;
    var set = options.set || defaultSet;
    for(var _i = 0, _a = Object.keys(update); _i < _a.length; _i++){
        var key = _a[_i];
        if (includes(protectedKeys, key)) {
            continue;
        }
        if (!includes(key, "[") && !includes(key, ".")) {
            set(data, key, update[key]);
            continue;
        }
        try {
            var path = extractPath(parseDataPath(key), protectedKeys || []);
            if (path) {
                setIn(data, path, update[key], set);
            } else {
                set(data, key, update[key]);
            }
        } catch (error) {
            set(data, key, update[key]);
        }
    }
}
function extractPath(expr, protectedKeys, path) {
    if (protectedKeys === void 0) {
        protectedKeys = [];
    }
    if (path === void 0) {
        path = [];
    }
    if (expr.type === "Identifier" /* Types.Identifier */ ) {
        path.unshift(expr.name);
        return path;
    }
    if (expr.type !== "MemberExpression" /* Types.MemberExpression */ ) {
        return;
    }
    var object = expr.object, property = expr.property, computed = expr.computed;
    if (computed) {
        if (property.type !== "Literal" /* Types.Literal */ ) {
            return;
        }
        var value = property.value;
        if (includes(protectedKeys, value)) {
            return;
        }
        path.unshift(value);
    } else {
        if (property.type !== "Identifier" /* Types.Identifier */ ) {
            return;
        }
        var name_1 = property.name;
        if (includes(protectedKeys, name_1)) {
            return;
        }
        path.unshift(name_1);
    }
    return extractPath(object, protectedKeys, path);
}
function setIn(data, path, value, set) {
    var ptr = data;
    for(var i = 0, ii = path.length - 1; i < ii; i++){
        var key = path[i];
        if (!has(ptr, key) || !isObject(ptr[key])) {
            set(ptr, key, typeof path[i + 1] === "string" ? {} : []);
        }
        ptr = ptr[key];
    }
    set(ptr, path[path.length - 1], value);
    return ptr;
}
function defaultSet(object, key, value) {
    // eslint-disable-next-line no-param-reassign
    object[key] = value;
}

function cloneDeep(value) {
    if (Array.isArray(value)) {
        return value.map(cloneDeep);
    }
    if (value == null || typeof value !== "object") {
        return value;
    }
    var result = {};
    for(var _iterator = _create_for_of_iterator_helper_loose(Object.keys(value)), _step; !(_step = _iterator()).done;){
        var key = _step.value;
        result[key] = cloneDeep(value[key]);
    }
    return result;
}

function createWeakRef(target) {
    if (typeof WeakRef !== "undefined") {
        return new WeakRef(target);
    }
    return {
        deref: function() {
            return target;
        }
    };
}

function useRef(invokeFrame) {
    var refid2refMap = new Map();
    var refid2eventMap = new Map();
    function getRef(name) {
        return _getRef.apply(this, arguments);
    }
    function _getRef() {
        _getRef = _async_to_generator(function(name) {
            var _refid2refMap_get, res, ref, newRef;
            return __generator(this, function(_state) {
                switch(_state.label){
                    case 0:
                        return [
                            4,
                            invokeFrame({
                                type: "refGet",
                                name: name
                            })
                        ];
                    case 1:
                        res = _state.sent();
                        if (!res) {
                            return [
                                2
                            ];
                        }
                        ref = (_refid2refMap_get = refid2refMap.get(res.refId)) == null ? void 0 : _refid2refMap_get.deref();
                        if (ref) {
                            return [
                                2,
                                ref
                            ];
                        }
                        newRef = createRef(res.refId);
                        refid2refMap.set(res.refId, createWeakRef(newRef));
                        return [
                            2,
                            newRef
                        ];
                }
            });
        });
        return _getRef.apply(this, arguments);
    }
    function handleEvent(refId, data) {
        var subscribers = refid2eventMap.get(refId);
        if (!subscribers) {
            return;
        }
        for(var _iterator = _create_for_of_iterator_helper_loose(subscribers), _step; !(_step = _iterator()).done;){
            var fn = _step.value;
            fn(data);
        }
    }
    function createRef(refId) {
        var subscribers = [];
        function invoke(name, data) {
            return invokeFrame({
                type: "refCall",
                refId: refId,
                name: name,
                data: data
            });
        }
        function subscribe(fn) {
            if (!refid2eventMap.has(refId)) {
                refid2eventMap.set(refId, subscribers);
            }
            subscribers.push(fn);
            return function() {
                var index = subscribers.indexOf(fn);
                if (index < 0) {
                    return;
                }
                subscribers.splice(index, 1);
                if (!subscribers.length) {
                    refid2eventMap.delete(refId);
                }
            };
        }
        return {
            invoke: invoke,
            subscribe: subscribe
        };
    }
    return {
        get: getRef,
        handleEvent: handleEvent
    };
}

function createOpenSessionInjector(params) {
    return new Promise(function(resolve, reject) {
        win.WWOpenData.createOpenSessionInjector(params, function(error, injectOpenSession) {
            if (error || !injectOpenSession) {
                reject(error || new Error("System error"));
            } else {
                resolve(injectOpenSession);
            }
        });
    });
}

var BUSINESS_URL_ORIGIN = "https://open.work.weixin.qq.com";
var BUSINESS_URL = "" + BUSINESS_URL_ORIGIN + "/wwopen/ww-open-data-frame";
var PROTECTED_DATA_KEYS = [
    "constructor",
    "prototype",
    "__proto__",
    "__ob__"
];
/**
 * 创建 open-data frame 工厂对象
 *
 * @compat WeCom >= 4.0.20
 *
 * @example
 * ```ts
 * const factory = ww.createOpenDataFrameFactory()
 * const instance = factory.createOpenDataFrame(options)
 *
 * containerEl.appendChild(instance.el)
 * ```
 */ function createOpenDataFrameFactory(params) {
    var openSessionInjectorPromise = createSessionInjector();
    function createSessionInjector() {
        return _createSessionInjector.apply(this, arguments);
    }
    function _createSessionInjector() {
        _createSessionInjector = _async_to_generator(function() {
            var error, _params_onError;
            return __generator(this, function(_state) {
                switch(_state.label){
                    case 0:
                        if (!isWeCom) {
                            return [
                                2
                            ];
                        }
                        _state.label = 1;
                    case 1:
                        _state.trys.push([
                            1,
                            3,
                            ,
                            4
                        ]);
                        return [
                            4,
                            initOpenData()
                        ];
                    case 2:
                        _state.sent();
                        return [
                            2,
                            createOpenSessionInjector({
                                url: BUSINESS_URL
                            })
                        ];
                    case 3:
                        error = _state.sent();
                        params == null ? void 0 : (_params_onError = params.onError) == null ? void 0 : _params_onError.call(params, error);
                        throw error;
                    case 4:
                        return [
                            2
                        ];
                }
            });
        });
        return _createSessionInjector.apply(this, arguments);
    }
    function injectOpenSession(iframe) {
        return _injectOpenSession.apply(this, arguments);
    }
    function _injectOpenSession() {
        _injectOpenSession = _async_to_generator(function(iframe) {
            var inject;
            return __generator(this, function(_state) {
                switch(_state.label){
                    case 0:
                        if (!isWeCom) {
                            return [
                                2
                            ];
                        }
                        return [
                            4,
                            openSessionInjectorPromise
                        ];
                    case 1:
                        inject = _state.sent();
                        if (inject) {
                            inject(iframe);
                        }
                        return [
                            2
                        ];
                }
            });
        });
        return _injectOpenSession.apply(this, arguments);
    }
    function createOpenDataFrame(options) {
        var iframeEl = createTransparentIFrame(options.el);
        var iframeId = win.WWOpenData.registerOpenFrame(iframeEl);
        if (!options.template) {
            throw new Error("options.template is required");
        }
        var _useIframeClient = useIframeClient(iframeEl, BUSINESS_URL_ORIGIN, function(msg) {
            switch(msg.type){
                case "init":
                    return handleInitMsg();
                case "call":
                    return handleFrameCallMsg(msg);
                case "refreshSession":
                    return handleRefreshSessionMsg();
                case "invokeJsapi":
                    return handleInvokeJsapiMsg(msg);
                case "agentConfig":
                    return handleAgentConfigMsg();
                case "modal":
                    return handleModalMsg(msg);
                case "refEvent":
                    return handleRefEventMsg(msg);
            }
        }), invoke$1 = _useIframeClient.invoke, dispose = _useIframeClient.dispose;
        var _useRef = useRef(invoke$1), getRef = _useRef.get, handleRefEvent = _useRef.handleEvent;
        var publicInstance = _extends({
            el: iframeEl,
            data: cloneDeep(options.data) || {},
            getRef: getRef,
            setData: setData,
            dispose: disposeComponent
        }, options.methods);
        function handleInitMsg() {
            return _handleInitMsg.apply(this, arguments);
        }
        function _handleInitMsg() {
            _handleInitMsg = _async_to_generator(function() {
                return __generator(this, function(_state) {
                    switch(_state.label){
                        case 0:
                            return [
                                4,
                                injectOpenSession(iframeEl)
                            ];
                        case 1:
                            _state.sent();
                            return [
                                2,
                                {
                                    id: iframeId,
                                    hooks: getHookNames(options),
                                    options: _extends({}, options, {
                                        el: undefined,
                                        methods: undefined,
                                        data: publicInstance.data
                                    })
                                }
                            ];
                    }
                });
            });
            return _handleInitMsg.apply(this, arguments);
        }
        function handleFrameCallMsg(msg) {
            var base = options;
            if (msg.kind === "method") {
                base = base.methods;
            }
            return handleCallMsg(msg, base, publicInstance);
        }
        function handleRefreshSessionMsg() {
            return _handleRefreshSessionMsg.apply(this, arguments);
        }
        function _handleRefreshSessionMsg() {
            _handleRefreshSessionMsg = _async_to_generator(function() {
                return __generator(this, function(_state) {
                    switch(_state.label){
                        case 0:
                            openSessionInjectorPromise = createSessionInjector();
                            return [
                                4,
                                injectOpenSession(iframeEl)
                            ];
                        case 1:
                            _state.sent();
                            return [
                                2
                            ];
                    }
                });
            });
            return _handleRefreshSessionMsg.apply(this, arguments);
        }
        function handleInvokeJsapiMsg(msg) {
            return invoke("wwapp.invokeJsApiByCallInfo", {
                callInfo: msg.callInfo
            });
        }
        function handleAgentConfigMsg() {
            return _handleAgentConfigMsg.apply(this, arguments);
        }
        function _handleAgentConfigMsg() {
            _handleAgentConfigMsg = _async_to_generator(function() {
                var url, _tmp;
                return __generator(this, function(_state) {
                    switch(_state.label){
                        case 0:
                            url = getSignURL();
                            _tmp = {
                                url: url
                            };
                            return [
                                4,
                                resolveAgentConfigParams(url)
                            ];
                        case 1:
                            return [
                                2,
                                (_tmp.params = _state.sent(), _tmp)
                            ];
                    }
                });
            });
            return _handleAgentConfigMsg.apply(this, arguments);
        }
        function handleModalMsg(msg) {
            return _handleModalMsg.apply(this, arguments);
        }
        function _handleModalMsg() {
            _handleModalMsg = _async_to_generator(function(msg) {
                var defaultPreviewType, res;
                return __generator(this, function(_state) {
                    defaultPreviewType = isWeCom ? "wecom-window" : undefined;
                    if (typeof options.handleModal !== "function") {
                        return [
                            2,
                            {
                                modalType: defaultPreviewType
                            }
                        ];
                    }
                    res = options.handleModal({
                        modalUrl: msg.modalUrl
                    });
                    return [
                        2,
                        {
                            modalType: res === false ? "iframe" : defaultPreviewType
                        }
                    ];
                });
            });
            return _handleModalMsg.apply(this, arguments);
        }
        function handleRefEventMsg(msg) {
            handleRefEvent(msg.refId, msg.data);
        }
        function setData(partialData) {
            return _setData.apply(this, arguments);
        }
        function _setData() {
            _setData = _async_to_generator(function(partialData) {
                return __generator(this, function(_state) {
                    switch(_state.label){
                        case 0:
                            patch(publicInstance.data, partialData, {
                                protectedKeys: PROTECTED_DATA_KEYS
                            });
                            return [
                                4,
                                invoke$1({
                                    type: "update",
                                    options: {
                                        data: partialData
                                    }
                                })
                            ];
                        case 1:
                            _state.sent();
                            return [
                                2
                            ];
                    }
                });
            });
            return _setData.apply(this, arguments);
        }
        function disposeComponent() {
            dispose();
            win.WWOpenData.unregisterOpenFrame(iframeId);
        }
        iframeEl.src = BUSINESS_URL;
        return publicInstance;
    }
    return {
        /**
     * 创建 open-data frame 组件
     */ createOpenDataFrame: createOpenDataFrame
    };
}
function getHookNames(options) {
    return Object.keys(options).filter(function(name) {
        return typeof options[name] === "function";
    });
}

/**
 * 显示确认安全网关配置页面。
 *
 * 在桌面端页面以 iframe 弹窗的形式覆盖在页面上；在移动端页面将跳转至确认页面，返回后页面需要主动确认 confirm_id 的确认情况。
 */ function showSecurityGatewayConfirmModal(options) {
    var url = new URL("https://open.work.weixin.qq.com/wwopen/secureGateway/confirm");
    url.searchParams.set("confirm_id", options.confirmId);
    if (isIOS || isAndroid) {
        location.href = url.href;
        return;
    }
    url.searchParams.set("modal", "true");
    var client = showModalFrame(url.href, function(msg) {
        switch(msg.type){
            case "call":
                return handleCallMsg(msg, options);
            case "close":
                return;
            default:
                throw new Error("Unknown message type '" + msg.type + "'");
        }
    });
    return {
        /**
     * 弹窗面板的 iframe 元素
     */ el: client.el,
        /**
     * 卸载弹窗面板
     */ unmount: function unmount() {
            var _client_el_parentNode;
            client.dispose();
            (_client_el_parentNode = client.el.parentNode) == null ? void 0 : _client_el_parentNode.removeChild(client.el);
        }
    };
}

var SDK_VERSION = "1.5.0-beta.6";

var WWLoginType;
(function(WWLoginType) {
    /**
   * [第三方应用登录](#45846)
   */ WWLoginType["serviceApp"] = "ServiceApp";
    /**
   * [企业自建应用登录](/document/path/98151)、[服务商代开发应用登录](/document/path/98173)
   */ WWLoginType["corpApp"] = "CorpApp";
})(WWLoginType || (WWLoginType = {}));
var WWLoginLangType;
(function(WWLoginLangType) {
    /**
   * 中文
   */ WWLoginLangType["zh"] = "zh";
    /**
   * 英文
   */ WWLoginLangType["en"] = "en";
})(WWLoginLangType || (WWLoginLangType = {}));
var WWLoginRedirectType;
(function(WWLoginRedirectType) {
    /**
   * 默认 `top window` 顶层页面跳转
   */ WWLoginRedirectType["top"] = "top";
    /**
   * 通过 `onLoginSuccess` 回调用户授权 `code`，开发者自行处理跳转
   */ WWLoginRedirectType["callback"] = "callback";
    /**
   * 登录组件跳转
   */ WWLoginRedirectType["self"] = "self";
})(WWLoginRedirectType || (WWLoginRedirectType = {}));
var WWLoginPanelSizeType;
(function(WWLoginPanelSizeType) {
    /**
   * 默认: 480x416px
   */ WWLoginPanelSizeType["middle"] = "middle";
    /**
   * 小尺寸: 320x380px
   */ WWLoginPanelSizeType["small"] = "small";
})(WWLoginPanelSizeType || (WWLoginPanelSizeType = {}));

var _obj;
var PANEL_SIZE = (_obj = {}, _obj[WWLoginPanelSizeType.middle] = [
    "480px",
    "416px"
], _obj[WWLoginPanelSizeType.small] = [
    "320px",
    "380px"
], _obj);
/**
 * 初始化企业微信Web登录组件，创建登录面板。
 *
 * @example
 * ```ts
 * // 初始化
 * const wwLogin = ww.createWWLoginPanel({
 *   el: '#ww_login',
 *   params: {
 *     login_type: 'CorpApp',
 *     appid: 'wwbbb6a7b539f2xxxxx',
 *     agentid: '10000xx',
 *     redirect_uri: 'https://work.weixin.qq.com',
 *     state: 'loginState',
 *     redirect_type: 'callback',
 *   },
 *   onCheckWeComLogin({ isWeComLogin }) {
 *     console.log(isWeComLogin)
 *   },
 *   onLoginSuccess({ code }) {
 *     console.log({ code })
 *   },
 *   onLoginFail(err) {
 *     console.log(err)
 *   },
 * })
 *
 * // 卸载
 * wwLogin.unmount()
 * ```
 */ function createWWLoginPanel(options) {
    var _options_params;
    var _getPanelSize = getPanelSize((_options_params = options.params) == null ? void 0 : _options_params.panel_size), width = _getPanelSize.width, height = _getPanelSize.height;
    var iframeEl = createTransparentIFrame(options.el);
    var style = iframeEl.style;
    style.width = width;
    style.height = height;
    var dispose = useIframeClient(iframeEl, "https://login.work.weixin.qq.com", function(msg) {
        if (msg.type === "call") {
            return handleCallMsg(msg, options);
        }
        throw new Error("Unknown message type '" + msg.type + "'");
    }).dispose;
    var url = new URL("https://login.work.weixin.qq.com/wwlogin/sso/login");
    for(var _iterator = _create_for_of_iterator_helper_loose(Object.keys(options.params || {})), _step; !(_step = _iterator()).done;){
        var key = _step.value;
        url.searchParams.set(key, options.params[key]);
    }
    url.searchParams.set("version", SDK_VERSION);
    iframeEl.src = url.href;
    return {
        el: iframeEl,
        unmount: function unmount() {
            var _iframeEl_parentNode;
            dispose();
            (_iframeEl_parentNode = iframeEl.parentNode) == null ? void 0 : _iframeEl_parentNode.removeChild(iframeEl);
        }
    };
}
function getPanelSize(panelSizeType) {
    if (panelSizeType === void 0) panelSizeType = WWLoginPanelSizeType.middle;
    var size = PANEL_SIZE[panelSizeType] || PANEL_SIZE[WWLoginPanelSizeType.middle];
    return {
        width: size[0],
        height: size[1]
    };
}

/**
 * 创建 open-data frame 内的 ScrollViewContext
 *
 * @param instance open-data frame 实例
 * @param refName 模板引用名称
 * @returns ScrollViewContext 对象
 */ function createScrollViewContext(instance, refName) {
    return _createScrollViewContext.apply(this, arguments);
}
function _createScrollViewContext() {
    _createScrollViewContext = _async_to_generator(function(instance, refName) {
        var ref;
        return __generator(this, function(_state) {
            switch(_state.label){
                case 0:
                    return [
                        4,
                        instance.getRef(refName)
                    ];
                case 1:
                    ref = _state.sent();
                    if (!ref) {
                        return [
                            2
                        ];
                    }
                    return [
                        2,
                        {
                            scrollTo: function scrollTo(options) {
                                ref.invoke("scrollTo", options);
                            },
                            scrollIntoView: function scrollIntoView(selector, options) {
                                ref.invoke("scrollIntoView", {
                                    selector: selector,
                                    options: options
                                });
                            }
                        }
                    ];
            }
        });
    });
    return _createScrollViewContext.apply(this, arguments);
}

/**
 * From https://gist.github.com/schacon/12703
 */ function hex_sha1(s) {
    return rstr2hex(rstr_sha1(s));
}
/**
 * Calculate the SHA1 of a raw string
 */ function rstr_sha1(s) {
    return binb2rstr(binb_sha1(rstr2binb(s), s.length * 8));
}
/**
 * Convert a raw string to a hex string
 */ function rstr2hex(input) {
    var hex_tab = "0123456789abcdef";
    var output = "";
    var x;
    for(var i = 0; i < input.length; i++){
        x = input.charCodeAt(i);
        output += hex_tab.charAt(x >>> 4 & 0x0f) + hex_tab.charAt(x & 0x0f);
    }
    return output;
}
/**
 * Convert a raw string to an array of big-endian words
 * Characters >255 have their high-byte silently ignored.
 */ function rstr2binb(input) {
    var output = new Array(input.length >> 2);
    for(var i = 0; i < output.length; i++)output[i] = 0;
    for(var i1 = 0; i1 < input.length * 8; i1 += 8)output[i1 >> 5] |= (input.charCodeAt(i1 / 8) & 0xff) << 24 - i1 % 32;
    return output;
}
/**
 * Convert an array of little-endian words to a string
 */ function binb2rstr(input) {
    var output = "";
    for(var i = 0; i < input.length * 32; i += 8)output += String.fromCharCode(input[i >> 5] >>> 24 - i % 32 & 0xff);
    return output;
}
/**
 * Calculate the SHA-1 of an array of big-endian words, and a bit length
 */ function binb_sha1(x, len) {
    /* append padding */ x[len >> 5] |= 0x80 << 24 - len % 32;
    x[(len + 64 >> 9 << 4) + 15] = len;
    var w = new Array(80);
    var a = 1732584193;
    var b = -271733879;
    var c = -1732584194;
    var d = 271733878;
    var e = -1009589776;
    for(var i = 0; i < x.length; i += 16){
        var olda = a;
        var oldb = b;
        var oldc = c;
        var oldd = d;
        var olde = e;
        for(var j = 0; j < 80; j++){
            if (j < 16) w[j] = x[i + j];
            else w[j] = bit_rol(w[j - 3] ^ w[j - 8] ^ w[j - 14] ^ w[j - 16], 1);
            var t = safe_add(safe_add(bit_rol(a, 5), sha1_ft(j, b, c, d)), safe_add(safe_add(e, w[j]), sha1_kt(j)));
            e = d;
            d = c;
            c = bit_rol(b, 30);
            b = a;
            a = t;
        }
        a = safe_add(a, olda);
        b = safe_add(b, oldb);
        c = safe_add(c, oldc);
        d = safe_add(d, oldd);
        e = safe_add(e, olde);
    }
    return [
        a,
        b,
        c,
        d,
        e
    ];
}
/**
 * Perform the appropriate triplet combination function for the current
 * iteration
 */ function sha1_ft(t, b, c, d) {
    if (t < 20) return b & c | ~b & d;
    if (t < 40) return b ^ c ^ d;
    if (t < 60) return b & c | b & d | c & d;
    return b ^ c ^ d;
}
/**
 * Determine the appropriate additive constant for the current iteration
 */ function sha1_kt(t) {
    return t < 20 ? 1518500249 : t < 40 ? 1859775393 : t < 60 ? -1894007588 : -899497514;
}
/**
 * Add integers, wrapping at 2^32. This uses 16-bit operations internally
 * to work around bugs in some JS interpreters.
 */ function safe_add(x, y) {
    var lsw = (x & 0xffff) + (y & 0xffff);
    var msw = (x >> 16) + (y >> 16) + (lsw >> 16);
    return msw << 16 | lsw & 0xffff;
}
/**
 * Bitwise rotate a 32-bit number to the left.
 */ function bit_rol(num, cnt) {
    return num << cnt | num >>> 32 - cnt;
}

function getSignature(options) {
    var normalized = options;
    if (typeof options === "string") {
        normalized = {
            ticket: options
        };
    }
    var nonceStr = normalized.nonceStr || Math.random().toString(36).slice(2);
    var timestamp = normalized.timestamp || Math.floor(Date.now() / 1000);
    var url = normalized.url || getHref().split("#")[0];
    var ticket = normalized.ticket;
    var signature = hex_sha1("jsapi_ticket=" + ticket + "&noncestr=" + nonceStr + "&timestamp=" + timestamp + "&url=" + url);
    return {
        timestamp: timestamp,
        nonceStr: nonceStr,
        signature: signature
    };
}

var env = {
    isWeChat: isWeChat,
    isWeCom: isWeCom
};
var IS_WECOM_SDK = true;

export { CameraMode, CreateExternalPaymentType, EntryType, EnvVersion, IS_WECOM_SDK, InTalkType, InputCorpGroupContactMode, InputCorpGroupContactType, LiveType, LocationType, NetworkType, OAType, OaExtDataType, OpenUserProfileType, PrintFileIdType, ProductViewType, Proximity, SDK_VERSION, ScanQRCodeType, SelectEnterpriseContactMode, SelectEnterpriseContactType, SelectExternalContactType, SelectPrivilegedContactMode, SizeType, SourceType, WWLoginLangType, WWLoginPanelSizeType, WWLoginRedirectType, WWLoginType, addCard, addDevice, checkJsApi, checkSchedule, chooseCard, chooseImage, chooseInvoice, chooseWXPay, claimClassAdmin, closeBLEConnection, closeBluetoothAdapter, closeWindow, connectWifi, consumeAndShareCard, createBLEConnection, createChatWithMsg, createCorpGroupChat, createExternalPayment, createJSAPIPanel, createOpenDataFrameFactory, createSchoolPayment, createScrollViewContext, createWWLoginPanel, discoverDevice, downloadImage, downloadLivingReplay, downloadVoice, ensureAgentConfigReady, ensureConfigReady, ensureCorpConfigReady, enterHWOpenTalk, enterpriseVerify, env, getApprovalSelectedItems, getBLEDeviceCharacteristics, getBLEDeviceServices, getBeacons, getBluetoothAdapterState, getBluetoothDevices, getClipboardData, getConnectedBluetoothDevices, getConnectedWifi, getContext, getCurCorpGroupChat, getCurCorpGroupContact, getCurExternalChat, getCurExternalContact, getLocalImgData, getLocation, getNetworkType, getShareInfo, getSignature, getVerifyParams, getWifiList, hideAllNonBaseMenuItem, hideChatAttachmentMenu, hideMenuItems, hideOptionMenu, initOpenData, invoke, isWeixinJSBridgeReady, launchMiniprogram, navigateToAddCustomer, navigateToKfChat, notifyBLECharacteristicValueChange, on, onBLECharacteristicValueChange, onBLEConnectionStateChange, onBeaconServiceChange, onBeaconUpdate, onBluetoothAdapterStateChange, onBluetoothDeviceFound, onGetWifiList, onHistoryBack, onLocationChange, onMenuShareAppMessage, onMenuShareQQ, onMenuShareQZone, onMenuShareTimeline, onMenuShareWechat, onMenuShareWeibo, onNetworkStatusChange, onSearchBeacons, onUserCaptureScreen, onVoicePlayEnd, onVoiceRecordEnd, onWeixinJSBridgeReady, onWifiConnected, openAddress, openAppComment, openAppDeviceDataAuth, openAppManage, openBluetoothAdapter, openBusinessView, openCard, openDefaultBrowser, openDeviceProfile, openEnterpriseChat, openEnterpriseRedPacket, openExistedChatWithMsg, openLocation, openProductSpecificView, openThirdAppServiceChat, openUserProfile, pauseVoice, playVoice, previewFile, previewImage, printFile, queryCurrHWOpenTalk, readBLECharacteristicValue, refundExternalPayment, register, replayLiving, saveApprovalSelectedItems, scanQRCode, selectCorpGroupContact, selectEnterpriseContact, selectExternalContact, selectPrivilegedContact, sendChatMessage, setClipboardData, setShareAttr, shareAppMessage, shareToExternalChat, shareToExternalContact, shareToExternalMoments, shareWechatMessage, showAllNonBaseMenuItem, showMenuItems, showOptionMenu, showSecurityGatewayConfirmModal, startAutoLBS, startBeaconDiscovery, startBluetoothDevicesDiscovery, startLiving, startMeeting, startRecord, startSearchBeacons, startWecast, startWifi, stopAutoLBS, stopBeaconDiscovery, stopBluetoothDevicesDiscovery, stopRecord, stopSearchBeacons, stopVoice, stopWifi, thirdPartyOpenPage, translateVoice, updateAppMessageShareData, updateCorpGroupChat, updateEnterpriseChat, updateMomentsSetting, updateTimelineShareData, uploadImage, uploadVoice, wedocSelectDoc, wedriveSelectDir, wedriveSelectFileForDownload, wedriveSelectFileForShare, writeBLECharacteristicValue };
