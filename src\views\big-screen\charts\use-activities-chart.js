import { markRaw, ref, onMounted } from "vue";
import { watchDebounced, useWindowSize } from "@vueuse/core";
import echarts from "@/utils/echarts";

export default function useServiceChart(chartRef, salvProName, salvProValue) {
  const chart = ref(null);
  const customerData = ref([]);
  const serviceData = ref([]);
  const xAxisData = ref([]);
  const { width, height } = useWindowSize();

  function setChart() {
    chart.value = markRaw(echarts.init(chartRef.value));
  }

  function paint(repaint = false) {
    if (chartRef.value && !chart.value) {
      setChart();
      repaint = false;
    }
    if (!chart.value) return;
    const option = getServiceOptions(salvProName, salvProValue);
    chart.value.setOption(option, repaint);
  }
  return paint();
  // onMounted(paint);
  // watchDebounced(
  //   [xAxisData, customerData, serviceData],
  //   (n) => {
  //     paint(true);
  //   },
  //   { debounce: 500, maxWait: 1000 }
  // );
  // watchDebounced(
  //   [width, height],
  //   () => {
  //     chart.value && chart.value.resize();
  //   },
  //   { debounce: 500, maxWait: 1500 }
  // );

  // return { customerData, serviceData, xAxisData };
  // return { customerData, serviceData, xAxisData };
}

function getServiceOptions(salvProName, salvProValue) {
  var salvProMax = []; //背景按最大值
  for (let i = 0; i < salvProValue.length; i++) {
    salvProMax.push(salvProValue[0]);
  }
  return {
    grid: {
      left: 0,
      right: 0,
      bottom: 0,
      top: 0,
      containLabel: true,
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "none",
      },
      formatter: function (params) {
        return params[0].name + " : " + params[0].value;
      },
    },
    xAxis: {
      show: false,
      type: "value",
    },
    yAxis: [
      {
        type: "category",
        inverse: true,
        axisLabel: {
          show: true,
          textStyle: {
            color: "#fff",
          },
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        data: salvProName,
      },
      {
        type: "category",
        inverse: true,
        axisTick: "none",
        axisLine: "none",
        show: true,
        axisLabel: {
          textStyle: {
            color: "#ffffff",
            fontSize: "12",
          },
        },
        data: salvProValue,
      },
    ],
    series: [
      {
        name: "值",
        type: "bar",
        zlevel: 1,
        itemStyle: {
          normal: {
            barBorderRadius: 20,
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: "rgb(57,89,255,1)",
              },
              {
                offset: 1,
                color: "rgb(46,200,207,1)",
              },
            ]),
          },
        },
        barWidth: 10,
        barGap: "10%",
        data: salvProValue,
      },
    ],
  };
}
