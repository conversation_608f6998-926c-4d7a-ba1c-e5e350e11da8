<template>
  <div>
    <el-dialog :model-value="visible" :width="350" title="字段调整" @close="close">
      <div class="w-full flex" justify-center v-if="type === '团队码'">
        <el-radio-group v-model="tabPosition" style="margin-bottom: 10px">
          <el-radio-button label="baseInfo">基础信息收集模版</el-radio-button>
          <el-radio-button label="healthInfo" v-if="teamFileds.healthInfo.length > 0">健康信息收集模版</el-radio-button>
        </el-radio-group>
      </div>
      <div class="w-100px">
        <el-popover placement="bottom" :width="300">
          <template #reference>
            <div class="flex cursor-pointer" style="align-items: center">
              <span class="pl-10px w-70px" style="color: #000">已有字段</span>
              <img src="@/assets/gantanhaozhong.png" class="w-20px h-20px" />
            </div>
          </template>
          <div>
            <div>
              <li>
                客户建档模版为客户扫二维码后，需填写的信息字段，该模版由机构管理员设定。如需要增删字段，请由机构管理员前往“配置中心”>>“客户档案模版”内设置。
              </li>
              <li>
                团队可支持对已有字段设置是否需要客户填写。必填状态字段不允许设置。选择“不填”则客户扫码建档该字段不需要填写。
              </li>
            </div>
          </div>
        </el-popover>
      </div>

      <div class="container p-15px">
        <div class="item pb-10px" v-for="item in list" :key="item.title" @mouseover="showTiem(item)">
          <div class="title" :class="selectTime.title === item.title ? 'active' : ''">
            <div :class="item.fieldStatus === 'disable' ? 'deleted' : ''">
              {{ item.name }}
            </div>
            <div v-if="selectTime.title === item.title">
              <div v-if="!item.required">
                <div main-color class="pointer" @click="removeItem(item)" v-if="item.fieldStatus !== 'disable'">
                  不填
                </div>
                <div v-else main-color @click="recover(item)" class="pointer">恢复</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { computed, ref } from "vue";
import { memberStore } from "@/store/member";
let { corpInfo } = memberStore();

const teamFileds = ref({
  healthInfo: [],
  viceInfo: [],
  baseInfo: [],
});
const visible = ref(false);
const tabPosition = ref("baseInfo");
const selectTime = ref({});
const type = ref("");

const list = computed(() => {
  return teamFileds.value[tabPosition.value];
});
function openDialog(t, obj) {
  visible.value = true;
  type.value = t;
  if (obj) {
    teamFileds.value = obj;
  }
}

function showTiem(item) {
  selectTime.value = item;
}
function removeItem(item) {
  teamFileds.value[tabPosition.value] = teamFileds.value[tabPosition.value].map(
    (i) => {
      if (i.title === item.title) {
        i["fieldStatus"] = "disable";
      }
      return i;
    }
  );
}
function recover(item) {
  teamFileds.value[tabPosition.value] = teamFileds.value[tabPosition.value].map(
    (i) => {
      if (i.title === item.title) {
        i["fieldStatus"] = "enable";
      }
      return i;
    }
  );
}
function close() {
  visible.value = false;
}

defineExpose({
  openDialog,
});
</script>
<style scoped lang="scss">
.container {
  display: flex;
  flex-wrap: wrap;
  width: 100%;

  .item {
    width: 50%;
    display: flex;
    align-items: center;
  }

  .title {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 5px 10px;
  }
}

.active {
  background: rgb(244, 244, 255);
}

.deleted {
  text-decoration: line-through;
}
</style>
