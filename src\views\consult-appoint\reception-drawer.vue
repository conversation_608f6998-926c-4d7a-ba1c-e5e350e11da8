<template>
  <el-drawer v-model="visible" title="新增咨询接待" size="70%" style="--el-drawer-padding-primary: 0">
    <my-layout v-loading="drawerLoading" class="bg-[#f2f3f4]">
      <layout-main ref="mainRef">
        <customer-detail canRename :visible="visible" :customer="customer" @showCustomerDetail="handleDetail" @changeName="changeName" :request="true" @reload="customerReload" />
        <div class="bg-white mt-10px" v-if="customer.addMethod === 'eStoreReport'">
          <div class="px-10px text-16px font-semibold py-10px">网络咨询记录</div>
          <el-table border stripe :data="eConsuleRecord">
            <el-common-column prop="date" label="咨询时间" :min-width="120" />
            <el-common-column prop="project" label="意向项目" :min-width="160">
              <template #default="{ row: { projectNames } }">
                <el-tooltip :disabled="!projectNames" placement="top" effect="light" :content="projectNames" popper-class="max-w-480px">
                  <span>{{ projectNames }}</span>
                </el-tooltip>
              </template>
            </el-common-column>
            <el-common-column prop="remark" label="报备说明" :min-width="240">
              <template #default="{ row: { reportDesc } }">
                <el-tooltip placement="top" effect="light" :disabled="!reportDesc || reportDesc.length < 7" :content="reportDesc" popper-class="max-w-480px">
                  <span>{{ reportDesc }}</span>
                </el-tooltip>
              </template>
            </el-common-column>
            <el-common-column prop="source" label="信息来源" :min-width="100">
              <template #default="{ row: { sourceName } }">
                <el-tooltip placement="top" effect="light" :disabled="!sourceName || sourceName.length < 7" :content="sourceName">
                  <span>{{ sourceName }}</span>
                </el-tooltip>
              </template>
            </el-common-column>
            <el-common-column prop="register" label="登记人" :min-width="140">
              <template #default="{ row: { userId, registrantUserId } }">
                <ww-user v-if="userId" :openid="registrantUserId || userId" />
              </template>
            </el-common-column>
          </el-table>
        </div>

        <div class="bg-white mt-10px" v-if="appointmentRecords.length > 0">
          <div class="px-10px text-16px font-semibold py-10px">网电预约记录</div>
          <el-table border stripe :data="appointmentRecords">
            <el-common-column prop="" label="到院状态" :min-width="100">
              <template #default="{ row }">
                <div style="color: #ff4949">未到院</div>
              </template>
            </el-common-column>
            <el-common-column prop="appointmentDate" label="预约时间" :min-width="120">
              <template #default="{ row }">
                {{ row.appointmentDate }}
              </template>
            </el-common-column>
            <el-common-column prop="projectName" label="咨询项目" :min-width="160">
              <template #default="{ row }">
                <el-tooltip :disabled="!row.projectName" placement="top" effect="light" :content="row.projectName" popper-class="max-w-480px">
                  <span>{{ row.projectName }}</span>
                </el-tooltip>
              </template>
            </el-common-column>
            <el-common-column prop="infoSource" label="信息来源" :min-width="100">
              <template #default="{ row }">
                <el-tooltip placement="top" effect="light" :disabled="!row.infoSource || row.infoSource.length < 7" :content="row.infoSource">
                  <span>{{ row.infoSource }}</span>
                </el-tooltip>
              </template>
            </el-common-column>
            <el-common-column prop="introducer" label="所属开发" :min-width="140">
              <template #default="{ row }">
                <ww-user v-if="row.introducer" :openid="row.introducer" />
              </template>
            </el-common-column>
          </el-table>
        </div>

        <el-form class="mt-10px px-20px py-15px bg-white" label-position="top">
          <div class="px-10px text-16px font-semibold py-10px">分诊</div>
          <el-form-item class="is-required mb-10px" label="就诊目的">
            <el-radio-group v-model="form.visitType">
              <el-radio label="consultation" value="consultation" size="large">咨询</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item class="is-required" label="信息来源">
            <remote-select-customer-source v-model="form.source" placeholder="请选择客户来源" />
          </el-form-item>
          <el-form-item class="mb-10px" label="意向项目">
            <project-intent-select v-model="selectedProjectIds" :placeholder="'请选择意向项目'" @change="handleProjectChange" />
          </el-form-item>
          <el-form-item class="is-required mb-10px" label="分诊给">
            <el-select v-model="receptionPerson" ref="selectRef" popper-class="repention-drawer-receptionPerson-select-popover" placeholder="请选择分诊给" class="w-full reception-drawer-receptionPerson-select" clearable @change="change" @visible-change="visibleChange">
              <template #prefix>
                <div class="truncate h-30px leading-30px text-left" :style="{ width: prefixWidth, color: 'rgb(96, 98, 102)' }">
                  <ww-user v-if="form.receptionPersonUserId" :openid="form.receptionPersonUserId" />
                  <span class="ml-10px">{{ teamName }}</span>
                </div>
              </template>
              <el-option-group v-for="group in filterOptions" :key="group.teamId" :label="group.name">
                <el-option v-for="item in group.list" :key="item.value" :value="item.value">
                  <div class="truncate">
                    <ww-user :openid="item.userId"></ww-user>
                  </div>
                </el-option>
              </el-option-group>
            </el-select>
          </el-form-item>
          <el-form-item label="分诊备注">
            <el-input v-model="form.triageRemark" type="textarea" placeholder="请输入分诊备注" maxlength="500" :autosize="{ minRows: 4, maxRows: 6 }" resize="none" show-word-limit></el-input>
          </el-form-item>
        </el-form>
        <div class="h-30vh"></div>
      </layout-main>
      <layout-item>
        <div class="text-center mt-10px py-10px bg-white" common-shadow--r>
          <el-button plain class="w-100px" @click="visible = false">取消</el-button>
          <el-button v-if="consult._id" class="w-100px" :loading="closeLoading" type="danger" @click="closeRecord">取消分诊</el-button>
          <el-button class="w-100px" :loading="loading" type="primary" @click="sumbit" v-loading="loading">确定</el-button>
        </div>
      </layout-item>
    </my-layout>
  </el-drawer>
  <teleport v-if="showSearchInput" to=".repention-drawer-receptionPerson-select-popover .teleport-container">
    <div class="px-20px pt-20px pb-5px">
      <el-input clearable v-model="keyword" placeholder="搜索..." />
    </div>
  </teleport>
  <teammate-picker :teamId="form.teamId" :userId="form.receptionPersonUserId" :visible="pickerVisible" :width="pickerWidth" @change="changeTeammate" @close="closePicker" />
</template>
<script setup>
import { computed, ref, onMounted, watch, nextTick } from "vue";
import { useVModel, useElementBounding } from "@vueuse/core";
import { storeToRefs } from "pinia";
import { ElMessage, ElMessageBox } from "element-plus";
import { memberStore } from "@/store/member";
import dayjs from "dayjs";
import { addConsultRecord, updateConsultRecord, getConsultRecord, deleteConsultRecord, updateCustomerPersonResponsibles, todayOrderExists, getPreConsultRecord, getEConsuleRecord, getAppointmentRecord, batchUpdateAppointmentRecordStatus } from "@/api/consult";
import { updateCustomerCounselor } from "@/api/member"; // 新增导入
import { staffStore } from "@/store/staff";
import { teamStore } from "@/store/team";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import teammatePicker from "./teammate-picker.vue";
import customerDetail from "@/components/customer-detail";
import elCommonColumn from "@/components/el-common-column/el-common-column.vue";
import projectIntentSelect from "@/components/benefit-management/project-intent-select.vue";
import remoteSelectCustomerSource from "@/components/benefit-management/remote-select-customer-source.vue";

const { corpInfo, memberInfo } = storeToRefs(memberStore());
const emit = defineEmits(["update:modelValue", "success", "showCustomerDetail", "changeName"]);
const { allTeams, currentTeamId } = storeToRefs(teamStore());
const props = defineProps({
  conditions: { type: Array, default: [] },
  customer: { type: Object, default: () => ({}) },
  consult: { type: Object, default: () => ({}) },
  modelValue: {
    type: Object,
    default: () => ({}),
  },
});
const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());
const selectRef = ref(null);
const staffMap = computed(() =>
  staffList.value.reduce((acc, cur) => {
    acc[cur.userid] = cur;
    return acc;
  }, {})
);
const mainRef = ref();
const loading = ref(false);
const closeLoading = ref(false);
const drawerLoading = ref(false);
const { width: selectWidth } = useElementBounding(selectRef);
const prefixWidth = computed(() => `${selectWidth.value - 40}px`);
const showSearchInput = ref(false);
const keyword = ref("");
const receptionPerson = ref("");
const options = computed(() => {
  const data = allTeams.value.reduce((acc, cur) => {
    const memberList = Array.isArray(cur.memberList) ? cur.memberList : [];
    const group = { teamId: cur.teamId, name: cur.name, list: [] };
    group.list = memberList
      .map((i) => ({
        value: `${cur.teamId}||${i}`,
        userId: i,
        teamId: cur.teamId,
        name: cur.name,
        staff: staffMap.value[i],
      }))
      .filter((i) => i.staff && Array.isArray(i.staff.job) && i.staff.job.includes("consultant"));
    if (group.list.length) return [...acc, group];
    return acc;
  }, []);
  return data;
});

const filterOptions = computed(() => {
  const name = keyword.value.trim();
  if (name === "") return options.value;
  return options.value
    .map((i) => {
      const { list, ...item } = i;
      return { ...item, list: list.filter((j) => j.staff && typeof j.staff.anotherName === "string" && j.staff.anotherName.includes(name)) };
    })
    .filter((i) => i.list.length);
});
const customer = computed(() => props.customer);
const form = ref({
  triagePersonUserId: "",
  triageRemark: "",
  visitType: "consultation",
});
const visible = useVModel(props, "modelValue", emit);

const teamName = computed(() => {
  const team = allTeams.value.find((i) => i.teamId === form.value.teamId && i.teamId);
  return team ? team.name : "";
});

const selectedProjectIds = ref([]);
const projects = ref([]);

function change(val) {
  const [teamId = "", userId = ""] = val.split("||");
  form.value.receptionPersonUserId = userId;
  form.value.teamId = teamId;
}
onMounted(() => {
  if (staffList.value.length === 0) getStaffList();
});

function handleDetail() {
  emit("showCustomerDetail", props.customer._id);
}

// 处理项目选择变更
function handleProjectChange(selectedProjects) {
  projects.value = selectedProjects.map((project) => ({
    _id: project.id,
    projectName: project.name,
    projectCateId: "",
  }));
}

function customerReload() {
  emit("success");
}

async function setProjects(arr) {
  await nextTick();
  if (Array.isArray(arr) && arr.length > 0) {
    selectedProjectIds.value = arr.map((item) => item._id).filter(Boolean);
    projects.value = arr.map((item) => ({
      _id: item._id,
      projectName: item.projectName || "",
      projectCateId: item.projectCateId || "",
    }));
  } else {
    selectedProjectIds.value = [];
    projects.value = [];
  }
}

async function closeRecord() {
  if (closeLoading.value) return;
  await ElMessageBox.confirm("确定要取消分诊吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  });
  closeLoading.value = true;
  const { success, message, data } = await deleteConsultRecord({ id: props.consult._id });
  if (success) {
    emit("success", data);
    visible.value = false;
  } else {
    ElMessage.error(message);
  }
  closeLoading.value = false;
}

async function initalFill() {
  drawerLoading.value = true;
  const { data } = await getPreConsultRecord({
    customerId: customer.value._id,
  });
  const record = data && data.data ? data.data : null;
  drawerLoading.value = false;
  if (record) {
    const source = record && Array.isArray(record.source) ? record.source : [];
    const projects = record && Array.isArray(record.projects) ? record.projects : [];
    form.value.source = source.filter(Boolean);
    // 直接使用 projectIds
    selectedProjectIds.value = record.projectIds || projects.map((item) => item._id).filter(Boolean);
    projects.value = projects;
  } else {
    const latestConsult = eConsuleRecord.value[0];
    // 优先使用网店咨询记录中的 projectIds
    if (latestConsult) {
      const projectIds = latestConsult.projectIds || (Array.isArray(latestConsult.projects) ? latestConsult.projects.map((p) => p._id).filter(Boolean) : []);

      selectedProjectIds.value = projectIds;
      // 保持 projects 对象结构一致
      projects.value = projectIds.map((id) => ({
        _id: id,
        projectName: "", // 名称会由组件自动填充
        projectCateId: "",
      }));
    } else {
      selectedProjectIds.value = [];
      projects.value = [];
    }
  }
}

async function visibleChange(val) {
  if (!val) return;
  const { top } = mainRef.value.getPosition();
  if (top < 400) {
    mainRef.value.scrollTo(400, 500);
  }
  await nextTick();
  const popover = document.querySelector(".repention-drawer-receptionPerson-select-popover");
  const el = document.querySelector(".repention-drawer-receptionPerson-select-popover .teleport-container");
  if (popover && !el) {
    const el = document.createElement("div");
    el.classList.add("teleport-container");
    popover.prepend(el);
  }
  await nextTick();
  showSearchInput.value = val;
}
watch(
  () => visible.value,
  async (n) => {
    if (props.consult._id) {
      // 编辑模式
      form.value.receptionPersonUserId = props.consult.receptionPersonUserId;
      form.value.triagePersonUserId = props.consult.triagePersonUserId;
      form.value.triageRemark = props.consult.triageRemark;
      form.value.visitType = props.consult.visitType;
      form.value.teamId = props.consult.teamId;
      form.value.source = props.consult.source;
      // 增加日志帮助调试
      console.log("编辑模式下的咨询记录:", props.consult);

      // 确保获取到 projectIds 数据
      if (Array.isArray(props.consult.projectIds) && props.consult.projectIds.length > 0) {
        selectedProjectIds.value = [...props.consult.projectIds];
      } else if (Array.isArray(props.consult.projects) && props.consult.projects.length > 0) {
        // 如果没有 projectIds，尝试从 projects 中获取
        selectedProjectIds.value = props.consult.projects.map((p) => p._id).filter(Boolean);
      } else {
        selectedProjectIds.value = [];
      }
      if (Array.isArray(props.consult.projects) && props.consult.projects.length > 0) {
        projects.value = props.consult.projects.map((p) => ({
          _id: p._id,
          projectName: p.projectName || "",
          projectCateId: p.projectCateId || "",
        }));
      } else {
        projects.value = selectedProjectIds.value.map((id) => ({
          _id: id,
          projectName: "",
          projectCateId: "",
        }));
      }
    } else {
      form.value.triageRemark = "";
      form.value.visitType = "consultation";
      form.value.triagePersonUserId = localStorage.getItem("userId");
      form.value.receptionPersonUserId = "";
      form.value.teamId = "";
      form.value.source = [];
    }
    if (n) {
      await nextTick();
      mainRef.value && mainRef.value.scrollTo(0);
      await getCustomerConsuleRecord();
      await getCustomerAppointmentRecord();
      if (!props.consult._id) {
        initalFill();
      } else {
        // 在组件显示后，确保 selectedProjectIds 已正确设置
        await nextTick();
        console.log("设置选中的项目IDs:", selectedProjectIds.value);
      }
      keyword.value = "";
      receptionPerson.value = form.value.teamId && form.value.receptionPersonUserId ? `${form.value.teamId}||${form.value.receptionPersonUserId}` : {};
    }
  },
  { immediate: true }
);

watch(
  () => selectedProjectIds.value,
  (newVal) => {
    if (newVal && newVal.length > 0) {
      console.log("selectedProjectIds 发生变化:", newVal);
    }
  }
);

async function sumbit() {
  if (loading.value) return;
  if (!Array.isArray(form.value.source) || form.value.source.length === 0) {
    ElMessage.warning("请选择信息来源");
    return;
  }
  if (!form.value.receptionPersonUserId) {
    ElMessage.warning("请选择分诊人员");
    return;
  }
  if (!props.consult._id) {
    const res = await todayOrderExists({ customerId: props.customer._id });
    if (res && res.data && res.data.exist) {
      await ElMessageBox.confirm("该客户今天已经存在分诊记录，是否继续分诊？", "提示", { type: "warning" });
    }
  }
  let params = {
    triagePersonUserId: form.value.triagePersonUserId,
    triageRemark: form.value.triageRemark,
    visitType: form.value.visitType,
    receptionPersonUserId: form.value.receptionPersonUserId,
    teamId: form.value.teamId,
    source: form.value.source,
    projectIds: selectedProjectIds.value, // 使用 projectIds 替代 projects
  };
  if (!props.consult._id) {
    params["customerId"] = props.customer._id;
  }

  if (props.customer.counselorRecord && Array.isArray(props.customer.counselorRecord) && props.customer.counselorRecord.length > 0) {
    params["counselorUserId"] = props.customer.counselorRecord[props.customer.counselorRecord.length - 1].counselor;
  } else {
    params["counselorUserId"] = form.value.receptionPersonUserId;
  }
  if (props.customer.introducerUserId) {
    params["introducerUserId"] = props.customer.introducerUserId;
  }
  if (props.customer.introducerRecord && Array.isArray(props.customer.introducerRecord) && props.customer.introducerRecord.length > 0) {
    params["introducerUserId"] = props.customer.introducerRecord[props.customer.introducerRecord.length - 1].introducer;
  }
  loading.value = true;
  const { success, message, data } = props.consult._id ? await updateConsultRecord({ id: props.consult._id, params, customerId: props.customer._id }) : await addConsultRecord(params);

  if (success) {
    if (appointmentRecords.value.length > 0 && !props.consult._id) {
      const appointmentIds = appointmentRecords.value.map((record) => record._id).filter(Boolean);
      if (appointmentIds.length > 0) {
        try {
          await batchUpdateAppointmentRecordStatus({
            ids: appointmentIds,
            appointmentStatus: "inHospital",
          });
        } catch (error) {
          console.error("更新预约记录状态失败", error);
        }
      }
    }
    // 更新咨询师信息
    try {
      await updateCustomerCounselor({
        customerId: props.customer._id,
        counselorRecord: props.customer.counselorRecord || [],
        creator: localStorage.getItem("userId"),
        counselor: form.value.receptionPersonUserId,
        teamId: form.value.teamId,
        personResponsibles: updateCounselorPersonResponsibles(),
        customerTeamIds: updateCounselorTeamId(),
      });
      console.log("咨询师更新成功");
    } catch (error) {
      console.error("更新咨询师失败", error);
    }

    ElMessage.success("分诊成功");
    emit("success", data);
  } else {
    ElMessage.error(message);
  }
  visible.value = false;
  loading.value = false;
}

// 更新客户负责人
function updateCounselorPersonResponsibles() {
  const { personResponsibles = [] } = props.customer;
  let oldCounselorUserId = "";
  let oldTeamId = "";

  // 获取当前的咨询师和团队ID
  if (props.customer.counselorRecord && Array.isArray(props.customer.counselorRecord) && props.customer.counselorRecord.length > 0) {
    const lastRecord = props.customer.counselorRecord[props.customer.counselorRecord.length - 1];
    oldCounselorUserId = lastRecord.counselor;
    oldTeamId = lastRecord.teamId;
  }

  // 如果新旧咨询师和团队ID相同，不需要更新
  if (oldCounselorUserId === form.value.receptionPersonUserId && oldTeamId === form.value.teamId) {
    return personResponsibles;
  }

  // 过滤掉旧的咨询师负责人
  const newPersonResponsibles = personResponsibles.filter((i) => !(i.corpUserId == oldCounselorUserId && i.teamId == oldTeamId));

  // 添加新的咨询师负责人
  newPersonResponsibles.push({
    corpUserId: form.value.receptionPersonUserId,
    teamId: form.value.teamId,
  });

  return newPersonResponsibles;
}

// 更新客户团队ID
function updateCounselorTeamId() {
  let customerTeamIds = props.customer.teamId || [];
  if (!Array.isArray(customerTeamIds)) {
    customerTeamIds = typeof customerTeamIds === "string" ? [customerTeamIds] : [];
  }

  // 获取旧的咨询师团队ID
  let oldTeamId = "";
  if (props.customer.counselorRecord && Array.isArray(props.customer.counselorRecord) && props.customer.counselorRecord.length > 0) {
    oldTeamId = props.customer.counselorRecord[props.customer.counselorRecord.length - 1].teamId;
  }

  // 如果团队ID变更，需要更新
  if (oldTeamId !== form.value.teamId) {
    customerTeamIds = customerTeamIds.filter((i) => i !== oldTeamId);
    if (!customerTeamIds.includes(form.value.teamId)) {
      customerTeamIds.push(form.value.teamId);
    }
  }

  return customerTeamIds;
}

function changeName(name) {
  emit("changeName", name);
}

const eConsuleRecord = ref([]);
async function getCustomerConsuleRecord() {
  const { data, message, success } = await getEConsuleRecord({
    customerId: props.customer._id,
    page: 1,
    pageSize: 10,
  });

  if (Array.isArray(data.list) && data.list.length > 0) {
    eConsuleRecord.value = data.list
      .map((i) => ({
        ...i,
        projectNames: Array.isArray(i.projectNames) ? i.projectNames.join("、") : "",
        sourceName: Array.isArray(i.source) ? i.source.join("-") : "",
        // 确保保留原始的 projectIds
        projectIds: i.projectIds || (Array.isArray(i.projects) ? i.projects.map((p) => p._id).filter(Boolean) : []),
      }))
      .slice(0, 1);
  } else {
    eConsuleRecord.value = [];
  }

  if (!success) ElMessage.error(message);
  if (props.consult._id) return;

  const record = eConsuleRecord.value[0];
  if (record) {
    // 设置信息来源
    if (Array.isArray(record.source)) {
      form.value.source = [...record.source];
    }
  }
}

const appointmentRecords = ref([]);

async function getCustomerAppointmentRecord() {
  const params = {
    corpId: localStorage.getItem("corpId"),
    page: 1,
    pageSize: 5,
    appointmentStatus: "notInHospital",
    customerId: customer.value._id,
  };

  try {
    const { data, success } = await getAppointmentRecord(params);
    if (success && data && data.data && Array.isArray(data.data.list)) {
      appointmentRecords.value = data.data.list.map((i) => {
        return {
          appointmentDate: dayjs(i.appointmentTime).format("YYYY-MM-DD HH:mm"),
          projectName: Array.isArray(i.projectNames) ? i.projectNames.join("、") : "",
          infoSource: Array.isArray(i.source) ? i.source.join("-") : "",
          appointmentStatus: i.appointmentStatus,
          introducer: i.introducer,
          ...i,
        };
      });
    }
  } catch (error) {
    console.error("获取预约记录失败", error);
  }
}
</script>
<style lang="scss" scoped>
:deep(.hidden-inner-content > .select-trigger > .el-input > .el-input__wrapper > .el-input__inner) {
  opacity: 0;
}

:deep(.reception-drawer-receptionPerson-select > .select-trigger > .el-input > .el-input__wrapper > .el-input__inner) {
  display: none;
}

:deep(.reception-drawer-receptionPerson-select > .select-trigger > .el-input > .el-input__wrapper > .el-input__prefix) {
  flex-grow: 1;
}
</style>
