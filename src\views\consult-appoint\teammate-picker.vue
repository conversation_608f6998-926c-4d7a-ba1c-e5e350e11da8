<template>
  <el-dialog :model-value="props.visible" :width="width" title="请选择" @close="close()">
    <div v-loading="loading" class="flex h-40vh">
      <div class="flex-shrink-0 w-1/2 border-r h-full border-gray-200">
        <el-scrollbar color-normal class="px-10px">
          <div class="text-16px font-semibold pt-10px mb-5px">从团队列表选择</div>
          <div v-for="team in allTeams" :key="team.teamId">
            <div class="flex items-center pl-10px pt-15px cursor-pointer text-15px" @click="toggle(team)">
              <div class="flex-shrink-0">
                <el-icon v-if="expand[team.teamId]">
                  <CaretBottom />
                </el-icon>
                <el-icon v-else>
                  <CaretRight />
                </el-icon>
              </div>
              <div class="w-0 flex-grow truncate"> {{ team.name }}</div>
            </div>
            <div v-if="team.memberList && team.memberList.length > 0" v-show="expand[team.teamId]" class="pl-30px"
              style="--el-checkbox-height:24px">
              <div v-for="userid in team.memberList" :key="`${team.teamId}-${userid}`" class="truncate pt-10px"
                @click="select(userid, team.teamId)">
                <el-checkbox :model-value="userId === userid && teamId === team.teamId">
                  <ww-user :openid="userid"></ww-user>
                </el-checkbox>
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
      <el-scrollbar class="flex-grow" color-normal>
        <div class="text-16px font-semibold pt-10px pl-15px mb-5px">已选成员</div>
        <div v-if="userId" class="flex items-center pt-10px px-15px ">
          <div class="w-0 flex-grow">
            <ww-user :openid="userId"></ww-user>
          </div>
          <div class="flex-shrink-0 text-gray-500 hover:text-red-500 cursor-pointer" @click="remove()">
            <el-icon class="transform translate-y-1px" :size="14">
              <CloseBold />
            </el-icon>
          </div>
        </div>
      </el-scrollbar>
    </div>
    <template #footer>
      <div text-center>
        <el-button plain class="w-100px" type="primary" @click="close()">取消</el-button>
        <el-button class="w-100px" :loading="loading" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { storeToRefs } from 'pinia'
import { ElMessage } from "element-plus";
import { teamStore } from "@/store/team";

const emits = defineEmits(['close', 'change']);
const props = defineProps({
  userId: { type: String, default: '' },
  teamId: { type: String, default: '' },
  visible: { type: Boolean, default: false }
})
const expand = ref({});
const userId = ref('')
const teamId = ref('')

const { allTeams } = storeToRefs(teamStore());

function select(userid, teamid) {
  userId.value = userid
  teamId.value = teamid
}

function toggle(team) {
  expand.value[team.teamId] = !expand.value[team.teamId]
}

function remove() {
  userId.value = ''
  teamId.value = ''
}

function close() {
  emits('close')
}

function confirm() {
  emits('change', userId.value, teamId.value)
}

watch(() => props.visible, (n) => {
  if (n) {
    userId.value = typeof props.userId === 'string' ? props.userId : ''
    teamId.value = typeof props.teamId === 'string' ? props.teamId : ''
  }
})

</script>