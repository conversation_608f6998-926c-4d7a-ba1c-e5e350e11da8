<template>
  <el-drawer :model-value="visible" title="咨询记录" size="70%" style="--el-drawer-padding-primary: 0" @close="close()">
    <my-layout class="bg-[#f2f3f4]">
      <layout-main>
        <customer-header request :customer-id="consultRecord.customerId" @showCustomerDetail="showCustomerDetail" :request="true" @reload="customerReload" />
        <div class="mt-10px bg-white">
          <div class="px-10px text-16px font-semibold py-10px">咨询信息</div>
          <div class="pt-30px pb-60px px-60px flex items-center border-b border-gray-200">
            <template v-for="(step, idx) in steps" :key="step.key">
              <div v-if="idx > 0" class="flex-grow rounded h-4px mx-4px" :class="step.checked ? 'bg-green-500' : 'bg-gray-200'"></div>
              <div class="relative flex-shrink-0 h-20px w-20px text-center rounded" :class="step.checked ? 'bg-green-500' : 'bg-gray-200'">
                <el-icon v-if="step.checked" class="text-16px text-white"><Select /></el-icon>
                <div class="absolute -top-24px w-80px -left-30px text-14px text-gray-500">{{ step.title || "" }}</div>
                <div class="absolute -bottom-44px w-160px text-14px truncate" :class="idx === 2 ? '-right-10px text-right' : '-left-30px text-left'">
                  <div v-if="step.time">{{ step.time }}</div>
                  <div v-if="step.userid" class="mt-6px">
                    {{ step.label }}：
                    <ww-user :openid="step.userid" />
                  </div>
                  <div v-else-if="step.text" class="mt-6px">{{ step.label }}：{{ step.text }}</div>
                </div>
              </div>
            </template>
          </div>
          <el-form class="p-15px" label-suffiix="：" label-position="top" label-suffix="：">
            <el-row>
              <el-col :span="6">
                <el-form-item label="就诊类型">
                  {{ VisitTypeMap[consultRecord.visitType] || "" }}
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="客户阶段">
                  {{ ConsultStageMap[consultRecord.consultStage] || "" }}
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="分诊备注">
              {{ consultRecord.triageRemark || "" }}
            </el-form-item>
            <el-form-item label="面诊医生">
              <ww-user-select :list="receptionDoctorList" placeholder="请选择面诊医生" :value="form.receptionDoctorUserId" @change="changeReceptionDoctor($event)" />
            </el-form-item>
            <el-form-item class="is-required">
              <template #label>
                <span>意向项目</span>
              </template>
              <project-intent-select v-model="form.projectIds" :disabled="isRead" @change="handleProjectChange" />
            </el-form-item>
            <el-form-item class="is-required" label="客户主诉">
              <el-input type="textarea" v-model="form.customerComplaint" placeholder="输入内容不超过3000字" maxlength="3000" :autosize="{ minRows: 6, maxRows: 10 }" resize="none" show-word-limit></el-input>
            </el-form-item>
            <!-- <el-form-item label="面诊医生">
              <ww-user-select class="w-full" :list="doctorIds" placeholder="请选择处理人" :value="form.interviewDoctor"
                @change="changeDoctor($event)" />
            </el-form-item> -->
            <el-form-item label="处理方案">
              <el-input type="textarea" v-model="form.dealPlan" placeholder="输入内容不超过3000字" maxlength="3000" :autosize="{ minRows: 6, maxRows: 10 }" resize="none" show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="未成交原因">
              <el-input type="textarea" v-model="form.noDealReason" placeholder="输入内容不超过3000字" maxlength="3000" :autosize="{ minRows: 6, maxRows: 10 }" resize="none" show-word-limit></el-input>
            </el-form-item>
          </el-form>
        </div>
      </layout-main>
      <layout-item>
        <div text-center class="py-10px mt-1px bg-white" common-shadow--r>
          <el-button plain class="w-100px" type="primary" @click="close()">取消</el-button>
          <el-button class="w-100px" :loading="loading" type="primary" @click="confirm()" v-if="viewType !== 'record'">确定</el-button>
        </div>
      </layout-item>
    </my-layout>
  </el-drawer>
  <project-picker selectWidthCate ref="projectPickerRef" :value="projects" :visible="projectModalVisible" :width="projectModalWidth" @close="closeProjectModal()" @change="changeProjects" />
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import useModal from "@/hooks/useModal";
import { updateConsultRecord } from "@/api/consult";
import { ConsultStageMap, VisitTypeMap, VisitStatusObj, CustomerComplaintTemp } from "@/baseData";
import { staffStore } from "@/store/staff";
import { Plus } from "@element-plus/icons-vue";
import customerHeader from "@/components/customer-header/customer-header.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import projectPicker from "@/components/project-picker/project-picker.vue";
import WwUserSelect from "@/components/ww-user-select";
import ProjectIntentSelect from "@/components/benefit-management/project-intent-select.vue";

const emits = defineEmits(["close", "success", "showCustomerDetail"]);
const props = defineProps({
  id: { type: String, default: "" },
  visible: { type: Boolean, default: false },
  width: { type: String, default: "500px" },
  consultRecord: { type: Object, default: () => ({}) },
  doctorList: { type: Array, default: () => [] },
  viewType: { type: String, default: "" },
});
const form = ref({
  projectIds: [],
});
const projects = ref([]);
const customerData = ref({});

const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());

const receptionDoctorList = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("doctor")).map((i) => i.userid);
});
const { close: closeProjectModal, show: showProjectModal, visible: projectModalVisible, width: projectModalWidth } = useModal(800);
const projectPickerRef = ref();
const doctorIds = computed(() => props.doctorList.map((item) => item.value).filter(Boolean));
const steps = computed(() => {
  const list = [{ key: "step1", title: "分诊成功", checked: true, time: props.consultRecord.triageTimeStr, userid: props.consultRecord.triagePersonUserId, label: "分诊人" }];
  if (["visited", "treated"].includes(props.consultRecord.visitStatus)) {
    list.push({ key: "step2", title: VisitStatusObj[props.consultRecord.visitStatus], checked: true, time: props.consultRecord.receptionTimeStr, userid: props.consultRecord.receptionPersonUserId, label: "接诊人" });
    if (props.consultRecord.tradeAmount) {
      list.push({
        key: "step3",
        title: "已成交",
        checked: true,
        time: props.consultRecord.tradeTimeStr,
        label: "成交金额",
        text: props.consultRecord.tradeAmount ? `￥${props.consultRecord.tradeAmount}` : "",
      });
    } else {
      list.push({ key: "step3", title: "未成交", checked: false });
    }
    return list;
  }
  list.push({ key: "step2", title: "待接诊", checked: false, label: "接诊人" }, { key: "step3", title: "未成交", checked: false });
  return list;
});
const middleStatus = computed(() => {
  if (props.consultRecord.visitStatus === "visited") {
    return { success: true, text: "接诊成功", time: props.consultRecord.receptionTimeStr, userid: props.consultRecord.receptionPersonUserId };
  }
  if (props.consultRecord.visitStatus === "pending") {
    return { text: "待接诊" };
  }
});

function add() {
  if (isRead.value) return;
  showProjectModal();
}

function handleProjectChange(selectedProjects) {
  projects.value = selectedProjects.map((item) => ({
    _id: item.id,
    projectName: item.name,
    projectCateId: item.id,
  }));
}

function changeProjects(data) {
  projects.value = data;
}

function close() {
  emits("close");
}

function selectProject() {
  showProjectModal();
}

function remove(index) {
  projects.value.splice(index, 1);
}

function showCustomerDetail(customer) {
  emits("showCustomerDetail", customer._id);
}

// 获取客户信息
async function getCustomerInfo() {
  if (!props.consultRecord.customerId) return;
  try {
    const { success, data } = await getCustomerInfoById(localStorage.getItem("userId"), { _id: props.consultRecord.customerId });
    if (success && data.data && data.data[0]) {
      customerData.value = data.data[0];
    }
  } catch (error) {
    console.error("获取客户信息失败:", error);
  }
}

function changeDoctor(val) {
  form.value.interviewDoctor = val;
}

function customerReload() {
  emits("success");
}

const loading = ref(false);
async function confirm() {
  if (!form.value.customerComplaint) {
    ElMessage.warning("请输入客户主诉");
    return;
  }
  if (!Array.isArray(form.value.projectIds) || form.value.projectIds.length === 0) {
    ElMessage.warning("请选择意向项目");
    return;
  }
  let params = {
    customerComplaint: form.value.customerComplaint,
    dealPlan: form.value.dealPlan,
    noDealReason: form.value.noDealReason,
    receptionDoctorUserId: form.value.receptionDoctorUserId,
    receptionTime: dayjs().valueOf(),
    visitStatus: "visited",
    interviewDoctor: form.value.interviewDoctor,
    projectIds: form.value.projectIds,
    projects: projects.value.map((item) => ({ _id: item._id, projectCateId: item.projectCateId })),
  };
  const { success, message, data } = await updateConsultRecord({ id: props.consultRecord._id, params });
  if (success) {
    ElMessage.success("接诊成功");
    close();
    emits("success", data);
  } else {
    ElMessage.error(message);
  }
}
const isRead = ref(false);

async function setProjects() {
  const list = await projectPickerRef.value.id2Project(form.value.projectIds);
  const consultProjects = Array.isArray(props.consultRecord.projects) ? props.consultRecord.projects : [];
  projects.value = consultProjects
    .map((i) => {
      const project = list.find((j) => j._id === i._id && Array.isArray(j.projectCateIdGroup) && j.projectCateIdGroup.includes(i.projectCateId));
      return project ? { ...i, projectName: project.projectName } : null;
    })
    .filter(Boolean);
}

function changeReceptionDoctor(value) {
  form.value.receptionDoctorUserId = value;
}

watch(
  () => props.visible,
  (n) => {
    if (n) {
      getCustomerInfo();
      if (staffList.value.length === 0) getStaffList();
      isRead.value = props.consultRecord.visitStatus === "visited";
      if (isRead.value) {
        form.value.customerComplaint = props.consultRecord.customerComplaint;
        form.value.dealPlan = props.consultRecord.dealPlan;
        form.value.noDealReason = props.consultRecord.noDealReason;
        form.value.receptionDoctorUserId = props.consultRecord.receptionDoctorUserId;
      } else {
        form.value.customerComplaint = CustomerComplaintTemp;
        form.value.dealPlan = "";
        form.value.noDealReason = "";
        form.value.receptionDoctorUserId = "";
      }
      form.value.projectIds = Array.isArray(props.consultRecord.projectIds) ? props.consultRecord.projectIds : [];

      if (Array.isArray(props.consultRecord.projects) && props.consultRecord.projects.length > 0) {
        projects.value = props.consultRecord.projects;
      } else {
        projects.value = [];
      }
    }
  }
);
</script>