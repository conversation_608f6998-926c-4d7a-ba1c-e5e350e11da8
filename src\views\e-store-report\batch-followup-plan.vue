<template>
  <el-drawer v-model="dialogVisible" title="批量回访设置" direction="rtl" size="90%" :with-header="true" @close="handleClose">
    <div class="batch-followup-plan">
      <div class="plan-layout">
        <!-- 左侧：选择回访人员和回访内容 -->
        <div class="left-panel">
          <!-- 选择回访人员 -->
          <div class="staff-section">
            <div class="section-header">
              <span class="section-title">选择回访人员</span>
              <el-button type="primary" size="small" @click="addStaffMember">新增回访人员</el-button>
            </div>

            <div class="staff-selection">
              <div class="staff-list">
                <div class="staff-list-container">
                  <div v-for="(member, memberIndex) in staffMembers" :key="memberIndex" class="member-item">
                    <div class="member-info">
                      <ww-user v-if="member.userId" :openid="member.userId" />
                      <span v-if="member.teamName" class="team-name">{{ member.teamName }}</span>
                    </div>
                    <div class="member-actions">
                      <el-button type="primary" text size="small" @click="editMember(memberIndex)">编辑</el-button>
                      <el-button type="danger" text size="small" @click="removeMember(memberIndex)">删除</el-button>
                    </div>
                  </div>
                  <div v-if="staffMembers.length === 0" class="empty-state">
                    <span class="empty-text">暂无回访人员，请点击"新增回访人员"添加</span>
                  </div>
                </div>
              </div>

              <div class="visit-period">
                <div class="period-inputs">
                  <span>*最早起始日期：</span>
                  <el-date-picker v-model="startDate" type="date" size="small" style="width: 120px" />
                  <span>*最迟起始日期：</span>
                  <el-date-picker v-model="endDate" type="date" size="small" style="width: 120px" />
                </div>
              </div>
            </div>
          </div>

          <!-- 回访内容设置 -->
          <div class="content-section">
            <div class="section-header">
              <span class="section-title">回访内容</span>
              <div class="header-actions">
                <el-button v-if="!selectedTemplate" size="small" type="primary" @click="showTemplateModal">使用模板</el-button>
                <el-button v-if="selectedTemplate" size="small" type="danger" @click="clearTemplate">删除模板</el-button>
              </div>
            </div>

            <div class="content-form">
              <!-- 模板内容展示区域 -->
              <div v-if="selectedTemplate" class="template-content">
                <!-- 模板名称 -->
                <div class="template-header">
                  <div class="template-name">模板名称: {{ selectedTemplate.planName || selectedTemplate.name }}</div>
                </div>

                <el-timeline v-if="selectedTemplate.taskList && selectedTemplate.taskList.length" class="ml-3px compact-timeline">
                  <el-timeline-item v-for="(task, index) in selectedTemplate.taskList" :key="index" color="#006eff" :timestamp="task.key" hollow placement="top" :hide-timestamp="true">
                    <div class="mt-10px">
                      <div class="task_cell pt-6px">
                        <div v-if="task.eventType && ToDoEventType[task.eventType]" border-bottom class="px-10px">
                          <div class="mb-6px text-14px">
                            <span grew-color>类型:</span>
                            {{ ToDoEventType[task.eventType] }}
                          </div>
                        </div>
                        <div border-bottom class="px-10px">
                          <div class="my-6px text-14px">
                            <span grew-color>执行时间:</span>
                            {{ formatTaskTime(task) }}
                          </div>
                        </div>
                        <div border-bottom class="px-10px">
                          <div grew-color>任务内容:</div>
                          <el-scrollbar wrap-style="max-height:50px">
                            <div class="py-6px">{{ task.taskContent || "未设置" }}</div>
                          </el-scrollbar>
                        </div>
                        <div border-bottom class="px-10px pb-10px">
                          <div grew-color class="py-6px">向联系人发送:</div>
                          <el-scrollbar v-if="task.sendContent" wrap-style="max-height:50px">
                            <div class="pb-6px text-12px leading-16px">
                              <span grew-color>[提醒]</span>
                              {{ task.sendContent }}
                            </div>
                          </el-scrollbar>
                          <div v-if="!task.sendContent && (!task.pannedEventSendFile || !task.pannedEventSendFile.name)" class="pb-6px">暂无发送内容</div>
                          <div class="pb-6px" v-if="Object.keys(task.pannedEventSendFile || {}).length !== 0 && task.pannedEventSendFile.name">
                            <span grew-color>[{{ task.pannedEventSendFile.type === "article" ? "文章" : "问卷" }}]</span>
                            <span main-color class="pointer">
                              {{ task.pannedEventSendFile.name }}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </el-timeline-item>
                  <el-timeline-item placement="top" color="#006eff" hollow timestamp="结束"></el-timeline-item>
                </el-timeline>
                <div v-else class="empty-template">
                  <div class="text-center text-gray-500 py-20px">该模板暂无任务内容</div>
                </div>
              </div>

              <!-- 新增内容表单 -->
              <el-form v-else :model="taskForm" size="small" label-width="90px">
                <el-form-item label="*类型：">
                  <el-select v-model="taskForm.eventType" size="small" style="width: 150px" placeholder="请选择类型">
                    <el-option v-for="type in eventTypeOptions" :key="type.value" :label="type.label" :value="type.value" />
                  </el-select>
                </el-form-item>

                <el-form-item label="*跟进方式：">
                  <el-radio-group v-model="taskForm.executeMethod" size="small" class="task-method-radio" @change="changeMethod">
                    <el-radio label="todo">
                      <div class="radio-content">
                        <div class="pl-9px">生成待办单</div>
                        <div class="radio-subtitle">（员工手动处理）</div>
                      </div>
                    </el-radio>
                    <el-radio label="groupTask">
                      <div class="radio-content">
                        <div class="pl-9px">生成群发单</div>
                        <div class="radio-subtitle">（员工批量发送）</div>
                      </div>
                    </el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item label="任务内容：">
                  <el-input v-model="taskForm.taskContent" type="textarea" :rows="3" placeholder="请输入任务内容" maxlength="500" show-word-limit size="small" />
                </el-form-item>

                <el-form-item label="发送内容：">
                  <el-input v-model="taskForm.sendContent" type="textarea" :rows="3" placeholder="请输入发送内容" maxlength="500" show-word-limit size="small" />
                </el-form-item>
              </el-form>
            </div>
          </div>
        </div>

        <!-- 右侧：客户列表 -->
        <div class="right-panel">
          <div class="customer-section">
            <div class="section-header">
              <div class="customer-mode">
                <el-radio-group v-model="customerMode" size="small">
                  <el-radio label="select">按指定的人员轮询分配</el-radio>
                  <el-radio label="average">按指定的人员平均分配</el-radio>
                </el-radio-group>
              </div>
              <div class="customer-count">选择客户数量：{{ selectedCustomers.length }}/{{ sortedCustomerList.length }}</div>
            </div>
            <div class="customer-table">
              <el-table ref="customerTableRef" :data="sortedCustomerList" size="small" style="width: 100%; height: 100%" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="40" />
                <el-table-column prop="name" label="姓名" width="80" />
                <el-table-column prop="gender" label="性别" width="60">
                  <template #default="{ row }">
                    <span>{{ row.sex }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="age" label="年龄" width="60">
                  <template #default="{ row }">
                    <span>{{ row.age }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="createTime" label="登记日期" width="100">
                  <template #default="{ row }">
                    <span>{{ getCreateDate(row.createTime) }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="lastVisitTime" label="最近来院" width="100">
                  <template #default="{ row }">
                    <span>{{ getLastVisitTime(row.visitTimes) }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="lastPlanContent" label="最近计划回访内容" width="150" class-name="text-wrap-cell">
                  <template #default="{ row }">
                    <div class="text-wrap">{{ getLastPlanContent(row.plans) }}</div>
                  </template>
                </el-table-column>
                <el-table-column prop="lastPlanDate" label="最近计划回访日期" width="120">
                  <template #default="{ row }">
                    <span>{{ getLastPlanDate(row.plans) }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <div class="table-actions">
              <el-button size="small" @click="selectAllCustomers">全选</el-button>
              <el-button size="small" @click="invertSelection">反选</el-button>
              <el-button size="small" @click="clearSelection">清空</el-button>
              <el-button size="small" @click="deleteSelectedCustomers" :disabled="selectedCustomers.length === 0">删除</el-button>
              <el-button type="primary" size="small" :loading="generatingStatus" @click="generatePreview">
                {{ generatingStatus ? "计划生成中..." : "生成预览" }}
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部：生成回访计划预览表 -->
      <div class="preview-section">
        <div class="preview-header">
          <span class="preview-title">回访计划预览表</span>
          <div v-if="previewList.length > 0" class="preview-stats">
            <el-tag size="small" type="info">
              分配模式: {{ customerMode === 'select' ? '轮询分配' : '平均分配' }}
            </el-tag>
            <el-tag size="small" type="success">
              任务数量: {{ previewList.length }}
            </el-tag>
            <el-tag size="small" type="info">
              客户数量: {{ uniqueCustomerCount }}
            </el-tag>
            <el-tag size="small" type="warning">
              执行人员: {{ staffMembers.length }}
            </el-tag>
            <el-tag size="small" type="primary">
              时间区间: {{ startDate ? dayjs(startDate).format('MM-DD') : '' }} 至 {{ endDate ? dayjs(endDate).format('MM-DD') : '' }}
            </el-tag>
          </div>
        </div>

        <div class="preview-table">
          <el-table :data="previewList" size="small" style="width: 100%; height: 100%">
            <el-table-column type="index" label="序号" width="60" />
            <el-table-column prop="customerName" label="客户姓名" width="100" />
            <el-table-column prop="createTime" label="创建时间" width="120">
              <template #default="{ row }">
                <span>{{ getCreateDate(row.createTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="planName" label="计划名称" width="150" class-name="text-wrap-cell">
              <template #default="{ row }">
                <div class="text-wrap">
                  {{ row.planName }}
                  <span v-if="row.taskIndex !== undefined" class="task-index">(任务{{ row.taskIndex + 1 }})</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="eventType" label="事件类型" width="120">
              <template #default="{ row }">
                <span>{{ ToDoEventType[row.eventType] || row.eventType }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="executeMethod" label="执行方式" width="100">
              <template #default="{ row }">
                <span>{{ row.executeMethod === 'todo' ? '待办单' : '群发单' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="executeTeamName" label="执行团队" width="120" />
            <el-table-column prop="executorUserId" label="执行人" width="100">
              <template #default="{ row }">
                <ww-user v-if="row.executorUserId" :openid="row.executorUserId" />
              </template>
            </el-table-column>
            <el-table-column prop="plannedExecutionTime" label="计划执行时间" width="120">
              <template #default="{ row }">
                <span>{{ getCreateDate(row.plannedExecutionTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="endTime" label="结束时间" width="120">
              <template #default="{ row }">
                <span>{{ getCreateDate(row.endTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="eventStatus" label="事件状态" width="100">
              <template #default="{ row }">
                <span :class="getStatusClass(row.eventStatus)">{{ getStatusText(row.eventStatus) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="result" label="结果" width="100" />
            <el-table-column prop="taskContent" label="任务内容" width="200" class-name="text-wrap-cell">
              <template #default="{ row }">
                <div class="text-wrap">{{ row.taskContent }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="sendContent" label="发送内容" width="150" class-name="text-wrap-cell">
              <template #default="{ row }">
                <div class="text-wrap">{{ row.sendContent || '无' }}</div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="preview-actions">
          <el-button size="small" @click="exportPreview" :disabled="previewList.length === 0">导出</el-button>
          <el-button type="primary" size="small" :loading="loading" @click="confirmGenerate" :disabled="previewList.length === 0">生成</el-button>
        </div>
      </div>
    </div>
  </el-drawer>

  <!-- 人员选择弹窗 -->
  <el-dialog v-model="memberPickerVisible" title="选择回访人员" width="600px" @close="closeMemberPicker">
    <div class="member-picker-content">
      <el-form-item label="回访人员">
        <el-select v-model="selectedMember" ref="memberSelectRef" popper-class="member-picker-select-popover" placeholder="请选择回访人员" class="w-full member-picker-select" clearable @change="changeMemberAndConfirm" @visible-change="visibleChange">
          <template #prefix>
            <div class="truncate h-30px leading-30px text-left" :style="{ width: memberPrefixWidth, color: 'rgb(96, 98, 102)' }">
              <ww-user v-if="currentMember.userId" :openid="currentMember.userId" />
              <span class="ml-10px">{{ currentMember.teamName }}</span>
            </div>
          </template>
          <el-option v-for="item in compactMemberOptions" :key="item.value" :value="item.value">
            <div class="compact-member-option">
              <ww-user :openid="item.userId" />
              <span class="member-name">{{ item.staff?.anotherName || item.staff?.name || item.userId }}</span>
              <span class="team-name">{{ item.teamName }}</span>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
    </div>
    <template #footer>
      <div class="text-center">
        <el-button @click="closeMemberPicker">取消</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 搜索输入框 -->
  <teleport v-if="showMemberSearchInput" to=".member-picker-select-popover .teleport-container">
    <div class="px-20px pt-20px pb-5px">
      <el-input clearable v-model="memberKeyword" placeholder="搜索..." />
    </div>
  </teleport>

  <!-- 任务抽屉 -->
  <task-drawer :visible="taskDrawerVisible" :type="taskDrawerType" :teamId="teamId" :data="currentTask" :customer="customer" :selectCustomers="customerList" :memberList="memberList" @close="closeTaskDrawer" @change="handleTaskChange" />

  <!-- 新建计划抽屉 -->
  <new-plan-drawer :visible="newPlanDrawerVisible" :type="taskDrawerType" :plan="currentPlan" :customer="customer" :teamId="teamId" :memberList="memberList" :selectCustomers="customerList" ref="newPlanDrawerRef" @close="closeNewPlanDrawer" @change="handlePlanChange" @onEdit="editTask" />

  <!-- 团队计划抽屉 -->
  <team-plan-drawer :visible="teamPlanDrawerVisible" :type="taskDrawerType" :teamId="teamId" @close="closeTeamPlanDrawer" @onSelect="selectPlan" />
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from "vue";
import { storeToRefs } from "pinia";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import { Plus, QuestionFilled } from "@element-plus/icons-vue";
import dayjs from "dayjs";
import { useElementBounding } from "@vueuse/core";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { getCustomTeamData } from "@/api/corp";
import { executeManagementPlanTodo } from "@/api/todo";
import { maskPhone, getRandomStr } from "@/utils";
import { ToDoEventType } from "@/baseData";
import { memberStore } from "@/store/member";
import { teamStore } from "@/store/team";
import { staffStore } from "@/store/staff";
import WwUser from "@/components/ww-user/index.vue";
import DeptSelect from "@/components/dept-components/dept-select.vue";
import DeptNameTransformer from "@/components/dept-components/dept-name-transformer.vue";
import TaskDrawer from "@/components/manage-plan-drawer/task-drawer.vue";
import NewPlanDrawer from "@/components/manage-plan-drawer/new-plan-drawer.vue";
import TeamPlanDrawer from "@/components/manage-plan-drawer/team-plan-drawer.vue";

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  customerList: {
    type: Array,
    default: () => [],
  },
  teamId: {
    type: String,
    default: "",
  },
});

// Emits
const emit = defineEmits(["close", "success", "delete-customers"]);

// Store
const { memberInfo } = storeToRefs(memberStore());
const { allTeams } = storeToRefs(teamStore());
const { staffList } = storeToRefs(staffStore());
const { getStaffList } = staffStore();

// Data
const loading = ref(false);
const generatingStatus = ref(false);
const assignmentMode = ref("person");
const customerMode = ref("select");
const selectedCustomers = ref([]);
const selectedStaff = ref([]);
const startDate = ref("");
const endDate = ref("");
const previewList = ref([]);
const customerTableRef = ref(null);

// 模板相关
const selectedTemplate = ref(null);

// 回访人员组相关
const staffMembers = ref([]);
const memberPickerVisible = ref(false);
const selectedMember = ref("");
const currentMember = ref({ userId: "", teamName: "" });
const editingMemberIndex = ref(-1);

// 人员选择相关
const memberSelectRef = ref(null);
const showMemberSearchInput = ref(false);
const memberKeyword = ref("");

// 任务相关
const taskDrawerVisible = ref(false);
const taskDrawerType = ref("");
const currentTask = ref({});
const newPlanDrawerVisible = ref(false);
const currentPlan = ref({});
const teamPlanDrawerVisible = ref(false);
const memberList = ref([]);
const newPlanDrawerRef = ref(null);
const customer = ref({}); // 客户对象

const taskForm = ref({
  planExecutionTime: "",
  eventType: "",
  taskContent: "",
  executeMethod: "todo",
  sendContent: "",
});

// Computed
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => {
    if (!value) {
      emit("close");
    }
  },
});

// 根据createTime排序的客户列表
const sortedCustomerList = computed(() => {
  if (!props.customerList || !Array.isArray(props.customerList)) {
    return [];
  }

  return [...props.customerList].sort((a, b) => {
    const timeA = a.createTime || a.reportTimeStamp || 0;
    const timeB = b.createTime || b.reportTimeStamp || 0;

    // 从早到晚排序（升序）
    return new Date(timeA) - new Date(timeB);
  });
});

const displayStaffList = computed(() => {
  return staffList.value.map((staff) => ({
    userid: staff.userid,
    name: staff.name,
    position: staff.position || "员工",
    teamName: staff.teamName || "默认团队",
  }));
});

const eventTypeOptions = computed(() => {
  return Object.keys(ToDoEventType).map((key) => ({
    value: key,
    label: ToDoEventType[key],
  }));
});

// 计算唯一客户数量
const uniqueCustomerCount = computed(() => {
  if (!previewList.value || previewList.value.length === 0) {
    return 0;
  }
  
  const uniqueCustomers = new Set();
  previewList.value.forEach(item => {
    uniqueCustomers.add(item.customerId);
  });
  
  return uniqueCustomers.size;
});

// 人员选择相关计算属性
const staffMap = computed(() =>
  staffList.value.reduce((acc, cur) => {
    acc[cur.userid] = cur;
    return acc;
  }, {})
);

const memberOptions = computed(() => {
  const data = allTeams.value.reduce((acc, cur) => {
    const memberList = Array.isArray(cur.memberList) ? cur.memberList : [];
    const group = { teamId: cur.teamId, name: cur.name, list: [] };
    group.list = memberList
      .map((i) => ({
        value: `${cur.teamId}||${i}`,
        userId: i,
        teamId: cur.teamId,
        name: cur.name,
        staff: staffMap.value[i],
      }))
      .filter((i) => i.staff && Array.isArray(i.staff.job) && i.staff.job.includes("onlineCustomerService"));
    if (group.list.length) return [...acc, group];
    return acc;
  }, []);
  return data;
});

const memberFilterOptions = computed(() => {
  const name = memberKeyword.value.trim();
  if (name === "") return memberOptions.value;
  return memberOptions.value
    .map((i) => {
      const { list, ...item } = i;
      return { ...item, list: list.filter((j) => j.staff && typeof j.staff.anotherName === "string" && j.staff.anotherName.includes(name)) };
    })
    .filter((i) => i.list.length);
});

const compactMemberOptions = computed(() => {
  const name = memberKeyword.value.trim();
  let allMembers = [];

  // 将所有团队成员扁平化到一个数组中
  memberOptions.value.forEach((group) => {
    group.list.forEach((member) => {
      allMembers.push({
        ...member,
        teamName: group.name,
      });
    });
  });

  // 如果有搜索关键词，进行过滤
  if (name) {
    allMembers = allMembers.filter((member) => member.staff && typeof member.staff.anotherName === "string" && member.staff.anotherName.includes(name));
  }

  return allMembers;
});

const { width: memberSelectWidth } = useElementBounding(memberSelectRef);
const memberPrefixWidth = computed(() => `${memberSelectWidth.value - 40}px`);

// Methods
const handleAssignmentModeChange = (value) => {
  assignmentMode.value = value;
};

const handleSelectionChange = (selection) => {
  selectedCustomers.value = selection;
};

const handleClose = () => {
  emit("close");
};

// 辅助函数
function getConsumeStatus(arr = []) {
  if (arr.length === 0) {
    return "未消费";
  } else if (arr.length === 1) {
    return "已消费";
  } else {
    return "多次消费";
  }
}

// 计算任务执行时间
function calculateTaskExecutionTime(baseExecutionTime, timeType, taskTime) {
  if (!taskTime && taskTime !== 0) {
    return baseExecutionTime; // 如果没有设置时间，使用基础执行时间
  }

  const baseTime = dayjs(baseExecutionTime);
  
  switch (timeType) {
    case "day":
      return baseTime.add(taskTime, 'day').valueOf();
    case "week":
      return baseTime.add(taskTime, 'week').valueOf();
    case "month":
      return baseTime.add(taskTime, 'month').valueOf();
    case "year":
      return baseTime.add(taskTime, 'year').valueOf();
    default:
      return baseTime.add(taskTime, 'day').valueOf();
  }
}

// 格式化任务时间显示
function formatTaskTime(task) {
  if (!task.taskTime && task.taskTime !== 0) {
    return "当天";
  }

  const timeType = task.timeType || "day";
  const timeValue = task.taskTime;

  let timeText = "";
  switch (timeType) {
    case "day":
      timeText = timeValue === 0 ? "当天" : `${timeValue}天`;
      break;
    case "week":
      timeText = `${timeValue}周`;
      break;
    case "month":
      timeText = `${timeValue}月`;
      break;
    case "year":
      timeText = `${timeValue}年`;
      break;
    default:
      timeText = `${timeValue}天`;
  }

  return `计划开始后${timeText}`;
}

// 获取性别显示
function getGender(gender) {
  if (!gender) return "未知";
  return gender === 1 ? "男" : gender === 2 ? "女" : "未知";
}

// 获取年龄显示
function getAge(birthday) {
  if (!birthday) return "未知";
  try {
    const birthDate = dayjs(birthday);
    const age = dayjs().diff(birthDate, "year");
    return age > 0 ? `${age}岁` : "未知";
  } catch (error) {
    return "未知";
  }
}

// 获取建档日期
function getCreateDate(createTime) {
  if (!createTime) return "";
  return dayjs(createTime).format("YYYY-MM-DD");
}

// 获取最近来院时间
function getLastVisitTime(visitTimes) {
  if (!visitTimes || !Array.isArray(visitTimes) || visitTimes.length === 0) {
    return "未到院";
  }

  // 假设visitTimes是按时间排序的，取最新的
  const lastVisit = visitTimes[visitTimes.length - 1];
  if (lastVisit && lastVisit.visitTime) {
    return dayjs(lastVisit.visitTime).format("YYYY-MM-DD");
  }

  return "未到院";
}

// 获取最近计划回访内容
function getLastPlanContent(plans) {
  if (!plans || !Array.isArray(plans) || plans.length === 0) {
    return "无";
  }

  // 假设plans是按时间排序的，取最新的
  const lastPlan = plans[plans.length - 1];
  if (lastPlan && lastPlan.content) {
    return lastPlan.content.length > 20 ? lastPlan.content.substring(0, 20) + "..." : lastPlan.content;
  }

  return "无";
}

// 获取最近计划回访日期
function getLastPlanDate(plans) {
  if (!plans || !Array.isArray(plans) || plans.length === 0) {
    return "";
  }

  // 假设plans是按时间排序的，取最新的
  const lastPlan = plans[plans.length - 1];
  if (lastPlan && lastPlan.planDate) {
    return dayjs(lastPlan.planDate).format("YYYY-MM-DD");
  }

  return "";
}

function isInHospital(inHospitalTimes) {
  return inHospitalTimes && Array.isArray(inHospitalTimes) && inHospitalTimes.length > 0;
}

function getSource(customer) {
  return customer && Array.isArray(customer.customerSource) && customer.customerSource.length ? customer.customerSource.join(" / ") : "";
}

function getInfoSource(customer) {
  return customer && Array.isArray(customer.infoSource) && customer.infoSource.length ? customer.infoSource.join(" 、 ") : "";
}

// 获取状态文本
function getStatusText(status) {
  const statusMap = {
    'pending': '待处理',
    'processing': '处理中',
    'completed': '已完成',
    'expire': '已过期',
    'cancelled': '已取消'
  };
  return statusMap[status] || status || '未知';
}

// 获取状态样式类
function getStatusClass(status) {
  const classMap = {
    'pending': 'text-orange-600',
    'processing': 'text-blue-600',
    'completed': 'text-green-600',
    'expire': 'text-red-600',
    'cancelled': 'text-gray-600'
  };
  return classMap[status] || 'text-gray-600';
}

// 回访人员组管理
const addStaffMember = () => {
  editingMemberIndex.value = -1; // 新增模式
  currentMember.value = { userId: "", teamName: "" };
  selectedMember.value = "";
  memberPickerVisible.value = true;
};

const removeStaffMember = (memberIndex) => {
  ElMessageBox.confirm("确定要删除这个成员吗？", "确认删除", {
    type: "warning",
    confirmButtonText: "确定",
    cancelButtonText: "取消",
  })
    .then(() => {
      staffMembers.value.splice(memberIndex, 1);
      ElMessage.success("删除成功");
    })
    .catch(() => {
      // 用户取消删除
    });
};

const editMember = (memberIndex) => {
  editingMemberIndex.value = memberIndex;

  const member = staffMembers.value[memberIndex];
  currentMember.value = { ...member };
  selectedMember.value = member.teamId && member.userId ? `${member.teamId}||${member.userId}` : "";

  memberPickerVisible.value = true;
};

const removeMember = (memberIndex) => {
  ElMessageBox.confirm("确定要删除这个成员吗？", "确认删除", {
    type: "warning",
    confirmButtonText: "确定",
    cancelButtonText: "取消",
  })
    .then(() => {
      staffMembers.value.splice(memberIndex, 1);
      ElMessage.success("删除成功");
    })
    .catch(() => {
      // 用户取消删除
    });
};

// 人员选择相关方法
const changeMember = (val) => {
  const [teamId = "", userId = ""] = val.split("||");
  currentMember.value.userId = userId;
  currentMember.value.teamId = teamId;

  const team = allTeams.value.find((i) => i.teamId === teamId);
  currentMember.value.teamName = team ? team.name : "";
};

const changeMemberAndConfirm = (val) => {
  if (!val) return; // 如果没有选择值，直接返回

  changeMember(val);

  // 延迟一下确保数据更新完成
  nextTick(() => {
    if (currentMember.value.userId) {
      confirmMemberSelection();
    }
  });
};

const closeMemberPicker = () => {
  memberPickerVisible.value = false;
  editingMemberIndex.value = -1;
  selectedMember.value = "";
  currentMember.value = { userId: "", teamName: "" };
  memberKeyword.value = "";
};

const confirmMemberSelection = () => {
  if (editingMemberIndex.value >= 0) {
    // 编辑现有成员
    staffMembers.value[editingMemberIndex.value] = { ...currentMember.value };
  } else {
    // 添加新成员
    staffMembers.value.push({ ...currentMember.value });
  }

  closeMemberPicker();
  ElMessage.success("操作成功");
};

const visibleChange = async (val) => {
  if (!val) return;
  await nextTick();
  const popover = document.querySelector(".member-picker-select-popover");
  const el = document.querySelector(".member-picker-select-popover .teleport-container");
  if (popover && !el) {
    const el = document.createElement("div");
    el.classList.add("teleport-container");
    popover.prepend(el);
  }
  await nextTick();
  showMemberSearchInput.value = val;
};

const showAddStaff = () => {
  ElMessage.info("新增员工功能");
};

const showTemplateModal = () => {
  taskDrawerType.value = "addBatchTask";
  teamPlanDrawerVisible.value = true;
};

// 选择模板后的处理
const selectPlan = (plan) => {
  console.log("选择的模板:", plan);

  // 关闭模板选择抽屉
  teamPlanDrawerVisible.value = false;

  // 设置选中的模板，完全带入模板内容
  if (plan.taskList && Array.isArray(plan.taskList) && plan.taskList.length > 0) {
    // 将所有任务内容都带入
    selectedTemplate.value = {
      ...plan,
      taskList: plan.taskList.map((task, index) => ({
        ...task,
        taskId: task.taskId || `task_${index}`,
        planExecutionTime: dayjs()
          .add(task.taskTime || 0, task.timeType || "day")
          .format("YYYY-MM-DD"),
      })),
    };
  } else {
    selectedTemplate.value = { ...plan };
  }

  console.log("处理后的模板:", selectedTemplate.value);
};

// 跟进方式改变时的处理
const changeMethod = (val) => {
  if (val === "groupTask") {
    // 群发任务时，发送内容为必填
    taskForm.value.sendContent = taskForm.value.sendContent || "";
  }
};

// 日期禁用逻辑
const disabledDate = (date) => {
  const startDate = dayjs().subtract(6, "days").startOf("day");
  return dayjs(date).isBefore(startDate, "day");
};

// 关闭模板选择抽屉
const closeTeamPlanDrawer = () => {
  teamPlanDrawerVisible.value = false;
};

// 关闭新建计划抽屉
const closeNewPlanDrawer = () => {
  currentPlan.value = {};
  newPlanDrawerVisible.value = false;
};

// 处理计划变更
const handlePlanChange = () => {
  // 这里可以处理计划变更后的逻辑
  console.log("计划已变更");
};

// 编辑任务
const editTask = (row = {}) => {
  console.log("编辑任务:", row);
  // 这里可以处理编辑任务的逻辑
};

// 关闭任务抽屉
const closeTaskDrawer = () => {
  taskDrawerVisible.value = false;
};

// 处理任务变更
const handleTaskChange = (row) => {
  console.log("任务已变更:", row);
  // 这里可以处理任务变更后的逻辑
};

// 清除模板内容，回到新增模式
const clearTemplate = () => {
  selectedTemplate.value = null;
  // 重置表单
  taskForm.value = {
    planExecutionTime: "",
    eventType: "",
    taskContent: "",
    executeMethod: "todo",
    sendContent: "",
  };
  ElMessage.success("已清除模板内容");
};

// 全选客户
const selectAllCustomers = () => {
  if (customerTableRef.value) {
    customerTableRef.value.clearSelection();
    sortedCustomerList.value.forEach((row) => {
      customerTableRef.value.toggleRowSelection(row, true);
    });
  }
};

// 反选客户
const invertSelection = () => {
  if (customerTableRef.value) {
    const currentSelection = selectedCustomers.value.map((item) => item);
    customerTableRef.value.clearSelection();

    sortedCustomerList.value.forEach((row) => {
      const isSelected = currentSelection.some((selected) => selected === row);
      if (!isSelected) {
        customerTableRef.value.toggleRowSelection(row, true);
      }
    });
  }
};

// 清空选择
const clearSelection = () => {
  if (customerTableRef.value) {
    customerTableRef.value.clearSelection();
  }
};

// 删除选中的客户
const deleteSelectedCustomers = () => {
  if (selectedCustomers.value.length === 0) {
    ElMessage.warning("请选择要删除的客户");
    return;
  }

  ElMessageBox.confirm(`确定要删除选中的 ${selectedCustomers.value.length} 位客户吗？`, "确认删除", {
    type: "warning",
    confirmButtonText: "确定",
    cancelButtonText: "取消",
  })
    .then(() => {
      // 这里需要根据实际的删除逻辑来实现
      // 如果是从props.customerList中删除，需要触发父组件的删除事件
      emit("delete-customers", selectedCustomers.value);
      ElMessage.success("删除成功");

      // 清空选择
      clearSelection();
    })
    .catch(() => {
      // 用户取消删除
    });
};

const generatePreview = async () => {
  if (selectedCustomers.value.length === 0) {
    ElMessage.warning("请选择客户");
    return;
  }

  if (staffMembers.value.length === 0) {
    ElMessage.warning("请添加回访人员");
    return;
  }

  // 检查是否有模板或表单内容
  const hasTemplateContent = selectedTemplate.value?.taskList && selectedTemplate.value.taskList.length > 0;
  const hasFormContent = taskForm.value.eventType;

  if (!hasTemplateContent && !hasFormContent) {
    ElMessage.warning("请选择模板或填写回访内容");
    return;
  }

  // 检查时间区间
  if (!startDate.value || !endDate.value) {
    ElMessage.warning("请设置时间区间");
    return;
  }

  generatingStatus.value = true;

  try {
    // 模拟生成预览数据
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // 计算时间区间内的天数
    const startDay = dayjs(startDate.value);
    const endDay = dayjs(endDate.value);
    const totalDays = endDay.diff(startDay, 'day') + 1;

    // 生成分配方案
    const assignmentPlan = generateAssignmentPlan(
      selectedCustomers.value.length,
      staffMembers.value,
      totalDays,
      customerMode.value
    );

    // 调试信息
    console.log('分配方案:', {
      customerCount: selectedCustomers.value.length,
      staffCount: staffMembers.value.length,
      totalDays,
      mode: customerMode.value,
      assignmentPlan: assignmentPlan.slice(0, 10) // 只显示前10个
    });

    // 统计分配结果
    const assignmentStats = {};
    assignmentPlan.forEach((assignment, index) => {
      const staffId = assignment.executorUserId;
      const executionDate = dayjs(assignment.executionTime).format('YYYY-MM-DD');
      
      if (!assignmentStats[staffId]) {
        assignmentStats[staffId] = {};
      }
      if (!assignmentStats[staffId][executionDate]) {
        assignmentStats[staffId][executionDate] = 0;
      }
      assignmentStats[staffId][executionDate]++;
    });

    console.log('分配统计:', assignmentStats);

    // 根据模板或表单内容生成预览列表
    if (selectedTemplate.value?.taskList && selectedTemplate.value.taskList.length > 0) {
      // 使用模板内容，为每个客户生成多个任务
      previewList.value = [];
      
      selectedCustomers.value.forEach((customer, customerIndex) => {
        const assignment = assignmentPlan[customerIndex];
        const basePlanName = selectedTemplate.value.planName || `回访计划_${customer.name || customer.customerName || '客户'}_${dayjs().format('YYYYMMDD')}`;
        
        // 为每个任务生成预览项
        selectedTemplate.value.taskList.forEach((task, taskIndex) => {
          // 计算任务执行时间
          const taskExecutionTime = calculateTaskExecutionTime(
            assignment.executionTime,
            task.timeType || 'day',
            task.taskTime || 0
          );
          
          // 生成任务名称
          const taskName = `${basePlanName}_任务${taskIndex + 1}`;
          
          previewList.value.push({
            customerName: customer.name || customer.customerName || '未知客户',
            createTime: Date.now(),
            planName: taskName,
            eventType: task.eventType,
            executeMethod: task.executeMethod || 'todo',
            executeTeamName: assignment.teamName,
            executorUserId: assignment.executorUserId,
            plannedExecutionTime: taskExecutionTime,
            endTime: dayjs(taskExecutionTime).add(7, 'day').valueOf(),
            eventStatus: 'pending',
            result: '',
            taskContent: task.taskContent || '未设置',
            sendContent: task.sendContent || '',
            creatorUserId: memberInfo.value?.userid || '',
            customerId: customer.customerId || customer.id || '',
            planId: `plan_${Date.now()}_${customerIndex}_${taskIndex}`,
            taskId: `task_${Date.now()}_${customerIndex}_${taskIndex}`,
            corpId: props.teamId || '',
            enableSend: false,
            expireTime: dayjs(taskExecutionTime).add(7, 'day').valueOf(),
            fileList: [],
            isFeedback: false,
            memberPlanId: '',
            pannedEventId: '',
            pannedEventSendFile: task.pannedEventSendFile || {},
            _id: `todo_${Date.now()}_${customerIndex}_${taskIndex}`,
            _type: 'plan-task-todo',
            // 添加任务相关信息
            taskIndex: taskIndex,
            taskTime: task.taskTime,
            timeType: task.timeType,
            originalTask: task
          });
        });
      });
    } else {
      // 使用表单内容，为每个客户生成单个任务
      previewList.value = selectedCustomers.value.map((customer, index) => {
        const assignment = assignmentPlan[index];
        
        // 生成计划名称
        const planName = `回访计划_${customer.name || customer.customerName || '客户'}_${dayjs().format('YYYYMMDD')}`;

        return {
          customerName: customer.name || customer.customerName || '未知客户',
          createTime: Date.now(),
          planName: planName,
          eventType: taskForm.value.eventType,
          executeMethod: taskForm.value.executeMethod || 'todo',
          executeTeamName: assignment.teamName,
          executorUserId: assignment.executorUserId,
          plannedExecutionTime: assignment.executionTime,
          endTime: dayjs(assignment.executionTime).add(7, 'day').valueOf(),
          eventStatus: 'pending',
          result: '',
          taskContent: taskForm.value.taskContent || '未设置',
          sendContent: taskForm.value.sendContent || '',
          creatorUserId: memberInfo.value?.userid || '',
          customerId: customer.customerId || customer.id || '',
          planId: `plan_${Date.now()}_${index}`,
          taskId: `task_${Date.now()}_${index}`,
          corpId: props.teamId || '',
          enableSend: false,
          expireTime: dayjs(assignment.executionTime).add(7, 'day').valueOf(),
          fileList: [],
          isFeedback: false,
          memberPlanId: '',
          pannedEventId: '',
          pannedEventSendFile: {},
          _id: `todo_${Date.now()}_${index}`,
          _type: 'plan-task-todo'
        };
      });
    }

    ElMessage.success("预览生成成功");
  } catch (error) {
    ElMessage.error("预览生成失败");
  } finally {
    generatingStatus.value = false;
  }
};

// 生成分配方案
const generateAssignmentPlan = (customerCount, staffMembers, totalDays, mode) => {
  const plan = [];
  const startDay = dayjs(startDate.value);
  
  if (mode === "select") {
    // 轮询分配：一个一个平均分配给员工
    // 例如：100个客户，2个员工，5天时间区间
    // 分配结果：客户1->员工A第1天，客户2->员工B第1天，客户3->员工A第2天...
    for (let i = 0; i < customerCount; i++) {
      const staffIndex = i % staffMembers.length;
      const dayIndex = Math.floor(i / staffMembers.length) % totalDays;
      const staff = staffMembers[staffIndex];
      
      plan.push({
        executorUserId: staff.userId,
        teamName: staff.teamName,
        executionTime: startDay.add(dayIndex, 'day').valueOf()
      });
    }
  } else {
    // 平均分配：按人员维度平均分配
    // 例如：100个客户，2个员工，5天时间区间
    // 分配结果：前50个客户分配给员工A，后50个客户分配给员工B
    // 每个员工的任务在时间区间内平均分配
    const customersPerStaff = Math.ceil(customerCount / staffMembers.length);
    
    for (let i = 0; i < customerCount; i++) {
      const staffIndex = Math.floor(i / customersPerStaff);
      const staff = staffMembers[staffIndex];
      
      // 计算该员工负责的客户在当前员工中的序号
      const customerIndexInStaff = i % customersPerStaff;
      
      // 在时间区间内平均分配该员工的任务
      // 确保每个员工的任务在时间区间内均匀分布
      const dayIndex = Math.min(
        Math.floor((customerIndexInStaff / customersPerStaff) * totalDays),
        totalDays - 1
      );
      
      plan.push({
        executorUserId: staff.userId,
        teamName: staff.teamName,
        executionTime: startDay.add(dayIndex, 'day').valueOf()
      });
    }
  }
  
  return plan;
};

const exportPreview = () => {
  if (previewList.value.length === 0) {
    ElMessage.warning("暂无预览数据");
    return;
  }

  // 显示加载状态
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '正在生成Excel文件，请稍候...',
    background: 'rgba(0, 0, 0, 0.7)'
  });

  try {
    // 准备导出数据
    const exportData = [];

    // 添加标题信息
    exportData.push(["回访计划预览表"]);

    // 添加统计信息
    exportData.push(["分配模式", customerMode.value === 'select' ? '轮询分配' : '平均分配']);
    exportData.push(["任务数量", previewList.value.length]);
    exportData.push(["客户数量", uniqueCustomerCount.value]);
    exportData.push(["执行人员", staffMembers.value.length]);
    exportData.push(["时间区间", `${startDate.value ? dayjs(startDate.value).format('YYYY-MM-DD') : ''} 至 ${endDate.value ? dayjs(endDate.value).format('YYYY-MM-DD') : ''}`]);
    
    // 添加执行人员详情
    if (staffMembers.value.length > 0) {
      exportData.push([]);
      exportData.push(["执行人员详情"]);
      staffMembers.value.forEach((member, index) => {
        const staffName = staffMap.value[member.userId]?.anotherName || member.userId || '未知';
        exportData.push([`执行人员${index + 1}`, staffName, member.teamName || '']);
      });
    }

    // 添加模板信息
    if (selectedTemplate.value) {
      exportData.push(["使用模板", selectedTemplate.value.planName || selectedTemplate.value.name || '未命名模板']);
    } else {
      exportData.push(["回访内容", `${taskForm.value.eventType ? ToDoEventType[taskForm.value.eventType] : ''} - ${taskForm.value.taskContent || ''}`]);
    }

    exportData.push([]); // 空行

    // 添加表头
    exportData.push([
      "序号",
      "客户姓名", 
      "创建时间",
      "计划名称",
      "事件类型",
      "执行方式",
      "执行团队",
      "执行人",
      "计划执行时间",
      "结束时间",
      "事件状态",
      "结果",
      "任务内容",
      "发送内容"
    ]);

    // 添加数据行
    previewList.value.forEach((row, index) => {
      exportData.push([
        index + 1,
        row.customerName || '未知客户',
        getCreateDate(row.createTime),
        row.planName || '',
        ToDoEventType[row.eventType] || row.eventType || '',
        row.executeMethod === 'todo' ? '待办单' : '群发单',
        row.executeTeamName || '',
        staffMap.value[row.executorUserId]?.anotherName || row.executorUserId || '',
        getCreateDate(row.plannedExecutionTime),
        getCreateDate(row.endTime),
        getStatusText(row.eventStatus),
        row.result || '',
        row.taskContent || '',
        row.sendContent || '无'
      ]);
    });

    // 添加汇总统计
    exportData.push([]);
    exportData.push(["汇总统计"]);
    
    // 按执行方式统计
    const methodStats = {};
    previewList.value.forEach(row => {
      const method = row.executeMethod === 'todo' ? '待办单' : '群发单';
      methodStats[method] = (methodStats[method] || 0) + 1;
    });
    
    Object.keys(methodStats).forEach(method => {
      exportData.push([`${method}数量`, methodStats[method]]);
    });
    
    // 按事件类型统计
    const eventTypeStats = {};
    previewList.value.forEach(row => {
      const eventType = ToDoEventType[row.eventType] || row.eventType || '未知';
      eventTypeStats[eventType] = (eventTypeStats[eventType] || 0) + 1;
    });
    
    Object.keys(eventTypeStats).forEach(eventType => {
      exportData.push([`${eventType}数量`, eventTypeStats[eventType]]);
    });

    // 创建工作簿和工作表
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(exportData);

    // 设置列宽
    const colWidths = [
      { wch: 8 },  // 序号
      { wch: 12 }, // 客户姓名
      { wch: 15 }, // 创建时间
      { wch: 25 }, // 计划名称
      { wch: 15 }, // 事件类型
      { wch: 12 }, // 执行方式
      { wch: 15 }, // 执行团队
      { wch: 12 }, // 执行人
      { wch: 15 }, // 计划执行时间
      { wch: 15 }, // 结束时间
      { wch: 12 }, // 事件状态
      { wch: 15 }, // 结果
      { wch: 30 }, // 任务内容
      { wch: 30 }  // 发送内容
    ];

    ws["!cols"] = colWidths;

    // 合并标题单元格
    ws["!merges"] = [
      { s: { r: 0, c: 0 }, e: { r: 0, c: 13 } }, // 标题行
    ];

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, "回访计划预览表");

    // 生成Excel文件并下载
    const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
    const blob = new Blob([excelBuffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });

    // 使用当前日期作为文件名的一部分
    const now = new Date();
    const fileName = `回访计划预览表_${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}.xlsx`;

    saveAs(blob, fileName);

    loadingInstance.close();
    ElMessage.success(`导出Excel成功，共导出 ${previewList.value.length} 条记录`);
  } catch (error) {
    loadingInstance.close();
    console.error('导出Excel失败:', error);
    ElMessage.error("导出Excel失败，请稍后重试");
  }
};

const confirmGenerate = async () => {
  if (previewList.value.length === 0) {
    ElMessage.warning("请先生成预览");
    return;
  }

  try {
    await ElMessageBox.confirm("确定要生成回访计划吗？", "提示", {
      type: "warning",
    });

    loading.value = true;

    // 模拟生成计划
    await new Promise((resolve) => setTimeout(resolve, 2000));

    ElMessage.success("回访计划生成成功");
    emit("success");
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("生成失败，请重试");
    }
  } finally {
    loading.value = false;
  }
};

// Lifecycle
onMounted(async () => {
  await getStaffList(true);
});

// Watch
watch(
  () => props.visible,
  (visible) => {
    if (visible) {
      // 重置表单
      assignmentMode.value = "person";
      customerMode.value = "select";
      selectedCustomers.value = [];
      selectedStaff.value = [];
      startDate.value = "";
      endDate.value = "";
      previewList.value = [];
      staffMembers.value = [];
      taskForm.value = {
        planExecutionTime: "",
        eventType: "",
        taskContent: "",
        executeMethod: "todo",
        sendContent: "",
      };
      selectedTemplate.value = null;
    }
  }
);
</script>

<style lang="scss" scoped>
.batch-followup-plan {
  background: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .plan-header {
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
    flex-shrink: 0;

    .plan-title {
      font-size: 16px;
      font-weight: 600;
      margin: 0;
      color: #333;
    }
  }

  .plan-layout {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;
    height: 610px;
    flex-shrink: 0;

    .left-panel {
      flex: 0 0 470px;
      display: flex;
      flex-direction: column;
      gap: 12px;
      height: 100%;

      .staff-section {
        border: 1px solid #ddd;
        border-radius: 4px;
        flex: 1;

        .section-header {
          background: #f5f5f5;
          padding: 6px 10px;
          border-bottom: 1px solid #ddd;
          font-size: 13px;
          font-weight: 500;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .section-title {
            color: #333;
          }
        }

        .staff-selection {
          padding: 10px;

          .staff-list {
            margin-bottom: 12px;

            .staff-list-container {
              height: 203px; /* 固定高度 */
              overflow-y: auto; /* 添加滚动 */
              border: 1px solid #eee;
              border-radius: 4px;
              background: #fff;
              padding: 6px;
              box-sizing: border-box;

              /* 自定义滚动条样式 */
              &::-webkit-scrollbar {
                width: 6px;
              }

              &::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 3px;
              }

              &::-webkit-scrollbar-thumb {
                background: #c1c1c1;
                border-radius: 3px;

                &:hover {
                  background: #a8a8a8;
                }
              }
            }

            .member-item {
              display: flex;
              align-items: center;
              gap: 6px;
              padding: 4px 6px;
              border: 1px solid #eee;
              border-radius: 3px;
              background: #fff;
              margin-bottom: 4px;
              box-shadow: 0 1px 1px rgba(0, 0, 0, 0.06);
              font-size: 12px;
              line-height: 1.2;

              &:last-child {
                margin-bottom: 0;
              }

              .member-info {
                display: flex;
                align-items: center;
                gap: 6px;
                flex: 1;
                min-width: 0;

                .ww-user {
                  flex-shrink: 0;
                  transform: scale(0.9);
                }

                .team-name {
                  font-size: 11px;
                  color: #666;
                  line-height: 1.1;
                }
              }

              .member-actions {
                display: flex;
                gap: 2px;
                flex-shrink: 0;

                .el-button {
                  padding: 1px 3px;
                  font-size: 11px;
                  min-height: auto;
                  line-height: 1.1;
                }
              }
            }

            .empty-state {
              text-align: center;
              color: #999;
              font-size: 13px;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              line-height: 1.2;
            }
          }

          .visit-period {
            .period-label {
              font-size: 14px;
              margin-bottom: 8px;
              color: #333;
            }

            .period-inputs {
              display: flex;
              align-items: center;
              gap: 8px;
              font-size: 12px;
              flex-wrap: wrap;

              span {
                color: #666;
                flex-shrink: 0;
                white-space: nowrap;
              }
            }
          }
        }
      }

      .content-section {
        border: 1px solid #ddd;
        border-radius: 4px;
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 0;

        .section-header {
          background: #f5f5f5;
          padding: 6px 10px;
          border-bottom: 1px solid #ddd;
          font-size: 13px;
          font-weight: 500;
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-shrink: 0;

          .section-title {
            color: #333;
          }

          .header-actions {
            display: flex;
            gap: 8px;
          }
        }

        .content-form {
          padding: 10px;
          height: 100%;
          overflow-y: auto;
          display: flex;
          flex-direction: column;

          /* 自定义滚动条样式 */
          &::-webkit-scrollbar {
            width: 6px;
          }

          &::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
          }

          &::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;

            &:hover {
              background: #a8a8a8;
            }
          }

          :deep(.el-form-item) {
            margin-bottom: 10px;

            .el-form-item__label {
              font-size: 12px;
              line-height: 18px;
            }
          }

          :deep(.task-method-radio) {
            display: flex;
            gap: 12px;
            max-width: 380px;

            .el-radio {
              margin-right: 0;
              align-items: flex-start;

              .el-radio__label {
                white-space: normal;
                line-height: 1.4;
                word-wrap: break-word;
                word-break: break-all;
                text-align: left;
              }

              .radio-content {
                font-size: 11px;
                line-height: 1.3;
                width: 110px;
                text-align: left;

                .radio-subtitle {
                  font-size: 10px;
                  color: #666;
                  margin-top: 1px;
                  line-height: 1.1;
                  text-align: left;
                }
              }
            }

            // 模板内容展示样式
            .template-content {
              padding: 10px;
              height: 100%;
              overflow-y: auto;
              display: flex;
              flex-direction: column;

              /* 自定义滚动条样式 */
              &::-webkit-scrollbar {
                width: 6px;
              }

              &::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 3px;
              }

              &::-webkit-scrollbar-thumb {
                background: #c1c1c1;
                border-radius: 3px;

                &:hover {
                  background: #a8a8a8;
                }
              }

              .template-header {
                margin-bottom: 8px;
                padding-bottom: 6px;
                border-bottom: 1px solid #e9ecef;

                .template-name {
                  font-size: 12px;
                  font-weight: 600;
                  color: #1e40af;
                  line-height: 1.3;
                }
              }

              .task_cell {
                background: #f8f9fa;
                border-radius: 4px;
                border: 1px solid #e9ecef;

                [border-bottom] {
                  border-bottom: 1px solid #e9ecef;
                }

                [grew-color] {
                  color: #6c757d;
                  font-size: 11px;
                  font-weight: 500;
                }

                .text-15px {
                  font-size: 13px;
                }

                .text-dark-500 {
                  color: #495057;
                }

                .font-semibold {
                  font-weight: 600;
                }

                .mt-15px {
                  margin-top: 10px;
                }

                .pt-6px {
                  padding-top: 6px;
                }

                .px-10px {
                  padding-left: 10px;
                  padding-right: 10px;
                }

                .py-6px {
                  padding-top: 6px;
                  padding-bottom: 6px;
                }

                .pb-6px {
                  padding-bottom: 6px;
                }

                .pb-10px {
                  padding-bottom: 10px;
                }

                .mb-6px {
                  margin-bottom: 6px;
                }

                .text-12px {
                  font-size: 12px;
                }

                .text-14px {
                  font-size: 12px;
                }

                .leading-16px {
                  line-height: 16px;
                }

                .leading-21px {
                  line-height: 16px;
                }

                .ml-5px {
                  margin-left: 3px;
                }

                .text-blue-600 {
                  color: #2563eb;
                }

                .font-medium {
                  font-weight: 500;
                }

                .compact-timeline {
                  :deep(.el-timeline-item__node) {
                    width: 8px;
                    height: 8px;
                  }

                  :deep(.el-timeline-item__wrapper) {
                    padding-left: 12px;
                  }

                  :deep(.el-timeline-item__content) {
                    font-size: 11px;
                  }
                }
              }

              .empty-template {
                .text-center {
                  text-align: center;
                }

                .text-gray-500 {
                  color: #6b7280;
                  font-size: 11px;
                }

                .py-20px {
                  padding-top: 15px;
                  padding-bottom: 15px;
                }
              }
            }
          }
        }
      }
    }

    .right-panel {
      flex: 1;
      height: 100%;
      min-height: 0;
      overflow: hidden;

      .customer-section {
        border: 1px solid #ddd;
        border-radius: 4px;
        height: 100%;
        display: flex;
        flex-direction: column;
        min-height: 0;
        overflow: hidden;

        .section-header {
          background: #f5f5f5;
          padding: 8px 12px;
          border-bottom: 1px solid #ddd;
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-shrink: 0;

          .customer-mode {
            font-size: 12px;

            .el-radio-group {
              display: flex;
              flex-wrap: nowrap;
              gap: 8px;

              .el-radio {
                flex-shrink: 0;
                white-space: nowrap;
              }
            }
          }

          .customer-count {
            font-size: 12px;
            color: #666;
          }
        }

        .customer-table {
          padding: 12px;
          flex: 1;
          overflow: hidden;
          display: flex;
          flex-direction: column;
          min-height: 0;
          height: 0; /* 确保flex子元素能够正确收缩 */

          :deep(.el-table) {
            height: 100%;
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
            overflow: hidden;
          }

          :deep(.el-table__inner-wrapper) {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
            overflow: hidden;
          }

          :deep(.el-table__header-wrapper) {
            flex-shrink: 0;
          }

          :deep(.el-table__body-wrapper) {
            flex: 1;
            overflow-y: auto;
            min-height: 0;
            max-height: none;

            /* 自定义滚动条样式 */
            &::-webkit-scrollbar {
              width: 6px;
            }

            &::-webkit-scrollbar-track {
              background: #f1f1f1;
              border-radius: 3px;
            }

            &::-webkit-scrollbar-thumb {
              background: #c1c1c1;
              border-radius: 3px;

              &:hover {
                background: #a8a8a8;
              }
            }
          }
        }

        .table-actions {
          padding: 8px 12px;
          border-top: 1px solid #eee;
          display: flex;
          gap: 8px;
          flex-wrap: nowrap;
          align-items: center;
          flex-shrink: 0;

          .el-button {
            flex-shrink: 0;
            white-space: nowrap;
          }
        }
      }
    }
  }

  .preview-section {
    border: 1px solid #ddd;
    border-radius: 4px;
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;

    .preview-header {
      background: #f5f5f5;
      padding: 8px 12px;
      border-bottom: 1px solid #ddd;
      font-size: 14px;
      font-weight: 500;
      flex-shrink: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .preview-title {
        font-weight: 600;
      }

      .preview-stats {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        align-items: center;
      }
    }

    .preview-table {
      padding: 12px;
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      min-height: 0;
      height: 0; /* 确保flex子元素能够正确收缩 */

      :deep(.el-table) {
        height: 100%;
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 0;
        overflow: hidden;
      }

      :deep(.el-table__inner-wrapper) {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 0;
        overflow: hidden;
      }

      :deep(.el-table__header-wrapper) {
        flex-shrink: 0;
      }

      :deep(.el-table__body-wrapper) {
        flex: 1;
        overflow-y: auto;
        min-height: 0;
        max-height: none;

        /* 自定义滚动条样式 */
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;

          &:hover {
            background: #a8a8a8;
          }
        }
      }
    }

    .preview-actions {
      padding: 8px 12px;
      border-top: 1px solid #eee;
      display: flex;
      gap: 8px;
      justify-content: flex-end;
      flex-shrink: 0;
    }


  }

  // 紧凑样式
  :deep(.el-table) {
    .el-table__header {
      th {
        padding: 4px 0;
        font-size: 12px;
        white-space: nowrap;

        .cell {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    .el-table__body {
      td {
        padding: 4px 0;
        font-size: 12px;

        .cell {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 100%;
        }
      }
    }

    // 支持换行的单元格样式
    .el-table__body td .cell {
      &.text-wrap-cell {
        white-space: normal;
        word-wrap: break-word;
        word-break: break-all;
        line-height: 1.4;
        padding: 4px 8px;
      }
    }
  }

  // 文本换行样式
  .text-wrap {
    word-wrap: break-word;
    word-break: break-all;
    white-space: normal;
    line-height: 1.4;
    max-height: 60px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }

  // 任务序号样式
  .task-index {
    font-size: 11px;
    color: #666;
    background: #f0f0f0;
    padding: 1px 4px;
    border-radius: 2px;
    margin-left: 4px;
  }

  :deep(.el-radio-group) {
    .el-radio {
      font-size: 12px;

      .el-radio__label {
        font-size: 12px;
      }
    }
  }

  :deep(.el-form-item) {
    margin-bottom: 8px;
  }
}

/* 人员选择相关样式 */
.member-picker-select {
  :deep(.el-input__inner) {
    display: none;
  }

  :deep(.el-input__prefix) {
    flex-grow: 1;
  }
}

/* 人员选择弹窗紧凑样式 */
:deep(.member-picker-select-popover) {
  .el-select-dropdown__item {
    padding: 6px 12px;
    height: auto;
    line-height: 1.2;
  }

  .el-select-dropdown__list {
    max-height: 300px;
  }
}

.member-picker-content {
  padding: 20px;
}

/* 紧凑人员选项样式 */
.compact-member-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;

  .member-name {
    font-size: 12px;
    color: #333;
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .team-name {
    font-size: 11px;
    color: #999;
    background: #f5f5f5;
    padding: 2px 6px;
    border-radius: 3px;
    flex-shrink: 0;
  }
}
</style>

<style lang="scss">
/* el-drawer 自定义样式 */
:deep(.el-drawer) {
  .el-drawer__header {
    padding: 16px 20px;
    border-bottom: 1px solid #eee;
    background: #f5f5f7;
    margin-bottom: 0;
  }

  .el-drawer__title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }

  .el-drawer__body {
    padding: 0;
    height: calc(100vh - 80px);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    max-height: calc(100vh - 80px);
  }
}
</style> 