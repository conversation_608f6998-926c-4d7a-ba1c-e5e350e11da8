import dayjs from 'dayjs';
import { getDetail, getAnswer } from "@/api/survery";
import { ServiceType, ToDoTaskStatus } from "@/baseData";
import { isURL } from "@/utils/common";

export default function useServiceTable() {

  function formatTime(str) {
    return str && dayjs(str).isValid() ? dayjs(str).format("YYYY-MM-DD HH:mm") : "";
  }

  function viewArticle(item) {
    isURL(item.url) && window.open(item.url)
  }

  async function getSurvery(item, memberId) {
    const { surveryId, answerId } = item;
    if (answerId && surveryId && memberId) {
      const { data } = await getAnswer(surveryId, memberId, answerId);
      if (data && data.record && data.record.submitTime) {
        return data && data.record ? data.record : null;
      }
    }
    if (surveryId) {
      const corpId = localStorage.getItem("corpId");
      const { data } = await getDetail(corpId, surveryId);
      return data && data.data ? data.data : null;
    }
  }

  function formatList(list) {
    return list.map((i) => {
      const status = ToDoTaskStatus.find((item) => item.value === i.eventStatus);
      const pannedEventSendFile = i.pannedEventSendFile;
      const hasSurvery = pannedEventSendFile && pannedEventSendFile.type === 'questionnaire';
      const hasArticle = pannedEventSendFile && pannedEventSendFile.type === 'article';
      return {
        ...i,
        hasSurvery,
        hasArticle,
        taskStatus: status ? status.label : "",
        taskType: ServiceType[i.eventType] || "",
        executionTime: formatTime(i.executionTime),
      };
    })
  }



  return { formatList, viewArticle, getSurvery }
}