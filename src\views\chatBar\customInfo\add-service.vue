<template>
  <el-dialog :model-value="props.visible" :width="width" title="添加服务记录" @close="close">
    <el-form :label-width="90" label-position="top">
      <el-form-item class="is-required" label="执行日期" prop="plannedExecutionDate">
        <el-date-picker v-model="form.plannedExecutionDate" type="date" format="YYYY-MM-DD" placeholder="执行日期" style="width: 100%" value-format="YYYY-MM-DD" />
      </el-form-item>
      <el-form-item class="is-required" label="执行时间" prop="plannedExecutionTime">
        <el-time-picker v-model="form.plannedExecutionTime" format="HH:mm" placeholder="执行时间" style="width: 100%" value-format="HH:mm" />
      </el-form-item>
      <el-form-item class="is-required" label="服务内容" prop="pannedSendContent">
        <el-input style="width: 100%" v-model="form.pannedSendContent" maxlength="100" :rows="3" show-word-limit type="textarea" placeholder="请输入服务记录内容" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div text-center>
        <el-button @click="close()">取消</el-button>
        <el-button type="primary" @click="save()">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import dayjs from "dayjs";
import { ElMessage } from "element-plus";
import { ref, watch } from "vue";
import { addServiceRecord } from "@/api/todo";

const props = defineProps({
  member: { type: String, default: "" },
  teamId: { type: String, default: "" },
  visible: { type: Boolean, default: false },
});
const emits = defineEmits(["close", "addSuccess"]);
function close() {
  emits("close");
}

const form = ref({ plannedExecutionDate: "", plannedExecutionTime: "", pannedSendContent: "" });
const width = ref(Math.floor(window.innerWidth * 0.75));
async function save() {
  if (!form.value.plannedExecutionDate) {
    ElMessage.info("请选择执行时间");
    return;
  }
  if (!form.value.plannedExecutionTime) {
    ElMessage.info("请选择执行时间");
    return;
  }
  if (!form.value.pannedSendContent || form.value.pannedSendContent.trim() == "") {
    ElMessage.info("请输入服务记录内容");
    return;
  }
  if (!props.member._id) {
    ElMessage.info("客户id不能为空");
    return;
  }
  const time = `${form.value.plannedExecutionDate} ${form.value.plannedExecutionTime}`;
  const params = {
    taskContent: form.value.pannedSendContent,
    executionTime: dayjs(time).valueOf(),
    customerId: props.member._id,
    customerName: props.member.name,
    customerUserId: props.member.externalUserId,
    executeTeamId: props.teamId,
    creatorUserId: localStorage.getItem("userId"), // 创建人
    executorUserId: localStorage.getItem("userId"), // 执行人
    corpId: localStorage.getItem("corpId"),
    eventType: "serviceSummary", // 服务类型是服务小结
  };
  const { success, message } = await addServiceRecord(params);
  if (success) {
    close();
    emits("addSuccess");
  } else {
    ElMessage.error(message);
  }
}
watch(
  () => props.visible,
  (n) => {
    if (n) {
      form.value = { plannedExecutionDate: "", plannedExecutionTime: "", pannedSendContent: "" };
      width.value = Math.floor(window.innerWidth - 40);
    }
  }
);

function noFutureDate(date) {
  return dayjs(date).startOf("day").isAfter(dayjs());
}
</script>
