<template>
  <el-drawer :model-value="visible" title="治疗记录" size="70%" style="--el-drawer-padding-primary: 0" @close="close()">
    <my-layout v-loading="loading">
      <layout-item>
        <div class="p-15px text-14px text-gray-500">以下为“{{ treatmemtItem.customerInfo?.name }}”的治疗记录</div>
      </layout-item>
      <layout-main :scroll="false">
        <el-table stripe border class="h-full" ref="multipleTableRef" :data="list" @selection-change="handleSelectionChange">
          <el-table-column align="center" type="selection" width="60" />
          <el-table-column prop="projectName" :min-width="160" label="项目名称" />
          <el-table-column prop="packageName" :min-width="160" label="所属套餐" />
          <el-table-column prop="num" :min-width="120" label="剩余/总数" />
          <el-table-column prop="treatmentDeptName" :min-width="120" label="治疗科室">
            <template #default="{ row }">
              <el-select :model-value="row.treatmentDeptName" placeholder="请选择治疗科室" @update:model-value="changeDept($event, row)">
                <el-option label="治疗科室1" value="治疗科室1" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="treatmentDoctorUserId" :min-width="120" label="治疗人员">
            <template #default="{ row }">
              <ww-user-select :list="['huxuejian']" placeholder="请选择治疗人员" :value="row.treatmentDoctorUserId" @change="changeUserid($event, row)" />
            </template>
          </el-table-column>
        </el-table>
      </layout-main>
      <layout-item>
        <div class="text-center mt-10px py-10px bg-white" common-shadow--r>
          <el-button plain class="w-100px" @click="close()">取消</el-button>
          <el-button class="w-100px" type="primary" @click="confirm()">确定</el-button>
        </div>
      </layout-item>
    </my-layout>
  </el-drawer>
</template>
<script setup>
import { computed, nextTick, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import WwUserSelect from "@/components/ww-user-select";

const emits = defineEmits(["close", "change"]);
const props = defineProps({
  visible: { type: Boolean, default: false },
  treatmemtItem: { type: Object, default: {} },
});

const list = ref([]);
const multipleSelection = ref([]);
const loading = ref(false);
const multipleTableRef = ref();
function close() {
  emits("close");
}
function confirm() {}

function changeDept(val, row) {
  const index = list.value.findIndex((item) => item.id === row.id);
  list.value[index].treatmentDeptName = val;
}

function changeUserid(val, row) {
  const index = list.value.findIndex((item) => item.id === row.id);
  list.value[index].treatmentDoctorUserId = val;
}

async function getList() {
  list.value = new Array(10).fill({}).map((item, index) => {
    return {
      id: Date.now() + "@" + index,
      projectName: "项目名称" + index,
      packageName: "所属套餐" + index,
      num: "剩余/总数" + index,
      treatmentDeptName: "治疗科室" + index,
      treatmentDoctorUserId: "治疗人员" + index,
    };
  });
}

function handleSelectionChange(val) {
  multipleSelection.value = val;
}

watch(
  () => props.visible,
  async (n) => {
    if (n) {
      getList();
      await nextTick();
      multipleTableRef.value && multipleTableRef.value.clearSelection();
    }
  }
);
</script>
<style scoped>
.triangle-up {
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-bottom: 20px solid currentColor;
}
</style>
