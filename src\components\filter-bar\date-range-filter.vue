<template>
  <base-filter  :label="label" :text="text" :width="width" @onClick="show()">
    <el-date-picker v-model="dates" ref="datePickerRef" type="daterange"  :unlink-panels="true"
 value-format="YYYY-MM-DD" :popper-options="option" :shortcuts="shortcuts" />
  </base-filter>
</template>
<script setup>
import { onMounted, ref } from "vue";
import dayjs from "dayjs";
import { useVModels } from "@vueuse/core";

import baseFilter from "./base-filter-item.vue";

const props = defineProps({
  dates: { type: Array, default: () => [] },
  label: { type: String, default: "" },
  text: { type: String, default: "" },
  width: { type: [Number, String], default: 280 },
  noAll: { type: Boolean, default: false },
});
const emits = defineEmits(["update:dates"]);
const { dates } = useVModels(props, emits);
const datePickerRef = ref();
const option = {
  modifiers: [{ name: "flip", options: { fallbackPlacements: ["bottom"], allowedAutoPlacements: ["bottom"] } }],
};

function show() {
  datePickerRef.value.handleOpen();
}
function clear() {
  if (props.noAll) {
    dates.value = [dayjs().startOf("year").format("YYYY-MM-DD"), dayjs().endOf("year").format("YYYY-MM-DD")];
  } else {
    dates.value = [];
  }
}
const shortcuts = ref([
  // {
  //   text: "全部",
  //   value() {
  //     dates.value = [];
  //     datePickerRef.value && datePickerRef.value.handleClose();
  //   },
  // },
  {
    text: "今天",
    value() {
      return [dayjs().toDate(), dayjs().toDate()];
    },
  },
  {
    text: "昨天",
    value() {
      return [dayjs().subtract(1, "day").toDate(), dayjs().subtract(1, "day").toDate()];
    },
  },
  {
    text: "本周",
    value: () => {
      const start = dayjs().startOf("week").toDate();
      const end = dayjs().toDate();
      return [start, end];
    },
  },
  {
    text: "上周",
    value: () => {
      const start = dayjs().subtract(1, "week").startOf("week").toDate();
      const end = dayjs().subtract(1, "week").endOf("week").toDate();
      return [start, end];
    },
  },
  {
    text: "本月",
    value: () => {
      const start = dayjs().startOf("month").toDate();
      const end = dayjs().toDate();
      return [start, end];
    },
  },
  {
    text: "上月",
    value: () => {
      const start = dayjs().subtract(1, "month").startOf("month").toDate();
      const end = dayjs().subtract(1, "month").endOf("month").toDate();
      return [start, end];
    },
  },
  {
    text: "去年",
    value: () => {
      const start = dayjs().subtract(1, "year").startOf("year").toDate();
      const end = dayjs().subtract(1, "year").endOf("year").toDate();
      return [start, end];
    },
  },
  {
    text: "今年",
    value: () => {
      const start = dayjs().startOf("year").toDate();
      const end = dayjs().endOf("year").toDate();
      return [start, end];
    },
  },
]);
onMounted(() => {
  if (props.noAll) shortcuts.value = shortcuts.value.filter((i) => i.text !== "全部");
});
</script>
<style lang="scss" scoped>
:deep(.el-date-editor--daterange) {
  position: absolute;
  left: 0;
  right: 0;
  top: 100%;
  height: 0;
  opacity: 0;
  overflow: hidden;
}
:deep(.el-picker-panel__sidebar) {
  width: 80px;
}

:deep(.el-picker-panel__shortcut) {
  text-align: center;
}
</style>
