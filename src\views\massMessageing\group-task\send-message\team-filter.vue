<template>
  <el-dialog :model-value="visible" :width="400" :show-close="teamList.length == 0" @close="close">
    <template #header="{ titleClass }">
      <div flex items-center justify-between>
        <div :class="titleClass">执行团队</div>
        <div flex items-center>
          <div font-14 font-semibold mr-10>全选</div>
          <div>
            <img src="@/assets/check-active.png" class="w-18px h-18px pointer" v-if="isAllTeam" @click="removeAllTeam()" />
            <img @click="selectAllTeam()" src="@/assets/check.png" class="w-18px h-18px pointer" v-else />
          </div>
        </div>
      </div>
    </template>
    <el-scrollbar wrap-style="max-height: calc(100vh - 360px)">
      <!-- <div flex items-center px-15 pb-10 border-bottom>
        <div flex-grow>
          <div pt-10 flex items-center>
            <div mr-10>
              <img src="@/assets/check-active.png" class="w-18px h-18px pointer" v-if="isAllTeam" @click="removeAllTeam()" />
              <img @click="selectAllTeam()" src="@/assets/check.png" class="w-18px h-18px pointer" v-else />
            </div>
            <div font-14 font-semibold>全选</div>
          </div>
        </div>
      </div> -->
      <div v-for="item in teamList" flex items-center px-15 pb-10 border-bottom>
        <div flex-grow>
          <div pt-10 flex items-center>
            <div mr-10>
              <img src="@/assets/check-active.png" class="w-18px h-18px pointer" v-if="selfSelectTeams.some((i) => i === item.teamId)" @click="removeTeam(item)" />
              <img @click="addTeam(item)" src="@/assets/check.png" class="w-18px h-18px pointer" v-else />
            </div>
            <div font-14 font-semibold>{{ item.name }}</div>
          </div>
        </div>
      </div>
      <div v-if="teamList.length === 0" text-center class="pt-40px pb-30px"><div class="pb-15px" font-16>暂无团队信息</div></div>
    </el-scrollbar>
    <template v-if="teamList.length > 0" #footer>
      <div text-center>
        <el-button @click="close()">取消</el-button>
        <el-button :loading="saveLoading" type="primary" @click="saveStaffTeam()">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>
  <script setup>
import { computed, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { teamStore } from "@/store/team";
import { useRoute, useRouter } from "vue-router";
const route = useRoute();
const sendTaskType = ref(route.params.sendTaskType);
const { allTeams, teams } = teamStore();
let props = defineProps({
  visible: { type: Boolean, default: false },
  selectTeams: { type: Array, default: [] },
});
const teamList = ref([]);
const selfSelectTeams = ref([]);
const teamName = ref("");

watch(
  () => props.visible,
  (newItem) => {
    if (newItem) {
      if (sendTaskType.value === "MINE") {
        teamList.value = teams;
      } else if (sendTaskType.value === "CORP") {
        teamList.value = allTeams;
      } else {
        teamList.value = [];
      }
      selfSelectTeams.value = props.selectTeams.length === 0 ? teamList.value.map((i) => i.teamId) : props.selectTeams;
    }
  },
  { immediate: true }
);
const emits = defineEmits(["close", "change"]);
const isAllTeam = computed(() => selfSelectTeams.value.length === teamList.value.length);
function close() {
  emits("close");
}
function saveStaffTeam(item) {
  if (selfSelectTeams.value.length === 0) {
    ElMessage.error("请选择团队");
    return;
  }
  emits("change", selfSelectTeams.value);
}
function addTeam(item) {
  selfSelectTeams.value.push(item.teamId);
}
function removeTeam(item) {
  selfSelectTeams.value = selfSelectTeams.value.filter((i) => i !== item.teamId);
}
function removeAllTeam() {
  selfSelectTeams.value = [];
}
function selectAllTeam() {
  selfSelectTeams.value = teamList.value.map((i) => i.teamId);
}
function saveTeam() {
  if (!teamName.value) {
    ElMessage.error("请输入团队名称");
    return;
  }
  createTeam();
  nameVisible.value = false;
}
function change(value, teamId) {}
</script>