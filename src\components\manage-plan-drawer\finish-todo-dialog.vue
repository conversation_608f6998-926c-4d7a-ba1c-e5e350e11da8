<template>
  <el-dialog :model-value="visible" :title="title" :width="600" @close="close">
    <div px-15 class="py-20px">
      <el-input v-model="result" type="textarea" placeholder="请填写处理结果" :autosize="{ minRows: 6, maxRows: 8 }" />
    </div>
    <template #footer>
      <div text-center>
        <el-button plain @click="close()">取消</el-button>
        <el-button type="primary" @click="save()"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref, toRefs, watch } from 'vue';

const emits = defineEmits(['close', 'save']);
const props = defineProps({
  visible: { type: Boolean, default: false },
  type: {
    default: 'closed',
    validator(value) {
      return ['closed', 'treated'].includes(value)
    }
  }
})
const { visible, type } = toRefs(props);
const result = ref('');
const title = computed(() => type.value === 'closed' ? '取消任务' : '完成任务');

function close() {
  emits('close')
}

function save() {
  emits('save', result.value.trim())
}

watch(visible, n => {
  if (n) {
    result.value = ''
  }
})
</script>