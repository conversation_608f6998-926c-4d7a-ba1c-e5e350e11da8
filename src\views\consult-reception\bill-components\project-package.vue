<template>
  <div class="pb-10px border-b border-gray-200">
    <el-input v-model.trim="keyword" placeholder="搜索项目" :prefix-icon="Search" clearable />
  </div>
  <div v-show="keyword" v-loading="loading">
    <el-scrollbar class="h-550px" color-normal>
      <div v-for="(i, idx) in searchList" :key="`search_${i.checkedKey}`"
        class="py-10px px-15px cursor-pointer flex items-center border-b border-gray-200" @click="toggle(i)">
        <div class="flex-grow w-0 mr-10px">
          <div class="text-15px">
            <span class="font-semibold">{{ i.projectName }}</span>
            <el-icon class="ml-5px text-16px text-blue-500 cursor-pointer transform translate-y-2px"
              @click.stop="show(i)">
              <Document />
            </el-icon>
          </div>
          <div v-if="i.cateNames" class="mt-10px text-13px break-all text-gray-500 truncate">{{ i.cateNames }}</div>
          <div class="flex items-center mt-10px text-13px">
            <div class="flex-grow w-0 text-gray-500 truncate">{{ i.deptNames }}</div>
            <div v-if="i.price > 0" class="ml-10px flex-shrink-0 text-orange-500">￥{{ (i.price - 0).toFixed(2) }}</div>
          </div>
        </div>
        <el-checkbox :model-value="Boolean(map[i.checkedKey])" />
      </div>
      <div v-if="searchMore"
        class="mt-10px px-10px flex items-center justify-center py-4px cursor-pointer text-gray-400 hover:text-blue-500"
        @click="getMore()">
        <div class="flex-grow h-1px bg-gray-200"></div>
        <div class="text-14px ml-10px">加载更多</div>
        <el-icon class="flex-shrink-0 ml-5px mr-10px text-14px">
          <ArrowDownBold />
        </el-icon>
        <div class="flex-grow h-1px bg-gray-200"></div>
      </div>
    </el-scrollbar>
  </div>
  <div v-show="!keyword" v-loading="loading" class="flex h-550px">
    <div class="flex-shrink-0 w-200px border-r border-gray-200">
      <el-scrollbar>
        <classify-list :checked-cate="current" :data="cateList" @change="onChange($event)" />
      </el-scrollbar>
    </div>
    <el-scrollbar class="flex-grow" color-normal>
      <div v-for="(i, idx) in list" :key="i._id"
        class="py-10px px-15px cursor-pointer flex items-center border-b border-gray-200" @click="toggle(i)">
        <div class="flex-grow w-0 mr-10px">
          <div class="text-15px font-semibold">
            <span>{{ i.projectName }}</span>
            <el-icon class="ml-5px text-16px text-blue-500 cursor-pointer transform translate-y-2px"
              @click.stop="show(i)">
              <Document />
            </el-icon>
          </div>
          <div class="flex items-center mt-10px text-13px">
            <div class="flex-grow w-0 text-gray-500 truncate">{{ i.deptNames }}</div>
            <div v-if="i.price > 0" class="ml-10px flex-shrink-0 text-orange-500">￥{{ (i.price - 0).toFixed(2) }}</div>
          </div>
        </div>
        <el-checkbox :model-value="Boolean(map[i.checkedKey])" />
      </div>
      <div v-if="more"
        class="mt-10px px-10px flex items-center justify-center py-4px cursor-pointer text-gray-400 hover:text-blue-500"
        @click="loadMore()">
        <div class="flex-grow h-1px bg-gray-200"></div>
        <div class="text-14px ml-10px">加载更多</div>
        <el-icon class="flex-shrink-0 ml-5px mr-10px text-14px">
          <ArrowDownBold />
        </el-icon>
        <div class="flex-grow h-1px bg-gray-200"></div>
      </div>
    </el-scrollbar>
  </div>
</template>
<script setup>
import { computed, onMounted, ref, watch } from "vue";
import { watchDebounced } from "@vueuse/core";
import { getProjectList, getProjectCateList, getProjectListWithCates } from "@/api/project-manage";
import { Search } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import useClassifyList from "@/components/classify-list/useClassifyList";
import classifyList from "@/components/classify-list/classify-list-side.vue";
const keyword = ref("");
const emits = defineEmits(["get-project", "show-project"]);
const loading = ref(false);
const options = {
  getList: getProjectCateList,
  callback: getProjects,
  format: list => list.filter(i => (i.level === 1 && i.deptId) || i.level !== 1),
  loading,
};
const { cateList, getCateList } = useClassifyList(options, false);
const searchPage = ref(1);
const searchList = ref([]);
const searchMore = ref(false);
const more = ref(false);
const current = ref({});
const page = ref(1);
const list = ref([]);
const selections = ref([]);
onMounted(async () => {
  if (cateList.value.length === 0 && !loading.value) {
    await getCateList();
  }
});
const map = computed(() => {
  return selections.value.reduce((p, c) => {
    p[c.checkedKey] = true;
    return p;
  }, {});
});

function close() {
  emits("close");
}
function onChange(data) {
  if (current.value._id !== data._id) {
    current.value = { ...data };
  }
}
function loadMore() {
  if (more.value && !loading.value) {
    page.value++;
    getProjects();
  }
}

function toggle(item) {
  const { _id, projectName, checkedKey } = item;
  if (map.value[checkedKey]) {
    selections.value = selections.value.filter((i) => i.checkedKey !== checkedKey);
  } else {
    selections.value.push(item);
  }
  emits("get-project", selections.value);
}

async function getProjects() {
  const p = {
    page: page.value,
    pageSize: 15,
    corpId: localStorage.getItem("corpId"),
    showDepts: true,
    projectStatus: 'enable',
    cateIds: [current.value._id, ...(current.value.childrenIds || [])],
  };
  const { arr, count } = await getList(p, true);
  const projects = Array.isArray(arr)
    ? arr.map((i) => ({
      ...i,
      checkedKey: `${current.value._id}_${i._id}`,
      deptNames: Array.isArray(i.depts) ? i.depts.map((d) => d.deptName).join(",") : "",
    }))
    : [];
  list.value = page.value === 1 ? projects : [...list.value, ...projects];
  more.value = count > list.value.length;
}

async function getProjectsByIds(ids) {
  const unexistIds = ids.filter((i) => !map.value[i]);
  const existProjects = ids.map((i) => map.value[i]).filter(Boolean);
  if (unexistIds) {
    const p = {
      page: 1,
      pageSize: ids.length,
      corpId: localStorage.getItem("corpId"),
      projectStatus: 'enable',
      ids: unexistIds,
    };
    const { arr } = await getList(p, false);
    return [...existProjects, ...arr];
  }
  return existProjects;
}

async function getMore() {
  if (searchMore.value && !loading.value) {
    searchPage.value++;
    loading.value = true;
    search();
  }
}

async function search() {
  const p = {
    page: searchPage.value,
    pageSize: 15,
    corpId: localStorage.getItem("corpId"),
    projectName: keyword.value,
    projectStatus: 'enable',
    showDepts: true,
    hasBillDept: true
  };
  const { arr, count } = await getList(p, true);
  const projects = Array.isArray(arr)
    ? arr.map((i) => ({
      ...i,
      checkedKey: `${current.value._id}_${i._id}`,
      deptNames: Array.isArray(i.depts) ? i.depts.map((d) => d.deptName).join(",") : "",
    }))
    : [];
  searchList.value = searchPage.value === 1 ? projects : [...list.value, ...projects];
  searchMore.value = count > list.value.length;
  loading.value = false;
  // const { arr, count } = await
  // const { data, success, message } = await getProjectList(p);
  // const list =
  //   data && Array.isArray(data.list)
  //     ? data.list.map((i) => {
  //       const cateParentGroupNames = Array.isArray(i.cateParentGroupNames) ? i.cateParentGroupNames : [];
  //       const projectCateName = typeof i.projectCateName === "string" ? i.projectCateName : "";
  //       const cateNames = [...cateParentGroupNames, projectCateName];
  //       return {
  //         ...i,
  //         checkedKey: `${i.projectCateId}_${i._id}`,
  //         cateNames: cateNames.join("/"),
  //         deptNames: Array.isArray(i.depts) ? i.depts.map((d) => d.deptName).join(",") : "",
  //       };
  //     })
  //     : [];
  // const count = data && data.total >= 0 ? data.total : 0;
  // searchList.value = searchPage.value === 1 ? list : [...searchList.value, ...list];
  // searchMore.value = searchPage.value > data.pages;
  // loading.value = false;
}

async function getList(payload, showMessage) {
  const { success, data, message } = await getProjectList(payload);
  const { list: arr, total: count = 0 } = data;
  if (success) {
    return { arr: Array.isArray(arr) ? arr : [], count };
  } else if (showMessage) {
    ElMessage.error(message);
  }
}

function removePorject(checkedKey) {
  selections.value = selections.value.filter((i) => i.checkedKey !== checkedKey);
}

function show(i) {
  emits("show-project", i);
}

watch(current, (n, o) => {
  if (n && n._id) {
    page.value = 1;
    getProjects();
  }
});

watchDebounced(
  keyword,
  (n) => {
    if (n.trim() === "") {
      searchList.value = [];
    } else {
      loading.value = true;
      searchPage.value = 1;
      search();
    }
  },
  { debounce: 500 }
);

function clear() {
  keyword.value = "";
  searchList.value = [];
  selections.value = [];
}

defineExpose({
  id2Project: getProjectsByIds,
  clear,
  removePorject,
});
</script>
<style lang="scss" scoped></style>