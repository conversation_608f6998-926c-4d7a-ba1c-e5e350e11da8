<template>
  <el-scrollbar>
    <!-- 卡号字段显示 -->
    <div v-if="hasCardNo" class="px-15px py-10px border-b border-gray-100 bg-white">
      <div class="flex items-center justify-between">
        <div>
          <div class="text-14px text-gray-600 mb-5px">卡号</div>
          <div class="text-16px text-gray-900">{{ customer.cardNo || '--' }}</div>
        </div>
        <el-icon color="#006eff" size="20" class="edit-btn cursor-pointer" @click="editCardNo()">
          <Edit />
        </el-icon>
      </div>
    </div>
    <secondary-info :stageList="stageList" @editAll="showEdit()" @changeTag="changeTag" @edit="showEditNotes($event)"
      @editStage="editStage"></secondary-info>
    <edit-inner-modal :visible="visible" :customer="customer" @close="visible = false" @update="handleEdit($event)" />
    <tag-drawer ref="tagDrawerRef" @get-select-tag="onTagChanged($event)"></tag-drawer>
    <stage-modal :value="customer.customerStage" :stageList="stageList" :visible="stageVisible"
      @close="stageVisible = false" @change="saveStage" />
    <el-dialog v-model="editVisible" title="编辑备注" draggable :width="600">
      <div style="padding: 15px;">
        <el-input type="textarea" placeholder="请填写备注信息" v-model="notes" :autosize="{ minRows: 6 }" />
      </div>
      <template #footer>
        <div class="text-center">
          <el-button class="w-100px" @click="editVisible = false">取消</el-button>
          <el-button class="w-100px" type="primary" @click="saveNotes()"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 卡号编辑弹窗 -->
    <el-dialog v-model="cardNoVisible" title="编辑卡号" draggable :width="500">
      <div style="padding: 15px;">
        <el-input v-model="editCardNoValue" placeholder="请输入卡号" :maxlength="50" show-word-limit clearable />
      </div>
      <template #footer>
        <div class="text-center">
          <el-button class="w-100px" @click="cardNoVisible = false">取消</el-button>
          <el-button class="w-100px" type="primary" @click="saveCardNo()"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </el-scrollbar>
</template>
<script setup>
import { provide, ref, toRefs, onMounted, computed } from "vue";
import { storeToRefs } from 'pinia';
import { ElMessage } from "element-plus";
import { updateMember } from '@/api/member';
import { configStore } from '@/store/config';
import { Edit } from '@element-plus/icons-vue';

import EditInnerModal from './edit-inner-modal';
import SecondaryInfo from './secondary-info';
import StageModal from "./stage-modal";
import TagDrawer from "@/components/tag-drawer/index.vue";

const emits = defineEmits(['reload']);

provide('labelWidth', 110)
provide('formSize', 'large')

const props = defineProps({ customer: { type: Object, default: () => ({}) } })
const { customer } = toRefs(props);
provide('customer', customer);

const config = configStore();
const { getStage } = config;
const { stageMap, stageList } = storeToRefs(config);

// 计算是否有卡号字段
const hasCardNo = computed(() => {
  return true; // 始终显示卡号字段
});

// 卡号编辑相关
const cardNoVisible = ref(false);
const editCardNoValue = ref('');

function editCardNo() {
  editCardNoValue.value = customer.value.cardNo || '';
  cardNoVisible.value = true;
}

async function saveCardNo() {
  const success = await update({ cardNo: editCardNoValue.value })
  if (success) {
    cardNoVisible.value = false;
  }
}

const tagDrawerRef = ref();
function changeTag() {
  tagDrawerRef.value.openDialog(Array.isArray(customer.value.tagIds) ? customer.value.tagIds : []);
}
async function onTagChanged(tagIds) {
  await update({ tagIds })
}

const editVisible = ref(false);
const notes = ref('');
function showEditNotes(val) {
  notes.value = val;
  editVisible.value = true
}
async function saveNotes() {
  const success = await update({ notes: notes.value })
  if (success) {
    editVisible.value = false
  }
}

const stageVisible = ref(false);
async function editStage() {
  if (stageList.value.length == 0) {
    await getStageList()
  }
  stageVisible.value = true
}
async function saveStage(customerStage) {
  const success = await update({ customerStage })
  if (success) {
    stageVisible.value = false;
    emits('reload');
  }
}


async function update(data) {
  const { success, message } = await updateMember(customer.value._id, data)
  if (success) {
    emits('reload');
    ElMessage.success(message)
  } else {
    ElMessage.error(message)
  }
  return success
}

const visible = ref(false)
function showEdit() {
  visible.value = true
}
async function handleEdit(data) {
  const success = await update(data)
  if (success) {
    visible.value = false
    emits('reload');
  }
}
onMounted(() => {
  if (customer.value.customerStage && !stageMap.value[customer.value.customerStage]) getStage();
})

</script>
<style scoped>
.edit-btn {
  cursor: pointer;
  transform: translateY(5px);
}
</style>