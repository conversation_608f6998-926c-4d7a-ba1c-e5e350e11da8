<template>
    <my-layout bg-fff common-shadow>
        <layout-main>
            <div class="pt-15px px-30px">
                <el-form label-width="100px" label-suffix="：" label-position="left">
                    <div title-bar class="pb-10px">机构信息</div>
                    <el-row>
                        <el-col v-bind="colAttr">
                            <el-form-item label="机构名称">
                                {{ corpInfo.corp_name }} <el-button flex-shrink-0 size="small" text type="primary"
                                    @click="editCorpName">修改</el-button>
                            </el-form-item>
                        </el-col>
                        <el-col v-bind="colAttr"> <el-form-item label="机构ID">
                                {{ corpInfo.corpId }}
                            </el-form-item>
                        </el-col>
                        <el-col v-bind="colAttr">
                            <el-form-item label="入驻时间">
                                <div v-if="corpInfo.createTime">
                                    {{ dayjs(corpInfo.createTime).format('YYYY-MM-DD HH:mm') }}
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24"><el-form-item label="超级管理员">
                                <div class="flex align-center mr-10px" v-for="(item, idx) in adminUsers" :key="idx">
                                    <el-icon class="mr-5px" flex-shrink-0>
                                        <Avatar />
                                    </el-icon>
                                    {{ item.superAdminName }}
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-form-item label="机构logo">
                            <div class="flex items-center">
                                <img v-if="customLogo" :src="customLogo" class="w-40px h-40px mr-10px">
                                <el-text type="primary cursor-pointer" @click="showModal()">修改</el-text>
                            </div>
                        </el-form-item>
                        <el-col :span="24">
                            <el-form-item label="机构图片">
                                <div class="flex flex-grow">
                                    <img v-if="corpPic" :src="corpPic" class="w-200px h-100px mr-10px"
                                        @click="showViewer=true">
                                    <el-image-viewer v-if="showViewer" @close="() => { showViewer = false }"
                                        :url-list="[corpPic]" />
                                    <el-text type="primary cursor-pointer" @click="showCorpPicture()">修改</el-text>
                                </div>
                            </el-form-item>
                        </el-col>

                    </el-row>
                    <el-divider border-style="dashed" />
                    <div title-bar class="pb-10px">套餐信息</div>
                    <el-row>
                        <el-col v-bind="colAttr">
                            <el-form-item label="当前套餐">
                                {{ corpPackage && corpPackage.packageName }}
                                <el-button text type="primary" size="small"
                                    @click="readPackage(item)">查看套餐记录</el-button>
                            </el-form-item>
                        </el-col>
                        <el-col v-bind="colAttr">
                            <el-form-item label="套餐状态">
                                {{ corpPackage && corpPackage.packageStatus === 'closed' ? '已结束' : '进行中' }}
                            </el-form-item>
                        </el-col>
                        <el-col v-bind="colAttr">
                            <el-form-item label="启用时间">
                                {{ corpPackage && dayjs(corpPackage.packageStartTime).format('YYYY-MM-DD') }}
                            </el-form-item>
                        </el-col>
                        <el-col v-bind="colAttr">
                            <el-form-item label="到期时间">
                                {{ corpPackage && dayjs(corpPackage.packageEndTime).format('YYYY-MM-DD') }}
                            </el-form-item>
                        </el-col>
                        <el-col v-bind="colAttr">
                            <el-form-item label="剩余天数">
                                {{ corpPackage && dayjs(corpPackage.packageEndTime).diff(dayjs(), 'day') }}
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row>
                        <el-col :span="24">
                            <el-form-item label="员工账号已经使用数/上限数" label-width="220px">
                                <div v-if="corpPackage">
                                    {{ corpMemberAndcustomtorCount.corpMemberCount }} / {{ corpPackage.accountCount +
                                    corpPackage.giveAccountCount }}
                                </div>
                            </el-form-item>
                        </el-col>
                        <el-col :span="24">
                            <el-form-item label="客户档案已有档案数/上限数" label-width="220px">
                                <div v-if="corpPackage">
                                    {{ corpMemberAndcustomtorCount.customtorCount }} / {{ corpPackage.customerCount +
                                    corpPackage.giveCustomerCount }}
                                </div>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-divider border-style="dashed" />
                    <div title-bar class="pb-10px">其他信息</div>
                    <el-form-item label="团队首页主题色" label-width="140">
                        <div class="flex items-center">
                            <div class="mr-10px h-20px w-20px" :style="{ backgroundColor: themeColor }"></div>
                            <el-text type="primary cursor-pointer" @click="show()">修改</el-text>
                        </div>
                    </el-form-item>
                </el-form>
            </div>
        </layout-main>
    </my-layout>
    <el-dialog v-model="addCorpNameDialogVisible" title="修改机构名称" draggable :width="500">
        <el-form class="pt-20px" :label-width="100">
            <el-form-item label="机构名称">
                <el-input class="w-300px" v-model="corpName" type="text" placeholder="请输入机构名称" />
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer text-center">
                <el-button class="w-100px" @click="addCorpNameDialogVisible = false">取消</el-button>
                <el-button class="w-100px" type="primary" @click="editCorpNameSubmit()">
                    确定
                </el-button>
            </div>
        </template>
    </el-dialog>
    <cropper-modal title="修改机构logo" :src="customLogo" :handle="changeLogo" :visible="cropperVisible"
        @close="closeModal" />
    <theme-color-modal :width="width" :visible="visible" @close="close" />
    <corp-pic-modal :width="width" :visible="corpPictureVisible" @close="closeCorpPicture" />
</template>
<script setup>
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import dayjs from "dayjs";
import { ref, reactive, computed, onMounted } from "vue";
import { getSuperAdmin as getSuperAdminUrl, getCorpMemberAndCustomorCount as getCorpMemberAndCustomorCountUrl, updateCorpInfo } from "@/api/corp.js"
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import { storeToRefs } from 'pinia';
import { memberStore } from "@/store/member";
import useModal from '@/hooks/useModal';
import CropperModal from '@/components/cropper-modal/cropper-modal.vue';
import ThemeColorModal from './theme-color-modal.vue';
import corpPicModal from "./corp-pic-modal.vue";

const { close, show, visible, width } = useModal(); //  设置主题色弹窗
const { close: closeModal, show: showModal, visible: cropperVisible } = useModal(); //  上传logo弹窗
const { close: closeCorpPicture, show: showCorpPicture, visible: corpPictureVisible } = useModal(); //  上传logo弹窗

const store = memberStore();
const { changeCorpInfo } = store;
const { corpInfo, themeColor, customLogo, corpPic } = storeToRefs(store);
const corpPackage = ref(corpInfo.value.package);
const router = useRouter();
const corpName = ref('');
const addCorpNameDialogVisible = ref(false);
const adminUsers = ref([]);
const corpMemberAndcustomtorCount = ref({
    customtorCount: 0,
    corpMemberCount: 0
});
getSuperAdmin();
getCorpMemberAndCustomorCount();
const colAttr = { span: 24, md: 24, lg: 24 }
async function getSuperAdmin() {
    let { success, data } = await getSuperAdminUrl();
    if (success) {
        adminUsers.value = data.data;
    }
}
async function getCorpMemberAndCustomorCount() {
    let { data, success } = await getCorpMemberAndCustomorCountUrl();
    if (success) {
        corpMemberAndcustomtorCount.value = data;
    }
}
function readPackage() {
    router.push({
        name: "PACKAGELIST"
    })
}
async function editCorpName() {
    addCorpNameDialogVisible.value = true;
    corpName.value = corpInfo.value.corp_name;
}

async function changeLogo(url) {
    const { success } = await updateCorpInfo({ customLogo: url })
    if (success) {
        ElMessage.success('修改成功');
        changeCorpInfo({ key: 'customLogo', value: url })
    } else {
        return Promise.reject()
    }
}
async function editCorpNameSubmit() {
    const { success } = await updateCorpInfo({ corp_name: corpName.value })
    if (success) {
        ElMessage.success('修改成功');
        changeCorpInfo({ key: 'corp_name', value: corpName.value })
        addCorpNameDialogVisible.value = false;
    } else {
        return Promise.reject()
    }
}

const showViewer = ref(false)

</script>
<style lang="scss" scoped>
.file-content {
    height: 300px;
    overflow-y: auto;

    .file-cell {
        display: flex;
        justify-content: space-between;
        padding: 0px 20px 10px 20px;
        align-items: center;
        border-bottom: 1px solid #eee;

        .file-cell-name {
            .t_1 {
                color: #000;
                font-weight: 600;
                align-items: center;
                padding-right: 10px;
                cursor: pointer;
            }

            .t_2 {
                font-size: 12px;
                padding-top: 10px;
            }
        }

        .file-cell-radio {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }
    }
}
</style>