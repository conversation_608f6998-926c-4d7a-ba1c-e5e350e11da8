<template>
  <div bg-fff common-shadow class="p-15px rounded-8px" v-loading="loading">
    <div active-title-bar class="text-16px leading-24px font-semibold">数据详情</div>
    <div class="flex items-center justify-between">
      <el-button-group class="my-15px flex-shrink-0">
        <el-button :type="status == 'todo' ? 'primary' : ''" :plain="status !== 'todo'" @click="changeStatus('todo')">未执行员工数据</el-button>
        <el-button :type="status == 'done' ? 'primary' : ''" :plain="status !== 'done'" @click="changeStatus('done')">已执行员工数据</el-button>
      </el-button-group>
    </div>
    <el-table stripe ref="multipleTableRef" :data="tableData" empty-text="暂无数据" @selection-change="handleSelectionChange">
      <el-table-column class-name="pl-15px" prop="customer" label="员工">
        <template #default="scope">
          <ww-user :openid="scope.row.executor" />
        </template>
      </el-table-column>
      <el-table-column prop="customersCount" label="预计发送客户数" />
      <template v-if="status === 'done'">
        <el-table-column prop="sendSuccessCount" label="发送成功客户数" />
        <el-table-column prop="sendFileCount" label="发送失败客户数">
          <template #default="scope">
            <el-text type="primary" class="cursor-pointer hover:underline" @click="selectFailCustomer(scope.row.sendFileList)">{{ scope.row.sendFileCount }}</el-text>
          </template>
        </el-table-column>
      </template>
      <el-table-column v-else fixed="right" class-name="el-table-fixed-column--shadow last-td" label="操作" width="120" v-if="groupmsg.executeStatus === 'doing'">
        <template #default="scope">
          <el-text type="primary" class="cursor-pointer" @click="remindSend(scope.row)">提醒员工执行</el-text>
        </template>
      </el-table-column>
    </el-table>
    <div class="pt-15px px-15px flex justify-end border-t border-gray-200">
      <el-pagination layout="prev, pager, next, sizes, jumper" :current-page="page" :page-size="pageSize" :page-sizes="[10, 20, 50]" :total="total" @current-change="changePage" @size-change="changeSize"></el-pagination>
    </div>
  </div>
  <fail-table :visible="tableModalVisible" :width="width" @close="hidden" :failCustomers="failCustomers" />
</template>
<script setup>
import { computed, ref } from "vue";
import { ElMessage } from "element-plus";
import useModal from "@/hooks/useModal";
import failTable from "./send-fail-table";
import WwUser from "@/components/ww-user/index.vue";
import { remindGroupmsgSend } from "../../api/groupmsg.js";
const props = defineProps({
  memberSendResult: {
    type: Array,
    default: () => [],
  },
  groupmsg: {
    type: Object,
    default: () => ({}),
  },
});
const { close: hidden, show: display, visible: tableModalVisible, width } = useModal(640); //  选择客户弹窗
const page = ref(1);
const pageSize = ref(10);
const failCustomers = ref([]); // 发送失败的客户

const status = ref("todo");
function changeStatus(val) {
  status.value = val;
}

const executeSuccessList = computed(() => {
  return props.memberSendResult.filter((item) => item.unexecutedCount === 0 && item.customersCount > 0);
});

const noExecuteList = computed(() => {
  return props.memberSendResult.filter((item) => item.unexecutedCount > 0);
});

const noNeedExecuteList = computed(() => {
  return props.memberSendResult.filter((item) => item.unexecutedCount === 0 && item.customersCount === 0);
});

const tableData = computed(() => {
  const list = status.value === "todo" ? noExecuteList.value : executeSuccessList.value;
  // 对list 进行分页
  return list.slice((page.value - 1) * pageSize.value, page.value * pageSize.value);
});

const total = computed(() => {
  return status.value === "todo" ? noExecuteList.value.length : executeSuccessList.value.length;
});

const selections = ref([]);
function handleSelectionChange(val) {
  selections.value = val;
}
function send() {
  if (selections.value.length === 0) return ElMessage.warning("请选择需要重发的客户");
}

function changePage(val) {
  page.value = val;
}
function changeSize(val) {
  pageSize.value = val;
}

function selectFailCustomer(list) {
  failCustomers.value = list;
  display();
}
const loading = ref(false);
async function remindSend(item) {
  loading.value = true;
  await remindGroupmsgSend(item.msgid);
  loading.value = false;
}
</script>
<style lang="scss" scoped></style>
