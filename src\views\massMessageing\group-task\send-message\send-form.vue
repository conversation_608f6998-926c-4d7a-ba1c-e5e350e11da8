<template>
  <el-form class="px-15px pt-15px" label-position="top" v-loading="loading">
    <el-form-item>
      <template #label>
        <div active-title-bar color-normal class="text-16px py-4px font-semibold">发送类型</div>
      </template>
      <div>
        <el-radio-group v-model="form.sendType">
          <el-radio label="MESSAGE">
            <span color-normal class="text-15px font-semibold">群发客户微信消息</span>
          </el-radio>
          <!-- <el-radio label="FRIEND">群发朋友圈</el-radio> -->
        </el-radio-group>
        <div class="pl-22px text-13px text-red-500">
          {{ tishiTitle }}
        </div>
      </div>
    </el-form-item>
    <el-form-item class="mb-10px" v-if="sendTaskType !== 'MINE'">
      <template #label>
        <div active-title-bar color-normal class="text-16px py-4px font-semibold">任务名称</div>
      </template>
      <div class="flex-grow">
        <el-input class="mt-10px" placeholder="请输入任务名称" v-model="form.taskName"></el-input>
      </div>
    </el-form-item>
    <el-form-item>
      <template #label>
        <div active-title-bar color-normal class="text-16px py-4px font-semibold">发送内容</div>
      </template>
      <div class="flex-grow">
        <el-input type="textarea" :autosize="{ minRows: 7, maxRows: 12 }" resize="none" placeholder="请输入内容" v-model="form.content" show-word-limit :maxlength="3000" @input="inputContent"></el-input>
        <div class="mt-10px">
          <span class="text-14px text-gray-500">附件：</span>
          <el-button plain type="primary" size="small" :icon="Plus" @click="chooseFile('image/*')">图片</el-button>
          <el-button plain type="primary" size="small" :icon="Plus" @click="chooseFile('video/*')">视频</el-button>
          <!-- <el-button plain type="primary" size="small" :icon="Plus" @click="addFileUrl">链接</el-button> -->
          <el-button plain type="primary" size="small" :icon="Plus" @click="addArticle">宣教文章</el-button>
          <!-- <el-popover placement="top-start" :width="270" trigger="hover">
            <template #reference>
              <el-button type="primary" size="small" :icon="Plus">从内容中心选择</el-button>
            </template>
            <template #default>
              <div class="flex">
                <el-button mr-15 plain type="primary" :icon="Plus" @click="choose('article')">宣教文章</el-button>
                <el-button mr-15 plain type="primary" :icon="Plus" @click="choose('questionnaire')">问卷调查</el-button>
              </div>
            </template>
          </el-popover> -->
        </div>
        <div v-if="fileList.length" class="mt-10px">
          <div v-for="(i, idx) in showFileList" :key="idx" class="flex items-center py-6px px-12px overflow-hidden bg-gray-100 rounded-8px" :class="idx > 0 ? 'mt-10px' : ''">
            <div color-normal class="flex-grow h-24px leading-24px w-0 truncate">
              <span v-if="i.typeStr" class="font-semibold text-14px">{{ i.typeStr }}</span>
              <span v-if="i.file && i.file.name">{{ i.file.name }}</span>
            </div>
            <el-icon :size="16" @click="removeFile(idx)" class="pointer">
              <CloseBold />
            </el-icon>
          </div>
        </div>
      </div>
    </el-form-item>
    <el-form-item>
      <template #label>
        <div active-title-bar color-normal class="text-16px py-4px font-semibold">发送客户</div>
      </template>
      <div class="flex-grow flex justify-end items-center px-12px py-4px border cursor-pointer border-gray-100 hover:border-gray-300 rounded-8px" @click="openPicker">
        <div class="flex-grow w-0 truncate" :class="customerStr ? '' : 'text-gray-400'">
          {{ customerStr || "请选择发送范围" }}
        </div>
        <el-icon size="18" class="ml-10px text-gray-400">
          <ArrowRight />
        </el-icon>
      </div>
    </el-form-item>
    <el-form-item v-if="sendTaskType !== 'MINE'">
      <template #label>
        <div active-title-bar class="text-16px py-4px font-semibold">任务起止时间</div>
      </template>
      <div class="flex items-center">
        <el-date-picker v-model="form.startTaskDate" placeholder="开始时间" :disabled-date="startPickerOptions" @change="handleStartDateChange" />
        <div class="w-20px border-t border-gray-500 mx-10px"></div>
        <el-date-picker v-model="form.endTaskDate" placeholder="结束时间" :disabled-date="endPickerOptions" />
      </div>
    </el-form-item>
  </el-form>
  <input class="w-0 h-0 invisible" type="file" ref="uploadImageRef" accept="image/*" @change="handleSelect($event)" />
  <input class="w-0 h-0 invisible" type="file" ref="uploadVideoRef" accept="video/*" @change="handleSelect($event)" />
  <customer-picker :customers="form.customers" :width="width" :visible="visible" @close="close" @confirm="selectCustomers" :teamIds="teamIds" />
  <file-url :visible="fileUrlVisible" @cancel="fileUrlVisible = false" @submit="submitUrl"></file-url>
  <range-picker :width="rangeWidth" :visible="rangeVisible" @close="rangeClose" @confirm="selectCustomers" :teamIds="teamIds" :sendTaskType="sendTaskType" :userIds="form.userIds" />
  <add-file ref="addFileRefs" @onGetSelectFile="getSelectFile"></add-file>
</template>
<script setup>
import { computed, ref, onUnmounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import useModal from "@/hooks/useModal";
import customerPicker from "./customer-picker.vue";
import rangePicker from "./range-picker.vue";
import { Plus, CloseBold } from "@element-plus/icons-vue";
import fileUrl from "@/components/file-url";
import { tagsStore } from "@/store/tags";
import { useRoute, useRouter } from "vue-router";
import addFile from "@/views/pannedEvent/components/addFile.vue";
import dayjs from "dayjs";
import { updateFile } from "@/api/uploadFIle.js";
import { createGroupmsgTask } from "../../api/groupmsg.js";
const route = useRoute();
const router = useRouter();
const sendTaskType = ref(route.params.sendTaskType);
const { removeTag } = tagsStore();
const uploadImageRef = ref();
const uploadVideoRef = ref();
const addFileRefs = ref("");
const { close, show, visible, width } = useModal(); //  选择客户弹窗
const { close: rangeClose, show: rangeShow, visible: rangeVisible, width: rangeWidth } = useModal(); //  选择客户弹窗
const emits = defineEmits(["uploadFile", "closeLoading"]);
const form = ref({
  sendType: "MESSAGE",
  content: "",
  teamId: "",
  teamName: "",
  taskName: "",
  customers: [],
  startTaskDate: "",
  endTaskDate: "",
});
const loading = ref(false);
const teamIds = ref([]);
const tishiTitle = computed(() => {
  if (sendTaskType.value === "MINE") {
    return "可将信息一键群发给我所有的客户。每位客户每天只能接受一条群发消息。";
  } else if (sendTaskType.value === "TEAM") {
    return "可统一创建内容并通知团队成员发送给团队客户。每位客户每天只能接受一条群发消息。";
  } else if (sendTaskType.value === "CORP") {
    return "可统一创建内容并通知机构成员发送给机构客户。每位客户每天只能接受一条群发消息。";
  }
});
function startPickerOptions(date) {
  return dayjs(date).endOf("day").isBefore(dayjs());
}
const endPickerOptions = computed(() => {
  return (time) => {
    if (form.value.startTaskDate) {
      const start = dayjs(form.value.startTaskDate);
      const end = dayjs(form.value.startTaskDate).add(2, "day");
      return dayjs(time).isBefore(start) || dayjs(time).isAfter(end);
    } else {
      return dayjs(time).endOf("day").isBefore(dayjs());
    }
  };
});
const fileList = ref([]);
const showFileList = computed(() =>
  fileList.value.map((i) => {
    let typeStr = "";
    const type = i.type;
    const fileType = i.file && typeof i.file.type === "string" ? i.file.type : "";
    // const fileType = i.file && typeof i.file.type === 'string' && i.file.type.includes('video')
    if (fileType.includes("video")) typeStr = "【视频】";
    else if (fileType.includes("image")) typeStr = "【图片】";
    else if (fileType === "article") typeStr = "【文章】";
    else if (type === "link") typeStr = "【链接】";
    return { ...i, typeStr };
  })
);
const addArticle = (file) => {
  addFileRefs.value.open("article", {});
};
function getSelectFile(file) {
  if (fileList.value.length >= 9) {
    ElMessage.info("最多上传9个附件");
    return;
  }
  const { url, name, summary } = file;
  fileList.value.push({
    URL: "https://796f-youcan-clouddev-1-8ewcqf31dbb2b5-1317294507.tcb.qcloud.la/other/19-%E9%97%AE%E5%8D%B7.png?sign=55a4cd77c418b2c548b65792a2cf6bce&t=1701328694",
    type: "link",
    file: {
      name,
      type: "article",
      subtitle: summary,
      url,
    },
  });
  emits("uploadFile", fileList.value, form.value.content);
}

const fileUrlVisible = ref(false);
function addFileUrl() {
  if (fileList.value.length >= 9) {
    ElMessage.info("最多上传9个附件");
    return;
  }
  fileUrlVisible.value = !fileUrlVisible.value;
}
function removeFile(index) {
  fileList.value.splice(index, 1);
  emits("uploadFile", fileList.value, form.value.content);
}
async function chooseFile(type) {
  if (fileList.value.length >= 9) {
    ElMessage.info("最多上传9个附件");
    return;
  }
  if (type === "image/*") {
    uploadImageRef.value.click();
  } else {
    uploadVideoRef.value.click();
  }
}
function handleStartDateChange(val) {
  form.value.startTaskDate = val;
  form.value.endTaskDate = "";
}
async function selectCustomers(data) {
  visible.value = false;
  rangeVisible.value = false;
  const { customers, teamIds: ids, userIds } = data;
  if (userIds) {
    form.value.userIds = userIds;
  }
  if (ids) {
    teamIds.value = ids;
  }
  form.value.customers = customers;
}
async function handleSelect(e) {
  const [file] = e.target.files;
  if (fileList.value.some((i) => i.file.name === file.name)) {
    ElMessage.info("附件已存在");
    return;
  }
  e.target.value = "";
  if (!file) return;
  let type = "";
  if (file.type.includes("image")) {
    // 限制文件大小 10M
    type = "image";
    if (file.size > 10485760) {
      ElMessage.info("图片大小不能超过10MB");
      return;
    }
  } else if (file.type.includes("video")) {
    // 限制文件大小 10M
    type = "video";
    if (file.size > 10485760) {
      ElMessage.info("视频大小不能超过10MB");
      return;
    }
  }
  loading.value = true;
  const { name } = file;
  let { media_id, success, message } = await updateFile(name, file, localStorage.getItem("corpId"), type, "mediaId");
  loading.value = false;
  if (!success) {
    return ElMessage.error(message);
  }
  fileList.value.push({
    type,
    URL: URL.createObjectURL(file),
    file,
    mediaId: media_id,
  });
  emits("uploadFile", fileList.value, form.value.content);
}
function submitUrl(obj) {
  fileList.value.push(obj);
  fileUrlVisible.value = false;
  emits("uploadFile", fileList.value, form.value.content);
}
const customerStr = computed(() => {
  const { customers = [] } = form.value;
  return `发送给${customers.length}位客户`;
});
function verify() {
  if (!form.value.sendType) return "请选择发送类型";
  if (!form.value.content) return "请输入发送内容";
  if (form.value.customers.length === 0) return "预计发送客户数为不能为0";
  if (sendTaskType.value != "MINE" && (!form.value.startTaskDate || !form.value.endTaskDate)) return "请选择任务起止时间";
  return true;
}
function inputContent() {
  emits("uploadFile", fileList.value, form.value.content);
}
async function submit() {
  const data = verify();
  if (data === true) {
    if (!form.value.startTaskDate) {
      form.value.startTaskDate = dayjs().format("YYYY-MM-DD");
      form.value.endTaskDate = dayjs().add(3, "day").format("YYYY-MM-DD");
    }
    try {
      await ElMessageBox.alert(`将于<span style="color: #006eff;">${dayjs(form.value.startTaskDate).format("YYYY年MM月DD日")}</span>通知员工<br>预计发送客户数：<span style="color: red;">${form.value.customers.length} </span> 位`, "确定创建任务？", {
        confirmButtonText: "确定",
        type: "warning",
        dangerouslyUseHTMLString: true,
      });
      form.value.startTaskDate = dayjs(form.value.startTaskDate).valueOf();
      form.value.endTaskDate = dayjs(form.value.endTaskDate).valueOf();
      let params = {
        ...form.value,
        sendSource: sendTaskType.value,
        executor: localStorage.getItem("userId"),
        fileList: fileList.value,
      };
      params["teamIds"] = teamIds.value;
      let res = await createGroupmsgTask(params);
      emits("closeLoading");
      if (res) {
        closeView();
      }
    } catch (error) {
      emits("closeLoading");
      return;
    }
  } else {
    emits("closeLoading");
    ElMessage.warning(data);
  }
}
//  初始化
function closeView() {
  removeTag(route.fullPath);
  // 跳转到列表页
  if (sendTaskType.value === "MINE") {
    router.push({ name: "GROUPTASK" });
  } else if (sendTaskType.value === "CORP") {
    router.push({ name: "CORPGROUPTASK" });
  } else if (sendTaskType.value === "TEAM") {
    router.push({ name: "TEAMGROUPTASK" });
  }
}
// 发送客户
function openPicker() {
  if (sendTaskType.value === "MINE") {
    show();
  } else {
    rangeShow();
  }
}
defineExpose({
  submit,
  closeView,
});

// 在页面消失前调用
onUnmounted(() => {
  removeTag(`/SENDMESSAGE/${sendTaskType.value}`);
});
</script>
<style lang="scss" scoped></style>
