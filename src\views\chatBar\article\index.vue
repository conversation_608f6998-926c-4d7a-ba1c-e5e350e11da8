<template>
  <div h-full flex bg-fff v-loading="loading">
    <template v-if="cates.length">
      <div flex-shrink-0 h-full class="w-120px border-left">
        <el-scrollbar class="h-full">
          <div v-for="item in cates" :key="item._id" px-15 py-10 font-12 border-bottom @click="checkedCate = item">
            <span v-if="checkedCate && checkedCate._id === item._id" color-primary>{{ item.name }}</span>
            <span v-else>{{ item.name }}</span>
          </div>
        </el-scrollbar>
      </div>
      <div flex-grow h-full>
        <el-scrollbar h-full ref="scrollRef" @scroll="handleScroll($event)">
          <div ref="listRef">
            <div v-for="item in list" :key="item._id" flex items-center px-15 py-10 border-bottom>
              <div class="w-0px truncate mr-10px" font-12 flex-grow>
                {{ item.title }}
              </div>
              <el-button text type="primary" size="small" @click="sendLink(item)">发送</el-button>
            </div>
            <empty-data v-if="list.length === 0" title="暂无文章"></empty-data>
          </div>
        </el-scrollbar>
      </div>
    </template>
    <div v-else h-full w-full items-center justify-center>
      <empty-data :top="0" title="暂无文章"></empty-data>
    </div>
  </div>
</template>
<script setup>
import { onMounted, ref, watch } from "vue";
import { useDebounceFn } from "@vueuse/core";
import { ElMessage } from "element-plus";
import { getArticleList, getArticleCates } from "@/api/knowledgeBase";
import EmptyData from "@/components/empty-data.vue";
import { memberStore } from "@/store/member";
import { createServiceRecord } from "@/utils/service";
import { getNameByexternalUserId } from "@/utils/common";
import { jsSDKConfig, getCurExternalContact } from "@/utils/jssdk";
import { envjudgwxWork, isURL } from "@/utils/common";
import * as ww from "@/libs/@wecom/jssdk/wecom.esm.js";
// import { setArticleCate, removeArticleCate, getArticleCates, getArticleList, toggleArticleStatus, removeArticle } from '@/api/knowledgeBase'

const cates = ref([]);
const checkedCate = ref({});
const list = ref([]);
const loading = ref(false);
const page = ref(1);
const total = ref(0);

onMounted(() => getCates());
async function getList() {
  loading.value = true;
  const { data, message, success } = await getArticleList({ corpId: localStorage.getItem("corpId"), page: page.value, pageSize: 30, enable: true, cateId: checkedCate.value._id });
  const { list: articleList = [], total: count = 0 } = data;
  list.value = articleList;
  total.value = count;
  loading.value = false;
  if (!success) ElMessage.error(message);
}
const scrollRef = ref();
const listRef = ref();
const handleScroll = useDebounceFn(function scroll({ scrollTop }) {
  if (loading.value) return;
  const isBottom = scrollRef.value.wrapRef.scrollHeight - scrollRef.value.wrapRef.clientHeight - scrollTop <= 20;
  if (isBottom) {
    page.value += page.value;
    getList();
  }
});
async function sendLink({ link, title }) {
  const desc = "宣教文章";
  createServiceRecordAction(link, title);

  ww.sendChatMessage({
    msgtype: isURL(link) ? "news" : "text", //消息类型，必填
    enterChat: true,
    text: {
      content: link, //文本内容
    },
    news: {
      link, //H5消息页面url 必填
      title,
      desc, //H5消息摘要
      imgUrl: "https://796f-youcan-clouddev-1-8ewcqf31dbb2b5-1317294507.tcb.qcloud.la/other/19-%E9%97%AE%E5%8D%B7.png?sign=55a4cd77c418b2c548b65792a2cf6bce&t=1701328694",
    },
    success: function (res) {
      //发送成功
      ElMessage.success("发送成功");
    },
    fail: function (res) {
      //发送失败
      ElMessage.error("发送失败");
    },
  });
}
async function createServiceRecordAction(link, title) {
  const { memberInfo } = memberStore();
  const { anotherName } = memberInfo;
  let externalUserId = "";
  if (envjudgwxWork()) {
    await jsSDKConfig();
    externalUserId = await getCurExternalContact();
  }
  const { name } = await getNameByexternalUserId(externalUserId);
  let item = {
    taskContent: `${anotherName}发送宣教"${title}"给客户${name}`,
    executionTime: new Date().getTime(),
    customerId: "",
    executeTeamId: "",
    eventType: "ContentReminder",
    customerName: name,
    pannedEventSendFile: {
      name: title,
      url: link,
      type: "article",
    },
  };
  await createServiceRecord(item);
}

async function getCates() {
  loading.value = true;
  const {
    success,
    data: { list },
    message,
  } = await getArticleCates(localStorage.getItem("corpId"));
  cates.value = list;
  checkedCate.value = cates.value.find((i) => i._id === checkedCate.value._id) || cates.value[0] || {};
  if (!success) ElMessage.error(message);
  loading.value = false;
}
watch(checkedCate, (n) => {
  if (n) {
    page.value = 1;
    getList();
  } else {
    list.value = [];
  }
});
</script>
<style>
.border-left {
  border: 1px solid #eee;
}
</style>