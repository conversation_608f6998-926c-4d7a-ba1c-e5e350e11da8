<template>
  <el-upload v-model:file-list="fileList" ref="uploadRef" action="" :list-type="props.listType" :class="props.classnames" :on-preview="handlePictureCardPreview" :on-remove="handleRemove" :limit="props.limit" :showFileList="false" :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload" :auto-upload="false" :on-change="handFileChange" :show-file-list="props.showFileList" :accept="props.fileType" :multiple="props.multiple">
    <slot>
      <el-icon>
        <Plus />
      </el-icon>
    </slot>
  </el-upload>

  <el-dialog v-model="dialogVisible">
    <img w-full :src="dialogImageUrl" alt="Preview Image" />
  </el-dialog>
</template>

<script setup>
import { ref } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";

const dialogImageUrl = ref("");
const dialogVisible = ref(false);
const emit = defineEmits(["hand-file-change"]);
let fileList = ref([]);
let files = [];
const props = defineProps({
  classnames: { type: String, default: "" },
  fileType: {
    type: String,
    default: "",
  },
  listType: {
    type: String,
    default: "picture-card",
  },
  path: {
    type: String,
    default: "",
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  limit: {
    type: Number,
    default: 1,
  },
  showFileList: {
    type: Boolean,
    default: true,
  },
  fileList: {
    type: Array,
    default: [],
  },
});
fileList.value = props.fileList;

// 格式化文件大小的函数
const formatFileSize = (bytes) => {
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// 图片压缩函数
async function compressImage(file, maxSizeMB = 1) {
  return new Promise((resolve, reject) => {
    if (!file.type.includes("image/")) {
      resolve(file);
      return;
    }

    const maxSize = maxSizeMB * 1024 * 1024; // 转换为字节
    if (file.size <= maxSize) {
      resolve(file);
      return;
    }

    console.log(`开始压缩图片，原始大小: ${formatFileSize(file.size)}`);

    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = (e) => {
      const img = new Image();
      img.src = e.target.result;
      img.onload = () => {
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");

        // 保持原始宽高比
        let width = img.width;
        let height = img.height;

        // 限制最大尺寸
        const MAX_WIDTH = 1920;
        const MAX_HEIGHT = 1920;
        if (width > MAX_WIDTH) {
          height = (MAX_WIDTH / width) * height;
          width = MAX_WIDTH;
        }
        if (height > MAX_HEIGHT) {
          width = (MAX_HEIGHT / height) * width;
          height = MAX_HEIGHT;
        }

        canvas.width = width;
        canvas.height = height;

        // 绘制图片到canvas
        ctx.drawImage(img, 0, 0, width, height);

        // 从0.9开始逐步降低图片质量，直到满足大小要求或达到最小质量0.3
        let quality = 0.9;
        const minQuality = 0.3;

        const compressLoop = () => {
          const dataURL = canvas.toDataURL(file.type, quality);
          const blobBin = atob(dataURL.split(",")[1]);
          const array = [];
          for (let i = 0; i < blobBin.length; i++) {
            array.push(blobBin.charCodeAt(i));
          }
          const blob = new Blob([new Uint8Array(array)], { type: file.type });

          if (blob.size <= maxSize || quality <= minQuality) {
            // 创建新的File对象
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: new Date().getTime(),
            });

            console.log(`压缩完成，压缩后大小: ${formatFileSize(compressedFile.size)}`);
            console.log(`压缩比例: ${((1 - compressedFile.size / file.size) * 100).toFixed(2)}%`);
            resolve(compressedFile);
          } else {
            quality -= 0.1;
            console.log(`当前压缩质量不足，降低质量至: ${quality.toFixed(1)}`);
            compressLoop();
          }
        };

        compressLoop();
      };
      img.onerror = (error) => {
        reject(error);
        ElMessage.error("图片压缩失败");
      };
    };
    reader.onerror = (error) => {
      reject(error);
      ElMessage.error("图片读取失败");
    };
  });
}

const handFileChange = async (e) => {
  try {
    const file = e.raw;

    // 压缩图片
    const compressedFile = await compressImage(file, 1);

    // 用压缩后的文件替换原始文件
    const updatedFileInfo = { ...e, raw: compressedFile };
    files.push(updatedFileInfo);

    emit("hand-file-change", files, {
      fileName: file.name,
      originalSize: file.size,
      compressedSize: compressedFile.size,
      compressionRatio: ((1 - compressedFile.size / file.size) * 100).toFixed(2) + "%",
    });
  } catch (error) {
    console.error("图片处理失败:", error);
    ElMessage.error("图片处理失败");
  }
};

function handleRemove(e) {
  files = files.filter((item) => item.uid !== e.uid);
  emit("hand-file-change", files);
}

const handlePictureCardPreview = (uploadFile) => {
  dialogImageUrl.value = uploadFile.url;
  dialogVisible.value = true;
};

const beforeAvatarUpload = (file) => {
  // 限制文件大小为10MB
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    ElMessage.error("上传图片大小不能超过 10MB!");
    return false;
  }
  return true;
};

const handleAvatarSuccess = () => {
  // 上传成功的回调
};

const uploadRef = ref();
defineExpose({
  clearFiles: () => {
    uploadRef.value.clearFiles();
    fileList.value = [];
    files = [];
  },
});
</script>

<style lang="scss" scoped>
/* 后面的提示文字颜色 */
[type="file"] {
  font-size: 0;
  width: 100px;
  height: 100px;
  opacity: 0;
}

.add-btn {
  margin-top: 10px;
  margin-left: 10px;
  width: 100px;
  height: 100px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: #006eff;
  border: 1px solid #006eff;
  text-align: center;
  border-radius: 8px;
  cursor: pointer;
  box-sizing: border-box;

  .inView {
    z-index: -1;
  }
}

/* 主按钮的样式自定义 */
::file-selector-button {
  border: none;
  outline: none;
  background: none;
  box-shadow: none;
}
</style>
