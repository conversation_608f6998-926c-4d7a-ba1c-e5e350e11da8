{"name": "create", "private": true, "version": "0.0.0", "scripts": {"dev": "vite --mode  dev", "deploy": "vite --mode deploy", "hz": "vite --mode hzdev", "uat": "vite --mode uat", "pro": "vite --mode pro", "deployPro": "vite --mode deployPro", "build:deploy": "vite build --mode deploy", "build:dev": "vite build --mode deploy", "build:uat": "vite build --mode uat", "build:pro": "vite build --mode pro", "build:hz": "vite build --mode hz", "build:deployPro": "vite build --mode deployPro", "preview": "vite preview"}, "dependencies": {"@babel/polyfill": "^7.12.1", "@chenfengyuan/vue-qrcode": "^2.0.0", "@vueuse/core": "^10.6.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.7.9", "babel-eslint": "^10.1.0", "babel-loader": "^9.1.3", "bignumber.js": "^9.1.2", "cheerio": "^1.0.0-rc.12", "copy-to-clipboard": "^3.3.3", "cornerstone-core": "^2.6.1", "cornerstone-math": "^0.1.10", "cornerstone-tools": "^6.0.10", "cornerstone-wado-image-loader": "^4.13.2", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "dicom-parser": "^1.8.21", "echarts": "^5.4.3", "element-plus": "^2.4.1", "eslint": "^6.7.2", "eslint-plugin-vue": "^7.0.0", "file-saver": "^2.0.5", "form-data": "^4.0.0", "jsencrypt": "^3.3.2", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "node-sass": "^9.0.0", "pinia": "2.0.36", "pinia-plugin-persist": "^1.0.0", "qrcode": "^1.5.3", "sass": "^1.67.0", "sass-loader": "^13.3.2", "solarlunar": "^2.0.7", "three": "^0.175.0", "vue": "^3.3.4", "vue-cropper": "^1.1.2", "vue-router": "^4.2.4", "vue-virtual-scroller": "^2.0.0-beta.8", "vue3-count-to": "^1.1.2", "vuedraggable": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.24.3", "@babel/plugin-transform-runtime": "^7.24.3", "@babel/preset-env": "^7.24.3", "@vitejs/plugin-vue": "^4.2.3", "babel-eslint": "^10.1.0", "babel-polyfill": "^6.26.0", "es6-promise": "^4.2.8", "eslint": "^6.7.2", "eslint-plugin-vue": "^7.0.0", "node-sass": "^9.0.0", "pinia": "2.0.36", "pinia-plugin-persist": "^1.0.0", "rollup-plugin-external-globals": "^0.9.0", "sass": "^1.67.0", "sass-loader": "^13.3.2", "terser": "^5.29.2", "vite": "^4.4.5", "vite-plugin-cdn-import": "^0.3.5", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-windicss": "^1.9.2", "vue-router": "^4.2.4", "windicss": "^3.5.6"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 11"]}