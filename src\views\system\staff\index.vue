<template>
  <my-layout :radius="0">
    <layout-item class="mb-15px">
      <account-size :corpMemberAndcustomtorCount="corpMemberAndcustomtorCount"></account-size>
    </layout-item>
    <layout-main :scroll="false">
      <div flex h-full>
        <staff-list flex-shrink-0 mr-15 @select-member="onSelectMember" :selectMember="selectMember"
          @update-success="updateSuccess" :rolesMemberList="rolesMemberList" :notOpenedAccount="notOpenedAccount"
          :corpMemberAndcustomtorCount="corpMemberAndcustomtorCount"></staff-list>
        <staff-edit :staff="selectMember" class="flex-grow" @get-corp-member-info="onGetCorpMemberInfo"
          @update-success="updateSuccess" @back-frist-member="onBackFristMember" :addType="addType"
          :transferUserId="transferUserId" :corpMemberAndcustomtorCount="corpMemberAndcustomtorCount"></staff-edit>
      </div>
    </layout-main>
  </my-layout>
</template>

<script setup>
import { ref } from "vue";
import { getOpenedAccount } from "@/api/corp.js";
import AccountSize from "./components/account-size.vue";
import StaffEdit from "./components/staff-edit.vue";
import StaffList from "./components/staff-list.vue";
import { getCorpMemberAndCustomorCount as getCorpMemberAndCustomorCountUrl, getNotOpenedAccount as getNotOpenedAccountUrl } from "@/api/corp.js";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
const selectMember = ref({});
const rolesMemberList = ref([]);
const corpMemberAndcustomtorCount = ref({});
const addType = ref("");
const notOpenedAccount = ref([]);
const transferUserId = ref("");
getData();
getNotOpenedAccount();
getCorpMemberAndCustomorCount();
async function getCorpMemberAndCustomorCount() {
  let { data, success } = await getCorpMemberAndCustomorCountUrl();
  if (success) {
    corpMemberAndcustomtorCount.value = data;
  }
}
function onSelectMember(item, type = "", userid = "") {
  selectMember.value = item;
  addType.value = type;
  transferUserId.value = userid;
}
function onGetCorpMemberInfo(item) {
  selectMember.value = item;
}
async function getData(userId) {
  let { data, success } = await getOpenedAccount();
  if (success && data.data) {
    rolesMemberList.value = data.data;
    rolesMemberList.value.sort((a, b) => {
      if (a.superAdminName && !b.superAdminName) {
        return -1;
      } else if (!a.superAdminName && b.superAdminName) {
        return 1;
      } else {
        return 0;
      }
    });
    rolesMemberList.value.sort((a, b) => {
      if (a.accountState !== "disable" && b.accountState === "disable") {
        return -1;
      } else if (a.accountState === "disable" && b.accountState !== "disable") {
        return 1;
      } else {
        return 0;
      }
    });
    if (userId) {
      selectMember.value = rolesMemberList.value.find((item) => item.userid === userId);
    } else {
      selectMember.value = data.data[0];
    }
  }
}
async function getNotOpenedAccount(userId, type) {
  let { success, data } = await getNotOpenedAccountUrl();
  if (success) {
    notOpenedAccount.value = data.data;
    if (!type && userId) selectMember.value = data.data.find((item) => item.userid === userId);
  }
}
function updateSuccess(userId, type) {
  addType.value = "";
  getData(userId);
  if (type) {
    getCorpMemberAndCustomorCount();
  }
  getNotOpenedAccount(userId, type);
}
function onBackFristMember() {
  selectMember.value = rolesMemberList.value[0];
}
</script>
