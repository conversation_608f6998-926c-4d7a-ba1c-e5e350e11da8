<template>
  <filter-item label="服务团队" :text="text">
    <checkbox-group v-model="teamId" :list="list" />
  </filter-item>
</template>
<script setup>
import { computed, ref } from 'vue';
import { storeToRefs } from 'pinia';
import { teamStore } from "@/store/team";
import FilterItem from './filter-item.vue';
import checkboxGroup from './checkbox-group.vue';

const { allTeams } = storeToRefs(teamStore());
const list = computed(() => allTeams.value.map(i => ({ label: i.name, value: i.teamId })))
const teamId = ref([]);
const text = computed(() => teamId.value && teamId.value.length ? `已选${teamId.value.length}项` : '');

function getParam() {
  return teamId.value.length ? { teamId: teamId.value } : {}
}

function reset() {
  teamId.value = []
}

defineExpose({
  getParam,
  reset
})
</script>
<style lang="scss" scoped></style>
