<template>
  <div class="h-full w-full flex-col flex items-center justify-center">
    <full-big-screen @close="close()" viewType="app" />
  </div>
</template>
<script setup>
import { useRoute } from "vue-router";
import { useWindowSize } from "@vueuse/core";
import { tagsStore } from "@/store/tags";

import fullBigScreen from "./full-screen/full-big-screen.vue";

const route = useRoute();
const { closeTag } = tagsStore();

function close() {
  closeTag({ path: route.path });
}
</script>
<style lang="scss" scoped>
</style>
