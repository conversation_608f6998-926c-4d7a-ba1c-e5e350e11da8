<template>
  <el-radio v-if="list.length" v-model="modelValue" class="w-full hover:text-blue-500" label="">全部</el-radio>
  <el-radio v-for="i in list" :key="i.value" v-model="modelValue" class="w-full hover:text-blue-500" :label="i.value">
    {{ i.label}}
  </el-radio>
</template>
<script setup>
import { computed, watch } from 'vue';
import { useVModels } from '@vueuse/core';

const props = defineProps({
  list: { type: Array, default: () => [] },
  modelValue: { type: String, default: '' },
  text: { type: String, default: '' }
})

const emits = defineEmits(['update:modelValue', 'update:text'])
const { modelValue, text } = useVModels(props, emits);
const label = computed(() => {
  const item = props.list.find(i => i.value === modelValue.value);
  return item ? item.label : '';
})

watch(label, n => text.value = n)
</script>
<style lang="scss" scoped></style>
