<template>
  <el-dialog :model-value="visible" :width="width" title="详情" @close="close">
    <el-scrollbar height="40vh">
      <el-form class="px-10px" label-suffix="：" :label-width="130" label-position="left">
        <el-row>
          <el-col :span="24">
            <el-form-item class="mb-0" label="任务名称">
              <span>{{ groupmsg.taskName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item class="mb-0" label="创建时间">
              <span>{{ groupmsg.createTime }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item class="mb-0" label="创建人">
              <ww-user :openid="groupmsg.creator" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item class="mb-0" label="任务时间">
              <span>{{ groupmsg.taskTime }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item class="mb-0" label="执行团队">
              <span>{{ teamNames }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item class="mb-0" label="需执行员工数">
              <span>{{ groupmsg.userIds.length }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item class="mb-0" label="预计发送客户数">
              <span>{{ groupmsg.customers.length }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item class="mb-0" label="发送内容">
              <div class="flex-grow pr-10px">
                <div class="pt-4px text-14px leading-24px whitespace-pre-line">{{ groupmsg.content }}</div>
                <div v-for="item in groupmsg.attachments" class="my-10px py-10px flex items-center pt-10px border-t border-gray-200">
                  <div class="w-0 flex-grow flex">
                    <div class="w-0 flex-grow text-14px flex" align-center>
                      <div class="leading-28px truncate" v-if="item.msgtype">[{{ templateType[item.msgtype] }}]</div>
                      <div class="text-gray-500 leading-20px truncate pl-10px">{{ item.title }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="text-center" v-loading="loading">
        <el-button class="w-100px" @click="close()">关闭</el-button>
        <el-button class="w-100px" type="primary" @click="stopTask()" v-if="groupmsg.executeStatus === 'doing'">停止任务</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref } from "vue";
import { getWecomGroupmesgList, stopGroupmsgTask } from "../../api/groupmsg.js";
import { teamStore } from "@/store/team";
import WwUser from "@/components/ww-user/index.vue";
const props = defineProps({
  visible: { type: Boolean, default: false },
  width: { type: [String, Number], default: 200 },
  groupmsg: { type: Object, default: () => ({}) },
});
const templateType = {
  link: "网页",
  image: "图片",
  video: "视频",
};
const emits = defineEmits(["close", "upload"]);
const teamNames = computed(() => {
  const { allTeams } = teamStore();
  return allTeams
    .filter((item) => props.groupmsg.teamIds.includes(item.teamId))
    .map((item) => item.name)
    .join("、");
});

function close() {
  emits("close");
}
let loading = ref(false);
async function stopTask() {
  // 是否停止任务
  loading.value = true;
  await stopGroupmsgTask(props.groupmsg._id);
  loading.value = false;
  emits("upload");
}
</script>
<style lang="scss" scoped></style>
