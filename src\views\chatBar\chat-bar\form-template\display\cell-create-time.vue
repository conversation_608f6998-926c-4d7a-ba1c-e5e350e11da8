<template>
  <div>
    {{ text }}
  </div>
</template>
<script setup>
import { computed, onMounted } from 'vue';
import dayjs from 'dayjs';

const props = defineProps({
  title: { default: '' },
  form: { type: Object, default: () => ({}) }
})

const text = computed(() => {
  const value = props.title && props.form[props.title] ? props.form[props.title] : '';
  return value && dayjs(value).isValid() ? dayjs(value).format('YYYY-MM-DD HH:mm') : value;
})

</script>
<style lang="scss" scoped></style>
