<template>
  <data-picker :popup-title="name" :map="map" :options="ranges" @change="onchange">
    <div class="px-15px py-6px" border-bottom>
      <el-form-item :class="required ? 'is-required' : ''" :label="name">
        <div class="w-full" flex items-center>
          <div class="w-0 flex-grow truncate">{{ valueStr }}</div>
          <div v-if="valueStr" flex-shrink-0 class="text-14px ml-10px mr-11px" style="color: #a8abb2;"
            @click.stop="clear()">
            <el-icon>
              <CircleClose />
            </el-icon>
          </div>
          <div v-else flex-shrink-0 class="text-14px ml-10px mr-11px" style="color: #a8abb2;">
            <el-icon>
              <ArrowDown />
            </el-icon>
          </div>
        </div>
      </el-form-item>
    </div>
  </data-picker>
</template>

<script setup>
import { computed } from 'vue';
import dataPicker from './data-picker.vue';

const emits = defineEmits(['change'])

const props = defineProps({
  form: { type: Object, default: () => ({}) },
  map: { type: Object, default: () => ({ text: 'label', value: 'label', children: 'children' }) },
  name: { default: '' },
  realRange: { },
  range: { type: Array, default: () => ([]) },
  required: { type: Boolean, default: false },
  title: { default: '' }
})
const ranges = computed(()=> Array.isArray(props.realRange) ? props.realRange : props.range)
const value = computed(() => Array.isArray(props.form[props.title]) ? props.form[props.title] : []);
const valueStr = computed(() => value.value.join('-'))

function onchange({ selected }) {
  emits('change', { title: props.title, value: selected.map(i => i[props.map.value]) })
}

function clear() {
  emits('change', { title: props.title, value: '' })
}

</script>

<style lang="scss" scoped></style>
