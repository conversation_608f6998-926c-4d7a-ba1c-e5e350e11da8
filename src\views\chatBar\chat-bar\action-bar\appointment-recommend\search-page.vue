<template>
  <my-layout bg-fff v-loading="loading">
    <!-- 标题 -->
    <layout-item>
      <div class="px-15px pt-20px pb-10px font-semibold" font-16 border-bottom>
        搜索结果
      </div>
    </layout-item>
    
    <!-- 搜索框 -->
    <layout-item>
      <div class="py-12px px-15px" border-bottom>
        <el-input 
          ref="searchInputRef"
          v-model="searchKeyword" 
          placeholder="请输入医生或科室名称"
          :suffix-icon="Search" 
          @input="handleSearch"
          @keyup.enter="handleSearch"
        />
      </div>
    </layout-item>
    
    <!-- 搜索结果 -->
    <layout-main :scroll="false">
      <el-scrollbar h-full ref="scrollRef">
        <!-- 科室结果展示 -->
        <div v-if="showDepartments">
          <div class="px-15px py-8px bg-gray-50 text-sm font-medium text-gray-700 border-bottom">科室</div>
          <div v-for="dept in searchResults.departments" :key="'dept-' + dept.id" class="px-15px py-10px border-bottom dept-item">
            <div class="flex items-start justify-between gap-12px">
              <div class="flex-grow min-w-0 cursor-pointer" @click="selectDepartment(dept)">
                <div class="text-sm font-semibold truncate">
                  <span v-html="highlightKeyword(dept.name, searchKeyword)"></span>
                  <!-- <span v-if="dept.areaName" class="text-gray-500 font-normal">（{{ dept.areaName }}）</span> -->
                </div>
                <div class="text-sm text-gray-600 leading-relaxed" v-if="dept.fullPath">
                  <span v-html="highlightKeyword(dept.fullPath, searchKeyword)"></span>
                </div>
              </div>
              <div class="flex-shrink-0 flex items-center text-gray-400">
                <el-icon size="16">
                  <ArrowRight />
                </el-icon>
              </div>
            </div>
          </div>
        </div>

        <!-- 医生结果展示 -->
        <div v-if="showDoctors">
          <div class="px-15px py-8px bg-gray-50 text-sm font-medium text-gray-700 border-bottom">医生</div>
          <div v-for="doctor in searchResults.doctors" :key="'doctor-' + doctor.id" class="px-15px py-10px border-bottom doctor-item">
            <div class="flex items-start justify-between gap-12px">
              <div class="flex-grow min-w-0" @click="previewDoctor(doctor)">
                <div class="flex items-center mb-6px">
                  <div class="text-sm font-semibold mr-8px truncate">
                    <span v-html="highlightKeyword(doctor.name, searchKeyword)"></span>
                  </div>
                  <span class="px-6px py-2px bg-orange-100 text-orange-700 text-xs rounded whitespace-nowrap">
                    <span v-html="highlightKeyword(doctor.title, searchKeyword)"></span>
                  </span>
                  <svg v-if="doctor.featured" class="w-4 h-4 text-yellow-500 ml-6px flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                </div>
                <div class="text-sm text-gray-600 leading-relaxed">
                  <span class="font-medium">擅长：</span>
                  <el-tooltip
                    :disabled="!doctor.speciality || doctor.speciality === '暂无专长介绍'"
                    placement="top"
                    effect="light"
                    :content="doctor.speciality"
                    popper-class="max-w-400px"
                  >
                    <span class="line-clamp-2 cursor-help" v-html="highlightKeyword(doctor.speciality, searchKeyword)"></span>
                  </el-tooltip>
                </div>
                <!-- <div v-if="doctor.deptName" class="text-xs text-gray-500 mt-4px">{{ doctor.deptName }}</div> -->
              </div>
              <div class="flex-shrink-0 mt-2px">
                <el-button
                  size="small"
                  circle
                  @click.stop="selectDoctor(doctor)"
                  class="w-32px h-32px p-0 doctor-send-btn"
                  title="发送医生信息"
                >
                  <el-icon size="14" class="text-blue-500">
                    <Promotion />
                  </el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 无搜索结果 -->
        <div v-if="!showDoctors && !showDepartments && !loading">
          <empty-data title="暂无搜索结果"></empty-data>
        </div>
      </el-scrollbar>
    </layout-main>
    
    <!-- 底部返回按钮 -->
    <layout-item>
      <div class="py-12px px-15px" common-shadow--r>
        <el-button class="block w-full" @click="goBack()">返回</el-button>
      </div>
    </layout-item>
  </my-layout>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { Search, Promotion, ArrowRight } from '@element-plus/icons-vue'
import MyLayout from '@/components/layout'
import LayoutItem from '@/components/layout/item.vue'
import LayoutMain from '@/components/layout/main.vue'
import EmptyData from '@/components/empty-data.vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  searchQuery: { type: String, default: '' },
  customer: { type: Object, default: () => ({}) },
  teams: { type: Array, default: () => [] },
  unionId: { type: String, default: '' }
})

const emits = defineEmits(['close', 'back', 'select'])

const searchInputRef = ref(null)
const loading = ref(false)
const searchKeyword = ref(props.searchQuery || '')
const searchResults = ref({
  departments: [],
  doctors: []
})

// 显示科室结果
const showDepartments = computed(() => searchResults.value.departments.length > 0)
// 显示医生结果
const showDoctors = computed(() => searchResults.value.doctors.length > 0)

onMounted(async () => {
  if (searchKeyword.value) {
    performSearch()
  }
  
  // 等待 DOM 渲染完成后自动聚焦到搜索输入框
  await nextTick()
  if (searchInputRef.value) {
    searchInputRef.value.focus()
  }
})

// 监听搜索关键词变化
watch(() => props.searchQuery, (newQuery) => {
  searchKeyword.value = newQuery
  if (newQuery) {
    performSearch()
  }
})

async function performSearch() {
  if (!searchKeyword.value.trim()) {
    searchResults.value = { departments: [], doctors: [] }
    return
  }

  loading.value = true
  
  try {
    // 获取企业ID
    const corpId = localStorage.getItem('corpId')
    if (!corpId) {
      ElMessage.error('未获取到企业信息')
      return
    }

    // 导入需要的API
    const { 
      searchMembers, 
      getAppointmentRecommendList 
    } = await import('@/api/internet-dept-manage')

    const keyword = searchKeyword.value.trim()
    const results = { departments: [], doctors: [] }

    // 并行搜索医生和科室
    const [doctorResult, deptResult] = await Promise.all([
      searchMembers(corpId, keyword, 1, 50),
      getAppointmentRecommendList()
    ])
    
    // 处理医生搜索结果
    if (doctorResult.success && (doctorResult.data?.data?.list || doctorResult.data?.data)) {
      // 兼容不同的数据结构
      const memberList = doctorResult.data?.data?.list || doctorResult.data?.data || []
      
      results.doctors = memberList.map(member => ({
        id: member.userid || member._id,
        name: member.anotherName || member.name || '未知',
        title: member.title || '医师',
        speciality: (() => {
          // 处理擅长领域字段，支持字符串和数组格式
          let speciality = member.specialty || member.expertise || member.memberTroduce || member.introduction || '';
          if (Array.isArray(speciality)) {
            speciality = speciality.filter(Boolean).join('、');
          }
          return speciality || '暂无专长介绍';
        })(),
        job: member.job || [],
        phone: member.mobile || member.phone || '',
        avatar: member.avatar || '',
        workId: member.doctorNo || member.workNo || member.workId || '',
        outpatientTime: member.outpatientTime || '',
        publicPhone: member.callNumber || member.publicPhone || '',
        featured: member.recommended === 1,
        deptName: getDeptNameById(member.hlwDeptIds || member.deptId, deptResult.data),
        originalData: member
      }))
    }

    // 处理科室搜索结果
    if (deptResult.success && deptResult.data?.departments) {
      const allDepts = []

      // 收集所有末级科室
      deptResult.data.departments.forEach(area => {
        if (area.children) {
          area.children.forEach(primaryDept => {
            if (primaryDept.children) {
              primaryDept.children.forEach(secondaryDept => {
                if (secondaryDept.children && secondaryDept.children.length > 0) {
                  // 有三级科室，添加三级科室
                  secondaryDept.children.forEach(tertiaryDept => {
                    allDepts.push({
                      ...tertiaryDept,
                      areaName: area.name,
                      primaryDeptName: primaryDept.name,
                      secondaryDeptName: secondaryDept.name,
                      fullPath: `${area.name} > ${primaryDept.name} > ${secondaryDept.name} > ${tertiaryDept.name}`,
                      level: 3
                    })
                  })
                } else {
                  // 没有三级科室，将二级科室作为末级科室
                  allDepts.push({
                    ...secondaryDept,
                    areaName: area.name,
                    primaryDeptName: primaryDept.name,
                    secondaryDeptName: secondaryDept.name,
                    fullPath: `${area.name} > ${primaryDept.name} > ${secondaryDept.name}`,
                    isVirtual: true,
                    realDeptId: secondaryDept.deptId,
                    level: 2
                  })
                }
              })
            }
          })
        }
      })

      // 过滤匹配的科室
      results.departments = allDepts.filter(dept =>
        dept.name.toLowerCase().includes(keyword.toLowerCase()) ||
        dept.fullPath.toLowerCase().includes(keyword.toLowerCase())
      )
    }

    searchResults.value = results
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败: ' + (error.message || '未知错误'))
    searchResults.value = { departments: [], doctors: [] }
  } finally {
    loading.value = false
  }
}

// 根据科室ID获取科室名称
function getDeptNameById(deptIds, departmentData) {
  if (!deptIds) {
    return '未知科室'
  }

  // 兼容单个ID和数组ID
  const idsArray = Array.isArray(deptIds) ? deptIds : [deptIds];
  if (idsArray.length === 0) {
    return '未知科室'
  }
  
  const deptNames = []
  
  if (departmentData && departmentData.departments) {
    departmentData.departments.forEach(area => {
      if (area.children) {
        area.children.forEach(primaryDept => {
          if (primaryDept.children) {
            primaryDept.children.forEach(secondaryDept => {
              // 检查二级科室ID
              if (idsArray.includes(secondaryDept.id)) {
                deptNames.push(secondaryDept.name)
              }

              // 检查三级科室ID
              if (secondaryDept.children) {
                secondaryDept.children.forEach(tertiaryDept => {
                  if (idsArray.includes(tertiaryDept.id)) {
                    deptNames.push(tertiaryDept.name)
                  }
                })
              }
            })
          }
        })
      }
    })
  }
  
  return deptNames.length > 0 ? deptNames.join('、') : '未知科室'
}

// 处理搜索输入
function handleSearch() {
  if (searchKeyword.value.trim()) {
    performSearch()
  }
}

// 预览医生详情
function previewDoctor(doctor) {
  // 预览医生详情逻辑
}

// 选择科室
function selectDepartment(dept) {
  console.log('selectDepartment 被调用，原始科室数据:', dept)
  // 构建科室数据，用于进入医生列表
  const departmentData = {
    id: dept.id,
    name: dept.name,
    type: 'department',
    description: dept.description,
    areaName: dept.areaName,
    fullPath: dept.fullPath,
    isVirtual: dept.isVirtual || false,
    realDeptId: dept.realDeptId || dept.deptId,
    level: dept.level || 3
  }

  console.log('构建的科室数据:', departmentData)
  console.log('准备发送 select 事件')

  emits('select', {
    type: 'department',
    data: departmentData
  })
}

// 选择医生
function selectDoctor(doctor) {
  emits('select', { 
    type: 'doctor', 
    data: doctor
  })
}

// 返回
function goBack() {
  emits('back')
}

// 高亮搜索关键词
function highlightKeyword(text, keyword) {
  if (!text || !keyword) return text

  // 转义特殊字符，避免正则表达式错误
  const escapedKeyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  const regex = new RegExp(`(${escapedKeyword})`, 'gi')
  return text.replace(regex, '<span class="text-blue-500 font-medium">$1</span>')
}

// 取消
function cancel() {
  emits('close')
}
</script>

<style scoped>
/* 搜索页面样式 */
.search-page {
  z-index: 9999;
}

/* 滚动条样式 */
.overflow-y-auto::-webkit-scrollbar,
.el-scrollbar :deep(.el-scrollbar__bar) {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track,
.el-scrollbar :deep(.el-scrollbar__track) {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb,
.el-scrollbar :deep(.el-scrollbar__thumb) {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover,
.el-scrollbar :deep(.el-scrollbar__thumb:hover) {
  background: #a8a8a8;
}

/* 选中状态 */
.border-r-2 {
  border-right-width: 2px;
}

.border-blue-500 {
  border-color: #3b82f6;
}

/* 医生列表样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 列表项样式 */
.doctor-item {
  border-bottom: 1px solid #f0f0f0;
}

.dept-item {
  border-bottom: 1px solid #f0f0f0;
}

/* 科室选择按钮样式 */
.dept-send-btn {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.dept-send-btn:hover {
  background: transparent !important;
  border: none !important;
}

.dept-send-btn:focus {
  background: transparent !important;
  border: none !important;
}

/* 发送按钮样式 */
.doctor-send-btn {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.doctor-send-btn:hover {
  background: transparent !important;
  border: none !important;
}

.doctor-send-btn:focus {
  background: transparent !important;
  border: none !important;
}
</style>
