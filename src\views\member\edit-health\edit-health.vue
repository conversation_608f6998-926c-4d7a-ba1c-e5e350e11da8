<template>
  <my-layout v-loading="loading" bg-fff>
    <template v-if="customer._id && customerId && healthTemplateList.length">
      <layout-item>
        <div font-18 font-semibold p-15>健康指标</div>
      </layout-item>
      <layout-main>
        <div px-15>
          <el-form :label-width="140" label-suffix="：" ref="formRef" :model="forms">
            <el-row>
              <el-col v-bind="item.col" v-for="item in healthTemplateList" :key="item.title">
                <el-form-item class="el-form-item--autoheight" :prop="item.title" :label="item.name"
                  :rules="rules[item.type] ? rules[item.type] : { required: item.required, message: `请输入${item.name}`, trigger: 'blur' }">
                  <form-cell :item="item" class="w-full" :form="forms" :value="forms[item.title]"
                    @change="change"></form-cell>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </layout-main>
      <layout-item>
        <div text-center common-shadow--r py-10>
          <el-button class="w-100px" plain @click="close()">取消</el-button>
          <el-button class="w-100px" :loading="loading" type="primary" @click="save()">保存</el-button>
        </div>
      </layout-item>
    </template>
    <layout-main v-else :scroll="false">
      <div class="flex flex-col justify-center items-center">
        <empty-data :title="msg" />
      </div>
    </layout-main>
  </my-layout>
</template>
<script setup>
import { computed, onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import { storeToRefs } from "pinia";
import { ElMessage } from "element-plus";
import { getCustomerInfoById, updateMember } from "@/api/member";
import { tagsStore } from "@/store/tags";
import { templateStore } from "@/store/template";
import dayjs from "dayjs";
import specialRule from "../customer/special-rule.js";


import EmptyData from "@/components/empty-data.vue";
import formCell from "@/components/form-cell";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";

const route = useRoute();
const store = templateStore();
const { corpTemplateList } = storeToRefs(store);
const { getCorpTemplate } = store;
const { removeTag, renameTag } = tagsStore();

const form = ref({});
const customer = ref({});
const loading = ref(false);
const customerId = ref("");
const forms = computed(() => ({ ...customer.value, ...form.value }));
const healthTemplateList = computed(() => getTemplate("healthTemplate"));
const msg = computed(() => {
  if (loading.value) return "加载中...";
  if (!customerId.value || !customer.value._id) return "客户信息不存在";
  if (customerId.value && customer.value._id !== customerId.value) return "获取客户信息失败或者客户信息不存在";
  if (healthTemplateList.lenght) return "未找到对应的健康模板";
  return "";
});

onMounted(async () => {
  customerId.value = route.params.id;
  if (!customerId.value) return;
  if (history.state.customer && history.state.customer._id == customerId.value) {
    customer.value = history.state.customer;
  } else {
    loading.value = true;
    await getCustomer(customerId.value);
  }
  if (customer.value.name) {
    const name = customer.value.name.length > 5 ? `【${customer.value.name.slice(0, 5)}...】健康指标` : `【${customer.value.name}】健康指标`;
    renameTag(route.fullPath, name);
  }
  if (!Array.isArray(corpTemplateList.value) || corpTemplateList.value.length === 0) {
    loading.value = true;
    await getCorpTemplate();
  }
  loading.value = false;
});

function getTemplate(templateType) {
  const temp = Array.isArray(corpTemplateList.value) ? corpTemplateList.value.find((i) => i.templateType === templateType) : [];
  const tempList = temp && Array.isArray(temp.templateList) ? temp.templateList.filter((item) => item.operateType !== "onlyRead") : [];
  const list = tempList.filter((item) => item.fieldStatus !== "disable");
  return list;
}

function change({ title, value }) {
  form.value[title] = value;
}

async function getCustomer(id) {
  const userId = localStorage.getItem("userId");
  const { success, data, message } = await getCustomerInfoById(userId, {
    _id: id,
  });
  if (success) {
    customer.value = data && data.data[0] ? data.data[0] : {};
  } else {
    customer.value = {};
    ElMessage.error(message);
    return Promise.reject();
  }
}

// 保存
const formRef = ref(null);
function save() {
  formRef.value.validate(async (valid) => {
    if (valid) {
      if (Object.keys(form.value).length === 0) {
        close();
        return;
      }
      const { success, message } = await updateMember(customerId.value, form.value);
      if (success) {
        ElMessage.success("保存成功");
        localStorage.setItem("reload-customer-id", customerId.value);
        close();
        setTimeout(() => localStorage.removeItem("reload-customer-id"), 2000);
      } else {
        ElMessage.error(message);
      }
    }
  });
}
function close() {
  removeTag(route.fullPath);
}

 
const rules = computed(() => {
  const map = {};
  healthTemplateList.value.forEach(item => {
    if (typeof specialRule[item.type] === 'function') {
      map[item.title] = specialRule[item.type](forms, item.required);
    }
  })
  return map
});

function checkInteger(value, min, max) {
  if ((typeof value === "string" && value.trim() === "") || (value !== 0 && !value)) return true;
  const number = Number(value);
  if (parseInt(value) !== number) return false;
  if (number < min || number > max) return false;
  return true;
}
</script>

<style lang="scss">
.group-title {
  padding: 0 12px 16px;
  color: var(--yc-main-text-color);
  font-weight: 600;
  font-size: 14px;
}
</style>
