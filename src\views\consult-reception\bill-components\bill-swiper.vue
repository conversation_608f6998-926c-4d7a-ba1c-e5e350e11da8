<template>
  <div ref="ruleRef" class="min-h-1px"></div>
  <div class="flex items-center" v-if="billGroup.length > 0">
    <el-icon class="mr-5px -mt-20px cursor-pointer text-24px" :class="initialIndex === 0 ? 'text-gray-300' : ''" @click="toggle('prev')">
      <ArrowLeftBold />
    </el-icon>
    <div class="w-0 flex-grow">
      <el-carousel :autoplay="false" :initial-index="initialIndex" :loop="false" arrow="never" indicator-position="none" ref="carouselRef" trigger="click" height="120px" @change="onCarouselChange">
        <el-carousel-item v-for="(item, idx) in billGroup" :key="idx" :name="idx">
          <div class="flex text-white select-none">
            <div v-for="(card, index) in item" :key="card.id" class="cursor-pointer relative rounded px-10px pt-5px pb-10px bg-green-500" :class="[index > 0 ? 'ml-10px' : '', billedDate[card.settlementTime] ? 'bg-gray-400' : card.classnames]" :style="{ width: `${cardWidth}px` }" @click="select(card.consultBillId)">
              <div class="mx-auto">
                <div class="h-30px flex justify-between items-center">
                  <div class="font-18px font-semibold">{{ card.billType }}</div>
                </div>
                <div class="h-25px leading-25px text-14px whitespace-nowrap">结算时间：{{ card.settlementTime }}</div>
                <div class="h-25px leading-25px text-14px truncate">结算金额：￥{{ card.consumeAmount }}</div>
              </div>
              <div v-if="value === card.consultBillId" class="triangle-up absolute left-1/2 -bottom-24px transform -translate-x-1/2" :class="card.triangleColor"></div>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>
    <el-icon class="ml-5px -mt-20px cursor-pointer text-24px" :class="initialIndex === billGroup.length - 1 ? 'text-gray-300' : ''" @click="toggle('next')">
      <ArrowRightBold />
    </el-icon>
  </div>
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { useElementBounding } from "@vueuse/core";
import dayjs from "dayjs";
const emits = defineEmits(["change"]);
const props = defineProps({
  value: { type: String, default: "" },
  list: { type: Array, default: () => [] },
});

const mockList = computed(() => {
  return props.list.map((item) => {
    return {
      id: item._id,
      classnames: item.billType === "门诊" ? "bg-green-500" : "bg-orange-500",
      triangleColor: item.billType === "门诊" ? "text-green-500" : "text-orange-500",
      billType: item.billType,
      settlementTime: dayjs(item.createTime).format("YYYY-MM-DD"),
      consumeAmount: item.consumeAmount,
      consultBillId: item.consultBillId,
    };
  });
});

const initialIndex = ref(0);
const ruleRef = ref();
const carouselRef = ref();
const billedDate = ref({});
const { width } = useElementBounding(ruleRef);

const groupNum = computed(() => {
  const num = Math.floor((width.value - 60) / 240);
  console.log(width.value, num);
  return num > 1 ? num : 1;
});
const cardWidth = computed(() => {
  const restWidth = width.value - 60 - groupNum.value * 10 - 10;
  return Math.floor(restWidth / groupNum.value);
});
const billGroup = computed(() => {
  const result = [];
  for (let i = 0; i < mockList.value.length; i += groupNum.value) {
    result.push(mockList.value.slice(i, i + groupNum.value));
  }
  return result;
});

function select(id) {
  console.log(id);

  emits("change", id);
}

function toggle(type) {
  if (type === "prev") {
    carouselRef.value.prev();
  } else {
    carouselRef.value.next();
  }
}

function onCarouselChange(i) {
  initialIndex.value = i;
}

watch(
  () => props.list,
  () => {},
  { deep: true }
);
</script>
<style lang="scss" scoped>
.triangle-up {
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-bottom: 20px solid currentColor;
}
</style>
