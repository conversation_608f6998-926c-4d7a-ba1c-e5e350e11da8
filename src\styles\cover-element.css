:root {
  --el-color-primary: #006eff !important;
  --el-menu-base-level-padding: 5px !important;
}

/** el-table **/
.el-table {
  --el-table-text-color: rgba(0, 0, 0, 0.9) !important;
  --el-table-header-text-color: #3b4859 !important;
}

.el-table__header {
  --el-table-header-bg-color: #ecf4ff;
}

.el-table-fixed-column--shadow::before {
  box-shadow: var(--el-table-fixed-right-column) !important;
}

/** el-checkbox **/
.el-checkbox__inner {
  --el-checkbox-input-border: 1px solid #ccc;
}

/** el-input **/
.el-input,
.el-textarea {
  --el-input-border-color: #bbb !important;
}

.el-button.is-hover {
  color: var(--el-button-hover-text-color);
  border-color: var(--el-button-hover-border-color);
  background-color: var(--el-button-hover-bg-color);
  outline: none;
}


.qrcode .el-dialog__header {
  border-bottom: none !important;
  padding-bottom: 0;
}

.el-dialog__header .el-dialog__title{
  font-size: 16px;
}