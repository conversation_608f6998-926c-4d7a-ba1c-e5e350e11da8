<template>
  <filter-item label="年龄" :text="text">
    <div class="mb-10px">输入年龄区间</div>
    <div class="flex items-center justify-between">
      <el-input v-model.trim="age[0]" clearable class="w-100px" :maxlength="3" @blur="sortAge()" />
      <span>至</span>
      <el-input v-model.trim="age[1]" clearable class="w-100px" :maxlength="3" @blur="sortAge()" />
      <span>岁</span>
    </div>
  </filter-item>

</template>
<script setup>
import { computed, ref } from 'vue';
import FilterItem from './filter-item.vue';

const age = ref([])
const text = computed(() => formatAge(age.value))

function reset() { age.value = [] }

function getParam() {
  const params = {};
  if (parseInt(age.value[0]) >= 0) {
    params.minAge = parseInt(age.value[0])
  }
  if (parseInt(age.value[1]) >= 0) {
    params.maxAge = parseInt(age.value[1])
  }
  return params
}

function formatAge(ages) {
  let [min, max] = ages;
  min = parseInt(min) >= 0 ? parseInt(min) : '';
  max = parseInt(max) >= 0 ? parseInt(max) : '';
  if (min > 0 && max > 0) return `${min}岁 - ${max}岁`
  if (min > 0) return `${min}岁以上`
  if (max > 0) return `${max}岁以下`
  return ''
}

function sortAge() {
  let [min, max] = age.value;
  min = parseInt(min) >= 0 ? parseInt(min) : '';
  max = parseInt(max) >= 0 ? parseInt(max) : '';
  if (typeof min === 'number' && typeof max === 'number') {
    age.value = [min, max].sort((a, b) => a - b).map(i => i + '')
  } else if (typeof min === 'number') {
    age.value = [min + '', max]
  } else if (typeof max === 'number') {
    age.value = [min, max + '']
  } else {
    age.value = []
  }
}

defineExpose({
  getParam,
  reset
})
</script>
<style lang="scss" scoped></style>
