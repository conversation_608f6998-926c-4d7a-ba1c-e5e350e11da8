<template>
  <el-drawer v-model="visibleDialog" :title="title || '预约客户列表'" :size="width || '70%'" direction="rtl" :destroy-on-close="true" @close="handleClose">
    <my-layout>
      <layout-item>
        <div class="py-5px flex bg-white">
          <expand-filter-box v-model:can-expand="canExpand" :enable-expand="true" class="flex-grow" :expand="expand">
            <date-range-filter v-model:dates="appointmentDates" label="预约日期" :text="appointmentDatesText" :width="320" />
            <input-filter v-model="mobile" label="客户手机号" placeholder="请输入" :width="200" />
            <radio-filter v-model="appointmentStatus" label="到院状态" :list="arrivalStatusOptions"></radio-filter>
            <input-filter v-model="name" label="客户姓名" placeholder="请输入" :width="200" />

            <filter-info-source v-model="infoSource" />
            <div class="text-gray-500 text-14px flex items-center">
              <div class="filter-label pr-5px">意向项目:</div>
              <project-intent-select v-model="projectIds" @change="handleProjectChange" placeholder="请选择意向项目" type="filter" style="width: 200px" multiple />
            </div>
            <check-box-filter v-model="introducers" label="所属开发" :list="peopleList" @clear="clearIntroducers">
              <template #item="{ item }">
                <ww-user :openid="item.value"></ww-user>
              </template>
            </check-box-filter>
          </expand-filter-box>
          <div class="flex-shrink-0 flex items-center h-40px pr-15px">
            <el-button v-if="canExpand" text plain @click="expand = !expand">
              <span class="mr-5px">{{ expand ? "收起筛选" : "更多筛选" }}</span>
              <el-icon v-if="expand">
                <ArrowUpBold />
              </el-icon>
              <el-icon v-else>
                <ArrowDownBold />
              </el-icon>
            </el-button>
            <el-button type="primary" @click="search">查询</el-button>
          </div>
        </div>
      </layout-item>
      <layout-main :scroll="false">
        <el-table stripe border height="100%" :data="tableData" v-loading="loading" ref="tableRef">
          <el-common-column prop="" label="到院状态" :min-width="100" fixed="left">
            <template #default="{ row }">
              <div :style="{ color: getArrivalStatusColor(row) }">
                {{ getArrivalStatusLabel(row) }}
              </div>
            </template>
          </el-common-column>
          <el-common-column prop="appointmentDate" label="预约时间" :min-width="120" fixed="left">
            <template #default="{ row }">
              <td-wrapper>{{ row.appointmentDate }}</td-wrapper>
            </template>
          </el-common-column>
          <el-common-column prop="name" label="姓名" :min-width="100" fixed="left">
            <template #default="{ row }">
              <span @click.stop="toCustomerDetail(row.customerId)" main-color class="pointer hover:underline underline-offset-2">
                {{ row.name }}
              </span>
            </template>
          </el-common-column>
          <el-common-column prop="mobile" label="联系方式" :min-width="120" fixed="left">
            <template #default="{ row }">
              <span>{{ maskPhone(row.mobile, 3, 4) }}</span>
            </template>
          </el-common-column>
          <el-common-column prop="consultTime" label="咨询时间" :min-width="160">
            <template #default="{ row }">
              <td-wrapper>{{ row.eConusltDate }}</td-wrapper>
            </template>
          </el-common-column>
          <el-common-column prop="projectName" label="咨询项目" :min-width="160">
            <template #default="{ row }">
              {{ row.projectName }}
            </template>
          </el-common-column>
          <el-common-column prop="consultSituation" label="咨询情况" :min-width="200">
            <template #default="{ row }">
              <el-tooltip 
                :content="row.reportDesc || '暂无咨询情况'" 
                placement="top" 
                :disabled="!row.reportDesc"
                effect="light"
          
                popper-class="max-w-300px"
              >
                <td-wrapper class="cursor-help">{{ row.reportDesc }}</td-wrapper>
              </el-tooltip>
            </template>
          </el-common-column>
          <el-common-column prop="infoSource" label="信息来源" :min-width="180">
            <template #default="{ row }">
              <td-wrapper>{{ row.infoSource }}</td-wrapper>
            </template>
          </el-common-column>
          <el-common-column prop="introducer" label="所属开发" :min-width="120">
            <template #default="{ row }">
              <ww-user v-if="row.introducer" :openid="row.introducer" />
            </template>
          </el-common-column>
          <el-table-column fixed="right" class-name="el-table-fixed-column--shadow" label="操作" width="60">
            <template #default="{ row }">
              <el-text class="cursor-pointer mr-5px" type="primary" @click="triageAction(row)" v-if="row.appointmentStatus === 'notInHospital'">分诊</el-text>
            </template>
          </el-table-column>
        </el-table>
      </layout-main>
      <layout-item>
        <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize" @handle-current-change="changePage" />
      </layout-item>
    </my-layout>

    <customer-detail :enableAction="['managementPlan']" :visible="customerDetailVisible" @close="customerDetailVisible = false" :customerId="customerId" title="客户详情" @update="changeSuccess" customerType="corpCustomer" />
  </el-drawer>
</template>
<script setup>
import { computed, onMounted, ref, watch } from "vue";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import useElPagination from "@/hooks/useElPagination";
import { staffStore } from "@/store/staff";
import { teamStore } from "@/store/team";
import { maskPhone } from "@/utils";
import { ElMessage } from "element-plus";
import { CheckBoxFilter, DateRangeFilter, InputFilter, expandFilterBox, RadioFilter, filterInfoSource } from "@/components/filter-bar";
import elCommonColumn from "@/components/el-common-column/el-common-column.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import WwUser from "@/components/ww-user/index.vue";
import { getAppointmentRecord } from "@/api/consult";
import projectIntentSelect from "@/components/benefit-management/project-intent-select.vue";
import customerDetail from "@/views/member/detail/index.vue";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  width: {
    type: [String, Number],
    default: "70%",
  },
  title: {
    type: String,
    default: "预约客户列表",
  },
});

const emit = defineEmits(["close", "triage"]);

const visibleDialog = computed({
  get: () => props.visible,
  set: (val) => {
    if (!val) {
      emit("close");
    }
  },
});

function handleClose() {
  emit("close");
}
const appointmentDates = ref([dayjs().format("YYYY-MM-DD"), dayjs().add(7, "day").format("YYYY-MM-DD")]); // 默认展示今天及未来7天
const appointmentDatesText = computed(() => appointmentDates.value.join(" ~ "));
const appointmentStatus = ref("");
const infoSource = ref([]);
const projectIds = ref([]);
const selectedProjects = ref([]);
const customerDetailVisible = ref(false);
const customerId = ref("");
const arrivalStatusMap = {
  "": { label: "全部", color: "" },
  notInHospital: { label: "未到院", color: "red" },
  inHospital: { label: "已到院", color: "green" },
  failToHospital: { label: "爽约", color: "orange" },
};
const arrivalStatusOptions = Object.entries(arrivalStatusMap).map(([value, info]) => ({
  label: info.label,
  value: value,
}));

// 获取状态颜色
function getArrivalStatusColor(row) {
  return arrivalStatusMap[row.appointmentStatus || "notInHospital"]?.color;
}

// 获取状态标签
function getArrivalStatusLabel(row) {
  return arrivalStatusMap[row.appointmentStatus || "notInHospital"]?.label;
}

const list = ref([]);
const total = ref(0);
const name = ref("");
const mobile = ref("");
const canExpand = ref(false);
const expand = ref(false);
const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());
const { currentTeamId } = storeToRefs(teamStore());

const introducers = ref([]);

const loading = ref(false);
const tableRef = ref(null);
const { page, pageSize, changePage, changeSize } = useElPagination(getList);

const peopleList = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("onlineCustomerService")).map((i) => ({ value: i.userid }));
});

function clearIntroducers() {
  introducers.value = [];
}

function handleProjectChange(projects) {
  selectedProjects.value = projects;
}

function toCustomerDetail(id) {
  customerId.value = id;
  customerDetailVisible.value = true;
}

function changeSuccess() {
  getList();
}

function triageAction(row) {
  const customerData = {
    _id: row.customerId,
    name: row.name,
    mobile: row.mobile,
    source: Array.isArray(row.source) ? row.source : [],
    introducerUserId: row.introducer,
  };
  const consultData = {
    appointmentId: row.id,
    projects: [],
  };
  if (row.projectNames && Array.isArray(row.projectNames)) {
    consultData.projects = row.projectNames.map((name) => ({ name }));
  }
  emit("triage", {
    customer: customerData,
    consult: consultData,
  });
}

watch(
  () => currentTeamId.value,
  () => {
    getList();
  }
);

watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      // 如果日期范围为空，则设置为近一周
      //   if (appointmentDates.value.length === 0) {
      //     appointmentDates.value = [dayjs().subtract(6, "day").format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")];
      //   }
      getList();
    }
  }
);

async function getList() {
  if (!props.visible || loading.value) return;
  loading.value = true;
  let params = {
    corpId: localStorage.getItem("corpId"),
    page: page.value,
    pageSize: pageSize.value,
    appointmentStatus: appointmentStatus.value,
  };
  if (name.value) params.name = name.value;
  if (mobile.value) params.mobile = mobile.value;
  if (appointmentDates.value.length) {
    params.startAppointmentTime = appointmentDates.value[0];
    params.endAppointmentTime = appointmentDates.value[1];
  }
  if (infoSource.value.length) params.infoSource = infoSource.value; // 添加信息来源筛选条件
  if (projectIds.value && projectIds.value.length > 0) params.projectIds = projectIds.value; // 添加项目筛选条件
  if (introducers.value.length) params.introducers = introducers.value; // 添加开发人员筛选条件

  try {
    const { data, success } = await getAppointmentRecord(params);
    if (!success) {
      loading.value = false;
      ElMessage.error("获取预约记录失败");
      return;
    }
    loading.value = false;
    list.value = data.data.list;
    total.value = data.data.total;
  } catch (error) {
    loading.value = false;
    ElMessage.error("获取预约记录失败");
    console.error(error);
  }
}

const tableData = computed(() => {
  return list.value.map((i) => {
    return { appointmentDate: dayjs(i.appointmentTime).format("YYYY-MM-DD HH:mm"), eConusltDate: dayjs(i.eConusltTime).format("YYYY-MM-DD HH:mm"), projectName: Array.isArray(i.projectNames) ? i.projectNames.join("、") : "", infoSource: Array.isArray(i.source) ? i.source.join("-") : "", ...i };
  });
});

function search() {
  page.value = 1;
  getList();
}

onMounted(async () => {
  await getStaffList(true);
});
</script>
<style lang="scss" scoped></style>
