<template>
  <el-drawer v-model="visible" title="开单记录" size="70%" style="--el-drawer-padding-primary: 0">
    <my-layout ref="layoutRef" class="bg-[#f2f3f4]" v-if="consultBillId" v-loading="loading">
      <layout-main :scroll="false">
        <div class="bg-white mt-10px">
          <bill-swiper :value="consultBillId" :list="consultBillRecord" @change="(event) => (consultBillId = event)" />
          <div class="flex justify-between align-center">
            <div class="p-10px text-16px font-semibold">关联缴费单</div>
          </div>
          <el-table stripe v-loading="loading" border :height="tableHeight" :data="feeList">
            <el-table-column property="orderName" width="150" label="项目名称" />
            <el-table-column property="projectCount" label="数量" width="80" />
            <el-table-column property="projectPrice" label="单价" width="110" />
            <el-table-column property="payment" label="总额 " width="110" />
            <el-table-column property="orderNo" label="订单号" width="110" />
            <el-table-column property="billTime" label="开单时间" width="120" />
            <el-table-column property="billDept" label="开单科室" width="120" />
            <el-table-column property="billDoctor" label="开单医生" width="120" />
            <el-table-column property="doctorAdvice" label="类型" width="110" />
          </el-table>
        </div>
        <div class="flex justify-end b-fff p-20px">
          合计总额：
          <span class="text-[#FC8352]">¥ {{ totalFee }}</span>
        </div>
        <div class="bg-white mt-10px">
          <div class="flex justify-between align-center">
            <div class="p-10px text-16px font-semibold">开单记录</div>
          </div>
          <el-table stripe v-loading="loading" border :height="tableHeight" :data="billList" :span-method="arraySpanMethod">
            <el-table-column property="billTime" label="开单时间" width="160" />
            <el-table-column property="packageName" label="类别" width="160" />
            <el-table-column property="projectName" label="项目名称" min-width="160" />
            <el-table-column property="introducerUserId" label="所属开发" width="120">
              <template #default="{ row }">
                <div class="cursor-pointer hover:text-primary align-center">
                  <ww-user v-if="row.introducerUserId" :openid="row.introducerUserId" />
                  <el-icon color="#006eff" class="ml-3px" @click.stop="changeIntroducer(row)" v-if="viewType === 'record'"><EditPen /></el-icon>
                </div>
              </template>
            </el-table-column>
            <!-- 新增所属咨询列 -->
            <el-table-column property="counselorUserId" label="所属咨询" width="120">
              <template #default="{ row }">
                <div class="cursor-pointer hover:text-primary align-center">
                  <ww-user v-if="row.counselorUserId" :openid="row.counselorUserId" />
                  <el-icon color="#006eff" class="ml-3px" @click.stop="changeCounselor(row)" v-if="viewType === 'record'"><EditPen /></el-icon>
                </div>
              </template>
            </el-table-column>
            <!-- 新增信息来源列 -->
            <el-table-column property="source" label="信息来源" width="150">
              <template #default="{ row }">
                <div class="cursor-pointer hover:text-primary align-center">
                  {{ Array.isArray(row.consultSource) ? row.consultSource.join("-") : "" }}
                  <el-icon color="#006eff" class="ml-3px" @click.stop="changeInfoSource(row)" v-if="viewType === 'record'"><EditPen /></el-icon>
                </div>
              </template>
            </el-table-column>
            <el-table-column property="treatmentDeptName" label="治疗科室" min-width="160" />
            <el-table-column property="usageCount" label="项目数量" min-width="90" />
            <el-table-column property="price" label="单价" :min-width="110" />
            <el-table-column property="discount" label="折扣" width="80">
              <template #default="{ row: { discount, isFree } }">
                {{ isFree ? "赠送" : discount }}
              </template>
            </el-table-column>
            <el-table-column property="totalPrice" label="开单总价" width="120" />
            <el-table-column property="billdCreator" label="开单人" width="120">
              <template #default="{ row: { billdCreator } }">
                <ww-user :openid="billdCreator" />
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="w-full flex justify-end p-20px">
          <el-button v-if="isBillToday && viewType !== 'record'" type="primary" @click="nullify">作废重开</el-button>
        </div>
      </layout-main>
    </my-layout>
    <div v-else>
      <empty-data title="暂无开单记录" />
    </div>
  </el-drawer>
  <!-- 使用抽取的组件 -->
  <edit-introducer-dialog v-model:visible="introducerDialogVisible" :current-id="selectedIntroducerId" :introducer-list="introducerList" @submit="handleIntroducerSubmit" />

  <edit-counselor-dialog v-model:visible="counselorDialogVisible" :current-id="selectedCounselorId" :counselor-list="counselorList" @submit="handleCounselorSubmit" />

  <edit-info-source-dialog v-model:visible="infoSourceDialogVisible" :current-source="selectedInfoSource" @submit="handleInfoSourceSubmit" />
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { useVModel, useElementBounding } from "@vueuse/core";
import MyLayout, { LayoutMain } from "@/components/layout";
import { getBillRecord, getConsultBillRecord, voidIssuedBill, updateBillRecord } from "@/api/bill-record";
import { getFeeRecord } from "@/api/member";
import dayjs from "dayjs";
import emptyData from "@/components/empty-data.vue";
import billSwiper from "./bill-components/bill-swiper.vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { staffStore } from "@/store/staff";
import { storeToRefs } from "pinia";
import { EditPen } from "@element-plus/icons-vue";
// 导入抽取出的组件
import EditIntroducerDialog from "./components/edit-introducer-dialog.vue";
import EditCounselorDialog from "./components/edit-counselor-dialog.vue";
import EditInfoSourceDialog from "./components/edit-info-source-dialog.vue";

const emit = defineEmits(["update:modelValue", "success", "showCustomerDetail"]);
const props = defineProps({
  customer: { type: Object, default: () => ({}) },
  consultRecord: { type: Object, default: () => ({}) },
  modelValue: {
    type: Object,
    default: () => ({}),
  },
  viewType: {
    type: String,
    default: "",
  },
});

// 所属开发修改相关
const introducerDialogVisible = ref(false);
const selectedIntroducerId = ref("");
const currentRecordId = ref(""); // 当前选中行的ID

// 获取开发人员列表
const { staffList } = storeToRefs(staffStore());
const introducerList = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("onlineCustomerService")).map((i) => ({ value: i.userid, label: i.anotherName || i.userid }));
});

// 获取咨询人员列表
const counselorList = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("consultant")).map((i) => ({ value: i.userid, label: i.anotherName || i.userid }));
});

// 打开修改所属开发弹窗
function changeIntroducer(row) {
  selectedIntroducerId.value = row.introducerUserId || "";
  currentRecordId.value = row._id; // 保存当前行的ID
  introducerDialogVisible.value = true;
}

// 提交修改所属开发
async function handleIntroducerSubmit(data) {
  if (!currentRecordId.value) {
    ElMessage.error("记录ID不能为空");
    return;
  }

  try {
    const { success, message } = await updateBillRecord({
      id: currentRecordId.value,
      params: data,
    });

    if (success) {
      ElMessage.success("修改成功");
      introducerDialogVisible.value = false;
      // 刷新账单列表
      getBillList();
      emit("success");
    } else {
      ElMessage.error(message || "修改失败");
    }
  } catch (error) {
    console.error("修改所属开发失败:", error);
    ElMessage.error("系统异常，请稍后重试");
  }
}

// 所属咨询修改相关
const counselorDialogVisible = ref(false);
const selectedCounselorId = ref("");

// 信息来源修改相关
const infoSourceDialogVisible = ref(false);
const selectedInfoSource = ref([]);

// 打开修改所属咨询弹窗
function changeCounselor(row) {
  selectedCounselorId.value = row.counselorUserId || "";
  currentRecordId.value = row._id;
  counselorDialogVisible.value = true;
}

// 提交修改所属咨询
async function handleCounselorSubmit(data) {
  if (!currentRecordId.value) {
    ElMessage.error("记录ID不能为空");
    return;
  }

  try {
    const { success, message } = await updateBillRecord({
      id: currentRecordId.value,
      params: data,
    });

    if (success) {
      ElMessage.success("修改成功");
      counselorDialogVisible.value = false;
      // 刷新账单列表
      getBillList();
      emit("success");
    } else {
      ElMessage.error(message || "修改失败");
    }
  } catch (error) {
    console.error("修改所属咨询失败:", error);
    ElMessage.error("系统异常，请稍后重试");
  }
}

// 打开修改信息来源弹窗
function changeInfoSource(row) {
  selectedInfoSource.value = row.source || [];
  currentRecordId.value = row._id;
  infoSourceDialogVisible.value = true;
}

// 提交修改信息来源
async function handleInfoSourceSubmit(data) {
  if (!currentRecordId.value) {
    ElMessage.error("记录ID不能为空");
    return;
  }
  try {
    const { success, message } = await updateBillRecord({
      id: currentRecordId.value,
      params: { consultSource: data.source || [] },
    });

    if (success) {
      ElMessage.success("修改成功");
      infoSourceDialogVisible.value = false;
      // 刷新账单列表
      getBillList();
      emit("success");
    } else {
      ElMessage.error(message || "修改失败");
    }
  } catch (error) {
    console.error("修改信息来源失败:", error);
    ElMessage.error("系统异常，请稍后重试");
  }
}

const billList = ref([]);
const feeList = ref([]);
const visible = useVModel(props, "modelValue", emit);
const layoutRef = ref();
const { height: layoutHeight } = useElementBounding(layoutRef);
const tableHeight = computed(() => (layoutHeight.value - 350) / 2);
const consultBillId = ref("");
const consultBillRecord = ref([]);
let loading = ref(false);

// 判断账单是否是今天开的
const isBillToday = computed(() => {
  if (billList.value.length === 0) return false;

  // 获取第一条记录的时间
  const billTimeStr = billList.value[0].billTime;
  if (!billTimeStr) return false;

  // 提取日期部分 (YYYY-MM-DD)
  const billDateStr = billTimeStr.split(" ")[0];
  const today = dayjs().format("YYYY-MM-DD");

  return billDateStr === today;
});

watch(
  () => visible.value,
  (n) => {
    if (n) {
      handGetConsultBillRecord();
    }
  }
);

watch(
  () => consultBillId.value,
  (n) => {
    getBillList();
    getFeeList();
  },
  { immediate: true }
);
const getSum = () => {
  let sum = 0;
  feeList.value.forEach((item) => {
    sum += Number(item.payment);
  });
  return sum.toFixed(2);
};
const totalFee = computed(() => getSum());

// 判断是否是当天的开单记录
const isTodayBill = computed(() => {
  if (!consultBillId.value ) return false;
  // 提取日期部分（YYYY-MM-DD）
  const billDate = consultBillId.value.billTime;
  // 获取今天的日期
  const today = dayjs().format('YYYY-MM-DD');
  return billDate === today;
});

const arraySpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  const rows = billList.value;
  const billTime = row.billTime;
  // 获取当前行的合并信息
  const currentRowSpan = rows.filter((item) => item.billTime === billTime).length;
  // 只在 item1 和 item2 列进行合并
  if (columnIndex === 0) {
    if (rowIndex === rows.findIndex((item) => item.billTime === billTime)) {
      return {
        rowspan: currentRowSpan,
        colspan: 1,
      };
    } else {
      return {
        rowspan: 0,
        colspan: 0,
      };
    }
  }
};

// 获取开单记录列表
async function handGetConsultBillRecord() {
  const params = {
    consultId: props.consultRecord._id,
  };
  const { success, data } = await getConsultBillRecord(params);
  if (success) {
    consultBillRecord.value = data.data;
    consultBillId.value = data.data[0]?.consultBillId;
  }
}
async function getFeeList() {
  const params = {
    memberId: props.customer._id,
    consultId: props.consultRecord._id,
    consultBillId: consultBillId.value,
    page: 1,
    pageSize: 1000,
  };
  const { success, data } = await getFeeRecord(params);
  if (success) {
    const { list: tableData = [] } = data;
    feeList.value = tableData.map((i) => {
      i.billTime = i.billTime ? dayjs(i.billTime).format("YYYY-MM-DD") : "";
      i.projectPrice = i.projectPrice ? Number(i.projectPrice).toFixed(2) : "";
      i.payment = i.payment ? Number(i.payment).toFixed(2) : "";
      return i;
    });
  }
}

async function getBillList() {
  let query = {
    customerId: props.customer._id,
    consultId: props.consultRecord._id,
    consultBillId: consultBillId.value,
    page: 1,
    pageSize: 100,
  };
  let { success, data } = await getBillRecord(query);
  if (success) {
    billList.value = data.list.map((i) => {
      return {
        ...i,
        billTime: i.billTime ? dayjs(i.billTime).format("YYYY-MM-DD HH:mm") : "",
        packageName: i.packageName || "单项目",
        price: i.price?.toFixed(2),
        totalPrice: i.totalPrice?.toFixed(2),
        discount: i.discount || "",
      };
    });
  }
}
async function nullify() {
  ElMessageBox.confirm("是否作废重开？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      loading.value = true;
      const consumeAmount = consultBillRecord.value.find((item) => item.consultBillId === consultBillId.value)?.consumeAmount;
      const { success, message } = await voidIssuedBill({ consultBillId: consultBillId.value, consumeAmount, consultId: props.consultRecord._id });
      loading.value = false;
      if (success) {
        handGetConsultBillRecord();
        emit("success");
      } else {
        ElMessage.error(message);
      }
    })
    .catch(() => {});
}
</script>
<style lang="scss" scoped>
:deep(.hidden-inner-content > .select-trigger > .el-input > .el-input__wrapper > .el-input__inner) {
  opacity: 0;
}
</style>