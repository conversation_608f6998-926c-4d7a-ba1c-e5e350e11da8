<template>
  <el-dialog :model-value="visible" :width="width" title="项目详情" @close="close()">
    <el-scrollbar wrap-class="el-dialog-body-full-scroll text-14px">
      <el-form class="px-15px pb-15px" label-position="left" :label-width="100" label-suffix="：">
        <el-form-item class="mb-0px" label="项目名称">
          <div class="text-14px" color-normal>{{ data.projectName }}</div>
        </el-form-item>
        <el-form-item class="mb-0px" label="项目编号">
          <div class="text-14px" color-normal>{{ data.projectNo }}</div>
        </el-form-item>
        <el-form-item class="mb-0px" label="项目类型">
          <div class="text-14px" color-normal>{{ data.projectType }}</div>
        </el-form-item>
        <el-form-item class="mb-0px" label="单位">
          <div class="text-14px" color-normal>{{ data.unit }}</div>
        </el-form-item>
        <el-form-item class="mb-0px" label="价格">
          <div class="text-14px text-orange-500">{{ data.price >= 0 ? "￥" + Number(data.price).toFixed(2) : "" }}</div>
        </el-form-item>
        <el-form-item class="mb-0px" label="关联治疗科室">
          <div class="text-14px" color-normal>{{ data.deptNames }}</div>
        </el-form-item>
        <el-form-item class="mb-0px" label="折扣">
          <div class="flex-grow flex items-center flex-wrap">
            <el-checkbox class="mr-15px" :model-value="data.isGift">支持赠送</el-checkbox>
            <el-checkbox class="mr-15px" :model-value="data.isDiscount">支持折扣</el-checkbox>
            <template v-if="data.isDiscount">
              <span class="ml-15px">最低折扣</span>
              <el-input class="text-center mx-8px w-80px" :model-value="data.lowestDiscount" readonly size="small" />
              <span>折</span>
            </template>
          </div>
        </el-form-item>
        <el-form-item class="mb-0px" label="项目描述"></el-form-item>
        <div class="my-5px text-14px leading-5" color-normal>
          {{ data.description }}
        </div>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button plain class="w-100px" type="primary" @click="close()">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed } from "vue";
import BigNumber from "bignumber.js";
import { ProjectTypeMap } from "@/baseData";

const emits = defineEmits(["close"]);
const props = defineProps({
  data: { type: Object, default: () => ({}) },
  visible: { type: Boolean, default: false },
  width: { type: [String, Number], default: 600 },
});

function close() {
  emits("close");
}
</script>