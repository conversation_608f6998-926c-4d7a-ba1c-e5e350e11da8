<template>
  <my-layout bg-fff common-shadow>
    <layout-main>
      <div class="pt-15px px-30px">
        <el-form :rules="rules" :model="eventsForm" label-width="130px" label-suffix="：" ref="eventsFormRef">
          <div title-bar class="pb-10px">执行事项描述</div>
          <el-row>
            <el-form-item label="计划事项名称" prop="pannedEventName">
              <el-input placeholder="请输入计划事项名称" class="w-250px" :disabled="canEdit" v-model="eventsForm.pannedEventName"></el-input>
            </el-form-item>
            <!-- <el-form-item label="执行团队" prop="teamId">
              <el-select placeholder="请选择执行团队" class="w-250px" v-model="eventsForm.teamId" :disabled="canEdit">
                <el-option v-for="option in teamList" :label="option.label" :value="option.value" />
              </el-select>
            </el-form-item> -->
          </el-row>
          <el-form-item label="计划事项描述" prop="taskContent">
            <el-input class="w-500px" v-model="eventsForm.taskContent" maxlength="100" :rows="3" show-word-limit type="textarea" placeholder="请输入计划事项描述" />
          </el-form-item>
          <el-form-item label="计划事项目的" prop="taskPurpose">
            <el-input class="w-500px" v-model="eventsForm.taskPurpose" maxlength="100" :rows="3" show-word-limit type="textarea" placeholder="请输入计划事项目的" />
          </el-form-item>
          <el-divider border-style="dashed" />
          <div title-bar class="pb-10px">目标客户筛选</div>
          <el-form-item label="客户阶段" prop="customerType">
            <el-select placeholder="请选择客户阶段" class="w-250px" v-model="eventsForm.customerType">
              <el-option v-for="option in customerStage" :label="option.label" :value="option.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="选择时间" prop="planTime">
            <div flex items-center>
              <div class="mr-10px" color-666>{{ timeTitle }}:</div>
              <el-input-number class="mr-10px" :min="1" v-model="eventsForm.planTime" />
              <div color-666>天</div>
            </div>
          </el-form-item>
          <el-form-item label="标签筛选" v-if="isShowCell('tagIds')">
            <div class="mr-6px">
              {{
                id2Tags
                  .map((item) => (item.options && item.options[0] ? `"${item.options[0].name}"` : ""))
                  .filter(Boolean)
                  .join(" 且 ")
              }}
            </div>
            <!-- <el-tag v-for="(item, idx) in id2Tags" :key="idx" class="mr-6 linear-tag" type="primary" effect="dark">
              {{ item.options && item.options[0] ? item.options[0].name : "" }}
            </el-tag> -->
            <el-button text class="item form-tag" type="primary" size="small" :icon="id2Tags.length ? EditPen : Plus" plain @click="addTags('include')">{{ id2Tags.length ? "调整" : "添加" }}</el-button>
          </el-form-item>
          <el-form-item label="标签剔除" v-if="isShowCell('tagIds')">
            <div class="mr-6px">
              {{
                excludeTags
                  .map((item) => (item.options && item.options[0] ? `"${item.options[0].name}"` : ""))
                  .filter(Boolean)
                  .join(" 且 ")
              }}
            </div>
            <!-- <el-tag v-for="(item, idx) in excludeTags" :key="'excludeTag__' + idx" class="mr-6 linear-tag" type="primary"
              effect="dark">
              {{ item.options && item.options[0] ? item.options[0].name : "" }}
            </el-tag> -->
            <el-button text class="item form-tag" type="primary" size="small" :icon="id2Tags.length ? EditPen : Plus" plain @click="addTags('exclude')">{{ excludeTags.length ? "调整" : "添加" }}</el-button>
          </el-form-item>
          <el-divider border-style="dashed" />
          <el-form-item label="向客户发送" prop="pannedSendContent">
            <el-input class="w-500px" v-model="eventsForm.pannedSendContent" maxlength="2000" :rows="3" show-word-limit type="textarea" placeholder="请输入文字提醒" />
          </el-form-item>
          <el-form-item label="">
            <div v-if="pannedEventSendFile.name" flex items-center>
              <div @click="readFile(pannedEventSendFile)" class="flex pointer" items-center>
                <div color-primary>{{ pannedEventSendFile.name }}</div>
                <el-icon color="#006eff">
                  <Tickets />
                </el-icon>
              </div>
              <el-icon color="red" class="ml-10px text-14px pointer" @click="removeDetele">
                <Delete />
              </el-icon>
            </div>
            <div v-else flex justify-center>
              <el-button plain type="primary" :icon="Plus" @click="choose('article')">宣教文章</el-button>
              <el-button plain type="primary" :icon="Plus" @click="choose('questionnaire')">问卷调查</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </layout-main>
    <layout-item>
      <div text-center p-15 common-shadow>
        <el-button class="w-100px" @click="cancel">取消</el-button>
        <el-button class="w-100px" @click="save(eventsFormRef)" type="primary">保存</el-button>
      </div>
    </layout-item>
  </my-layout>
  <add-file ref="addFileRefs" @onGetSelectFile="getSelectFile"></add-file>
  <el-dialog v-model="useEventDialogVisible" title="提示!" draggable :width="300">
    <div class="p-15px">
      {{ canEdit ? "是否更新该计划事项?" : "是否立即启用该计划事项? " }}
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="useDialogCancel">
          {{ canEdit ? "取消" : "稍后启用" }}
        </el-button>
        <el-button type="primary" @click="useDialogSubmit">
          {{ canEdit ? "确定" : "立即启动" }}
        </el-button>
      </span>
    </template>
  </el-dialog>
  <tag-drawer ref="tagRef" @get-select-tag="onGetSelectTag" title="标签筛选"></tag-drawer>
  <survery-dialog :data="currentSurvery" :visible="visible" @close="visible = false" />
</template>
<script setup>
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import tagDrawer from "@/components/tag-drawer/index.vue";
import { getCorpTags } from "@/utils/common";
import { Plus, Delete, EditPen } from "@element-plus/icons-vue";
import { ref, reactive, computed, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { tagsStore } from "@/store/tags";
import { getCustomerType, createPannedEvent } from "@/api/member";
import addFile from "./components/addFile.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import SurveryDialog from "@/views/system/survery/dialog.vue";
import { getPannedEventById, updatePannedEvent } from "@/api/member";
import { teamStore } from "@/store/team";
import { templateStore } from "@/store/template";
const { isShowCell } = templateStore();
const router = useRouter();
const route = useRoute();
const canEdit = ref(false);
const { removeTag: removeRouteTag } = tagsStore();
const eventsFormRef = ref("");
const addFileRefs = ref("");
const useEventDialogVisible = ref(false);
const pannedEventSendFile = ref({}); // 附件
const excludeTagIds = ref([]);
let tagIds = ref([]);
let tagRef = ref();
let customerStage = ref([]);
const timeTitleObj = ref({});
let rules = reactive({
  pannedEventName: [{ required: true, message: "请输入计划事项名称", trigger: "blur" }],
  // teamId: [{ required: true, message: "请选择执行团队", trigger: "change" }],
  planTime: [{ required: true, message: "请输入计划执行时间", trigger: "blur" }],
  customerType: [{ required: true, message: "请选择客户阶段", trigger: "change" }],
  // pannedSendContent: [
  //   { required: true, message: "请输入计划发送内容", trigger: "blur" },
  // ],
  // taskContent: [
  //   { required: true, message: "请输入计划事项描述", trigger: "blur" },
  // ],
});

const timeTitle = computed(() => {
  return timeTitleObj.value[eventsForm.value.customerType];
});
let eventsForm = ref({});
onMounted(async () => {
  if (route.params.id) {
    canEdit.value = route.params.id ? true : false;
    await getData();
  }
});

async function getData() {
  let { data, success } = await getPannedEventById(route.params.id);
  if (success) {
    const { pannedEventName, teamId, planTime, customerType, pannedSendContent, taskContent, pannedEventSendFile: newPannedEventSendFile, tagIds: newTagIds, taskPurpose, excludeTagIds: newExcludeTagIds } = data.data[0];
    pannedEventSendFile.value = newPannedEventSendFile;
    tagIds.value = newTagIds;
    excludeTagIds.value = newExcludeTagIds;
    eventsForm.value = {
      pannedEventName,
      teamId,
      planTime,
      customerType,
      pannedSendContent,
      taskContent,
      taskPurpose,
    };
  }
}

// getTeamData();
customerTypeAction();
const id2Tags = computed(() => {
  const { list } = getCorpTags(tagIds.value || []);
  return list;
});
const excludeTags = computed(() => {
  const { list } = getCorpTags(excludeTagIds.value || []);
  return list;
});

let tagType = ""; // 标签筛选
function addTags(type) {
  tagType = type;
  tagRef.value.openDialog(tagType === "include" ? tagIds.value : excludeTagIds.value);
}

function onGetSelectTag(ids) {
  if (tagType === "include") {
    tagIds.value = ids;
    excludeTagIds.value = excludeTagIds.value.filter((i) => !ids.some((id) => id == i));
  } else {
    excludeTagIds.value = ids;
    tagIds.value = tagIds.value.filter((i) => !ids.some((id) => id == i));
  }
}
function changePlanTime(e) {
  if (e < 0) eventsForm.value.planTime = 0;
}

function choose(type) {
  addFileRefs.value.open(type, {});
}
async function customerTypeAction() {
  let { data, success } = await getCustomerType();
  if (success) {
    const list = data.data;
    customerStage.value = list.map((item) => {
      return {
        value: item.type,
        label: item.name,
      };
    });
    list.forEach((item) => {
      timeTitleObj.value[item.type] = item.timeTitle;
    });
    // eventsForm.value.customerType = customerStage.value[0].value;
  }
}
function save(formEl) {
  if (!formEl) return;
  formEl.validate((valid, fields) => {
    if (valid) {
      useEventDialogVisible.value = true;
    }
  });
}

function cancel() {
  router.push({ name: "PANNEDEVENT" });
  removeRouteTag(route.fullPath);
}
async function useDialogSubmit() {
  await createPannedEventAction(true);
  useEventDialogVisible.value = false;
  router.push({ name: "PANNEDEVENT" });
  removeRouteTag(route.fullPath);
}

async function useDialogCancel() {
  if (canEdit.value) return;
  await createPannedEventAction(false);
  useEventDialogVisible.value = false;
  setTimeout(() => {
    router.push({ name: "PANNEDEVENT" });
    removeRouteTag(route.fullPath);
  }, 500);
}

async function createPannedEventAction(executionSatus) {
  const { currentTeam } = teamStore();
  const tagsName = id2Tags.value.map((item) => (item.options && item.options[0] ? item.options[0].name : ""));
  const tagIds = id2Tags.value.map((item) => (item.options && item.options[0] ? item.options[0].id : ""));
  // 修改处 新增 剔除标签
  const excludeTagsName = excludeTags.value.map((item) => (item.options && item.options[0] ? item.options[0].name : ""));
  const excludeTagIds = excludeTags.value.map((item) => (item.options && item.options[0] ? item.options[0].id : ""));
  const params = {
    teamName: currentTeam.name,
    teamId: currentTeam.teamId,
    tagsName,
    tagIds,
    excludeTagsName,
    excludeTagIds,
    pannedEventSendFile: pannedEventSendFile.value,
    ...eventsForm.value,
  };
  if (canEdit.value) {
    await updatePannedEvent(route.params.id, params);
    ElMessage.success("更新成功!");
  } else {
    params["executionSatus"] = executionSatus;
    params["corpId"] = localStorage.getItem("corpId");
    params["pannedCreateMember"] = localStorage.getItem("userId");
    await createPannedEvent(params);
    ElMessage.success("保存成功!");
  }
}

function getSelectFile(file) {
  pannedEventSendFile.value = file;
}

// 删除附件
async function removeDetele() {
  await ElMessageBox.confirm("是否删除", "提示");
  pannedEventSendFile.value = {};
}

const currentSurvery = ref({});
const visible = ref(false);
function readFile(item) {
  if (item.type === "questionnaire") {
    currentSurvery.value = item;
    visible.value = true;
  } else {
    window.open(item.url);
  }
}
</script>
<style lang="scss" scoped>
.file-content {
  height: 300px;
  overflow-y: auto;

  .file-cell {
    display: flex;
    justify-content: space-between;
    padding: 0px 20px 10px 20px;
    align-items: center;
    border-bottom: 1px solid #eee;

    .file-cell-name {
      .t_1 {
        color: #000;
        font-weight: 600;
        align-items: center;
        padding-right: 10px;
        cursor: pointer;
      }

      .t_2 {
        font-size: 12px;
        padding-top: 10px;
      }
    }

    .file-cell-radio {
      width: 18px;
      height: 18px;
      cursor: pointer;
    }
  }
}
</style>
