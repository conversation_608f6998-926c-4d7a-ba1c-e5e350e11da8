<template>
  <el-table stripe ref="tableRef" :data="selfTableData" :row-key="(row) => row.title" v-loading="loading">
    <template #empty>
      <empty-data :top="0" :title="暂无字段" />
    </template>
    <el-table-column prop="systemFieldName" label="系统字段名称" :min-width="150">
      <template #default="{ row, $index: index }">
        <span>{{ row.systemFieldName }}</span>
        <span class="pl-10px text-gray-500 text-12px" v-if="row.hint">({{ row.hint }})</span>
      </template>
    </el-table-column>
    <el-table-column prop="name" label="展示字段名称" :min-width="150" />
    <el-table-column prop="enable" label="启用" :min-width="200">
      <template #default="{ row, $index: index }">
        <div v-if="!row.isMustEnable">
          <img src="@/assets/check-active.png" class="w-14px h-14px pointer" v-if="row.showField" @click="change(row, 'qiyong', 'disable')" />
          <img @click="change(row, 'qiyong', 'enable')" src="@/assets/check.png" class="w-14px h-14px pointer" v-else />
        </div>
        <el-checkbox :disabled="true" v-model="row.isMustEnable" v-else />
        <span class="ml-6px text-gray-500 mt-2px" v-if="row.isMustEnable">特殊字段，不能修改</span>
      </template>
    </el-table-column>
    <el-table-column prop="required" label="必填" :min-width="200">
      <template #default="{ row, $index: index }">
        <div v-if="!row.isMustRequired">
          <img src="@/assets/check-active.png" class="w-14px h-14px pointer" v-if="row.required" @click="change(row, 'bitian', false)" />
          <img @click="change(row, 'bitian', true)" src="@/assets/check.png" class="w-14px h-14px pointer" v-else />
        </div>
        <el-checkbox :disabled="true" v-model="row.isMustRequired" v-else />
        <span class="ml-6px text-gray-500 mt-2px" v-if="row.isMustRequired">特殊字段，不能修改</span>
      </template>
    </el-table-column>
    <el-table-column prop="action" label="操作" :min-width="80">
      <template #default="{ row, $index: index }">
        <div flex items-center>
          <el-text pointer type="primary" @click="eidtField(row)">编辑</el-text>
          <span class="ml-8px inline-block" title="按住可进行排序" v-if="row.isCorpField">
            <svg-icon class="icon-draggable-handle cursor-move" style="color: #999" size="16" name="drag"></svg-icon>
          </span>
        </div>
      </template>
    </el-table-column>
  </el-table>
  <modal-field :width="width" :visible="visibleField" @close="cancelField()" :field="field" @onChangeTemplate="changeTemplate" />
</template>

<script setup>
import { computed, onMounted, ref } from "vue";
import { storeToRefs } from "pinia";
import { ElMessage } from "element-plus";
import Sortable from "sortablejs";
import { updateCorpTemplate } from "@/api/corp";
import useModal from "@/hooks/useModal";
import { templateStore } from "@/store/template";
import EmptyData from "@/components/empty-data.vue";
import SvgIcon from "@/components/svg-icon";
import modalField from "./modal-field.vue";
const store = templateStore();
const { getCorpTemplate } = store;
const { platformTemplateList } = storeToRefs(store);
const loading = ref(false);
const props = defineProps({
  templateType: { default: "" },
  list: { type: Array, default: () => [] },
});
const tableData = computed(() => {
  const platformRows = platformTemplateList.value.find((i) => i.templateType === props.templateType)?.templateList || [];
  const rows = Array.isArray(props.list) ? props.list : []; // 当前机构的模板字段
  const res = platformRows
    .map((i) => {
      const item = { systemFieldName: i.name, showField: false, ...i };
      if (Array.isArray(i.range)) item.corpRange = [...i.range];
      const row = rows.find((item) => item.title == i.title);
      if (row) {
        const { isMustRequired, isMustEnable, ...reat } = row;
        Object.assign(item, reat, {
          isCorpField: true,
          showField: row.fieldStatus === "enable" || !row.fieldStatus,
        });
        if (Array.isArray(row.range)) item.corpRange = row.range;
      }
      return item;
    })
    .sort((a, b) => a.sort - b.sort);
  return res;
});
const selfTableData = computed(() => tableData.value);
async function change(item, type, status) {
  // 证件类型证件号做绑定
  let obj = JSON.parse(JSON.stringify(item));
  if (type === "qiyong") {
    if (item.isMustEnable) return ElMessage.warning("特殊字段不能修改!");
    obj.fieldStatus = status;
  } else if (type === "bitian") {
    if (item.isMustRequired) return ElMessage.warning("特殊字段不能修改!");
    obj.required = status;
  }
  await changeTemplate(obj);
}
// 模板字段排序
const tableRef = ref();

function handleDrag() {
  const tableElement = tableRef.value.$el;
  const tbody = tableElement.querySelector(".el-table__body-wrapper tbody");
  Sortable.create(tbody, {
    //  可被拖拽的子元素
    draggable: ".el-table__row",
    handle: ".icon-draggable-handle",
    filter: ".disabled-drag-sort",
    onMove(e) {
      return e.related.className.indexOf("disabled-drag-sort") === -1;
    },
    async onEnd({ newIndex, oldIndex }) {
      const arr = tableData.value.map((i) => ({ ...i }));
      const [moveItem] = arr.splice(oldIndex, 1);
      arr.splice(newIndex, 0, moveItem);
      arr.forEach((item, index) => {
        //定义fieldStatus字段
        item["fieldStatus"] = item.showField ? "enable" : "disable";
        delete item.col;
        item.sort = index + 1;
      });
      // 剔除showField为false的字段
      const arr2 = arr.filter((item) => item.isCorpField);
      changeTemplate(arr2);
    },
  });
}
/**编辑 模板字段 */
const field = ref({});
const { close: closeField, show: showField, visible: visibleField } = useModal();
function eidtField(data = {}) {
  field.value = data;
  showField();
}
function cancelField() {
  closeField();
}
//更改成功后，触发事件
async function changeTemplate(item) {
  loading.value = true;
  await updateCorpTemplate(props.templateType, item);
  await getCorpTemplate();
  loading.value = false;
  item.isCorpField = true;
}
onMounted(()=> handleDrag())

/**删除模板 */
// async function remove(callback) {
//   await ElMessageBox.confirm('是否删除当前模板？', '提示', { type: 'warning' });
// }

// function tableRowClassName({ row, rowIndex }) {
//   if (rowIndex === 0) return 'bg-gray-row disabled-drag-sort';
//   if (row.disabledSort) return 'disabled-drag-sort';
// }

// function mergeTableCell({ row, column, rowIndex, columnIndex }) {
//   if (rowIndex === 0 && columnIndex === 0) return [1, 4];
//   else if (rowIndex === 0 && column.property === 'action') return [1, 1];
//   else if (rowIndex === 0) return [0, 0];
// }
</script>

<style lang="scss" scoped>
:deep(.el-table__row.bg-gray-row) {
  background-color: #f5f6fa;
}
</style>
