<template>
  <!-- 机构分组 衍生的团队分组 -->
  <el-dialog v-if="group.parentGroupId" :model-value="props.visible" :width="width" title="添加计划" @close="close()">
    <el-scrollbar wrap-class="el-dialog-body-full-scroll">
      <el-form class="p-15px" label-suffix="：" label-position="left" :label-width="160">
        <el-form-item class="mb-5px" label="分组名称">
          <span color-noraml>{{ group.groupName }}</span>
        </el-form-item>
        <el-form-item class="mb-5px" label="使用说明">
          <span color-noraml>{{ group.description || "暂无使用说明" }}</span>
        </el-form-item>
        <el-form-item class="mb-5px" label="绑定回访计划">
          <span v-if="group.relatePlan && group.planId" color-noraml>{{ group.planName }}</span>
          <span v-else class="text-gray-500">未绑定回访计划</span>
        </el-form-item>
        <!-- <el-form-item label="本分组计划跟踪人" :label-width="150">
          <ww-user-select :list="memberList" placeholder="请选择计划跟踪人" :value="form.executorUserId"
            @change="change($event)" />
        </el-form-item> -->
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button plain class="w-100px" type="primary" @click="close()">取消</el-button>
        <el-button class="w-100px" :loading="loading" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <el-dialog v-else :model-value="props.visible" :width="width" :title="group._id ? '修改团队分组' : '新增团队分组'" @close="close()">
    <el-scrollbar wrap-class="el-dialog-body-full-scroll">
      <el-form class="p-15px" label-suffix="：" label-position="right" :label-width="100">
        <el-form-item class="is-required" label="分组名称">
          <el-input v-model="form.groupName" placeholder="请输入分组名称" />
        </el-form-item>
        <el-form-item label="使用说明">
          <el-input v-model="form.description" maxlength="200" :autosize="{ minRows: 5, maxRows: 8 }" resize="none" show-word-limit type="textarea" placeholder="请输入使用说明" />
        </el-form-item>
        <el-form-item label="是否关联回访计划" :label-width="150">
          <el-switch v-model="form.relatePlan" inline-prompt active-text="是" inactive-text="否" @change="changeRelatePlan" />
        </el-form-item>
        <el-form-item v-if="form.relatePlan" class="is-required" label="回访计划名称" :label-width="150">
          <div class="flex-grow flex items-center text-14px text-blue-500" @click="showPicker()">
            <template v-if="form.managementPlan && form.managementPlan.planId">
              <div class="truncate" :style="{ maxWidth: width / 2 + 'px' }">{{ form.managementPlan.planName }}</div>
              <div class="flex-shrink-0 ml-5px cursor-pointer">修改</div>
            </template>
            <div v-else class="flex-shrink-0 ml-5px cursor-pointer">
              <el-icon class="transform translate-y-2px">
                <Plus />
              </el-icon>
              添加
            </div>
          </div>
        </el-form-item>
        <!-- <el-form-item v-if="form.relatePlan" label="本分组计划跟踪人" :label-width="150">
          <ww-user-select :list="memberList" placeholder="请选择计划跟踪人" :value="form.executorUserId"
            @change="change($event)" />
        </el-form-item> -->
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button plain class="w-100px" type="primary" @click="close()">取消</el-button>
        <el-button class="w-100px" :loading="loading" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <team-plan-picker :data="[]" :visible="pickerVisible" :teamId="group.teamId" :width="pickerWidth" @close="closePicker" @change="changePlan" />
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { createGroup, updateGroup } from "@/api/group";
import useModal from "@/hooks/useModal";

import teamPlanPicker from "./plan-picker/team-plan-picker.vue";
import WwUserSelect from "@/components/ww-user-select";

const emits = defineEmits(["close", "change"]);
const props = defineProps({
  width: { type: Number, default: 600 },
  visible: { type: Boolean, default: false },
  group: { type: Object, default: () => ({}) },
});

const form = ref({});
const memberList = computed(() => {
  const memberList = props.group.teamData && Array.isArray(props.group.teamData.memberList) ? props.group.teamData.memberList : [];
  return memberList;
});
const { close: closePicker, show: showPicker, visible: pickerVisible, width: pickerWidth } = useModal(640);

function change(val) {
  form.value.executorUserId = val;
}

function close() {
  emits("close");
}

const loading = ref(false);
async function confirm() {
  if (form.value.groupName === "") {
    ElMessage.error("请输入分组名称");
  } else {
    const params = { ...form.value };
    loading.value = true;
    const { success, message } = await (props.group._id ? update(params) : addGroup(params));
    if (success) {
      emits("change", props.group.teamId);
      close();
    } else {
      ElMessage.error(message);
    }
    loading.value = false;
  }
}

function addGroup(params) {
  params.teamId = props.group.teamId;
  params.creator = localStorage.getItem("userId");
  return createGroup(params);
}

function update(params) {
  if (props.group.parentGroupId) {
    return updateGroup(props.group._id, { executorUserId: params.executorUserId });
  }
  return updateGroup(props.group._id, params);
}

function changeRelatePlan(val) {
  if (!val) {
    form.value.planName = "";
    form.value.planId = "";
    form.value.executorUserId = "";
  }
}

function changePlan(plan) {
  const managementPlan = {};
  managementPlan.planId = plan ? plan.planId : "";
  managementPlan.planName = plan ? plan.planName : "";
  managementPlan.taskList = plan && Array.isArray(plan.taskList) ? JSON.parse(JSON.stringify(plan.taskList)) : [];
  form.value.managementPlan = managementPlan;
}

watch(
  () => props.visible,
  (n) => {
    if (n) {
      form.value = {
        groupName: typeof props.group.groupName === "string" ? props.group.groupName : "",
        description: typeof props.group.description === "string" ? props.group.description : "",
        managementPlan: props.group.managementPlan && typeof props.group.managementPlan === "object" ? JSON.parse(JSON.stringify(props.group.managementPlan)) : {},
        relatePlan: typeof props.group.relatePlan === "boolean" ? props.group.relatePlan : false,
        executorUserId: typeof props.group.executorUserId === "string" ? props.group.executorUserId : "",
      };
    }
  }
);
</script>