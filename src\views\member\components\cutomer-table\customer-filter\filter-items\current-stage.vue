<template>
  <filter-item label="所处阶段" :text="text">
    <checkbox-group v-model="customerStage" :list="stageList" />
  </filter-item>
</template>
<script setup>
import { computed, ref } from "vue";
import { storeToRefs } from "pinia";
import { configStore } from "@/store/config";
import FilterItem from "./filter-item.vue";
import checkboxGroup from "./checkbox-group.vue";

const config = configStore();
const { getStage } = config;
const { stageList } = storeToRefs(config);
const customerStage = ref([]);
const text = computed(() => (customerStage.value && customerStage.value.length ? `已选${customerStage.value.length}项` : ""));

function getParam() {
  return customerStage.value.length ? { customerStage: customerStage.value } : {};
}

function reset() {
  customerStage.value = [];
}

defineExpose({
  getParam,
  reset,
});
</script>
<style lang="scss" scoped></style>
