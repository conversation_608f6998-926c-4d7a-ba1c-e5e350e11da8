<template>
  <el-drawer :with-header="false" :model-value="visible" :title="title" size="75%" style="--el-drawer-padding-primary: 0" :before-close="close">
    <my-layout class="member-detail bg-[#f3f4f7]" :key="customer._id" :class="customer && customer._id ? '' : 'bg-white'">
      <template v-if="customer && customer._id">
        <div bg-fff class="overflow-hidden" :class="currentTab == 'visit' ? '' : 'mb-10px'">
          <record-head :customer="customer" @change="changeExternalUserId" @update-customer="update" @reload="reload()" :customerType="customerType" :corpType="corpType" />
          <div class="flex justify-between px-15px">
            <div class="w-0 flex-grow">
              <el-tabs v-model="currentTab" @tab-change="change($event)">
                <el-tab-pane v-for="tab in leftTabs" :key="tab.name" :label="tab.label" :name="tab.name"></el-tab-pane>
              </el-tabs>
            </div>
            <div class="flex-shrink-0">
              <el-tabs v-model="currentTab" @tab-change="change($event)">
                <el-tab-pane v-for="tab in rightTabs" :key="tab.name" :label="tab.label" :name="tab.name"></el-tab-pane>
              </el-tabs>
            </div>
          </div>
        </div>
        <layout-main :scroll="false">
          <div h-full>
            <component :enable-action="enableActions[currentTab]" :is="Comp" :customer="customer" :memberId="customer._id" :memberName="customer.name" @reload="reload()" @update-customer="update" :customerType="customerType"></component>
          </div>
        </layout-main>
      </template>
      <el-empty v-else class="customerEmpty" description="未查询到客户信息~" />
    </my-layout>
  </el-drawer>
</template>
<script setup>
import { computed, onMounted, ref, watch, provide } from "vue";
import { onBeforeRouteLeave, useRoute } from "vue-router";
import { dayjs, ElMessage } from "element-plus";
import { tagsStore } from "@/store/tags";
import { getCustomerInfoById } from "@/api/member";
import EmptyData from "@/components/empty-data.vue";
import MyLayout, { LayoutMain } from "@/components/layout";
import RecordHead from "./components/record-head.vue";
import ServiceRecord from "./components/service-record.vue";
import PlanItems from "./components/plan-items/plan-items.vue";
import VisitRecord from "./components/visit-record";
import ManagementPlan from "./components/management-plan";
import newManagementPlan from "./components/new-management-plan";
import { memberStore } from "@/store/member";
import { teamStore } from "@/store/team";
import { storeToRefs } from "pinia";
import innerInfo from "./components/customer-info/inner-info.vue";
import BasicInfo from "./components/customer-info/basic-info.vue";
import FeeRecord from "./components/fee-record/index";
import ConsultRecord from "./components/consult-record.vue";
import EConsultRecord from "./components/e-consult-record.vue";
import billRecord from "./components/bill-record.vue";
import treatmentRecord from "./components/treatment-record.vue";
import ChatRecord from "./components/chat-record.vue";
import { authMemberList, getUserAuthList } from "@/views/chat-history/api/authMember.js";
const { corpInfo } = storeToRefs(memberStore());
const isBenefitManagement = computed(() => {
  return corpInfo.value && corpInfo.value.isBenefitManagement;
});

let customerId = "";
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  visible: {
    type: Boolean,
    default: false,
  },
  customerId: {
    type: String,
    default: "",
  },
  customerType: {
    type: String,
    default: "customer",
  },
  enableAction: {
    type: Array,
    default: () => [],
  },
  corpType: {
    type: String,
    default: "",
  },
});
const emit = defineEmits(["close", "update"]);
const close = () => {
  emit("close");
};
const update = () => {
  emit("update");
};
watch(
  () => props.customerId,
  (newVal) => {
    if (newVal) {
      customerId = newVal;
      getCustomer();
    }
  },
  { immediate: true }
);

provide("notifyCustomerServiceTime", updateCustomerServiceTime);
const customer = ref({});
const enableActions = computed(() => {
  return Array.isArray(props.enableAction)
    ? props.enableAction.reduce((acc, item) => {
        acc[item] = true;
        return acc;
      }, {})
    : [];
});

const { currentTeam } = storeToRefs(teamStore());
watch(currentTeam, (newItem, oldItem) => {
  if (newItem && oldItem && newItem.teamId !== oldItem.teamId) {
    getCustomer();
  }
});
const leftTabs = computed(() => {
  const tabs = [
    { label: "内部信息", name: "inner", comp: innerInfo },
    { label: "基础信息", name: "basic", comp: BasicInfo },
    { label: "健康档案", name: "visit", comp: VisitRecord },
  ];

  if (isBenefitManagement.value) {
    tabs.push({ label: "费用记录", name: "fee", comp: FeeRecord }, { label: "开单记录", name: "bill", comp: billRecord }, { label: "治疗记录", name: "record", comp: treatmentRecord });
  }
  return tabs;
});

const rightTabs = computed(() => {
  const tabs = [
    { label: "回访计划", name: "managementPlan", comp: newManagementPlan },
    // { label: "待跟进事项", name: "plan", comp: PlanItems },
  ];

  if (isBenefitManagement.value) {
    tabs.push({ label: "现场咨询记录", name: "consult", comp: ConsultRecord }, { label: "网络咨询记录", name: "eConsult", comp: EConsultRecord });
  }

  // tabs.unshift({ label: "微信聊天", name: "chatRecord", comp: ChatRecord });
  tabs.push({ label: "服务记录", name: "service", comp: ServiceRecord });

  return tabs;
});

const Comp = computed(() => {
  const tab = [...leftTabs.value, ...rightTabs.value].find((i) => i.name === currentTab.value);
  return tab && tab.comp ? tab.comp : EmptyData;
});

const currentTab = ref("inner");
function change(name) {
  currentTab.value = name;
}
const { getTagState, renameTag, setTagState } = tagsStore();

async function getCustomer() {
  if (!customerId) {
    customer.value = {};
    return;
  }
  const { currentTeamId } = teamStore();
  const userId = localStorage.getItem("userId");
  let params = { _id: customerId };
  if (props.customerType !== "corpCustomer") params["teamId"] = currentTeamId;
  const { success, data, message } = await getCustomerInfoById(userId, params);
  if (success) {
    customer.value = data && data.data[0] ? data.data[0] : {};
  } else {
    customer.value = {};
    ElMessage.error(message);
  }
}

function changeExternalUserId(id) {
  customer.value.externalUserId = id;
}

onMounted(async () => {
  const state = getTagState();
  const tabs = [...leftTabs.value, ...rightTabs.value];
  if (tabs.some((i) => i.name === state.currentTab)) {
    currentTab.value = state.currentTab;
  }
});
async function reload() {
  await getCustomer();
  emit("update", customer.value);
}
onBeforeRouteLeave(() => setTagState({ currentTab: currentTab.value }));

function updateCustomerServiceTime(_id, time) {
  if (customer.value._id === _id && time && dayjs(time).isValid()) {
    const serviceTime = customer.value.serviceTime && dayjs(customer.value.serviceTime).isValid() ? dayjs(customer.value.serviceTime) : "";
    if (!serviceTime || serviceTime.isBefore(dayjs(time))) {
      customer.value.serviceTime = time;
    }
  }
}
</script>
<style scoped lang="scss">
.member-detail {
  position: relative;
  margin-top: -15px;

  .customerEmpty {
    position: absolute;
    margin: auto;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
  }
}

:deep(.el-tabs__item) {
  // font-size: 16px;
  color: #000;
  padding-bottom: 4px;
}

:deep(.el-tabs__item:hover) {
  color: var(--el-color-primary);
}

:deep(.el-tabs__item.is-active) {
  color: var(--el-color-primary);
}

:deep(.el-tabs__header) {
  margin-bottom: 0;
}

:deep(.el-tabs__active-bar) {
  height: 4px;
  border-radius: 4px 4px 0 0;
}

:deep(.el-tabs__item.is-active) {
  font-weight: 600;
}

:deep(.el-scrollbar__view) {
  height: 100%;
}
</style>
