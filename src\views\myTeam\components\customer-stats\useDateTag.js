import { ref, watch } from 'vue';
import dayjs from 'dayjs';

const dates = ref([]);
const activeTag = ref('thisMonth');
const rangeTags = [
  { label: '本月', key: 'thisMonth' },
  { label: '上月', key: 'lastMonth' },
  { label: '昨日', key: 'yesterday' },
  { label: '本周', key: 'thisWeek' },
  { label: '上周', key: 'lastWeek' }
]
const tagDates = {};
function toggleTag(tag) {
  updateTagDate();
  dates.value = [tagDates[tag][0], tagDates[tag][1]]
}

function updateTagDate() {
  tagDates['thisMonth'] = [dayjs().startOf('month').format('YYYY-MM-DD'), dayjs().endOf('month').format('YYYY-MM-DD')];
  tagDates['lastMonth'] = [dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD'), dayjs().subtract(1, 'month').endOf('month').format('YYYY-MM-DD')];
  tagDates['yesterday'] = [dayjs().subtract(1, 'day').format('YYYY-MM-DD'), dayjs().subtract(1, 'day').format('YYYY-MM-DD')];
  tagDates['thisWeek'] = [dayjs().startOf('week').format('YYYY-MM-DD'), dayjs().endOf('week').format('YYYY-MM-DD')];
  tagDates['lastWeek'] = [dayjs().subtract(1, 'week').startOf('week').format('YYYY-MM-DD'), dayjs().subtract(1, 'week').endOf('week').format('YYYY-MM-DD')];
}

updateTagDate();
dates.value = [tagDates['thisMonth'][0], tagDates['thisMonth'][1]]
watch(dates, ([start, end]) => {
  activeTag.value = Object.entries(tagDates).reduce((tag, [key, dates]) => {
    return start === dates[0] && end === dates[1] ? key : tag;
  }, '')
})

export { activeTag, dates, rangeTags, toggleTag, updateTagDate }