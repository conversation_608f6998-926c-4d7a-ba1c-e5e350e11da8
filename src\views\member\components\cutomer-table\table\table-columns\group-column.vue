<template>
  <el-table-column label="分组" prop="groupNames" :width="200">
    <template #default="{row}">
      <td-wrapper>{{ getName(row) }}</td-wrapper>
    </template>
  </el-table-column>
</template>
<script setup>
import { configStore } from "@/store/config";
import tdWrapper from './td-wrapper.vue';

const { getGroupName } = configStore();

function getName(customer) {
  return getGroupName(customer.groupIds) || '-'
}
</script>
