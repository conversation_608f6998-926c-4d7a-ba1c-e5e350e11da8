import { storeToRefs } from "pinia";
import { envjudgwxWork, isMobile, getAccessUser } from "@/utils/common";
import { memberStore } from "@/store/member";
import { teamStore as useTeamStore } from "@/store/team";
import { templateStore } from "@/store/template";
import { urlParamsToObject } from "@/utils/index.js";
import { jsSDKConfig } from "@/utils/jssdk";
import { configStore } from "@/store/config";
import { getRolesByRoleId } from "@/api/corp";

let loginPromise = null;

function getLoginPromise() {
  if (loginPromise) return loginPromise;
  const store = memberStore();
  const { getMemberInfo, getCorpInfo } = store;
  loginPromise = Promise.all([getMemberInfo(), getCorpInfo()]);
  return loginPromise;
}

function resetLoginPromise() {
  loginPromise = null;
}
export default async function useGuard(to) {
  const logined = await useLogin(to);
  return logined;
}
function usePCWorkSpace(to) {
  if (isMobile() && !to.meta.macthMobile) {
    const path = import.meta.env.VITE_TCB_MOBILE_PATH;
    window.location.replace(`${path}?code=${urlParamsToObject().code}`);
    return false;
  }
  return true;
}
async function useLogin(to) {
  // 是否使用PC版的工作台，否则跳转移动版的工作台
  const usePC = usePCWorkSpace(to);
  if (!usePC) return usePC;

  const store = memberStore();
  const { setLogined } = store;
  const { logined } = storeToRefs(store);

  // 无需校验登录的页面 或者 需要校验并且是已经登录 直接跳转
  if (to.meta.validLogin === false || logined.value === "logined") return true;
  
  const needLogin = import.meta.env.VITE_NEED_LOGIN === "true";
  const errorStatus = needLogin ? "unlogin" : "error";
  const errorPage = needLogin ? "LOGIN" : "UNAVAILABLE";
  
  // 在开发环境中设置测试用的 localStorage 值
  if (import.meta.env.DEV && import.meta.env.VITE_TCB_PATH === "yktPCDeploy") {
    localStorage.setItem("userId", "23090101"); // ********* 23090101 luoying
    localStorage.setItem("corpId", "wwe3fb2faa52cf9dfb"); // wwe3fb2faa52cf9dfb
  }
  
  // 如果是恢复状态，尝试恢复用户信息而不是重新登录
  if (logined.value === "restore") {
    setLogined("loading");
    try {
      // 重置 Promise 以确保获取最新的用户信息
      resetLoginPromise();
      const p = getLoginPromise();
      const [user, corp] = await p;
      // 检查用户信息和机构信息是否有效
      if (user && user.userid && corp && corp.corpId) {
        const packageStatus = corp && corp.package ? corp.package.packageStatus : "";
        const packageIsValid = ["expire", "closed"].includes(packageStatus);
        const userDisabled = user.accountState === "disable";
        
        if (!packageIsValid && !userDisabled) {
          // 检查角色信息
          let { data: roleRes } = await getRolesByRoleId(user.roleIds || []);
          const hasTeamOrIsAdmin = isAdminOrHaveTeam(roleRes.data, user);
          
          if (hasTeamOrIsAdmin) {
            // 恢复成功，继续完成登录流程
            const { getGroups } = configStore();
            const { getCorpTemplate } = templateStore();
            const { getTeams, getAllTeams, updateRole } = useTeamStore();
            updateRole(Array.isArray(roleRes.data) ? roleRes.data : []);
            if (envjudgwxWork()) jsSDKConfig();
            const promiseList = [getTeams(), getAllTeams(), getCorpTemplate(), getGroups()];
            await Promise.all(promiseList);
            setLogined("logined");
            return true;
          }
        }
      }
    } catch (error) {
      console.error("恢复登录状态失败:", error);
    }
    // 恢复失败，执行完整的退出登录清理
    try {
      await store.logout();
    } catch (logoutError) {
      console.error("退出登录清理失败:", logoutError);
      // 如果清理失败，至少清理基本的存储
      localStorage.clear();
      sessionStorage.clear();
      setLogined("unlogin");
    }
    return { name: errorPage };
  }
  
  setLogined("loading");
  if (envjudgwxWork()) {
    await getAccessUser();
  }
  if (import.meta.env.DEV && import.meta.env.VITE_TCB_PATH === "yktPCDeploy") {
    localStorage.setItem("userId", "23090101"); // ********* 23090101 luoying
    localStorage.setItem("corpId", "wwe3fb2faa52cf9dfb"); // wwe3fb2faa52cf9dfb
  }
  const p = getLoginPromise();
  const [user, corp] = await p;
  // 机构信息或者 用户信息不存在 根据配置跳转登录页面或者错误页面
  if (!(user && user.userid && corp && corp.corpId)) {
    setLogined(errorStatus);
    return { name: errorPage };
  }
  const packageStatus = corp && corp.package ? corp.package.packageStatus : "";
  const packageIsValid = ["expire", "closed"].includes(packageStatus); // 套餐是否关闭或者过期
  const userDisabled = user.accountState === "disable"; // 账户是否禁用
  if (packageIsValid || userDisabled) {
    setLogined(errorStatus);
    return { name: errorPage };
  }
  // 查询角色信息
  let { data: roleRes } = await getRolesByRoleId(user.roleIds || []);
  const hasTeamOrIsAdmin = isAdminOrHaveTeam(roleRes.data, user); // 是否有团队 或者是 管理员身份
  if (!hasTeamOrIsAdmin) {
    setLogined(errorStatus);
    return { name: errorPage };
  }
  const { getGroups } = configStore();
  const { getCorpTemplate } = templateStore();
  const { getTeams, getAllTeams, updateRole } = useTeamStore();
  updateRole(Array.isArray(roleRes.data) ? roleRes.data : []);
  if (envjudgwxWork()) jsSDKConfig();
  const promiseList = [getTeams(), getAllTeams(), getCorpTemplate(), getGroups()];
  await Promise.all(promiseList);
  setLogined("logined");
  return true;
}

function isAdminOrHaveTeam(roleList, user) {
  return (Array.isArray(roleList) && roleList.some((item) => item.roleId === "admin")) || (user.teamId && user.teamId.length > 0);
}
