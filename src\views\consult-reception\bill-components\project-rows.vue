<template>
  <div class="flex justify-between">
    <div v-for="item in rows" :class="item.key === 'operation' ? 'operationClass' : 'item'">
      <div class="p-10px h-50px text-ellipsis text-14px line-height-30px flex align-center" :style="'min-width:' + item.width + 'px'" border-bottom v-for="project in projectList" :key="project._id">
        <div v-if="item.key === 'operation'">
          <el-icon v-if="operationType === 'project'" class="pointer" @click="removeProject(project)" color="red"><Delete /></el-icon>
          <el-checkbox v-else :disabled="project.required" class="ml-5px" v-model="project.selected" @change="selectedProject(project)" />
        </div>
        <div v-if="item.key === 'number'">
          <el-input type="number" v-model="project[item.key]" class="w-50px" v-if="operationType === 'project'" @input="inputNumber(project)" />
          <div v-else>{{ project[item.key] }}</div>
        </div>
        <div v-else-if="item.key === 'discount'">
          <div class="flex align-center" v-if="!packageItem">
            <el-input type="number" :disabled="!project.isDiscount" v-model="project[item.key]" class="w-60px" @blur="inputDiscount(project)" />
            <el-checkbox class="ml-5px" v-model="project['isFree']" label="赠送" v-if="project.isGift" @change="changeFree(project)" />
          </div>
          <div v-else>
            {{ project[item.key] }}
          </div>
        </div>
        <div v-else-if="item.key === 'totalPrice'">
          <el-input type="number" v-if="!packageItem" v-model="project[item.key]" class="w-90px" @blur="inputTotalPrice(project)" />
          <span v-else>{{ formatPrice(project[item.key]) }}</span>
        </div>
        <div v-else-if="item.key === 'billDeptId'">
          <el-select v-model="project[item.key]" class="w-130px">
            <el-option v-for="dept in project.billDepts" :key="dept._id" :label="dept.deptName" :value="dept._id" />
          </el-select>
        </div>
        <div v-else-if="item.key === 'treatmentDept_id'">
          <el-select v-model="project[item.key]" class="w-130px">
            <el-option v-for="dept in project.depts" :key="dept._id" :label="dept.deptName" :value="dept._id" />
          </el-select>
        </div>
        <div v-else-if="item.key === 'treatmentDoctorUserId'">
          <el-select v-model="project[item.key]" class="w-130px el-select--hiddenValue">
            <template #prefix>
              <div class="h-30px" color-666>
                <ww-user v-if="project[item.key]" :openid="project[item.key]"></ww-user>
              </div>
            </template>
            <el-option v-for="userId in getDoctors(project)" :key="userId" :label="userId" :value="userId">
              <ww-user :openid="userId"></ww-user>
            </el-option>
          </el-select>
        </div>
        <div v-else-if="item.key === 'price'">{{ formatPrice(project[item.key]) }}</div>
        <div v-else>{{ project[item.key] }}</div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ElMessage, ElMessageBox } from "element-plus";
import wwUser from "@/components/ww-user/index.vue";
import BigNumber from "bignumber.js";
const props = defineProps({
  projectList: { type: Array, default: () => [] },
  rows: { type: Array, default: () => [] },
  staffList: { type: Array, default: () => [] },
  operationType: { type: String, default: "" },
  packageItem: { type: Object, default: () => {} },
});
const emits = defineEmits(["removePorject", "inputDiscount", "selectedPackageProject"]);

// 格式化价格，保留两位小数
function formatPrice(price) {
  return Number(price).toFixed(2);
}

function getTotal(project) {
  let total = 0;
  if (project.price && project.number) {
    total = project.price * project.number;
  }
  if (project.discount) {
    total = (total * project.discount) / 10;
  }
  if (project.isFree) {
    total = 0;
  }
  return total.toFixed(2);
}

async function selectedProject(project) {
  if (props.packageItem.canSelectedCount < 0) {
    try {
      await ElMessageBox.alert("该项目已达到可选数量上限", "提示", {
        confirmButtonText: "确定",
        type: "warning",
      });
      project.selected = false;
      return;
    } catch (error) {
      project.selected = false;
      return;
    }
  }
  if (props.packageItem.canSelectedCount === 0) {
    const selectProject = props.projectList.filter((item) => item.selected);
    const projectTotalPrice = selectProject.reduce((total, item) => {
      return total.plus(new BigNumber(item.totalPrice)); // Use BigNumber for precision
    }, new BigNumber(0)); // Start with BigNumber(0)
    const packageTotalPrice = new BigNumber(props.packageItem.packageTotalPrice);
    if (projectTotalPrice.isLessThan(packageTotalPrice)) {
      try {
        await ElMessageBox.alert("已选项目总价不能小于套餐总价", "提示", {
          confirmButtonText: "确定",
          type: "warning",
        });
        project.selected = false;
        return;
      } catch (error) {
        project.selected = false;
        return;
      }
    }
  }
  emits("selectedPackageProject", props.packageItem);
}
function getDoctors(project) {
  if (!project.treatmentDept_id) return [];
  let userIds =
    props.staffList && Array.isArray(props.staffList)
      ? props.staffList
          .filter((item) => {
            const deptIds = Array.isArray(item.deptIds) ? item.deptIds : [];
            const jobs =  Array.isArray(item.job) ? item.job : [];
            return jobs.includes('doctor') && deptIds.includes(project.treatmentDept_id)
          })
          .map((item) => {
            return item.userid;
          })
      : [];
  return userIds;
}

function inputDiscount(project) {
  if (project.discount && Number(project.discount) < Number(project.lowestDiscount) && project.lowestDiscount) {
    project.discount = "";
    ElMessage.warning("折扣不能低于最低折扣");
  } else if (Number(project.discount) <= 0) {
    project.discount = "";
    ElMessage.warning("折扣不能小于等于0");
  } else {
    project.discount = project.discount ? Number(project.discount) : "";
    project.totalPrice = getTotal(project);
  }
}

function inputNumber(project) {
  project.totalPrice = getTotal(project);
}
function inputTotalPrice(project) {
  if (project.number) {
    project.discount = new BigNumber(project.totalPrice).dividedBy(new BigNumber(project.number)).dividedBy(new BigNumber(project.price)).times(new BigNumber(10)).toFixed(2);
    if (project.discount && Number(project.discount) < Number(project.lowestDiscount) && project.lowestDiscount) {
      project.discount = 10;
      project.totalPrice = getTotal(project);
      ElMessage.warning("折扣不能低于最低折扣");
    } else if (Number(project.discount) <= 0) {
      project.discount = 10;
      project.totalPrice = getTotal(project);
      ElMessage.warning("折扣不能小于等于0");
    }
  }
}

function changeFree(project) {
  project.totalPrice = getTotal(project);
}

function removeProject(project) {
  emits("removePorject", project);
}
</script>
<style lang="scss" scoped>
.item {
  flex: 1;
}
.operationClass {
  position: sticky;
  right: 0;
  box-shadow: 0 0px 4px 0 rgba(54, 58, 80, 0.2);
  background-color: #fff;
}
</style>