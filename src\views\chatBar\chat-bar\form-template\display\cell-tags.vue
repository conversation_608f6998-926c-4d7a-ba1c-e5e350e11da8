<template>
  <div class="flex flex-wrap">
    <customer-tag v-for="tagId in value" :key="tagId" :tag-id="tagId" />
  </div>
</template>
<script setup>
import { computed } from 'vue';
import CustomerTag from "@/components/customer-tag/customer-tag.vue";
const props = defineProps({
  title: { default: '' },
  form: { type: Object, default: () => ({}) }
})

const value = computed(() => Array.isArray(props.form[props.title]) ? props.form[props.title] : [])
</script>
<style lang="scss" scoped></style>
