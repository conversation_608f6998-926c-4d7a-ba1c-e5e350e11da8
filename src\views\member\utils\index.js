import { ElMessage } from "element-plus";
import { getUnionidByExternalUserId } from "@/api/wecom.js";
import { selectExternalContact } from "@/utils/jssdk";

export async function bindWechat(_id, corpUserId) {
  let res = await selectExternalContact();
  const { err_msg, userIds } = res;
  if (err_msg !== "selectExternalContact:ok" && err_msg !== "selectExternalContact:cancel") {
    ElMessage({
      message: err_msg,
      type: "error",
    });
    return;
  }
  if (userIds.length > 1) {
    ElMessage({
      message: "只能选择一个用户!",
      type: "warning",
    });
    return;
  }
  const userId = localStorage.getItem("userId");
  if (Array.isArray(corpUserId) && !corpUserId.some((i) => i === userId)) {
    corpUserId.push(userId);
  }
  let params = {
    externalUserId: userIds[0],
    corpUserId,
    personResponsible: corpUserId,
  };
  const unionId = await getUnionid(userIds[0]);
  if (unionId) params.unionid = unionId;
  let { success } = await updateMember(_id, params);
  if (success) {
    ElMessage({
      message: "更新成功",
      type: "success",
    });
    // 页面刷新
    return success;
  }
}

async function getUnionid(externalUserId) {
  const res = await getUnionidByExternalUserId(externalUserId);
  if (res.data && res.data.data && res.data.data.unionid) {
    return res.data.data.unionid;
  } else {
    return "";
  }
}
