<template>
  <my-layout bg-fff common-shadow>
    <layout-item>
      <div class="top-module">
        <div bg-fff class="query mt-15px aligin-center">
          <date-range-filter v-model:dates="dates" label="咨询日期" :text="getDatesText" />
          <div class="text-gray-500 text-14px flex items-center ml-10px">
            <div class="filter-label pr-5px">意向项目:</div>
            <project-intent-select v-model="projectIds" @change="handleProjectChange" placeholder="请选择意向项目" type="filter" style="width: 200px" multiple />
          </div>
          <filter-info-source v-model="infoSource" label="信息来源" />
          <el-form-item class="query__item query__item--auto">
            <el-button type="primary" @click="handleChange">查询</el-button>
            <el-button type="primary" @click="reset">重置</el-button>
          </el-form-item>
        </div>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <div h-full px-15>
        <el-table border stripe :loading="loading" height="100%" :data="list">
          <el-common-column prop="date" label="咨询时间" :min-width="120" />
          <el-common-column prop="project" label="意向项目" :min-width="160">
            <template #default="{ row: { projectNames } }">
              <el-tooltip :disabled="!projectNames" placement="top" effect="light" :content="projectNames" popper-class="max-w-480px">
                <span>{{ projectNames }}</span>
              </el-tooltip>
            </template>
          </el-common-column>
          <el-common-column prop="remark" label="报备说明" :min-width="240">
            <template #default="{ row: { reportDesc } }">
              <el-popover placement="top" width="400" trigger="click" v-if="reportDesc">
                <template #reference>
                  <div class="cursor-pointer whitespace-pre-wrap leading-5 max-h-10 overflow-hidden line-clamp-2 hover:text-blue-500 transition-colors" @click.stop>
                    {{ reportDesc }}
                  </div>
                </template>
                <div class="whitespace-pre-wrap text-14px leading-6 max-h-300px overflow-y-auto">
                  {{ reportDesc }}
                </div>
              </el-popover>
              <div v-else class="text-gray-400">暂无报备说明</div>
            </template>
          </el-common-column>
          <el-common-column prop="source" label="信息来源" :min-width="100">
            <template #default="{ row: { sourceName } }">
              <el-tooltip placement="top" effect="light" :disabled="!sourceName || sourceName.length < 7" :content="sourceName">
                <span>{{ sourceName }}</span>
              </el-tooltip>
            </template>
          </el-common-column>
          <el-common-column prop="register" label="登记人" :min-width="140">
            <template #default="{ row: { userId, registrantUserId } }">
              <ww-user v-if="registrantUserId || userId" :openid="registrantUserId || userId" />
            </template>
          </el-common-column>
          <el-common-column fixed="right" prop="action" label="操作" :width="80">
            <template #default="{ row }">
              <span class="cursor-pointer text-blue-500" @click="editEConsult(row)">详情</span>
            </template>
          </el-common-column>
        </el-table>
      </div>
    </layout-main>
    <layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize" @handle-current-change="changePage" />
    </layout-item>
  </my-layout>
  <add-e-consult :data="customer._id" :record="currentConsult" :visible="addEConsultVisible" mode="view" @close="addEConsultVisible = false" @change="() => changePage(1)" />
</template>
<script setup>
import { computed, ref, onMounted } from "vue";
import { watchDebounced } from "@vueuse/core";
import dayjs from "dayjs";
import { getEConsuleRecord } from "@/api/consult";
import useElPagination from "@/hooks/useElPagination";
import useModal from "@/hooks/useModal";

import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import elCommonColumn from "@/components/el-common-column/el-common-column.vue";
import pagination from "@/components/pagination/pagination.vue";
import { baseFilterItem, DateRangeFilter, filterCustomerSource, filterInfoSource } from "@/components/filter-bar";
import projectIntentSelect from "@/components/benefit-management/project-intent-select.vue";
const { memberId } = defineProps(["memberId", "customer", "memberName", "customerType"]);
import addEConsult from "@/views/e-store-report/add-e-consult.vue";

const { list, page, pageSize, changePage } = useElPagination(getList);
const loading = ref(false);
const total = ref(0);
const projectIds = ref([]);
const selectedProjects = ref([]);
const infoSource = ref([]);

const dates = ref([]);

const getDatesText = computed(() => {
  return dates.value && Array.isArray(dates.value) ? dates.value.join(" ~ ") : "";
});
const addEConsultVisible = ref(false);
const currentConsult = ref({});

function handleProjectChange(projects) {
  selectedProjects.value = projects;
}

function editEConsult(data = {}) {
  currentConsult.value = { ...data };
  addEConsultVisible.value = true;
}

async function getList() {
  let query = {
    page: page.value,
    pageSize: pageSize.value,
    customerId: memberId,
  };
  if (dates.value.length) {
    query.startDate = dates.value[0];
    query.endDate = dates.value[1];
  }
  if (projectIds.value && projectIds.value.length > 0) {
    query.projectIds = projectIds.value;
  }
  if (infoSource.value.length) {
    query.source = [...infoSource.value];
  }
  loading.value = true;
  const { success, data } = await getEConsuleRecord(query);
  loading.value = false;
  list.value = Array.isArray(data.list)
    ? data.list.map((i) => ({
        ...i,
        projectNames: Array.isArray(i.projectNames) ? i.projectNames.join("、") : "",
        sourceName: Array.isArray(i.source) ? i.source.join("-") : "",
      }))
    : [];
  total.value = data.total > 0 ? data.total : 0;
}

async function handleChange() {
  getList();
}
async function reset() {
  dates.value = [];
  infoSource.value = [];
  projectIds.value = [];
  selectedProjects.value = [];
}

onMounted(() => changePage(1));
watchDebounced([dates, infoSource, projectIds], () => changePage(1), { debounce: 500 });
</script>
<style scoped lang="scss">
:deep(.el-table__inner-wrapper) {
  height: 100% !important;
}

.query {
  display: flex;
  flex-wrap: wrap;
  padding: 0 15px;
  align-items: center;
  margin-bottom: 10px;
  .query__item {
    width: 180px;
    margin-bottom: 15px;
    margin-right: 10px;

    @at-root &--large {
      width: 240px;
    }

    @at-root &--small {
      width: 140px;
    }

    @at-root &--small {
      width: 140px;
    }

    @at-root &--auto {
      width: auto;
    }

    @at-root &:last-of-type {
      margin-left: auto;
      margin-right: 0;
    }
  }
}

.filter-label {
  color: #606266;
  font-size: 14px;
}
</style>