<template>
  <div v-if="editing" ref="editRef" :class="classnames" class="flex items-center">
    <el-input v-model="tagText" :maxlength="10" ref="inputRef" class="w-100px"></el-input>
    <el-icon class="ml-10px cursor-pointer text-gray-500" @click="cancel()">
      <CloseBold />
    </el-icon>
    <el-icon v-if="loadingType === 'save'" class="ml-10px animate-spin text-gray-500">
      <Refresh />
    </el-icon>
    <el-icon v-else class="ml-10px cursor-pointer text-green-500" @click="confirm()">
      <Select />
    </el-icon>
  </div>
  <el-popover v-else-if="id" popper-style="min-width:0" placement="right" :width="65" trigger="hover">
    <template #reference>
      <div
        class="py-4px px-10px text-sm text-gray-500 rounded border cursor-pointer border-gray-200 hover:border-blue-500 hover:text-blue-500"
        :class="classnames">
        {{ text }}
      </div>
    </template>
    <div class="flex items-center text-sm">
      <el-icon v-if="loadingType === 'remove'" class="ml-10px animate-spin text-gray-500">
        <Refresh />
      </el-icon>
      <el-icon v-else class="cursor-pointer text-red-500" @click="remove()">
        <Delete />
      </el-icon>
      <svg-icon name="pen" class="cursor-pointer ml-10px text-blue-500" @click="edit()" />
    </div>
  </el-popover>
</template>
<script setup>
import { nextTick, onMounted, ref } from 'vue';
import { onClickOutside } from '@vueuse/core';
import SvgIcon from '@/components/svg-icon';
import { ElMessageBox, ElMessage } from 'element-plus';
import { addRateTag, deleteRateTag, updateRateTagText } from '@/api/knowledgeBase.js'

const emits = defineEmits(['cancelAdd', 'remove', 'updated'])
const props = defineProps({
  classnames: {
    type: String,
    default: ''
  },
  id: {
    type: String,
    default: ''
  },
  rateStar: {
    type: String,
    default: ''
  },
  text: {
    type: String,
    default: ''
  },
})

const loadingType = ref('')

const editRef = ref();
onClickOutside(editRef, () => {
  editing.value = false;
  if (!props.id) emits('cancelAdd')
});

const tagText = ref('');
const editing = ref(false);
const inputRef = ref();
async function edit() {
  tagText.value = props.text;
  editing.value = true;
  await nextTick()
  inputRef.value.focus()
}

function cancel() {
  editing.value = false;
  if (!props.id) emits('cancelAdd')
}

function remove() {
  ElMessageBox.confirm('确定删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    cancelButtonClass: 'w-100px ml-auto',
    confirmButtonClass: 'w-100px mr-auto',
    type: 'warning'
  }).then(async () => {
    loadingType.value = 'remove';
    const { success, message } = await deleteRateTag(props.id);
    if (success) {
      ElMessage.success(message || '删除成功')
      emits('remove', { id: props.id, rateStar: props.rateStar })
    } else {
      ElMessage.error(message || '删除失败')
    }
    loadingType.value = '';
  })
}

async function confirm() {
  if (tagText.value.trim() === '') {
    ElMessage.info('请输入标签');
  } else {
    loadingType.value = 'save';
    const { id, success, message } = await (props.id ? update() : add());
    if (success) {
      emits('updated', { id, text: tagText.value, rateStar: props.rateStar })
      ElMessage.success(message || '保存成功')
      editing.value = false;
    } else {
      ElMessage.error(message || '保存失败')
    }
    loadingType.value = '';
  }
}

async function add() {
  const { data, success, message } = await addRateTag(tagText.value, props.rateStar)
  return { success, id: data && data.id ? data.id : '', message }
}

async function update() {
  const { success, message } = await updateRateTagText(props.id, tagText.value)
  return { success, id: props.id, message }
}



onMounted(() => {
  if (!props.id) edit()
})

</script>
