<template>
  <Teleport to="body">
    <div id="screenId" :class="viewType === 'app' ? 'screen_view' : 'screen_view_big'">
      <!-- <el-scrollbar class="w-full h-full"> -->
      <div class="bigScreen w-1920px h-1080px bg-[#031941] flex" :style="style">
        <!-- 企业微信不支持全屏 -->
        <svg-icon v-if="viewType === 'app'" name="exit-full-screen" class="fixed top-[1rem] right-[1rem] cursor-pointer text-white z-20" :size="30" @click="exit()"></svg-icon>
        <div class="flex-shrink-0 h-full w-1/4 pt-[4vh] pl-[1rem]">
          <div class="mb-[1rem]" style="height: calc(33% - 1rem)">
            <team-rank-table />
          </div>
          <div class="mb-[1rem]" style="height: calc(33% - 1rem)">
            <member-rank-table />
          </div>
          <div style="height: calc(34% - 1rem)">
            <rate-stats :viewType="viewType" />
          </div>
        </div>
        <div class="h-full w-1/2 mx-15px flex flex-col">
          <div class="big-screen__head flex-shrink-0 text-center py-[1.5rem] truncate text-32px font-semibold text-white leading-34px tracking-4px">患者精细化管理服务分析</div>
          <!-- 头部数字统计栏  -->
          <div class="flex flex-shrink-0 items-center pt-[2vh] text-center text-white font-semibold">
            <div v-for="(item, idx) in numberList" :key="item.key" class="w-1/5">
              <div class="flex justify-center">
                <count-to-bar class="text-30px" :end-val="item.value" :duration="1500" :decimals="item.decimals" />
                <div class="mt-[1rem] text-15px ml-5px">{{ item.unit }}</div>
              </div>
              <div class="mt-[1rem] text-15px">{{ item.label }}</div>
            </div>
          </div>
          <today-overview :todayCustomerData="todayCustomerData" :todayChatData="todayChatData" />
          <div class="flex-grow relative mt-[1rem]" @scroll="handleScroll">
            <div class="absolute inset-0">
              <todo-table />
            </div>
          </div>
          <div class="flex-shrink-0 my-[1rem] h-[36vh]">
            <img v-if="corpPic" :src="corpPic" class="w-full h-full" alt="" />
            <img v-else src="./imgs/building.png" class="w-full h-full" alt="" />
          </div>
        </div>
        <div class="flex-shrink-0 h-full pt-[4vh] pb-[1rem] pr-[1rem] w-1/4 flex flex-col">
          <div class="h-1/4 flex-grow flex flex-col rect-border-box">
            <div class="flex justify-between items-center">
              <title-bar class="flex-shrink-0" title="活动新增患者数概况" />
              <div v-if="viewType === 'app'" class="text-white cursor-pointer text-13px hover:text-[#f9b008]" @click="more('MARKETCQRCODE', '获客拉新')">
                更多
                <el-icon class="transform translate-y-2px">
                  <ArrowRightBold />
                </el-icon>
              </div>
            </div>
            <div class="flex-grow relative mt-[1rem]">
              <div class="absolute inset-0" ref="activitiesRef"></div>
            </div>
          </div>
          <div class="h-1/4 flex-grow mt-[1rem] flex flex-col rect-border-box">
            <div class="flex justify-between items-center">
              <title-bar class="flex-shrink-0" title="患者来源分析" />
              <div v-if="viewType === 'app'" class="text-white cursor-pointer text-13px hover:text-[#f9b008]" @click="more('CORPCUSTOMER', '机构患者')">
                更多
                <el-icon class="transform translate-y-2px">
                  <ArrowRightBold />
                </el-icon>
              </div>
            </div>
            <div class="flex-grow relative mt-[1rem]">
              <div class="absolute inset-0" ref="sourceRef"></div>
            </div>
          </div>
          <div class="h-1/4 flex-grow mt-[1rem] flex flex-col rect-border-box">
            <div class="flex justify-between items-center">
              <title-bar class="flex-shrink-0" title="宣教互动热度" />
              <div v-if="viewType === 'app'" class="text-white cursor-pointer text-13px hover:text-[#f9b008]" @click="more('ARTICLE', '文章')">
                更多
                <el-icon class="transform translate-y-2px">
                  <ArrowRightBold />
                </el-icon>
              </div>
            </div>
            <div class="flex-grow relative mt-[1rem]">
              <div class="absolute inset-0">
                <article-table />
              </div>
            </div>
          </div>
          <div class="h-1/4 flex-grow mt-[1rem] flex flex-col rect-border-box">
            <title-bar class="flex-shrink-0" title="入组精细化管理" />
            <div class="flex-grow relative mt-[1rem]">
              <div class="absolute inset-0" ref="diseaseRef"></div>
            </div>
          </div>
        </div>
      </div>
      <!-- </el-scrollbar> -->
    </div>
  </Teleport>
</template>
<script setup>
import { nextTick, onMounted, ref, onBeforeUnmount } from "vue";
import { storeToRefs } from "pinia";
import { useEventListener, useTimeoutFn } from "@vueuse/core";
import { memberStore } from "@/store/member";
import useActivitiesChart from "../charts/use-activities-chart";
import SvgIcon from "@/components/svg-icon";
import articleTable from "./article-table.vue";
import countToBar from "../count-to-bar.vue";
import memberRankTable from "./member-rank-table.vue";
import rateStats from "./rate-stats.vue";
import teamRankTable from "./team-rank-table.vue";
import titleBar from "./title-bar.vue";
import todayOverview from "./today-overview.vue";
import todoTable from "./todo-table.vue";
import useTodayPieChart from "../charts/use-today-pie-chart.js";
import useRoseChart from "../charts/use-rose-chart";
import useNextMenu from "./useNextMenu.js";
import { getSessionArchiveUserList } from "@/api/sessionArchive";
import { customerSourceCountSort, customerGroupCountSort, qrcodeStaticsticsSort, getCustomerSourceCountSortApi, getCustomerGroupCountSortApi, getQrcodeStaticsticsSortApi, getChatCountApi, getCustomerAndServiceCountApi, getOpenedAccountApi, getStaffRateApi } from "../api";

const style = ref({});
const store = memberStore();
const { corpPic } = storeToRefs(store);

function exit() {
  let path = encodeURIComponent(`${import.meta.env.VITE_TCB_PATH_URL}FULLBIGSCREEN`);
  const url = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=wwc1c7baebf62eab4a&redirect_uri=${path}&response_type=code&scope=snsapi_privateinfo&state=STATE#wechat_redirect`;
  window.open(url);
}
const props = defineProps({
  viewType: {
    type: String,
    default: "app",
  },
});
const isActive = ref(false);
const { isPending, start, stop } = useTimeoutFn(() => (isActive.value = false), 1500);
let todayCustomerData = ref({});
let todayChatData = ref({});
useEventListener(document, "mousemove", () => {
  isActive.value = true;
  if (isPending.value) stop();
  start();
});
let width = 1920;
let height = 1080;

const numberList = ref([
  { label: "使用员工数", value: 0, key: "userCount", offset: -2, unit: "人" },
  { label: "会话监管员工数", value: 0, key: "chatUserCount", offset: -1, unit: "人" },
  { label: "患者总数", value: 0, key: "customerCount", offset: 0, unit: "人" },
  { label: "服务满意率", value: 0, key: "averageScore", offset: 0, unit: "%", decimals: 2 },
  { label: "服务记录", value: 0, key: "serviceCount", offset: -1, unit: "条" },
  // { label: "会话消息数", value: 0, key: "messageCount", offset: -2, unit: "条" },
]);
const todayPieRef = ref();
useTodayPieChart(todayPieRef);
const activitiesRef = ref();
const sourceRef = ref();
const diseaseRef = ref();
async function getCustomerSourceCountSort() {
  await getCustomerSourceCountSortApi();
  useRoseChart(sourceRef, customerSourceCountSort.value);
}
async function getCustomerGroupCountSort() {
  await getCustomerGroupCountSortApi();
  useRoseChart(diseaseRef, customerGroupCountSort.value);
}
async function getQrcodeStaticsticsSort() {
  await getQrcodeStaticsticsSortApi();
  var salvProName = qrcodeStaticsticsSort.value.map((item) => item.name);
  var salvProValue = qrcodeStaticsticsSort.value.map((item) => item.value);
  useActivitiesChart(activitiesRef, salvProName, salvProValue);
}
async function getChatCount() {
  let data = await getChatCountApi();
  todayChatData.value = data;
  // numberList.value[5].value = data.chatTotalCount;
}
async function getCustomerAndServiceCount() {
  let data = await getCustomerAndServiceCountApi();
  todayCustomerData.value = data;
  numberList.value[2].value = data.customerTotalCount;
  numberList.value[4].value = data.serviceTotalCount;
}

async function getOpenedAccount() {
  numberList.value[0].value = await getOpenedAccountApi();
}

async function getStaffRate() {
  let data = await getStaffRateApi();
  numberList.value[3].value = data;
}
// 获取到回话监管数

async function getUserAuthList() {
  const { success, data } = await getSessionArchiveUserList();
  if (success) {
    numberList.value[1].value = data.data.length;
  }
}

window.addEventListener("resize", () => {
  setScale();
});
onMounted(async () => {
  getCustomerSourceCountSort();
  getCustomerGroupCountSort();
  getQrcodeStaticsticsSort();
  getChatCount();
  getCustomerAndServiceCount();
  getOpenedAccount();
  getStaffRate();
  setScale();
  getUserAuthList();
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", setScale);
});
// 获取放大缩小比例
function getScale() {
  const screenDiv = document.getElementById("screenId");
  // if (screenDiv.offsetWidth <= width && screenDiv.offsetHeight <= height) return [1, 1];
  let w = screenDiv.offsetWidth / width;
  let h = screenDiv.offsetHeight / height;
  return [w, h];
}
// 设置比例
function setScale() {
  const scale = getScale();
  style.value.transform = `scale(${scale[0]},${scale[1]})`;
}
const next = useNextMenu();
function more(path, menuName) {
  next(path, menuName);
}
</script>
<style lang="scss" scoped>
.rect-border-box {
  padding: 1rem;
  border: 1px solid #1a7cc8;
  border-radius: 0.5rem;
  box-shadow: inset 0 0 0.5rem #1a7cc8; // 0C1232
}

.big-screen__head {
  background: url("./imgs/top.png") no-repeat;
  background-size: 100% 100%;
}

.big-screen {
  font-size: 100%;
  font-family: "微软雅黑";
}

.box-shadow-inner {
  box-shadow: inset 0 0 0.5rem #1a7cc8;
}

.bigScreen {
  transform-origin: 0 0;
  // transition: 0.1s;
  overflow: hidden;
  overflow-x: hidden;
  position: absolute;
  left: 0;
  top: 0;
}

.screen_view {
  position: fixed;
  top: 97px;
  left: 150px;
  right: 0;
  bottom: 0;
  overflow: auto;
}

.screen_view_big {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: auto;
}
</style>
