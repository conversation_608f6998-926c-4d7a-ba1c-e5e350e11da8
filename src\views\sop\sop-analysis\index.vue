<template>
  <div v-if="!sop" v-loading="!sop" class="h-full flex flex-col items-center justify-center">
    <empty-data v-if="getFail" :top="0" title="获取sop内容失败" padding="0 0 15px" text-top="10px" :image-width="200"></empty-data>
  </div>
  <template v-else-if="sop && !getFail">
    <tag-sop-analysis :sop="sop" v-if="executeMethod === 'tag'" />
    <group-sop-analysis :sop="sop" v-else-if="executeMethod === 'enterGroup'" />
    <common-analysis :sop="sop" v-else />
  </template>
</template>
<script setup>
import { onMounted, ref } from "vue";
import { useRoute } from "vue-router";
import dayjs from "dayjs";
import { getSopTask } from "@/api/member";
import { statusMap } from "../sop-enum";
import commonAnalysis from "./common-analysis.vue";
import groupSopAnalysis from "./group-sop-analysis.vue";
import tagSopAnalysis from "./tag-sop-analysis.vue";
const route = useRoute();
const sop = ref(null);
const getFail = ref(false);
const executeMethod = route.params.executeMethod;
async function getSop() {
  const { data, success } = await getSopTask(route.params.id);
  if (success && data.data) {
    sop.value = data.data;
    const disabled = sop.value.sopEnableStatus === "disable";
    if (data.timeStamp && dayjs(data.timeStamp).isValid()) {
      if (sop.value.executeStartTime > data.timeStamp) sop.value.status = "unexecuted";
      else if (sop.value.executeEndTime && sop.value.executeEndTime < data.timeStamp) sop.value.status = "closed";
      else if (sop.value.executeStartTime) sop.value.status = "executing";
    }
    sop.value.statusName = sop.value.status === "closed" ? statusMap[sop.value.status] : disabled ? "已停用" : statusMap[sop.value.status] || "";
  } else {
    getFail.value = true;
  }
}
onMounted(async () => {
  if (route.params.id) getSop();
  else getFail.value = true;
});
</script>
<style lang="scss" scoped>
.text-primary {
  color: var(--el-color-primary);
}
</style>
