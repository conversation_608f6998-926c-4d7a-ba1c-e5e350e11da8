import { teamStore } from "@/store/team";
import { post } from "./axios";
async function useToDo(data) {
  const res = await post("todo", data);
  return res;
}
export async function createManagementPlan(params) {
  const res = await useToDo({
    type: "createManagementPlan",
    corpId: localStorage.getItem("corpId"),
    params,
  });
  return res;
}
export async function updateManagementPlan(id, params = {}) {
  const res = await useToDo({
    type: "updateManagementPlan",
    corpId: localStorage.getItem("corpId"),
    id,
    params,
  });
  return res;
}
export async function removeManagementPlan(id) {
  const res = await useToDo({
    type: "removeManagementPlan",
    corpId: localStorage.getItem("corpId"),
    id
  });
  return res;
}
export async function getManagementPlan(page, pageSize, teamId) {
  const res = await useToDo({
    type: "getManagementPlan",
    page,
    pageSize,
    corpId: localStorage.getItem("corpId"),
    userId: localStorage.getItem("userId"),
    teamId: teamId || teamStore().currentTeamId,
  });
  return res;
}

export async function getManagementPlanList(data) {
  const res = await useToDo({
    type: "getManagementPlan",
    ...data
  });
  return res;
}

export async function addManagementPlanTask(id, params) {
  const res = await useToDo({
    type: "addManagementPlanTask",
    id,
    params,
  });
  return res;
}
export async function getManagementPlanById(id, params) {
  const res = await useToDo({
    type: "getManagementPlanById",
    id,
  });
  return res;
}
export async function executeManagementPlan(params) {
  const res = await useToDo({
    type: "executeManagementPlan",
    params,
  });
  return res;
}

export async function stopManagementPlan(customerId, planId, memberPlanId) {
  const res = await useToDo({
    type: "stopManagementPlan",
    customerId,
    planId,
    memberPlanId,
  });
  return res;
}

export async function getPlanTask(customerId, planId, memberPlanId) {
  const res = await useToDo({
    type: "getPlanTask",
    customerId,
    planId,
    memberPlanId,
  });
  return res;
}

export async function removePlanTask(id, memberPlanId) {
  const res = await useToDo({
    type: "removePlanTask",
    id,
    memberPlanId,
  });
  return res;
}

export async function updatePlanTask(id = "", params) {
  const res = await useToDo({
    type: "updatePlanTask",
    id,
    params,
  });
  return res;
}

export async function getMemberManagementPlan(customerId, executeTeamId) {
  const res = await useToDo({
    type: "getMemberManagementPlan",
    customerId,
    executeTeamId,
  });
  return res;
}

export async function updateMemberManagementPlanStatus(id, planExecutStaus) {
  const res = await useToDo({
    type: "updateMemberManagementPlanStatus",
    id,
    planExecutStaus,
  });
  return res;
}

export async function updateMemberMangePlanStatus(memberPlanId, params) {
  const res = await useToDo({
    type: "updateMemberMangePlanStatus",
    memberPlanId,
    params,
  });
  return res;
}

export async function createPlanTask(params) {
  const res = await useToDo({
    type: "createPlanTask",
    params,
  });
  return res;
}

export async function addManageMentPlanCate(data) {
  const res = await useToDo({
    type: "addManageMentPlanCate",
    ...data,
  });
  return res;
}

export async function deleteManageMentPlanCate(data) {
  const res = await useToDo({
    type: "deleteManageMentPlanCate",
    ...data,
  });
  return res;
}

export async function getManageMentPlanCate(data) {
  const res = await useToDo({
    type: "getManageMentPlanCateList",
    ...data,
  });
  return res;
}

export async function updateManageMentPlanCate(data) {
  const res = await useToDo({
    type: "updateManageMentPlanCate",
    ...data,
  });
  return res;
}

export async function sortManageMentPlanCate(data) {
  const res = await useToDo({
    type: "sortManageMentPlanCate",
    ...data,
  });
  return res;
}
