<template>
  <div v-if="value > 0" class="flex-grow">
    <div class="flex items-center">
      <div ref="displayRef" class="truncate">
        <!-- hover:text-blue-500  cursor-pointer-->
        <span v-for="people in showPeoples" :key="people._id" class="text-14px mr-10px  color-normal " @click="view(people)">
          {{ people.name }}
        </span>
      </div>
      <el-popover :visible="visible" placement="bottom-start" title="" :width="440">
        <template #reference>
          <div ref="moreRef" class="flex-shrink-0" :class="showMore ? 'cursor-pointer text-gray-500 hover:text-blue-500' : 'opacity-0'" @click="showList()">更多</div>
        </template>
        <div v-loading="loading">
          <div class="flex items-center justify-between pb-10px">
            <div class="text-14px font-semibold py-8px">我的推荐</div>
            <el-icon class="text-20px pointer" @click="visible = false">
              <Close />
            </el-icon>
          </div>
          <el-table stripe :data="list" :max-height="300">
            <el-table-column width="120" property="name" label="姓名">
              <template #default="{ row: { name } }">
                <el-tooltip placement="top" effect="light" :disabled="!name || name.length < 7" :content="name">
                  <div class="truncate">{{ name }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column width="80" property="sex" label="性别" />
            <el-table-column width="140" property="mobile" label="手机号" />
            <el-table-column fixed="right" width="60" property="action" label="">
              <template #default="{ row }">
                <el-text type="primary" class="pointer" @click="view(row)">查看</el-text>
              </template>
            </el-table-column>
          </el-table>
          <div class="text-right pt-15px flex justify-end">
            <el-pagination small background layout="prev, pager, next, jumper" :current-page="page" @current-change="handleCurrentChange" :page-size="pageSize" :page-count="pages" class="mt-4" />
          </div>
        </div>
      </el-popover>
    </div>
    <div class="overflow-hidden h-0">
      <div ref="realRef" class="whitespace-nowrap">
        <span v-for="people in showPeoples" :key="people._id" class="text-14px mr-10px hover:underline color-normal cursor-pointer">
          {{ people.name }}
        </span>
      </div>
    </div>
  </div>
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { getReferredPeople } from "@/api/member";
import { useElementBounding } from "@vueuse/core";
import { ElMessage } from "element-plus";
import { useRouter } from "vue-router";
const props = defineProps({
  value: { default: "" },
  form: { type: Object, default: () => ({}) },
});
const loading = ref(false);
const visible = ref(false);
const list = ref([]);
const page = ref(1);
const pages = ref(1);
const pageSize = 10;

function handleCurrentChange(e) {
  page.value = e;
  getData(page.value, pageSize);
}

async function showList() {
  if (visible.value || !props.value > 0) return;
  page.value = 1;
  const res = await getData(page.value, pageSize);
  if (res.list) {
    list.value = res.list;
    pages.value = res.pages;
  }
  visible.value = true;
}

async function getData(page, pageSize) {
  if (loading.value) return;
  loading.value = true;
  const { data, success, message } = await getReferredPeople(props.form.corpId, props.form._id, page, pageSize);
  loading.value = false;
  if (!success) ElMessage.error(message);
  return {
    list: Array.isArray(data.list) ? data.list : [],
    pages: data.pages,
    success,
  };
}

const router = useRouter();
function view(row) {
  // router.push({ name: "DETAIL", params: { id: row._id } });
}

const showPeoples = ref([]);
watch(
  () => props.value,
  (val) => {
    if (val > 0 && showPeoples.value.length == 0) getShowList();
  },
  { immediate: true }
);

async function getShowList() {
  const res = await getData(1, 20);
  if (res && res.list) showPeoples.value = res.list;
}
const displayRef = ref();
const moreRef = ref();
const realRef = ref();
const { width: displayWidth } = useElementBounding(displayRef);
const { width: moreWidth } = useElementBounding(moreRef);
const { width: realWidth } = useElementBounding(realRef);

const showMore = computed(() => realWidth.value === displayWidth.value + moreWidth.value);
</script>
<style lang="scss" scoped></style>
