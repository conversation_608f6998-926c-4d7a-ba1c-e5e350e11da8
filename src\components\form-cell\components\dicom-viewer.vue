<template>
  <div class="dicom-viewer">
    <div class="viewer-controls">
      <div class="thumbnails-container" v-if="files.length > 1">
        <div v-for="(file, index) in files" :key="index" class="thumbnail" :class="{ active: currentImageIndex === index }" @click="updateCurrentImage(index)">
          <span>{{ index + 1 }}</span>
        </div>
      </div>
      <div class="zoom-controls">
        <el-button @click="zoomIn">放大</el-button>
        <el-button  @click="zoomOut">缩小</el-button>
        <el-button  @click="resetView">重置</el-button>
        <el-button  @click="prevImage" :disabled="currentImageIndex === 0" v-if="files.length > 1">上一张</el-button>
        <el-button  @click="nextImage" :disabled="currentImageIndex === files.length - 1" v-if="files.length > 1">下一张</el-button>
      </div>
    </div>
    <div class="viewer-container" ref="viewerContainer"
         @mousedown="startDrag" 
         @mousemove="doDrag" 
         @mouseup="endDrag"
         @mouseleave="endDrag">
      <div ref="dicomElement" style="width: 100%; height: 100%"></div>
      <div v-if="loading" class="loading-overlay">
        <el-loading spinner="el-icon-loading" />
      </div>
      <div class="image-counter" v-if="files.length > 1">{{ currentImageIndex + 1 }} / {{ files.length }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, onBeforeUnmount } from "vue";
// 引入Cornerstone库
// import * as cornerstone from "cornerstone-core";
// import * as cornerstoneWADOImageLoader from "cornerstone-wado-image-loader";
// import * as cornerstoneMath from "cornerstone-math";
// import * as cornerstoneTools from "cornerstone-tools";
// import * as dicomParser from "dicom-parser";

const props = defineProps({
  files: {
    type: Array,
    required: true,
  },
  width: {
    type: Number,
    default: 1000,
  },
  height: {
    type: Number,
    default: 1000,
  }
});

const dicomElement = ref(null);
const viewerContainer = ref(null);
const loading = ref(true);
const currentImageIndex = ref(0);
const metadata = ref(null);
let element = null;

// 拖动相关状态
const isDragging = ref(false);
const lastPoint = ref({ x: 0, y: 0 });

// 初始化Cornerstone
const initCornerstone = () => {
  // 注册WADO图像加载器
  cornerstoneWADOImageLoader.external.cornerstone = cornerstone;
  cornerstoneWADOImageLoader.external.dicomParser = dicomParser;

  // 初始化工具
  cornerstoneTools.external.cornerstone = cornerstone;
  cornerstoneTools.external.cornerstoneMath = cornerstoneMath;

  // 注册WebWorker
  if (window.Worker) {
    const webWorkerUrl = cornerstoneWADOImageLoader.webWorkerManager.initialize({
      taskConfiguration: {
        decodeTask: {
          codecsPath: "https://unpkg.com/cornerstone-wado-image-loader/dist/cornerstoneWADOImageLoaderCodecs.js",
        },
      },
    });
  }

  // 启用元素
  element = dicomElement.value;
  cornerstone.enable(element);
};

// 提取DICOM元数据
const extractMetadata = (image) => {
  if (!image || !image.data) return {};

  const metadata = {
    patientId: image.data.string("x00100020") || "未知",
    patientName: image.data.string("x00100010") || "未知",
    studyDate: image.data.string("x00080020") || "未知",
    modality: image.data.string("x00080060") || "未知",
  };

  return metadata;
};

const updateCurrentImage = (index) => {
  currentImageIndex.value = index;
  renderImage();
};

const renderImage = async () => {
  if (!props.files.length || currentImageIndex.value >= props.files.length) {
    return;
  }

  loading.value = true;

  try {
    const currentFile = props.files[currentImageIndex.value];

    // 加载DICOM文件
    await loadAndViewDicomFile(currentFile);

    // 确保每次加载新图像后重新配置工具
    configureTools();
  } catch (error) {
    console.error("渲染DICOM图像失败:", error);
  } finally {
    loading.value = false;
  }
};

// 加载并显示DICOM文件
const loadAndViewDicomFile = async (fileObj) => {
  if (!element) return;

  let imageUrl;

  // 处理两种不同类型的文件对象
  if (fileObj.url) {
    // 后台接口返回的数据，包含远程URL
    imageUrl = "wadouri:" + fileObj.url;
  } else {
    // 本地上传的文件对象，直接使用原始文件
    const fileBlob = fileObj.file || fileObj;
    const fileUrl = URL.createObjectURL(fileBlob);
    imageUrl = "wadouri:" + fileUrl;
  }

  // 用Cornerstone加载图像
  try {
    const image = await cornerstone.loadImage(imageUrl);
    cornerstone.displayImage(element, image);

    // 提取并显示元数据
    metadata.value = extractMetadata(image);

    // 如果使用了本地文件URL，需要释放URL
    if (!fileObj.url) {
      URL.revokeObjectURL(imageUrl.replace("wadouri:", ""));
    }
  } catch (error) {
    console.error("加载DICOM文件失败:", error);
  }
};

// 配置鼠标交互工具
const configureTools = () => {
  if (!element) return;

  // 添加所需工具，移除PanTool
  cornerstoneTools.addTool(cornerstoneTools.ZoomTool);
  cornerstoneTools.addTool(cornerstoneTools.ZoomMouseWheelTool);

  // 只配置滚轮缩放，不再使用PanTool
  cornerstoneTools.setToolActive("ZoomMouseWheel", {});

  // 设置初始鼠标样式
  element.style.cursor = "grab";
};

// 自定义拖动实现
const startDrag = (e) => {
  if (e.button === 0) { // 左键点击
    isDragging.value = true;
    lastPoint.value = { x: e.clientX, y: e.clientY };
    element.style.cursor = "grabbing";
  }
};

const doDrag = (e) => {
  if (!isDragging.value) return;
  
  // 计算移动的距离
  const deltaX = e.clientX - lastPoint.value.x;
  const deltaY = e.clientY - lastPoint.value.y;
  
  // 更新上一个点的位置
  lastPoint.value = { x: e.clientX, y: e.clientY };
  
  // 如果距离太小，不处理
  if (Math.abs(deltaX) < 1 && Math.abs(deltaY) < 1) return;
  
  // 获取当前视口
  const viewport = cornerstone.getViewport(element);
  
  // 根据图像尺寸和缩放比例计算平移量
  const pixelDeltaX = deltaX / viewport.scale;
  const pixelDeltaY = deltaY / viewport.scale;
  
  // 更新视口的平移
  viewport.translation.x += pixelDeltaX;
  viewport.translation.y += pixelDeltaY;
  
  // 应用更新后的视口
  cornerstone.setViewport(element, viewport);
};

const endDrag = () => {
  if (isDragging.value) {
    isDragging.value = false;
    element.style.cursor = "grab";
  }
};

const zoomIn = () => {
  const viewport = cornerstone.getViewport(element);
  viewport.scale += 0.1;
  cornerstone.setViewport(element, viewport);
};

const zoomOut = () => {
  const viewport = cornerstone.getViewport(element);
  viewport.scale -= 0.1;
  cornerstone.setViewport(element, viewport);
};

const resetView = () => {
  cornerstone.reset(element);
};

// 新增前后导航功能
const prevImage = () => {
  if (currentImageIndex.value > 0) {
    currentImageIndex.value--;
    renderImage();
  }
};

const nextImage = () => {
  if (currentImageIndex.value < props.files.length - 1) {
    currentImageIndex.value++;
    renderImage();
  }
};

// 添加键盘导航
const handleKeyDown = (e) => {
  if (e.key === "ArrowLeft") {
    prevImage();
  } else if (e.key === "ArrowRight") {
    nextImage();
  }
};

// 监听文件变化并重新渲染
watch(
  () => props.files,
  (newFiles) => {
    if (newFiles.length > 0) {
      currentImageIndex.value = 0;
      renderImage();
    }
  },
  { deep: true }
);

onMounted(() => {
  initCornerstone();

  if (props.files.length > 0) {
    renderImage();
  } else {
    loading.value = false;
  }

  // 添加键盘事件监听
  window.addEventListener("keydown", handleKeyDown);
});

onBeforeUnmount(() => {
  // 清理资源
  if (element) {
    cornerstone.disable(element);
  }

  // 移除键盘事件监听
  window.removeEventListener("keydown", handleKeyDown);
});
</script>

<style scoped>
.dicom-viewer {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background-color: #000;
  border-radius: 4px;
  overflow: hidden;
}

.viewer-controls {
  display: flex;
  flex-direction: column;
  padding: 10px;
  background-color: #222;
}

.zoom-controls {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 10px;
  flex-wrap: wrap;
}

.thumbnails-container {
  display: flex;
  overflow-x: auto;
  gap: 8px;
  margin-top: 10px;
}

.thumbnail {
  width: 40px;
  height: 40px;
  background-color: #444;
  border: 2px solid #555;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  flex-shrink: 0;
  color: white;
  font-size: 12px;
  transition: all 0.2s;
}

.thumbnail.active {
  border-color: #409eff;
  background-color: #1c2b46;
}

.thumbnail:hover {
  background-color: #555;
}

.viewer-container {
  position: relative;
  flex: 1;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: grab;
  user-select: none; /* 防止拖动时选中文本 */
}

.viewer-container:active {
  cursor: grabbing;
}

.image-counter {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 14px;
}

canvas {
  max-width: 100%;
  max-height: 100%;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
}

.image-info {
  padding: 10px;
  background-color: #222;
  color: white;
  font-size: 14px;
}

.image-info p {
  margin: 5px 0;
}
</style>
