<template>
  <my-layout>
    <layout-item>
      <div px-15 py-10 flex items-center justify-between>
        <div flex-shrink-0 font-semibold class="text-20px">团队回访计划</div>
        <div class="flex-grow flex items-center">
          <div class="ml-10px text-14px text-gray-700">切换团队：</div>
          <el-text>
            <div class="inline-flex" items-center>
              <span font-14 pointer class="max-w-240px truncate" :title="team.name || ''">
                {{ team.name }}
              </span>
              <el-dropdown class="switch-el-dropdown" trigger="click">
                <svg-icon class="ml-4px hover:text-blue-500" pointer name="switch" size="16"></svg-icon>
                <template #dropdown>
                  <el-dropdown-menu>
                    <div v-if="showTeams.length > 4" class="px-10px py-5px">
                      <el-input clearable v-model="teamName" size="small" placeholder="搜索团队"></el-input>
                    </div>
                    <el-scrollbar wrap-style="max-height:60vh">
                      <el-dropdown-item v-for="team in dropdownList" :key="team.teamId" @click="selectTeam(team)">{{
                        team.name }}</el-dropdown-item>
                    </el-scrollbar>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </el-text>
        </div>
        <el-button class="w-100px" type="primary" @click="toEdit()">新增</el-button>
      </div>
    </layout-item>
    <layout-main class="article" :scroll="false">
      <my-layout v-if="list.length">
        <layout-main>
          <div v-for="item in list" :key="item._id" bg-fff rounded-8 mb-10 px-15 common-shadow>
            <div flex items-center justify-between py-15 border-bottom>
              <div font-16 font-semibold mr-10 class="truncate">{{ item.planName }}</div>
              <div v-if="item.planType === 'corp'"
                class="flex-shrink-0 mx-15px rounded bg-blue-500 text-white px-10px py-5px text-12px">
                机构
              </div>
              <div class="flex-grow"></div>
            </div>
            <div flex items-center justify-between py-15 flex-wrap>
              <div flex items-center flex-wrap font-14 color-666>
                <span>创建人：</span>
                <el-text class="mr-15px" min-w-80>
                  <ww-user v-if="item.createor" :openid="item.createor"></ww-user>
                </el-text>
                <span>创建时间：</span>
                <el-text class="mr-15px">
                  {{ item.createTime ? dayjs(item.createTime).format("YYYY-MM-DD HH:mm") : "--" }}
                </el-text>
                <span>发布状态 ：</span>
                <el-text :type="item.planStatus ? 'primary' : ''">
                  {{ item.planStatus ? "已启用" : "已停用" }}
                </el-text>
              </div>
              <div flex items-center>
                <el-text pointer class="mr-15px" type="primary" @click="showDetail(item)">详情</el-text>
                <el-text pointer class="mr-15px" type="primary" @click="toEdit(item._id, true)">复制</el-text>
                <template v-if="item.planType === 'team'">
                  <el-text v-if="!item.planStatus" pointer class="mr-15px" type="primary"
                    @click="toEdit(item._id)">编辑</el-text>
                  <el-text v-if="!item.planStatus" pointer class="mr-15px" type="danger"
                    @click="remove(item._id)">删除</el-text>
                  <el-text pointer class="mr-15px" :type="item.planStatus ? 'danger':'primary'"
                    @click="toggle(item._id, item.planStatus)">
                    {{ item.planStatus ? '停用' : '启用' }}
                  </el-text>
                </template>

              </div>
            </div>
          </div>
        </layout-main>
        <layout-item>
          <pagination :bgWhite="false" :totalRow="total" :pageSize="pageSize" :currentPage="page"
            @handle-size-change="changeSize" @handle-current-change="changePage" />
        </layout-item>
      </my-layout>
      <div v-else h-full flex flex-col items-center justify-center flex-grow>
        <empty-data title="暂无团队回访计划"></empty-data>
      </div>
    </layout-main>
  </my-layout>
  <task-detial :visible="taskVisible" :managementPlan="managementPlan" @close="taskVisible = false"></task-detial>
</template>
<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { useStorage } from '@vueuse/core'
import { useRouter } from "vue-router";
import { storeToRefs } from "pinia";
import { ElMessage, ElMessageBox } from "element-plus";
import dayjs from "dayjs";

import { getManagementPlanList, removeManagementPlan } from "@/api/managementPlan";
import useElPagination from "@/hooks/useElPagination";
import { managePlanStore } from "@/store/managePlan/managePlan.js";
import { teamStore } from "@/store/team";

import EmptyData from "@/components/empty-data.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import SvgIcon from "@/components/svg-icon";
import taskDetial from "./components/task-detial";
import WwUser from "@/components/ww-user/index.vue";
const router = useRouter();
const total = ref(0);
const team = ref({});
const teamName = ref("");
const managementPlan = ref({});
const taskVisible = ref(false);
const { stopPlan, enablePlan } = managePlanStore();
const { currentTeamId, allTeams, isAdmin, teams } = storeToRefs(teamStore());
const { list, page, pageSize, changePage, changeSize } = useElPagination(getList);
const showTeams = computed(() => (isAdmin.value ? allTeams.value : teams.value));
const dropdownList = computed(() => (teamName.value.trim() ? showTeams.value.filter((item) => item.name.includes(teamName.value)) : showTeams.value));
const teamId = useStorage('teamManagaTeamId', '')

function selectTeam(item) {
  if (team.value.teamId != item.teamId) {
    team.value = { ...item };
    teamId.value = item.teamId
    changePage(1);
  }
}

function toEdit(id = "", copy = false) {
  router.push({ name: "MANAGEMENTPLANDETAIL", params: { id, type: copy ? 'copy' : '' }, state: { planType: "team", teamId: team.value.teamId, teamName: team.value.name } });
}

async function getList() {
  if (!team.value.teamId) {
    list.value = [];
    total.value = 0;
    return;
  }
  const params = {
    page: page.value,
    pageSize: pageSize.value,
    corpId: localStorage.getItem("corpId"),
    planType: "team",
    teamId: team.value.teamId,
    userId: localStorage.getItem("userId"),
  };

  const { success, data, message } = await getManagementPlanList(params);
  list.value = Array.isArray(data.data) ? data.data : [];
  total.value = data.total;
  if (!success) {
    ElMessage.error(message);
  }
}

function showDetail(item) {
  taskVisible.value = true;
  managementPlan.value = item || {};
}

async function toggle(_id, enable) {
  const res = await (enable ? stopPlan(_id, false) : enablePlan(_id));
  res && getList();
}

async function remove(id) {
  await ElMessageBox.confirm('确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
  const { success, message } = await removeManagementPlan(id);
  if (success) {
    ElMessage.success('删除成功');
    getList()
  } else {
    ElMessage.error(message);
  }
}

watch(allTeams, n => {
  if (n.length) {
    const currentTeam = n.find((item) => item.teamId === teamId.value);
    const defaultTeam = n.find((item) => item.teamId === currentTeamId.value);
    const team = currentTeam || defaultTeam || n[0];
    team && selectTeam(team);
  }
}, { immediate: true })
</script>
