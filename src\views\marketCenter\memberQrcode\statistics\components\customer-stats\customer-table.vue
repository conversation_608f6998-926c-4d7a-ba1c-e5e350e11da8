<template>
  <el-table stripe height="100%" empty-text="暂无数据" :data="addCustomerList">
    <el-table-column prop="name" label="客户微信名" />
    <el-table-column prop="userId" label="添加员工">
      <template #default="{ row: { userId } }">
        <ww-user v-if="userId" :openid="userId"></ww-user>
      </template>
    </el-table-column>
    <el-table-column prop="createTime" label="添加时间" />
  </el-table>
</template>
  <script setup>
import { ref, watch } from "vue";
import WwUser from "@/components/ww-user/index.vue";
const props = defineProps({
  addCustomerList: { type: Array, default: () => [] },
});
async function getData() {}
</script>
  