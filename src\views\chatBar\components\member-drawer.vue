<template>
  <el-drawer class="memberDrawer" :model-value="props.visible" :with-header="false" style="width: 90%" @close="close()">
    <el-scrollbar :style="wrapStyle">
      <customer-bar v-for="member in props.members" :key="member._id" :member="member" @click="select(member)" />
    </el-scrollbar>
    <div px-15 py-10 text-center>
      <el-button type="primary" class="w-100px" @click="send">发送授权链接</el-button>
    </div>
  </el-drawer>
</template>
<script setup>
import { computed } from "vue";
import CustomerBar from "./customer-bar.vue";

const props = defineProps({
  teams: { type: Array, default: () => [] },
  visible: { type: Boolean, default: false },
  members: { type: Array, default: () => [] },
});
const emits = defineEmits(["close", "select", "send"]);
const wrapStyle = computed(() => (props.teams && props.teams.length ? "height:calc(100% - 60px)" : "height:100%"));
function close() {
  emits("close");
}

function select(member) {
  emits("select", member);
}
function send() {
  close();
  emits("send");
}
</script>
<style>
.memberDrawer.el-drawer {
  width: auto !important;
}

.memberDrawer .el-drawer__body {
  padding-top: 10px;
  padding-bottom: 10px;
}
</style>