<template>
  <div class="flex-shrink-0 mx-5px text-gray-500">包含任意</div>
  <el-select multiple collapse-tags v-model="projects" class="flex-shrink-0 w-200px" placeholder="请选择">
    <el-option v-for="i in projectList" :key="i.value" :label="i.label" :value="i.value" />
  </el-select>
</template>
<script setup>
import { computed } from 'vue';
import { storeToRefs } from 'pinia'
import { useVModel } from '@vueuse/core';
import { memberStore } from "@/store/member";

const emit = defineEmits(['update:modelValue'])
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})
const data = useVModel(props, 'modelValue', emit);
const projects = computed({
  get() { return Array.isArray(data.value) ? data.value : [] },
  set(val) { data.value = val }
})
const { corpInfo } = storeToRefs(memberStore());
const projectList = computed(() => corpInfo.value && Array.isArray(corpInfo.value.intentedProjectList) ? corpInfo.value.intentedProjectList.map(i => ({ label: i, value: i })) : [])

</script>
<style lang="scss" scoped></style>
