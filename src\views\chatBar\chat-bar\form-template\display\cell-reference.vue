<template>
  <div>
    <span v-if="form.referenceType" class="mr-4px"> {{ form.referenceType }}</span>
    <ww-user v-if="form.referenceUserId" :openid="form.referenceUserId" />
    <span v-else-if="form.reference">{{ form.reference }}</span>
  </div>
</template>
<script setup>
import WwUser from '@/components/ww-user/index'
const props = defineProps({
  form: { type: Object, default: () => ({}) }
})
</script>
<style lang="scss" scoped></style>
