import { ref, shallowRef, onBeforeUnmount } from 'vue'

// 创建全局单例状态
const state = {
  ManagePlanDrawer: shallowRef(null),
  mounted: ref(false),
  error: ref(null),
  isLoading: ref(false),
  visible: ref(false)
}

export default function useManagePlanDrawer() {
  const loadComponent = async () => {
    if (!state.ManagePlanDrawer.value && !state.error.value) {
      state.isLoading.value = true
      state.error.value = null

      try {
        const component = await import('@/components/manage-plan-drawer/manage-plan-drawer.vue')
        state.ManagePlanDrawer.value = component.default
        state.mounted.value = true
      } catch (err) {
        state.error.value = err
      } finally {
        state.isLoading.value = false
      }
    }
  }

  const openDrawer = async () => {
    await loadComponent()
    if (!state.error.value) {
      state.visible.value = true
    }
  }

  const closeDrawer = () => {
    state.visible.value = false
  }

  onBeforeUnmount(closeDrawer)

  return {
    ManagePlanDrawer: state.ManagePlanDrawer,
    mounted: state.mounted,
    error: state.error,
    isLoading: state.isLoading,
    visible: state.visible,
    openDrawer,
    closeDrawer
  }
}
