<template>
  <el-dialog 
    :model-value="visible" 
    title="关联人员信息" 
    :width="500" 
    @close="handleClose"
    :close-on-click-modal="false"
  >
    <div class="staff-select-content">
      <!-- 搜索输入框 -->
      <div class="search-section mb-20px">
        <el-input
          v-model="searchKeyword"
          placeholder="请搜索或下拉选择账号使用人员的信息"
          class="w-full"
          @input="handleSearch"
          @focus="handleSearchFocus"
        >
          <template #suffix>
            <el-icon class="text-gray-400">
              <Search />
            </el-icon>
          </template>
        </el-input>
      </div>

      <!-- 提示信息 -->
      <div class="tip-section mb-15px">
        <div class="text-gray-600 text-14px">
          请从人员信息列表中选择账号使用人员的名称
        </div>
        <div class="text-right">
          <el-button 
            type="text" 
            class="text-blue-600"
            @click="navigateToStaffInfo"
          >
            +添加人员
          </el-button>
        </div>
      </div>

      <!-- 员工列表 -->
      <div class="staff-list-section">
        <div v-if="isSearching" class="text-center py-40px text-gray-500">
          搜索中...
        </div>
        
        <div v-else-if="staffList.length === 0" class="text-center py-40px text-gray-500">
          未找到相关人员
        </div>
        
        <div v-else class="staff-list max-h-300px overflow-y-auto">
          <div 
            v-for="staff in staffList" 
            :key="staff.id"
            class="staff-item px-12px py-10px hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
            @click="selectStaff(staff)"
          >
            <div class="text-blue-600 hover:text-blue-800 font-medium">
              {{ staff.name }}
            </div>
            <div v-if="staff.department || staff.job" class="text-gray-500 text-12px mt-2px">
              {{ [staff.department, staff.job].filter(Boolean).join(' · ') }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="text-center">
        <el-button @click="handleClose">取消</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 员工详情弹窗 -->
  <StaffDetailModal
    :visible="staffDetailVisible"
    :staff="currentStaff"
    :readonly="modalReadonly"
    @close="staffDetailVisible = false"
    @update="handleStaffUpdate"
  />
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import StaffDetailModal from '@/views/internet-dept-manage/components/staff-detail-modal.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  currentRow: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['close', 'select'])

// 状态管理
const searchKeyword = ref('')
const isSearching = ref(false)
const staffList = ref([])
const searchTimeout = ref(null)

// 员工详情弹窗状态
const staffDetailVisible = ref(false)
const currentStaff = ref({})
const modalReadonly = ref(false)

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 弹窗打开时，如果当前行有账号名称，设置为搜索关键词
    if (props.currentRow && props.currentRow.accountName) {
      searchKeyword.value = props.currentRow.accountName
      handleSearch(props.currentRow.accountName)
    } else {
      // 否则加载所有员工
      loadAllStaff()
    }
  } else {
    // 弹窗关闭时重置状态
    resetState()
  }
})

// 重置状态
const resetState = () => {
  searchKeyword.value = ''
  staffList.value = []
  isSearching.value = false
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
    searchTimeout.value = null
  }
}

// 处理搜索
const handleSearch = (value) => {
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }
  
  searchTimeout.value = setTimeout(() => {
    if (value && value.trim()) {
      searchStaff(value.trim())
    } else {
      loadAllStaff()
    }
  }, 300)
}

// 搜索焦点处理
const handleSearchFocus = () => {
  if (searchKeyword.value && searchKeyword.value.trim()) {
    handleSearch(searchKeyword.value.trim())
  }
}

// 搜索员工
const searchStaff = async (keyword) => {
  try {
    isSearching.value = true
    
    const corpId = localStorage.getItem('corpId')
    if (!corpId) {
      ElMessage.error('未获取到企业信息')
      return
    }

    // 导入搜索API - 使用与 select-member 相同的 API
    const { searchMembers } = await import('@/api/internet-dept-manage.js')

    const result = await searchMembers(corpId, keyword.trim(), 1, 50, null, true)

    if (result.success) {
      const newData = result.data?.data || result.data || []

      if (Array.isArray(newData)) {
        staffList.value = newData.map(member => ({
          id: member._id || member.userid,
          name: member.anotherName || member.name,
          phone: member.mobile || '',
          department: member.deptName || '',
          job: member.jobName || '',
          userid: member.userid,
          originalData: member
        }))
      } else {
        staffList.value = []
      }
    } else {
      staffList.value = []
    }
  } catch (error) {
    console.error('搜索员工失败:', error)
    staffList.value = []
    ElMessage.error('搜索员工失败')
  } finally {
    isSearching.value = false
  }
}

// 加载所有员工
const loadAllStaff = async () => {
  try {
    isSearching.value = true
    
    const corpId = localStorage.getItem('corpId')
    if (!corpId) {
      ElMessage.error('未获取到企业信息')
      return
    }

    // 导入员工API - 使用与搜索相同的API
    const { searchMembers } = await import('@/api/internet-dept-manage.js')

    const result = await searchMembers(corpId, '', 1, 50, null, true)

    if (result.success) {
      const newData = result.data?.data || result.data || []

      if (Array.isArray(newData)) {
        staffList.value = newData.map(member => ({
          id: member._id || member.userid,
          name: member.anotherName || member.name,
          phone: member.mobile || '',
          department: member.deptName || '',
          job: member.jobName || '',
          userid: member.userid,
          originalData: member
        }))
      } else {
        staffList.value = []
      }
    } else {
      staffList.value = []
    }
  } catch (error) {
    console.error('加载员工失败:', error)
    staffList.value = []
  } finally {
    isSearching.value = false
  }
}

// 选择员工
const selectStaff = (staff) => {
  emit('select', staff)
  handleClose()
}

// 打开新增员工弹窗
const navigateToStaffInfo = () => {
  currentStaff.value = {}
  modalReadonly.value = false
  staffDetailVisible.value = true
}

// 处理员工更新
const handleStaffUpdate = () => {
  // 员工信息更新后，重新加载员工列表
  if (searchKeyword.value && searchKeyword.value.trim()) {
    searchStaff(searchKeyword.value.trim())
  } else {
    loadAllStaff()
  }
  staffDetailVisible.value = false
}

// 关闭弹窗
const handleClose = () => {
  emit('close')
}
</script>

<style scoped>
.staff-select-content {
  padding: 10px 0;
}

.search-section {
  margin-bottom: 20px;
}

.tip-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.staff-list {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
}

.staff-item {
  transition: background-color 0.3s;
}

.staff-item:hover {
  background-color: #f5f7fa;
}

.staff-item:last-child {
  border-bottom: none;
}
</style>
