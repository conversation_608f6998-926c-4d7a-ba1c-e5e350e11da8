<template>
  <my-layout class="member">
    <layout-item>
      <customer-statistics ref="staticsRef" @uploadCustomer="selectImportType" @updateTotal="updateTotal"></customer-statistics>
      <div class="bg-white rounded mt-10px" common-shadow :class="currentTab === 'noTeamCustomer' ? '' : 'mb-10px'">
        <customer-filter :currentTab="currentTab" :tabs="tabs" :layout="layout" @changeTab="changeTab" @search="search($event)" />
      </div>
      <div v-if="currentTab === 'noTeamCustomer'" class="py-3px">
        <el-text type="danger" class="text-12px">以下客户目前无服务团队为其服务，您可以根据客户阶段、客户意向等信息给客户分配服务团队。</el-text>
      </div>
    </layout-item>
    <layout-main v-loading="loading" :scroll="false" common-shadow class="bg-white rounded">
      <customer-show-table v-model:loading="loading" :canAdd="currentTab === 'noTeamCustomer'" canBatchEdit canSelect canBatchDelete :canMoveGroup="true" :fixed-group-type="currentTab === 'noTeamCustomer' ? 'replace' : ''" ref="customerTableRef" :columns="columns" :params="tableParams" customerType="corpCustomer" corpType="main" />
    </layout-main>
  </my-layout>
  <el-dialog v-model="importTypeVisible" title="请选择导入类型" :width="400" @close="importTypeVisible = false">
    <div class="flex justify-between pb-10px align-center px-10px pointer" border-bottom @click="batchImport('')">
      <div>客户档案</div>
      <el-icon>
        <ArrowRight />
      </el-icon>
    </div>
    <div class="flex justify-between py-10px align-center px-10px pointer" border-bottom @click="batchImport('outpatient')">
      <div>客户档案+门诊病历概要</div>
      <el-icon>
        <ArrowRight />
      </el-icon>
    </div>
    <div class="flex justify-between py-10px align-center px-10px pointer" border-bottom @click="batchImport('inhospital')">
      <div>客户档案+住院病历概要</div>
      <el-icon>
        <ArrowRight />
      </el-icon>
    </div>
    <div class="flex justify-between pt-10px align-center px-10px pointer" @click="batchImport('physicalExaminationTemplate')">
      <div>客户档案+体检档案</div>
      <el-icon>
        <ArrowRight />
      </el-icon>
    </div>
  </el-dialog>
  <import-customer :visible="importVisible" @close="importVisible = false" :width="800" @confirm="importSuccess" :importType="importType"></import-customer>
  <customer-table :importReuslt="importReuslt" :importBoxType="importBoxType" :visible="tableVisible" @close="closeCustomerTable" :width="800" :tableData="importTableData" :importType="importType" @confirm="importComplete"></customer-table>
</template>
<script setup name="CORPMEMBER">
import { ElMessage } from "element-plus";
import { ref, onBeforeMount, computed, nextTick, onMounted } from "vue";
import { memberStore } from "@/store/member";
import { configStore } from "@/store/config";
import importCustomer from "./components/importCustomer/index.vue";
import customerTable from "./components/importCustomer/customer-table.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import customerStatistics from "./components/customer-statistics.vue";
import { ElMessageBox } from "element-plus";
import CustomerFilter from "../components/cutomer-table/customer-filter/customer-filter.vue";
import CustomerShowTable from "../components/cutomer-table/table/table.vue";
const staticsRef = ref();
const config = configStore();
const { getStage } = config;
let importVisible = ref(false);
let importTypeVisible = ref(false);
let total = ref(0);
let loading = ref(false);
let tableVisible = ref(false);
let importTableData = ref([]);
let importType = ref("");
let importBoxType = ref("");
let importReuslt = ref({});
onBeforeMount(async () => {
  getStage();
});

function updateTotal(val) {
  total.value = val;
}

function selectImportType() {
  const { corpInfo } = memberStore();
  const { giveCustomerCount, customerCount } = corpInfo.package;
  if (giveCustomerCount + customerCount - total.value > 500) {
    importTypeVisible.value = true;
  } else {
    ElMessageBox.alert("客户档案数不足500，不支持导入。", "提示", {
      type: "error",
      confirmButtonText: "知道了",
    });
    return;
  }
}
let archivesImportSuccessCount = 0;
let recordImportSuccessCount = 0;
let updateImportSuccessCount = 0;
// 导入数据
function batchImport(type) {
  importVisible.value = true;
  importTypeVisible.value = false;
  importType.value = type;
}
// 获取数据成功
function importSuccess(data) {
  if (data.length === 0) {
    ElMessage.error("导入数据为空");
    return;
  }
  if (data.length > 500) {
    ElMessage.error("导入数据不能超过500条");
    return;
  }
  importBoxType.value = "";
  importVisible.value = false;
  tableVisible.value = true;
  importTableData.value = data;
}
function closeCustomerTable(type, archivesImportSuccessCount, recordImportSuccessCount, updateImportSuccessCount) {
  tableVisible.value = false;
  showImportResult(recordImportSuccessCount, archivesImportSuccessCount, updateImportSuccessCount);
}
// 导入完成
async function importComplete(importFailList, archivesCount = 0, recordCount = 0, updateCount = 0) {
  archivesImportSuccessCount = archivesCount;
  recordImportSuccessCount = recordCount;
  updateImportSuccessCount = updateCount;
  if (importFailList.length === 0) {
    tableVisible.value = false;
    await showImportResult(recordImportSuccessCount, archivesImportSuccessCount, updateImportSuccessCount);
    return;
  }
  importBoxType.value = "error";
  importReuslt.value = {
    successCount: importTableData.value.length - importFailList.length,
    errorCount: importFailList.length,
  };
}

async function showImportResult(recordImportSuccessCount, archivesImportSuccessCount, updateImportSuccessCount) {
  if (!recordImportSuccessCount && !archivesImportSuccessCount && !updateImportSuccessCount) return;
  let title = "";
  let warningName = "客户档案导入成功";
  if (importType.value === "inhospital") {
    title = `为客户新增住院病历概要：${recordImportSuccessCount}份`;
    warningName = "住院病历导入成功";
  } else if (importType.value === "outpatient") {
    warningName = "门诊病历导入成功";
    title = `为客户新增门诊病历概要：${recordImportSuccessCount}份`;
  } else if (importType.value === "physicalExaminationTemplate") {
    warningName = "体检档案导入成功";
    title = `为客户新增体检档案：${recordImportSuccessCount}份`;
  }
  let html = "";
  if (archivesImportSuccessCount) {
    html += `<div>新增客户：${archivesImportSuccessCount}人</div>`;
  }
  if (updateImportSuccessCount) {
    html += `<div>更新客户：${updateImportSuccessCount}人</div>`;
  }
  html += `<div>${title}</div>`;
  customerTableRef.value && customerTableRef.value.search(true);
  staticsRef.value && staticsRef.value.reload();
  await ElMessageBox.alert(html, warningName, { confirmButtonText: "知道了", dangerouslyUseHTMLString: true });
}

const currentTab = ref("teamCustomer");
const tabs = [
  { label: "在服务", name: "teamCustomer" },
  { label: "公共", name: "noTeamCustomer" },
];
const teamCustomerLayout = ["sex", "age", "currentStage", "customerSource", "customerTags", "customerTeam", "plan", "createTimeRange", "arriveTimeRange", "arriveStatusRange", "serviceTimeRange", "customDaysRange"];
const noTeamCustomerLayout = ["customerTags", "currentStage", "customerSource", "createTimeRange"];
const layout = computed(() => (currentTab.value === "teamCustomer" ? teamCustomerLayout : noTeamCustomerLayout));
const teamCustomerColumns = ["name", "ageSex", "mobile", "wxContact", "concactUser", "customerStage", "customerSource", "tagIds", "serviceTeam", "createTime", "arriveTime", "serviceTime"];
const noTeamCustomerColumns = ["name", "ageSex", "mobile", "wxContact", "concactUser", "customerStage", "customerSource", "tagIds", "createTime"];
const columns = computed(() => (currentTab.value === "teamCustomer" ? teamCustomerColumns : noTeamCustomerColumns));

function changeTab(val) {
  currentTab.value = val;
}
// 搜索方法
const tableParams = ref({});
const customerTableRef = ref(null);
async function search(data = {}) {
  const { params = {}, reset = false } = data;
  if (currentTab.value === "teamCustomer" && !("teamId" in params)) {
    params.teamId = "HAS_TEAM";
  } else if (currentTab.value === "noTeamCustomer") {
    params.teamId = "NO_TEAM";
  }
  tableParams.value = params;
  await nextTick();
  customerTableRef.value && customerTableRef.value.search(reset);
}

onMounted(() => search());
</script>
<style scoped lang="scss">
.pointer {
  cursor: pointer;
}
</style>