<template>
    <wx-friend />
    <!-- <full-big-screen></full-big-screen> -->
  </template>
  <script setup>
  import { computed } from "vue";
  import { useRoute } from "vue-router";
  import { useWindowSize } from "@vueuse/core";
  import { tagsStore } from "@/store/tags";
  
  import fullBigScreen from "./full-screen/full-big-screen.vue";
  import EmptyData from "@/components/empty-data.vue";
  import wxFriend from "./wx-friend";
  
  const route = useRoute();
  const { closeTag } = tagsStore();
  
  const { width, height } = useWindowSize();
  function close() {
    closeTag({ path: route.path });
  }
  </script>
  <style lang="scss" scoped>
  </style>
  