<template>
  <div v-loading="loading" class="text-14px">
    <div ref="headRef" class="flex items-center justify-between px-15px py-12px bg-white shadow-xl">
      <div class="flex items-center justify-between w-full">
        <el-select v-model="templateType" @change="getList">
          <el-option label="全部" value="ALL"></el-option>
          <el-option v-for="item in templist" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </div>
    </div>
    <div class="mt-10px">
      <div v-if="records.length === 0" class="pt-30px">
        <empty-data class="mt-20px" :top="0" title="暂无就诊记录"></empty-data>
      </div>
      <el-lock-scrollbar :lock="lock" :max-height="actualMaxHeight" @scroll="handleScroll">
        <div class="px-10px">
          <record-item v-for="record in records" :record="record" :key="record._id" @viewDetail="viewDetail" />
        </div>
      </el-lock-scrollbar>
    </div>
  </div>
  <visit-record-modal :visible="visible" :title="currentTemp.label" :tempList="currentTemp.templateList" :record="currentRecord" :width="width" @close="close" @onRemove="onRemove" />
  <health-index :visible="showHealthIndex" :customer="customer" @close="showHealthIndex = false" />
</template>
<script setup>
import { onMounted, ref, computed, watch, inject, onUnmounted } from "vue";
import { useElementBounding } from "@vueuse/core";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import { getCustomerMedicalRecord, mergeMedicalHisRecord } from "@/api/member";
import useModal from "@/hooks/useModal";
import { memberStore } from "@/store/member";
import { templateStore } from "@/store/template";
import useLockScrollbar from "../useLockScrollbar.js";

import elLockScrollbar from "@/components/el-lock-scrollbar/el-lock-scrollbar";
import EmptyData from "@/components/empty-data.vue";
import RecordItem from "./record-item.vue";
import healthIndex from "./health-index.vue";
import VisitRecordModal from "./visit-record-modal.vue";

const trigger = inject("side-bar-event-trigger");

const props = defineProps({
  maxHeight: { type: Number, default: 300 },
  customer: { type: Object, default: () => ({}) },
});

const { memberInfo, corpInfo } = storeToRefs(memberStore());
const store = templateStore();
const { corpTemplateList } = storeToRefs(store);
const { getCorpTemplate } = store;
const templist = computed(() =>
  (corpTemplateList.value || [])
    .filter((i) => i.parentTemplateType === "medicalRecord")
    .map((i) => ({ value: i.templateType, label: i.service ? i.name : "", templateList: i.templateList }))
    .filter((i) => Boolean(i.value && i.label))
);
const tempKey = computed(() => templist.value.reduce((val, item) => ((val[item.value] = item.label), val), {}));
onMounted(async () => {
  trigger.$on("change-customer-visit-record", getList);
  if (corpTemplateList.value.length === 0) await getCorpTemplate();
  await mergeHisRecord();
  getList();
});

async function mergeHisRecord() {
  if (!corpInfo.value.isConnectHis || !props.customer.customerNumber) return;
  return await mergeMedicalHisRecord({
    memberId: props.customer._id,
    customerNumber: props.customer.customerNumber,
  });
}

onUnmounted(() => trigger.$off("change-customer-visit-record"));

const templateType = ref("ALL");
const records = ref([]);
const loading = ref(false);
watch(() => props.customer._id, getList);

function onRemove() {
  close();
  getList();
}
async function getList() {
  if (loading.value || templist.value.length === 0) return;
  loading.value = true;
  if (!props.customer._id) {
    records.value = [];
    loading.value = false;
    return;
  }
  const params = {
    memberId: props.customer._id,
    corpId: memberInfo.value.corpId,
    medicalType: templateType.value === "ALL" ? templist.value.map((i) => i.value) : [templateType.value],
  };
  const {
    data: { list = [] },
  } = await getCustomerMedicalRecord(params);
  records.value = list.map((i) => ({
    ...i,
    ...compate(i),
    temp: templist.value.find((t) => t.value === i.medicalType),
    title: tempKey.value[i.medicalType] || "",
    date: i.sortTime && dayjs(i.sortTime).isValid() ? dayjs(i.sortTime).format("YYYY-MM-DD") : "",
  }));
  loading.value = false;
}

const corpMap = { "current corp": "本院", other: "其他" };
//兼容旧数据
function compate(data) {
  const res = {};
  res.corp = corpMap[data.corp] || data.corp;
  res.diagnosisName = data.diagnosisNameLabel || data.diagnosisName;
  if (typeof data.files === "string") {
    // 兼容老数据
    res.files = data.files
      .split(",")
      .filter(Boolean)
      .map((url) => ({
        url,
        name: "",
        type: "image/jpeg",
      }));
  }
  return res;
}

const currentTemp = ref({});
const currentRecord = ref({});

const { close, show, visible, width } = useModal(); //  选择客户弹窗
function viewDetail(record) {
  const temp = templist.value.find((i) => i.value === record.medicalType);
  if (temp) {
    currentTemp.value = temp;
    currentRecord.value = record;
    show();
  }
}

const showHealthIndex = ref(false);
const headRef = ref();
const { height: headHeight } = useElementBounding(headRef);
const actualMaxHeight = computed(() => props.maxHeight - headHeight.value - 10);
const { lock, handleScroll } = useLockScrollbar();
</script>
