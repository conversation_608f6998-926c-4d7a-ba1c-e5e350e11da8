<template>
  <el-select multiple collapse-tags v-model="diseases" filterable remote clearable allow-create default-first-option
    reserve-keyword class="ml-5px" placeholder="请搜索" :remote-method="findDisease" :loading="loading"
    :remote-show-suffix="true">
    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
  </el-select>
</template>
<script setup>
import { computed, ref } from 'vue'
import { useVModel, watchDebounced } from '@vueuse/core';
import { getDisease } from "@/api/knowledgeBase";

const emit = defineEmits(['update:modelValue'])
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})
const data = useVModel(props, 'modelValue', emit);
const diseases = computed({
  get: () => Array.isArray(data.value) ? data.value : [],
  set: (val) => { data.value = val }
})

const loading = ref(false);
const diseaseList = ref([]);
const searchText = ref("");
const options = computed(() => {
  const prefix = diseases.value.map(item => ({ value: item, label: item }));
  const next = diseaseList.value.filter(item => !prefix.some(i => i.value === item.value));
  return [...prefix, ...next];
})
function findDisease(val) {
  if (val.trim()) {
    loading.value = true;
    searchText.value = val.trim();
  }
}
async function getDieaseList(diseaseName) {
  const { data } = await getDisease(diseaseName);
  const list = Array.isArray(data.data) ? data.data.map(i => ({ label: i.diseaseName, value: i.diseaseName })).filter((i) => i.label !== diseaseName) : [];
  diseaseList.value = [{ value: diseaseName, label: diseaseName }, ...list];
}
watchDebounced(searchText, async () => {
  if (searchText.value) await getDieaseList(searchText.value);
  loading.value = false;
}, { debounce: 1000 });
</script>
