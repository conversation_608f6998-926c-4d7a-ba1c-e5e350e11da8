<template>
  <div class="model-uploader">
    <div class="buttons-container">
      <!-- 上传按钮 -->
      <input type="file" ref="fileInput" @change="handleFileUpload" multiple directory webkitdirectory class="file-input" />
      <el-button type="primary" @click="triggerFileInput()" v-if="!isRead">上传</el-button>
      <el-button v-if="plyFile" type="primary" @click="openPreview()">查看模型</el-button>
      <!-- 查看按钮 -->
      <el-button v-if="plyFile && !isRead" type="danger" @click="deleteFiles()">删除</el-button>
    </div>
    <div v-if="errorMessage" class="error-message">{{ errorMessage }}</div>

    <!-- 模态框预览 -->
    <div v-if="showModal" class="modal-overlay" @click="closePreview">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>3D模型预览</h3>
          <button class="close-button" @click="closePreview">&times;</button>
        </div>
        <div class="model-viewer-container">
          <ThreeDViewer modelType="ply" :plyPath="plyFile" :textureUrl="textureFile" :width="800" :height="600"   :allow-vertical-flip="true" />
        </div>
      </div>
    </div>
  </div>
</template>
  
<script setup>
import { ref, watch } from "vue";
import ThreeDViewer from "./ThreeDViewer.vue";
import { getRandomStr, file2Base64 } from "@/utils";

const props = defineProps({
  preview: {
    type: Boolean,
    default: true,
  },
  item: {
    type: Object,
    default: () => ({}),
  },
  isRead: {
    type: Boolean,
    default: false,
  },
  value: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(["change", "upload-remove"]);

const fileInput = ref(null);
const loading = ref(false);
const errorMessage = ref("");
const showModal = ref(false);
const uploadedFileName = ref("");

// 文件URL
const plyFile = ref("");
const textureFile = ref("");

// 存储文件列表
const fileList = ref([]);

// 初始化文件数据
const initFileData = (files) => {
  if (!files || files.length === 0) {
    plyFile.value = "";
    textureFile.value = "";
    fileList.value = [];
    return;
  }

  fileList.value = files;

  // 查找 ply 文件和纹理文件
  const plyFileObj = files.find((file) => file.name.toLowerCase().endsWith(".ply"));
  const textureFileObj = files.find((file) => file.name.toLowerCase().endsWith(".jpg") || file.name.toLowerCase().endsWith(".jpeg"));

  if (plyFileObj) {
    plyFile.value = plyFileObj.url;
    uploadedFileName.value = plyFileObj.name;
  }

  if (textureFileObj) {
    textureFile.value = textureFileObj.url;
  }
};

// 监听 value 变化
watch(
  () => props.value,
  (newValue) => {
    initFileData(newValue);
  },
  { immediate: true }
);

const triggerFileInput = () => {
  fileInput.value.click();
};

const handleFileUpload = (event) => {
  loading.value = true;
  errorMessage.value = "";

  const files = event.target.files;

  if (!files || files.length === 0) {
    errorMessage.value = "没有选择任何文件";
    loading.value = false;
    return;
  }

  processFiles(files);
};

// 打开预览模态框
const openPreview = () => {
  showModal.value = true;
};

// 关闭预览模态框
const closePreview = () => {
  showModal.value = false;
};

// 删除已上传文件
const deleteFiles = () => {
  // 如果有URL，需要释放
  if (plyFile.value) {
    URL.revokeObjectURL(plyFile.value);
  }
  if (textureFile.value) {
    URL.revokeObjectURL(textureFile.value);
  }

  // 重置状态
  plyFile.value = "";
  textureFile.value = "";
  uploadedFileName.value = "";
  fileList.value = [];

  // 通知父组件文件已删除
  emit("upload-remove");

  // 通过 change 事件发送空文件列表
  emit("change", {
    title: props.item.title,
    value: fileList.value,
  });

  // 清空文件输入框，允许重新上传相同的文件
  if (fileInput.value) {
    fileInput.value.value = "";
  }
};

const processFiles = async (files) => {
  // 寻找.ply和.jpg文件
  let foundPly = null;
  let foundJpg = null;

  for (let i = 0; i < files.length; i++) {
    const file = files[i];

    if (file.name.toLowerCase().endsWith(".ply")) {
      foundPly = file;
    } else if (file.name.toLowerCase().endsWith(".jpg") || file.name.toLowerCase().endsWith(".jpeg")) {
      foundJpg = file;
    }
  }

  if (!foundPly) {
    errorMessage.value = "未找到.ply文件";
    loading.value = false;
    return;
  }

  // 保存文件名
  uploadedFileName.value = foundPly.name;

  // 如果已有URL，先释放
  if (plyFile.value) {
    URL.revokeObjectURL(plyFile.value);
  }
  if (textureFile.value) {
    URL.revokeObjectURL(textureFile.value);
  }

  // 创建文件的URL
  plyFile.value = URL.createObjectURL(foundPly);

  // 获取文件的base64编码
  const plyBase64 = await file2Base64(foundPly);

  // 构建PLY文件对象
  const plyFileObj = {
    base64: plyBase64,
    name: foundPly.name,
    key: getRandomStr(),
    file: foundPly,
    size: foundPly.size,
    type: foundPly.type,
    loading: false,
    url: plyFile.value,
  };

  // 清空之前的文件列表
  fileList.value = [plyFileObj];

  if (foundJpg) {
    textureFile.value = URL.createObjectURL(foundJpg);

    // 获取纹理文件的base64编码
    const jpgBase64 = await file2Base64(foundJpg);
    // 构建纹理文件对象
    const jpgFileObj = {
      base64: jpgBase64,
      name: foundJpg.name,
      key: getRandomStr(),
      file: foundJpg,
      size: foundJpg.size,
      type: foundJpg.type,
      loading: false,
      url: textureFile.value,
    };
    // 添加纹理文件到文件列表
    fileList.value.push(jpgFileObj);
  } else {
    textureFile.value = ""; // 没有纹理文件
  }

  // 通过 change 事件发送文件列表
  emit("change", {
    title: props.item.title,
    value: fileList.value,
  });

  loading.value = false;
};
</script>
  
<style scoped>
.model-uploader {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.file-input {
  display: none;
}

.buttons-container {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  width: 100%;
}

.action-button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-button {
  background-color: #67c23a;
  color: white;
}

.upload-button:hover {
  background-color: #85ce61;
}

.view-button {
  background-color: #409eff;
  color: white;
}

.view-button:hover {
  background-color: #66b1ff;
}

.delete-button {
  background-color: #f56c6c;
  color: white;
}

.delete-button:hover {
  background-color: #f78989;
}

.upload-icon-small {
  margin-right: 5px;
  font-size: 16px;
}

.file-info {
  width: 100%;
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-bottom: 15px;
  text-align: center;
}

.file-name {
  color: #409eff;
  font-weight: 500;
  margin-top: 5px;
  word-break: break-all;
}

.error-message {
  color: #f56c6c;
  margin-bottom: 15px;
}

.model-viewer-container {
  width: 100%;
  height: 600px;
  border: 1px solid #eee;
  border-radius: 4px;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 5px;
  width: 850px;
  max-width: 95%;
  max-height: 90vh;
  overflow: auto;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.33);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}
</style>
