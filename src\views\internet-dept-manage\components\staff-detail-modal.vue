<template>
  <el-dialog 
    :model-value="visible" 
    @update:model-value="handleVisibleChange" 
    :title="readonly ? '员工详情' : '编辑员工信息'" 
    :width="900" 
    :show-close="false" 
    top="8vh"
    class="staff-detail-dialog"
  >
    <div class="dialog-content">
      
      <el-form 
        ref="formRef" 
        :model="formData" 
        :rules="rules" 
        class="staff-info" 
        :label-width="120" 
        label-position="right" 
        label-suffix="：" 
        :disabled="readonly"
      >
        <!-- 第一行：姓名、性别、手机号 -->
        <el-row :gutter="20">
          <el-col :lg="6" :md="8" :sm="24">
            <el-form-item label="姓名" prop="anotherName" class="is-required">
              <el-input v-model="formData.anotherName" placeholder="请输入姓名" />
            </el-form-item>
          </el-col>
          <el-col :lg="6" :md="8" :sm="24">
            <el-form-item label="性别">
              <el-radio-group v-model="formData.gender" class="horizontal-radio">
                <el-radio label="0">男</el-radio>
                <el-radio label="1">女</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :lg="12" :md="8" :sm="24">
            <el-form-item label="手机号" prop="mobile">
              <el-input v-model="formData.mobile" placeholder="请输入手机号" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第二行：所属部门、岗位 -->
        <el-row :gutter="20">
          <el-col :lg="12" :md="12" :sm="24">
            <el-form-item label="所属部门">
              <el-cascader
                v-if="!readonly"
                v-model="formData.deptIds"
                :options="departmentOptions"
                :props="cascaderProps"
                placeholder="请选择所属部门"
                class="w-full"
                clearable
                multiple
                :show-all-levels="false"
              />
              <div v-else class="readonly-text">
                {{ getDepartmentNames(formData.deptIds) || '-' }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :lg="12" :md="12" :sm="24">
            <el-form-item label="岗位">
              <el-select 
                v-if="!readonly"
                v-model="formData.job" 
                multiple 
                class="w-full" 
                placeholder="请选择岗位" 
                filterable 
                allow-create
              >
                <el-option 
                  v-for="item in jobOptions" 
                  :key="item._id" 
                  :label="item.label" 
                  :value="item.value"
                />
              </el-select>
              <div v-else class="readonly-job-display">
                <span v-if="displayJobNames.length === 0" class="text-gray-400">暂无</span>
                <span v-else class="text-gray-500">{{ displayJobNames.join('、') }}</span>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第三行：院内工号、头像 -->
        <el-row :gutter="20">
          <el-col :lg="12" :md="12" :sm="24">
            <el-form-item label="院内工号">
              <el-input v-model="formData.workNo" placeholder="请输入院内工号" />
            </el-form-item>
          </el-col>
          <el-col :lg="12" :md="12" :sm="24">
            <el-form-item label="头像" v-if="!readonly || formData.avatar">
              <div class="avatar-upload-area">
                <upload-file
                  v-if="!readonly"
                  ref="avatarUploadRef"
                  classnames="h-60px w-60px"
                  :show-file-list="false"
                  list-type=""
                  @hand-file-change="onAvatarChange"
                  file-type="image/*"
                  class="avatar-upload-wrapper"
                >
                  <div
                    v-loading="uploadingAvatar"
                    element-loading-text="正在上传..."
                    class="avatar-upload-placeholder"
                  >
                    <el-avatar
                      v-if="formData.avatar"
                      shape="square"
                      :size="60"
                      fit="fill"
                      :src="getAvatarUrl(formData.avatar)"
                      class="uploaded-avatar"
                    />
                    <div v-else class="upload-placeholder">
                      <el-icon class="upload-icon">
                        <Plus />
                      </el-icon>
                      <span>上传</span>
                    </div>
                  </div>
                </upload-file>
                <el-avatar
                  v-else-if="formData.avatar"
                  shape="square"
                  :size="60"
                  :src="getAvatarUrl(formData.avatar)"
                  fit="fill"
                  class="readonly-avatar"
                >
                  <el-icon><User /></el-icon>
                </el-avatar>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 分割线 -->
        <el-divider class="no-top-margin" />

        <!-- 职称 -->
        <el-row>
          <el-col :span="24">
            <el-form-item label="职称">
              <el-select v-model="formData.title" placeholder="请选择职称" class="w-full">
                <el-option 
                  v-for="title in jobTitleOptions" 
                  :key="title.value" 
                  :label="title.label" 
                  :value="title.value" 
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 门诊科室 -->
        <el-row>
          <el-col :span="24">
            <el-form-item label="门诊科室">
              <el-cascader
                v-model="formData.outpatientDept"
                :options="clinicDepartmentOptions"
                :props="clinicCascaderProps"
                placeholder="请选择门诊科室"
                class="w-full"
                :clearable="!readonly"
                multiple
                :show-all-levels="false"
                :collapse-tags="false"
                :disabled="readonly"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 门诊时间 -->
        <el-row>
          <el-col :span="24">
            <el-form-item label="门诊时间">
              <el-input 
                v-model="formData.outpatientTime" 
                type="textarea" 
                :autosize="{ minRows: 3, maxRows: 6 }" 
                placeholder="请输入门诊时间"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 擅长领域 -->
        <el-row>
          <el-col :span="24">
            <el-form-item label="擅长领域">
              <el-input
                v-model="formData.specialty"
                type="textarea"
                :autosize="{ minRows: 3, maxRows: 6 }"
                placeholder="请输入擅长领域"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 个人介绍 -->
        <el-row>
          <el-col :span="24">
            <el-form-item label="个人介绍">
              <el-input
                v-model="formData.memberTroduce"
                type="textarea"
                :autosize="{ minRows: 3, maxRows: 6 }"
                placeholder="请输入个人介绍"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 分割线 -->
        <el-divider />

        <!-- 对外手机号码 -->
        <el-row>
          <el-col :span="24">
            <el-form-item label="对外手机号码">
              <div class="flex-grow">
                <el-input v-model="formData.callNumber" class="w-full" placeholder="请输入对外手机号码" />
                <div v-if="!readonly" class="mt-10px flex items-center text-13px text-red-500">
                  <el-icon class="text-16px mr-5px">
                    <Warning />
                  </el-icon>
                  <span>对外手机号码会在患者端页面展示并支持患者直接拨打，请慎重填写</span>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 便民服务 -->
        <el-row>
          <el-col :span="24">
            <el-form-item label="便民服务">
              <div class="flex-grow -mb-10px">
                <div v-if="!readonly" class="flex items-center text-blue-500 mb-10px">
                  <el-icon class="text-14px mr-5px cursor-pointer" @click="addService()">
                    <CirclePlusFilled />
                  </el-icon>
                  <span class="cursor-pointer" @click="addService()">添加</span>
                </div>
                <div v-for="(service, index) in formData.convenienceService" :key="index" class="flex items-center mb-10px text-14px">
                  <div class="flex-shrink-0 text-gray-500 mr-10px">页面名称：</div>
                  <el-input v-model="service.name" placeholder="请输入页面名称" class="w-200px mr-10px" />
                  <div class="flex-shrink-0 text-gray-500 mr-10px">页面地址：</div>
                  <el-input v-model="service.href" placeholder="以http://或https://开头" class="flex-grow" />
                  <el-icon v-if="!readonly" class="cursor-pointer text-16px text-red-500 ml-10px" @click="removeService(index)">
                    <Delete />
                  </el-icon>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <div class="text-center">
        <el-button @click="cancel" class="w-100px">取消</el-button>
        <el-button v-if="readonly" type="primary" @click="enableEdit" class="w-100px">编辑</el-button>
        <el-button v-else type="primary" @click="confirm" :loading="loading" class="w-100px">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, onMounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { Plus, Delete, Warning, CirclePlusFilled, User } from '@element-plus/icons-vue';
import { getRandomStr } from '@/utils';
import uploadFile from '@/components/upload-file/index.vue';
import { dbStore } from '@/store/db';

const emits = defineEmits(['close', 'update']);
const props = defineProps({
  visible: { type: Boolean, default: false },
  staff: { type: Object, default: () => ({}) },
  readonly: { type: Boolean, default: true }
});

const formRef = ref(null);
const loading = ref(false);
const internalReadonly = ref(props.readonly);
const avatarUploadRef = ref(null);
const uploadingAvatar = ref(false);

// 选项数据
const departmentOptions = ref([]);
// 使用 store 中的岗位列表
const store = dbStore();
const defaultJobOptions = ref([
  { label: '医生', value: 'doctor', _id: 'doctor' },
  { label: '护士', value: 'nurse', _id: 'nurse' },
  { label: '技师', value: 'technician', _id: 'technician' },
  { label: '管理员', value: 'administrator', _id: 'administrator' }
]);

// 岗位选项计算属性，结合全局岗位和自定义岗位
const jobOptions = computed(() => {
  // 如果全局岗位列表存在，则使用全局列表
  if (store.jobList && Array.isArray(store.jobList)) {
    // 获取用户自定义的岗位（不在全局列表中的岗位）
    const customJobs = Array.isArray(formData.value.job) 
      ? formData.value.job.filter(job => !store.jobList.some(item => item.value === job))
      : [];
    
    // 合并全局岗位和自定义岗位（将 name 映射到 label 以适配 el-option 组件）
    return [
      ...store.jobList.map(item => ({ 
        label: item.name || item.value, 
        value: item.value,
        _id: item._id || item.value 
      })),
      ...customJobs.filter(Boolean).map(job => ({ 
        label: job, 
        value: job, 
        _id: job 
      }))
    ];
  }
  
  // 如果全局岗位列表不存在，则使用默认列表
  return defaultJobOptions.value;
});

const jobTitleOptions = ref([
  { label: '主任医师', value: '主任医师' },
  { label: '副主任医师', value: '副主任医师' },
  { label: '主治医师', value: '主治医师' },
  { label: '医师', value: '医师' },
  { label: '教授', value: '教授' },
  { label: '副教授', value: '副教授' },
  { label: '讲师', value: '讲师' },
  { label: '主任护师', value: '主任护师' },
  { label: '副主任护师', value: '副主任护师' },
  { label: '主管护师', value: '主管护师' },
  { label: '护师', value: '护师' },
  { label: '护士', value: '护士' }
]);

const clinicDepartmentOptions = ref([]);

// 所属部门级联选择器配置（多选）
const cascaderProps = {
  value: '_id', // 使用 _id 字段
  label: 'label',
  children: 'children',
  expandTrigger: 'hover',
  emitPath: false, // 只返回选中节点的值，不返回路径
  checkStrictly: true, // 允许选择任意级别的节点
  multiple: true // 支持多选
};

// 门诊科室级联选择器配置（多选）
const clinicCascaderProps = {
  value: 'value',
  label: 'label',
  children: 'children',
  expandTrigger: 'hover',
  emitPath: false, // 只返回选中节点的值，不返回路径
  checkStrictly: true, // 允许选择任意级别的节点
  multiple: true // 支持多选
};

// 表单数据
const formData = ref({
  anotherName: '',
  gender: '',
  mobile: '',
  deptIds: [], // 所属部门，科室ID数组
  job: [],
  workNo: '',
  title: '',
  outpatientDept: [], // 门诊科室，科室ID数组
  outpatientTime: '',
  specialty: '',
  memberTroduce: '',
  callNumber: '',
  convenienceService: [],
  // 隐藏字段
  userid: '',
  avatar: '',
  hlwSortOrder: {},
  recommended: 0,
  hlwDeptIds: [] // 门诊科室ID数组，与outpatientDept同步
});

// 计算属性
const readonly = computed(() => internalReadonly.value);

// 岗位中英文转换映射
const jobTranslationMap = {
  'doctor': '医生',
  'nurse': '护士',
  'technician': '技师',
  'administrator': '管理员'
};

// 转换岗位显示
const displayJobNames = computed(() => {
  if (!formData.value.job || !Array.isArray(formData.value.job)) return [];
  
  return formData.value.job.map(jobValue => {
    // 优先从全局岗位映射中查找（jobMap 是 value 到 name 的映射）
    if (store.jobMap && store.jobMap[jobValue]) {
      return store.jobMap[jobValue];
    }
    
    // 其次从本地映射中查找
    if (jobTranslationMap[jobValue]) {
      return jobTranslationMap[jobValue];
    }
    
    // 如果是中文值或其他值，直接返回
    return jobValue;
  });
});

// 表单验证规则 - 使用corp-member字段
const rules = {
  anotherName: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  mobile: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
};

// 初始化表单数据
function initFormData() {
  internalReadonly.value = props.readonly;
  
  if (props.staff && Object.keys(props.staff).length > 0) {
    const staff = props.staff;


    
    // 处理便民服务数据
    let convenienceService = [];
    if (Array.isArray(staff.convenienceService)) {
      convenienceService = staff.convenienceService.map(item => ({
        name: item.name || '',
        href: item.href || '',
        key: getRandomStr()
      }));
    } else if (staff.convenienceService && typeof staff.convenienceService === 'string') {
      try {
        const parsed = JSON.parse(staff.convenienceService);
        convenienceService = Array.isArray(parsed) ? parsed.map(item => ({
          name: item.name || '',
          href: item.href || '',
          key: getRandomStr()
        })) : [];
      } catch (e) {
        convenienceService = [];
      }
    }
    
    // 如果没有便民服务，使用空数组
    if (convenienceService.length === 0) {
      convenienceService = [];
    }
    
    // 处理门诊科室数据 - 优先使用hlwDeptIds，兼容outpatientDept和旧的_id字段
    let outpatientDept = [];
    if (Array.isArray(staff.hlwDeptIds) && staff.hlwDeptIds.length > 0) {
      // 使用hlwDeptIds作为门诊科室数据，确保是字符串数组
      outpatientDept = staff.hlwDeptIds.map(id => String(id));
    } else if (Array.isArray(staff.outpatientDept)) {
      // 兼容outpatientDept字段，可能包含deptId或_id
      outpatientDept = staff.outpatientDept.map(id => String(id));
    } else if (staff.outpatientDept && typeof staff.outpatientDept === 'string') {
      outpatientDept = staff.outpatientDept.split(',').map(dept => dept.trim());
    }


    
    // 处理职位数据
    let job = [];
    if (Array.isArray(staff.job)) {
      job = staff.job;
    } else if (staff.job && typeof staff.job === 'string') {
      job = [staff.job];
    }
    
    formData.value = {
      // 基本信息 - 直接使用corp-member字段
      anotherName: staff.anotherName || staff.name || '',
      gender: staff.gender !== undefined && staff.gender !== null && staff.gender !== '' ? staff.gender : '',
      mobile: staff.mobile || staff.phone || '',
      deptIds: Array.isArray(staff.deptIds) ? staff.deptIds : (staff.deptIds ? [staff.deptIds] : []), // 所属部门，科室ID数组
      job: job,
      workNo: staff.doctorNo || staff.workNo || staff.workNumber || '',
      title: staff.title || '',

      // 门诊信息
      outpatientDept: outpatientDept,
      outpatientTime: staff.outpatientTime || '',

      // 专业信息 - 使用corp-member字段，兼容多种字段名
      specialty: staff.specialty || staff.specialtyFields || staff.expertise || '',
      memberTroduce: staff.memberTroduce || '',

      // 联系方式 - 使用corp-member字段
      callNumber: staff.callNumber || '',

      // 便民服务
      convenienceService: convenienceService,

      // 隐藏字段 - 使用corp-member字段
      userid: staff.userid || '',
      avatar: staff.avatar || '',
      hlwSortOrder: staff.hlwSortOrder || {},
      recommended: staff.recommended || 0,
      hlwDeptIds: outpatientDept // 使用处理后的门诊科室数据
    };

    console.log('表单数据初始化:', formData.value);


  } else {
    // 重置表单 - 使用corp-member字段
    formData.value = {
      anotherName: '',
      gender: '',
      mobile: '',
      deptIds: [],
      job: [],
      workNo: '',
      title: '',
      outpatientDept: [],
      outpatientTime: '',
      specialty: '',
      memberTroduce: '',
      callNumber: '',
      convenienceService: [],
      userid: '',
      avatar: '',
      hlwSortOrder: {},
      recommended: 0,
      hlwDeptIds: []
    };
  }
}

// 添加便民服务
function addService() {
  formData.value.convenienceService.push({
    name: '',
    href: '',
    key: getRandomStr()
  });
}

// 删除便民服务
function removeService(index) {
  formData.value.convenienceService.splice(index, 1);
}

// 启用编辑
function enableEdit() {
  internalReadonly.value = false;
}

// 取消
function cancel() {
  emits('close');
}

// 确认提交
async function confirm() {
  try {
    await formRef.value.validate();
    loading.value = true;
    
    // 调用更新API
    const result = await updateStaffInfo();
    
    if (result.success) {
      ElMessage.success('保存成功');
      // 传递更新后的完整员工数据
      const updatedStaff = {
        ...formData.value,
        // 确保传递正确的字段映射
        workNo: formData.value.workNo,
        outpatientDept: formData.value.outpatientDept
      };
      emits('update', updatedStaff);
      emits('close');
    } else {
      ElMessage.error(result.message || '保存失败');
    }
  } catch (error) {
    if (error.message) {
      ElMessage.error(error.message);
    }
  } finally {
    loading.value = false;
  }
}

// 获取头像URL，如果没有头像则使用默认头像
function getAvatarUrl(avatar) {
  if (avatar && avatar.trim()) {
    return avatar;
  }
  // 使用默认头像
  return 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png';
}

// 头像上传处理
async function onAvatarChange(fileList) {
  if (!fileList || fileList.length === 0) return;
  
  const file = fileList[fileList.length - 1];
  if (!file) return;
  
  uploadingAvatar.value = true;
  
  try {
    // 导入上传API
    const { updateFile } = await import('@/api/uploadFIle.js');
    
    // 生成唯一文件名
    const fileName = `avatar_${Date.now()}_${parseInt(Math.random() * 1000000)}`;
    
    // 上传文件
    const result = await updateFile(fileName, file.raw, 'head');
    
    if (result.download_url) {
      formData.value.avatar = result.download_url;
      ElMessage.success('头像上传成功');
    } else {
      throw new Error('上传结果无效');
    }
  } catch (error) {
    ElMessage.error('头像上传失败: ' + (error.message || '未知错误'));
  } finally {
    uploadingAvatar.value = false;
    // 清空文件选择
    if (avatarUploadRef.value) {
      avatarUploadRef.value.clearFiles();
    }
  }
}

// 更新员工信息
async function updateStaffInfo() {
  try {
    const { updateCorpMember } = await import('@/api/internet-dept-manage');
    
    const corpId = localStorage.getItem('corpId');
    if (!corpId) {
      throw new Error('未获取到企业ID，请重新登录');
    }
    
    // 直接使用corp-member字段格式，无需转换
    const updateParams = {
      anotherName: formData.value.anotherName,
      gender: formData.value.gender,
      mobile: formData.value.mobile,
      deptIds: formData.value.deptIds || [],
      job: formData.value.job,
      doctorNo:formData.value.workNo,
      workNo: formData.value.workNo,
      title: formData.value.title,
      outpatientDept: formData.value.outpatientDept,
      outpatientTime: formData.value.outpatientTime,
      specialty: formData.value.specialty,
      memberTroduce: formData.value.memberTroduce,
      callNumber: formData.value.callNumber,
      avatar: formData.value.avatar,
      convenienceService: formData.value.convenienceService.map(item => ({
        name: item.name,
        href: item.href
      })),
      hlwSortOrder: formData.value.hlwSortOrder,
      recommended: formData.value.recommended,
      hlwDeptIds: formData.value.outpatientDept || [] // 门诊科室ID数组
    };

    console.log('更新参数:', updateParams);
    
    const result = await updateCorpMember(formData.value.userid, corpId, updateParams);

    // 注意：现在直接使用corp-member API，无需额外同步

    return result;
    
  } catch (error) {
    return {
      success: false,
      message: '更新失败: ' + error.message
    };
  }
}

// 注意：由于现在直接使用corp-member API，不再需要同步函数

// 处理弹窗关闭
function handleVisibleChange(value) {
  if (!value) {
    emits('close');
  }
}

// 监听 visible 变化，初始化数据
watch(() => props.visible, (newVal) => {
  if (newVal) {
    initFormData();
  }
}, { immediate: true });

// 监听 readonly 变化
watch(() => props.readonly, (newVal) => {
  internalReadonly.value = newVal;
}, { immediate: true });

// 监听 staff 变化
watch(() => props.staff, () => {
  if (props.visible) {
    initFormData();
  }
}, { deep: true });

// 根据部门ID获取部门名称
function getDepartmentName(deptId) {
  if (!deptId) {
    return '-';
  }

  let deptName = null;

  function findDeptName(options, targetId) {
    for (const option of options) {
      // 检查当前层级的选项
      if (option.value === String(targetId) && !option.disabled) {
        deptName = option.label;
        return true;
      }

      // 递归检查子选项
      if (option.children && option.children.length > 0) {
        for (const child of option.children) {
          if (child.value === String(targetId)) {
            deptName = child.label;
            return true;
          }

          // 如果子选项还有子选项，继续递归
          if (child.children && child.children.length > 0) {
            if (findDeptName(child.children, targetId)) {
              return true;
            }
          }
        }
      }
    }
    return false;
  }

  findDeptName(departmentOptions.value, deptId);
  return deptName || '-'; // 如果找不到名称，返回'-'而不是ID
}

// 根据部门ID数组获取部门名称
function getDepartmentNames(deptIds) {
  if (!deptIds || !Array.isArray(deptIds) || deptIds.length === 0) {
    return '-';
  }

  const deptNames = deptIds.map(deptId => {
    let deptName = null;

    function findDeptName(options, targetId) {
      for (const option of options) {
        // 检查当前层级的选项，使用 _id 进行匹配
        if (option._id === String(targetId) && !option.disabled) {
          deptName = option.label;
          return true;
        }

        // 递归检查子选项
        if (option.children && option.children.length > 0) {
          for (const child of option.children) {
            if (child._id === String(targetId)) {
              deptName = child.label;
              return true;
            }

            // 如果子选项还有子选项，继续递归
            if (child.children && child.children.length > 0) {
              if (findDeptName(child.children, targetId)) {
                return true;
              }
            }
          }
        }
      }
      return false;
    }

    findDeptName(departmentOptions.value, deptId);
    return deptName || `未知部门(${deptId})`;
  }).filter(name => name);

  return deptNames.length > 0 ? deptNames.join(', ') : '-';
}

// 根据科室ID获取科室名称（与主页面保持一致）
function getDeptNames(deptIds) {
  if (!deptIds || !Array.isArray(deptIds) || deptIds.length === 0) {
    return '-';
  }

  const deptNames = deptIds.map(deptId => {
    // 从门诊科室选项中查找科室名称
    let deptName = null;

    function findDeptName(options, targetId) {
      for (const option of options) {
        if (option.children) {
          for (const child of option.children) {
            // 直接匹配value
            if (child.value === String(targetId)) {
              deptName = child.label;
              return true;
            }
            // 兼容性检查：检查_originalData
            if (child._originalData) {
              const { deptId: origDeptId, _id } = child._originalData;
              if ((origDeptId && String(origDeptId) === String(targetId)) ||
                  (_id && String(_id) === String(targetId))) {
                deptName = child.label;
                return true;
              }
            }
          }
        }
      }
      return false;
    }

    findDeptName(clinicDepartmentOptions.value, deptId);
    return deptName || `未知科室(${deptId})`;
  }).filter(name => name);

  return deptNames.length > 0 ? deptNames.join(', ') : '-';
}

// 更新门诊科室选中值
function updateOutpatientDeptSelection() {
  if (!props.staff || !formData.value.outpatientDept || formData.value.outpatientDept.length === 0) {
    return;
  }

  // 如果门诊科室数据是ID数组，需要验证这些ID在选项中是否存在
  const validDeptIds = [];
  const targetIds = formData.value.outpatientDept.map(id => String(id)); // 确保目标ID是字符串

  function findValidIds(options, targetIds) {
    for (const option of options) {
      if (!option.disabled) {
        // 检查当前选项值是否匹配
        if (targetIds.includes(String(option.value))) {
          validDeptIds.push(String(option.value));
        } else if (option._originalData) {
          // 兼容性检查：检查是否匹配旧的_id字段
          const { deptId, _id } = option._originalData;
          if ((deptId && targetIds.includes(String(deptId))) ||
              (_id && targetIds.includes(String(_id)))) {
            // 如果匹配旧字段，使用新的value值
            validDeptIds.push(String(option.value));
          }
        }
      }
      if (option.children && option.children.length > 0) {
        findValidIds(option.children, targetIds);
      }
    }
  }

  findValidIds(clinicDepartmentOptions.value, targetIds);

  if (validDeptIds.length > 0) {
    formData.value.outpatientDept = validDeptIds;
  }
}

// 加载科室选项
async function loadDepartmentOptions() {
  try {
    // 动态导入API
    const [
      { getDeptList },
      { getInternetDeptList }
    ] = await Promise.all([
      import('@/api/dept-manage'),
      import('@/api/internet-dept-manage')
    ]);
    
    // 获取企业ID
    const corpId = localStorage.getItem('corpId');
    if (!corpId) {
      throw new Error('未获取到企业ID');
    }
    
    // 并行获取普通科室管理架构和互联网科室管理架构
    const [deptResult, internetDeptResult] = await Promise.all([
      getDeptList({ corpId }),
      getInternetDeptList()
    ]);
    
    // 处理【所属部门】选项 - 使用普通科室管理的级联结构
    if (deptResult.success && deptResult.data && deptResult.data.list) {
      // 构建级联选择器数据结构
      const buildCascaderTree = (deptList) => {
        const deptMap = new Map();
        const rootDepts = [];
        
        // 创建部门映射
        deptList.forEach(dept => {
          deptMap.set(dept._id, {
            _id: dept._id, // 使用 _id 字段
            value: dept._id, // 使用 _id 作为 value
            label: dept.deptName || dept.label,
            children: [],
            _originalData: dept
          });
        });
        
        // 构建树形结构
        deptList.forEach(dept => {
          const deptNode = deptMap.get(dept._id);
          if (dept.parentId && deptMap.has(dept.parentId)) {
            // 有父级部门，添加到父级的children中
            const parent = deptMap.get(dept.parentId);
            parent.children.push(deptNode);
          } else {
            // 没有父级部门，作为根级部门
            rootDepts.push(deptNode);
          }
        });
        
        // 清理空的children数组
        const cleanupTree = (nodes) => {
          nodes.forEach(node => {
            if (node.children.length === 0) {
              delete node.children;
            } else {
              cleanupTree(node.children);
            }
          });
        };
        
        cleanupTree(rootDepts);
        return rootDepts;
      };
      
      departmentOptions.value = buildCascaderTree(deptResult.data.list);
      console.log('所属部门选项构建完成:', departmentOptions.value);
    }
    
    // 处理【门诊科室】选项 - 使用互联网科室管理的数据结构
    if (internetDeptResult.success && internetDeptResult.data && internetDeptResult.data.list) {
      // 门诊科室级联选择器构建逻辑，兼容旧的_id字段
      const buildClinicTree = (list) => {
        // 获取所有院区
        const districts = list.filter(item => item.type === 'district');

        return districts.map(district => {
          // 查找该院区下的所有科室 - 兼容多种关联方式
          const depts = list.filter(item => {
            if (item.type !== 'dept') return false;

            // 方式1：通过areaId关联（新方式）
            if (item.areaId === district.districtId) return true;

            // 方式2：通过areaId关联院区的_id（兼容方式）
            if (item.areaId === district._id) return true;

            // 方式3：通过parentId直接关联院区_id（旧方式）
            if (!item.parentId && item.areaId === district.districtId) return true;

            return false;
          });

          return {
            value: district.districtId,
            label: district.districtName,
            disabled: true, // 院区不可选择
            children: buildDeptHierarchy(depts)
          };
        });
      };

      // 构建科室层级关系
      const buildDeptHierarchy = (depts) => {
        // 创建科室映射
        const deptMap = new Map();

        // 先创建所有科室节点
        depts.forEach(dept => {
          const deptValue = String(dept.deptId || dept.hlwDeptId || dept._id);
          const deptKey = dept.deptId || dept.hlwDeptId; // 优先使用业务ID作为key

          const deptNode = {
            value: deptValue,
            label: dept.deptName || dept.hlwDeptName,
            children: [],
            _originalData: {
              deptId: dept.deptId,
              hlwDeptId: dept.hlwDeptId,
              _id: dept._id,
              deptName: dept.deptName || dept.hlwDeptName,
              areaId: dept.areaId,
              parentId: dept.parentId,
              level: dept.level
            }
          };

          // 使用业务ID作为主key
          if (deptKey) {
            deptMap.set(deptKey, deptNode);
          }
          // 同时也用MongoDB ID作为备用key，确保兼容性
          if (dept._id && dept._id !== deptKey) {
            deptMap.set(dept._id, deptNode);
          }
        });

        const rootDepts = [];

        // 构建父子关系
        depts.forEach(dept => {
          const deptKey = dept.deptId || dept.hlwDeptId;
          const deptNode = deptMap.get(deptKey) || deptMap.get(dept._id);

          if (dept.parentId) {
            // 有父科室，查找父科室节点
            // 支持多种查找方式：业务ID匹配
            let parentDept = null;

            // 方式1：直接通过业务ID查找
            parentDept = deptMap.get(dept.parentId);

            // 方式2：如果没找到，遍历查找匹配的父科室
            if (!parentDept) {
              for (const [, node] of deptMap.entries()) {
                const originalData = node._originalData;
                if (originalData.deptId === dept.parentId ||
                    originalData.hlwDeptId === dept.parentId ||
                    originalData._id === dept.parentId) {
                  parentDept = node;
                  break;
                }
              }
            }

            if (parentDept) {
              // 父科室在当前院区的科室列表中
              parentDept.children.push(deptNode);
            } else {
              // 父科室不在当前院区，作为根级科室处理
              rootDepts.push(deptNode);
            }
          } else {
            // 没有父科室，作为根级科室
            rootDepts.push(deptNode);
          }
        });

        // 排序函数
        const sortDepts = (deptList) => {
          return deptList.sort((a, b) => {
            const aSort = a._originalData?.sort || 0;
            const bSort = b._originalData?.sort || 0;
            return aSort - bSort;
          }).map(dept => {
            if (dept.children && dept.children.length > 0) {
              dept.children = sortDepts(dept.children);
            }
            return dept;
          });
        };

        return sortDepts(rootDepts);
      };

      clinicDepartmentOptions.value = buildClinicTree(internetDeptResult.data.list);



      // 门诊科室选项加载完成后，更新表单中的门诊科室选中值
      updateOutpatientDeptSelection();
    }
    
    // 如果没有获取到数据，设置为空数组，不使用默认选项
    if (departmentOptions.value.length === 0) {
      departmentOptions.value = [];
    }
    
    if (clinicDepartmentOptions.value.length === 0) {
      clinicDepartmentOptions.value = [];
    }
    
  } catch (error) {
    console.error('加载科室选项失败:', error);
    // 设置为空数组，不使用默认选项
    departmentOptions.value = [];
    clinicDepartmentOptions.value = [];
  }
}

// 组件挂载时加载选项
onMounted(() => {
  loadDepartmentOptions();
  // 加载全局岗位列表
  store.getJobList();
});
</script>

<style scoped lang="scss">
.dialog-content {
  padding: 20px;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  max-height: calc(80vh - 120px);
}

.staff-info .el-form-item {
  margin-bottom: 20px;
}

.pb-15px {
  padding-bottom: 15px;
}

.border-b {
  border-bottom: 1px solid #ebeef5;
}

.mb-20px {
  margin-bottom: 20px;
}

.mb-15px {
  margin-bottom: 15px;
}

.mb-10px {
  margin-bottom: 10px;
}

.mb-5px {
  margin-bottom: 5px;
}

.mr-20px {
  margin-right: 20px;
}

.mr-15px {
  margin-right: 15px;
}

.mr-10px {
  margin-right: 10px;
}

.mr-5px {
  margin-right: 5px;
}

.ml-10px {
  margin-left: 10px;
}

.mt-10px {
  margin-top: 10px;
}

.text-16px {
  font-size: 16px;
}

.text-14px {
  font-size: 14px;
}

.text-13px {
  font-size: 13px;
}

.text-gray-600 {
  color: #6b7280;
}

.text-gray-500 {
  color: #9ca3af;
}

.text-blue-500 {
  color: #3b82f6;
}

.text-red-500 {
  color: #ef4444;
}

.font-semibold {
  font-weight: 600;
}

.w-full {
  width: 100%;
}

.w-200px {
  width: 200px;
}

.w-100px {
  width: 100px;
}

.flex {
  display: flex;
}

.flex-grow {
  flex-grow: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.items-center {
  align-items: center;
}

.cursor-pointer {
  cursor: pointer;
}

.-mb-10px {
  margin-bottom: -10px;
}

.avatar-upload-area {
  .avatar-upload-wrapper {
    display: inline-block;
    
    .avatar-upload-placeholder {
      position: relative;
      
      .uploaded-avatar {
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }
      }
      
      .upload-placeholder {
        width: 60px;
        height: 60px;
        background-color: #f5f7fa;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          border-color: #409eff;
          background-color: #ecf5ff;
        }

        .upload-icon {
          font-size: 16px;
          color: #409eff;
        }

        span {
          font-size: 10px;
          color: #409eff;
          margin-top: 2px;
        }
      }
    }
  }
  
  .readonly-avatar-container {
    display: inline-block;
    position: relative;
    
    .readonly-avatar {
      border: 1px solid #e4e7ed;
      border-radius: 4px;
    }
  }
}

.readonly-job-display {
  min-height: 32px;
  padding: 6px 12px;
  line-height: 1.5;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #f5f7fa;
  color: #606266;
  display: flex;
  align-items: center;
}

.horizontal-radio {
  .el-radio {
    margin-right: 20px;
    display: inline-block !important;
  }
  
  // 强制性别选项始终横向排列
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  align-items: center !important;
}

:deep(.el-textarea .el-input__count) {
  bottom: 5px;
  right: 10px;
}

:deep(.staff-detail-dialog) {
  .el-dialog {
    margin-top: 8vh !important;
    margin-bottom: 8vh !important;
    max-height: 84vh !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
  }
  
  .el-dialog__body {
    padding: 0 !important;
    flex: 1 !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
  }
  
  .el-dialog__header {
    padding: 20px 20px 10px !important;
    flex-shrink: 0 !important;
  }
  
  .el-dialog__footer {
    padding: 10px 20px 20px !important;
    flex-shrink: 0 !important;
  }
}

// 修复弹窗遮罩层导致的页面滚动问题
:deep(.el-overlay) {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  overflow: hidden !important;
}

:deep(.el-overlay-dialog) {
  overflow: hidden !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 8vh 20px !important;
}

// 表单样式优化
:deep(.el-form-item__label) {
  text-align: right !important;
  white-space: nowrap;
  display: inline-block;
  line-height: 1.5;
}

:deep(.el-form-item__content) {
  display: inline-block;
  vertical-align: top;
  line-height: 1.5;
}

// 禁用状态样式
:deep(.el-form-item.is-disabled) {
  .el-input__inner,
  .el-textarea__inner,
  .el-select .el-input__inner,
  .el-radio__input,
  .el-checkbox__input {
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #606266;
  }
  
  .el-input__inner,
  .el-textarea__inner {
    cursor: not-allowed;
  }
  
  .el-select .el-input__inner {
    cursor: not-allowed;
  }
  
  .el-radio__label,
  .el-checkbox__label {
    color: #c0c4cc;
  }
}

// 强制性别单选框横向排列
:deep(.horizontal-radio) {
  .el-radio-group {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    align-items: center !important;
  }
  
  .el-radio {
    margin-right: 20px !important;
    margin-bottom: 0 !important;
    white-space: nowrap !important;
  }
  
  .el-radio__label {
    white-space: nowrap !important;
  }
}

.readonly-text {
  color: #606266;
  font-size: 14px;
  line-height: 32px;
  min-height: 32px;
  padding: 0 11px;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.no-top-margin {
  margin-top: 0 !important;
}
</style>
