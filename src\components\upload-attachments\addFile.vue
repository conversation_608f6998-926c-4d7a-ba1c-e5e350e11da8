<template>
  <div>
    <el-dialog v-model="addFileDialogVisible" :title="dialogTitle" draggable :width="480">
      <div>
        <!-- <el-input
          v-model="input1"
          class="w-450px px-30px py-10px"
          size="large"
          :placeholder="placeholder"
          :suffix-icon="Search"
        /> -->
        <div class="file-content">
          <div v-if="list.length > 0">
            <div class="file-cell" v-for="item in list" :key="item._id" @click="selectFile(item)">
              <div class="file-cell-name">
                <div class="t_1 flex" @click="readFile(item)">
                  <div>{{ item.name }}</div>
                  <el-icon color="#006eff"><Tickets /></el-icon>
                </div>
                <div class="t_2">创建时间: {{ dayjs(item.createTime).format("YYYY-MM-DD") }}</div>
              </div>
              <img src="@/assets/check.png" class="file-cell-radio" v-if="item._id !== file._id" />
              <img src="@/assets/check-active.png" class="file-cell-radio" v-else />
            </div>
          </div>
          <empty-data v-else :top="0" title="暂无待办" padding="0 0 15px" text-top="10px" :image-width="200"></empty-data>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogCancel">取消</el-button>
          <el-button type="primary" @click="dialogSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
  <survery-dialog :data="currentSurvery" :visible="visible" @close="visible = false" />
</template>
<script setup>
import { computed, ref } from "vue";
import { getList as getSurveyList } from "@/api/survery";
import SurveryDialog from "@/views/system/survery/dialog.vue";
import dayjs from "dayjs";
const addFileDialogVisible = ref(false);
import { ElMessage } from "element-plus";
const file = ref({});
import EmptyData from "@/components/empty-data.vue";
const type = ref("");
const list = ref([]);
const emit = defineEmits(["onGetSelectFile"]);
const visible = ref(false);
const currentSurvery = ref({});
const dialogTitle = computed(() => {
  return type.value === "article" ? `宣教文章列表(${list.value.length})` : `调查问卷列表(${list.value.length})`;
});
const placeholder = computed(() => {
  return type.value === "article" ? `请输入宣教文章标题` : `请输入问卷标题`;
});
function dialogCancel() {
  addFileDialogVisible.value = false;
}
function open(item, f) {
  file.value = f;
  addFileDialogVisible.value = true;
  type.value = item;
  getList();
}
function readFile(item) {
  if (type.value === "questionnaire") {
    currentSurvery.value = item;
    visible.value = true;
  }
}
function dialogSubmit() {
  if (!file.value._id) {
    ElMessage.warning("请选择!");
    return;
  }
  let obj = {
    type: type.value,
    name: file.value.name,
  };
  if (type.value === "questionnaire") {
    obj["surveryId"] = file.value._id;
  }
  emit("onGetSelectFile", obj);
  addFileDialogVisible.value = false;
}
async function getList() {
  if (type.value === "article") {
  } else {
    const { success, message, data = {} } = await getSurveyList(1, 99, "", "enable");
    if (success) {
      const { list: tableData = [], total: count = 0 } = data;
      list.value = tableData;
    }
  }
}
async function selectFile(item) {
  file.value = file.value._id === item._id ? {} : item;
}
defineExpose({
  open,
});
</script>
<style lang="scss">
.file-content {
  height: 300px;
  overflow-y: auto;
  .file-cell {
    display: flex;
    justify-content: space-between;
    padding: 0px 20px 10px 20px;
    align-items: center;
    border-bottom: 1px solid #eee;
    .file-cell-name {
      .t_1 {
        color: #000;
        font-weight: 600;
        align-items: center;
        padding-right: 10px;
        cursor: pointer;
      }
      .t_2 {
        font-size: 12px;
        padding-top: 10px;
      }
    }
    .file-cell-radio {
      width: 18px;
      height: 18px;
      cursor: pointer;
    }
  }
}
</style>
