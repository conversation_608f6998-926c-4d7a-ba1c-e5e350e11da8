import { markRaw, ref, onMounted } from 'vue';
import { watchDebounced, useWindowSize } from "@vueuse/core";
import echarts from '@/utils/echarts';

function getServiceOptions() {

  var data = [
    {
      name: '新增客户数',
      percent: 54,
      value: 239
    }, {
      name: '服务记录',
      percent: 44,
      value: 408
    }, {
      name: '会话消息数',
      percent: 35,
      value: 1592
    }, {
      name: '今日待办',
      percent: 30,
      value: 532
    }, {
      name: '完成率',
      percent: 80,
      value: 446

    }]

  var titleArr = [], seriesArr = [];
  var colors = [
    ['#7cd6f8', 'rgba(124, 214, 248,.5)'],
    ['#ee7779', 'rgba(238, 119, 121,.5)'],
    ['#f9dc74', 'rgba(249, 220, 116,.5)'],
    ['#5792f6', 'rgba(87, 146, 246,.5)'],
    ['#57bd94', 'rgba(87, 189, 148,.5)']
  ]
  data.forEach(function (item, index) {
    titleArr.push(
      {
        text: item.name,
        left: index * 20 + 10 + '%',
        top: '85%',
        textAlign: 'center',
        textStyle: {
          fontWeight: 'normal',
          fontSize: '16',
          color: colors[index][0],
          textAlign: 'center',
        },
      }
    );
    seriesArr.push(
      {
        name: item.name,
        type: 'pie',
        clockWise: false,
        radius: [35, 40],
        itemStyle: {
          normal: {
            color: colors[index][0],
            shadowColor: colors[index][0],
            shadowBlur: 0,
            label: {
              show: false
            },
            labelLine: {
              show: false
            },
          }
        },
        hoverAnimation: false,
        center: [index * 20 + 10 + '%', '50%'],
        data: [{
          value: item.percent,
          label: {
            normal: {
              formatter: function (params) {
                return item.value;
              },
              position: 'center',
              show: true,
              textStyle: {
                fontSize: '20',
                fontWeight: 'bold',
                color: colors[index][0]
              }
            }
          },
        }, {
          value: 100 - item.percent,
          name: 'invisible',
          itemStyle: {
            normal: {
              color: colors[index][1]
            },
            emphasis: {
              color: colors[index][1]
            }
          }
        }]
      }
    )
  });


  return {
    // backgroundColor: "#fff",
    grid: {
      top: 0,       // 图表距离容器顶部的距离
      bottom: 0,    // 图表距离容器底部的距离
      left: 0,
      right: 0
    },
    title: titleArr,
    series: seriesArr
  }
}


export default function useTodayPieChart(chartRef) {
  const chart = ref(null);
  const { width, height } = useWindowSize();

  function setChart() {
    chart.value = markRaw(echarts.init(chartRef.value));
  }

  function paint(repaint = false) {
    if (chartRef.value && !chart.value) {
      setChart();
      repaint = false;
    };
    if (!chart.value) return;
    const option = getServiceOptions();
    chart.value.setOption(option, repaint);
  }
  onMounted(paint)
  // watchDebounced([todoData, finishData], n => {
  //   paint(true)
  // }, { debounce: 500, maxWait: 1000 })
  watchDebounced([width, height], () => {
    chart.value && chart.value.resize();
  }, { debounce: 500, maxWait: 1500 })

}