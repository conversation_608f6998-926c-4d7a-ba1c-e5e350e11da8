<template>
  <el-dialog :model-value="visible" :width="width" :close-on-click-modal="false" :show-close="false">
    <template #header>
      <div v-if="importBoxType === 'error'" class="font-bold flex">
        <div>提示</div>
        <img src="@/assets/gantanhaozhong.png" alt="" class="w-25px h-25px ml-5px pointer" @click="prompt" />
      </div>
      <div v-else class="font-bold">请确认导入信息</div>
    </template>
    <div v-if="importBoxType === 'error'">
      <div class="font-bold">导入成功数据： {{ importReuslt.successCount }}条</div>
      <div>
        <span class="font-bold">
          导入失败数据：
          <span style="color: red">{{ importReuslt.errorCount }}条</span>
        </span>
        <span style="color: red" class="pl-15px">【红色异常】</span>
        需按要求录入正确数据才可导入，
        <span class="text-yellow-300">【黄色异常】</span>
        可以忽略该字段导入该条患者数据，但是该字段的数据不做导入
      </div>
    </div>
    <div v-else>
      <div>1.必填字段不能为空，否则会导入失败。</div>
      <div>
        2.为了保证客户唯一性
        <span style="color: red">【身份证号】</span>
        和
        <span style="color: red">【客户档案编号】</span>
        需保证必填一个字段以校验客户唯一性。否则会导入失败。
      </div>
    </div>
    <div style="height: 400px">
      <div class="table">
        <div class="row header">
          <div class="cell" v-for="(header, index) in tabBarList" :key="index">{{ header }}</div>
          <div v-if="importBoxType === 'error'" class="cell operate-cell">操作</div>
        </div>
        <div class="row" v-for="(row, rowIndex) in myTableData" :key="rowIndex">
          <div class="cell" v-for="(header, index) in tabBarList" :key="index">
            <div v-if="Array.isArray(row[header])" class="w-full">
              <div class="child-cell" v-for="(item, itemIndex) in row[header]" :key="itemIndex">{{ item }}</div>
            </div>
            <div v-else>
              <template v-if="importBoxType === 'error'">
                <div class="flex align-center">
                  <div :class="styleClass(header, row[header], row)">{{ row[header] ? row[header] : mustJudgment(header, row[header], row) ? "" : "?" }}</div>
                  <el-icon size="12" class="ml-5px pointer" v-if="!mustJudgment(header, row[header], row) || !warningJudgment(header, row[header], row)">
                    <Edit @click="editFile(header, row[header], rowIndex)"></Edit>
                  </el-icon>
                </div>
              </template>
              <template v-else>
                {{ row[header] }}
              </template>
            </div>
          </div>
          <div v-if="importBoxType === 'error'" class="cell operate-cell">
            <el-button type="text" @click="remove(row, rowIndex)">删除</el-button>
            <el-button type="text" v-if="rowMustJudgment(row)" @click="ignore(row, rowIndex)">
              {{ ignoreDataList.includes(rowIndex) ? "取消忽略" : "忽略异常" }}
            </el-button>
          </div>
        </div>
      </div>
      <!-- <el-table stripe style="height: 100%" :data="myTableData">
        <el-table-column v-for="item in columns" :property="item.key" :label="item.title" min-width="160">
          <template #default="{ row, $index }">
            <template v-if="importBoxType === 'error'">
              <div class="flex align-center">
                <div :class="styleClass(item.key, row[item.key], row)">{{ row[item.key] ? row[item.key] : mustJudgment(item.key, row[item.key], row) ? "" : "?" }}</div>
                <el-icon size="12" class="ml-5px pointer" v-if="!mustJudgment(item.key, row[item.key], row) || !warningJudgment(item.key, row[item.key], row)">
                  <Edit @click="editFile(item.key, row[item.key], $index)" />
                </el-icon>
              </div>
            </template>
            <template v-else>
              {{ row[item.key] }}
            </template>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="140" fixed="right" v-if="importBoxType === 'error'">
          <template #default="{ row, $index }">
            <el-button type="text" @click="remove(row, $index)">删除</el-button>
            <el-button type="text" v-if="rowMustJudgment(row)" @click="ignore(row, $index)">
              {{ ignoreDataList.includes($index) ? "取消忽略" : "忽略异常" }}
            </el-button>
          </template>
        </el-table-column>
      </el-table> -->
    </div>
    <template #footer>
      <div v-if="importBoxType === 'error'" class="flex justify-center">
        <el-button class="w-100px" @click="close('reupload')">关闭</el-button>
        <el-button class="w-100px" type="primary" @click="exportExcel()">导出表格</el-button>
        <el-button class="w-100px" type="primary" @click="confirm()">重新导入</el-button>
      </div>
      <div v-else class="flex justify-center">
        <el-button class="w-100px" @click="close('')">取消</el-button>
        <el-button class="w-100px" type="primary" @click="confirm()" v-loading.fullscreen.lock="fullscreenLoading">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <el-dialog title="修改" v-model="filedvisible" width="400" @close="closeFiled">
    <div p-15>
      <el-input v-model="filed" class="select" placeholder="请输入"></el-input>
    </div>
    <template #footer>
      <div text-center>
        <el-button class="w-100px" @click="closeFiled">取消</el-button>
        <el-button class="w-100px" type="primary" @click="saveFiled">保存</el-button>
      </div>
    </template>
  </el-dialog>

  <el-dialog title="导入须知" v-model="promptVisible" width="600" @close="promptVisible = false">
    <div class="p-5px">
      <div>1、导入文件格式为 Excel文件格式，支持xlsx和xls，不允许合并单元格、不允许公式、不允许空行。</div>
      <div>2、导入文件表格第一行为‘表头字段’，从第二行开始为‘患者信息内容字段’，每次最多可导入500条‘患者信息‘数据。</div>
      <div>3、表头字段请参照医客通软件中‘配置中心-模板档案’功能下，各模板中配置启用的字段，命名与’展示字段名称‘严格保持一致，否则会导致导入数据的丢失或导入失败。</div>
      <div>4、为了保证客户唯一性，【身份证号】和【客户档案编号】必须至少有一个非空，模板中约定的必填字段也需要非空，否则相关患者数据会导入失败。</div>
      <div>5、更新客户档案情况下字段内容更新规则：</div>
      <div>5.1、基础信息、内部信息、健康指标内容部分，导入字段内容非空并校验通过的，则进行更新；否则不更新。</div>
      <div>5.2、门诊病历概要、住院病历概要部分，为客户创建一份门诊、住院病历概要记录信息。</div>
    </div>
  </el-dialog>
</template>
<script setup>
import { ElLoading, ElMessageBox, ElMessage } from "element-plus";
import { ref, watch, computed } from "vue";
import { getImportTable, tags } from "../../api/importCustomer.js";
import { batchImportCustomer, addMedicalRecord } from "@/api/member";
import { saveAs } from "file-saver";
import { utils, write } from "xlsx";
import { memberStore } from "@/store/member";
import { getCustomerSources, getIntenteds } from "../../../corpInfo";
import dayjs from "dayjs";
import { getCustomerType } from "@/api/member";
const { corpInfo } = memberStore();
const myTableData = ref([]);
const props = defineProps({
  visible: { type: Boolean, default: false },
  width: { type: [String, Number], default: 200 },
  tableData: { type: Array, default: () => [] },
  importType: { type: String, default: "" },
  importBoxType: { type: String, default: "" },
  importReuslt: { type: Object, default: () => {} },
});
let archivesImportSuccessCount = 0;
let recordImportSuccessCount = 0;
let updateImportSuccessCount = 0;
// let customerSources = getCustomerSources();
let intenteds = getIntenteds();
let stageList = ref([]);
getStage();
const columns = ref([]);
const emits = defineEmits(["close", "confirm"]);
const tabBarList = ref([]);
const pList = ref([]);
let errCustomerList = [];
let baseFeiledList = [];
let inhospitalList = [];
let outpatientList = [];
let physicalExaminationList = [];
const filedvisible = ref(false);
const promptVisible = ref(false);
const filed = ref("");
let filedKey = "";
let changeFiledIndex = -1;
let ignoreDataList = ref([]);
let idCardTitle = "";
let customerNumberTitle = "";
function prompt() {
  promptVisible.value = true;
}
const duplicatesIdcard = computed(() => {
  return myTableData.value
    .filter((item, index, array) => {
      return array.findIndex((t) => t[idCardTitle] === item[idCardTitle]) !== index;
    })
    .map((item) => item[idCardTitle]);
});
const duplicatesCustomerNumber = computed(() => {
  return myTableData.value
    .filter((item, index, array) => {
      return array.findIndex((t) => t[customerNumberTitle] === item[customerNumberTitle]) !== index;
    })
    .map((item) => item[customerNumberTitle]);
});

watch(
  () => props.visible,
  async (val) => {
    if (val) {
      myTableData.value = props.tableData;
      archivesImportSuccessCount = 0;
      recordImportSuccessCount = 0;
      updateImportSuccessCount = 0;
      errCustomerList = [];
      const { corpTemplateList, list, baseFeiled, inhospital, outpatient, physicalExamination } = await getImportTable(props.importType);
      baseFeiledList = baseFeiled;
      inhospitalList = inhospital;
      outpatientList = outpatient;
      physicalExaminationList = physicalExamination;
      tabBarList.value = list;
      pList.value = corpTemplateList;
      ignoreDataList.value = [];
      columns.value = generateColumns();
      getCardIdAndCustomerNumberName(corpTemplateList);
    }
  }
);

// 根据数据格式, 进行单元格合并

function getCardIdAndCustomerNumberName(list) {
  const idCard = list.find((i) => i.title === "idCard");
  const customerNumber = list.find((i) => i.title === "customerNumber");
  idCard && (idCardTitle = idCard.name || idCard.systemFieldName);
  customerNumber && (customerNumberTitle = customerNumber.name || customerNumber.systemFieldName);
}

async function getStage() {
  const { data } = await getCustomerType();
  const list = data.data && Array.isArray(data.data) ? data.data : [];
  stageList.value = list;
}
function editFile(key, value, index) {
  filed.value = value;
  filedvisible.value = true;
  filedKey = key;
  changeFiledIndex = index;
}
function closeFiled() {
  filedvisible.value = false;
  filed.value = "";
  changeFiledIndex = -1;
}
function saveFiled() {
  if (!filed.value) {
    ElMessage.info("请输入修改内容");
    return;
  }
  if (changeFiledIndex === -1) {
    return;
  }
  myTableData.value[changeFiledIndex][filedKey] = filed.value;
  if (filedKey === idCardTitle && filed.value && /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(filed.value)) {
    let { birthday } = getBirthdayAndGender(filed.value);
    myTableData.value[changeFiledIndex]["出生日期"] = birthday;
    // 年纪
    myTableData.value[changeFiledIndex]["年龄"] = new Date().getFullYear() - parseInt(filed.value.substring(6, 10));
    // 性别
    myTableData.value[changeFiledIndex]["性别"] = parseInt(filed.value.substring(16, 17)) % 2 === 0 ? "女" : "男";
  }
  filedvisible.value = false;
  filed.value = "";
}
function remove(row, index) {
  ElMessageBox.confirm("是否删除该条数据").then(() => {
    myTableData.value.splice(index, 1);
  });
}
function ignore(row, index) {
  if (ignoreDataList.value.includes(index)) {
    ignoreDataList.value = ignoreDataList.value.filter((i) => i !== index);
  } else {
    ignoreDataList.value.push(index);
  }
}
const generateColumns = () => {
  return tabBarList.value.map((item) => {
    return {
      key: item,
      dataKey: item,
      title: item,
      width: 150,
    };
  });
};
const close = (type) => {
  emits("close", type, archivesImportSuccessCount, recordImportSuccessCount, updateImportSuccessCount);
};
function styleClass(key, value, row) {
  let className = "";
  if (!mustJudgment(key, value, row)) {
    className = "text-red-500";
  }
  if (!warningJudgment(key, value, row)) {
    className = "text-yellow-300";
  }
  return className;
}
function rowMustJudgment(row) {
  let flag = true;
  let idCardValue = row[idCardTitle];
  let customerIdValue = row[customerNumberTitle];
  if (!customerIdValue && !idCardValue) {
    return false;
  }
  for (let key in row) {
    flag = mustJudgment(key, row[key], row);
    if (!flag) break;
  }
  return flag;
}
function mustJudgment(key, value, row) {
  let item = pList.value.find((i) => i.name === key);
  if (!item) return true;
  let { title = "", isMustEnable = false } = item;
  let idCardValue = row[idCardTitle];
  let customerIdValue = row[customerNumberTitle];
  if (!key || typeof key !== "string") return true;
  // 判断key 是否为字符串类型
  if (isMustEnable) {
    return value;
  } else if (title === "idCard") {
    if (!customerIdValue && !idCardValue) {
      return false;
    }
    return value && /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(value) && !duplicatesIdcard.value.includes(value);
  } else if (title === "customerNumber") {
    if (!customerIdValue && !idCardValue) {
      return false;
    }
    return value && !duplicatesCustomerNumber.value.includes(value);
  } else {
    return true;
  }
}

function warningJudgment(key, value, row) {
  let title = pList.value.find((i) => i.name === key) && pList.value.find((i) => i.name === key).title;
  if (!key || typeof key !== "string") return true;
  if (!value) return true;
  if (key.includes("客户标签")) {
    title = "tagIds";
  }
  // if (key.includes("来源")) {
  //   title = "customerSource";
  // }
  switch (title) {
    case "mobile":
    case "phone1":
    case "phone2":
    case "phone3":
      return value ? /^1[3456789]\d{9}$/.test(value) : true;
    case "sex":
      return ["男", "女", "male", "female"].includes(value);
    case "tagIds":
      const groupName = key.match(/\((.+)\)/)[1];
      const item = tags.value.find((i) => i.groupName === groupName);
      const tag = item && item.tag;
      const tagNames = tag && tag.map((i) => i.name);
      // 把value 用、分割成数组
      if (!value) return false;
      const values = typeof value === "string" ? value.split("、") : [value];
      return values.some((i) => tagNames.includes(i));
    case "customerStage":
      return stageList.value.find((i) => i.name === value);
    // case "customerSource":
    //   let oneLeave = row["来源一级"];
    //   let twoLeave = row["来源二级"];
    //   let treeLeave = row["来源三级"];
    //   if (!value) return true;
    //   if (key === "来源一级") {
    //     return customerSources.some((i) => i.value === oneLeave);
    //   } else if (key === "来源二级") {
    //     let oneSource = customerSources.find((i) => i.value === oneLeave);
    //     let twoSources = oneSource && oneSource.children;
    //     return twoSources && twoSources.some((i) => i.value === twoLeave);
    //   } else if (key === "来源三级") {
    //     let oneSource = customerSources.find((i) => i.value === oneLeave);
    //     let twoSources = oneSource && oneSource.children;
    //     let treeSources = twoSources && twoSources.find((i) => i.value === twoLeave);
    //     return treeSources && treeSources.children && treeSources.children.some((i) => i.value === treeLeave);
    //   }
    case "intentedProject":
      return intenteds.some((i) => i.value === value);
    case "operationDate":
    case "visitTime":
    case "birthday":
    case "outhosDate":
    case "inhosDate":
      return dayjs(value).isValid();
    default:
      return true;
  }
}
let loadingInstance = null;
const confirm = async () => {
  // 获取到tabBarList.value 中字段含必填字段的数组
  let requiredList = pList.value.filter((item) => item.isMustEnable || item.required).map((item) => item.name);
  requiredList = [...requiredList, customerNumberTitle, idCardTitle];
  let isRequiredObj = {};
  requiredList.forEach((item) => {
    isRequiredObj[item] = myTableData.value.map((i) => i[item]).filter((j) => j);
  });
  // 判断 isRequiredObj 中的值是否为空
  let isRequired = true;
  Object.keys(isRequiredObj).forEach((item) => {
    if (item.includes("必填") && !isRequiredObj[item].length) {
      isRequired = false;
      return;
    }
  });
  if (!isRequired) {
    ElMessageBox.alert("必填字段不能为空");
    return;
  }
  if (!isRequiredObj[customerNumberTitle].length && !isRequiredObj[idCardTitle].length) {
    ElMessageBox.alert("【证件号】和【客户档案编号】需保证必填一个字段以校验客户唯一性");
    return;
  }
  const handler = async (item, index) => {
    return await addCustomer(item, index);
  };
  loadingInstance = ElLoading.service({ fullscreen: true, text: "数据导入中,请稍后..." });
  await processInBatches(myTableData.value, handler, 10);
  loadingInstance.close();
  if (errCustomerList.length) {
    myTableData.value = errCustomerList;
  }
  emits("confirm", errCustomerList, archivesImportSuccessCount, recordImportSuccessCount, updateImportSuccessCount);
  errCustomerList = [];
  ignoreDataList.value = [];
};
function getQuery(obj, index) {
  let params = {
      corpId: localStorage.getItem("corpId"),
    },
    flag = true;
  let keys = Object.keys(obj);
  // 判断keys 同时不包含 客户档案编号 和 证件号
  if (!keys.includes(customerNumberTitle) && !keys.includes(idCardTitle)) {
    errCustomerList.push(obj);
    return false;
  }
  // 判断keys 同时不包含 必填字段
  if (
    columns.value
      .filter((i) => i.key.includes("必填"))
      .map((i) => i.key)
      .some((i) => !keys.includes(i))
  ) {
    errCustomerList.push(obj);
    return false;
  }
  for (let key in obj) {
    let title = pList.value.find((i) => i.name === key) && pList.value.find((i) => i.name === key).title;
    if (!typeJudgment(title, obj[key], key)) {
      if (!ignoreDataList.value.includes(index)) {
        if (flag) {
          errCustomerList.push(obj);
        }
        flag = false;
      } else {
        params[title] = "";
      }
      continue;
    }
    if (!key || typeof key !== "string" || !obj[key]) continue;
    if (key === "收缩压") {
      params["systolicPressure"] = obj[key];
      continue;
    }
    if (key === "舒张压") {
      params["diastolicPressure"] = obj[key];
      continue;
    }
    if (key === "测量日期") {
      params["bloodPressureDate"] = obj[key];
      continue;
    }
    if (key === "阳性发现") {
      !params["positiveFind"] && (params["positiveFind"] = {});
      params["positiveFind"]["category"] = obj[key];
      continue;
    }
    if (key === "阳性发现处理意见") {
      !params["positiveFind"] && (params["positiveFind"] = {});
      params["positiveFind"]["opinion"] = obj[key];
      continue;
    }
    // if (key === "") {
    //   params["customerNumber"] = obj[key];
    //   continue;
    // }
    // if (key === "来源一级") {
    //   let sourceName = customerSources && customerSources.find((i) => i.sourceName === obj[key]) && customerSources.find((i) => i.sourceName === obj[key]).sourceName;
    //   params["customerSource"] = sourceName && [sourceName];
    //   continue;
    // }
    // if (key === "来源二级" && params["customerSource"] && Array.isArray(params["customerSource"]) && params["customerSource"].length) {
    //   const oneLeave = params["customerSource"][0];
    //   let twoSources = customerSources.find((i) => i.value === oneLeave) && customerSources.find((i) => i.value === oneLeave).children;
    //   let sourceName = twoSources && twoSources.find((i) => i.sourceName === obj[key]) && twoSources.find((i) => i.sourceName === obj[key]).sourceName;
    //   sourceName && (params["customerSource"][1] = sourceName);
    //   continue;
    // }
    // if (key === "来源三级" && params["customerSource"] && Array.isArray(params["customerSource"]) && params["customerSource"].length) {
    //   const oneLeave = params["customerSource"][0];
    //   const twoLeave = params["customerSource"][1];
    //   let twoSources = customerSources && customerSources.find((i) => i.value === oneLeave) && customerSources.find((i) => i.value === oneLeave).children;
    //   let treeSources = twoSources && twoSources.find((i) => i.value === twoLeave) && twoSources.find((i) => i.value === twoLeave).children;
    //   let sourceName = treeSources && treeSources.find((i) => i.sourceName === obj[key]) && treeSources.find((i) => i.sourceName === obj[key]).sourceName;
    //   sourceName && (params["customerSource"][2] = sourceName);
    //   continue;
    // }
    if (key.includes("客户标签")) {
      let groupName = key.match(/\((.+)\)/) ? key.match(/\((.+)\)/)[1] : "";
      if (!groupName) continue;
      let item = tags.value.find((i) => i.groupName === groupName);
      // 获取系统的标签
      let tagList = item && item.tag;
      if (!tagList) continue;
      // 获取到客户标签的id, 并用、组成数组
      if (!obj[key]) continue;

      const values = typeof obj[key] === "string" ? obj[key].split("、") : [obj[key]];
      if (!values.length) continue;
      for (let i of values) {
        let item = tagList.find((e) => e.name === i);
        if (item) {
          if (!params["tagIds"]) params["tagIds"] = [];
          params["tagIds"].push(item.id);
          //  去重
          params["tagIds"] = Array.from(new Set(params["tagIds"]));
        }
      }
      continue;
    }
    // 时间转换为时间戳
    if (["operationDate", "visitTime", "birthday", "outhosDate", "inhosDate"].includes(title)) {
      if (!dayjs(obj[key]).isValid()) continue;
      const regex = /^\d{4}年\d{1,2}月\d{1,2}日$/;
      if (regex.test(obj[key])) {
        obj[key] = obj[key].replace(/年|月/g, "-").replace(/日/g, "");
      }
      // 判断是否为时间格式
      if (title !== "birthday") {
        params[title] = dayjs(obj[key]).valueOf();
      } else {
        params[title] = obj[key];
      }
      continue;
    }
    if (title === "customerStage" && obj[key]) {
      const stage = stageList.value.find((i) => i.name === obj[key]);
      params[title] = stage ? stage.type : "";
      continue;
    }
    if (title === "sex" && obj[key]) {
      params[title] = obj[key] === "male" ? "男" : obj[key] === "female" ? "女" : obj[key];
      continue;
    }
    if (title && warningJudgment(key, obj[key], obj)) params[title] = obj[key];
  }
  // 身份证号转换为生日和性别
  if (params["idCard"] && /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(params["idCard"])) {
    let { birthday, gender } = getBirthdayAndGender(params["idCard"]);
    params["birthday"] = birthday;
    params["sex"] = gender;
    // 通过身份证号获取年龄
    params["age"] = new Date().getFullYear() - parseInt(params["idCard"].substring(6, 10));
    params["cardType"] = "身份证";
  }
  return flag ? params : false;
}
function getBirthdayAndGender(idCard) {
  let birthday = `${idCard.substring(6, 10)}-${idCard.substring(10, 12)}-${idCard.substring(12, 14)}`;
  let gender = parseInt(idCard.substring(16, 17)) % 2 === 0 ? "女" : "男";
  return { birthday, gender };
}
// 类型判断
function typeJudgment(type, value) {
  // 1.判断是否为必填字段
  if (type === "customerStage" && value) {
    return stageList.value.find((i) => i.name === value);
  } else if (["phone1", "phone2", "phone3", "mobile"].includes(type) && value) {
    return /^1[3456789]\d{9}$/.test(value);
  } else if (type === "idCard" && value) {
    return /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(value) && !duplicatesIdcard.value.includes(value);
  } else if (["operationDate", "visitTime", "birthday", "outhosDate", "inhosDate"].includes(type) && value) {
    return dayjs(value).isValid();
  } else if (type === "sex" && value) {
    return ["男", "女", "male", "female"].includes(value);
  } else if (type === "customerNumber" && value) {
    return !duplicatesCustomerNumber.value.includes(value);
  } else {
    return true;
  }
}
function getFiled(obj, list = []) {
  let item = {};
  list = list.map((i) => i.split("-")[0]);
  for (let key in obj) {
    if (list.includes(key)) {
      item[key] = obj[key];
    }
  }
  return item;
}

async function addCustomer(item, index) {
  let obj = getQuery(item, index);
  if (!obj) return;
  let baseParams = getFiled(obj, baseFeiledList);
  baseParams["corpId"] = localStorage.getItem("corpId");
  baseParams["creator"] = localStorage.getItem("userId");
  baseParams["relationship"] = "本人";

  const { success, data } = await batchImportCustomer(baseParams);
  if (!success) {
    errCustomerList.push(obj);
  } else {
    if (data.operationType === "add") {
      archivesImportSuccessCount++;
    }
    if (data.operationType === "update") {
      updateImportSuccessCount++;
    }
    if (props.importType) {
      let list = [];
      if (props.importType === "outpatient") {
        list = outpatientList;
      } else if (props.importType === "inhospital") {
        list = inhospitalList;
      } else if (props.importType === "physicalExaminationTemplate") {
        list = physicalExaminationList;
      }
      let recordParams = getFiled(obj, list);
      let sortTime = "";
      if (props.importType === "outpatient") {
        sortTime = obj["visitTime"];
      } else if (props.importType === "inhospital") {
        sortTime = obj["inhosDate"];
      } else if (props.importType === "physicalExaminationTemplate") {
        sortTime = obj["inspectTime"];
        if (obj["positiveFind"]) {
          let { category, opinion } = obj["positiveFind"];
          let positiveFind = category.map((i, index) => {
            return {
              category: i,
              opinion: opinion[index],
            };
          });
          recordParams["positiveFind"] = positiveFind;
        }
      }
      recordParams["sortTime"] = sortTime ? dayjs(sortTime).valueOf() : "";
      recordParams["memberId"] = data.data;
      recordParams["userId"] = localStorage.getItem("userId");
      recordParams["corpId"] = localStorage.getItem("corpId");
      recordParams["corp"] = "本院";
      recordParams["medicalType"] = props.importType;
      let { success } = await addMedicalRecord(recordParams);
      if (!success) {
        errCustomerList.push(obj);
      } else {
        recordImportSuccessCount++;
      }
    }
  }
}
async function processInBatches(arr, handler, batchSize = 5) {
  const result = [];
  for (let i = 0; i < arr.length; i += batchSize) {
    const batch = arr.slice(i, i + batchSize);
    const batchResult = await Promise.all(batch.map((item, index) => handler(item, index)));
    result.push(...batchResult);
  }
  return result;
}
// 导出表格
async function exportExcel() {
  // 创建一个工作簿
  const workbook = utils.book_new();
  let sortedTableData = myTableData.value.map((row) => {
    let sortedRow = {};
    tabBarList.value.forEach((key) => {
      sortedRow[key] = row[key] || "";
    });
    return sortedRow;
  });
  const list = sortedTableData.map(Object.values);

  const worksheetData = [tabBarList.value, ...list];
  const worksheet = utils.aoa_to_sheet(worksheetData);
  // 将工作表添加到工作簿
  utils.book_append_sheet(workbook, worksheet, "Sheet1");
  // 将工作簿转换为 ArrayBuffer 对象
  const workbookBinary = write(workbook, { bookType: "xlsx", type: "binary" });
  const workbookBuffer = new ArrayBuffer(workbookBinary.length);
  const workbookView = new Uint8Array(workbookBuffer);
  for (let i = 0; i < workbookBinary.length; i++) {
    workbookView[i] = workbookBinary.charCodeAt(i) & 0xff;
  }
  // 创建 Blob 对象
  const workbookBlob = new Blob([workbookBuffer], { type: "application/octet-stream" });
  // 使用 file-saver 库保存 Blob 对象为 Excel 文件
  saveAs(workbookBlob, "客户档案.xlsx");
}
</script>
 <style lang="scss" scoped>
.my-alert .el-message-box__wrapper {
  width: 500px; /* 设置你需要的宽度 */
}

.importClass {
  flex-direction: column;
  align-items: center;
}
::-webkit-scrollbar {
  width: 6px;
  background-color: transparent;
}

::-webkit-scrollbar-track {
  border-radius: 4px;
  // background-color: white;
}

::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: #909399;
}

.table {
  display: flex;
  flex-direction: column;
  border: 1px solid rgb(235, 238, 245);
  background-color: #fff;
  overflow: auto;
  height: 100%;
  /* 添加外边框 */
}

.row {
  display: flex;
  border-bottom: 1px solid rgb(235, 238, 245); /* 添加行的底部边框 */
}

.cell {
  flex: 1;
  border-right: 1px solid rgb(235, 238, 245); /* 添加单元格的右边框 */
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 0;
  min-width: 160px;
  border-bottom: 1px solid rgb(235, 238, 245); /* 添加行的底部边框 */
}

.child-cell {
  height: 80px;
  width: 100%;
  border-bottom: 1px solid rgb(235, 238, 245);
  text-align: center;
  padding: 10px 0;
  overflow-y: auto;
}

.child-cell:last-child {
  border-bottom: 0;
}

.header .cell {
  background-color: rgb(236, 244, 255);
  color: #000;
}

.operate-cell {
  position: sticky;
  right: 0;
  background-color: #fff;
  box-shadow: -5px 0 5px -2px rgba(0, 0, 0, 0.2); /* 添加右侧阴影 */
}

// .row:last-child .cell {
//   border-bottom: 0; /* 移除最后一行的底部边框 */
// }

// .cell:last-child {
//   border-right: 0; /* 移除每行最后一个单元格的右边框 */
// }
</style>
    