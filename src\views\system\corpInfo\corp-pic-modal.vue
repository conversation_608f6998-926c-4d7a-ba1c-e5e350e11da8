<template>
  <el-dialog :model-value="visible" title="修改机构图片" :width="width" @close="close()">
    <div class="px-15px py-12px">
      <el-upload action="#" accept="image/*" :show-file-list="false" list-type="picture" :auto-upload="false" @change="changeFile">
        <template #trigger>
          <div :class="coverFile && coverFile.base64 ? '' : 'border-1 border-dashed border-blue-500'" class="flex items-center justify-center w-400px h-200px text-blue-500 rounded mx-auto">
            <img v-if="coverFile && coverFile.base64" :src="coverFile.base64" class="w-400px h-200px" />
            <el-icon v-else>
              <CameraFilled />
            </el-icon>
          </div>
        </template>
      </el-upload>
    </div>
    <template #footer>
      <div flex items-center justify-center>
        <el-button class="w-100px" @click="close">取消</el-button>
        <el-button class="w-100px" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref, watch } from "vue";
import { ElMessage, ElLoading } from "element-plus";
import { updateFile } from "@/api/uploadFIle.js";
import { imageToBase64 } from "@/utils";
import { updateCorpInfo } from "@/api/corp.js";
import { memberStore } from "@/store/member";

const emits = defineEmits(["close", "confirm", "remove"]);
const props = defineProps({
  handle: { type: Function, default: () => {} },
  src: { type: String, default: "" },
  title: { type: String, default: "" },
  visible: { type: Boolean, default: false },
  width: { type: Number },
});

const store = memberStore();
const { changeCorpInfo } = store;
function close() {
  emits("close");
}

const coverFile = ref(null);
const loading = ref(false);
async function confirm() {
  if (coverFile.value && coverFile.value.file) {
    loading.value = true;
    const url = await upload();
    if (url) {
      const { success, message } = await updateCorpInfo({ corpPic: url });
      if (success) {
        changeCorpInfo({ key: "corpPic", value: url });
        emits("close");
      } else {
        ElMessage.error(message);
      }
    }
    loading.value = false;
  } else {
    ElMessage.info("请选择图片");
  }
}

async function changeFile(uploadFile) {
  const { raw: file } = uploadFile; // blobUrl仅仅本地预览
  const { size, name, type } = file;
  const newFile = { file, size, name, type };
  if (/^image\//.test(type)) {
    const base64String = await imageToBase64(file);
    const base64WithPrefix = `data:${file.type};base64,${base64String}`;
    coverFile.value = { file: newFile, base64: base64WithPrefix };
  }
}

async function upload() {
  const loadingInstance = ElLoading.service({ fullscreen: true, text: "正在上传" });
  try {
    const file = coverFile.value.file;
    const res = await updateFile(`${+new Date()}_${Math.ceil(Math.random() * 100000)}_${file.name}`, file.file, localStorage.getItem("corpId"));
    if (res) {
      loadingInstance.close();
      return res.download_url;
    } else {
      loadingInstance.close();
      ElMessage.error(`图片上传失败`);
      return false;
    }
  } catch (e) {
    loadingInstance.close();
    ElMessage.error(`图片上传失败`);
    return false;
  }
}

watch(
  () => props.visible,
  (n) => {
    if (n) {
      coverFile.value = null;
    }
  }
);
</script>
<style lang="scss" scoped>
:deep(.el-upload--picture) {
  width: 100%;
  margin: 0 auto;
}
</style>