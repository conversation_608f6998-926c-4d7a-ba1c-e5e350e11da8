<template>
  <!-- <div @click="doBind">引入</div> -->
  <span v-if="isShowName">{{ anotherName }}</span>
  <ww-open-data v-else ref="openDataRef" :type="type" :openid="openid"></ww-open-data>
</template>
<script setup>
// import { jsSDKConfig } from "@/utils/jssdk";
import { computed, onMounted, ref } from "vue";
import { storeToRefs } from "pinia";
import { staffStore } from "@/store/staff";
const props = defineProps({
  openid: {
    type: String,
    default: "",
  },
  type: {
    type: String,
    default: "userName",
  },
});
// const isShowName = computed(() => import.meta.env.DEV || (import.meta.env.VITE_NEED_LOGIN === "true" && !envjudgwxWork()));
const isShowName = true;
const { staffList } = storeToRefs(staffStore());
const openDataRef = ref(null);
const anotherName = computed(() => {
  const staff = staffList.value.find((i) => i.userid == props.openid);
  return staff && staff.anotherName ? staff.anotherName : props.openid;
});
onMounted(async () => {
  if (staffList.value.length === 0) {
    await staffStore().getStaffList();
  }
});
function envjudgwxWork() {
  var ua = navigator.userAgent.toLowerCase();
  return ua.match(/wxwork/i) == "wxwork";
}
</script>




