<template>
  <el-drawer v-loading="loading" :close-on-click-modal="false" :model-value="visible" title="新增回访计划" size="65%" style="--el-drawer-padding-primary: 0" @close="close()">
    <my-layout>
      <layout-main class="text-14px">
        <div class="flex items-center flex-wrap px-15px pb-10px border-b borde-gray-200">
          <div class="w-1/2 pt-10px flex-shrink-0">
            <span class="text-gray-500 mr-10px">计划名称:</span>
            <span>{{ plan.planName || "--" }}</span>
          </div>
          <div class="w-1/2 pt-10px flex-shrink-0">
            <span class="text-gray-500 mr-10px">开始日期:</span>
            <el-date-picker v-model="startDate" class="w-200px" format="YYYY-MM-DD" placeholder="请选择执行日期" type="date" value-format="YYYY-MM-DD" :disabled-date="judgeDisabled" />
          </div>
        </div>
        <div class="flex items-center flex-wrap px-15px pb-10px border-b borde-gray-200">
          <div class="w-1/2 pt-10px flex-shrink-0 flex items-center">
            <div class="flex-shrink-0 text-gray-500 mr-10px">执行人:</div>
            <div class="flex-grow">
              <div v-if="type === 'addBatchTask'">所属开发</div>
              <el-select v-else v-model="selectedExecutor" ref="executorSelectRef" popper-class="new-plan-drawer-executor-select-popover" placeholder="请选择处理人" class="w-full max-w-300px new-plan-drawer-executor-select" clearable @change="changeExecutor" @visible-change="visibleChange">
                <template #prefix>
                  <div class="truncate h-30px leading-30px text-left" :style="{ width: executorPrefixWidth, color: 'rgb(96, 98, 102)' }">
                    <ww-user v-if="executorUserId" :openid="executorUserId" />
                    <span class="ml-10px">{{ executorTeamName }}</span>
                  </div>
                </template>
                <el-option-group v-for="group in filterExecutorOptions" :key="group.teamId" :label="group.name">
                  <el-option v-for="item in group.list" :key="item.value" :value="item.value">
                    <div class="truncate">
                      <ww-user :openid="item.userId"></ww-user>
                    </div>
                  </el-option>
                </el-option-group>
              </el-select>
            </div>
          </div>
        </div>
        <div class="px-15px pt-10px">
          <!-- <el-button text type="primary" class="mb-10px" :icon="Plus" @click="editTask()">新增任务项</el-button> -->
          <el-timeline v-if="taskList.length" class="ml-5px">
            <el-timeline-item v-for="(item, index) in taskList" :key="index" color="#006eff" :timestamp="item.key" hollow placement="top" :hide-timestamp="true">
              <div class="text-15px text-dark-500 font-semibold">
                处理日期: {{ item.planExecutionTime }} 处理人:
                <ww-user v-if="item.executorUserId" :openid="item.executorUserId" />
              </div>
              <div :key="item.taskId" class="mt-15px">
                <div class="task_cell pt-10px">
                  <div v-if="item.eventType && ToDoEventType[item.eventType]" border-bottom class="px-15px mb-10px">
                    <div grew-color>类型:</div>
                    <el-scrollbar wrap-style="max-height:80px">
                      <div class="py-10px">{{ ToDoEventType[item.eventType] }}</div>
                    </el-scrollbar>
                  </div>
                  <div border-bottom class="px-15px">
                    <div grew-color>任务内容:</div>
                    <el-scrollbar wrap-style="max-height:80px">
                      <div class="py-10px">{{ item.taskContent }}</div>
                    </el-scrollbar>
                  </div>
                  <div border-bottom class="px-15px pb-15px">
                    <div grew-color class="py-10px">向联系人发送:</div>
                    <el-scrollbar v-if="item.sendContent" wrap-style="max-height:80px">
                      <div class="pb-10px text-14px leading-21px">
                        <span grew-color>[提醒]</span>
                        {{ item.sendContent }}
                      </div>
                    </el-scrollbar>
                    <div v-if="!item.sendContent && (!item.pannedEventSendFile || !item.pannedEventSendFile.name)" class="pb-10px">暂无发送内容</div>
                    <div class="pb-10px" v-if="Object.keys(item.pannedEventSendFile || {}).length !== 0 && item.pannedEventSendFile.name">
                      <span grew-color>[{{ item.pannedEventSendFile.type === "article" ? "文章" : "问卷" }}]</span>
                      <span main-color class="pointer">
                        {{ item.pannedEventSendFile.name }}
                      </span>
                    </div>
                  </div>
                  <!-- <div class="text-right py-5px px-10px">
                    <el-button text type="primary" @click="editTask(item)" :icon="Edit" class="px-5px">编辑</el-button>
                    <el-button text type="danger" @click="deleteTask(item, index)" class="px-5px"
                      :icon="Delete">删除</el-button>
                  </div> -->
                </div>
              </div>
            </el-timeline-item>
            <!-- v-if="taskShowList.length > 0" -->
            <el-timeline-item v-if="taskList.length" placement="top" color="#006eff" hollow timestamp="结束"></el-timeline-item>
          </el-timeline>
          <empty-data v-else :top="120" title="暂无任务" padding="0 0 15px" text-top="10px" :image-width="160" />
        </div>
      </layout-main>
      <layout-item>
        <div common-shadow--r class="text-center py-12px">
          <el-button plain class="w-100px" @click="close">取消</el-button>
          <el-button type="primary" class="w-100px" :loading="loading" @click="confirm()">确定</el-button>
        </div>
      </layout-item>
    </my-layout>
  </el-drawer>
  <teleport v-if="showExecutorSearchInput" to=".new-plan-drawer-executor-select-popover .teleport-container">
    <div class="px-20px pt-20px pb-5px">
      <el-input clearable v-model="executorKeyword" placeholder="搜索..." />
    </div>
  </teleport>
</template>
<script setup>
import { computed, ref, watch, nextTick } from "vue";
import { useElementBounding } from "@vueuse/core";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import { ElMessage, ElMessageBox } from "element-plus";
import { executeManagementPlanTodo } from "@/api/todo";
import { pushCustomerTeamId } from "@/api/member";
import { ToDoEventType } from "@/baseData";
import { memberStore } from "@/store/member";
import { teamStore } from "@/store/team";
import { staffStore } from "@/store/staff";
import { getRandomStr } from "@/utils";
import { teamStore as useTeamStore } from "@/store/team";
import EmptyData from "@/components/empty-data.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import WwUserSelect from "@/components/ww-user-select";
import WwUser from "@/components/ww-user";

const emits = defineEmits(["close", "change"]);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  plan: {
    type: Object,
    default: () => ({}),
  },
  customer: {
    type: Object,
    default: () => ({}),
  },
  teamId: {
    type: String,
    default: "",
  },
  memberList: {
    type: Array,
    default: () => [],
  },
  type: {
    type: String,
    default: "",
  },
  selectCustomers: {
    type: Array,
    default: () => [],
  },
});

const loading = ref(false);
const startDate = ref();
const taskList = ref([]);
const executorUserId = ref("");
const { memberInfo } = storeToRefs(memberStore());
const { allTeams } = storeToRefs(teamStore());
const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());
const team = computed(() => {
  return allTeams.value.find((i) => i.teamId === props.teamId);
});

// 处理人选择相关
const selectedExecutor = ref("");
const executorSelectRef = ref(null);
const { width: executorSelectWidth } = useElementBounding(executorSelectRef);
const executorPrefixWidth = computed(() => `${executorSelectWidth.value - 40}px`);
const showExecutorSearchInput = ref(false);
const executorKeyword = ref("");

const staffMap = computed(() =>
  staffList.value.reduce((acc, cur) => {
    acc[cur.userid] = cur;
    return acc;
  }, {})
);

const executorOptions = computed(() => {
  const data = allTeams.value.reduce((acc, cur) => {
    const memberList = Array.isArray(cur.memberList) ? cur.memberList : [];
    const group = { teamId: cur.teamId, name: cur.name, list: [] };
    group.list = memberList
      .map((i) => ({
        value: `${cur.teamId}||${i}`,
        userId: i,
        teamId: cur.teamId,
        name: cur.name,
        staff: staffMap.value[i],
      }))
      .filter((i) => i.staff); // 移除角色过滤，允许所有员工
    if (group.list.length) return [...acc, group];
    return acc;
  }, []);
  return data;
});

const filterExecutorOptions = computed(() => {
  const name = executorKeyword.value.trim();
  if (name === "") return executorOptions.value;
  return executorOptions.value
    .map((i) => {
      const { list, ...item } = i;
      return { ...item, list: list.filter((j) => j.staff && typeof j.staff.anotherName === "string" && j.staff.anotherName.includes(name)) };
    })
    .filter((i) => i.list.length);
});

const executorTeamName = computed(() => {
  const team = allTeams.value.find((i) => i.teamId === executorTeamId.value && i.teamId);
  return team ? team.name : "";
});

const executorTeamId = ref("");
function close() {
  emits("close");
}

async function confirm() {
  if (!startDate.value || !dayjs(startDate.value).isValid()) {
    ElMessage.warning("请选择开始日期");
    return;
  }
  if (taskList.value.length === 0) {
    ElMessage.warning("请添加任务");
    return;
  }
  if (!team.value) {
    ElMessage.warning("获取团队信息失败, 请稍后再试");
    return;
  }

  if (hasTemplateGroupTask()) {
    return ElMessage.warning("该档案还未绑定微信联系人，无法执行群发任务!");
  }

  // 根据类型判断是单个添加还是批量添加
  if (props.type === "addBatchTask") {
    addBatchPlans();
  } else {
    if (!executorUserId.value) {
      ElMessage.warning("请选择执行人");
      return;
    }
    addPlan();
  }
}

// 单个添加计划
async function addPlan() {
  loading.value = true;
  const params = {
    corpId: memberInfo.value.corpId,
    customerId: props.customer._id,
    customerName: props.customer.name,
    customerUserId: props.customer.externalUserId || "",
    executeTeamId: team.value.teamId,
    executeTeamName: team.value.name,
    planId: props.plan._id,
    planName: props.plan.planName,
    userId: memberInfo.value.userid,
    taskList: taskList.value,
  };

  const { success, message } = await executeManagementPlanTodo(params);
  if (success) {
    // 更新客户档案的团队授权
    await updateCustomerTeamAuth(executorTeamId.value || team.value.teamId);
    
    ElMessage.success(message || "操作成功");
    emits("change");
    close();
  } else {
    ElMessage.error(message);
  }
  loading.value = false;
}

// 批量添加计划
async function addBatchPlans() {
  if (loading.value) return;
  if (props.selectCustomers.length === 0) {
    return ElMessage.warning("请选择客户");
  }

  loading.value = true;
  let successCount = 0;
  let failCount = 0;
  for (const customer of props.selectCustomers) {
    // 跳过没有微信联系人的客户（对于包含群发任务的情况）
    if (taskList.value.some((item) => item.executeMethod === "groupTask") && !customer.externalUserId) {
      failCount++;
      continue;
    }

    const introducerRecord = customer.introducerRecord;
    const customerUserId = introducerRecord ? introducerRecord[introducerRecord.length - 1]?.introducer : "";
    const executeTeamId = introducerRecord ? introducerRecord[introducerRecord.length - 1]?.teamId : team.value.teamId;
    const team = allTeams.value.find((i) => i.teamId === executeTeamId);
    const executeTeamName = team ? team.name : "";
    const params = {
      corpId: memberInfo.value.corpId,
      customerId: customer._id,
      customerName: customer.name,
      customerUserId,
      executeTeamId,
      executeTeamName,
      planId: props.plan._id,
      planName: props.plan.planName,
      userId: memberInfo.value.userid,
      taskList: taskList.value,
    };

    try {
      const { success } = await executeManagementPlanTodo(params);
      if (success) {
        // 更新客户档案的团队授权
        await updateCustomerTeamAuth(executeTeamId);
        successCount++;
      } else {
        failCount++;
      }
    } catch (error) {
      failCount++;
    }
  }

  if (successCount > 0) {
    ElMessage.success(`成功添加 ${successCount} 个回访计划`);
    emits("change");
    close();
  }

  if (failCount > 0) {
    ElMessage.warning(`${failCount} 个回访计划添加失败`);
  }

  loading.value = false;
}

function hasTemplateGroupTask() {
  return taskList.value.some((item) => item.executeMethod === "groupTask") && !props.customer.externalUserId;
}

function editTask(item = { executeMethod: "todo" }) {
  emits("onEdit", item);
}

function deleteTask(item) {
  ElMessageBox.confirm("确定删除该任务吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      taskList.value = taskList.value.filter((task) => task.taskId !== item.taskId);
    })
    .catch(() => {
      // catch error
    });
}

function changeTask(task) {
  if (task.taskId) {
    const index = taskList.value.findIndex((item) => item.taskId === task.taskId);
    taskList.value.splice(index, 1, task);
  } else {
    task.taskId = getRandomStr();
    taskList.value.push(task);
  }
  taskList.value.sort((a, b) => {
    return dayjs(a.planExecutionTime).isBefore(dayjs(b.planExecutionTime)) ? -1 : 1;
  });
}

function judgeDisabled(date) {
  return dayjs(date).isBefore(dayjs().startOf("day"));
}

function setTaskList() {
  const list = taskList.value.map((item) => {
    const taskTime = typeof item.taskTime === "number" ? item.taskTime : "";
    const timeType = ["day", "week", "month", "year"].includes(item.timeType) ? item.timeType : "";
    if (taskTime !== "" && timeType !== "") {
      const planExecutionTime = startDate.value && dayjs(startDate.value).isValid() ? dayjs(startDate.value).add(taskTime, timeType).format("YYYY-MM-DD") : "";
      return { ...item, planExecutionTime };
    }
    return { ...item };
  });
  taskList.value = list.sort((a, b) => {
    return dayjs(a.planExecutionTime).isBefore(dayjs(b.planExecutionTime)) ? -1 : 1;
  });
}

function changeExecutor(val) {
  const [teamId = "", userId = ""] = val.split("||");
  executorUserId.value = userId;
  executorTeamId.value = teamId;
  taskList.value.forEach((i) => {
    i.executorUserId = userId;
  });
}

async function visibleChange(val) {
  if (!val) return;
  await nextTick();
  const popover = document.querySelector(".new-plan-drawer-executor-select-popover");
  const el = document.querySelector(".new-plan-drawer-executor-select-popover .teleport-container");
  if (popover && !el) {
    const el = document.createElement("div");
    el.classList.add("teleport-container");
    popover.prepend(el);
  }
  await nextTick();
  showExecutorSearchInput.value = val;
}

// 更新客户档案的团队授权
async function updateCustomerTeamAuth(executorTeamId) {
  if (!props.customer._id || !executorTeamId) return;
  
  try {
    // 直接调用 pushCustomerTeamId 接口授权团队
    await pushCustomerTeamId({ 
      customerId: props.customer._id, 
      teamId: executorTeamId 
    });
    console.log(`客户档案已授权给团队: ${executorTeamId}`);
  } catch (error) {
    console.error("更新客户档案团队授权失败:", error);
  }
}

function change(val) {
  executorUserId.value = val;
  taskList.value.forEach((i) => {
    i.executorUserId = val;
  });
}

watch(startDate, (n) => {
  setTaskList();
});

watch(
  () => props.visible,
  (n) => {
    if (n) {
      // 初始化处理人选择
      if (staffList.value.length === 0) getStaffList();
      executorUserId.value = props.memberList.includes(memberInfo.value.userid) ? memberInfo.value.userid : "";
      executorTeamId.value = team.value?.teamId || "";
      selectedExecutor.value = executorTeamId.value && executorUserId.value ? `${executorTeamId.value}||${executorUserId.value}` : "";
      executorKeyword.value = "";
      
      startDate.value = dayjs().format("YYYY-MM-DD");
      taskList.value = Array.isArray(props.plan.taskList)
        ? props.plan.taskList.map((item) => {
            const taskTime = typeof item.taskTime === "number" ? item.taskTime : "";
            const timeType = ["day", "week", "month", "year"].includes(item.timeType) ? item.timeType : "";
            const planExecutionTime = taskTime !== "" && timeType !== "" ? dayjs().add(taskTime, timeType).format("YYYY-MM-DD") : dayjs().format("YYYY-MM-DD");
            return { ...item, planExecutionTime, executorUserId: executorUserId.value };
          })
        : [];
    }
  }
);

defineExpose({
  changeTask,
});
</script>
<style lang="scss" scoped>
:deep(.new-plan-drawer-executor-select > .select-trigger > .el-input > .el-input__wrapper > .el-input__inner) {
  display: none;
}

:deep(.new-plan-drawer-executor-select > .select-trigger > .el-input > .el-input__wrapper > .el-input__prefix) {
  flex-grow: 1;
}
</style>
