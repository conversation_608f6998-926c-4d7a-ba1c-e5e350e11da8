<template>
  <div class="customer-bar">
    <!-- v-if="member.sex === '女'" -->
    <img class="avatar" src="@/assets/icons/avatar-common.svg" />
    <!-- <img v-else-if="member.sex === '男'" class="avatar" src="@/assets/icons/avatar-man.svg" /> -->
    <!-- <img v-else class="avatar" src="@/assets/images/avatar.png" /> -->
    <div class="user-info overflow-hidden">
      <div class="person-info">
        <span class="memberName truncate">{{ member.name }}</span>
        <span class="flex-shrink-0">{{ member.sex || '' }}{{ member.sex && member.age ? ' | ' : '' }} {{ member.age || ''
        }}</span>
        <el-tag v-if="member.relationship" class="memberTag flex-shrink-0" type="primary" size="small">
          {{ member.relationship }}
        </el-tag>
      </div>
      <div class="origin-ifno">
        <span c-666>客户来源：</span>
        <span v-if="member.customerSource && member.customerSource[0]">{{
          member.customerSource[0]
        }}</span>
      </div>
    </div>
  </div>
</template>
<script setup>
defineProps({
  member: {
    type: Object,
    default: () => ({})
  }
})
</script>
<style lang="scss" scoped>
.customer-bar {
  display: flex;
  // align-items: center;
  flex-grow: 1;
  padding: 10px 0;

  .avatar {
    flex-shrink: 0;
    width: 60px;
    margin-right: 10px;
  }

  .person-info {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }

  .memberName {
    margin-right: 8px;
    font-size: 16px;
    font-weight: 600;
    color: var(--yc-main-text-color);
  }

  .memberTag {
    margin-left: 6px;
    color: #F59A23;
    background-color: #FFEEE6;
    border-color: #F59A23;
  }

  .user-info {
    flex-grow: 1;
  }
}
</style>