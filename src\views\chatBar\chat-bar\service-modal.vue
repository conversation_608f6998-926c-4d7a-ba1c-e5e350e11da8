<template>
  <el-dialog :model-value="visible" :width="width" title="责任人" @close="close">
    <el-scrollbar wrap-style="max-height: calc(100vh - 360px)">
      <div v-for="item in serviceTimes" :key="item.id" class="px-15px py-12px rounded-4px" border-bottom>
        <div flex items-center class="mb-4px">
          <svg-icon class="mr-8px" name="wx-work" size="20"></svg-icon>
          <ww-user :openid="item.id"></ww-user>
        </div>
        <div>最近服务时间：{{ item.executionTime || '--' }}</div>
      </div>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button plain class="w-100px" @click="close()">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import SvgIcon from '@/components/svg-icon';

const props = defineProps({
  serviceTimes: { type: Array, default: () => [] },
  visible: { type: Boolean, default: false },
  width: { type: Number, default: 520 }
})

const emits = defineEmits(['close']);
function close() {
  emits('close')
}
</script>
<style lang="scss" scoped>
.customer-item:hover {
  background: #FFFAFA;
}
</style>
