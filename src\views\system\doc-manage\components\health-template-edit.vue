<template>
    <el-dialog :model-value="visible" :width="width" title="健康档案模块设置" @close="close">
        <div px-15>
          
            <div class="text-14px text-black py-12px">您可以根据机构类型设置需要的健康档案模块，启用之后，客户健康档案信息内展示启用模块，停用则不展示。</div>
            <div class="flex align-center justify-between h-40px px-5px" border-bottom v-for="item in templates"
                :key="item.templateType">
                <div :class="item.templateStatus === 'enable' ? 'is-active' : ''">{{ item.name }}</div>
                <!-- {{ item.templateStatus }} -->
                <el-button text :type ="item.templateStatus === 'enable' ? 'danger':'primary'" @click="confirm(item)">{{ item.templateStatus === 'enable' ?
        '停用' : '启用' }}</el-button>
            </div>
        </div>
    </el-dialog>
</template>
<script setup>
import { nextTick, ref, toRefs, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { updateCorpTemplateStatus } from '@/api/corp';
const emits = defineEmits(['close', 'onChangeTemplate']);
const props = defineProps({
    visible: { type: Boolean, default: false },
    width: { type: Number, default: 520 },
    templates: { type: Array, default: [] }
})
const name = ref('');
const list = ref([]);
function close() {
    emits('close')
}
function confirm(item) {
    // 提示是否确认修改
    ElMessageBox.confirm("是否确认修改?", { type: 'warning' }).then(async () => {
        const { success } = await updateCorpTemplateStatus(item.templateType, item.templateStatus === 'enable' ? 'disable' : 'enable')
        if (success) {
            ElMessage.success('修改成功')
            emits('onChangeTemplate',item.templateStatus)
        }
    })
}
</script>
<style lang="scss" scoped>
.customer-item:hover {
    background: #FFFAFA;
}

.is-active {
    color: #006eff;
}
</style>