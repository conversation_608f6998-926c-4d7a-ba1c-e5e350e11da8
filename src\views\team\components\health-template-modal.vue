<template>
  <el-dialog :model-value="visible" title="配置健康档案字段" :width="width" @close="close()">
    <div class="flex" style="height:calc(85vh - 200px)">
      <el-scrollbar ref="leftScrollbar" class="w-200px h-full flex-shrink-0 border-r border-gray-200">
        <div v-for="i in tempList" :key="i._id"
          class="flex items-center justify-between px-10px py-5px cursor-pointer border-b border-gray-200 "
          :class="i.templateType === templateType ?'bg-blue-500 text-white':'hover:text-blue-500'" color-normal
          @click="toggle(i.templateType)">
          <div class="mr-10px">{{ i.name }}</div>
          <div @click.stop="toggleEnable(i.templateType)">
            <el-checkbox :model-value="enableTemp[i.templateType]">
              <span :class="i.templateType === templateType ? 'text-white':''">启用</span>
            </el-checkbox>
          </div>
        </div>
      </el-scrollbar>
      <el-scrollbar v-if="enableTemp[templateType]" ref="rightScrollbar" class="flex-grow h-full">
        <el-form class="px-10px pt-10px" label-position="top">
          <el-form-item>
            <template #label> <span color-normal>已选字段</span> </template>
            <div class="flex-grow flex flex-wrap pb-20px border-b border-gray-200">
              <div class="flex-grow flex flex-wrap" :class="tempFields.length ? '' : 'pb-15px'">
                <div v-for="i in tempFields" :key="'selection_' + i.title"
                  class="w-90px relative flex-shrink-0 px-10px py-6px mr-10px mb-15px flex items-center justify-center text-14px leading-4 rounded border-1 border-gray-200 hover:border-blue-500">
                  {{ i.name }}
                  <el-icon v-if="!i.required" class="absolute -right-9px -top-9px cursor-pointer text-red-500" size="18"
                    @click="cancelField(i.title)">
                    <CircleCloseFilled />
                  </el-icon>
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item>
            <template #label> <span color-normal>{{title}}</span> <span
                class="ml-4px text-12px text-gray-500">请点击选择</span>
            </template>
            <div class="flex-grow flex flex-wrap" :class="fieldList.length ? '' : 'pb-15px'">
              <div v-for="i in fieldList" :key="i.title"
                :class="titleMap[i.title] ? 'text-white bg-orange-500' : 'text-gray-500 bg-gray-100'"
                class="w-90px flex-shrink-0 px-10px py-8px mr-10px mb-15px flex items-center justify-center text-14px leading-4 rounded cursor-pointer hover:text-white hover:bg-orange-500"
                @click="toggleField(i.title, i)">
                {{ i.name }}
              </div>
            </div>
          </el-form-item>
        </el-form>
      </el-scrollbar>
      <div v-else class="flex flex-col flex-grow items-center justify-center">
        <empty-data :top="0" title="暂未启用该模版" padding="0 0 15px" text-top="10px" :image-width="120"></empty-data>
      </div>
    </div>
    <template #footer>
      <div text-center>
        <el-button class="w-100px" @click="close">取消</el-button>
        <el-button class="w-100px" type="primary" @click="confirm()"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref, watch, nextTick } from 'vue';
import EmptyData from "@/components/empty-data.vue";
import { ElMessage } from 'element-plus';

const emits = defineEmits(['close', 'change']);
const props = defineProps({
  width: { type: Number },
  data: { type: Object, default: () => ({}) },
  tempList: { type: Array, default: () => ([]) },
  visible: { type: Boolean, default: false },
})

const templateType = ref('');
const enableTemp = ref({});
const tempFieldMap = ref({});
const fieldList = computed(() => getFieldList(templateType.value)); // 当前模板所有字段
const titleList = computed(() => Array.isArray(tempFieldMap.value[templateType.value]) ? tempFieldMap.value[templateType.value] : []); // 当前模板选择了哪些字段的title
const tempFields = computed(() => fieldList.value.filter(i => titleList.value.includes(i.title))); // 当前模板选择了哪些字段
const titleMap = computed(() => titleList.value.reduce((acc, cur) => (acc[cur] = true, acc), {})) // 字段选择情况

const leftScrollbar = ref();
const rightScrollbar = ref();


function close() {
  emits('close')
}

function confirm() {
  const res = [];
  for (const temp of props.tempList) {
    const { templateType } = temp;
    if (enableTemp.value[templateType]) {
      const item = {
        templateType,
        titleList: tempFieldMap.value[templateType] || [],
      }
      if (!Array.isArray(item.titleList) || !item.titleList.length) {
        ElMessage.info(`请选择【${temp.name}】模板的字段`)
        return
      }
      res.push(item)
    }
  }
  emits('change', res);
  close()
}

function getFieldList() {
  const temp = props.tempList.find(i => i.templateType === templateType.value);
  return temp ? temp.templateList.map(i => ({ name: i.name, title: i.title, required: i.required })) : [];
}

async function toggle(i) {
  if (i == templateType.value) return;
  templateType.value = i;
  if (!tempFieldMap.value[i]) {
    tempFieldMap.value[i] = fieldList.value.filter(i => i.required).map(i => i.title);
  }
  await nextTick();
  rightScrollbar.value && rightScrollbar.value.setScrollTop(0);
}

function toggleEnable(templateType) {
  const status = !enableTemp.value[templateType];
  enableTemp.value[templateType] = status;
  if (status && !tempFieldMap.value[templateType]) {
    tempFieldMap.value[templateType] = fieldList.value.filter(i => i.required).map(i => i.title);
  }
}

function toggleField(title, field) {
  if (titleMap.value[title] && !field.required) {
    tempFieldMap.value[templateType.value] = tempFieldMap.value[templateType.value].filter(i => i !== title);
  } else if (!titleMap.value[title]) {
    tempFieldMap.value[templateType.value].push(title);
  }
}

function cancelField(title) {
  tempFieldMap.value[templateType.value] = tempFieldMap.value[templateType.value].filter(i => i !== title);
}

function init() {
  const data = Array.isArray(props.data) ? props.data : [];
  data.forEach(i => {
    const { templateType, titleList } = i;
    if (templateType) {
      enableTemp.value[templateType] = true;
      tempFieldMap.value[templateType] = Array.isArray(titleList) ? [...titleList] : [];
    }
  })
  toggle(props.tempList[0] ? props.tempList[0].templateType : '');
}

watch(() => props.visible, async val => {
  if (val) {
    tempFieldMap.value = {};
    enableTemp.value = {}
    init()
    await nextTick()
    leftScrollbar.value && leftScrollbar.value.setScrollTop(0);
    rightScrollbar.value && rightScrollbar.value.setScrollTop(0);
  }
})

</script>