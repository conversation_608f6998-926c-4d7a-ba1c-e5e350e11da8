<template>
  <el-dialog :model-value="props.visible" :width="width" :title="title" @close="close">
    <div p-15>
      <el-input style="width: 100%;" v-model="result" maxlength="100" :rows="3" show-word-limit type="textarea"
        placeholder="请填写处理结果" />
    </div>
    <template #footer>
      <div text-center>
        <el-button @click="close()">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm()"> 保存 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { setTodoStatus } from "@/api/todo";
const props = defineProps({
  todo: { type: Object, default: () => ({}) },
  type: { type: String, default: '' },
  visible: { type: Boolean, default: false }
})
const title = computed(() => props.type === 'closed' ? '取消任务' : '完成任务');
const width = ref(Math.floor(window.innerWidth * 0.75));

const emits = defineEmits(['close', 'change']);
function close() {
  emits('close')
}
const result = ref('');
const loading = ref(false);
async function confirm() {
  const action = props.type === 'closed' ? '取消' : '完成'
  await ElMessageBox.confirm(`确定${action}该待办事项吗?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  });
  loading.value = true;
  const { success, message } = await setTodoStatus(
    props.todo._id,
    props.type,
    result.value || `已${action}`,
    localStorage.getItem("userId")
  );
  if (success) {
    ElMessage.success(message);
    close()
    emits('change')
  } else {
    ElMessage.error(message);
  }
  loading.value = false;
}
watch(() => props.visible, n => {
  if (n) {
    result.value = '';
    width.value = Math.floor(window.innerWidth - 40);
  }
})

</script>
