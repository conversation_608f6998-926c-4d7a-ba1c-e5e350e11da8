<template>
  <my-layout>
    <layout-main>
      <div class="py-12px px-15px text-14px font-semibold border-b border-gray-100" v-if="planForm">{{ planForm.planName }}</div>
      <form-template ref="formRef" :items="formItems" :form="planForm" :rule="rule" @change="change" />
      <plan-node-list class="mt-40rpx" @edit="editNode($event)" @addTask="addTask" />
    </layout-main>
    <layout-item>
      <div class="p-15px flex bg-white shadow-up relative z-2" common-shadow--r>
        <el-button class="flex-grow" plain @click="cancel()">取消</el-button>
        <el-button class="flex-grow ml-15px" type="primary" @click="executePlan()">执行计划</el-button>
      </div>
    </layout-item>
  </my-layout>
</template>
<script setup>
import { computed, ref, toRefs } from "vue";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import { memberStore } from "@/store/member";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import formTemplate from "../../form-template/edit/index.vue";
import planNodeList from "./components/plan-node-list.vue";
import useChatBarSub from "../../useChatBarSub";
import { memberMangePlanStore } from "@/store/managePlan/memberMangePlan";
import { taskStore } from "@/store/managePlan/task";
const { planForm, team } = storeToRefs(memberMangePlanStore());
const { executPlan } = memberMangePlanStore();
const emits = defineEmits(["cancel", "complete", "editPlanNote"]);

const props = defineProps({
  operationType: {
    type: String,
    default: "add",
  },
});
const formItems = computed(() => {
  let item = [
    { type: "date", name: "开始时间", operateType: "formCell", title: "planExecutionTime", required: true },
    { type: "selectWwuser", name: "计划跟踪人", operateType: "custom", title: "executorUserId", required: true, memberList: Array.isArray(team.value.memberList) ? team.value.memberList : [] },
  ];
  if (props.operationType === "add") {
    item.unshift({ type: "input", name: "计划名称", operateType: "formCell", title: "planName", required: true });
  }
  return item;
});

const rule = {
  planExecutionTime: (e) => {
    if (!e) return "请选择开始时间";
    if (!dayjs(e).isValid()) return "开始时间无效，请重新选择开始时间";
    if (dayjs(e).isBefore(dayjs().startOf("day"))) return "开始时间不得小于当天，请重新选择开始时间";
    return true;
  },
};
function change({ title, value }) {
  planForm.value[title] = value;
}

function cancel() {
  emits("cancel");
}
const formRef = ref(null);
async function executePlan() {
  if (formRef.value.verify()) {
    await executPlan();
    emits("complete");
  }
}
function editNode(node) {
  emits("editPlanNote", "edit");
}

function addTask() {
  emits("editPlanNote", "add");
}
</script>
<style lang="scss" scoped></style>
