<!-- 预约弹框组件 -->
<template>
  <el-dialog v-model="dialogVisible" :title="title" :width="width" :before-close="handleClose" :close-on-click-modal="false">
    <div class="appointment-form">
      <div class="appointment-item flex">
        <span style="color: red">*</span>
        <span class="label">到院日期：</span>
        <el-date-picker 
          v-model="appointmentDate" 
          type="date" 
          placeholder="选择日期" 
          style="width: 100%" 
          format="YYYY-MM-DD" 
          value-format="YYYY-MM-DD"
          :disabled-date="disabledDate" />
      </div>
      <div class="appointment-item flex">
        <span style="color: red">*</span>
        <span class="label">到院时间：</span>
        <el-time-picker 
          v-model="appointmentTime" 
          format="HH:mm" 
          value-format="HH:mm" 
          placeholder="选择时间" 
          style="width: 100%" 
          :disabled-hours="disabledHours"
          :disabled-minutes="disabledMinutes" />
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave" :disabled="!isValid">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import { addAppointmentRecord } from "@/api/consult";
import dayjs from "dayjs";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  width: {
    type: [String, Number],
    default: "500px",
  },
  customer: {
    type: Object,
    default: () => ({}),
  },
  title: {
    type: String,
    default: "预约登记",
  },
});

const emit = defineEmits(["close", "success"]);

const dialogVisible = ref(false);
const appointmentDate = ref("");
const appointmentTime = ref("");

// 禁用今天之前的日期
const disabledDate = (time) => {
  // 获取今天的开始时间（00:00:00）
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return time < today;
};

// 禁用当天当前时间之前的小时
const disabledHours = () => {
  const selectedDate = appointmentDate.value;
  if (!selectedDate) return [];
  
  const today = dayjs().format('YYYY-MM-DD');
  const currentHour = dayjs().hour();
  
  if (selectedDate === today) {
    return Array.from({ length: currentHour }, (_, i) => i);
  }
  
  return [];
};

// 禁用当天当前时间之前的分钟
const disabledMinutes = (hour) => {
  const selectedDate = appointmentDate.value;
  if (!selectedDate) return [];
  
  const today = dayjs().format('YYYY-MM-DD');
  const currentHour = dayjs().hour();
  const currentMinute = dayjs().minute();

  if (selectedDate === today && hour === currentHour) {
    return Array.from({ length: currentMinute }, (_, i) => i);
  }
  
  return [];
};

watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val;
    if (val) {
      resetForm();
    }
  }
);

watch(
  () => dialogVisible.value,
  (val) => {
    if (!val) {
      emit("close");
    }
  }
);

const isValid = computed(() => {
  return appointmentDate.value && appointmentTime.value;
});

const resetForm = () => {
  appointmentDate.value = "";
  appointmentTime.value = "";
  
  // 检查客户是否有未过期的预约时间
  if (props.customer && props.customer.lastAppointmentTime) {
    const appointmentTimestamp = props.customer.lastAppointmentTime;
    const now = Date.now();
    
    // 只有当预约时间未过期时才自动填充
    if (appointmentTimestamp > now) {
      const appointmentDateObj = dayjs(appointmentTimestamp);
      appointmentDate.value = appointmentDateObj.format('YYYY-MM-DD');
      appointmentTime.value = appointmentDateObj.format('HH:mm');
    }
  }
};

const handleClose = () => {
  dialogVisible.value = false;
};

const handleSave = async () => {
  if (!isValid.value) {
    ElMessage.warning("请完善预约信息");
    return;
  }

  try {
    const introducerRecord = props.customer.introducerRecord;
    const introducer = introducerRecord ? introducerRecord[introducerRecord.length - 1]?.introducer : "";
    const params = {
      customerId: props.customer._id,
      appointmentTime: new Date(`${appointmentDate.value}T${appointmentTime.value}`).getTime(),
      name: props.customer.name,
      mobile: props.customer.mobile,
      introducer,
    };
    const { success, message } = await addAppointmentRecord({ params });
    if (!success) {
      ElMessage.error(message || "保存预约失败，请重试");
      return;
    }
    ElMessage.success(message || "预约登记成功");
    emit("success");
  } catch (error) {
    console.error("保存预约失败", error);
    ElMessage.error("保存预约失败，请重试");
  }
};
</script>

<style scoped>
.appointment-form {
  padding: 0 20px;
}
.appointment-item {
  margin-bottom: 20px;
  align-items: center;
}
.label {
  font-weight: bold;
  display: block;
  width: 100px;
  padding-left: 5px;
}
</style>
