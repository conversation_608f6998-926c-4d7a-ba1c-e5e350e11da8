<template>
  <div class="card">
    <div class="card-head">
      <div class="head-main">{{ props.title }}</div>
      <slot name="head"></slot>
    </div>
    <div class="card-body" :class="border ? 'card-body__border' : ''">
      <slot></slot>
    </div>
  </div>
</template>
<script setup>
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  border: {
    type: Boolean,
    default: true,
  },
  icon: {
    type: String,
    default: "",
  },
});
</script>
<style scoped lang="scss">
.card {
  padding: 0 15px;

  .card-head {
    display: flex;
    align-items: center;
    padding: 5px 0 10px;
    font-size: 16px;
    font-weight: 600;
  }

  .head-main {
    flex-grow: 1;
  }

  .card-body {
    padding: 0 15px 15px 0;
    @at-root &__border {
      border-bottom: 1px dashed #eee;
    }
  }

  .card-icon {
    display: inline-block;
    /* width: 24px; */
    height: 24px;
    margin-right: 8px;
  }
}

:deep(.el-form-item) {
  margin-bottom: 0;
}
</style>
