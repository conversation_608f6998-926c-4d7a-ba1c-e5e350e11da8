<template>
  <my-layout v-loading="getTaskLoading" bg-fff common-shadow>
    <!-- <div class="member"> -->
    <layout-item>
      <div class="flex flex-wrap pt-15px px-15px">
        <el-form-item class="w-180px mb-15px mr-10px">
          <el-select v-model="query.executeStatus" placeholder="任务状态" clearable>
            <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item class="w-auto mb-15px mr-10px">
          <el-button type="primary" @click="search()">查询</el-button>
        </el-form-item>
        <el-form-item class="w-auto mb-15px ml-auto">
          <el-button type="primary" @click="add()">新建团队群发</el-button>
        </el-form-item>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <el-table stripe :align="center" :data="selfGroupmsgList" empty-text="暂无数据" height="100%">
        <el-table-column class-name="first-td" prop="taskName" label="任务名称" :min-width="80" />
        <!-- <el-table-column prop="sendType" label="发送类型" :min-width="80" /> -->
        <el-table-column prop="executeStatus" label="任务状态" :min-width="80" />
        <el-table-column prop="createTime" label="创建时间" :min-width="160" />
        <el-table-column prop="creator" label="创建人" :min-width="150">
          <template #default="{ row }">
            <ww-user :openid="row.creator" />
          </template>
        </el-table-column>
        <el-table-column prop="taskTime" label="任务时间" :min-width="220" />
        <el-table-column prop="taskCount" label="需执行成员数" :min-width="110" />
        <el-table-column prop="sendSuccessCount" label="已完成" :min-width="80" />
        <el-table-column prop="noSendCount" label="待完成" :min-width="80" />
        <el-table-column fixed="right" class-name="el-table-fixed-column--shadow last-td" label="操作" width="130">
          <template #default="scope">
            <div flex items-center>
              <el-button class="pl-0" text type="primary" size="small" @click="viewDetail(scope.row)">详情</el-button>
              <el-button text type="primary" class="pl-0 !important" size="small" @click="stopTask(scope.row._id)"
                v-if="scope.row.executeStatus === '进行中' || scope.row.executeStatus === '未开始'">停止任务</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </layout-main>
    <layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="currentPage"
        @handle-size-change="onHandleSizeChange" @handle-current-change="onHandleCurrentChange" />
    </layout-item>
  </my-layout>
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { useRouter } from "vue-router";
import pagination from "@/components/pagination/pagination.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import WwUser from "@/components/ww-user/index.vue";
import dayjs from "dayjs";
import { currentTeam, currentPage, pageSize, groupmsgList, getTaskLoading, total, getWecomGroupmesgList, stopGroupmsgTask } from "../api/groupmsg.js";
let sendTaskType = "TEAM";
let params = { sendSource: sendTaskType };
watch(currentTeam, (newItem, oldItem) => {
  if (newItem && oldItem && newItem.teamId !== oldItem.teamId) {
    getWecomGroupmesgList(params);
  }
});
getWecomGroupmesgList(params);
const router = useRouter();
const query = ref({
  source: "",
  type: "",
  status: "",
  sendTime: "",
});
function add() {
  router.push({ name: "SENDMESSAGE", params: { sendTaskType } });
}
function viewDetail(row) {
  router.push({ name: "TEAMGROUPTASKDETAIL", params: { id: row._id } });
}
const sourceList = [
  { label: "我的群发", value: "MINE" },
  { label: "团队群发", value: "TEAM" },
  { label: "机构群发", value: "CORP" },
];
const typeList = [
  { label: "群发消息", value: "MESSAGE" },
  { label: "群发朋友圈", value: "FRIEND" },
];
const statusList = [
  { label: "未开始", value: "notStart" },
  { label: "进行中", value: "doing" },
  { label: "已结束", value: "end" },
];
const selfGroupmsgList = computed((item) => {
  let list = JSON.parse(JSON.stringify(groupmsgList.value));
  list.forEach((item) => {
    item.sendSource = sourceList.find((source) => source.value === item.sendSource)?.label;
    item.executeStatus = statusList.find((source) => source.value === item.executeStatus)?.label;

    item.createTime = dayjs(item.createTime).format("YYYY-MM-DD HH:mm");
    item.taskTime = item.startTaskDate && item.endTaskDate ? `${dayjs(item.startTaskDate).format("YYYY-MM-DD")}至${dayjs(item.endTaskDate).format("YYYY-MM-DD")}` : "";
    let str = item.attachments
      .map((item) => {
        if (item.msgtype === "link" && item.linkType === 'article') return "[文章]";
        else if (item.msgtype === "link") return "[网页]";
        else if (item.msgtype === "image") return "[图片]";
        else if (item.msgtype === "video") return "[视频]";
        else return "";
      })
      .join("");
    item.content = item.content + str;
  });
  return list;
});

function search() {
  if (query.value.executeStatus) {
    params.executeStatus = query.value.executeStatus;
  } else {
    delete params.executeStatus;
  }
  getWecomGroupmesgList(params);
}

async function stopTask(id) {
  await stopGroupmsgTask(id);
  getWecomGroupmesgList(params);
}

function onHandleSizeChange(e) {
  currentPage.value = 1;
  pageSize.value = e;
  getWecomGroupmesgList(params);
}
function onHandleCurrentChange(e) {
  currentPage.value = e;
  getWecomGroupmesgList(params);
}
</script>
