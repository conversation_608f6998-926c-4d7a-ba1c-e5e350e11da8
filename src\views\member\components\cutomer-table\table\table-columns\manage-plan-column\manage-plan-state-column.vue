<template>
  <el-table-column label="计划执行状态" prop="managePlanState" :width="120">
    <template #default="{row}">
      <td-wrapper classnames="w-full">
        <plan-wrapper :plans="row.plans">
          <template #default="{plan}">
            {{ plan && managementPlanStatus[plan.planExecutStaus] }}
          </template>
        </plan-wrapper>
      </td-wrapper>
    </template>
  </el-table-column>
</template>
<script setup>
import tdWrapper from '../td-wrapper.vue';
import planWrapper from './plan-wrapper.vue';
import { managementPlanStatus } from "@/baseData";

</script>
<style lang="scss" scoped></style>
