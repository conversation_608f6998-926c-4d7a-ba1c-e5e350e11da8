import { ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { createGroupmsgTaskUrl, getWecomGroupmesgListUrl, addWecomMsgTemplateUrl, stopGroupmsgTaskUrl, getGroupmesgByIdUrl, getWecomGroupmsgSendResultUrl, getCusomterGroupmsgSendResultUrl, addWecomMsgTemplateForTeamOrCorpUrl, getTeamMemberExecuteStateUrl, remindGroupmsgSendUrl } from "@/api/groupmsg";
import { ElMessage, ElMessageBox } from "element-plus";
import { teamStore } from "@/store/team";
import { memberStore } from "@/store/member";
import dayjs from "dayjs";

const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const { currentTeam } = storeToRefs(teamStore());
const { corpInfo } = memberStore();
const groupmsgList = ref([]);
const getTaskLoading = ref(false);
async function createGroupmsgTask(params) {
  let { customers, fileList, ...query } = params;
  // externalUserIds
  const attachments = fileList.map((item) => {
    let { type, mediaId, file, URL } = item;
    const { subtitle, name, url, type: fileType } = file;
    let attachment = {};
    if (type === "image") {
      attachment = {
        msgtype: type,
        title: name,
        image: {
          media_id: mediaId,
        },
      };
    } else if (type === "video") {
      attachment = {
        msgtype: type,
        title: name,
        video: {
          media_id: mediaId,
        },
      };
    } else if (type === "link") {
      attachment = {
        msgtype: type,
        title: name,
        linkType: fileType || "",
        link: {
          title: name,
          picurl: URL,
          desc: subtitle,
          url,
        },
      };
    }
    return attachment;
  });
  customers = customers.map((item) => item._id);
  const { corpId, permanent_code } = corpInfo;
  // 去重
  query["corpId"] = corpId;
  query["permanentCode"] = permanent_code;
  query["customers"] = customers;
  query["attachments"] = attachments;
  query["creator"] = localStorage.getItem("userId");
  query["createSounrce"] = query.sendSource;
  if (query["startTaskDate"]) {
    query["executeStatus"] = dayjs(query["startTaskDate"]).isSame(dayjs(), "day") ? "doing" : "notStart";
  }
  const { success, message, data } = await createGroupmsgTaskUrl(query);
  // dayjs 判断 query.startTaskDate 是否等于当天
  if (dayjs(query["startTaskDate"]).isSame(dayjs(), "day") && Array.isArray(query.userIds) && query.userIds.length > 0) {
    await addWecomMsgTemplateForTeamOrCorpUrl(query, data.id);
  }
  if (success) {
    ElMessage.success("创建成功");
    return true;
  } else {
    ElMessage.error(message);
  }
}

async function getWecomGroupmesgList(params = {}) {
  const { corpId, permanent_code } = corpInfo;
  groupmsgList.value = [];
  getTaskLoading.value = true;
  if (params.sendSource === "TEAM") {
    params.teamIds = currentTeam.value && currentTeam.value.teamId ? [currentTeam.value.teamId] : [];
  }
  const { success, data, message } = await getWecomGroupmesgListUrl(corpId, permanent_code, currentPage.value, pageSize.value, params);
  getTaskLoading.value = false;
  if (success) {
    groupmsgList.value = data.data;
    total.value = data.total;
  } else {
    ElMessage.error(message);
  }
}
async function getGroupmesgById(id) {
  let { data, message, success } = await getGroupmesgByIdUrl(id);
  if (success) {
    return data;
  } else {
    ElMessage.error(message);
    return false;
  }
}

async function addWecomMsgTemplate(templateId) {
  const { corpId, permanent_code } = corpInfo;
  const { success, message } = await addWecomMsgTemplateUrl(corpId, permanent_code, templateId);
  if (success) {
    return true;
  } else {
    ElMessage.error(message);
  }
}

async function stopGroupmsgTask(id) {
  await ElMessageBox.confirm("确定停止吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  });
  const { success, message } = await stopGroupmsgTaskUrl(id);
  if (success) {
    ElMessage.success("停止成功");
    return true;
  } else {
    ElMessage.error(message);
  }
}

async function getWecomGroupmsgSendResult(executor, msgid) {
  const { corpId, permanent_code } = corpInfo;
  const query = {
    corpId,
    permanentCode: permanent_code,
    msgid,
    userid: executor,
  };
  let { data, success, message } = await getWecomGroupmsgSendResultUrl(query);
  if (success) {
    return data;
  } else {
    ElMessage.error(message);
    return false;
  }
}
async function getCusomterGroupmsgSendResult(executor, msgid, customers) {
  const { corpId, permanent_code } = corpInfo;
  const query = {
    corpId,
    permanentCode: permanent_code,
    msgid,
    userid: executor,
    customers,
  };
  let { data, success, message } = await getCusomterGroupmsgSendResultUrl(query);
  if (success) {
    return data;
  } else {
    ElMessage.error(message);
    return false;
  }
}
async function getTeamMemberExecuteState(teamTaskId, customers) {
  const { corpId, permanent_code } = corpInfo;
  const query = {
    corpId,
    permanentCode: permanent_code,
    teamTaskId,
    customers,
  };
  let { data, success, message } = await getTeamMemberExecuteStateUrl(query);
  if (success) {
    return data;
  } else {
    ElMessage.error(message);
    return false;
  }
}

async function remindGroupmsgSend(msgid) {
  const { corpId, permanent_code } = corpInfo;
  const { success, message } = await remindGroupmsgSendUrl({ corpId, permanentCode: permanent_code, msgid });
  if (success) {
    ElMessage.success("提醒成功");
    return true;
  } else {
    ElMessage.error(message);
  }
}

export { groupmsgList, currentPage, pageSize, currentTeam, getTaskLoading, total, getTeamMemberExecuteState, getGroupmesgById, createGroupmsgTask, getWecomGroupmesgList, addWecomMsgTemplate, stopGroupmsgTask, getWecomGroupmsgSendResult, getCusomterGroupmsgSendResult, remindGroupmsgSend };
