<template>
  <el-dialog v-model="props.visible" title="麻醉登记" @close="close()" :width="width">
    <div>
      <el-form inline label-suffix="：" :rules="rules" :model="form" ref="formRef">
        <el-row class="m-15px border-gray-200">
          <el-col :span="12">
            <el-form-item required label="麻醉开始时间" label-width="130px" class="mt-10px" prop="aenesthesiaStartTime">
              <el-time-picker v-model="form.aenesthesiaStartTime" placeholder="请选择麻醉时间" style="width: 150px" format="HH:mm" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item required label="麻醉时长" label-width="110px" class="mt-10px" prop="aenesthesiaDuration">
              <el-input type="number" v-model="form.aenesthesiaDuration" class="el-select--hiddenValue w-120px" placeholder="请输入麻醉时长" filterable></el-input>
              <div class="ml-10px">分</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <div text-center>
        <el-button class="w-100px" plain @click="close()">取消</el-button>
        <el-button class="w-100px" type="primary" @click="save(formRef)">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>
  <script setup>
import { ref, watch, reactive } from "vue";
import dayjs from "dayjs";
import { updateDeductRecord } from "@/api/treatment-record";
import { ElMessage } from "element-plus";
const props = defineProps({
  visible: { type: Boolean, default: false },
  project: { type: Object, default: () => {} },
  width: { type: Number, default: 630 },
});
const formRef = ref(null);
const emits = defineEmits(["success", "close"]);
const form = ref({
  aenesthesiaStartTime: "",
  aenesthesiaDuration: "",
});

let rules = reactive({
  aenesthesiaStartTime: [{ required: true, message: "请选择麻醉时间", trigger: "blur" }],
  aenesthesiaDuration: [{ required: true, message: "请输入麻醉时长", trigger: "blur" }],
});

function close() {
  emits("close");
}
watch(
  () => props.visible,
  async (n) => {}
);

async function save(formEl) {
  if (!formEl) return;
  await formEl.validate(async (valid) => {
    if (valid) {
      let { aenesthesiaStartTime, aenesthesiaDuration } = form.value;
      let aenesthesiaEndTime = dayjs(aenesthesiaStartTime).add(aenesthesiaDuration, "minute").valueOf();
      aenesthesiaStartTime = dayjs(aenesthesiaStartTime).valueOf();
      const { success, message } = await updateDeductRecord({
        id: props.project?._id,
        params: {
          aenesthesiaStartTime,
          aenesthesiaEndTime,
        },
      });

      if (success) {
        ElMessage.success("保存成功");
        emits("close");
        emits("success");
      } else {
        ElMessage.error(message);
      }
    }
  });
}
</script>
    
<style lang="scss">
.el-select.el-select--hiddenValue .el-input__inner {
  display: none;
}

.el-select.el-select--hiddenValue .el-input__prefix {
  flex-grow: 1;
}

:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-timeline-item__tail) {
  border-left-color: #006eff;
}
</style>
    