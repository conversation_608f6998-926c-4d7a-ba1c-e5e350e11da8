<template>
  <div bg-fff title-bar class="query-title">
    <!-- 待办日程 -->
  </div>
  <div bg-fff class="query-tabs">
    <el-badge v-for="tab in tabs" :key="tab.name" :value="badgeMap[tab.countKey]" :hidden="!(badgeMap[tab.countKey] > 0)" :max="99">
      <div class="query-tabs__item pointer" :class="query.tab === tab.name ? 'query-tabs__item--active' : ''" @click="changeTab(tab.name)">
        {{ tab.label }}
      </div>
    </el-badge>
  </div>
  <el-form bg-fff inline :label-width="90" label-suffix="：">
    <template v-if="isServiceType">
      <div class="query">
        <el-form-item class="query__item" prop="eventType">
          <el-select :model-value="query.eventType" placeholder="请选择服务类型" clearable @change="change($event, 'eventType')">
            <el-option v-for="(label, value) in ServiceType" :key="value" :label="label" :value="value" />
          </el-select>
        </el-form-item>
        <el-form-item class="query__item" label-width="auto">
          <el-date-picker v-model="query.executionTime" value-format="YYYY-MM-DD" placeholder="请选择服务时间" @change="change($event, 'executionTime')" />
        </el-form-item>
        <el-form-item class="query__item">
          <el-button class="ml-20px" type="primary" @click="search()">查询</el-button>
          <el-button type="primary" @click="reset()" plain>重置</el-button>
        </el-form-item>
        <el-form-item class="query__item query__item--auto">
          <el-button type="primary" @click="addServiceRecordAction()">新增服务记录</el-button>
        </el-form-item>
      </div>
    </template>
    <template v-else>
      <div class="px-15px">
        <el-form-item prop="taskContent">
          <el-input :model-value="query.taskContent" placeholder="请输入任务内容" @input="change($event, 'taskContent')"></el-input>
        </el-form-item>
        <el-form-item prop="eventType">
          <el-select :model-value="query.eventType" placeholder="请选择任务类型" clearable @change="change($event, 'eventType')">
            <el-option v-for="(label, value) in ToDoEventType" :key="value" :label="label" :value="value" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="showExecuteTime" label-width="auto">
          <el-date-picker v-model="query.plannedExecutionTime" placeholder="请选择计划执行时间" value-format="YYYY-MM-DD" @change="change($event, 'plannedExecutionTime')" />
        </el-form-item>
        <el-form-item v-if="showEndTime" label-width="auto">
          <el-date-picker value-format="YYYY-MM-DD" placeholder="请选择结束时间" v-model="query.endTime" @change="change($event, 'endTime')" />
        </el-form-item>
        <el-form-item>
          <el-button class="ml-20px" type="primary" @click="search()">查询</el-button>
          <el-button type="primary" @click="reset()" plain>重置</el-button>
        </el-form-item>
      </div>
    </template>
  </el-form>
  <create-service ref="createServiceRef" @save="onSaveService"></create-service>
</template>
<script setup>
import { computed, ref, toRefs, reactive } from "vue";
import { ToDoEventType, ToDoTaskType, ServiceType } from "@/baseData";
import createService from "@/components/create-service/index.vue";
const tabs = [
  { label: "我的待办", name: ToDoTaskType.pendding, countKey: "untreatedCount" },
  { label: "我的待办处理记录", name: ToDoTaskType.end, countKey: "" },
  { label: "团队未认领待办", name: ToDoTaskType.common, countKey: "commonEventsCount" },
  { label: "我的服务记录", name: "myServiceRecord", countKey: "" },
  { label: "团队服务记录", name: "teamServiceRecord", countKey: "" },
  // { label: "团队完结任务", name: ToDoTaskType.teamEnd, countKey: '' } //countKey: 'treatedCount'
];
const isServiceType = ref(false);
const createServiceRef = ref("");
const props = defineProps({
  count: { type: Object, default: () => ({}) },
  params: { type: Object, default: () => ({}) },
});
const { params: query } = toRefs(props);
const showExecuteTime = computed(() => [ToDoTaskType.pendding, ToDoTaskType.common].includes(props.params.tab));
const showEndTime = computed(() => [ToDoTaskType.end, ToDoTaskType.teamEnd].includes(props.params.tab));

const badgeMap = computed(() => {
  const res = tabs.reduce((val, { countKey }) => {
    val[countKey] = props.count[countKey];
    return val;
  }, {});
  return res;
});
const emits = defineEmits(["change", "reset", "search", "changeType"]);
function changeTab(tab) {
  isServiceType.value = ["myServiceRecord", "teamServiceRecord"].includes(tab);
  emits("changeType", tab);
  if (query.value.tab !== tab) {
    change(tab, "tab");
    search();
  }
}
function change(value, key) {
  emits("change", { value, key });
}
function reset() {
  emits("reset");
}

function search() {
  emits("search");
}

function addServiceRecordAction() {
  createServiceRef.value.open("add");
}

function onSaveService() {
  emits("search");
}
</script>
<style scoped lang="scss">
.query-title {
  padding: 15px 0 0;
}

.query-tabs {
  padding: 0 15px 15px;
}

.query-tabs__item {
  display: inline-block;
  padding: 6px 16px;
  margin-right: 12px;
  border-radius: 16px;
  background-color: #f5f7fb;
  color: #333;
  border: 1px solid #ddd;
  font-size: 14px;
}

.query-tabs__item--active {
  background-color: #1261ff;
  border-color: #1261ff;
  color: #fff;
  font-weight: 600;
}

.query-form {
  padding: 0 15px;
}

:deep(.auto-width .el-form-item__content) {
  width: auto;
}

:deep(.el-form--inline .el-form-item) {
  margin-right: 10px;
}

:deep(.el-form-item) {
  margin-right: 10px;
}

:deep(.el-badge__content.is-fixed) {
  right: calc(1px + var(--el-badge-size) / 2 + 12px);
}

.query {
  display: flex;
  flex-wrap: wrap;
  padding: 0 15px;

  .query__item {
    margin-bottom: 15px;
    margin-right: 10px;

    @at-root &--large {
      width: 240px;
    }

    @at-root &--small {
      width: 140px;
    }

    @at-root &--small {
      width: 140px;
    }

    @at-root &--auto {
      width: auto;
    }

    @at-root &:last-of-type {
      margin-left: auto;
      margin-right: 0;
    }
  }
}
</style>
<!-- <style>
.query-form .el-form-item__content {
  width: 200px;
}

.query-form .el-form-item {
  margin-right: 10px;
}
</style> -->
