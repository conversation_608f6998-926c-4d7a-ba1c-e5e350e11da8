<template>
  <filter-item label="到院状态" :text="text" :width="100">
    <radio-group v-model="arrivalStatus" v-model:text="text" :list="list" />
  </filter-item>
</template>
  <script setup>
import { ref } from "vue";
import FilterItem from "./filter-item.vue";
import radioGroup from "./radio-group.vue";

const list = [
  { label: "未到院", value: "notInHospital" },
  { label: "已到院", value: "inHospital" },
];
const arrivalStatus = ref("");
const text = ref("");

function getParam() {
  return arrivalStatus.value ? { arrivalStatus: arrivalStatus.value } : {};
}

function reset() {
    arrivalStatus.value = "";
}

defineExpose({
  getParam,
  reset,
});
</script>
  <style lang="scss" scoped></style>
  