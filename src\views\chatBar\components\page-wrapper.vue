<template>
  <MyLayout class="bg-gray-50">
    <layout-item>
      <div class="bg-white px-15px py-12px">
        <div class="flex items-center text-16px text-dark-500" @click="back()">
          <el-icon>
            <ArrowLeftBold />
          </el-icon>
          <div class="ml-5px">返回</div>
        </div>
      </div>
    </layout-item>
    <layout-item>
      <slot name="header"></slot>
    </layout-item>
    <LayoutMain>
      <slot></slot>
    </LayoutMain>
    <layout-item>
      <slot name="footer"></slot>
    </layout-item>
  </MyLayout>
</template>
<script setup>
import { useThrottleFn } from "@vueuse/core";
import MyLayout, { LayoutMain } from "@/components/layout";
const emits = defineEmits(['back'])

const back = useThrottleFn(() => {
  emits('back')
}, 300)

</script>
<style lang="scss" scoped></style>
