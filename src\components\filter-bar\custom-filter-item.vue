<template>
  <div class="flex-shrink-0 relative flex items-center text-14px h-40px pr-30px" @click="onClick()">
    <div class="flex-shrink-0 pl-10px text-gray-500"> {{ label ? `${label}：`:''}}</div>
    <slot></slot>
  </div>
</template>
<script setup>
const emits = defineEmits(['onClick'])
const props = defineProps({
  label: { type: String, default: '' },
  text: { type: String, default: '' },
  width: { type: [Number, String], default: 280 },
})

function onClick() {
  emits('onClick')
}
</script>
<style lang="scss" scoped></style>
