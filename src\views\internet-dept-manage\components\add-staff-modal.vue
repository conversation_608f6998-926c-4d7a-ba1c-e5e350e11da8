    <template>
  <el-dialog :model-value="visible" :width="width" title="添加员工到科室" @close="close()">
    <el-scrollbar wrap-class="el-dialog-body-full-scroll">
      <el-form class="p-15px" label-suffix="：" :label-width="100" ref="formRef" :model="form" :rules="rules">
        <div v-if="dept" color-normal class="mb-15px pl-10px text-14px font-semibold">
          向【{{ dept.deptName }}】科室添加员工
        </div>
        
        <el-form-item class="is-required" label="选择员工" prop="userId">
          <el-select 
            v-model="form.userId" 
            placeholder="请选择员工" 
            class="w-full"
            filterable
            remote
            reserve-keyword
            :remote-method="searchStaffList"
            :loading="searchLoading"
          >
            <el-option 
              v-for="staff in staffOptions" 
              :key="staff.userId" 
              :label="`${staff.name} (${staff.userId})`" 
              :value="staff.userId"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item class="is-required" label="岗位" prop="jobName">
          <el-select v-model="form.jobName" placeholder="请选择岗位" class="w-full">
            <el-option label="主任医师" value="主任医师" />
            <el-option label="副主任医师" value="副主任医师" />
            <el-option label="主治医师" value="主治医师" />
            <el-option label="住院医师" value="住院医师" />
            <el-option label="护士长" value="护士长" />
            <el-option label="主管护师" value="主管护师" />
            <el-option label="护师" value="护师" />
            <el-option label="护士" value="护士" />
            <el-option label="技师" value="技师" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="备注" prop="remark">
          <el-input 
            type="textarea" 
            v-model="form.remark" 
            placeholder="请输入备注信息" 
            maxlength="200"
            :autosize="{ minRows: 3, maxRows: 6 }" 
            resize="none" 
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button plain class="w-100px" @click="close()">取消</el-button>
        <el-button class="w-100px" :loading="loading" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from "vue";
import { ElMessage } from "element-plus";

const emits = defineEmits(["close", "change"]);
const props = defineProps({
  dept: { type: Object, default: null },
  visible: { type: Boolean, default: false },
  width: { type: [String, Number], default: "600px" },
});

const formRef = ref();
const form = ref({
  userId: '',
  jobName: '',
  remark: ''
});
const loading = ref(false);
const searchLoading = ref(false);

// 模拟员工数据
const allStaff = ref([]);
const staffOptions = ref([]);

// 获取所有员工数据
async function fetchAllStaff() {
  try {
    const { data, success } = await searchStaff({});
    if (success) {
      allStaff.value = data.list;
      staffOptions.value = data.list.slice(0, 5);
    }
  } catch (error) {
    ElMessage.error('获取员工数据失败');
  }
}

// 表单验证规则
const rules = {
  userId: [
    { required: true, message: "请选择员工", trigger: "change" }
  ],
  jobName: [
    { required: true, message: "请选择岗位", trigger: "change" }
  ]
};

// 搜索员工
async function searchStaffList(query) {
  if (query) {
    searchLoading.value = true;
    try {
      const { data, success } = await searchStaff({ keyword: query });
      if (success) {
        staffOptions.value = data.list;
      }
    } catch (error) {
      ElMessage.error('搜索员工失败');
    } finally {
      searchLoading.value = false;
    }
  } else {
    staffOptions.value = allStaff.value.slice(0, 5);
  }
}

// 关闭弹窗
function close() {
  emits("close");
}

// 确认提交
async function confirm() {
  if (loading.value) return;
  
  try {
    await formRef.value.validate();
  } catch (error) {
    return;
  }
  
  loading.value = true;
  

}

// 监听visible变化，重置表单
watch(() => props.visible, (newVal) => {
  if (newVal) {
    form.value = {
      userId: '',
      jobName: '',
      remark: ''
    };
    fetchAllStaff(); // 获取员工数据
    if (formRef.value) {
      formRef.value.clearValidate();
    }
  }
});
</script>

<style scoped lang="scss">
.w-full {
  width: 100%;
}
</style>
