<template>
  <my-layout>
    <layout-item>
      <div class="overflow-hidden mb-8px">
        <el-row :gutter="10">
          <el-col v-for="i in count" :key="i.key" :span="6">
            <div class="flex flex-col items-center bg-white rounded px-20px py-16px h-80px text-center select-none">
              <div class="w-full flex items-center justify-center pt-4px truncate text-18px font-semibold">
                <span>{{ i.enableHidden && i.hidden ? "--" : i.count }}</span>
              </div>
              <div class="w-full mt-8px text-14px truncate text-gray-500">{{ i.label }}</div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="py-5px flex bg-white">
        <expand-filter-box v-model:can-expand="canExpand" :enable-expand="true" class="flex-grow" :expand="expand">
          <date-range-filter v-model:dates="searchForm.billTimeDates" label="购买日期" :text="getDatesText(searchForm.billTimeDates)" />
          <base-filter-item :clearable="selectDepts.length > 0" class="cursor-pointer" label="治疗科室" :text="deptText" @clear="clearDepts" @onClick="showDeptPicker()" />
          <input-filter v-model="searchForm.name" label="客户姓名" placeholder="请输入" :width="200" />
          <check-box-filter v-model="searchForm.treatmentStatus" label="治疗状态" :list="TreatmentStatus"></check-box-filter>
          <base-filter-item :clearable="projectCategories.length > 0" class="cursor-pointer" label="项目分类" :text="categoryText" @clear="clearCategories" @onClick="showCategoryPicker()" />
          <base-filter-item :clearable="projects.length > 0" class="cursor-pointer" label="开单项目" :text="projectText" @clear="clearProjects" @onClick="showProjectModal()" />
          <base-filter-item :clearable="packageList.length > 0" class="cursor-pointer" label="套餐" :text="packageText" @clear="changePackage([])" @onClick="showPackageModal()" />
        </expand-filter-box>
        <div class="flex-shrink-0 flex items-center h-40px pr-15px">
          <el-button v-if="canExpand" text plain @click="expand = !expand">
            <span class="mr-5px">{{ expand ? "收起筛选" : "更多筛选" }}</span>
            <el-icon v-if="expand">
              <ArrowUpBold />
            </el-icon>
            <el-icon v-else>
              <ArrowDownBold />
            </el-icon>
          </el-button>
          <el-button type="primary" @click="search">查询</el-button>
          <el-button plain @click="reset">重置</el-button>
          <!-- <el-button plain type="primary" @click="bill()">新增治疗</el-button> -->
          <el-button plain type="primary" @click="addBenefit()">新增权益</el-button>
        </div>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <el-table border stripe height="100%" :data="list" v-loading="loading" @row-click="handleRowClick" :span-method="arraySpanMethod" :cell-class-name="getCellClassName">
        <el-common-column fixed prop="name" label="姓名" :min-width="100">
          <template #default="{ row: { name, customerId } }">
            <div @click.stop="toCustomerDetail(customerId)" main-color class="pointer">{{ name }}</div>
          </template>
        </el-common-column>
        <el-common-column fixed prop="sexAge" label="性别/年龄" :min-width="90" />
        <el-common-column fixed prop="mobile" label="联系方式" :min-width="120">
          <template #default="{ row: { mobile } }">
            <span>{{ maskPhone(mobile, 3, 4) }}</span>
          </template>
        </el-common-column>
        <el-common-column prop="wechat" label="微信联系人" :min-width="120" classnames="w-full">
          <template #default="{ row }">
            <wx-contact-td :customerId="row.customerInfo ? row.customerInfo._id : ''" :externalUserId="row.customerInfo ? row.customerInfo.externalUserId : ''" :row="row.customerInfo" @change="getList" />
          </template>
        </el-common-column>
        <el-common-column prop="treatmentStatusStr" label="治疗状态" :min-width="160">
          <template #default="{ row }">
            <div>
              <div :style="'color:' + TreatmentStatusColor[row.treatmentStatus]">{{ row.treatmentStatusStr }}</div>
              <div class="text-red-500" v-if="row.aenesthesiaEndTime && row.treatmentStatus === 'pending'">麻醉中(剩余{{ getAnesthesiaRestTme(row) }}分钟)</div>
            </div>
          </template>
        </el-common-column>
        <el-common-column prop="projectName" label="开单项目" :min-width="160"></el-common-column>
        <el-common-column prop="packageName" label="所属套餐" :min-width="160">
          <template #default="{ row: { packageName } }">{{ packageName || "-" }}</template>
        </el-common-column>
        <el-common-column prop="usageCount" label="购买数量" :min-width="120" />
        <el-common-column prop="restUsageCount" label="剩余数量" :min-width="120" />
        <el-common-column prop="validTime" label="有效期" :min-width="120">
          <template #default="{ row: { validTime } }">
            <div v-if="!validTime">无限期</div>
            <div style="color: red" v-else-if="validTime && validTime < dayjs().endOf('day').valueOf()">{{ dayjs(validTime).format("YYYY-MM-DD") }}(已过期)</div>
            <div v-else>
              {{ dayjs(validTime).format("YYYY-MM-DD") }}
            </div>
          </template>
        </el-common-column>
        <el-common-column prop="treatmentDeptName" label="治疗科室" :min-width="140" />
        <el-common-column prop="billTimeStr" label="购买日期" :min-width="140" />
        <el-common-column prop="treatmentDoctorUserId" label="开单人" :min-width="140">
          <template #default="{ row: { billdCreator } }">
            <ww-user :openid="billdCreator" />
          </template>
        </el-common-column>
        <el-common-column prop="price" label="原价" :min-width="140">
          <template #default="{ row: { price } }">
            <span>{{ price && price.toFixed ? price.toFixed(2) : price }}</span>
          </template>
        </el-common-column>
        <el-common-column prop="discount" label="折扣" :min-width="140" />
        <el-common-column prop="totalPrice" label="实付" :min-width="140">
          <template #default="{ row: { totalPrice } }">
            <span>{{ totalPrice && totalPrice.toFixed ? totalPrice.toFixed(2) : totalPrice }}</span>
          </template>
        </el-common-column>
        <el-table-column fixed="right" class-name="el-table-fixed-column--shadow" label="操作" width="150">
          <template #default="{ row }">
            <el-text class="cursor-pointer mr-5px" type="primary" v-if="row.treatmentStatus === 'init'" @click.stop="openReceptionInList(row)">开始治疗</el-text>
            <el-text class="cursor-pointer mr-5px" type="primary" v-if="row.treatmentStatus !== 'init' || row.deductUsageCount" @click.stop="showCureRecord(row)">治疗记录</el-text>
          </template>
        </el-table-column>
      </el-table>
    </layout-main>
    <layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize" @handle-current-change="changePage" />
    </layout-item>
  </my-layout>
  <search-modal v-model="searchVisible" @close="searchClose()" @consult="openBenefitReception" @addCustomer="onAddCustomerWithBenefit" />
  <add-customer :customer="newCustomerInfo" :visible="addCustomerVisible" @close="addCustomerVisible = false" @update="addBenefitCustomerSuccess" title="新增患者" viewType="consult" />
  <customer-detail :enableAction="['managementPlan']" :visible="customerDetailVisible" @close="customerDetailVisible = false" :customerId="customerId" title="客户详情" @update="addSuccess" customerType="corpCustomer" />
  <project-list-picker :value="projects" :visible="projectModalVisible" :width="projectModalWidth" :current-category="projectCategories" @close="closeProjectModal()" @change="changeProjects" />
  <category-picker ref="categoryPickerRef" :value="projectCategories" :visible="categoryPickerVisible" :width="categoryPickerWidth" @close="closeCategoryPicker" @change="changeCategories" @categoryChange="handleCategoryChange" />
  <customer-bill-record :visible="billVisible" @close="closeBillVisible" :customer="customer" @success="getList" />
  <anesthesia-registration :visible="registrationVisible" @close="closeRegistrationVisible" :project="selectproject" @success="getList"></anesthesia-registration>
  <deduct-modal v-model="deductVisible" @close="closeDeductVisible" :project="selectproject" @success="success" @showCustomerDetail="toCustomerDetail"></deduct-modal>
  <treatment-record :visible="treatmentRecordVisible" @close="closeTreatmentRecordVisible" :project="selectproject" @success="success" />
  <dept-picker-modal :onlyStaffDepts="!(isManager || isAdmin)" ref="deptPickerRef" :value="deptIds" :visible="deptVisible" :width="deptPickerWidth" @change="deptChange" @close="closeDeptPicker" />
  <project-package-picker :data="packageList" :visible="packageModalVisible" :width="packageModalWidth" @close="closePackageModal()" @change="changePackage" />
  <cure-record-drawer :visible="cureRecordVisible" @close="cureRecordVisible = false" :treatmemtItem="treatmemtItem" />
  <benefit-drawer v-model:visible="benefitDrawerVisible" @close="closeBenefitDrawerHandler" :customer="customer" @success="benefitSuccess" @showCustomerDetail="toCustomerDetail" />
</template>
<script setup>
import { ref, onMounted, computed, watch } from "vue";
import { storeToRefs } from "pinia";
import { ElMessageBox, ElMessage } from "element-plus";
import dayjs from "dayjs";
import { getCustomers } from "@/api/member";
import { getTreatmentRecord, treatmentRecordStatistic, treatmentRecordStatisticByStatus } from "@/api/treatment-record";
import { getProjectListByCateIds } from "@/api/project-manage";
import { TreatmentStatus, TreatmentStatusColor, DeductStatus } from "@/baseData";
import useElPagination from "@/hooks/useElPagination";
import useModal from "@/hooks/useModal";
import { memberStore } from "@/store/member";
import { staffStore } from "@/store/staff";
import { teamStore } from "@/store/team";
import { maskPhone } from "@/utils";

import addCustomer from "@/views/member/customer/add-customer.vue";
import anesthesiaRegistration from "./anesthesia-registration.vue";
import benefitDrawer from "./benefit-drawer.vue";
import { baseFilterItem, DateRangeFilter, InputFilter, expandFilterBox, CheckBoxFilter } from "@/components/filter-bar";
import cureDrawer from "./cure-drawer.vue";
import cureRecordDrawer from "./cure-record-drawer.vue";
import customerBillRecord from "./customer-bill-record.vue";
import customerDetail from "@/views/member/detail/index.vue";
import deductModal from "./deduct-modal.vue";
import deptPickerModal from "@/components/dept-picker-modal/dept-picker-modal.vue";
import elCommonColumn from "@/components/el-common-column/el-common-column.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import projectListPicker from "@/components/project-picker/project-list-picker.vue";
import categoryPicker from "@/components/project-picker/category-picker.vue";
import projectPackagePicker from "@/components/project-package-picker/project-package-picker.vue";
import searchModal from "../consult-appoint/search-modal.vue";
import treatmentRecord from "./treatment-record.vue";
import wwUser from "@/components/ww-user/index.vue";
import wxContactTd from "@/components/wx-contact-td/wx-contact-td.vue";

const { currentTeamId, managerList } = storeToRefs(teamStore());
const { memberInfo } = storeToRefs(memberStore());
const customerDetailVisible = ref(false);
const loading = ref(false);
const customerId = ref("");
const selectproject = ref({});
const belongDeptIds = ref([]);
const canExpand = ref(false);
const expand = ref(false);
const packageList = ref([]);
const newCustomerInfo = ref({});
const cureDrawerVisible = ref(false);
const cureRecordVisible = ref(false);
const treatmemtItem = ref({});

function toCustomerDetail(id) {
  customerId.value = id;
  customerDetailVisible.value = true;
}
const searchForm = ref({
  billTimeDates: [dayjs().format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")],
});
const deptPickerRef = ref(null);

watch(
  () => currentTeamId.value,
  () => {
    getList();
    treatmentStatistic();
  }
);

const selectDepts = ref([]);
const projects = ref([]);
const projectCategories = ref([]);
const categoryPickerRef = ref(null);
const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());
const list = ref([]);
const { isAdmin } = storeToRefs(memberStore());
const { judgmentIsAdmin } = memberStore();
const isManager = computed(() => {
  return managerList.value.includes(10009);
});

const projectText = computed(() => {
  if (projects.value.length === 0) return "";
  if (projects.value.length == 1 && projects.value[0].projectName) return projects.value[0].projectName;
  return `已选择${projects.value.length}个项目`;
});
const categoryText = computed(() => {
  if (projectCategories.value.length === 0) return "";
  if (projectCategories.value.length > 1) return `已选择${projectCategories.value.length}个分类`;
  return projectCategories.value[0].name;
});
const packageText = computed(() => {
  if (packageList.value.length === 0) return "";
  if (packageList.value.length === 1) return packageList.value[0].name;
  return `已选择${packageList.value.length}个套餐`;
});

const deptIds = computed(() => selectDepts.value.map((i) => i._id));
const deptText = computed(() => {
  if (selectDepts.value.length === 0) return "";
  if (selectDepts.value.length > 1) return `已选择${selectDepts.value.length}个项目`;
  return selectDepts.value[0].deptName;
});
function clearProjects() {
  projects.value = [];
}
function clearCategories() {
  projectCategories.value = [];
  categoryPickerRef.value?.clear();
}
function getDatesText(dates) {
  return dates && Array.isArray(dates) ? dates.join(" ~ ") : "";
}
const total = ref(0);
const { page, pageSize, changePage, changeSize } = useElPagination(getList);
const { show: addCustomerShow, visible: addCustomerVisible, close: addCustomerClose } = useModal();
const { close: searchClose, show: searchShow, visible: searchVisible, width: searchWidth } = useModal(1200);
const { close: closeProjectModal, show: showProjectModal, visible: projectModalVisible, width: projectModalWidth } = useModal(800);
const { close: closeCategoryPicker, show: showCategoryPicker, visible: categoryPickerVisible, width: categoryPickerWidth } = useModal(600);
const { close: closeBillVisible, show: showBillVisible, visible: billVisible } = useModal(800);
const { close: closeRegistrationVisible, show: showRegistrationVisible, visible: registrationVisible } = useModal(800);
const { close: closeDeductVisible, show: showDeductVisible, visible: deductVisible } = useModal(800);
const { close: closeTreatmentRecordVisible, show: showTreatmentRecordVisible, visible: treatmentRecordVisible } = useModal(800);
const { close: closeDeptPicker, show: showDeptPicker, visible: deptVisible, width: deptPickerWidth } = useModal(640);
const { close: closePackageModal, show: showPackageModal, visible: packageModalVisible, width: packageModalWidth } = useModal(800);
const { close: closeBenefitDrawer, show: showBenefitDrawer, visible: benefitDrawerVisible } = useModal(800);

const customer = ref({});
const consultRecord = ref({});
const orderCount = ref({});
const arraySpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  // 只处理需要合并的列
  if (columnIndex !== 0 && columnIndex !== 1 && columnIndex !== 2) {
    return;
  }

  const rows = list.value;

  // 防御性检查，确保行和customerInfo存在
  if (!row || !row.customerInfo || !row.customerInfo._id) {
    return { rowspan: 1, colspan: 1 };
  }

  const currentId = row.customerInfo._id;
  let count = 1; // 当前行算一行

  // 向下查找连续的相同customerInfo._id的行
  for (let i = rowIndex + 1; i < rows.length; i++) {
    const nextRow = rows[i];
    if (nextRow && nextRow.customerInfo && nextRow.customerInfo._id === currentId) {
      count++;
    } else {
      break; // 一旦不同就停止
    }
  }

  // 检查当前行是否是连续相同行的第一行
  if (rowIndex > 0 && rows[rowIndex - 1] && rows[rowIndex - 1].customerInfo && rows[rowIndex - 1].customerInfo._id === currentId) {
    // 不是第一行，隐藏
    return { rowspan: 0, colspan: 0 };
  }

  // 是连续相同行的第一行
  return { rowspan: count, colspan: 1 };
};
const count = ref([
  { key: 1, label: "总治疗单", count: 0 },
  { key: 2, label: "待治疗", count: 0 },
  { key: 3, label: "治疗中", count: 0 },
  { key: 4, label: "已治疗", count: 0 },
]);
function deptChange(val) {
  selectDepts.value = val;
  closeDeptPicker();
}
function changeProjects(data) {
  projects.value = data.projects;
  closeProjectModal();
}
function changeCategories(data) {
  projectCategories.value = data.categories;
  // 当分类变更时，清空已选项目
  projects.value = [];
}
function handleCategoryChange(category) {
  // 当前分类变化时，清空已选项目
  projects.value = [];
}
function changePackage(data) {
  packageList.value = data;
}
function clearDepts() {
  selectDepts.value = [];
}
async function treatmentStatistic() {
  let params = {
    dates: searchForm.value.billTimeDates,
    treatmentDoctorUserIds: searchForm.value.treatmentDoctorUserIds,
  };
  if (!isAdmin.value) {
    params.creators = [localStorage.getItem("userId")];
  }
  if (!isManager.value && !isAdmin) {
    params.teamId = currentTeamId.value;
  }
  if (!isAdmin.value && !selectDepts.value.length && !isManager.value) {
    params.treatmentDept_ids = belongDeptIds.value;
  }
  if (selectDepts.value.length) {
    params.treatmentDept_ids = selectDepts.value.map((i) => i._id);
  }

  // 添加项目筛选逻辑，与search函数保持一致
  if (projects.value.length > 0) {
    // 如果选择了具体项目，直接使用项目ID
    params.projectIds = projects.value.map((i) => i._id);
  } else if (projectCategories.value.length > 0) {
    // 如果只选择了项目分类而没有选择具体项目，根据分类获取项目ID
    try {
      const cateIds = projectCategories.value.map((category) => category._id);
      const { success, data } = await getProjectListByCateIds({ 
        corpId: localStorage.getItem("corpId"), 
        cateIds 
      });
      
      if (success && data && Array.isArray(data.data)) {
        const projectIds = data.data.map((project) => project._id);
        params.projectIds = projectIds;
      }
    } catch (error) {
      console.error('获取分类下的项目失败:', error);
    }
  }
  
  if (searchForm.value.categoryIds) {
    params.categoryIds = searchForm.value.categoryIds;
  }
  if (packageList.value.length > 0) {
    params.packageIds = packageList.value.map((i) => i._id);
  }

  let { success, data } = await treatmentRecordStatisticByStatus({
    params,
  });
  if (success) {
    const { totalCount = 0, init = 0, pending = 0, treated = 0 } = data.data;
    count.value = [
      { key: 1, label: "总治疗单", count: totalCount },
      { key: 2, label: "待治疗", count: init },
      { key: 3, label: "治疗中", count: pending },
      { key: 4, label: "已治疗", count: treated },
    ];
  }
}

function openReception(item) {
  customer.value = item;
  searchClose();
  showBillVisible();
}
function openReceptionInList(item) {
  customer.value = item.customerInfo;
  searchClose();
  showBillVisible();
}
function success() {
  treatmentStatistic();
  getList();
}
// 计算麻醉剩余时间 以分钟为单位
function getAnesthesiaRestTme(row) {
  const aenesthesiaEndTime = dayjs(row.aenesthesiaEndTime);
  const restTime = aenesthesiaEndTime.diff(dayjs(), "minute");
  return restTime > 0 ? restTime : 0;
}
function onAddCustomer() {
  newCustomerInfo.value = { relationship: "本人" };
  searchClose();
  addCustomerShow();
}
async function addSuccess(id) {
  // 根据id获取数据
  let { success, data } = await getCustomers({ _id: id });
  if (success && data && data.data.length > 0) {
    customer.value = data.data[0];
    receptionShow();
  }
}

async function getList() {
  let query = {
    page: page.value,
    pageSize: pageSize.value,
    ...searchForm.value,
  };

  if (searchForm.value.treatmentStatus && searchForm.value.treatmentStatus.length === 0) {
    delete query.treatmentStatus;
  }
  if (!isManager.value && !isAdmin) {
    query.teamId = currentTeamId.value;
  }
  if (!selectDepts.value.length && !isManager.value && !isAdmin.value) {
    query.treatmentDept_ids = belongDeptIds.value;
  }
  if (selectDepts.value.length) {
    query.treatmentDept_ids = selectDepts.value.map((i) => i._id);
  }

  // 处理项目筛选逻辑，与search函数保持一致
  if (projects.value.length > 0) {
    // 如果选择了具体项目，直接使用项目ID
    query.projectIds = projects.value.map((i) => i._id);
  } else if (projectCategories.value.length > 0) {
    // 如果只选择了项目分类而没有选择具体项目，根据分类获取项目ID
    try {
      const cateIds = projectCategories.value.map((category) => category._id);
      const { success, data } = await getProjectListByCateIds({ 
        corpId: localStorage.getItem("corpId"), 
        cateIds 
      });
      
      if (success && data && Array.isArray(data.data)) {
        const projectIds = data.data.map((project) => project._id);
        query.projectIds = projectIds;
      }
    } catch (error) {
      console.error('获取分类下的项目失败:', error);
    }
  }

  // 只有选择了套餐时才设置套餐ID
  if (packageList.value.length > 0) {
    query.packageIds = packageList.value.map((i) => i._id);
  }

  loading.value = true;
  const { success, data } = await getTreatmentRecord(query);
  loading.value = false;
  if (success) {
    total.value = data.total;
    orderCount.value = data.list.reduce((acc, item) => {
      if (item.treatmentOrderId) acc[item.treatmentOrderId] = (acc[item.treatmentOrderId] || 0) + 1;
      return acc;
    }, {});
    list.value = data.list.map((i) => {
      return {
        ...i,
        name: i.customerInfo.name,
        mobile: i.customerInfo.mobile,
        projectNames: Array.isArray(i.projectNames) ? i.projectNames.join(" / ") : "",
        sexAge: i.customerInfo.sex && i.customerInfo.age ? `${i.customerInfo.sex} / ${i.customerInfo.age}` : `${i.customerInfo.sex || ""}${i.customerInfo.age || ""}`,
        createTreatementTimeStr: i.createTreatementTime ? dayjs(i.createTreatementTime).format("YYYY-MM-DD") : "",
        treatmentStatusStr: i.treatmentStatus ? TreatmentStatus.find((j) => j.value === i.treatmentStatus).label : "",
        deductStatusStr: i.deductStatus ? DeductStatus.find((j) => j.value === i.deductStatus).label : "",
        treatmentTimeStr: i.treatmentTime ? dayjs(i.treatmentTime).format("YYYY-MM-DD") : "",
        billTimeStr: i.billTime ? dayjs(i.billTime).format("YYYY-MM-DD") : "",
        deductedPriceStr: i.deductedPrice?.toFixed(2),
        deductCount: i.usageCount - i.restUsageCount,
        discount: i.isFree ? "赠送" : i.discount,
        price: i.price * i.usageCount, // 原价 * 数量
      };
    });
  }
}

async function search() {
  page.value = 1;
  
  // 处理项目筛选逻辑
  if (projects.value.length > 0) {
    // 如果选择了具体项目，直接使用项目ID
    searchForm.value.projectIds = projects.value.map((i) => i._id);
  } else if (projectCategories.value.length > 0) {
    // 如果只选择了项目分类而没有选择具体项目，根据分类获取项目ID
    try {
      const cateIds = projectCategories.value.map((category) => category._id);
      const { success, data } = await getProjectListByCateIds({ 
        corpId: localStorage.getItem("corpId"), 
        cateIds 
      });
      
      if (success && data && Array.isArray(data.data)) {
        const projectIds = data.data.map((project) => project._id);
        searchForm.value.projectIds = projectIds;
      } else {
        delete searchForm.value.projectIds;
      }
    } catch (error) {
      console.error('获取分类下的项目失败:', error);
      delete searchForm.value.projectIds;
    }
  } else {
    delete searchForm.value.projectIds;
  }
  
  // 只有选择了套餐时才设置套餐ID
  if (packageList.value.length > 0) {
    searchForm.value.packageIds = packageList.value.map((i) => i._id);
  } else {
    delete searchForm.value.packageIds;
  }
  
  getList();
  treatmentStatistic();
}
function reset() {
  page.value = 1;
  searchForm.value = {
    billTimeDates: [dayjs().format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")],
  };

  projects.value = [];
  projectCategories.value = [];
  packageList.value = [];
  selectDepts.value = [];
  search();
}
function recept(item) {
  consultRecord.value = item;
  show();
}
function read(item) {
  consultRecord.value = item;
  show();
}
onMounted(async () => {
  await getStaffList(true);
  getStaffBelongDept();
  await judgmentIsAdmin();
  await treatmentStatistic();
  getList();
});
// 开单
async function bill() {
  customer.value = {};
  searchShow();
}

// 新增权益
async function addBenefit() {
  customer.value = {};
  searchShow();
}

// 新增权益相关的客户选择处理函数
function openBenefitReception(item) {
  customer.value = item;
  searchClose();
  showBenefitDrawer();
}

// 新增客户处理函数
function onAddCustomerWithBenefit() {
  newCustomerInfo.value = { relationship: "本人" };
  searchClose();
  addCustomerShow();
}

// 新增客户成功后的处理（权益流程）
async function addBenefitCustomerSuccess(id) {
  // 根据id获取数据
  let { success, data } = await getCustomers({ _id: id });
  if (success && data && data.data.length > 0) {
    customer.value = data.data[0];
    showBenefitDrawer();
  }
}

function handleRowClick(row) {
  if (row.visitStatus === "visited") {
    read(row);
  } else if (row.visitStatus === "pending") {
    recept(row);
  }
}
function getStaffBelongDept() {
  belongDeptIds.value = memberStore().memberInfo?.deptIds || [];
}
function getCellClassName({ row, column }) {
  if (orderCount.value[row.treatmentOrderId] > 1 && column.property === "visitStatusStr") {
    return "border-l border-gray-200";
  }
  return "";
}

function showCureDrawer(row) {
  cureDrawerVisible.value = true;
}

function showCureRecord(row) {
  treatmemtItem.value = row;
  cureRecordVisible.value = true;
}

function benefitSuccess() {
  // 权益新增成功后的处理
  ElMessage.success("权益新增成功");
  // 可以根据需要刷新列表或其他操作
  getList();
  treatmentStatistic();
}

// 关闭权益抽屉时清理数据
function closeBenefitDrawerHandler() {
  // 显式设置为false，确保状态正确重置
  benefitDrawerVisible.value = false;
  // 清空客户数据，确保下次打开时数据是干净的
  customer.value = {};
}
</script>
<style lang="scss" scoped></style>