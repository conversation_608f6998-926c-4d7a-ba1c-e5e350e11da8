<template>
  <div v-if="isRead">
    {{ form[item.title] }}
    <span v-if="note && form[item.title]">{{ note }}</span>
  </div>
  <el-input v-else :model-value="form[item.title]" placeholder="请输入" :maxlength="item.wordLimit"
    @update:model-value="change($event)" class="w-250px">
    <template #append>
      <el-select v-model="note" placeholder="备注" class="w-80px" clearable @change="changeNote">
        <el-option v-for="item in mobileRelation" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </template>
  </el-input>
</template>
<script setup>
import { watch, ref, computed } from "vue";
import { mobileRelation } from "@/baseData";
const $emit = defineEmits(["change"]);
const props = defineProps({
  form: {
    type: String,
    default: {},
  },
  item: {
    type: Object,
    default: {},
  },
  value: {
    type: String || Number,
    default: "",
  },
  isRead: {
    type: Boolean,
    default: false,
  },
});

const note = ref("");

function changeNote(val) {
  $emit("change", {
    title: props.item.title,
    value: {
      phone:props.form[props.item.title],
      note: note.value,
    },
  });
}
watch(
  () => props.item.title,
  (item) => {
    note.value = props.form[props.item.title + "Note"] || "";
  },
  { immediate: true }
);

function change(value) {
  $emit("change", {
    title: props.item.title,
    value: {
      phone: value,
      note: note.value,
    },
  });
}
</script>

<style></style>