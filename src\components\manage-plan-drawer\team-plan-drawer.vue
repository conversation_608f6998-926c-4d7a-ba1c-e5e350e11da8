<template>
  <el-drawer :model-value="visible" title="团队回访计划库" size="65%" style="--el-drawer-padding-primary: 0" @close="close()">
    <my-layout v-loading="loading">
      <layout-main v-if="list.length">
        <div v-for="i in list" :key="i._id" class="px-15px py-10px border-b border-gray-200 flex items-center">
          <div class="w-0 flex-grow">
            <div class="flex items-center">
              <div class="max-w-1/2 font-semibold text-16px truncate">{{ i.planName }}</div>
              <div v-if="i.planType === 'corp'" class="flex-shrink-0 mx-15px rounded bg-blue-500 text-white px-10px py-5px text-12px">机构</div>
              <el-button plain round class="ml-5px" type="primary" size="small" @click="showDetail(i)">详情</el-button>
            </div>
            <div class="mt-10px text-14px text-gray-500 truncate">应用范围: {{ i.planDetail || "--" }}</div>
          </div>
          <el-button type="primary" class="flex-shrink-0 w-80px ml-10px" round @click="select(i)">选择</el-button>
        </div>
      </layout-main>
      <layout-item v-if="list.length">
        <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize" @handle-current-change="changePage" />
      </layout-item>
      <layout-main v-else-if="!loading" :scroll="false">
        <div class="flex flex-col items-center justify-center h-full">
          <empty-data :top="0" :title="暂无回访计划" padding="0 0 15px" text-top="10px" :image-width="200"></empty-data>
        </div>
      </layout-main>
    </my-layout>
  </el-drawer>
  <plan-detail  :visible="taskVisible" :managementPlan="managementPlan" @close="taskVisible = false" />
</template>
<script setup>
import { ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { getManagementPlanList } from "@/api/managementPlan";
import useElPagination from "@/hooks/useElPagination";
import { memberStore } from "@/store/member";

import EmptyData from "@/components/empty-data.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import planDetail from "./plan-detail-modal/index.vue";
import { ElMessage } from "element-plus";

const emits = defineEmits(["close", "onSelect"]);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  teamId: {
    type: String,
    default: "",
  },
  type: {
    type: String,
    default: "",
  },
  selectCustomers: {
    type: Array,
    default: () => [],
  },
});

const loading = ref(false);
const managementPlan = ref({});
const taskVisible = ref(false);
const total = ref(0);
const { list, page, pageSize, changePage, changeSize } = useElPagination(getList);
const { memberInfo } = storeToRefs(memberStore());

function close() {
  emits("close");
}
function select(i) {
  emits("onSelect", i);
  close();
}

function showDetail(item) {
  managementPlan.value = item;
  taskVisible.value = true;
}

async function getList() {
  loading.value = true;
  if (!props.teamId) {
    list.value = [];
    total.value = 0;
    loading.value = false;
    return;
  }
  const params = {
    page: page.value,
    pageSize: pageSize.value,
    corpId: memberInfo.value.corpId,
    planType: "team",
    teamId: props.teamId,
    planStatus: true,
    userId: memberInfo.value.userid,
  };

  const { success, data, message } = await getManagementPlanList(params);
  list.value = Array.isArray(data.data) ? data.data : [];
  total.value = data.total;
  if (!success) {
    ElMessage.error(message);
  }
  loading.value = false;
}

watch(
  () => props.visible,
  (n) => {
    if (n) {
      changePage(1);
    }
  }
);
</script>
<style lang="scss" scoped></style>
