<template>
  <el-table-column label="最近到院日期" prop="inhosDate" :width="160">
    <template #default="{ row }">
      <td-wrapper>{{ getTime(row) }}</td-wrapper>
    </template>
  </el-table-column>
  <el-table-column label="到院状态" :width="160">
    <template #default="{ row }">
      <td-wrapper>{{ getTime(row) ? "已到院" : "未到院" }}</td-wrapper>
    </template>
  </el-table-column>
</template>
<script setup>
import dayjs from "dayjs";
import tdWrapper from "./td-wrapper.vue";

function getTime(customer) {
  const { inHospitalTimes } = customer;
  const lastTime = Array.isArray(inHospitalTimes) && inHospitalTimes.length > 0 ? inHospitalTimes[inHospitalTimes.length - 1] : "";
  return lastTime ? dayjs(lastTime).format("YYYY-MM-DD hh:mm") : "";
}
</script>
