<template>
  <my-layout v-loading="loading">
    <layout-item>
      <div class="mt-10px">
        <div class="mb-10px px-15px flex flex-nowrap py-10px items-center bg-white rounded">
          <date-range-filter v-model:dates="dates" label="发起日期" :text="datesText" :width="320" />
          <el-button type="primary" @click="search">查询</el-button>
          <el-button plain type="primary" @click="reset">重置</el-button>
          <div class="flex-grow"></div>
          <el-button type="primary" @click="showViewer=true">群发处理方式</el-button>
        </div>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <div class="h-full flex">
        <div class="w-1/3 min-w-200px max-w-300px flex-shrink-0 h-full">
          <div style="height:calc(100% - 50px)">
            <el-scrollbar v-if="list.length" ref="scrollbarRef">
              <div v-for="i in list" :key="i._id"
                class="bg-white rounded px-15px mb-10px cursor-pointer hover:bg-light-200"
                :class="msg && msg._id === i._id ?'shadow-lg border-blue-500 border-2px':''" @click="toggle(i)">
                <div class="flex items-center justify-between py-10px">
                  <div class="px-10px py-5px bg-orange-400 text-white text-13px rounded max-w-1/2 truncate"
                    :class="i.typeStr ?'':'opacity-0'">{{i.typeStr}}</div>
                  <div v-if="i.statusStr" class="text-13px">{{i.statusStr}}</div>
                </div>
                <div class="line-clamp-2 text-13px leading-20px max-h-40px overflow-hidden">
                  {{ i.content }}
                </div>
                <div class="mt-10px py-10px flex items-center justify-between border-t border-gray-200">
                  <div class="flex-grow w-0 truncate mr-10px text-14px">
                    <el-icon class="transform translate-y-2px">
                      <Avatar />
                    </el-icon>
                    {{ i.customersCount }}
                  </div>
                  <div v-if="i.createTime" class="text-13px text-gray-500">发起时间： {{i.createTime}}</div>
                </div>
              </div>
            </el-scrollbar>
            <div v-else class="h-full flex flex-col items-center justify-center bg-white">
              <empty-data :top="0" title="暂无群发" padding="0 0 15px" text-top="10px" :image-width="100"></empty-data>
            </div>
          </div>
          <div class="p-10px mt-10px flex items-center justify-between h-40px rounded bg-white">
            <div class="text-13px flex-shrink-0 text-gray-500">共{{total}}条</div>
            <el-pagination v-model:current-page="page" :page-size="pageSize" size="small" layout="prev,slot, next"
              pager-count="0" :total="total" @current-change="handleCurrentChange">
              <span>{{page}}</span>
            </el-pagination>
          </div>
        </div>
        <div v-if="msg && msg._id && msg.statusStr === '已发送'" class="flex-grow  ml-10px h-full">
          <group-task-detail :groupmsg="msg" />
        </div>
        <el-scrollbar v-else-if="msg && msg._id " ref="todoScrollbarRef" class="flex-grow h-full ml-10px bg-white">
          <div class="p-15px border-b border-gray-200">
            <div class="text-16px font-semibold">{{msg.typeStr}}</div>
            <div v-if="msg.taskName" class="text-14px mt-15px leading-20px">
              任务内容：{{ msg.taskName }}
            </div>
            <div class="text-14px leading-24px mt-15px bg-gray-100 bg-opacity-80 p-10px">
              <div>向客户发送内容：</div>
              <div>{{ msg.content }}</div>
              <div v-if="msg.attachments.length" color-primary class="flex">
                <div class="flex-shrink-0">附件：</div>
                <div class="w-0 flex-grow break-all">
                  <div v-for="(i,idx) in msg.attachments" :key="idx">
                    {{ i.name }} {{ i.title }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-if="groups.length" class="border-b border-gray-200">
            <div class="p-15px text-16px font-semibold">群发对象</div>
            <el-table stripe :max-height="500" :data="groups" :header-cell-style="{ background: '#ecf4ff' }">
              <el-table-column align="center" property="name" label="客户名称">
                <template #default="{row}">
                  <div class="flex w-full">
                    <div v-for="i in row" :key="i.id" color-primary
                      class="w-1/3 flex-shrink-0 px-10px truncate cursor-pointer" @click="toggleCustomer(i)">
                      {{i.name }}
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div
            v-if="isAdmin &&msg.executeStatus === 'doing' &&  (msg.createSounrce === 'TEAM' || msg.createSounrce=='CORP') "
            class="py-10px text-center">
            <el-button plain class="w-100px" type="primary" @click="stopTask">停用群发任务</el-button>
          </div>
        </el-scrollbar>

        <div v-else class="flex-grow flex items-center justify-center h-full">
          <empty-data :top="0" title="请选择群发" padding="0 0 15px" text-top="10px" :image-width="200"></empty-data>
        </div>
      </div>
    </layout-main>
  </my-layout>
  <el-image-viewer v-if="showViewer" :url-list="[pic]" @close="() => { showViewer = false }" />
  <customer-detail :visible="customerDetailVisible" @close="customerDetailVisible = false"
    :customerId="currentCustomer._id" title="客户详情" customerType="corpCustomer" />
</template>
<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { storeToRefs } from "pinia";
import { ElMessage, ElMessageBox } from 'element-plus';
import dayjs from 'dayjs';
import { getWecomGroupmesgListUrl, stopGroupmsgTaskUrl } from "@/api/groupmsg";
import { getCustomerNamesById } from "@/api/member";

import { memberStore } from "@/store/member";
import { teamStore } from "@/store/team";

import { DateRangeFilter } from '@/components/filter-bar';
import EmptyData from "@/components/empty-data.vue";
import GroupTaskDetail from './group-task-detail.vue';
import MyLayout, { LayoutMain } from "@/components/layout";
import customerDetail from "@/views/member/detail/index.vue";

const pic = new URL("@/assets/group-msg.png", import.meta.url).href; // 默认logo

const props = defineProps({
  showFilter: { type: Boolean, default: false },
})

const sourceMap = {
  MINE: '我的群发',
  TEAM: '团队群发',
  CORP: '机构群发',
}
const { corpInfo, memberInfo } = storeToRefs(memberStore());
const { isAdmin } = storeToRefs(teamStore());

const dates = ref([]);
const list = ref([]);
const loading = ref(false);
const pageSize = 15;
const msg = ref({});
const page = ref(1);
let payload = {};
const showViewer = ref(false);
const total = ref(0);
const datesText = computed(() => dates.value.join(' ~ '));
const customerIds = computed(() => msg.value && msg.value.customers ? msg.value.customers : '')
const customerDetailVisible = ref(false);
const currentCustomer = ref({});
const customers = ref([])
const groups = computed(() => {
  return customers.value.reduce((acc, _, i) => {
    if (i % 3 === 0) {
      acc.push(customers.value.slice(i, i + 3));
    }
    return acc;
  }, []).filter(i => i.length > 0);
})

function format(list) {
  return list.map(i => {
    const statusStr = i.executeStatus === "end" ? "已结束" : i.taskStatus === "closed" ? "已取消" : i.unexecutedCount > 0 ? "未发送" : "已发送";
    const typeStr = sourceMap[i.createSounrce] || '';
    const createTime = i.createTime && dayjs(i.createTime).isValid() ? dayjs(i.createTime).format('YYYY-MM-DD HH:mm').replace('00:00','') : '';
    const attachments = Array.isArray(i.attachments) ? i.attachments.map(item => {
      let name = ''
      if (item.msgtype === "link" && item.linkType === "article") name = "【文章】";
      else if (item.msgtype === "link") name = "【网页】";
      else if (item.msgtype === "image") name = "【图片】";
      else if (item.msgtype === "video") name = "【视频】";
      return { ...item, name }
    }) : [];
    return { ...i, statusStr, typeStr, createTime, attachments }
  })
}

function handleCurrentChange(val) {
  page.value = val;
  getList();
}

async function stopTask() {
  await ElMessageBox.confirm("确定停止吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  });
  const { success, message } = await stopGroupmsgTaskUrl(msg.value._id);
  if (success) {
    ElMessage.success("停止成功");
    getList()
  } else {
    ElMessage.error(message);
  }
}

function search() {
  const query = {}
  if (dates.value.length) {
    query.startCreateTime = dayjs(dates.value[0]).startOf('day').valueOf()
    query.endCreateTime = dayjs(dates.value[1]).endOf('day').valueOf()
  }
  payload = { ...query };
  page.value = 1;
  getList()
}

function reset() {
  dates.value = [];
  search()
}

function toggle(i) {
  msg.value = { ...i }
}

function toggleCustomer(i) {
  currentCustomer.value = i
  customerDetailVisible.value = true
}

async function getList() {
  loading.value = true;
  const { corpId, permanent_code } = corpInfo.value;
  const { success, data, message } = await getWecomGroupmesgListUrl(corpId, permanent_code, page.value, pageSize, {
    sendSource: 'MINE',
    executor: memberInfo.value.userid,
    executeStatus: 'doing',
    orderType:'asc',
    ...payload
  });
  if (success) {
    list.value = format(data.data);
    total.value = data.total;
  } else {
    ElMessage.error(message);
  }
  loading.value = false;
  msg.value = list.value.find(i => msg.value && i._id === msg.value._id) || list.value[0];
}

async function getCustomers() {
  const { data } = await getCustomerNamesById({ ids: customerIds.value })
  customers.value = data && Array.isArray(data.data) ? data.data : []
}

onMounted(() => getList())

watch(customerIds, n => {
  if (Array.isArray(n) && n.length) getCustomers(n)
  else {
    customers.value = []
  }
})
</script>
<style lang="scss" scoped></style>
