<template>
  <div v-if="isRead">
    {{ value }}
  </div>
  <div class="flex ml-10px" v-else>
    <el-input v-model="year" @update:model-value="change($event, 'year')" class="w-40px" type="number"></el-input>
    <div class="mx-10px">年</div>
    <el-input v-model="month" @update:model-value="change($event, 'month')" class="w-40px" type="number"></el-input>
    <div class="mx-10px">月</div>
    <el-input v-model="day" @update:model-value="change($event, 'day')" class="w-40px" type="number"></el-input>
    <div class="mx-10px">日</div>
  </div>
</template>
  <script setup>
import { watch, ref, computed } from "vue";

const $emit = defineEmits(["change"]);
const year = ref(null);
const month = ref(null);
const day = ref(null);
let str = "";
const props = defineProps({
  item: {
    type: Object,
    default: {},
  },
  value: {
    type: String || Number,
    default: "",
  },
  isRead: {
    type: Boolean,
    default: false,
  },
});

watch(
  () => props.value,
  (val) => {
    if (!val) return;
    const arr = extractNumbers(val);
    const yearMatch = val.match(/(\d+)年/);
    const monthMatch = val.match(/(\d+)月/);
    const dayMatch = val.match(/(\d+)日/);
    year.value = yearMatch ? Number(yearMatch[1]) : "";
    month.value = monthMatch ? Number(monthMatch[1]) : "";
    day.value = dayMatch ? Number(dayMatch[1]) : "";
  },
  { immediate: true }
);
function extractNumbers(dateString) {
  const numbers = dateString.match(/\d+/g);
  return numbers.map(Number);
}

function change() {
  const yearStr = year.value && year.value !== "0" ? `${year.value}年` : "";
  const monthStr = month.value && month.value !== "0" ? `${month.value}月` : "";
  const dayStr = day.value && day.value !== "0" ? `${day.value}日` : "";
  $emit("change", {
    title: props.item.title,
    value: `${yearStr}${monthStr}${dayStr}`,
  });
}
</script>
  
  <style></style>