<template>
  <div v-if="visible" v-loading="loading" class="h-screen w-screen fixed inset-0 z-99">
    <page-wrapper @back="back()">
      <!-- 第一步 -->
      <template v-if="step === 0">
        <div class="shadow-sm">
          <div font-16 font-semibold px-15 border-bottom class="py-12px">基础信息</div>
          <form-template ref="baseRef" :items="basicTempList" :rule="rule" :form="basicForm" @change="change($event, basicForm)" />
        </div>
      </template>
      <!-- 第二步 -->
      <template v-else-if="step === 1">
        <div class="shadow-lg">
          <div font-16 font-semibold px-15 border-bottom class="py-12px">内部信息</div>
          <form-template ref="innerRef" :items="innerTempList" :form="innerForm" @change="change($event, innerForm)" />
        </div>
      </template>
      <template #footer>
        <div v-if="step === 0" class="relative z-2" bg-fff flex items-center px-15 py-10 common-shadow--r>
          <el-button plain class="flex-grow mr-15px" @click="close()">取消</el-button>
          <el-button class="flex-grow" type="primary" @click="next()">下一步</el-button>
        </div>
        <div v-else-if="step === 1" class="relative z-2" bg-fff flex items-center px-15 py-10 common-shadow--r>
          <el-button plain class="flex-grow mr-15px" @click="step = 0">上一步</el-button>
          <el-button :loading="saveLoading" class="flex-grow" type="primary" @click="save()">保存</el-button>
        </div>
      </template>
    </page-wrapper>
    <customer-repeat :visible="repeatVisible" :customer="customer" :width="300" @acceptCustomer="acceptCustomer" :repeatType="repeatType" @close="repeatVisible = false"></customer-repeat>
    <mobile-repeat :width="300" :visible="mobileRepeatVisible" :customers="mobileReatCustomers" @close="mobileRepeatVisible = false"></mobile-repeat>
  </div>
</template>
<script setup>
import { ref, onMounted, toRefs, watch, computed } from "vue";
import { storeToRefs } from "pinia";
import { ElMessage, ElMessageBox } from "element-plus";
import { addMember, updateMember } from "@/api/member";
import { memberStore } from "@/store/member";
import { templateStore } from "@/store/template";
import customerRepeat from "@/views/member/components/customer-repeat/index";
import repeatComponentApi from "@/views/member/components/customer-repeat/api";
import mobileRepeat from "@/components/mobile-repeat/index.vue";
import mobileRepeatApi from "@/components/mobile-repeat/api";
import validate from "@/utils/validate";
import { checkInteger, checkNumber } from "../form-template/edit/verify";
import formTemplate from "../form-template/edit/index.vue";
import pageWrapper from "../../components/page-wrapper.vue";

const { repeatVisible, customer, getReatCustomer, repeatType } = repeatComponentApi();
const { mobileReatCustomers, mobileRepeatVisible, getMobileReatCustomer } = mobileRepeatApi();
const { corpInfo } = storeToRefs(memberStore());
const emits = defineEmits(["close", "created"]);
const props = defineProps({
  unionId: { type: String, default: "" },
  visible: { type: Boolean, default: false },
  externalUserId: { type: String, default: "" },
  team: { type: Object, default: () => ({}) },
});

const { visible } = toRefs(props);
const store = templateStore();

const { corpTemplateList, loading } = storeToRefs(store);
const { getCorpTemplate } = store;
const basicTempList = computed(() => getTemplate("baseTemplate"));
const innerTempList = computed(() => getTemplate("internalTemplate"));
const rule = {
  age(value, name) {
    const res = checkInteger(value, 1, 120);
    if (res === true) return true;
    return `请输入有效的${name}`;
  },
  height(value, name) {
    const res = checkNumber(value, 20, 250);
    if (res === true) return true;
    else if (typeof res === "string") return `${name}: ${res}`;
    return `请输入有效的${name}`;
  },
  telphone(val, name) {
    if (val && !/^\d{3,4}-\d{6,8}$/.test(val)) {
      return `请输入正确的${name}`;
    }
    return true;
  },
  weight(value, name) {
    const res = checkNumber(value, 5, 200);
    if (res === true) return true;
    else if (typeof res === "string") return `${name}: ${res}`;
    return `请输入有效的${name}`;
  },
};
function getTemplate(templateType) {
  const temp = Array.isArray(corpTemplateList.value) ? corpTemplateList.value.find((i) => i.templateType === templateType) : [];
  const tempList = temp && Array.isArray(temp.templateList) ? temp.templateList.filter((i) => i.operateType !== "onlyRead" && !["disable", "disabled"].includes(i.fieldStatus)) : [];
  return tempList;
}

const basicForm = ref({
  cardType: "身份证",
  relationship: "本人",
});
const innerForm = ref({});
const baseRef = ref();
const healthRef = ref();
const innerRef = ref();
const step = ref(0);
const defaultValues = {
  relationship: "本人",
  cardType: "身份证",
};

onMounted(async () => {
  if (!Array.isArray(corpTemplateList.value) || corpTemplateList.value.length === 0) await getCorpTemplate();
});

function back() {
  if (step.value === 1) step.value = 0;
  else close();
}
function next() {
  if (!baseRef.value.verify()) return;
  if (healthRef.value && !healthRef.value.verify()) return;
  step.value = 1;
}
watch(visible, (n) => {
  if (n) {
    console.log("========>", props.team.teamId);
    basicForm.value = {
      cardType: "身份证",
      relationship: "本人",
    };
    innerForm.value = {};
    customer.value = {};
    repeatVisible.value = false;
    step.value = 0;
    basicTempList.value.forEach((i) => {
      if (i.title && defaultValues[i.title]) basicForm.value[i.title] = defaultValues[i.title];
    });
  }
});

function change({ title, value }, form) {
  form[title] = value;
  if (title === "idCard" && validate.isChinaId(value)[0]) handleIdCardChange(value, title);
  if (title === "mobile" && validate.isMobile(value)) getMobileReatCustomer(value);
  if (title === "customerNumber" && corpInfo.value.isConnectHis) getReatCustomer(value, title);
}

function close() {
  emits("close");
}

const saveLoading = ref(false);
async function save() {
  if (saveLoading.value) return;
  saveLoading.value = true;
  let params = {
    ...basicForm.value,
    ...innerForm.value,
  };
  let query = {};
  // 更新操作
  if (customer.value && customer.value._id) {
    if (typeof customer.value.teamId === "string") customer.value.teamId = customer.value.teamId ? [customer.value.teamId] : [];
    if (!customer.value.personResponsibles) customer.value.personResponsibles = [];
    if (!customer.value.teamId.includes(props.team.teamId)) {
      query.teamId = [...customer.value.teamId, props.team.teamId];
    }
    let contains = customer.value.personResponsibles.some((item) => item.corpUserId === localStorage.getItem("userId") && item.teamId === props.team.teamId);
    if (!contains) {
      query.personResponsibles = [
        ...customer.value.personResponsibles,
        {
          corpUserId: localStorage.getItem("userId"),
          teamId: props.team.teamId,
        },
      ];
    }
    if (!customer.value.externalUserId) query.externalUserId = props.externalUserId;
    if (!customer.value.unionid) query.unionid = props.unionId;
    if (customer.value.isConnectHis) query.isConnectHis = true;
  } else {
    query = {
      teamId: [props.team.teamId],
      corpUserId: [localStorage.getItem("userId")],
      creator: localStorage.getItem("userId"),
      corpId: localStorage.getItem("corpId"),
      externalUserId: props.externalUserId,
      unionid: props.unionId,
      personResponsibles: [
        {
          corpUserId: localStorage.getItem("userId"),
          teamId: props.team.teamId,
        },
      ],
    };
  }
  params = { ...params, ...query };
  if (customer.value && customer.value._id) {
    const { success, message = "更新客户失败" } = await updateMember(customer.value._id, params);
    if (success) {
      ElMessage.success("更新客户信息成功");
      emits("created");
      emits("close");
    } else {
      ElMessage.error(message);
    }
  } else {
    const { success, message = "新增客户失败", data } = await addMember(params);
    if (success) {
      ElMessage.success("新增客户信息成功");
      // createServiceRecordAction(params, data.data.id);
      emits("created", data.data ? data.data.id : "");
      emits("close");
    } else {
      ElMessage.error(message);
    }
  }
  saveLoading.value = false;
}

// function createServiceRecordAction(params, customerId) {
//   if (!customerId) return;
//   const { anotherName } = memberInfo.value;
//   let time = dayjs().format("YYYY-MM-DD HH:mm");
//   let item = {
//     taskContent: `${anotherName}为客户${params.name}新增一份档案，建档时间为${time}`,
//     executionTime: new Date().getTime(),
//     customerId,
//     executeTeamId: params.teamId,
//     teamName: props.team.name,
//     eventType: "addCustomerRrofile",
//     customerName: params.name,
//   };
//   createServiceRecord(item);
// }
async function handleIdCardChange(value, title) {
  const list = [...basicTempList.value];
  if (list.some((item) => item.title === "cardType")) {
    const item = list.find((item) => item.title === "cardType");
    if (item && Array.isArray(item.range) && item.range.some((i) => i === "身份证")) {
      basicForm.value["cardType"] = "身份证";
    }
  }
  if (list.some((item) => item.title === "birthday")) {
    basicForm.value["birthday"] = getBirthdayFromIdCard(value);
  }
  if (list.some((item) => item.title === "sex")) {
    basicForm.value["sex"] = getGenderFromIdCard(value);
  }
  if (list.some((item) => item.title === "age")) {
    basicForm.value["age"] = getAgeFromBirthday(getBirthdayFromIdCard(value));
  }
  await getReatCustomer(value, title);
}

// 获取重复的档案数据
function acceptCustomer() {
  repeatVisible.value = false;
  if (customer.value.externalUserId && customer.value.externalUserId !== props.externalUserId) {
    ElMessageBox.alert("该档案已经绑定微信联系人，不允许绑定在当前微信联系人下。");
    basicForm.value.idCard = "";
    basicForm.value.cardType = "";
    basicForm.value.sex = "";
    basicForm.value.age = "";
    return;
  }
  const basickeys = basicTempList.value.map((item) => item.title);
  const innerkeys = innerTempList.value.map((item) => item.title);
  // 获取customer.value 中匹配 basickeys 的值
  basickeys.forEach((key) => {
    if (customer.value[key]) basicForm.value[key] = customer.value[key];
  });
  innerkeys.forEach((key) => {
    if (customer.value[key]) innerForm.value[key] = customer.value[key];
  });
}

// 从身份中截取生日信息 返回形式是YYYY-MM-DD
function getBirthdayFromIdCard(idCard) {
  return `${idCard.substring(6, 10)}-${idCard.substring(10, 12)}-${idCard.substring(12, 14)}`;
}

// 从身份证中截取性别信息 返回形式是男或女
function getGenderFromIdCard(idCard) {
  const gender = idCard.substring(16, 17) % 2 === 0 ? "女" : "男";
  return gender;
}

// 输入一个生日字符串，返回年龄
function getAgeFromBirthday(birthday) {
  const today = new Date();
  const birthDate = new Date(birthday);
  let age = today.getFullYear() - birthDate.getFullYear();
  const m = today.getMonth() - birthDate.getMonth();
  if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return Math.max(age, 1);
}
</script>
<style>
.create-customer .el-form-item__label {
  height: auto;
  font-size: 14px;
  padding-top: 9px;
  padding-bottom: 9px;
  line-height: 18px;
}
</style>
