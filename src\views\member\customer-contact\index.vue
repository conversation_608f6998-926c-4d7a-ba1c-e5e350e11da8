<template>
  <my-layout bg-fff common-shadow>
    <layout-item>
      <div flex items justify-between p-15>
        <div flex items-center>
          <date-range-filter v-model:dates="dates" label="时间" :text="datesText" :width="320" />
          <div class="flex align-center">
            <div class="flex-shrink-0 mr-10px text-gray-500 text-14px">客户联系人:</div>
            <el-input placeholder="输入客户联系人" v-model="searchName" />
            <el-button type="primary" @click="search()" plain class="ml-10px">查询</el-button>
            <el-button type="primary" @click="reset()" plain class="ml-10px">重置</el-button>
          </div>
        </div>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <el-table height="100%" :data="wechatList" v-loading="loading" :header-cell-style="{ background: '#ecf4ff' }" border :highlight-current-row="false" :cell-style="{ background: 'white' }">
        <el-table-column property="name" label="微信好友" :width="200">
          <template #default="{ row }">
            <div class="flex items-center cursor-pointer pl-10px text-15px" @click="chat(row)">
              <div class="truncate flex-shrink">{{ row.name }}</div>
              <div class="flex-shrink-0 mx-5px text-12px text-[#66CB70]">@微信</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column property="source" label="来源" :width="120" />
        <el-table-column property="addTime" label="添加日期" :width="200"></el-table-column>
        <el-table-column property="customerInfo" label="关联档案">
          <template #default="{ row }">
            <div v-if="row.customer.length > 0" class="flex items-center flex-wrap">
              <div v-for="i in row.customer" class="w-1/3 text-14px py-5px" @click="toCustomerDetail(i)">
                <div color-primary class="inline-block max-w-7/10 leading-20px truncate cursor-pointer">
                  {{ i.name }}
                </div>
                <div v-if="stageMap[i.customerStage]" :class="[stageMap[i.customerStage].bg, stageMap[i.customerStage].text]" class="inline-block px-10px py-4px ml-5px text-12px rounded max-w-3/10 truncate">
                  {{ stageMap[i.customerStage].label }}
                </div>
              </div>
            </div>
            <div v-else>--</div>
          </template>
        </el-table-column>
      </el-table>
    </layout-main>
    <layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="currentPage" @handle-size-change="onSizeChange" @handle-current-change="onCurrentChange" />
    </layout-item>
  </my-layout>
  <customer-detail :visible="customerDetailVisible" @close="customerDetailVisible = false" :customerId="customerId" title="客户详情" customerType="corpCustomer" />
</template>
<script setup>
import { computed, onMounted, ref, watch } from "vue";
import { memberStore } from "@/store/member";
import { storeToRefs } from "pinia";
import { getWechatFriends } from "@/api/corp";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import { dayjs } from "element-plus";
import { DateRangeFilter } from "@/components/filter-bar";
import customerDetail from "@/views/member/detail/index.vue";
import { configStore } from "@/store/config";
import { openChatWindow } from "@/utils/common";
import { useRoute } from "vue-router";
import addWay from "./addWay";
const colors = [
  { bg: "bg-orange-100", text: "text-orange-500" },
  { bg: "bg-yellow-100", text: "text-yellow-500" },
  { bg: "bg-blue-100", text: "text-blue-500" },
  { bg: "bg-green-100", text: "text-green-500" },
  { bg: "bg-red-100", text: "text-red-500" },
  { bg: "bg-gray-100", text: "text-gray-500" },
];
const cStore = configStore();
const { getStage } = cStore;
const { stageList } = storeToRefs(cStore);
const route = useRoute();
const searchName = ref("");
const pageSize = ref(10);
const currentPage = ref(1);
const dates = ref([]);
const customerDetailVisible = ref(false);
const customerId = ref("");
const total = ref(0);
const datesText = computed(() => dates.value.join(" ~ "));
const stageMap = computed(() =>
  stageList.value.reduce((acc, cur, idx) => {
    const colorConfig = colors[idx] || colors[colors.length - 1];
    acc[cur.value] = { ...cur, bg: colorConfig.bg, text: colorConfig.text };
    return acc;
  }, {})
);
onMounted(async () => {
  if (stageList.value.length == 0) getStage();
  if (route.params.time === "today") dates.value = [dayjs().format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")];
  getWechatFriendList();
});
const loading = ref(false);
const wechatList = ref([]);

function onSizeChange(e) {
  currentPage.value = 1;
  pageSize.value = e;
  getWechatFriendList();
}
function onCurrentChange(e) {
  currentPage.value = e;
  getWechatFriendList();
}
function reset() {
  searchName.value = "";
  currentPage.value = 1;
  dates.value = [];
  getWechatFriendList();
}

function search() {
  currentPage.value = 1;
  getWechatFriendList();
}

async function getWechatFriendList() {
  const query = {
    userId: localStorage.getItem("userId"),
    page: currentPage.value,
    pageSize: pageSize.value,
    dates: dates.value,
    searchName: searchName.value,
    haveCustomer: true,
  };
  let { data, success } = await getWechatFriends(query);
  if (success) {
    const customers = data.customers && Array.isArray(data.customers) ? data.customers : [];
    const list = data.list && Array.isArray(data.list) ? data.list : [];
    wechatList.value = list.map((i) => ({
      ...i,
      source: addWay[i.addWay],
      addTime: i.createtime ? dayjs.unix(i.createtime).format("YYYY-MM-DD") : "",
      customer: customers.filter((item) => i.external_userid === item.externalUserId),
    }));
    total.value = data.total;
  }
}

function toCustomerDetail(item) {
  customerId.value = item._id;
  customerDetailVisible.value = true;
}
function chat(row) {
  openChatWindow(customerId.value, row.external_userid, row.userid);
}
</script>

<style scoped>
.table-action-btn {
  padding-left: 0;
  padding-right: 0;
}

.info-table {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.customer-bottom:not(:last-child) {
  border-bottom: 1px solid #eee;
}
</style>