<template>
  <my-layout bg-fff height="100%">
    <layout-main class="h-300px y-auto">
      <div class="el-layout">
        <el-table stripe :data="list" v-loading="loading">   
          <el-table-column label="员工姓名">
            <template #default="{ row }">
              <ww-user :openid="row.userId"></ww-user>
            </template>
          </el-table-column>
          <el-table-column
            min-width="100"
            property="distributedTaskCount"
            label="已接收任务数"
          />
          <el-table-column
            property="executedTaskCount"
            label="已执行任务数"
            min-width="100"
          />
          <el-table-column
            property="feedbackTaskCount"
            label="已反馈任务数"
            min-width="100"
          />
          <el-table-column
            property="executedTaskCompletionRate"
            label="任务执行完成率"
            min-width="100"
          />
          <el-table-column
            property="feedbackTaskCompletionRate"
            label="任务信息完成率"
            min-width="100"
          />
        </el-table>
      </div>
    </layout-main>
  </my-layout>
</template>
<script setup>
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import WwUser from "@/components/ww-user/index.vue";
import { ref, computed } from "vue";
const props = defineProps({
  detial: { type: Object, default: {} },
  pannedEventResult: { type: Object, default: {} },
});

const list = computed(() => {
  const { toDoEvents } = props.pannedEventResult;
  let list = toDoEvents.filter((item) => item.executorUserId);
  let mergeArr = mergeArray(list);
  let arr = mergeArr.map((item) => {
    let toDoEvents = JSON.parse(JSON.stringify(item.list)) ;
    const distributedTaskCount = toDoEvents.length;
    // 已执行任务数
    const executedTaskCount = toDoEvents.filter((item) => {
      return item.eventStatus === "treated";
    }).length;
    // 已反馈任务数
    const feedbackTaskCount = toDoEvents.filter((item) => {
      return item["isFeedback"];
    }).length;
    let executedTaskCompletionRate =
      (distributedTaskCount == 0 || executedTaskCount == 0)
        ?  "0.0" + "%" 
        : (executedTaskCount / distributedTaskCount).toFixed(1) * 100 + "%" ;
    let feedbackTaskCompletionRate =
      (distributedTaskCount == 0 || feedbackTaskCount == 0)
        ? "0.0" + "%" 
        : (feedbackTaskCount / distributedTaskCount).toFixed(1) * 100 + "%";
    return {
      userId: item.name,
      distributedTaskCount,
      executedTaskCount,
      feedbackTaskCount,
      executedTaskCompletionRate,
      feedbackTaskCompletionRate,
    };
  });
  return arr;
});

function mergeArray(arr) {
  let obj = arr.reduce((acc, curr) => {
    let userId = curr.executorUserId;
    if (!acc[userId]) {
      acc[userId] = [];
    }
    acc[userId].push(curr);
    return acc;
  }, {});
  let list = [];
  for (let key in obj) {
    let item = {
      name: key,
      list: obj[key],
    };
    list.push(item);
  }
  return list;
}
</script>
<style scoped lang="scss">
.sub-title {
  font-size: 14px;
  color: #006eff;
  padding-top: 5px;
}
</style>
