import { post } from "./axios";
async function useMember(data) {
  const res = await post("member", data, "MEMBER");
  return res;
}

export async function addBillRecord(data) {
  const res = await useMember({ type: "addBillRecord", ...data });
  return res;
}

export async function getBillRecord(data) {
  const res = await useMember({ type: "getBillRecord", ...data });
  return res;
}

export async function updateFeeRecord(data) {
  const res = await useMember({ type: "updateFeeRecord", ...data });
  return res;
}

export async function addFeeRecord(data) {
  const res = await useMember({ type: "addFeeRecord", ...data });
  return res;
}

export async function batchAddFeeRecord(data) {
  const res = await useMember({ type: "batchAddFeeRecord", ...data });
  return res;
}

export async function getConsultBillRecord(data) {
  const res = await useMember({ type: "getConsultBillRecord", ...data });
  return res;
}
export async function updateBillRecord(data) {
  const res = await useMember({ type: "updateBillRecord", ...data });
  return res;
}

export async function voidIssuedBill(data) {
  const res = await useMember({ type: "voidIssuedBill", ...data });
  return res;
}

export async function getDoctorPerformance(data) {
  const res = await useMember({ type: "getDoctorPerformance", ...data });
  return res;
}

export async function getConsultantStatistics(data) {
  const res = await useMember({ type: "getConsultantStatistics", ...data });
  return res;
}

export async function getConsultantSalesRanking(data) {
  const res = await useMember({ type: "getConsultantSalesRanking", ...data });
  return res;
}

export async function getBillRecordsWithMemberInfo(data) {
  const res = await useMember({ type: "getBillRecordsWithMemberInfo", ...data });
  return res;
}

export async function getBillRecordsWithMemberDetails(data) {
  const res = await useMember({ type: "getBillRecordsWithMemberDetails", ...data });
  return res;
}

export async function getEConsultProjectStatistics(data) {
  const res = await useMember({ type: "getEConsultProjectStatistics", ...data });
  return res;
}


