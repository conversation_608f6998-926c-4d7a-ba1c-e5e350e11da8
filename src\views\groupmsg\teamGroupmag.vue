<template>
    <my-layout bg-fff>
      <layout-item mb-10>
        <div flex justify-between items-center p-15>
          <el-input class="w-180px" placeholder="请输入计划事项名称" v-model="pannedEvent" @keyup.enter="search"
            @input="searchInput" />
          <el-button ml-10 type="primary" @click="search()">查询</el-button>
          <div flex-grow></div>
          <el-button type="primary" @click="addTeam()">新增</el-button>
        </div>
      </layout-item>
      <layout-main>
        <div class="el-layout">
          <el-table stripe height="100%" :data="list" v-loading="loading">
            <el-table-column property="pannedEventName" label="计划事项名称" width="130px" />
            <el-table-column property="member" label="目标客户" v-if="isShowCell('tagIds')">
              <template #default="{ row }">
                <el-tag v-for="(item, idx) in row.tagsName" :key="idx" class="mr-3 linear-tag mb-3px" type="primary"
                  effect="dark">
                  <el-icon>
                    <Check />
                  </el-icon>
                  {{ item }}
                </el-tag>
                <el-tag v-for="(item, idx) in row.excludeTagsName" :key="idx" class="mr-3 linear-tag mb-3px" type="primary"
                  effect="dark">
                  <el-icon>
                    <Close />
                  </el-icon>
                  {{ item }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column property="customerType" label="客户所属阶段" width="120">
              <template #default="{ row }">
                {{ customerTypeObj[row.customerType] }}
              </template>
            </el-table-column>
            <el-table-column property="executionSatus" label="状态" width="80">
              <template #default="{ row }">
                {{ row.executionSatus ? "启用" : "停用" }}
              </template>
            </el-table-column>
            <el-table-column property="teamTroduce" label="已下发任务数" width="120">
              <template #default="{ row }">
                {{ row.customersTotal }}
              </template>
            </el-table-column>
            <el-table-column property="teamTroduce" label="所属团队" width="130">
              <template #default="{ row }">
                {{ row.teamName || "" }}
              </template>
            </el-table-column>
            <el-table-column property="teamTroduce" label="创建人" width="120">
              <template #default="{ row }">
                <ww-user :openid="row.pannedCreateMember"></ww-user>
              </template>
            </el-table-column>
            <el-table-column property="teamTroduce" label="创建时间" width="150">
              <template #default="{ row }">
                {{
                  row.createTime &&
                  dayjs(row.createTime).format("YYYY-MM-DD HH:mm")
                }}
              </template>
            </el-table-column>
            <el-table-column fixed="right" class-name="el-table-fixed-column--shadow" label="操作" width="220">
              <template #default="{ row }">
                <el-button text class="no-ml table-action-btn" type="primary" size="small"
                  @click="showDetail(row)">详情</el-button>
                <el-button text class="table-action-btn" type="primary" size="small" style="margin-left: 0;"
                  @click="EditDetail(row)">编辑</el-button>
                <el-button text class="no-ml table-action-btn" type="primary" size="small" @click="editExecutionSatus(row)"
                  style="margin-left: 0">
                  {{ row.executionSatus ? "停用" : "启用" }}
                </el-button>
                <el-button text class="no-ml table-action-btn" type="danger" size="small" @click="remove(row)"
                  style="margin-left: 0" v-if="!row.executionSatus">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </layout-main>
    </my-layout>
  </template>
  <script setup>
  import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
  import { useRouter, useRoute } from "vue-router";
  import { teamStore } from "@/store/team";
  import {
    getPannedEvent,
    updatePannedEvent,
    removePannedEvent,
    getCustomerType
  } from "@/api/member";
  import { templateStore } from "@/store/template";
  import { ref , watch} from "vue";
  import { storeToRefs } from "pinia";
  import WwUser from "@/components/ww-user/index.vue";
  import { ElMessageBox } from "element-plus";
  import dayjs from "dayjs";
  const { isShowCell } = templateStore();
  const list = ref([]);
  const loading = ref(false);
  const router = useRouter();
  const pannedEvent = ref("");
  const { currentTeam } = storeToRefs(teamStore());
  watch(currentTeam, (newItem, oldItem) => {
    if (newItem && oldItem &&  newItem.teamId !== oldItem.teamId) {
      getPannedEventAction();
    }
  });
  getPannedEventAction();
  function addTeam() {
    router.push({ name: "PANNEDEVENTDETIAL" });
  }
  async function getPannedEventAction() {
    const params = {
      userId: localStorage.getItem("userId"),
      corpId: localStorage.getItem("corpId"),
    };
    await getData(params);
  }
  async function editExecutionSatus(res) {
    const { executionSatus, _id } = res;
    const title = executionSatus
      ? "计划事项停用后，未执行客户对应的计划事项任务取消，后续符合执行条件的客户不再加入此计划事项进行管理"
      : "是否启用计划事项";
    ElMessageBox.confirm(title).then(async () => {
      const params = {
        executionSatus: !executionSatus,
      };
      await updatePannedEvent(_id, params);
      getPannedEventAction();
    });
  }
  async function remove(res) {
    const { _id } = res;
    ElMessageBox.confirm("是否删除?").then(async () => {
      await removePannedEvent(_id);
      getPannedEventAction();
    });
  }
  
  async function showDetail(res) {
    const { _id } = res;
    router.push({ name: "PANNEDEVENTINFORMATION", params: { id: _id } });
  }
  
  async function EditDetail(res) {
    const { _id } = res;
    router.push({ name: "PANNEDEVENTDETIAL", params: { id: _id } });
  }
  
  // 搜索
  async function search() {
    const params = {
      userId: localStorage.getItem("userId"),
      corpId: localStorage.getItem("corpId"),
    };
    if (pannedEvent.value) {
      params["pannedEventName"] = pannedEvent.value;
    }
    await getData(params);
  }
  
  async function getData(params) {
    loading.value = true;
    const { data, success } = await getPannedEvent(params);
    loading.value = false;
    if (success) {
      list.value = data.data;
    }
  }
  
  async function searchInput() {
    if (!pannedEvent.value) {
      const params = {
        userId: localStorage.getItem("userId"),
        corpId: localStorage.getItem("corpId"),
      };
      await getData(params);
    }
  }
  
  // 获取客户阶段
  const customerTypeObj = ref({});
  customerTypeList();
  async function customerTypeList() {
    let { data, success } = await getCustomerType();
    if (success) {
      const list = data.data;
      list.forEach((item) => {
        customerTypeObj.value[item.type] = item.name;
      });
    }
  }
  
  </script>
  <style scoped></style>
  