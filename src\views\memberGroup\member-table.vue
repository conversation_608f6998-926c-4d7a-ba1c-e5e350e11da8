<template>
  <div class="flex flex-col h-full">
    <div common-shadow class="flex-shrink-0 mb-10px rounded bg-white">
      <customer-filter ref="filterRef" :layout="teamCustomerLayout" @search="filterSearch($event)" />
    </div>
    <div class="flex-grow relative bg-white">
      <div class="absolute inset-0">
        <customer-table ref="teamCustomerTableRef" v-model:loading="loading" :columns="teamCustomerColumns" :params="teamCustomerParams" :actionColumnWidth="140" customerType="corpCustomer">
          <template #fixedRightColumn="{ row }">
            <el-dropdown class="switch-el-dropdown" trigger="click">
              <el-text pointer type="primary" class="mr-10px mt-4px" @click="selectGroup(row)">进组</el-text>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-for="item in groupNewList" :key="item._id" @click="transferGroup(row, item)">
                    <span class="leading-20px">{{ item.label }}</span>
                    <span v-if="item.showCorpTag" class="leading-20px bg-blue-500 text-12px text-white ml-5px rounded px-5px">机构</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <el-text v-if="group && group.groupType === 'team' && group.teamId" pointer type="primary" @click="showPlan(row)">回访计划</el-text>
          </template>
        </customer-table>
      </div>
    </div>
    <plan-modal :visible="visible" @close="visible = false" @executePlan="addPlan" :customer="customer" />
  </div>
</template>
<script setup>
import { computed, nextTick, onMounted, ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { addGroupIdForMember as addGroupIdForMemberUrl } from "@/api/group";
import dayjs from "dayjs";
import { memberStore } from "@/store/member";
import { configStore } from "@/store/config";
import planModal from "./plan-modal";
import CustomerTable from "@/views/member/components/cutomer-table/table/table.vue";
import CustomerFilter from "@/views/member/components/cutomer-table/customer-filter/customer-filter.vue";
import { executeManagementPlanTodo } from "@/api/todo";
import { ElMessageBox, ElMessage } from "element-plus";
import { getRandomStr } from "@/utils";
import { teamStore } from "@/store/team";
const { currentTeam } = storeToRefs(teamStore());
const { teams } = storeToRefs(memberStore());
let customer = ref({}); // 选择的客户
const loading = ref(false);
const emits = defineEmits(["transferGroup", "changeDefaultGroupMemberCount", "showPlan"]);
const groupListVisible = ref({});
const props = defineProps({
  group: { type: Object, default: () => ({}) },
  groupList: { type: Array, default: [] },
});
const currentPlan = ref({});
const currentGroupId = ref("");
const groupNewList = computed(() => {
  return props.groupList.filter((item) => item._id !== props.group._id);
});
const visible = ref(false);
function executePlan(item, managementPlan, id) {
  currentGroupId.value = id;
  currentPlan.value = managementPlan;
  customer.value = item;
  visible.value = true;
}
// 转组
function selectGroup() {
  if (props.groupList.length === 0) {
    ElMessage.warning("该团队未建分组!");
  }
}
// 执行任务
async function addPlan(item) {
  let { planId, planName, taskList = [] } = currentPlan.value;
  const { planExecutionTime, executorUserId } = item;
  const { _id: customerId, name: customerName, externalUserId } = customer.value;
  taskList = taskList.map((item) => {
    return {
      ...item,
      executorUserId,
      planExecutionTime: dayjs(planExecutionTime).add(item.taskTime, item.timeType).format("YYYY-MM-DD"),
    };
  });
  if (hasTemplateGroupTask(taskList)) {
    return ElMessage.warning("该档案还未绑定微信联系人，无法执行改回访计划!");
  }
  const executeTeamId = props.group.teamId;
  const { name: executeTeamName } = teams.value.find((item) => item.teamId === executeTeamId) || {};
  // 执行人为当天团队的责任人
  const params = {
    corpId: localStorage.getItem("corpId"),
    customerId: customerId,
    planId,
    planName,
    planExecutStaus: "executing", //  计划状态为执行中
    customerName: customerName,
    customerUserId: externalUserId,
    executeTeamName,
    executorUserId,
    userId: executorUserId,
    executeTeamId,
    memberPlanId: getRandomStr(),
    taskList,
  };
  const { success, message } = await executeManagementPlanTodo(params);
  if (success) {
    visible.value = false;
    search();
  } else {
    ElMessage.error(message);
  }
}

function hasTemplateGroupTask(taskList) {
  return taskList.some((item) => item.executeMethod === "groupTask") && !customer.value.externalUserId;
}

async function transferGroup(item, newGroup) {
  groupListVisible.value[item._id] = false;
  const { _id } = item;
  await ElMessageBox.confirm(`是否把该客户转入【${newGroup.groupName}】`, "提示", { type: "warning" });
  if (newGroup && newGroup.managementPlan && newGroup.managementPlan.planId) {
    try {
      await ElMessageBox.confirm(`该分组已关联回访计划 【${newGroup.managementPlan.planName}】，是否立即加入该回访计划？`, "提示", {
        type: "warning",
        confirmButtonText: "加入计划",
        cancelButtonText: "暂不加入",
      });
      await addGroupIdForMember(_id, newGroup._id, props.group._id);
      executePlan(item, newGroup.managementPlan, props.group._id);
    } catch (error) {
      addGroupIdForMember(_id, newGroup._id, props.group._id);
    }
  } else {
    addGroupIdForMember(_id, newGroup._id, props.group._id);
  }
}

async function addGroupIdForMember(_id, newGroupId, groupId) {
  const { success, message } = await addGroupIdForMemberUrl(_id, newGroupId, groupId);
  if (success) {
    ElMessage.success("转组成功");
    search();
    emits("transferGroup");
  } else {
    ElMessage.error(message);
  }
}

const customerTableRef = ref(); // 有分组的的表格 Ref
const teamCustomerTableRef = ref(); // 未分组的表格 Ref
const filterRef = ref(); // 筛选条件组件 ref

const teamCustomerLayout = ["sex", "age", "currentStage", "customerSource", "customerTags", "plan", "createTimeRange", "arriveTimeRange", "serviceTimeRange", "teammatePicker", "customDaysRange"];
const teamCustomerColumns = ["name", "ageSex", "mobile", "wxContact", "concactUser", "personResponsible", "notes", "customerStage", "customerSource", "tagIds", "group", "plan", "createTime", "arriveTime", "serviceTime"];

const columns = ["name", "ageSex", "mobile", "managePlanNameColumn", "managePlanDateColumn", "managePlanStateColumn"];

const filterParams = ref({});
const tableParams = computed(() => ({ showHasPlan: "YES", teamId: currentTeam.value.teamId, groupIds: [props.group._id] }));
const teamCustomerParams = computed(() => ({
  ...filterParams.value,
  teamId: props.group.teamId,
  hasGroup: "NO",
  showHasPlan: "YES",
}));

const config = configStore();
const { getStage } = config;
const { stageList } = storeToRefs(config);

function showPlan(row) {
  emits("showPlan", row);
}

onMounted(async () => {
  if (stageList.value.length === 0) await getStage();
  filterRef.value && filterRef.value.search();
});

watch(
  () => props.group.teamId,
  async (n) => {
    n && search();
  }
);

function formatList(list) {
  return list.map((item) => {
    const plan = item.plans;
    return { ...item, plan };
  });
}

function search() {
  filterRef.value && filterRef.value.search();
}

function searchGroupCustomer() {
  customerTableRef.value && customerTableRef.value.search(true);
}

async function filterSearch(data) {
  filterParams.value = data.params;
  await nextTick();
  teamCustomerTableRef.value && teamCustomerTableRef.value.search(true);
}
</script>