<template>
  <div class="benefit-drawer">
    <!-- 抽屉组件 -->
    <el-drawer v-model="visible" title="新增权益" destroy-on-close size="80%" class="remark-drawer">
      <my-layout>
        <layout-item>
          <customer-header v-if="customer._id" :customer="customer" @showCustomerDetail="showCustomerDetail" />
          <!-- 灰色分隔线 -->
          <div class="bg-gray-100 h-2"></div>

          <div class="flex justify-between items-center px-4 py-3">
            <h3 class="text-base font-medium">录入权益</h3>
            <div class="flex gap-1">
              <el-button type="primary" @click="addBenefit">添加</el-button>
              <el-button @click="clearBenefits">清空</el-button>
            </div>
          </div>
        </layout-item>
        <layout-main :scroll="false">
          <el-table stripe border height="100%" :data="benefitList">
            <el-table-column type="index" label="序号" width="70" align="center" />
            <el-table-column property="projectName" :min-width="180" label="项目">
              <template #default="{ row, $index }">
                <el-autocomplete v-model="row.projectSearchKeyword" :fetch-suggestions="(queryString, cb) => fetchSuggestions(queryString, cb, row, $index)" placeholder="请输入项目名称搜索" @select="(item) => handleSelect(item, row, $index)" clearable class="w-full" :trigger-on-focus="false" :debounce="300">
                  <template #default="{ item }">
                    <div class="py-2">
                      <div class="text-sm font-medium text-gray-800 mb-1">{{ item.projectName }}</div>
                      <div class="flex justify-between items-center text-xs">
                        <span class="text-gray-500 flex-1">{{ item.deptNames }}</span>
                        <span class="text-orange-500 font-medium ml-2">￥{{ item.price || 0 }}</span>
                      </div>
                    </div>
                  </template>
                </el-autocomplete>
              </template>
            </el-table-column>
            <el-table-column property="usageCount" :min-width="100" label="购买数量">
              <template #default="{ row }">
                <el-input v-model.number="row.usageCount" placeholder="购买数量" type="number" @input="calculateDiscount(row)" />
              </template>
            </el-table-column>
            <el-table-column property="restUsageCount" :min-width="100" label="剩余数量">
              <template #default="{ row }">
                <el-input v-model.number="row.restUsageCount" placeholder="剩余数量" type="number" />
              </template>
            </el-table-column>
            <el-table-column property="validTime" :min-width="180" label="有效期">
              <template #default="{ row }">
                <div class="flex flex-col gap-1">
                  <el-checkbox v-model="row.isUnlimited" @change="handleUnlimitedChange(row)">无限期</el-checkbox>
                  <el-date-picker v-model="row.validTime" type="date" placeholder="选择有效期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" class="w-full" :disabled="row.isUnlimited" />
                </div>
              </template>
            </el-table-column>
            <el-table-column property="treatmentDeptName" :min-width="160" label="治疗科室">
              <template #default="{ row }">
                <el-select v-model="row.treatmentDeptName" placeholder="请选择科室" class="w-full" @change="(value) => handleDeptChange(value, row)">
                  <el-option v-for="dept in allDeptList" :key="dept._id" :label="dept.deptName" :value="dept.deptName" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column property="price" :min-width="100" label="原价(单价)">
              <template #default="{ row }">
                <el-input v-model.number="row.price" placeholder="原价" type="number" readonly />
              </template>
            </el-table-column>
            <el-table-column property="totalPrice" :min-width="120" label="现价(总额)">
              <template #default="{ row }">
                <el-input v-model.number="row.totalPrice" placeholder="现价总额" type="number" @input="calculateDiscount(row)" />
              </template>
            </el-table-column>
            <el-table-column property="discount" :min-width="100" label="折扣">
              <template #default="{ row }">
                <el-input v-model="row.discount" placeholder="折扣" readonly>
                  <template #suffix>折</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column property="createTreatementTime" :min-width="160" label="购买日期">
              <template #default="{ row }"><el-date-picker v-model="row.createTreatementTime" type="date" placeholder="选择购买日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" class="w-full" /></template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="120">
              <template #default="{ row, $index }">
                <span class="operation-text text-red-500 hover:text-red-600 cursor-pointer text-sm mx-1 transition-colors duration-300" @click="deleteBenefitRow($index)">删除</span>
                <span class="operation-text text-blue-600 hover:text-blue-800 cursor-pointer text-sm mx-1 transition-colors duration-300" @click="clearBenefitRow($index)">清空</span>
              </template>
            </el-table-column>
          </el-table>
          <!-- 底部操作按钮 -->
        </layout-main>
        <layout-item>
          <div class="flex justify-center gap-4 px-4 py-2">
            <el-button size="large" @click="handleCancel">取消</el-button>
            <el-button type="primary" size="large" @click="handleConfirm">确定添加</el-button>
          </div>
        </layout-item>
      </my-layout>
    </el-drawer>

    <!-- HIS档案绑定弹窗 -->
    <bind-member :visible="bindCustomerVisible" :list="hisCustomers" :customer="customer" :member-id="customer._id" @cancel="bindCustomerVisible = false" @bindCustomerSuccess="bindCustomer" />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import { storeToRefs } from "pinia";
import { getProjectList } from "@/api/project-manage";
import { getHisCustomerArchive } from "@/api/customerHisSync";
import { addTreatmentRecord } from "@/api/treatment-record";
import { getDeptList } from "@/api/dept-manage";
import { memberStore } from "@/store/member";
import CustomerHeader from "@/components/customer-header/customer-header.vue";
import bindMember from "@/views/member/components/bind-his-customer.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  customer: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["update:visible", "close", "success", "showCustomerDetail"]);

// 获取路由器实例
const router = useRouter();

// 获取公司信息
const { corpInfo } = storeToRefs(memberStore());

// HIS绑定相关变量
const bindCustomerVisible = ref(false);
const hisCustomers = ref([]);
const hisOutpatientId = ref("");

// 科室列表
const allDeptList = ref([]);

// 计算属性用于双向绑定
const visible = computed({
  get: () => props.visible,
  set: (val) => emit("update:visible", val),
});

// 权益列表数据 - 默认展示1条
const benefitList = ref([
  {
    projectName: "",
    projectSearchKeyword: "",
    usageCount: 0,
    restUsageCount: 0,
    validTime: null,
    isUnlimited: true, // 默认为无限期
    treatmentDeptName: "",
    treatmentDept_id: "",
    price: 0,
    totalPrice: 0,
    discount: "10.0",
    createTreatementTime: "",
    selectedProject: null,
    availableDepts: [],
  },
]);

// Autocomplete 获取建议列表
const fetchSuggestions = async (queryString, callback, row, rowIndex) => {
  if (!queryString || queryString.trim().length < 2) {
    callback([]);
    return;
  }

  try {
    const { data, success } = await getProjectList({
      page: 1,
      pageSize: 10,
      projectName: queryString.trim(),
      corpId: localStorage.getItem("corpId"),
      projectStatus: "enable",
      showDepts: true,
    });

    if (success && data && data.list) {
      const suggestions = data.list.map((project) => ({
        ...project,
        value: project.projectName, // Autocomplete 需要的 value 字段
        deptNames: project.depts ? project.depts.map((d) => d.deptName).join("、") : "",
      }));
      callback(suggestions);
    } else {
      callback([]);
    }
  } catch (error) {
    console.error("搜索项目失败:", error);
    callback([]);
  }
};

// 处理项目选择
const handleSelect = (item, row, rowIndex) => {
  row.projectName = item.projectName;
  row.projectSearchKeyword = item.projectName;
  row.selectedProject = item;

  // 设置可用科室（保留用于显示，但不限制选择）
  row.availableDepts = item.depts || [];

  // 清空治疗科室选择，让用户从所有科室中选择
  row.treatmentDeptName = "";
  row.treatmentDept_id = "";

  // 设置项目原价和购买金额
  row.price = item.price || 0;
  row.totalPrice = 0;

  // 计算折扣
  calculateDiscount(row);
};

// 计算折扣
const calculateDiscount = (row) => {
  const originalTotal = (row.price || 0) * (row.usageCount || 0);
  const currentTotal = row.totalPrice || 0;

  if (originalTotal > 0) {
    const discountValue = (currentTotal / originalTotal) * 10;
    row.discount = discountValue <= 10 ? discountValue.toFixed(1) : "10.0";
  } else {
    row.discount = "10.0";
  }
};

// 处理无限期变化
const handleUnlimitedChange = (row) => {
  if (row.isUnlimited) {
    row.validTime = null; // 清空日期选择
  }
};

// 处理科室选择变更
const handleDeptChange = (deptName, row) => {
  // 根据科室名称找到对应的科室对象
  const selectedDept = allDeptList.value?.find((dept) => dept.deptName === deptName);
  if (selectedDept) {
    row.treatmentDept_id = selectedDept._id;
  } else {
    row.treatmentDept_id = "";
  }
};

// 删除权益行
const deleteBenefitRow = (index) => {
  if (benefitList.value.length <= 1) {
    ElMessage.warning("至少保留一行数据");
    return;
  }
  benefitList.value.splice(index, 1);
  ElMessage.success("已删除该行数据");
};

// 清空权益行
const clearBenefitRow = (index) => {
  benefitList.value[index] = {
    projectName: "",
    projectSearchKeyword: "",
    usageCount: 0,
    restUsageCount: 0,
    validTime: null,
    isUnlimited: true, // 默认为无限期
    treatmentDeptName: "",
    treatmentDept_id: "",
    price: 0,
    totalPrice: 0,
    discount: "10.0",
    createTreatementTime: "",
    selectedProject: null,
    availableDepts: [],
  };
  ElMessage.success("已清空该行数据");
};

// 添加权益
const addBenefit = () => {
  const newBenefit = {
    projectName: "",
    projectSearchKeyword: "",
    usageCount: 0,
    restUsageCount: 0,
    validTime: null,
    isUnlimited: true, // 默认为无限期
    treatmentDeptName: "",
    treatmentDept_id: "",
    price: 0,
    totalPrice: 0,
    discount: "10.0",
    createTreatementTime: "",
    selectedProject: null,
    availableDepts: [],
  };
  benefitList.value.push(newBenefit);
};

// 清空权益列表 - 清空所有框内已录入的数据
const clearBenefits = () => {
  benefitList.value = benefitList.value.map(() => ({
    projectName: "",
    projectSearchKeyword: "",
    usageCount: 0,
    restUsageCount: 0,
    validTime: null,
    isUnlimited: true, // 默认为无限期
    treatmentDeptName: "",
    treatmentDept_id: "",
    price: 0,
    totalPrice: 0,
    discount: "10.0",
    createTreatementTime: "",
    selectedProject: null,
    availableDepts: [],
  }));
  ElMessage.success("已清空数据");
};

// 获取所有科室列表
const getAllDeptList = async () => {
  try {
    const { data, success } = await getDeptList();
    if (success && data && Array.isArray(data.list)) {
      allDeptList.value = data.list;
    } else {
      allDeptList.value = [];
    }
  } catch (error) {
    console.error("获取科室列表失败:", error);
    allDeptList.value = [];
  }
};

// 取消操作
const handleCancel = () => {
  emit("close");
};

// 处理显示客户详情

function showCustomerDetail() {
  emit("showCustomerDetail", props.customer._id);
}

// 绑定院内档案
async function bindInhosCustomer() {
  // 先判断是否有身份证或客户编号
  if (!props.customer.idCard && !props.customer.customerNumber) {
    // 弹出输入框让用户输入HIS门诊号
    ElMessageBox.prompt("请输入HIS门诊号", "绑定院内档案", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      inputPlaceholder: "请输入门诊号",
      inputValidator: (value) => {
        if (!value) {
          return "门诊号不能为空";
        }
        return true;
      },
    })
      .then(({ value }) => {
        hisOutpatientId.value = value;
        getCustomerArchiveInhos("outpatientId");
      })
      .catch(() => {
        // 用户取消输入
        ElMessage.info("已取消输入");
      });
    return;
  }
  await getCustomerArchiveInhos();
  if (hisCustomers.value.length > 0) {
    bindCustomerVisible.value = true;
  } else {
    ElMessageBox.alert("his系统内未查询到客户档案!", "温馨提示", { type: "warning", confirmButtonText: "知道了" });
  }
}

// 查询HIS系统档案
async function getCustomerArchiveInhos(queryType) {
  let query = {};
  if (queryType === "outpatientId") {
    // 通过门诊号查询
    query = {
      idNo: hisOutpatientId.value,
    };
  } else if (props.customer.idCard) {
    // 通过身份证号查询
    query = {
      idCard: props.customer.idCard,
    };
  } else if (props.customer.customerNumber) {
    // 通过档案编号查询
    query = {
      idNo: props.customer.customerNumber,
    };
  }

  let { success, data } = await getHisCustomerArchive(query);
  hisCustomers.value = success ? data.list : [];

  // 如果查询成功并有结果，显示绑定弹窗
  if (success && data.list && data.list.length > 0) {
    bindCustomerVisible.value = true;
  } else {
    ElMessageBox.alert("his系统内未查询到客户档案!", "温馨提示", { type: "warning", confirmButtonText: "知道了" });
  }
}

// 绑定客户回调
function bindCustomer(updatedCustomer) {
  bindCustomerVisible.value = false;
  // 更新customer对象的isConnectHis状态
  if (updatedCustomer) {
    // 如果有返回更新后的客户数据，则使用它
    Object.assign(props.customer, updatedCustomer);
  } else {
    // 否则手动更新绑定状态
    props.customer.isConnectHis = true;
  }
  ElMessage.success("院内档案绑定成功");
  // 重新触发确认添加操作
  setTimeout(() => {
    handleConfirm();
  }, 100);
}

// 确定添加操作
const handleConfirm = async () => {
  // 检查是否需要绑定HIS档案
  if (corpInfo.value?.isConnectHis && props.customer._id && !props.customer.isConnectHis) {
    ElMessageBox.confirm("检测到当前客户未绑定院内档案，新增权益前需要先绑定院内档案。是否立即绑定？", "绑定院内档案", {
      confirmButtonText: "立即绑定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        bindInhosCustomer();
      })
      .catch(() => {
        ElMessage.info("已取消添加权益操作");
      });
    return;
  }

  // 验证数据
  const validData = benefitList.value.filter((item) => {
    const hasValidTime = item.isUnlimited || item.validTime; // 无限期或有具体日期都可以
    return item.projectName && item.usageCount > 0 && item.restUsageCount >= 0 && hasValidTime && item.treatmentDeptName && item.createTreatementTime;
  });

  if (validData.length === 0) {
    ElMessage.error("请填写完整的权益数据（项目名称、购买数量、剩余数量、有效期（无限期或具体日期）、治疗科室、购买日期均为必填）");
    return;
  }

  try {
    // 调用addTreatmentRecord接口保存每一条权益数据
    for (const benefit of validData) {
      let treatmentData = {
        billType: "门诊", // 权益类型
        customerId: props.customer._id,
        customerName: props.customer.name,
        treatmentDeptName: benefit.treatmentDeptName,
        treatmentDeptId: benefit.treatmentDept_id,
        usageCount: benefit.usageCount,
        restUsageCount: benefit.restUsageCount,
        projectId: benefit.selectedProject?._id,
        projectName: benefit.projectName,
        treatmentDept_id: benefit.treatmentDept_id,
        price: benefit.selectedProject?.price || 0,
        billdCreator: localStorage.getItem("userId"),
        discount: parseFloat(benefit.discount) || 0, // 使用计算出的折扣率
        totalPrice: benefit.totalPrice,
        treatmentStatus: benefit.restUsageCount > 0 ? "init" : "treated",
        corpId: localStorage.getItem("corpId"),
        deductUsageCount: 0, // 初始已划扣数量为0
        createTreatementTime: new Date(benefit.createTreatementTime).getTime(),
        billTime: new Date(benefit.createTreatementTime).getTime(),
      };
      if (!benefit.isUnlimited) treatmentData.validTime = new Date(benefit.validTime).getTime();
      const { success, message } = await addTreatmentRecord({ params: treatmentData });

      if (!success) {
        ElMessage.error(`保存权益"${benefit.projectName}"失败：${message || "未知错误"}`);
        return;
      }
    }

    ElMessage.success(`成功添加 ${validData.length} 条权益数据`);
    emit("success"); // 触发成功事件，让父组件刷新数据
    emit("close");
  } catch (error) {
    console.error("保存权益数据失败:", error);
    ElMessage.error("保存失败，请重试");
  }
};

// 组件挂载时获取科室列表
onMounted(() => {
  getAllDeptList();
});

// 监听抽屉打开，清空数据并获取权益列表
watch(
  () => props.visible,
  (newVal, oldVal) => {
    if (newVal) {
      // 清空权益数据，重置为初始状态
      benefitList.value = [
        {
          projectName: "",
          projectSearchKeyword: "",
          usageCount: 0,
          restUsageCount: 0,
          validTime: null,
          isUnlimited: true, // 默认为无限期
          treatmentDeptName: "",
          treatmentDept_id: "",
          price: 0,
          totalPrice: 0,
          discount: "10.0",
          createTreatementTime: "",
          selectedProject: null,
          availableDepts: [],
        },
      ];
      // 这里可以添加获取权益列表的API调用
      // getBenefitList()
    }
  }
);
</script>

<style scoped>
.benefit-drawer :deep(.el-drawer.remark-drawer .el-drawer__body) {
  padding: 0px !important;
}
</style>
