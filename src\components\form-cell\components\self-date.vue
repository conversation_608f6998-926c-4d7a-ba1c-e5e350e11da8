<template>
  <div v-if="isRead">
    {{ modelValue }}
  </div>
  <el-date-picker v-else :model-value="modelValue" placeholder="请选择" type="date" @update:model-value="change($event)" value-format="YYYY-MM-DD" style="width: 200px" />
</template>
<script setup>
import { watch, ref, computed } from "vue";
import dayjs from "dayjs";
const $emit = defineEmits(["change"]);
const props = defineProps({
  item: {
    type: Object,
    default: {},
  },
  value: {
    type: String,
    default: "",
  },
  isRead: {
    type: Boolean,
    default: false,
  },
});
const modelValue = computed(() => {
  if (!props.value) return "";
  return props.item.format ? dayjs(props.value).format(props.item.format) : dayjs(props.value).format("YYYY-MM-DD");
});

function change(value) {
  $emit("change", {
    title: props.item.title,
    value,
  });
}
</script>

<style></style>