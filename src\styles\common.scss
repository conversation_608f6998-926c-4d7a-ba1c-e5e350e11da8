.pointer {
    cursor: pointer;
}

[pointer] {
    cursor: pointer;
}

[border-bottom] {
    border-bottom: 1px solid #eee;
}

[border-bottom--dashed] {
    border-bottom: 1px dashed #eee;
}

[border-top] {
    border-top: 1px solid #eee;
}

[border-top-2] {
    border-top: 2px solid var(--el-border-color-light);
}


[border-left] {
    border-left: 1px solid #eee;
}

[border-right] {
    border-right: 1px solid #eee;
}

[main-color] {
    color: #006eff;
}

[grew-color] {
    color: #888888;
}

$main-color: #006eff;
$grew-color: #888888;

.align-center {
    align-items: center
}

.cell-radio {
    width: 18px;
    height: 18px;
    cursor: pointer;
}


body {
    padding-bottom: env(safe-area-inset-bottom);
}

.b-fff {
    background-color: #fff;
}


.yc-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.yc-scrollbar::-webkit-scrollbar-track {
    background: transparent;

}

.yc-scrollbar::-webkit-scrollbar-thumb {
    background-color: rgba(144, 147, 153, 0.3);
    opacity: 0.3;
    border-radius: 4px;
}