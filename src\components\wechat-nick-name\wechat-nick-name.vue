<template>
  <span ref="elementRef" class="inline-block min-w-20px">
    <el-icon v-if="loading" class="animate-spin transform translate-y-2px">
        <Loading />
    </el-icon>
    {{ name }} 
  </span>
</template>
<script setup>
import { computed, ref, watch } from 'vue';
import { storeToRefs } from 'pinia'
import { useElementVisibility } from '@vueuse/core'
import wxContact from "@/store/wxContact";

const props = defineProps({
  name: { type: String, default: '' },
  wechatId: { type: String, default: '' }
})

const elementRef = ref(null)
const visible = useElementVisibility(elementRef);
const { addTask } = wxContact();
const { pending, contactName } = storeToRefs(wxContact())

const loading = computed(() => props.wechatId ? Boolean(pending.value[props.wechatId]) : false);
const name = computed(() => props.wechatId ? contactName.value[props.wechatId] : '');

watch(visible, n => {
  if (n && props.wechatId && !contactName.value[props.wechatId] && !pending.value[props.wechatId]) {
    addTask(props.wechatId)
  }
}, { immediate: true })
</script>
<style lang="scss" scoped></style>
