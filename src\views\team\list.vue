<template>
  <my-layout bg-fff common-shadow>
    <layout-item>
      <div flex justify-between items-center p-15>
        <el-input placeholder="输入团队名称搜索" v-model="name" @keyup.enter="search" class="w-180px" />
        <el-button ml-10 type="primary" @click="search()">查询</el-button>
        <div flex-grow></div>
        <el-button type="primary" @click="addTeam()">新增</el-button>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <el-table stripe height="100%" :data="list" v-loading="loading" row-key="_id">
        <template #empty>
          <el-empty description="无团队数据，快去创建团队吧！">
            <el-button type="primary" @click="addTeam()">创建团队</el-button>
          </el-empty>
        </template>
        <el-table-column property="name" label="团队名称" :min-width="160" />
        <!-- <el-table-column property="memberCount" label="团队人数" :min-width="100">
          <template #default="{ row: { memberList } }">
            {{ memberList && memberList.length > 0 ? memberList.length : 0 }}
          </template>
        </el-table-column> -->

        <el-table-column property="member" label="团队成员" :min-width="200">
          <template #default="{ row: { _id, memberList } }">
            <el-popover :key="_id" placement="top-start" :width="320" trigger="hover">
              <template #reference>
                <div>
                  <span v-for="(userid, idx) in memberList" :key="userid + '_' + idx">
                    <ww-user :openid="userid"></ww-user>
                    <span v-if="idx !== memberList.length - 1">、</span>
                  </span>
                </div>
              </template>
              <span v-for="(userid, idx) in memberList" :key="userid">
                <ww-user :openid="userid"></ww-user>
                <span v-if="idx !== memberList.length - 1">、</span>
              </span>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column property="customerCount" label="患者人数" :min-width="100">
          <template #default="{ row: { customerCount } }">
            {{ customerCount > 0 ? customerCount : 0 }}
          </template>
        </el-table-column>
        <el-table-column property="qrcodes" label="团队二维码" :min-width="200">
          <template #default="{ row: { qrcodes, name, teamId } }">
            <div class="min-h-40px flex flex-wrap">
              <div v-for="(i, idx) in qrcodes" :key="i.id" class="relative h-40px w-80px cursor-pointer"
                :class="idx > 0 ? 'ml-8px' : ''" :title="i.name ? i.name : ''" @click="showQrcode(i, name, teamId)">
                <img class="relative h-40px w-40px mx-auto" src="@/assets/svg-icons/qrcode.svg" />
                <div v-if="i.name"
                  class="px-6px absolute top-1/2 w-full text-center truncate text-12px leading-18px -translate-y-9px opacity-90 transform text-white bg-blue-500">
                  {{ i.name }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column property="teamTroduce" label="团队简介">
          <template #default="{ row: { teamTroduce } }">
            <el-popover placement="top-start" :width="320" trigger="hover" :content="teamTroduce">
              <template #reference>
                <el-text truncated> {{ teamTroduce }}</el-text>
              </template>
            </el-popover>
          </template>
        </el-table-column> -->
        <el-table-column fixed="right" class-name="el-table-fixed-column--shadow" label="操作" width="140">
          <template #default="{ row }">
            <el-text class="cursor-pointer" type="primary" @click="showDetail(row)">详情</el-text>
            <el-text class="cursor-pointer ml-10px" type="primary" @click="editTeam(row)">编辑</el-text>
            <el-text v-if="row.customerCount === 0" class="cursor-pointer  ml-10px" type="danger"
              @click="removeTeam(row)">删除</el-text>
          </template>
        </el-table-column>
      </el-table>
    </layout-main>
    <layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="currentPage" @handle-size-change="onSizeChange"
        @handle-current-change="onCurrentChange" />
    </layout-item>
  </my-layout>
  <el-dialog title="团队详情" v-model="visible" width="640">
    <el-scrollbar :max-height="maxHeight">
      <el-form px-15 :label-width="120" label-position="left">
        <el-form-item label="团队名称：">
          <el-text size="default">{{ currentTeam.name }}</el-text>
        </el-form-item>
        <el-form-item label="团队负责人：">
          <span class="team-member" v-for="userid in currentTeam.leaders" :key="userid" mr-5>
            <el-tag>
              <ww-user :openid="userid"></ww-user>
            </el-tag>
          </span>
        </el-form-item>
        <el-form-item label="团队成员：">
          <span class="team-member" v-for="userid in currentTeam.members" :key="userid" mr-5>
            <el-tag>
              <ww-user :openid="userid"></ww-user>
            </el-tag>
          </span>
        </el-form-item>
        <el-form-item v-if="currentTeam.friends.length" label="可加好友人员：">
          <span class="team-member" v-for="userid in currentTeam.friends" :key="'friend_' + userid" mr-5 title="支持加好友">
            <el-tag>
              <ww-user :openid="userid"></ww-user>
            </el-tag>
          </span>
        </el-form-item>
        <el-form-item label="团队简介：">
          <el-text size="default">{{ currentTeam.teamTroduce }}</el-text>
        </el-form-item>
        <el-form-item v-if="currentTeam.qrcodes && currentTeam.qrcodes.length" label="团队二维码：">
          <div v-for="item in currentTeam.qrcodes" :key="item.id" text-center class="qrcode-item">
            <vue-qrcode class="mx-auto" :value="item.qrcode" :options="{ width: 120, margin: 2 }"></vue-qrcode>
            <div>
              <el-text size="default">{{ item.name }}</el-text>
            </div>
            <div>
              <el-button type="primary" text @click="download(item.qrcode, `${item.name}.png`)">下载二维码</el-button>
              <el-button type="primary" text @click="copyLink(item.qrcode)">复制链接</el-button>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </el-scrollbar>

    <template #footer>
      <div text-center>
        <el-button class="w-100px" type="primary" @click="visible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>

  <single-qrcode-modal :data="qrcode" :width="width" :visible="singleQrcodeVisible" @close="close" />
</template>

<script setup>
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import useModal from "@/hooks/useModal";
import { downloadQrcode, useCopy } from "@/utils";
import { getCorpTeams, deleteCorpTeam } from "@/api/corp";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import WwUser from "@/components/ww-user/index.vue";
import VueQrcode from "@chenfengyuan/vue-qrcode";
import { configStore } from "@/store/config";
import singleQrcodeModal from "./components/single-qrcode-modal.vue";

const name = ref("");
const list = ref([]);
const total = ref(0);
const pageSize = ref(10);
const currentPage = ref(1);
const loading = ref(false);
function onSizeChange(e) {
  pageSize.value = e;
  getList();
}
function onCurrentChange(e) {
  currentPage.value = e;
  getList();
}

function search() {
  currentPage.value = 1;
  getList();
}

async function getList() {
  loading.value = true;
  const { success, data = {}, message } = await getCorpTeams(currentPage.value, pageSize.value, localStorage.getItem("corpId"), name.value.trim());
  const { list: teams = [], total: count = 0 } = data;
  list.value = teams.map((i) => ({
    ...i,
    memberList: Array.isArray(i.memberList) ? i.memberList.filter(Boolean) : [],
    qrcodes: Array.isArray(i.qrcodes) ? i.qrcodes.filter((i) => i.qrcode) : [],
  }));
  total.value = count;
  loading.value = false;
  if (!success) ElMessage.error(message);
}
getList();

const currentTeam = ref({});
const visible = ref(false);
const maxHeight = ref(400);
async function showDetail(team) {
  const config = configStore();
  currentTeam.value.intentionQrcode = `${import.meta.env.VITE_PATIENT_PAGE_BASE_URL}pages/external/basicFiling/index?teamId=${team.teamId}`;
  currentTeam.value.checkInQrcode = `${import.meta.env.VITE_PATIENT_PAGE_BASE_URL}pages/index/index?teamId=${team.teamId}`;
  currentTeam.value = { ...team, ...groupTeammate(team) };
  visible.value = true;
  const height = window.innerHeight;
  maxHeight.value = height - height * 0.15 - 220 + "px";
}

function groupTeammate(team) {
  const memberList = Array.isArray(team.memberList) ? team.memberList : [];
  const memberLeaderList = Array.isArray(team.memberLeaderList) ? team.memberLeaderList : [];
  const friendlyMembers = Array.isArray(team.friendlyMembers) ? team.friendlyMembers : [];
  return memberList.reduce(
    (val, item) => {
      if (memberLeaderList.includes(item)) {
        val.leaders.push(item);
      } else {
        val.members.push(item);
      }
      if (friendlyMembers.includes(item)) val.friends.push(item);
      return val;
    },
    { members: [], leaders: [], friends: [] }
  );
}

const router = useRouter();
function editTeam(team) {
  router.push({ name: "TEAMDETAIL", params: { teamId: team.teamId } });
}
function addTeam() {
  router.push({ name: "TEAMDETAIL" });
}

function download(content, name) {
  downloadQrcode(content, name);
}

function copyLink(link) {
  useCopy(link);
}

const { close, show, visible: singleQrcodeVisible, width } = useModal(); //  选择客户弹窗
const qrcode = ref({});
function showQrcode(code, teamName, teamId) {
  qrcode.value = { ...code, teamName, teamId };
  show();
}

async function removeTeam(team) {
  if (loading.value) return;
  await ElMessageBox.confirm("确定要删除该团队吗?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      loading.value = true
      const { success, message } = await deleteCorpTeam(team.teamId, team._id, localStorage.getItem("corpId"));
      loading.value = false;
      if (success) {
        ElMessage.success("删除成功");
        currentPage.value = 1;
        getList();
      } else ElMessage.error(message);
    })
}

</script>

<style scoped>
:deep(.el-empty__description) {
  line-height: 40px;
}

:deep(.el-empty__bottom) {
  margin-top: 0;
}

:deep(.el-form-item) {
  margin-bottom: 10px;
}

.query-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
}

.table-action-btn {
  padding-left: 0;
}

.no-ml {
  margin-left: 0;
}

.qrcode-item {
  margin-right: 10px;
}

.team-member {
  position: relative;
}

.team-member__tag {
  position: absolute;
  right: -4px;
  bottom: 0;
  width: 16px;
  height: 16px;
}
</style>
