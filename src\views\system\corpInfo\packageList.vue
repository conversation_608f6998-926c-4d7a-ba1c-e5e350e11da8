<template>
  <my-layout common-shadow>
    <layout-main :scroll="false">
      <div h-full>
        <el-table v-loading="loading" height="100%" :data="list">
          <el-table-column property="packageName" label="套餐名称" />
          <el-table-column label="员工账户数">
            <template #default="{ row }">
              <div class="flex">
                <div>{{ row.accountCount }}</div>
                <div class="ml-10px" v-if="row.giveAccountCount">(赠送{{ row.giveAccountCount }})</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="客户档案数">
            <template #default="{ row }">
              <div class="flex">
                <div>{{ row.customerCount }}</div>
                <div class="ml-10px" v-if="row.giveCustomerCount">(赠送{{ row.giveCustomerCount }})</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="有效时长">
            <template #default="{ row }">{{ getDaysBetweenDates(row) }} 天</template>
          </el-table-column>
          <el-table-column property="packageStarAndEndTime" label="套餐起止时间" width="220">
            <template #default="{ row }">{{ dayjs(row.packageStartTime).format("YYYY-MM-DD") }}至 {{ dayjs(row.packageEndTime).format("YYYY-MM-DD") }}</template>
          </el-table-column>
          <el-table-column property="packagePrice" label="价格" />
          <el-table-column property="placeOrderTime" label="下单时间" />
          <el-table-column label="套餐状态">
            <template #default="{ row }">
              {{ getPackageStatus(row) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </layout-main>
    <layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="currentPage" @handle-size-change="onSizeChange" @handle-current-change="onCurrentChange" />
    </layout-item>
  </my-layout>
</template>

<script setup>
import { computed, ref, reactive } from "vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import { getPackageList } from "@/api/corp.js";
import { ElMessage, ElMessageBox } from "element-plus";
import dayjs from "dayjs";
import { useRouter } from "vue-router";
import { memberStore } from "@/store/member";
const { corpInfo } = memberStore();
const router = useRouter();
const total = ref(10);
const pageSize = ref(10);
const currentPage = ref(1);
const loading = ref(false);
const list = ref([]);
import { useRoute } from "vue-router";
const route = useRoute();
getList();
function onSizeChange(e) {
  pageSize.value = e;
  getList();
}
function onCurrentChange(e) {
  currentPage.value = e;
  getList();
}
function getPackageStatus(row) {
  if (row.packageStatus === "closed") {
    return "已结束";
  }
  const starTime = dayjs(row.packageStartTime).format("YYYY-MM-DD");
  const diffStartTime = dayjs().diff(starTime, "days");
  const endTime = dayjs(row.packageEndTime).format("YYYY-MM-DD");
  const diffEndtTime = dayjs().diff(endTime, "days");
  if (diffStartTime < 0) {
    return "未开始";
  }
  if (diffEndtTime <= 0) {
    return "进行中";
  } else {
    return "已结束";
  }
}

function getDaysBetweenDates(item) {
  const { packageStartTime, packageEndTime } = item;
  const startDate = dayjs(packageStartTime);
  const endDate = dayjs(packageEndTime);
  return endDate.diff(startDate, "day");
}
// 获取服务记录
async function getList() {
  const { data, message, success } = await getPackageList(currentPage.value, pageSize.value, corpInfo._id);
  if (success) {
    list.value = data.data;
    total.value = data.total;
  } else {
    ElMessage.error(message);
  }
}
</script>

<style scoped lang="scss">
:deep(.el-table__inner-wrapper) {
  height: 100% !important;
}

.query {
  display: flex;
  flex-wrap: wrap;
  padding: 0 15px;

  .query__item {
    width: 180px;
    margin-bottom: 15px;
    margin-right: 10px;

    @at-root &--large {
      width: 240px;
    }

    @at-root &--small {
      width: 140px;
    }

    @at-root &--small {
      width: 140px;
    }

    @at-root &--auto {
      width: auto;
    }

    @at-root &:last-of-type {
      margin-left: auto;
      margin-right: 0;
    }
  }
}
</style>