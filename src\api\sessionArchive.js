import { post } from "./axios";

async function useCorp(data) {
  const res = await post("sessionArchive", data);
  return res;
}

export async function getCorpMemberChatList({ page = 1, pageSize = 50, memberUserId }) {
  const res = await useCorp({ type: "getCorpMemberChatList", page, pageSize, corpId: localStorage.getItem("corpId"), memberUserId });
  return res;
}
export async function getCorpMemberChatRecord({ page = 1, pageSize = 10, memberUserId, customerUserId, msgIds, orderBy = "asc", chatDateRange }) {
  const res = await useCorp({ type: "getCorpMemberChatRecord", page, pageSize, corpId: localStorage.getItem("corpId"), memberUserId, customerUserId, msgIds, orderBy, chatDateRange });
  return res;
}
export async function getSessionArchiveUserList() {
  const res = await useCorp({ type: "getSessionArchiveUserList", corpId: localStorage.getItem("corpId") });
  return res;
}

export async function getCorpSessionArchive() {
  const res = await useCorp({ type: "getCorpSessionArchive", corpId: localStorage.getItem("corpId") });
  return res;
}

export async function getModalTaskResult(data) {
  const res = await useCorp({ type: "getModalTaskResult", corpId: localStorage.getItem("corpId"), ...data });
  return res;
}

export async function batchStorageSession() {
  const res = await useCorp({ type: "batchStorageSession", corpId: localStorage.getItem("corpId") });
  return res;
}
export async function searchChatRecord({ keyword, cursor, limit, permanentCode }) {
  const res = await useCorp({ type: "searchChatRecord", corpId: localStorage.getItem("corpId"), keyword, cursor, limit, permanentCode });
  return res;
}

export async function getChatCount() {
  const res = await useCorp({ type: "getChatCount", corpId: localStorage.getItem("corpId") });
  return res;
}

export async function getSummaryResult({ magList }) {
  const res = await useCorp({ type: "getSummaryResult", corpId: localStorage.getItem("corpId"), magList });
  return res;
}

export async function getRecommendResult({ magList, kbId }) {
  const res = await useCorp({ type: "getRecommendResult", corpId: localStorage.getItem("corpId"), magList, kbId });
  return res;
}

export async function getEmotionResult({ magList }) {
  const res = await useCorp({ type: "getEmotionResult", corpId: localStorage.getItem("corpId"), magList });
  return res;
}

export async function getToDayAllChatRecord({ memberUserId, customerUserId }) {
  const res = await useCorp({ type: "getToDayAllChatRecord", corpId: localStorage.getItem("corpId"), memberUserId, customerUserId });
  return res;
}

export async function getReplyChatRecord({ memberUserId, customerUserId }) {
  const res = await useCorp({ type: "getReplyChatRecord", corpId: localStorage.getItem("corpId"), memberUserId, customerUserId });
  return res;
}

export async function getDocumentList() {
  const res = await useCorp({ type: "getDocumentList", corpId: localStorage.getItem("corpId") });
  return res;
}
