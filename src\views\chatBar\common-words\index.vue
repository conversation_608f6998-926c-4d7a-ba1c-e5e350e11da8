<template>
  <div class="full-h-screen">
    <my-layout bg-fff no-radius v-loading="loading">
      <layout-tem>
        <div v-if="useAi" common-shadow class="flex items-center py-12px px-12px mb-4px">
          <img class="w-24px h-24px flex-shrink-0" src="@/assets/icons/logo.png" />
          <div class="ml-5px text-14px text-blue-400 flex-grow">AI智能服务</div>
          <div class="flex-shrink-0 ml-5px text-white text-12px bg-blue-500 py-6px px-8px rounded-xl" @click="toAiService('intelligentReply')">智能回复</div>
          <div class="flex-shrink-0 ml-5px text-white text-12px bg-blue-500 py-6px px-8px rounded-xl" @click="toAiService('serviceRecord')">会话概要</div>
        </div>
        <div flex items-center>
          <div class="cate" :class="cateType === 'corp' ? 'cate--active' : ''" px-15 py-10 font-14 text-center border-bottom flex-grow @click="changeCateType('corp')">更多常用语</div>
          <div class="cate" :class="cateType === 'user' ? 'cate--active' : ''" px-15 py-10 font-14 text-center border-bottom flex-grow @click="changeCateType('user')">我的常用语</div>
        </div>
      </layout-tem>
      <layout-main border-bottom :scroll="false">
        <div flex h-full>
          <my-layout class="min-w-120px w-1/3 max-w-180px" flex-shrink-0 no-radius border-right>
            <layout-main>
              <classify-list v-if="cateType === 'corp'" appendAll :checked-cate="current" :data="cateList" @change="changeCurrent($event)" />
              <div v-else>
                <div v-for="i in myCates" :key="i._id" class="mr-10px break-all text-14px py-12px px-5px border-b border-gray-200 cursor-pointer" :class="current && current._id === i._id ? 'text-blue-500' : ''" @click="changeCurrent(i)">
                  {{ i.label }}
                </div>
              </div>
            </layout-main>
          </my-layout>
          <my-layout no-radius flex-grow>
            <layout-item>
              <div px-15 py-10 border-bottom>
                <el-input text-center placeholder="搜索" v-model="keyword" />
              </div>
            </layout-item>
            <layout-main :scroll="false">
              <el-scrollbar height="100%" ref="scrollRef" @scroll="handleScroll($event)">
                <div v-for="item in list" :key="item._id">
                  <div px-15 py-10 border-bottom font-14 class="break-all" :class="popoverWords._id === item._id ? 'text-blue-500' : ''" @click="showPopover(item)">
                    {{ item.content }}
                  </div>
                </div>
                <empty-data v-if="list.length === 0" :top="120" title="暂无常用语" padding="0 0 15px" text-top="10px" :image-width="160"></empty-data>
              </el-scrollbar>
            </layout-main>
          </my-layout>
        </div>
      </layout-main>
      <layout-item v-if="cateType === 'user'">
        <div px-15 py-10 font-14 font-semibold pointer text-center color-primary class="flex">
          <el-button plain class="flex-grow" type="primary" @click="showGroupModal">编辑分组</el-button>
          <el-button class="ml-15px flex-grow" type="primary" @click="addWords()">新增常用语</el-button>
        </div>
      </layout-item>
    </my-layout>
  </div>
  <edit-modal :visible="groupVisible" :width="width" :cates="myCates" @change="getMyCates()" @delete="onDelete" @close="closeGroupModal" />
  <edit-words :words="currentWords" :visible="wordsVisible" :width="width" :cates="myCates" @change="reloadList()" @close="closeWordsModal" />

  <el-dialog v-model="visible" class="chat-commonwords-dialog" :close-on-click-modal="false" title="选择分组" :width="width" @close="close">
    <template #header>
      <span font-14 font-semibold>选择分类</span>
    </template>
    <el-form px-15 pt-10 :label-width="0">
      <el-form-item prop="name">
        <el-select w-full v-model="newWords.cateId" placeholder="请选择常用语分类">
          <el-option v-for="item in myCates" :class="item._id === 'all' ? 'hidden' : ''" :key="item._id" :label="item.label" :value="item._id" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div text-center>
        <el-button @click="close">取消</el-button>
        <el-button type="primary" @click="setWords()">保存</el-button>
      </div>
    </template>
  </el-dialog>
  <action-sheet :visible="showSheet" :item-list="actions" cancel-text="取消" close-on-click-action @close="hidePopover" @select="selectAction" />
  <ai-modal :visible="aiVisible" @close="aiVisible = false" :publicKeys="publicKeys" :externalUserId="externalUserId" />
  <chat-summary :visible="chatVisible" @close="chatVisible = false" :publicKeys="publicKeys" :externalUserId="externalUserId" />
</template>
<script setup>
import { computed, nextTick, onMounted, ref, watch } from "vue";
import { useRoute } from "vue-router";
import { watchDebounced } from "@vueuse/core";
import { ElMessage, ElMessageBox } from "element-plus";
import { useCopy } from "@/utils";
import EditModal from "./edit-modal";
import editWords from "./edit-words.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import EmptyData from "@/components/empty-data.vue";
import useModal from "@/hooks/useModal";
import useElScrollbar from "@/hooks/useElScrollbar";
import ActionSheet from "@/components/weui/action-sheet.vue";
import useCommonWordsApi from "./useApi";
import useCustomer from "../chat-bar/useCustomer";
import aiModal from "./ai-modal.vue";
import chatSummary from "./chat-summary.vue";

import * as ww from "@/libs/@wecom/jssdk/wecom.esm.js";
import classifyList from "@/components/classify-list/classify-list-side.vue";
import useClassifyList from "@/components/classify-list/useClassifyList";
import { getCorpSessionArchiveInfo, publicKeys } from "@/views/chat-history/api/authMember.js";
import { getSessionArchiveUserList } from "@/api/sessionArchive";
import { getCommonWordsList, getCorpCommonWordCate, getUserCommonWordCate } from "@/api/knowledgeBase";
const { remove, setWords: setMyWords } = useCommonWordsApi();
const { externalUserId, getCustomer } = useCustomer();
const keyword = ref("");
const page = ref(1);
const more = ref(false);
const list = ref([]);
const scrollRef = ref();
const haveSessionAuth = ref(false);
const { close: closeGroupModal, show: showGroupModal, visible: groupVisible } = useModal(560); // 选择客户弹窗
const { close: closeWordsModal, show: showWordsModal, visible: wordsVisible } = useModal(560); // 选择客户弹窗
const route = useRoute();
const useAi = computed(() => route.query.AI === "YES");
const { atBottom, handleScroll } = useElScrollbar(scrollRef, 0);
watch(atBottom, async (n) => {
  if (n) {
    await loadmore();
    atBottom.value = false;
  }
});

async function loadmore() {
  if (more.value && !loading.value) {
    page.value += 1;
    await getList();
  }
}

const actions = computed(() => {
  const list = [{ label: "发送" }, { label: "复制" }];
  if (cateType.value === "corp") list.push({ label: "添加为我的常用语" });
  else if (cateType.value === "user") list.push({ label: "编辑常用语", classnames: "text-blue-500" }, { label: "删除常用语", classnames: "text-red-500" });
  return list;
});

const currentWords = ref({});
const showSheet = ref(false);
const popoverWords = ref({});
function showPopover(words) {
  popoverWords.value = words;
  showSheet.value = true;
}
function hidePopover() {
  popoverWords.value = {};
  showSheet.value = false;
}
function selectAction({ label }) {
  if (label === "发送") sendText(popoverWords.value.content);
  else if (label === "复制") copyContent(popoverWords.value);
  else if (label === "添加为我的常用语") addToMine({ ...popoverWords.value });
  else if (label === "编辑常用语") {
    currentWords.value = { ...popoverWords.value };
    showWordsModal();
  } else if (label === "删除常用语") {
    removeWords(popoverWords.value._id);
  }
  hidePopover();
}

function addWords() {
  currentWords.value = {};
  showWordsModal();
}

async function removeWords(id) {
  await ElMessageBox.confirm("确认删除该常用语吗？", "提示", { type: "warning" });
  const success = await remove(id);
  if (success) {
    ElMessage.success("删除成功");
    page.value = 1;
    getList();
  }
}

function copyContent(item) {
  useCopy(item.content, "复制成功");
  hidePopover();
}

const { close, show, visible, width } = useModal(); //  选择客户弹窗
const newWords = ref({ content: "", cateId: "" });
function addToMine(item) {
  newWords.value = { content: item.content, cateId: "" };
  hidePopover();
  show();
}
async function setWords() {
  if (!newWords.value.cateId) {
    ElMessage.info("请选择常用语分类");
  } else if (!newWords.value.content || newWords.value.content.trim() === "") {
    ElMessage.info("请输入常用语内容");
  } else {
    const success = await setMyWords(newWords.value.cateId, newWords.value.content);
    if (success) close();
  }
}

function sendText(content) {
  ww.sendChatMessage({
    msgtype: "text",
    enterChat: true,
    text: { content },
  });
}

const loading = ref(false);
const cateType = ref("corp");
function changeCateType(val) {
  cateType.value = val;
}

const myCates = ref([]); //  我的常用语分类

const options = {
  getList: getCorpCommonWordCate,
  callback: handleCurrentChang,
};

const { cateList, current, changeCurrent } = useClassifyList(options);

function handleCurrentChang() {
  page.value = 1;
  getList();
}

function onDelete(id) {
  const index = myCates.value.findIndex((i) => i._id === id);
  if (index > -1) {
    myCates.value.splice(index, 1);
  }
  if (current.value._id === id) {
    current.value = { label: "全部", _id: "all" };
  }
}
async function getMyCates() {
  const { data } = await getUserCommonWordCate({
    corpId: localStorage.getItem("corpId"),
    userId: localStorage.getItem("userId"),
  });
  myCates.value = data && Array.isArray(data.list) ? [{ label: "全部", _id: "all" }, ...data.list] : [{ label: "全部", _id: "all" }];
}
function reloadList() {
  page.value = 1;
  getList();
}
async function getList() {
  loading.value = true;
  const params = {
    page: page.value,
    corpId: localStorage.getItem("corpId"),
    userId: localStorage.getItem("userId"),
    // wordsType: cateType.value,
    keyword: keyword.value,
  };
  if (cateType.value === "corp") {
    if (current.value._id === "all") {
      params.cateIds = cateList.value.map((i) => i._id);
    } else {
      const childrenIds = Array.isArray(current.value.childrenIds) ? current.value.childrenIds : [];
      const cateIds = current.value._id ? [current.value._id, ...childrenIds] : [];
      params.cateIds = cateIds;
    }
  } else {
    params.cateIds = current.value._id === "all" ? myCates.value.map((i) => i._id) : [current.value._id];
  }
  const { data } = await getCommonWordsList(params);
  const arr = Array.isArray(data.list) ? data.list : [];
  list.value = page.value === 1 ? arr : [...list.value, ...arr];
  more.value = data.pages > page.value;
  loading.value = false;
  if (page.value === 1) {
    await nextTick();
    scrollRef.value.setScrollTop(0);
  }
}

async function getUserAuthList() {
  const { success, data, message } = await getSessionArchiveUserList();
  if (success) {
    haveSessionAuth.value = data.data;
  } else {
    haveSessionAuth.value = [];
  }
}
onMounted(async () => {
  getMyCates();
  if (!publicKeys.value) getCorpSessionArchiveInfo();
  if (!externalUserId.value) getCustomer();
  getUserAuthList();
});
async function toAiService(type) {
  if (!useAi.value) return;
  if (!Array.isArray(haveSessionAuth.value)) await getUserAuthList();
  if (!haveSessionAuth.value.some((i) => i.userid === localStorage.getItem("userId"))) {
    ElMessage.error("未开通会话存档权限");
    return;
  }
  if (type === "intelligentReply") aiVisible.value = true;
  if (type === "serviceRecord") chatVisible.value = true;
}
watch(
  cateType,
  async (n) => {
    if (n === "user") {
      await getMyCates();
      current.value = myCates.value[0];
    } else {
      current.value = cateList.value[0];
    }
    page.value = 1;
    getList();
  }
  // { immediate: true }
);

watchDebounced(
  keyword,
  () => {
    page.value = 1;
    getList();
  },
  { debounce: 500 }
);

const aiVisible = ref(false);
const chatVisible = ref(false);
</script>
<style lang="scss" scoped>
.cate--active {
  color: #fff;
  background-color: var(--el-color-primary);
}

[no-radius] {
  border-radius: 0 !important;
}

.full-h-screen {
  height: calc(100vh - env(safe-area-inset-bottom, 0px));
}
</style>
<style>
.commonWordsPopover.el-popover.el-popper {
  padding: 0;
}

.chat-commonwords-dialog .el-dialog__header {
  padding: 10px 0 10px 20px;
}

.chat-commonwords-dialog .el-dialog__headerbtn {
  top: -1px;
}

.chat-commonwords-dialog footer.el-dialog__footer {
  padding-top: 10px;
  padding-bottom: 10px;
}
</style>