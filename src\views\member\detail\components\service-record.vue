<template>
  <my-layout bg-fff common-shadow>
    <layout-item>
      <div class="top-module">
        <div bg-fff class="query mt-15px">
          <el-form-item class="query__item query__item--large">
            <el-date-picker v-model="executionTime" value-format="YYYY-MM-DD" placeholder="执行时间" @change="handleChange" />
          </el-form-item>
          <el-form-item class="query__item">
            <el-select v-model="eventType" placeholder="服务类型" clearable @change="handleChange">
              <el-option v-for="item in eventTypeList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item class="query__item query__item--auto">
            <el-button type="primary" @click="handleChange">查询</el-button>
            <el-button type="primary" @click="reset">重置</el-button>
          </el-form-item>
          <el-form-item class="query__item query__item--auto">
            <el-button type="primary" @click="addServiceRecordAction()">新增服务记录</el-button>
          </el-form-item>
        </div>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <div h-full px-15>
        <el-table stripe v-loading="loading" border height="100%" :data="list">
          <el-table-column property="executionTime" label="执行时间" width="150" />
          <el-table-column property="taskType" label="服务类型"></el-table-column>
          <el-table-column property="taskContent" label="服务内容">
            <template #default="{ row }">
              <div text-ellipsis>
                <el-popover v-if="row.taskContent" placement="top-start" :width="300" trigger="hover" :content="row.taskContent">
                  <template #reference>
                    <el-text truncated class="pointer" style="transform: translateY(4px)">
                      <content-userid-transform :content="row.taskContent"></content-userid-transform>
                    </el-text>
                  </template>
                  <content-userid-transform :content="row.taskContent"></content-userid-transform>
                </el-popover>
              </div>
            </template>
          </el-table-column>
          <el-table-column property="executionTeamName" label="所属团队" />
          <el-table-column property="executorUserId" label="处理人">
            <template #default="{ row: { executorUserId } }">
              <span v-if="executorUserId === 'system'">系统自动</span>
              <ww-user v-else-if="executorUserId" :openid="executorUserId"></ww-user>
            </template>
          </el-table-column>
          <!-- <el-table-column
            property="isFeedback"
            label="客户反馈"
            width="150px"
          ></el-table-column> -->
          <el-table-column fixed="right" class-name="el-table-fixed-column--shadow" label="操作" width="180">
            <template #default="{ row }">
              <el-text type="primary" class="mr-2" pointer @click="editRecord(row)" v-if="customerType !== 'corpCustomer'">编辑</el-text>
              <el-text v-if="row.pannedEventSendFile && Object.keys(row.pannedEventSendFile).length > 0" type="primary" class="mr-2" pointer @click="read(row.pannedEventSendFile)">{{ row.eventType === "questionnaire" ? "查看问卷" : "宣教文章" }}</el-text>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </layout-main>
    <layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="currentPage" @handle-size-change="onSizeChange" @handle-current-change="onCurrentChange" />
    </layout-item>
  </my-layout>

  <create-service ref="createServiceRef" @remove="removeServiceRecord" @save="handleChange"></create-service>
  <survery-dialog :data="currentSurvery" :visible="visible" @close="visible = false" />
</template>
<script setup>
import dayjs from "dayjs";
import SurveryDialog from "@/views/system/survery/dialog.vue";
import { computed, ref, reactive } from "vue";
import { ToDoTaskStatus, ServiceType } from "@/baseData";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import { getServiceRecord, addServiceRecord, updateServiceRecord, removeRecord } from "@/api/todo";
import WwUser from "@/components/ww-user/index.vue";
import { getAnswer, getDetail } from "@/api/survery";
import { openChatWindow, isURL } from "@/utils/common";
import { ElMessage, ElMessageBox } from "element-plus";
import createService from "@/components/create-service/index.vue";
import contentUseridTransform from "@/components/content-userid-transform/index.vue";
import { teamStore } from "@/store/team";
const { allTeams } = teamStore();
const corpId = localStorage.getItem("corpId");
const { memberId, customer, memberName, customerType } = defineProps(["memberId", "customer", "memberName", "customerType"]);
const listData = ref([]);
const total = ref(0);
const pageSize = ref(10);
const currentPage = ref(1);
const visible = ref(false);
const currentSurvery = ref({});
const loading = ref(false);
const dialogVisible = ref(false);
const executionTime = ref("");
const eventType = ref("");
let selectRecordId = "";
let actionType = ref("");
const ruleForm = ref({
  taskContent: "",
  executionTime: "",
});
getList();
function onSizeChange(e) {
  pageSize.value = e;
  getList();
}
function onCurrentChange(e) {
  currentPage.value = e;
  getList();
}

const list = computed(() => {
  return listData.value.map((i) => {
    const status = ToDoTaskStatus.find((item) => item.value === i.eventStatus);
    const willBeExpired = i.expireTime ? dayjs().isAfter(dayjs(i.expireTime).subtract(2, "days")) && dayjs().isBefore(dayjs(i.expireTime)) : false;
    i.executionTime = dayjs(i.executionTime).format("YYYY-MM-DD HH:mm");
    i.executionTeamName = allTeams.find((item) => item.teamId === i.executeTeamId)?.name;
    return {
      ...i,
      willBeExpired,
      taskStatus: status ? status.label : "",
      taskType: ServiceType[i.eventType] || "",
      taskContent: `${i.taskContent} ${i.result ? `(处理结果:${i.result})` : ""}`,
    };
  });
});

const eventTypeList = Object.keys(ServiceType).map((item) => {
  return {
    label: ServiceType[item],
    value: item,
  };
});
// 获取服务记录
async function getList() {
  loading.value = true;
  const params = {
    corpId,
    customerId: memberId,
  };
  if (executionTime.value) {
    params["executionTime"] = executionTime.value;
  }
  if (eventType.value) {
    params["eventType"] = eventType.value;
  }
  const { success, data } = await getServiceRecord(currentPage.value, pageSize.value, params);
  if (success) {
    const { data: tableData = [], total: count } = data;
    listData.value = tableData;
    total.value = count;
  }
  loading.value = false;
}

async function toChat(item) {
  openChatWindow(item.customerId, item.externalUserId);
}
async function dialogSubmit(formEl) {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      if (actionType.value === "add") {
        await addRecord();
      } else {
        await updateRecord();
      }
    }
  });
}

// 新增服务小结类型的待办, 待办的类型事已办结
async function addRecord() {
  const params = {
    taskContent: ruleForm.value.taskContent,
    executionTime: new Date(ruleForm.value.executionTime).getTime(),
    customerId: memberId,
    executeTeamId: customer.teamId,
    customerUserId: customer.externalUserId,
    creatorUserId: localStorage.getItem("userId"), // 创建人
    executorUserId: localStorage.getItem("userId"), // 执行人
    corpId: localStorage.getItem("corpId"),
    eventType: "serviceSummary",
    customerName: memberName,
  };
  await addServiceRecord(params);
  dialogVisible.value = false;
  getList();
}

async function updateRecord() {
  const params = {
    taskContent: ruleForm.value.taskContent,
    executionTime: new Date(ruleForm.value.executionTime).getTime(),
  };
  let res = await updateServiceRecord(selectRecordId, params);
  dialogVisible.value = false;
  getList();
}

// 删除服务记录
async function removeRecordAction(id) {
  ElMessageBox.confirm("是否删除该服务记录?", "提示", { type: "warning" }).then(async () => {
    let { success, message } = await removeRecord(id);
    if (success) {
      ElMessage.success("删除成功");
      getList();
    } else {
      ElMessage.error(message);
    }
  });
}
function removeServiceRecord() {
  currentPage.value = 1;
  getList();
}
// 编辑服务记录
async function editRecord(item) {
  createServiceRef.value.open("edit", customer, item);
}
async function handleChange() {
  getList();
}

async function reset() {
  eventType.value = "";
  executionTime.value = "";
  getList();
}

let createServiceRef = ref("");
function addServiceRecordAction() {
  createServiceRef.value.open("add", customer);
}
async function read(item) {
  if (item.type === "article") {
    isURL(item.url) && window.open(item.url);
  } else if (item.type === "questionnaire") {
    await getSurvery(item);
    visible.value = true;
  }
}

async function getSurvery(item) {
  const { surveryId, answerId } = item;
  if (answerId) {
    const { data } = await getAnswer(surveryId, memberId, answerId);
    if (data && data.record && data.record.submitTime) {
      currentSurvery.value = data && data.record ? data.record : {};
      return;
    }
  }
  getSurveryDetial(surveryId);
}

async function getSurveryDetial(id) {
  const corpId = localStorage.getItem("corpId");
  const { success, data, message } = await getDetail(corpId, id);
  currentSurvery.value = data && data.data ? data.data : {};
}
</script>
<style scoped lang="scss">
:deep(.el-table__inner-wrapper) {
  height: 100% !important;
}

.query {
  display: flex;
  flex-wrap: wrap;
  padding: 0 15px;

  .query__item {
    width: 180px;
    margin-bottom: 15px;
    margin-right: 10px;

    @at-root &--large {
      width: 240px;
    }

    @at-root &--small {
      width: 140px;
    }

    @at-root &--small {
      width: 140px;
    }

    @at-root &--auto {
      width: auto;
    }

    @at-root &:last-of-type {
      margin-left: auto;
      margin-right: 0;
    }
  }
}
</style>
