<template>
  <div class="h-screen w-full">
    <div class="flex justify-between h-60px p-10px">
      <div v-if="selectHistoryUser">{{ selectHistoryUser?.name }}/{{ selectHistoryUser?.sex }}/{{ selectHistoryUser?.age }}</div>
      <div class="flex items-center">
        <div v-if="isShowIllnessSummary" class="flex-shrink-0 border border-blue-500 rounded px-10px py-4px flex-shrink-0 cursor-pointer text-blue-500 text-14px" @click="showIllnessSummary">本次病情概要</div>
        <div v-if="diagnosisExist" class="ml-5px flex-shrink-0 border border-blue-500 rounded px-10px py-4px flex-shrink-0 cursor-pointer text-blue-500 text-14px" @click="showDiagnosisDrawer()">预览本次医嘱</div>
      </div>
    </div>
    <el-scrollbar height="100%" class="bg-white">
      <div class="p-36px">
        <template v-for="i in messageList" :key="i._id">
          <div class="text-gray-400 text-12px leading-4 py-10px text-center mx-auto w-4/5" v-if="showTime(i)">{{ formatTime(i.EventTime) }}</div>
          <div v-for="item in i.MsgBody">
            <customeMessageCustom v-if="item.MsgType === 'TIMCustomElem'" :messageItem="transformCustomMessage(item.MsgContent)"></customeMessageCustom>
            <div v-if="item.MsgType === 'TIMTextElem'">
              <message-me v-if="i.From_Account === doctorCode" :text="item.MsgContent.Text" />
              <message-other v-else :text="item.MsgContent.Text" />
            </div>
          </div>
        </template>
      </div>
    </el-scrollbar>
  </div>
  <illness-summary :order="selectHistoryUser" :visible="summaryVisible" @close="summaryVisible = false" />
  <diagnosis-drawer :canEdit="false" :order="selectHistoryUser" :visible="visible" @close="visible = false" />
</template>
<script setup>
import { onMounted, ref, watch, inject, computed } from "vue";
import customeMessageCustom from "../../../TUIKit/customComponents/custome-message-custom/index.vue";
import illnessSummary from "../chat-home/illness-summary/illness-summary.vue";
import diagnosisDrawer from "../chat-home/diagnosis-drawer/diagnosis-drawer.vue";
import dayjs from "dayjs";
import messageMe from "./message-me.vue";
import messageOther from "./message-other.vue";
import { getChatRecord, orderHasDiagnosis } from "@/api/hlw";
const doctorCode = sessionStorage.getItem("doctorNo");
const summaryVisible = ref(false);
const visible = ref(false);
function showIllnessSummary() {
  summaryVisible.value = true;
}

function showDiagnosisDrawer() {
  visible.value = true;
}
const formatTime = (time) => {
  const now = dayjs();
  const messageTime = dayjs(time);

  if (now.isSame(messageTime, "day")) {
    return messageTime.format("HH:mm");
  } else if (now.isSame(messageTime, "year")) {
    return messageTime.format("MM-DD");
  } else {
    return messageTime.format("YYYY-MM-DD");
  }
};
const props = defineProps({
  selectHistoryUser: {
    type: Object,
    default: null,
  },
});
const messageList = ref([]);
const isShowIllnessSummary = computed(() => props.selectHistoryUser && props.selectHistoryUser._id);

// 添加变量跟踪上一次显示的日期
const lastDisplayedDate = ref(null);

function showTime(item) {
  const msg = item.MsgBody[0];
  if ((msg.MsgType === "TIMCustomElem" && msg.MsgContent.Ext) || (msg.MsgType === "TIMTextElem" && msg.MsgContent.Text)) {
    // 检查当前消息日期是否与最后显示的日期相同
    const currentDate = dayjs(item.EventTime).format('YYYY-MM-DD');
    
    if (currentDate !== lastDisplayedDate.value) {
      // 更新最后显示的日期并显示时间
      lastDisplayedDate.value = currentDate;
      return true;
    }
    return false;
  }
  return true;
}

// 转换消息类型
function transformCustomMessage(message) {
  return {
    payload: {
      data: message.Data,
      extension: message.Ext,
    },
  };
}

const diagnosisExist = ref(false);

async function queryDiagnosisExist() {
  if (props.selectHistoryUser.orderId && props.selectHistoryUser.patientId) {
    const res = await orderHasDiagnosis({
      orderId: props.selectHistoryUser.orderId,
      patientId: props.selectHistoryUser.patientId,
    });
    diagnosisExist.value = res.data && typeof res.data.exist === "boolean" ? res.data.exist : false;
  } else {
    diagnosisExist.value = true;
  }
}

// 获取历史聊天记录
const getHistoryChatRecord = async () => {
  const { data, success } = await getChatRecord({
    doctorNo: sessionStorage.getItem("doctorNo"),
    orderId: props.selectHistoryUser?.orderId,
  });
  if (success) {
    messageList.value = data.data;
    // 重置最后显示的日期
    lastDisplayedDate.value = null;
  }
};

watch(
  () => props.selectHistoryUser,
  (value) => {
    if (value) {
      getHistoryChatRecord();
      queryDiagnosisExist();
    }
  },
  { deep: true, immediate: true }
);
</script>
<style lang="scss" scoped></style>
