import { inject, ref, watch } from 'vue';

export default function useLockScrollbar() {
  const atBottom = inject('main-scrollbar-at-bottom', false); // 侧边栏主滚动条是否位于底部状态
  const trigger = inject('side-bar-event-trigger'); 

  const atTop = ref(true); // 子元素滚动条是否处于顶部状态
  const lock = ref(true);
  function handleScroll(data) {
    atTop.value = data.atTop;
  }
  watch([atBottom, atTop], ([atBottom, atTop]) => {
    if (atBottom) { //  侧边栏主滚动条滚到底部
      lock.value = false;
      if (atTop) {
        trigger.$invoke('unlock-sidebar-scroll') // 解锁侧边栏主滚动条
      } else {
        trigger.$invoke('lock-sidebar-scroll') // 锁定侧边栏主滚动条
      }
    } else {
      lock.value = true;
      trigger.$invoke('unlock-sidebar-scroll') // 解锁侧边栏主滚动条
    }
  })

  return { lock, handleScroll }
}