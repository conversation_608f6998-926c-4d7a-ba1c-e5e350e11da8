<template>
  <div class="flex-shrink-0 mx-5px text-gray-500">包含任意</div>
  <el-popover placement="top-start" :offset="0" :width="360" trigger="click">
    <template #reference>
      <div class="flex-shrink-0 flex items-center ml-5px h-40px cursor-pointer">
        <div color-normal class="mx-5px max-w-360px truncate" :class="sourceStr ? '':'text-gray-500'">
          {{sourceStr ||'请选择'}}
        </div>
        <el-icon class="flex-shrink-0" :size="18">
          <CaretBottom />
        </el-icon>
      </div>
    </template>
    <source-popover v-model="source" />
  </el-popover>
</template>
<script setup>
import { computed } from 'vue';
import { useVModel } from '@vueuse/core';
import SourcePopover from './customer-source-popover.vue';

const emit = defineEmits(['update:modelValue'])
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})
const data = useVModel(props, 'modelValue', emit);
const source = computed({
  get: () => Array.isArray(data.value) ? data.value : [],
  set: (val) => { data.value = val }
})
const sourceStr = computed(() => source.value.join(','))

</script>
<style lang="scss" scoped></style>
