<template>
  <el-select v-model="type" class="w-140px flex-shrink-0 ml-5px" placeholder="请选择" @change="change">
    <el-option v-if="typeMap.before" label="前" value="before" />
    <el-option v-if="typeMap.after" label="后" value="after" />
    <el-option v-if="typeMap.sameDay" label="当天" value="sameDay" />
  </el-select>
  <number-input v-if="type === 'before' || type === 'after'" v-model:value="days" class="ml-5px" :min="1" :max="364"
    unit="天" />
</template>
<script setup>
import { computed, ref, watch } from 'vue';
import { useVModel } from '@vueuse/core';
import numberInput from './number-input.vue';

const emit = defineEmits(['update:type', 'update:days'])
const props = defineProps({
  valueType: { type: String, default: '' },
  types: { type: Array, default: () => ['before', 'sameDay'] },
  days: {
    type: [Number, String],
    default: ''
  },
})
const typeMap = computed(() => {
  return props.types.reduce((acc, cur) => {
    acc[cur] = true
    return acc
  }, {})
})

const type = ref('')
const days = useVModel(props, 'days', emit);

function change() {
  if (type.value == 'sameDay') {
    days.value = 0
  } else {
    days.value = 1
  }
}
watch(days, n => {
  if (n === 0 || n === '0') {
    type.value = 'sameDay'
  } else if (n > 0 && props.valueType === 'before') {
    type.value = 'before'
  } else if (n > 0 && props.valueType === 'after') {
    type.value = 'after'
  }

}, { immediate: true })



</script>
