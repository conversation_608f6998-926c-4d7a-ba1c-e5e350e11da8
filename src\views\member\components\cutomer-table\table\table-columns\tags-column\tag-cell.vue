<template>
  <div ref="cellRef" class="w-full flex items-center flex-nowrap">
    <div class="h-20px overflow-hidden">
      <div class="flex flex-wrap">
        <customer-tag custom v-for="(tagId, idx) in showTagIds" :key="`show_${tagId}`" :tag-id="tagId">
          <template #default="{ tagName }">
            <div
              class="mb-4px max-w-full h-20px text-12px leading-20px px-10px rounded truncate text-white bg-[#FC8352]"
              :class="idx > 0 ? 'ml-4px' : ''">
              {{ tagName }}
            </div>
          </template>
        </customer-tag>
      </div>
    </div>
    <el-popover ref="popover" :disabled="!showMore" placement="right" :width="200" trigger="click">
      <template #reference>
        <div ref="moreRef" class="flex-shrink-0 ml-4px hover:text-blue-500 hover:font-semibold"
          :class="showMore ? 'opacity-100 cursor-pointer' : ' opacity-0'">...</div>
      </template>
      <div class="flex flex-wrap">
        <customer-tag custom v-for="tagId in tagIds" :key="`popover_${tagId}`" :tag-id="tagId">
          <template #default="{ tagName }">
            <div
              class="mr-4px mb-4px max-w-full h-20px text-12px leading-20px px-10px rounded truncate text-white bg-[#FC8352]">
              {{ tagName }}
            </div>
          </template>
        </customer-tag>
      </div>
    </el-popover>
  </div>
  <div v-if="loading" class="h-20px overflow-hidden">
    <div class="flex flex-nowrap">
      <customer-tag custom v-for="(tagId, idx) in tagIds" :key="`calc_${tagId}`" :tag-id="tagId">
        <template #default="{ tagName }">
          <div :ref="(el) => (itemsRef[idx] = el)" :title="tagId"
            class="mb-4px max-w-full flex-shrink-0 h-20px text-12px leading-20px px-10px rounded truncate text-white bg-[#FC8352]"
            :class="idx > 0 ? 'ml-4px' : ''">
            {{ tagName }}
          </div>
        </template>
      </customer-tag>
    </div>
  </div>
</template>
<script setup>
import { computed, nextTick, ref, watch } from "vue";
import { useElementBounding } from "@vueuse/core";
import customerTag from "@/components/customer-tag/customer-tag.vue";

const props = defineProps({
  tagIds: {
    type: Array,
    default: () => [],
  },
});
const loading = ref(false);
const cellRef = ref(null);
const moreRef = ref(null);
const index = ref(0);
const { width: cellElWidth } = useElementBounding(cellRef);
const { width: moreElWidth } = useElementBounding(moreRef);
const itemsRef = ref([]);
const showMore = ref(false);
const showTagIds = computed(() => (Array.isArray(props.tagIds) ? props.tagIds.slice(0, index.value) : []));
function calulate() {
  const maxWidth = cellElWidth.value - moreElWidth.value - 4; // 4px for margin
  showMore.value = false;
  index.value = props.tagIds.length;
  for (let i = 0; i < itemsRef.value.length; i++) {
    const el = itemsRef.value[i];
    const offsetLeft = el && el.offsetLeft > 0 ? el.offsetLeft : 0;
    const offsetWidth = el && el.offsetWidth > 0 ? el.offsetWidth : 0;
    if (offsetLeft + offsetWidth > maxWidth) {
      showMore.value = i <= itemsRef.value.length - 1;
      index.value = Math.max(0, i);
      break;
    }
  }
}
watch(
  () => props.tagIds,
  async (n) => {
    if (n && n.length) {
      loading.value = true;
      await nextTick();
      calulate();
    } else {
      showMore.value = false;
    }
    loading.value = false;
  },
  { immediate: true }
);
</script>
<style lang="scss" scoped></style>
