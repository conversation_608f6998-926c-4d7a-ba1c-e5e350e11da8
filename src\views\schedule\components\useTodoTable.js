import { useRouter } from "vue-router";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import { ToDoEventType, ToDoTaskStatus, ServiceType } from "@/baseData";
import { memberStore } from "@/store/member";
import { openChatWindow } from "@/utils/common";

export default function useTodoTable() {
  const router = useRouter();
  const store = memberStore();
  const { wxContact } = storeToRefs(store);

  function formatTime(str) {
    return str && dayjs(str).isValid() ? dayjs(str).format("YYYY-MM-DD HH:mm") : "";
  }

  function formatList(list) {
    return list.map((i) => {
      const status = ToDoTaskStatus.find((item) => item.value === i.eventStatus);

      const item = {
        ...i,
        taskStatus: status ? status.label : "",
        taskType: ServiceType[i.eventType] || "",
        executionTime: formatTime(i.executionTime),
        createTime: formatTime(i.createTime),
        planTime: dayjs(i.planTime).format("YYYY-MM-DD"),
        expireTime: dayjs(i.expireTime).format("YYYY-MM-DD"),
        plannedExecutionTime: dayjs(i.plannedExecutionTime).format("YYYY-MM-DD"),
        endTime: formatTime(i.endTime),
      };
      item.willBeExpired = item.expireTime ? dayjs().isAfter(dayjs(item.expireTime).subtract(2, "days")) && dayjs().isBefore(dayjs(item.expireTime)) : false;
      return item;
    });
  }

  function showDetail(row) {
    router.push({ name: "SCHEDULEDETAIL", params: { id: row._id } });
  }

  function toChat(item) {
    if (wxContact.value[item.customerUserId]) {
      openChatWindow(item.customerId, item.customerUserId, "", { name: "todo", time: new Date().getTime() });
    } else if (wxContact.value[item.externalUserId]) {
      openChatWindow(item.customerId, item.externalUserId, "", { name: "todo", time: new Date().getTime() });
    }
  }

  return { formatList, showDetail, toChat, wxContact };
}
