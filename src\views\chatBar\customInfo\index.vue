<template>
  <my-layout class="customInfo" v-loading="loading">
    <div class="noDataBox" v-if="memberList.length === 0 && !loading">
      <img src="@/assets/zanwushuju.png" mode="scaleToFill" class="noData" />
      <div class="text">暂无数据</div>
      <div v-if="teamList.length" class="button" @click="authVisible = true">点击发送建档链接</div>
      <!-- <div v-for="item in teamList" class="button" @click="sendLink(item)" :key="item._id">
        点击发送{{ item.name }}服务授权
      </div> -->
    </div>
    <template v-else-if="!loading">
      <layout-item>
        <div bg-fff class="head-bar">
          <div class="head-bar__user">
            <customer-bar :member="selectmember" />
          </div>
          <div class="head-bar__toggle" @click="memberDrawerVisible = true">
            <el-icon class="icon-switch">
              <Switch />
            </el-icon>
            <div>切换人员</div>
          </div>
        </div>
      </layout-item>
      <layout-main>
        <div class="card baseInfo card--nomargin">
          <div class="box box--small">
            <div class="title">电话</div>
            <div flex-grow>
              <div class="mobile">
                <div class="t_2" :class="selectmember.mobile ? '' : 'opacity-0'">
                  {{ selectmember && selectmember.mobile ? selectmember.mobile : "" }}
                  本人
                </div>
                <div pr-15 class="t_3 more pointer flex items-center justify-end" @click="goCustomerDetail()">
                  <span>查看更多</span>
                  <el-icon><DArrowRight /></el-icon>
                </div>
              </div>
              <template v-if="selectmember.assistantByMobile">
                <div class="mobile" v-for="item in selectmember.assistantByMobile">
                  <div class="t_2">{{ item.mobile }} {{ item.note }}</div>
                </div>
              </template>
            </div>
          </div>
          <div class="box box--normal">
            <div flex-grow>
              <div class="flex-center space-between" pr-15>
                <div class="title">标签</div>
                <el-icon color="#006eff" class="pointer" size="16" @click="editTags()">
                  <Edit />
                </el-icon>
              </div>
              <div>
                <el-tag v-for="(name, idx) in id2Tags" :key="'tag_' + idx" class="tag" type="primary" effect="dark">
                  {{ name }}
                </el-tag>
              </div>
            </div>
          </div>
          <div class="box box--normal">
            <div flex-grow>
              <div class="flex-center space-between" pr-15>
                <div class="title">备注</div>
                <el-icon color="#006eff" class="pointer" size="16" @click="editNots()">
                  <Edit />
                </el-icon>
              </div>
              <div class="content">{{ selectmember.notes }}</div>
            </div>
          </div>
        </div>
        <div class="card baseInfo">
          <div class="box" style="border-bottom: none">
            <div class="title">服务待办</div>
            <div class="t_2 more pointer" @click="openRouteHref({ name: 'SCHEDULE' })">查看更多>></div>
          </div>
          <template v-if="todos.length">
            <div class="box box--nopt" v-for="item in todos" :key="item._id">
              <el-card class="box-card">
                <div class="card-row space-between">
                  <div class="taskType">{{ item.taskType }}</div>
                  <div>{{ item.showDate }}</div>
                </div>
                <div class="card-row card-row--content">
                  {{ item.taskContent }}
                </div>
                <div text-right>
                  <el-button v-if="item.sendContent" class="send-btn" type="primary" plain style="background-color: #fff" size="small" @click="sendMessage(item)">点击发送</el-button>
                  <el-button class="send-btn" type="primary" plain style="background-color: #fff" size="small" @click="finishTodo('closed', item)">取消任务</el-button>
                  <el-button class="send-btn" type="primary" plain style="background-color: #fff" size="small" @click="finishTodo('treated', item)">完成任务</el-button>
                </div>
              </el-card>
            </div>
          </template>
          <empty-data v-else :top="0" title="暂无待办" padding="0 0 15px" text-top="10px" :image-width="80"></empty-data>
        </div>
        <div class="card baseInfo">
          <div class="box" style="border-bottom: none">
            <div class="title">回访计划</div>
            <div class="t_2 more pointer">查看更多>></div>
          </div>
          <template v-if="false">
            <div class="box box--nopt">
              <el-card class="box-card"></el-card>
            </div>
          </template>
          <empty-data v-else :top="0" title="暂无计划" padding="0 0 15px" text-top="10px" :image-width="80"></empty-data>
        </div>
        <div class="card baseInfo">
          <div class="box" style="border-bottom: none">
            <div class="title">服务记录</div>
            <div class="t_2 more pointer" @click="addService()">
              <el-icon>
                <Plus />
              </el-icon>
              添加记录
            </div>
          </div>
          <template v-if="activities.length">
            <div class="box box--nopt">
              <el-timeline class="pl-10px">
                <el-timeline-item v-for="item in activities" :key="'service_' + item._id" :timestamp="item.timeTitle" placement="top">
                  <div flex font-14 class="pt-10px">
                    <div class="min-w-60px mr-5px" flex-shrink-0 color-666>跟进人：</div>
                    <div>
                      <ww-user v-if="item.executorUserId" :openid="item.executorUserId"></ww-user>
                    </div>
                  </div>
                  <div flex font-14 class="pt-10px">
                    <div class="min-w-60px mr-5px" flex-shrink-0 color-666>服务内容：</div>
                    <div>{{ item.taskContent }}</div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </div>
          </template>
          <empty-data v-else :top="0" title="暂无记录" padding="0 0 15px" text-top="10px" :image-width="80"></empty-data>
        </div>
      </layout-main>
    </template>
  </my-layout>
  <el-dialog v-model="isEditNots" title="修改备注" width="300px" center>
    <el-input type="textarea" v-model="nots" rows="4" />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="isEditNots = false">取消</el-button>
        <el-button type="primary" @click="saveEditNots">确定</el-button>
      </span>
    </template>
  </el-dialog>
  <member-drawer :visible="memberDrawerVisible" :members="memberList" :teams="teamList" @close="memberDrawerVisible = false" @select="select($event)" @send="authVisible = true" />
  <tag-modal :member-id="selectmember._id" :tag-ids="selectmember.tagIds" :visible="visible" @close="visible = false" @change="changeTagIds"></tag-modal>
  <auth-modal :teams="teamList" :visible="authVisible" @close="authVisible = false" @send="sendLink"></auth-modal>
  <add-service-vue :member="selectmember" :teamId="selectmember._id" :visible="addServiceVisible" @close="addServiceVisible = false" @addSuccess="getServiceRecordAction()" />
  <todo-modal :todo="currentTodo" :type="finishType" :visible="todoVisible" @change="getTodo()" @close="todoVisible = false" />
</template>
<script setup>
import { ref, computed, onBeforeMount } from "vue";
import { useRouter, useRoute } from "vue-router";
import { createEvents, getToDoList, updateTodo, getServiceRecord } from "@/api/todo";
import { configStore } from "@/store/config";
import { getCurExternalContact, sendChatMessage } from "@/utils/jssdk.js";
import { jsSDKConfig } from "@/utils/jssdk";
import { envjudgwxWork, getCorpTags } from "@/utils/common";
import { ToDoEventType, ServiceType } from "@/baseData";
import dayjs from "dayjs";
import addServiceVue from "./add-service";
import AuthModal from "./auth-modal";
import TodoModal from "./todo-modal";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import EmptyData from "@/components/empty-data";
import CustomerBar from "../components/customer-bar";
import MemberDrawer from "../components/member-drawer";
import TagModal from "./tag-modal";
import { getTeamList } from "@/api/corp";
import { getNameByexternalUserId } from "@/utils/common";
import WwUser from "@/components/ww-user/index.vue";
import { getSelectedChatMemberUrl, getCustomerInfoById } from "@/api/member";

let externalUserId = ref("wmLgjyawAAQzaoYIGD668-Mg8jMWKpgw");
let memberList = ref([]);
let selectmember = ref({});
let isEditNots = ref(false);
let nots = ref("");
let teamList = ref([]);
const loading = ref(true);
//根据外部联系人userID
onBeforeMount(async () => {
  getTeamData();
  if (envjudgwxWork()) {
    await jsSDKConfig();
    let res = await getCurExternalContact();
    externalUserId.value = res;
  }
  // 同时获取到聊天人和当前客户建档成员
  const [[chatMember], members] = await Promise.all([getSelectedChatMember(), getMemberInfo()]);
  memberList.value = members;
  if (chatMember) {
    // watchMemberChange();
  } else {
    members.length > 0 && selectMember(members[0]);
  }
});
async function getTeamData() {
  const { data } = await getTeamList();
  teamList.value = Array.isArray(data.data) ? data.data : [];
}
// async function watchMemberChange() {
//   await cloudbase
//     .database()
//     .collection("selected-chat-member")
//     .where({ externalUserId: externalUserId.value })
//     .watch({
//       onChange: (snapshot) => {
//         // 处理获取到的数据
//         snapshot.docChanges.forEach(async (change) => {
//           if (["add", "update", "init"].includes(change.dataType)) {
//             const { memberId } = change.doc;
//             const members = await getMemberInfo();
//             let item = members.find((i) => i._id === memberId);
//             if (!item && members.length > 0) {
//               item = members[0];
//             }
//             selectMember(item);
//           }
//         });
//       },
//     });
// }

async function getSelectedChatMember() {
  let { data } = await getSelectedChatMemberUrl({ externalUserId: externalUserId.value });
  return data.data;
}

async function getMemberInfo() {
  // 根据externalUserID 获取到外部联系人信息
  const { data } = await getCustomerInfoById(localStorage.getItem("userId"), {
    externalUserId: externalUserId.value,
  });
  loading.value = false;
  return data.data;
}

async function selectMember(item, idx) {
  if (!item) return;
  selectmember.value = item;
  if (item) {
    getTodo();
    getServiceRecordAction();
  } else todos.value = [];
}
function editNots() {
  nots.value = selectmember.value.notes;
  isEditNots.value = true;
}
const visible = ref(false);
const id2Tags = computed(() => {
  const { list } =  getCorpTags(selectmember.value.tagIds || []);
  
  return list.map((i) => (i.options && i.options[0] ? i.options[0].name : "")).filter(Boolean);
});

function editTags() {
  visible.value = true;
}

function changeTagIds(tagIds) {
  selectmember.value.tagIds = tagIds;
}
const config = configStore();
const authVisible = ref(false);
async function sendLink(item) {
  // const toDoEventId = await createToDoforJD(item);
  const corpUserID = localStorage.getItem("userId");
  const corpId = localStorage.getItem("corpId");
  const url = `${import.meta.env.VITE_PATIENT_PAGE_BASE_URL}pages/external/memberManage/index?qrid=${item.qrid || ""}&teamId=${item.teamId}&externalUserId=${externalUserId.value}&corpUserID=${corpUserID}&corpId=${corpId}`;
  const desc = `确定家庭成员与${item.name}建立服务关系`;
  sendLinkMessage(desc, "服务授权", url);
}
// 生成建档待办
async function createToDoforJD(team) {
  const { data } = await getNameByexternalUserId(externalUserId.value);
  let params = {
    eventType: "remindFiling",
    customerId: "",
    externalUserId: externalUserId.value,
    customerName: "",
    weixinName: data.name,
    executeTeamId: team.teamId,
    executName: team.name,
    executorUserId: localStorage.getItem("userId"),
    corpId: localStorage.getItem("corpId"),
  };
  const { data: event } = await createEvents(params);
  return event && event.id;
}
async function sendMessage(item) {
  let { _id, sendContent, eventType, pannedEventSendFile } = item;
  const corpId = localStorage.getItem("corpId");
  if (eventType === "ContentReminder" && pannedEventSendFile.type === "article") {
    sendLinkMessage(sendContent, "温馨提示", pannedEventSendFile.url);
  } else if (eventType === "questionnaire") {
    const url = `${import.meta.env.VITE_PATIENT_PAGE_BASE_URL}pages/survery/fill?corpId=${corpId}&surveryId=${pannedEventSendFile.surveryId}&memberId=${selectmember.value._id}&unionid=${selectmember.value.unionid || ""}`;
    sendLinkMessage(sendContent, "问卷调查", url);
  } else {
    sendChatMessage({ type: "text", desc: sendContent });
  }
  updateEvent(_id);
}

function sendLinkMessage(desc, title, url) {
  sendChatMessage({
    desc,
    title,
    url,
    type: "news",
    imgUrl: "https://796f-youcan-clouddev-1-8ewcqf31dbb2b5-**********.tcb.qcloud.la/other/%E6%A1%A3%E6%A1%88.png?sign=ad25044f43b599712bb290e486bd5038&t=**********",
  });
}

//更新已执行状态 状态变为办结
async function updateEvent(id) {
  await updateTodo(id, { eventStatus: "treated" });
  getTodo();
  getServiceRecordAction();
}

async function saveEditNots() {
  await updateMember(selectmember.value._id, { notes: nots.value });
  isEditNots.value = false;
  selectmember.value.notes = nots.value;
}

const todos = ref([]);
async function getTodo() {
  const { _id: memberId } = selectmember.value;
  const { data } = await getToDoList(1, 99, {
    executorUserId: localStorage.getItem("userId"),
    customerId: memberId,
    eventStatus: "untreated",
  });
  const list =
    data && Array.isArray(data.data)
      ? data.data.map((i) => {
          const showDate = i.plannedExecutionTime ? dayjs(i.plannedExecutionTime).format("YYYY年MM月DD日") : "";
          return { ...i, taskType: ToDoEventType[i.eventType] || "", showDate };
        })
      : [];
  todos.value = list;
}

const memberDrawerVisible = ref(false);
function select(member) {
  memberDrawerVisible.value = false;
  selectMember(member);
}

const router = useRouter();
const route = useRoute();
function goCustomerDetail() {
  openRouteHref({ name: "DETAIL", params: { id: selectmember.value._id } });
}

function openRouteHref(params = {}) {
  const nextRoute = router.resolve(params);
  const href = window.location.href.replace(route.fullPath, nextRoute.fullPath);
  let url = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=wwc1c7baebf62eab4a&redirect_uri=${encodeURIComponent(href)}&response_type=code&scope=snsapi_privateinfo&state=STATE#wechat_redirect`;
  window.open(url, "_target");
}

const activities = ref([]);
async function getServiceRecordAction() {
  const { data } = await getServiceRecord(1, 99, {
    corpId: localStorage.getItem("corpId"),
    customerId: selectmember.value._id,
  });
  activities.value = Array.isArray(data.data)
    ? data.data
        .map((i) => ({
          ...i,
          timeTitle: `${dayjs(i.executionTime).format("YYYY年MM月DD日 HH:mm")} ${ServiceType[i.eventType]}`,
        }))
        .sort((a, b) => b.executionTime - a.executionTime)
    : [];
}
const addServiceVisible = ref(false);
function addService() {
  addServiceVisible.value = true;
}
const todoVisible = ref(false);
const finishType = ref("");
const currentTodo = ref({});
function finishTodo(type, todo) {
  currentTodo.value = todo;
  finishType.value = type;
  todoVisible.value = true;
}
</script>
<style scoped lang="scss">
.customInfo {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: #f5f5f5;
  font-size: 14px;
  border-radius: 0;
}

.noDataBox {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  margin-bottom: 140px;

  .noData {
    width: 120px;
    margin-bottom: 10px;
  }

  .text {
    margin-bottom: 40px;
  }

  .button {
    background: #328dff;
    border-radius: 4px;
    color: #fff;
    padding: 8px 15px;
    margin-bottom: 10px;
  }
}

.memberList {
  display: flex;
  // overflow-x: auto;
  padding: 10px;

  .member {
    min-width: 100px;
    margin-right: 10px;
    padding: 5px 10px;
    flex-shrink: 0;
    cursor: pointer;
    position: relative;
  }

  .active {
    background: #328dff14;
    border: 1px solid #328dff;
  }

  .active::after {
    content: "";
    position: absolute;
    right: 0;
    bottom: 0;
    background-image: url("@/assets/isSelectIcon.png");
    background-size: 24px 24px;
    width: 24px;
    height: 24px;
  }

  .item {
    display: flex;
    align-items: center;
  }

  .item_1 {
    display: flex;
    margin-top: 10px;
  }

  .name {
    font-weight: 600;
    font-size: 16px;
    color: #333333;
  }

  .icon {
    width: 16px;
    margin-left: 5px;
  }

  .relationship {
    color: #666666;
    padding-left: 20px;
  }

  .age {
    color: #666666;
  }
}

.card {
  background: #ffffff;
  box-shadow: 0 2px 12px 0 #88888824;
  // border-radius: 8px;

  @at-root &.card--nomargin {
    margin-left: 0;
    margin-right: 0;
    border-radius: 0;
    padding-right: 0;
  }
}

.baseInfo {
  padding: 0 15px;
  margin: 0 0 8px;

  .box {
    padding: 10px 0;
    display: flex;

    @at-root &--small {
      padding-top: 5px;
      padding-bottom: 0;

      .title {
        padding-top: 10px;
      }
    }

    @at-root &--nopt {
      padding-top: 0;
    }

    @at-root &--normal {
      padding: 12px 0;
    }

    .title {
      flex-shrink: 0;
      padding-right: 5px;
      color: rgba(0, 0, 0, 0.9);
      min-width: 50px;
      font-weight: 600;
    }

    .content {
      // width: 220px;
      margin-top: 8px;
      padding-right: 20px;
      white-space: pre-wrap;
    }

    .tag {
      margin: 10px 5px 0 0;
    }

    .mobile {
      display: flex;
      align-items: center;
      padding: 10px 0;
      // transform: translateY(-4px);

      .t_3 {
        padding-left: 10px;
      }
    }

    .mobile + .mobile {
      border-top: 1px solid #eee;
    }
  }

  .box:not(:last-child) {
    border-bottom: 1px solid #eeeeee;
  }

  .flex-center {
    display: flex;
    align-items: center;
  }
}

.more {
  flex-grow: 1;
  text-align: right;
  color: #409eff;
}

.box-card {
  flex-grow: 1;
  background-color: #f6f9fe;
  box-shadow: none !important;
  border: none;

  .taskType {
    // color: #fff;
    font-weight: 600;
    font-size: 14px;
    border-radius: 6px;
  }

  .card-row {
    display: flex;
    align-items: center;
    margin-bottom: 6px;
    color: rgba(0, 0, 0, 0.9);

    @at-root &--content {
      color: #9c9ea2;
    }
  }
}

.head-bar {
  position: static;
  top: 0;
  left: 0;
  display: flex;
  padding: 5px 0 5px 15px;
  margin-bottom: 8px;

  @at-root &__user {
    flex-grow: 1;
  }

  @at-root &__toggle {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    padding: 0 10px;
    margin: 15px 0 15px 10px;
    border-left: 1px solid #eee;
    font-size: 14px;
    color: #006eff;
    text-align: center;

    .icon-switch {
      margin-bottom: 5px;
    }
  }
}

[pr-15] {
  padding-right: 15px;
}

.space-between {
  justify-content: space-between;
}

:deep(.el-tag--dark.el-tag--primary) {
  background-image: linear-gradient(180deg, #4391f5 60%, #70c4e8); // background: red
  border-color: transparent !important;
}

:deep(.el-card__body) {
  padding: 10px 15px;
}

.el-button.send-btn {
  border-color: var(--el-button-text-color);
  font-weight: 600;
}

.el-button.send-btn:focus,
.el-button.send-btn:hover {
  color: var(--el-button-text-color);
}
</style>
