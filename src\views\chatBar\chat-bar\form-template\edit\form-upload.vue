<template>
  <div class="px-15px py-6px" border-bottom>
    <el-form-item :class="required ? 'is-required' : ''" :label="name"></el-form-item>
    <div class="flex flex-wrap mt-10px">
      <div v-for="(item, idx) in list" :key="item.name + '_' + idx" class="mr-15px mb-10px relative w-80px h-80px">
        <el-image v-if="item.isImage" class="w-80px h-80px" :src="item.base64 || item.url" :zoom-rate="1.2"
          :max-scale="7" :min-scale="0.2" fit="cover" @click.stop="previewImage(item)" />
        <img v-else-if="item.isPdf" class="w-80px h-80px" src="@/assets/pdf.svg" alt="item.name" />
        <img v-else class="w-80px h-80px" src="@/assets/svg-icons/icon-bt-file.svg" alt="item.name" />
        <div v-if="item.name"
          class="absolute text-12px w-full bg-gray-500 bg-opacity-60 text-center text-white truncate px-10px py-6px bottom-0">
          {{ item.name }}
        </div>
        <div class="p-12px absolute top-0 right-0 transform translate-x-1/2 -translate-y-1/2 text-16px text-red-500"
          @click.stop="removeImage(idx)">
          <el-icon>
            <CircleCloseFilled />
          </el-icon>
        </div>
      </div>
      <el-upload v-if="fileLimit > value.length" action="#" :accept="accept" :show-file-list="false" list-type="picture"
        :auto-upload="false" @change="changeFile">
        <template #trigger>
          <div
            class="flex items-center justify-center w-80px h-80px border-1 border-dashed text-blue-500 border-blue-500 rounded">
            <el-icon>
              <CameraFilled />
            </el-icon>
          </div>
        </template>
      </el-upload>
    </div>
  </div>
</template>
<script setup>
import { computed, inject, onMounted } from 'vue';
import { ElLoading, ElMessage } from 'element-plus';
import { updateFile } from "@/api/uploadFIle.js";
import { getRandomStr, imageToBase64 } from '@/utils'


const emits = defineEmits(['change'])
const props = defineProps({
  form: { type: Object, default: () => ({}) },
  name: { type: String, default: '' },
  required: { type: Boolean, default: false },
  title: { type: String, default: '' },
  accept: { type: String, default: '' },
})
const value = computed(() => Array.isArray(props.form[props.title]) ? props.form[props.title] : []);
const fileLimit = computed(() => typeof props.limit === 'number' && props.limit > 0 ? props.limit : 10)
const list = computed(() => value.value.map(item => ({
  ...item,
  isImage: item.type.startsWith('image/'),
  isPdf: item.type === 'application/pdf'
})))

function change(value) { emits('change', { title: props.title, value }) }

function removeImage(idx) {
  const list = [...value.value];
  list.splice(idx, 1)
  change(list)
}


async function changeFile(uploadFile) {
  const { raw: file } = uploadFile; // blobUrl仅仅本地预览
  const { size, name, type } = file;
  const list = [...value.value];
  const newFile = { file, size, name, type };
  if (/^image\//.test(type)) {
    const base64String = await imageToBase64(file);
    const base64WithPrefix = `data:${file.type};base64,${base64String}`;
    newFile.base64 = base64WithPrefix;
  } else if (type === 'application/pdf') {
    list.push(newFile);
  } else {
    ElMessage.info('暂不支持当前类型文件')
  }
  change(list)
}

const addRule = inject('addRule')
const addBeforeSave = inject('addBeforeSave');
onMounted(() => {
  addRule(props.title, check);
  addBeforeSave(props.title, uploads)
});
function check() {
  if (props.required && value.value.length === 0) return '请上传文件'
  return true
}

function previewImage(item) {
  if (/https?/.test(item.url)) {
    wx.previewImage({
      current: item.url,
      urls: [item.url],
      complete: (res) => {
        if (res.errMsg !== 'previewImage:ok') {
          ElMessage.error('预览失败')
        }
      }
    })
  }
}

async function uploads() {
  const fileList = value.value.filter(i => i.file);
  if (fileList.length === 0) return true;
  const loadingInstance = ElLoading.service({ fullscreen: true, text: '正在上传' })
  for (let item of fileList) {
    try {
      const res = await updateFile(`${+new Date()}_${Math.ceil(Math.random() * 100000)}_${item.file.name}`, item.file, localStorage.getItem('corpId'));
      if (res) {
        const list = [...value.value];
        const index = list.findIndex(i => i.url === item.url);
        list.splice(index, 1, { url: res.download_url, size: item.file.size, name: item.file.name, type: item.file.type });
        change(list);
      } else {
        loadingInstance.close()
        ElMessage.error(`${props.name} ${item.file.name}上传失败`);
        return false;
      }
    } catch (e) {
      loadingInstance.close()
      ElMessage.error(`${props.name} ${item.file.name}上传失败`);
      return false;
    }
  }
  loadingInstance.close()
  return true;
}

</script>
