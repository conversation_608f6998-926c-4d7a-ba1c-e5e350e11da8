<template>
  <el-dialog :model-value="props.visible" :width="width" :title="`${props.source._id ? '编辑' : '新增'}来源`" @close="close()">
    <el-scrollbar wrap-class="el-dialog-body-full-scroll">
      <el-form class="p-15px" label-suffix="：" :label-width="120">
        <el-form-item class="is-required" label="来源名称">
          <el-input v-model="form.sourceName" placeholder="请输入" maxlength="30" />
        </el-form-item>
        <el-form-item class="is-required" label="拼音码">
          <div class="flex-grow">
            <el-input v-model="form.pinyin" placeholder="请输入" />
          </div>
        </el-form-item>
        <el-form-item class="is-required" label="所属分类">
          <el-cascader v-model="form.sourceCateIdGroup" :show-all-levels="false" :options="cateTree" :props="{ checkStrictly: true, value: '_id', multiple: false, emitPath: false }" class="w-full" clearable placeholder="请选择分类" />
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button plain class="w-100px" @click="close()">取消</el-button>
        <el-button class="w-100px" :loading="loading" type="primary" @click="confirm()">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ElMessage } from "element-plus";
import { computed, ref, watch } from "vue";
import { addSource, updateSource } from "@/api/benefitManagement";
import { getCateTreeData } from "@/utils";

const emits = defineEmits(["close", "success"]);
const props = defineProps({
  visible: { type: Boolean, default: false },
  width: { type: String, default: "500px" },
  source: { type: Object, default: () => ({}) },
  cateList: { type: Array, default: () => [] },
});

const form = ref({
  sourceName: "",
  pinyin: "",
  cateId: "",
  sourceCateIdGroup: [], // 新增多分类字段
});
const loading = ref(false);

// 将分类列表转换为树形结构
const cateTree = computed(() => {
  return getCateTreeData(props.cateList);
});

function close() {
  emits("close");
}

async function confirm() {
  if (!form.value.sourceName) {
    ElMessage.error("来源名称不能为空");
    return;
  }
  if (!form.value.pinyin) {
    ElMessage.error("拼音码不能为空");
    return;
  }

  loading.value = true;
  try {
    const params = {
      sourceName: form.value.sourceName,
      pinyin: form.value.pinyin,
      sourceCateIdGroup: form.value.sourceCateIdGroup,
    };

    const { success, message } = props.source._id ? await updateSource({ id: props.source._id, params }) : await addSource({ params });

    if (success) {
      ElMessage.success(message || "保存成功");
      close();
      emits("success");
    } else {
      ElMessage.error(message || "保存失败");
    }
  } catch (error) {
    ElMessage.error("操作异常");
  } finally {
    loading.value = false;
  }
}

watch(
  () => props.visible,
  (n) => {
    if (n) {
      form.value = {
        sourceName: props.source.sourceName || "",
        pinyin: props.source.pinyin || "",
        cateId: props.source.cateId || "",
        // 如果存在sourceCateIdGroup则使用，否则使用cateId构建数组，确保向后兼容
        sourceCateIdGroup: Array.isArray(props.source.sourceCateIdGroup) ? props.source.sourceCateIdGroup : props.source.cateId ? [props.source.cateId] : [],
      };
    }
  }
);
</script>
