<template>
  <my-layout bg-fff v-loading="loading">
    <!-- 标题 -->
    <layout-item>
      <div class="px-15px pt-20px pb-10px font-semibold" font-16 border-bottom>
        <span v-if="department && department.name">{{ department.name }}</span>
        <span v-else>医生列表</span>
      </div>
    </layout-item>
    
    <!-- 搜索 -->
    <layout-item>
      <div class="py-12px px-15px" border-bottom>
        <el-input v-model="searchKeyword" placeholder="输入医生姓名搜索" :suffix-icon="Search" />
      </div>
    </layout-item>
    
    <!-- 医生列表 -->
    <layout-main :scroll="false">
      <el-scrollbar h-full ref="scrollRef">
        <div v-for="doctor in filteredDoctors" :key="doctor.id" class="px-15px py-10px border-bottom doctor-item">
          <div class="flex items-start justify-between gap-12px">
            <div class="flex-grow min-w-0" @click="previewDoctor(doctor)">
              <div class="flex items-center mb-6px">
                <div class="text-sm font-semibold mr-8px truncate">
                  <span v-html="highlightKeyword(doctor.name, searchKeyword)"></span>
                </div>
                <span class="px-6px py-2px bg-orange-100 text-orange-700 text-xs rounded whitespace-nowrap">
                  <span v-html="highlightKeyword(doctor.title, searchKeyword)"></span>
                </span>
                <svg v-if="doctor.featured" class="w-4 h-4 text-yellow-500 ml-6px flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              </div>
              <div class="text-sm text-gray-600 leading-relaxed">
                <span class="font-medium">擅长：</span>
                <el-tooltip
                  :disabled="!doctor.speciality || doctor.speciality === '暂无专长介绍'"
                  placement="top"
                  effect="light"
                  :content="doctor.speciality"
                  popper-class="max-w-400px"
                >
                  <span class="line-clamp-2 cursor-help" v-html="highlightKeyword(doctor.speciality, searchKeyword)"></span>
                </el-tooltip>
              </div>
            </div>
            <div class="flex-shrink-0 mt-2px">
              <el-button
                size="small"
                circle
                @click.stop="selectDoctor(doctor)"
                class="w-32px h-32px p-0 doctor-send-btn"
                title="选择医生"
              >
                <el-icon size="14" class="text-blue-500">
                  <Promotion />
                </el-icon>
              </el-button>
            </div>
          </div>
        </div>
        <empty-data v-if="filteredDoctors.length === 0" title="暂无医生信息"></empty-data>
      </el-scrollbar>
    </layout-main>
    
    <!-- 底部返回按钮 -->
    <layout-item>
      <div class="py-12px px-15px" common-shadow--r>
        <el-button class="block w-full" @click="goBack()">返回</el-button>
      </div>
    </layout-item>
  </my-layout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { Search, Promotion } from '@element-plus/icons-vue'
import MyLayout from '@/components/layout'
import LayoutItem from '@/components/layout/item.vue'
import LayoutMain from '@/components/layout/main.vue'
import EmptyData from '@/components/empty-data.vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  department: { type: Object, default: () => ({}) },
  customer: { type: Object, default: () => ({}) },
  teams: { type: Array, default: () => [] },
  unionId: { type: String, default: '' }
})

const emits = defineEmits(['close', 'select', 'back'])

const loading = ref(false)
const doctors = ref([])
const searchKeyword = ref('')

// 根据搜索关键词过滤医生
const filteredDoctors = computed(() => {
  if (!searchKeyword.value) {
    return doctors.value
  }
  
  const keyword = searchKeyword.value.toLowerCase()
  return doctors.value.filter(doctor => 
    doctor.name.toLowerCase().includes(keyword) ||
    doctor.title.toLowerCase().includes(keyword) ||
    doctor.speciality.toLowerCase().includes(keyword)
  )
})

onMounted(() => {
  loadDoctors()
})

async function loadDoctors() {
  if (!props.department || !props.department.id) {
    return
  }
  
  loading.value = true
  
  try {
    // 获取企业ID
    const corpId = localStorage.getItem('corpId')
    if (!corpId) {
      ElMessage.error('未获取到企业信息')
      return
    }
    
    // 导入API
    const { getHlwHospitalMembersByDept } = await import('@/api/internet-dept-manage')
    
    // 获取要查询的科室ID
    let deptId = props.department.deptId || props.department.hlwDeptId

    // 如果是虚拟科室（只有一级科室会创建虚拟二级科室），需要特殊处理
    if (props.department.isVirtual) {
      // 优先使用 realDeptId（真实科室的业务ID）
      if (props.department.realDeptId) {
        deptId = props.department.realDeptId
      } else if (props.department.hlwDeptId) {
        deptId = props.department.hlwDeptId
      } else if (props.department.deptId && props.department.deptId !== props.department.id) {
        deptId = props.department.deptId
      } else if (props.department.parentId) {
        // 如果有parentId，使用parentId作为真实科室ID
        deptId = props.department.parentId
      } else if (props.department.id.includes('_virtual')) {
        // 虚拟科室的ID格式是 "原科室ID_virtual"
        deptId = props.department.id.replace('_virtual', '')
      }
    }

    // 如果还是没有获取到有效的科室ID，使用department的id作为备选
    if (!deptId) {
      deptId = props.department.id
    }

    // 确保去除任何可能的_virtual后缀（双重保险）
    if (typeof deptId === 'string' && deptId.endsWith('_virtual')) {
      deptId = deptId.replace('_virtual', '')
    }

    // 验证科室ID格式
    if (!deptId || deptId.includes('_virtual')) {
      console.error('科室ID格式错误:', deptId, props.department)
      ElMessage.error('科室ID格式错误，无法获取医生列表')
      return
    }
    
    // 调用API获取科室医生
    const result = await getHlwHospitalMembersByDept(corpId, deptId)
    
    if (result.success && result.data) {
      // 现在getHlwHospitalMembersByDept直接返回转换后的数据数组
      const memberList = Array.isArray(result.data) ? result.data : []
      
      // 转换数据格式 - 使用corp-member字段
      doctors.value = memberList.map(member => ({
        id: member.userid || member._id,
        name: member.anotherName || member.name || '未知',
        title: member.title || '医师',
        speciality: (() => {
          // 处理擅长领域字段，支持字符串和数组格式
          let speciality = member.specialty || member.expertise || member.specialtyFields || member.introduction || '';
          if (Array.isArray(speciality)) {
            speciality = speciality.filter(Boolean).join('、');
          }
          return speciality || '暂无专长介绍';
        })(),
        job: member.job || [],
        phone: member.mobile || member.phone || '',
        avatar: member.avatar || '',
        workId: member.doctorNo || member.workNo || member.workId || '',
        outpatientTime: member.outpatientTime || '',
        publicPhone: member.callNumber || member.publicPhone || '',
        featured: member.recommended === 1, // 推荐医生显示星标
        department: props.department,
        // 保留原始数据
        originalData: member
      }))
      
      // 如果没有医生，显示提示
      if (doctors.value.length === 0) {
        // 该科室暂无医生
      }
    } else {
      doctors.value = []
      // 获取医生列表失败
    }
  } catch (error) {
    console.error('加载医生列表出错:', error)
    ElMessage.error('加载医生列表失败')
    doctors.value = []
  } finally {
    loading.value = false
  }
}

// 高亮搜索关键词
function highlightKeyword(text, keyword) {
  if (!text || !keyword) return text

  // 转义特殊字符，避免正则表达式错误
  const escapedKeyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  const regex = new RegExp(`(${escapedKeyword})`, 'gi')
  return text.replace(regex, '<span class="text-blue-500 font-medium">$1</span>')
}

function previewDoctor(doctor) {
  // 预览医生详情逻辑
}

function selectDoctor(doctor) {
  emits('select', { 
    type: 'doctor', 
    data: {
      ...doctor,
      department: props.department
    }
  })
}

function goBack() {
  emits('back')
}

function cancel() {
  emits('close')
}
</script>

<style scoped>
/* 确保页面在最顶层 */
.doctor-page {
  z-index: 9999;
}

/* 滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 医生列表样式 */
.doctor-item {
  border-bottom: 1px solid #f0f0f0;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 发送按钮样式 */
.doctor-send-btn {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.doctor-send-btn:hover {
  background: transparent !important;
  border: none !important;
}

.doctor-send-btn:focus {
  background: transparent !important;
  border: none !important;
}
</style>
