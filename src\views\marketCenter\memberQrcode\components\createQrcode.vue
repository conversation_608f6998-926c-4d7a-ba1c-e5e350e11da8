<template>
  <el-dialog :model-value="memberQrcodeVisible" :title="title" draggable :width="640" @close="close" :close-on-click-modal="false">
    <el-scrollbar ref="scrollbarRef" wrap-style="max-height: calc(85vh - 200px)" v-loading="fileLoading">
      <div class="pl-10px pr-30px">
        <el-form :label-width="130" label-suffix="：" @click="scroll">
          <el-row>
            <el-col :span="24">
              <el-form-item label="活码类型">{{ form.qrCodetype === 1 ? "单人码" : "多人码" }}</el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item class="is-required" label="活码名称">
                <div v-if="operationType === 'read'">{{ form.qrCodeName }}</div>
                <el-input v-else placeholder="请输入活码名称" :model-value="form.qrCodeName" @update:model-value="changeQrCodeName($event)" />
              </el-form-item>
            </el-col>
            <el-col v-if="requireCate" :span="24">
              <el-form-item class="is-required" label="活码分类">
                <el-cascader v-model="form.cateId" class="w-full" :show-all-levels="false" :options="cateTree" :props="{ checkStrictly: true, value: '_id' }" clearable />
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item label="使用员工" class="is-required">
                <div class="flex align-center w-full" flex-wrap>
                  <div class="mr-10px h-32px leading-32px" v-for="item in form.corpUserId" :key="item">
                    <ww-user :openid="item" />
                  </div>
                  <el-button type="primary" size="small" :icon="Plus" plain @click="selectMember()" v-if="(operationType === 'edit' && form.qrCodetype != 1 && operationType !== 'read') || operationType === 'add'">添加</el-button>
                </div>
                <remind-title title="选择多人时，将使用相同的配置项为所选员工批量创建多个二维码" v-if="qrcodeType === 1 && operationType === 'add' && operationType !== 'read'" />
                <remind-title title="为多个员工创建一个二维码，客户扫码后随机添加员工" v-if="qrcodeType === 2 && operationType !== 'read'" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item class="mb-0" label="添加设置">
                <div v-if="operationType === 'read'">
                  {{ form.skipVerify === 1 ? "客户添加时自动通过成为好友" : "手动通过" }}
                </div>
                <el-radio-group :model-value="form.skipVerify" @update:model-value="changeSkipVerify($event)" v-else>
                  <el-radio :label="1">客户添加时自动通过成为好友</el-radio>
                  <el-radio :label="2">手动通过</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-divider border-style="dashed" />
            <el-col :span="24">
              <el-form-item label="欢迎语设置">
                <remind-title title="使用成员需关闭企业微信后台欢迎语设置功能，否则该欢迎语可能无法生效" class="pb-10px mt-3px" v-if="operationType !== 'read'" />
                <div v-if="operationType === 'read'">{{ form.welcomContent }}</div>
                <el-input v-else ref="welcomContentRef" :model-value="form.welcomContent" @update:model-value="changeWelcomContent($event)" show-word-limit type="textarea" placeholder="个性化设置欢迎语，扫描该员工二维码添加的客户，自动推送该欢迎语" :autosize="{ minRows: 6, maxRows: 8 }" :maxlength="1000" />
                <div class="mt-10px" v-if="operationType !== 'read'">
                  <span class="text-14px text-gray-500">昵称：</span>
                  <el-button text type="primary" size="small" :icon="Link" @click="insertNickName">插入客户昵称</el-button>
                </div>
                <div class="mt-10px flex" v-if="operationType !== 'read'">
                  <span class="text-14px flex-shrink-0 text-gray-500">附件：</span>
                  <div class="-mt-4px">
                    <el-button plain class="add-btn" type="primary" size="small" :icon="Plus" @click="chooseFile('image/*')">图片</el-button>
                    <!-- <el-button plain class="add-btn" type="primary" size="small" :icon="Plus"
                      @click="chooseFile('video/*')">视频</el-button> -->
                    <!-- <el-button plain class="add-btn" type="primary" size="small" :icon="Plus" @click="addFileUrl">链接</el-button> -->
                    <el-button plain class="add-btn" type="primary" size="small" :icon="Plus" @click="addArticle('article')">宣教文章</el-button>
                    <el-button plain class="add-btn" type="primary" size="small" :icon="Plus" @click="showLink">建档链接</el-button>
                  </div>
                </div>
              </el-form-item>
              <div v-if="showFileList.length > 0" class="mt-10px ml-130px">
                <div v-for="(i, idx) in showFileList" :key="idx" class="flex items-center py-6px px-12px overflow-hidden bg-gray-100 rounded-8px" :class="idx > 0 ? 'mt-10px' : ''">
                  <div color-normal class="flex-grow h-24px leading-24px w-0 truncate">
                    <span v-if="i.typeStr" class="font-semibold text-14px">{{ i.typeStr }}</span>
                    <span v-if="i.file && i.file.name">{{ i.file.name }}</span>
                  </div>
                  <el-icon :size="16" @click="removeFile(idx)" class="pointer" v-if="operationType !== 'read'">
                    <CloseBold />
                  </el-icon>
                </div>
              </div>
              <div v-if="form.archiveLink" class="mt-10px ml-130px">
                <div class="flex items-center py-6px px-12px overflow-hidden bg-gray-100 rounded-8px">
                  <div color-normal class="flex-grow h-24px leading-24px w-0 truncate">
                    <span class="font-semibold text-14px">【建档】</span>
                    <span>{{ form.archiveLink.name }}</span>
                  </div>
                  <el-icon :size="16" @click="clearArchiveLink()" class="pointer" v-if="operationType !== 'read'">
                    <CloseBold />
                  </el-icon>
                </div>
              </div>
            </el-col>
            <el-divider border-style="dashed" />
            <el-col :span="24">
              <el-form-item label="自动建档">
                <div class="h-32px flex items-center">
                  <el-switch v-model="form.autoCreateCustomer" size="large" inline-prompt active-text="启用" inactive-text="关闭" @change="changeAutoCreateCustomer" />
                  <el-icon class="ml-20px cursor-pointer text-gray-500" size="20" @click="show">
                    <QuestionFilled />
                  </el-icon>
                </div>
              </el-form-item>
            </el-col>
            <template v-if="form.autoCreateCustomer">
              <el-col :span="24">
                <el-form-item label="客户来源">
                  <div v-if="operationType === 'read'">{{ Array.isArray(form.customerSource) ? form.customerSource.join("/") : "" }}</div>
                  <el-cascader v-else style="width: 100%" :model-value="form.customerSource" :options="customerSources" @change="changeCustomerSource" placeholder="请选择渠道来源" clearable />
                  <remind-title title="客户扫码添加好友后，自动打上客户来源" v-if="operationType !== 'read'" />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="客户标签" style="margin-bottom: 0">
                  <div class="flex flex-wrap w-full pt-6px">
                    <customer-tag v-for="tagId in form.tagIds || []" :key="idx" :tag-id="tagId" />
                    <el-button type="primary" size="small" :icon="Plus" plain @click="changeTag()" v-if="operationType !== 'read'">标签</el-button>
                  </div>
                  <remind-title title="客户扫码添加好友后，自动打上标签" v-if="operationType !== 'read'" />
                </el-form-item>
              </el-col>
            </template>
            <el-divider border-style="dashed" />
            <el-col :span="24">
              <el-form-item label="活码头像" style="margin-bottom: 0">
                <div class="flex align-center">
                  <div class="w-300px">
                    <img v-if="form.headerImage" :src="form.headerImage" class="w-100px h-100px pointer" @click="showModal" />
                    <el-button v-else plain class="w-100px h-100px" type="primary" size="small" :icon="Plus" @click="showModal"></el-button>
                  </div>
                  <div class="leading-normal">若不设置，活码头像将默认为所选员工的头像。建议尺寸：1:1，支持上传jpg/png格式的图片，大小不超过2M</div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-scrollbar>
    <template #footer v-if="operationType !== 'read'">
      <div class="dialog-footer text-center" v-loading="loading">
        <el-button class="w-100px" @click="close">取消</el-button>
        <el-button class="w-100px" type="danger" @click="removeStaffQrcode()" v-if="operationType === 'edit' && qrCodeInfo.staffQrcodeType !== 'personal'">
          {{ !qrCodeInfo.allAddCustomerCount || qrCodeInfo.allAddCustomerCount === 0 ? "删除" : "停用" }}
        </el-button>
        <el-button class="w-100px" type="primary" @click="dialogSubmit()">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <select-member-vue ref="selectMemberRef" @getSelectItems="onGetSelectItems"></select-member-vue>
  <tag-drawer ref="tagDrawerRef" :mult="true" @get-select-tag="onTagChanged($event)"></tag-drawer>
  <input class="fixed -z-1 w-0 h-0 invisible" type="file" ref="uploadImageRef" accept="image/*" @change="handleSelect($event)" />
  <input class="fixed -z-1 w-0 h-0 invisible" type="file" ref="uploadVideoRef" accept="video/*" @change="handleSelect($event)" />
  <add-file ref="addFileRefs" @onGetSelectFile="getSelectFile"></add-file>
  <file-url :visible="fileUrlVisible" @cancel="fileUrlVisible = false" @submit="submitUrl"></file-url>
  <auto-fill-modal :visible="visible" :width="width" @close="closeAutoFillModal" />
  <archive-link-modal :corpUserId="form.corpUserId" :link="form.archiveLink" :visible="linkVisible" :width="linkWidth" @close="closeLink" @change="changeArchiveLink" />
  <cropper-modal title="活码头像" :src="form.headerImage" :handle="changeHeaderImage" :visible="cropperVisible" @close="closeModal" updateType="init" />
</template>
<script setup>
import { ref, computed, watch, nextTick } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { getCustomerSources } from "@/views/member/corpInfo";
import fileUrl from "@/components/file-url";
import { updateFile } from "@/api/uploadFIle.js";
import { getRandomStr } from "@/utils";
import { addStaffQrcode, removeStaffQrcode as removeStaffQrcodeUrl, updateStaffQrcode } from "@/api/knowledgeBase";
import { addContactWay } from "@/api/wecom";
import useModal from "@/hooks/useModal";
import CropperModal from "@/components/cropper-modal/cropper-modal.vue";
import { Plus, Link } from "@element-plus/icons-vue";
import addFile from "@/views/pannedEvent/components/addFile.vue";
import autoFillModal from "./auto-fill-modal.vue";
import customerTag from "@/components/customer-tag/customer-tag.vue";
import remindTitle from "@/components/remind-title/index.vue";
import selectMemberVue from "@/components/select-member/index.vue";
import TagDrawer from "@/components/tag-drawer/index.vue";
import WwUser from "@/components/ww-user/index.vue";
import archiveLinkModal from "./archive-link-modal.vue";
const { show, close: closeAutoFillModal, visible, width } = useModal();
const { show: showLink, close: closeLink, visible: linkVisible, width: linkWidth } = useModal();
const { close: closeModal, show: showModal, visible: cropperVisible } = useModal(); //  上传logo弹窗
let customerSources = getCustomerSources();
const selectMemberRef = ref(null);
const tagDrawerRef = ref(null);
const addFileRefs = ref(null);
const uploadImageRef = ref(null);
const uploadVideoRef = ref(null);
const fileUrlVisible = ref(false);
const emit = defineEmits(["close", "submit"]);
const loading = ref(false);
const fileLoading = ref(false);
let newFormValue = {};
const props = defineProps({
  cateTree: { type: Array, default: () => [] },
  cateId: { type: String, default: "" },
  requireCate: { type: Boolean, default: false },
  memberQrcodeVisible: { default: false },
  qrCodeInfo: { type: Object, default: () => ({}) },
  operationType: { default: "" },
  qrcodeType: { default: 1 },
});
const form = ref({
  qrCodeName: "",
  welcomContent: "",
  customerSource: "",
  skipVerify: 1,
  corpUserId: [],
  tagIds: [],
  fileList: [],
  autoCreateCustomer: false,
});
const title = computed(() => {
  if (props.operationType === "add") return "新建员工活码";
  if (props.operationType === "edit") return "编辑员工活码";
  if (props.operationType === "read") return "查看员工活码";
});
const cateIdStr = computed(() => {
  if (typeof form.value.cateId === "string") return form.value.cateId;
  return Array.isArray(form.value.cateId) ? form.value.cateId[form.value.cateId.length - 1] || "" : "";
});
let headerImageFileUrl = "";
async function changeHeaderImage(url, data) {
  const blob = new Blob([data], { type: "image/png" }); // 假设 data 是你的图片数据
  headerImageFileUrl = URL.createObjectURL(blob);
  newFormValue.headerImage = url;
  form.value.headerImage = url;
}
function changeAutoCreateCustomer(e) {
  form.value.tagIds = [];
  form.value.customerSource = "";
  newFormValue.autoCreateCustomer = e;
  newFormValue.tagIds = [];
  newFormValue.customerSource = "";
}
const mergeImages = async (qrcode) => {
  if (!headerImageFileUrl) return qrcode;
  // 异步
  return new Promise((resolve, reject) => {
    const baseImage = new Image();
    baseImage.crossOrigin = "anonymous";
    baseImage.src = qrcode;
    baseImage.onload = () => {
      const uploadedImage = new Image();
      uploadedImage.onload = () => {
        const canvas = document.createElement("canvas");
        const context = canvas.getContext("2d");
        canvas.width = 396; // baseImage.width;
        canvas.height = 396; // baseImage.height;
        context.drawImage(baseImage, 0, 0, 396, 396);
        context.fillStyle = "white"; // 设置白色底色
        context.fillRect(150, 150, 96, 96);
        context.drawImage(uploadedImage, 150, 150, 96, 96);
        // mergedImageUrl.value = canvas.toDataURL("image/png");
        canvas.toBlob(async (blob) => {
          let name = `${localStorage.getItem("corpId")}_logo_${new Date().getTime()}.png`;
          const file = new File([blob], name);
          const { download_url } = await updateFile(name, file, localStorage.getItem("corpId"));
          resolve(download_url);
        }, "image/png");
      };
      uploadedImage.src = headerImageFileUrl;
    };
  });
};

watch(
  () => props.memberQrcodeVisible,
  () => {
    newFormValue = {};
    headerImageFileUrl = "";
    if (props.operationType === "add") {
      form.value = {
        qrCodeName: "",
        welcomContent: "",
        customerSource: "",
        skipVerify: 1,
        corpUserId: [],
        tagIds: [],
        fileList: [],
        headerImage: "",
        qrCodetype: props.qrcodeType,
        autoCreateCustomer: true,
      };
      if (props.requireCate) form.value.cateId = props.cateId;
    } else {
      form.value = JSON.parse(JSON.stringify(props.qrCodeInfo));
      form.value.fileList = Array.isArray(form.value.fileList) ? form.value.fileList : [];
    }
  },
  { immediate: true }
);
const showFileList = computed(() => {
  if (!Array.isArray(form.value.fileList)) return [];
  return form.value.fileList.map((i) => {
    let typeStr = "";
    const type = i.type;
    const fileType = i.file && typeof i.file.type === "string" ? i.file.type : "";
    if (fileType.includes("video")) typeStr = "【视频】";
    else if (fileType.includes("image") || type === "image") typeStr = "【图片】";
    else if (fileType === "article") typeStr = "【文章】";
    else if (type === "link") typeStr = "【链接】";
    return { ...i, typeStr };
  });
});
function addFileUrl() {
  if (form.value.fileList.length >= 9) {
    ElMessage.info("最多上传9个附件");
    return;
  }
  fileUrlVisible.value = !fileUrlVisible.value;
}

function clearArchiveLink() {
  form.value.archiveLink = "";
  newFormValue.archiveLink = "";
}

function submitUrl(obj) {
  form.value.fileList.push(obj);
  fileUrlVisible.value = false;
  if (props.operationType === "edit") {
    newFormValue.fileList = form.value.fileList;
  }
}
async function selectMember() {
  selectMemberRef.value.openDialog(form.value.corpUserId, [], false);
}
function onGetSelectItems({ leaders, checkedMemberList }) {
  form.value.corpUserId = checkedMemberList;
  if (props.operationType === "edit") {
    newFormValue.corpUserId = checkedMemberList;
  }
}
function changeTag() {
  tagDrawerRef.value.openDialog(Array.isArray(form.value.tagIds) ? form.value.tagIds : []);
}
function changeCustomerSource(e) {
  form.value.customerSource = e;
  if (props.operationType === "edit") {
    newFormValue.customerSource = e;
  }
}
// 编辑活码名称
function changeQrCodeName(value) {
  form.value.qrCodeName = value;
  if (props.operationType === "edit") {
    newFormValue.qrCodeName = value;
  }
}
// 编辑欢迎语
function changeWelcomContent(value) {
  form.value.welcomContent = value;
  if (props.operationType === "edit") {
    newFormValue.welcomContent = value;
  }
}
// 编辑添加设置
function changeSkipVerify(value) {
  form.value.skipVerify = value;
  if (props.operationType === "edit") {
    newFormValue.skipVerify = value;
  }
}
// 删除活码
async function removeStaffQrcode() {
  const title = props.qrCodeInfo.allAddCustomerCount > 0 ? "停用后无法再次启用，确定停用吗？" : "确定删除该员工活码吗？";
  await ElMessageBox.confirm(title, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  });
  const params = {
    configId: props.qrCodeInfo.configId,
    allAddCustomerCount: props.qrCodeInfo.allAddCustomerCount,
    corpId: localStorage.getItem("corpId"),
  };
  loading.value = true;
  const { success } = await removeStaffQrcodeUrl(props.qrCodeInfo._id, params);
  loading.value = false;
  if (success) {
    emit("submit");
    ElMessage.success("删除成功");
  } else {
    ElMessage.error("删除失败");
  }
}

function onTagChanged(value) {
  form.value.tagIds = value;
  if (props.operationType === "edit") {
    newFormValue.tagIds = value;
  }
}
const addArticle = (name) => {
  addFileRefs.value.open(name, {});
};
function getSelectFile(file) {
  if (form.value.fileList.length >= 9) {
    ElMessage.info("最多上传9个附件");
    return;
  }
  const { url, name, summary, type } = file;
  const item = {
    URL: "https://796f-youcan-clouddev-1-8ewcqf31dbb2b5-1317294507.tcb.qcloud.la/other/19-%E9%97%AE%E5%8D%B7.png?sign=55a4cd77c418b2c548b65792a2cf6bce&t=1701328694",
    type: "link",
    file: {
      name,
      type: type,
      subtitle: summary,
      url,
    },
  };
  form.value.fileList.push(item);
  scrollBottom();
  if (props.operationType === "edit") {
    newFormValue.fileList = form.value.fileList;
  }
}
function changeArchiveLink(data) {
  form.value.archiveLink = data;
  if (props.operationType === "edit") {
    newFormValue.archiveLink = data;
  }
}

function removeFile(index) {
  form.value.fileList.splice(index, 1);
  if (props.operationType === "edit") {
    newFormValue.fileList = form.value.fileList;
  }
}
async function chooseFile(type) {
  if (form.value.fileList.length >= 9) {
    ElMessage.info("最多上传9个附件");
    return;
  }
  if (type === "image/*") {
    uploadImageRef.value.click();
  } else {
    uploadVideoRef.value.click();
  }
}
async function handleSelect(e) {
  const [file] = e.target.files;
  if (form.value.fileList.some((i) => i.file.name === file.name)) {
    ElMessage.info("附件已存在");
    return;
  }
  e.target.value = "";
  if (!file) return;
  let type = "";
  if (file.type.includes("image")) {
    // 限制文件大小 10M
    type = "image";
    if (file.size > 10485760) {
      ElMessage.info("图片大小不能超过10MB");
      return;
    }
  } else if (file.type.includes("video")) {
    // 限制文件大小 10M
    type = "video";
    if (file.size > 10485760) {
      ElMessage.info("视频大小不能超过10MB");
      return;
    }
  }
  const { name } = file;
  fileLoading.value = true;
  let { url, success, message } = await updateFile(name, file, localStorage.getItem("corpId"), type, "image");
  fileLoading.value = false;
  if (!success) {
    ElMessage.error(message);
    return;
  }
  form.value.fileList.push({
    type,
    URL: url,
    file: {
      name,
      type,
    },
  });
  newFormValue.fileList = form.value.fileList;
  scrollBottom();
}
function close() {
  emit("close");
}
async function dialogSubmit() {
  form.value["qrcodeId"] = `q&${getRandomStr(5)}`;
  form.value["corpId"] = localStorage.getItem("corpId");
  if (props.operationType === "add") form.value["creator"] = localStorage.getItem("userId");
  form.value["qrCodeStatus"] = "enable";
  if (form.value.qrCodeName.length === 0) {
    ElMessage.error("请输入活码名称");
    return;
  }
  if (props.requireCate && !cateIdStr.value) {
    ElMessage.error("请选择活码分类");
    return;
  }
  if (form.value.corpUserId.length === 0) {
    ElMessage.error("请选择员工");
    return;
  }
  // if (props.qrcodeType == 2 && form.value.corpUserId.length < 2) {
  //   ElMessage.error("多人码使用员工必须选择大于等于2人");
  //   return;
  // }
  loading.value = true;
  newFormValue["corpId"] = localStorage.getItem("corpId");
  newFormValue["configId"] = props.qrCodeInfo.configId;
  const formData = { ...form.value };
  if (props.requireCate) {
    formData.cateId = cateIdStr.value;
    newFormValue.cateId = cateIdStr.value;
  }
  if (props.qrcodeType === 1 && props.operationType === "add" && form.value.corpUserId.length > 1) {
    // 循环添加员工活码
    let corpUserIds = form.value.corpUserId;
    const query = JSON.parse(JSON.stringify(formData));
    for (let i = 0; i < corpUserIds.length; i++) {
      query.corpUserId = [corpUserIds[i]];
      await addStaffQrcode(query);
      loading.value = false;
    }
  } else {
    if (props.operationType === "edit") {
      await updateQrcode(form.value._id, newFormValue);
    } else {
      await addQrcode(formData);
    }
    loading.value = false;
  }
}
async function updateQrcode(id, query) {
  if (headerImageFileUrl) {
    const url = await mergeImages(form.value.qrCode);
    query["qrCode"] = url;
  }
  const { success, message } = await updateStaffQrcode(id, query);
  if (!success) {
    ElMessage.error(message);
    return;
  }
  emit("submit");
}

async function addQrcode(query) {
  const { success, data, message } = await addContactWay(query);
  if (!success) {
    loading.value = false;
    ElMessage.error(message);
    return;
  }
  let { data: qrCode, configId } = data;
  // 生成头像和 二维码合成图片
  let url = await mergeImages(qrCode);
  query["configId"] = configId;
  query["qrCode"] = url;
  // 生成员工个人活码
  let res = await addStaffQrcode(query);
  if (!res.success) return ElMessage.error(res.message);
  emit("submit");
}
const scrollbarRef = ref();
async function scrollBottom() {
  await nextTick();
  scrollbarRef.value && scrollbarRef.value.setScrollTop(9999);
}

const welcomContentRef = ref();
async function insertNickName() {
  const textarea = welcomContentRef.value.$refs.textarea;
  const start = textarea.selectionStart; // 获取光标起始位置
  const newContent = (form.value.welcomContent || "") + "[微信昵称]";
  if (newContent.length > 1000) {
    ElMessage.error("欢迎语内容不能超过1000字");
    return;
  } else {
    if (start >= 0) {
      const text = form.value.welcomContent || "";
      const before = text.substring(0, start);
      const after = text.substring(start);
      const newText = before + "[微信昵称]" + after;
      form.value.welcomContent = newText;
    } else {
      form.value.welcomContent = newContent;
    }
  }
  await nextTick();
  start >= 0 ? textarea.setSelectionRange(start + 6, start + 6) : "";
  welcomContentRef.value.focus();
  newFormValue.welcomContent = form.value.welcomContent;
}
</script>
<style lang="scss" scoped>
.add-btn {
  margin-left: 0;
  margin-right: 10px;
}
</style>