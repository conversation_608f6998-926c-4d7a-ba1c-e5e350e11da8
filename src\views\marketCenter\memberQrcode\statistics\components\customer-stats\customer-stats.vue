<template>
  <div bg-fff common-shadow rounded-8>
    <div flex items-center justify-between p-15 font-16>
      <div font-semibold active-title-bar flex-shrink-0>新增客户趋势</div>
      <div flex items-center>
        <el-date-picker v-model="dates" type="daterange"  :unlink-panels="true"
 range-separator="-" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="YYYY-MM-DD" :disabled-date="disabledDate"
          @calendar-change="calendarChange" @change="updateTagDate()" />
        <el-text v-for="tag in rangeTags" :key="tag.key" pointer class="ml-10px"
          :type="activeTag === tag.key ? 'primary' : ''" @click="toggleTag(tag.key)">
          {{ tag.label }}
        </el-text>
      </div>
    </div>
    <div v-loading="loading" class="h-300px" ref="lineChartRef"></div>
  </div>
  <div bg-fff common-shadow rounded-8 class="mt-15px">
    <stats-table :addCustomerList="addCustomerList" />
  </div>
</template>
<script setup>
import { ref, watch } from "vue";
import dayjs from "dayjs";
import { activeTag, dates, rangeTags, toggleTag, updateTagDate } from "./useDateTag";
import statsTable from "./stats-table";
import useLineChart from "@/hooks/useLineChart";
import { getQrcodeCustomer } from "@/api/knowledgeBase";
const props = defineProps({
  qrcodeInfo: { type: Object, default: () => { } },
});
const addCustomerList = ref([]);
const { calendarChange, disabledDate } = useDisabledDate(31);
function useDisabledDate(days) {
  let startDate = "";
  function calendarChange([start, end]) {
    if (!end) {
      startDate = dayjs(start);
    }
  }
  function disabledDate(date) {
    if (startDate) {
      const minDate = startDate.subtract(days, "day");
      const maxDate = startDate.add(days, "day");
      return dayjs(date).isBefore(minDate) || dayjs(date).isAfter(maxDate);
    }
    return false;
  }
  return { calendarChange, disabledDate };
}

watch(
  () => [dates, props.qrcodeInfo],
  (n) => {
    if (n[0] && n[1].configId) {
      getList();
    }
  },
  { immediate: true }
);

watch(dates, () => {
  getList();
});
const loading = ref(false);
const lineChartRef = ref();
const { xAxisData, seriesData } = useLineChart(lineChartRef, { grid: { left: 30, right: 40 } });
async function getList() {
  if (!props.qrcodeInfo.configId) return;
  const date = [dates.value[0], dates.value[1]];
  let { success, data } = await getQrcodeCustomer(props.qrcodeInfo.configId, date);
  if (success) {
    addCustomerList.value = data.data.map((item) => {
      return {
        ...item,
        createTime: dayjs(item.createTime).format("YYYY-MM-DD"),
      };
    });
    const xData = getDatesBetween(date[0], date[1]);
    const sData = xData.map((item) => {
      const list = addCustomerList.value.filter((i) => i.createTime == item);
      return list.length ? list.length : 0;
    });
    getData(xData, sData);
  }
}

function getDatesBetween(start, end) {
  const startDate = new Date(start);
  const endDate = new Date(end);
  const dates = [];

  while (startDate <= endDate) {
    dates.push(startDate.toISOString().split("T")[0]);
    startDate.setDate(startDate.getDate() + 1);
  }

  return dates;
}

async function getData(xData, sData) {
  loading.value = true;
  setTimeout(function mock() {
    loading.value = false;
    xAxisData.value = xData;
    seriesData.value = sData;
  }, 500);
}
</script>
