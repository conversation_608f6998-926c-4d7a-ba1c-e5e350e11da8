<template>
  <el-drawer :model-value="visible" title="报备详情" size="60%" style="--el-drawer-padding-primary: 0" @close="close">
    <my-layout>
      <layout-main>
        <el-form class="p-15px" label-suffiix="：" label-position="top" label-suffix="：">
          <el-form-item class="is-required" label="客户电话">
            <el-popover :visible="existMobileCustomer.list.length > 0" :disabled="!(visible && existMobileCustomer.list.length)" placement="bottom" title="" :width="inputWidth" trigger="#">
              <template #reference>
                <el-input v-model="form.mobile" ref="mobileInput" placeholder="请输入客户电话" :suffix-icon="searchLoading ? suffixLoadingIcon : ''" @input="inputMobile" />
              </template>
              <div class="pb-5px flex items-center text-red-500 text-13px border-b border-gray-200">
                <el-icon class="text-16px">
                  <WarnTriangleFilled />
                </el-icon>
                <span class="ml-5px">该手机号已经关联档案，不支持新增报备</span>
              </div>
              <el-scrollbar wrap-class="max-h-50vh">
                <div v-for="i in existMobileCustomer.list" color-normal class="py-15px text-14px border-b border-gray-200 cursor-pointer group" @click="showExistCustomer(i)">
                  <div class="flex items-end">
                    <div class="text-16px font-semibold group-hover:text-blue-500">{{ i.name }}</div>
                    <div v-if="i.sex" class="text-13px text-gray-500 ml-10px">{{ i.sex }}</div>
                  </div>
                  <div class="mt-15px">
                    <span class="inline-block w-80px text-gray-500">身份证号：</span>
                    {{ maskIdNo(i.idCard) }}
                  </div>
                  <div class="mt-15px">
                    <span class="inline-block w-80px text-gray-500">手机号：</span>
                    {{ i.mobile }}
                  </div>
                  <div class="mt-15px">
                    <span class="inline-block w-80px text-gray-500" v-if="i.phone1">联系电话1</span>
                    {{ i.phone1 }}
                  </div>
                  <div class="mt-15px">
                    <span class="inline-block w-80px text-gray-500" v-if="i.phone2">联系电话2：</span>
                    {{ i.phone2 }}
                  </div>
                  <div class="mt-15px">
                    <span class="inline-block w-80px text-gray-500" v-if="i.phone3">联系电话3：</span>
                    {{ i.phone3 }}
                  </div>
                  <div class="mt-15px">
                    <span class="inline-block w-80px text-gray-500">所属咨询:</span>
                    <ww-user v-if="i && i.counselorRecord && Array.isArray(i.counselorRecord) && i.counselorRecord.length > 0" :openid="i.counselorRecord[i.counselorRecord.length - 1]?.counselor"></ww-user>
                  </div>
                  <div class="mt-15px">
                    <span class="inline-block w-80px text-gray-500">所属开发:</span>
                    <ww-user v-if="i && i.introducerRecord && Array.isArray(i.introducerRecord) && i.introducerRecord.length > 0" :openid="i.introducerRecord[i.introducerRecord.length - 1]?.introducer"></ww-user>
                  </div>
                </div>
              </el-scrollbar>
            </el-popover>
          </el-form-item>
          <el-form-item class="is-required" label="客户姓名">
            <el-input v-model="form.name" placeholder="请输入客户姓名" />
          </el-form-item>
          <el-form-item label="性别">
            <el-radio-group v-model="form.sex">
              <el-radio label="男">男</el-radio>
              <el-radio label="女">女</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="年龄">
            <el-input v-model="form.age" placeholder="请输入客户年龄" />
          </el-form-item>

          <el-form-item label="信息来源" class="is-required">
            <remote-select-customer-source v-model="form.infoSource" placeholder="请选择信息来源" @change="handleInfoSourceChange" />
          </el-form-item>

          <el-form-item label="意向项目" class="is-required">
            <project-intent-select v-model="form.projectIds" placeholder="请选择意向项目" multiple @change="handleProjectChange" />
          </el-form-item>

          <el-form-item label="开发渠道">
            <div class="flex-grow">
              <el-cascader v-model="form.customerSource" class="w-full" :props="{ label: 'sourceName', value: 'sourceName' }" :options="customerSources" placeholder="请选择客户来源" filterable clearable @change="change($event)" />
              <el-input v-if="form.referenceType === '同事'" :placeholder="form.referenceUserId ? '' : '请选择同事'" readonly class="mt-10px">
                <template #prefix>
                  <span v-if="form.referenceUserId" class="text-black mr-10px">
                    <ww-user :openid="form.referenceUserId"></ww-user>
                  </span>
                </template>
                <template #append>
                  <el-text pointer @click="chooseCoWorker()">选择</el-text>
                </template>
              </el-input>
              <remote-select-customer v-else-if="form.referenceType === '客户'" class="mt-10px w-full" :value="form.referenceCustomerId" placeholder="请搜索客户姓名" @change="changeRefer($event)" />
            </div>
          </el-form-item>
          <el-form-item label="报备说明">
            <el-input type="textarea" v-model="form.reportDesc" placeholder="请输入报备说明" maxlength="500" :autosize="{ minRows: 6, maxRows: 10 }" resize="none" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="报备人">
            <el-select v-model="receptionPerson" ref="selectRef" popper-class="report-modal-receptionPerson-select-popover" placeholder="请选择报备人" class="w-full report-modal-receptionPerson-select" clearable @change="changeReportUser" @visible-change="visibleChange">
              <template #prefix>
                <div class="truncate h-30px leading-30px text-left" :style="{ width: prefixWidth, color: 'rgb(96, 98, 102)' }">
                  <ww-user v-if="form.reportUserId" :openid="form.reportUserId" />
                  <span class="ml-10px">{{ teamName }}</span>
                </div>
              </template>
              <el-option-group v-for="group in filterOptions" :key="group.teamId" :label="group.name">
                <el-option v-for="item in group.list" :key="item.value" :value="item.value">
                  <div class="truncate">
                    <ww-user :openid="item.userId"></ww-user>
                  </div>
                </el-option>
              </el-option-group>
            </el-select>
          </el-form-item>
        </el-form>
      </layout-main>
      <layout-item>
        <div text-center class="p-15px">
          <el-button plain class="w-100px" type="primary" :icon="Plus" @click="close()">取消</el-button>
          <el-button class="w-100px" :loading="loading" type="primary" :icon="Plus" @click="confirm()">确定</el-button>
        </div>
      </layout-item>
    </my-layout>
  </el-drawer>
  <teleport v-if="showSearchInput" to=".report-modal-receptionPerson-select-popover .teleport-container">
    <div class="px-20px pt-20px pb-5px">
      <el-input clearable v-model="keyword" placeholder="搜索..." />
    </div>
  </teleport>
  <teammate-picker :teamId="form.teamId" :userId="form.receptionPersonUserId" :visible="pickerVisible" :width="pickerWidth" @change="changeTeammate" @close="closePicker" />
</template>
<script setup>
import { computed, ref, watch, defineComponent, h, nextTick } from "vue";
import { useElementBounding } from "@vueuse/core";
import { storeToRefs } from "pinia";
import { ElMessage } from "element-plus";
import dayjs from "dayjs";
import { addEConsuleRecord } from "@/api/consult";
import { ReportRemarkTemp } from "@/baseData";
import useModal from "@/hooks/useModal";
import { memberStore } from "@/store/member";
import { teamStore } from "@/store/team";
import { staffStore } from "@/store/staff";
import { selectEnterpriseContact } from "@/utils/jssdk";
import { addMember, updateMember, getCustomers, searchCorpCustomer } from "@/api/member";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import RemoteSelectCustomerSource from "@/components/benefit-management/remote-select-customer-source";
import RemoteSelectCustomer from "@/components/remote-select-customer/remote-select-customer";
import teammatePicker from "@/views/consult-appoint/teammate-picker.vue";
import ProjectIntentSelect from "@/components/benefit-management/project-intent-select.vue";
import WwUserSelect from "@/components/ww-user-select";
import { maskIdNo } from "@/utils";
import { Loading } from "@element-plus/icons-vue";
import WwUser from "@/components/ww-user/index.vue";

const { currentTeam, allTeams, currentTeamId } = storeToRefs(teamStore());
const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());
const emits = defineEmits(["close", "success", "showExistCustomer"]);
const props = defineProps({
  id: { type: String, default: "" },
  visible: { type: Boolean, default: false },
  width: { type: String, default: "500px" },
  customer: { type: Object, default: () => ({}) },
  peopleList: { type: Array, default: () => [] },
});
const { corpInfo, memberInfo } = storeToRefs(memberStore());
const customerSources = computed(() => (corpInfo.value && Array.isArray(corpInfo.value.customerSourceList) ? corpInfo.value.customerSourceList : []));
const { close: closeProjectModal, show: showProjectModal, visible: projectModalVisible, width: projectModalWidth } = useModal(800);
const { close: closePicker, show: showPicker, visible: pickerVisible, width: pickerWidth } = useModal(640);
const form = ref({});

// 人员选择相关
const selectRef = ref(null);
const { width: selectWidth } = useElementBounding(selectRef);
const prefixWidth = computed(() => `${selectWidth.value - 40}px`);
const showSearchInput = ref(false);
const keyword = ref("");
const receptionPerson = ref("");

const staffMap = computed(() =>
  staffList.value.reduce((acc, cur) => {
    acc[cur.userid] = cur;
    return acc;
  }, {})
);

const options = computed(() => {
  const data = allTeams.value.reduce((acc, cur) => {
    const memberList = Array.isArray(cur.memberList) ? cur.memberList : [];
    const group = { teamId: cur.teamId, name: cur.name, list: [] };
    group.list = memberList
      .map((i) => ({
        value: `${cur.teamId}||${i}`,
        userId: i,
        teamId: cur.teamId,
        name: cur.name,
        staff: staffMap.value[i],
      }))
      .filter((i) => i.staff && Array.isArray(i.staff.job) && i.staff.job.includes("onlineCustomerService"));
    if (group.list.length) return [...acc, group];
    return acc;
  }, []);
  return data;
});

const filterOptions = computed(() => {
  const name = keyword.value.trim();
  if (name === "") return options.value;
  return options.value
    .map((i) => {
      const { list, ...item } = i;
      return { ...item, list: list.filter((j) => j.staff && typeof j.staff.anotherName === "string" && j.staff.anotherName.includes(name)) };
    })
    .filter((i) => i.list.length);
});

const teamName = computed(() => {
  const team = allTeams.value.find((i) => i.teamId === form.value.reportTeamId && i.teamId);
  return team ? team.name : "";
});

const mobileInput = ref();
const { width: inputWidth } = useElementBounding(mobileInput);
const existMobileCustomer = ref({ mobile: "", list: [] });
const searchLoading = ref(false);
const suffixLoadingIcon = defineComponent({ render: () => h(Loading, { class: "animate-spin" }) });

const reportUserList = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("onlineCustomerService")).map((i) => i.userid);
});

function changeTeammate(userid, teamId) {
  form.value.reportTeamId = teamId;
  form.value.reportUserId = userid;
  closePicker();
}

function close() {
  emits("close");
}

async function inputMobile(value) {
  const mobile = form.value.mobile.trim();
  if (/^1[3456789]\d{9}$/.test(mobile)) {
    searchLoading.value = true;
    const { data } = await searchCorpCustomer({
      corpId: localStorage.getItem("corpId"),
      page: 1,
      pageSize: 20,
      showHasPlan: "YES",
      showProjectName: "YES",
      showConsumeAmount: "YES",
      mobile: form.value.mobile,
    });
    existMobileCustomer.value = { mobile, list: Array.isArray(data.list) ? data.list : [] };
  } else {
    existMobileCustomer.value = { mobile: "", list: [] };
  }
  searchLoading.value = false;
}

const loading = ref(false);
async function confirm() {
  if (loading.value) return;
  if (!form.value.mobile) {
    ElMessage.warning("请输入手机号码");
    return;
  }
  if (!form.value.name) {
    ElMessage.warning("请输入客户姓名");
    return;
  }
  if (form.value.mobile && !/^1[3456789]\d{9}$/.test(form.value.mobile)) {
    ElMessage.warning("请输入正确的手机号码");
    return;
  }
  if (existMobileCustomer.value.list.length) {
    ElMessage.warning("该手机号已经关联档案，不支持新增报备");
    return;
  }
  // if (!form.value.customerSource || form.value.customerSource.length === 0) {
  //   ElMessage.warning("请选择客户来源");
  //   return;
  // }
  if (!form.value.infoSource || form.value.infoSource.length === 0) {
    ElMessage.warning("请选择信息来源");
    return;
  }
  if (!form.value.projectIds || form.value.projectIds.length === 0) {
    ElMessage.warning("请选择意向项目");
    return;
  }

  const userId = form.value.reportUserId || localStorage.getItem("userId");
  const teamId = form.value.reportTeamId || currentTeamId.value;
  let params = {};
  const memberId = props.customer._id;
  if (memberId) {
    params = form.value;
  } else {
    params = {
      creator: userId, // 使用选择的报备人作为创建者
      corpId: localStorage.getItem("corpId"),
      addMethod: "eStoreReport", // 添加方式为网店报备
      teamId: [teamId],
      personResponsibles: [
        {
          corpUserId: userId,
          teamId: teamId,
        },
      ],
      ...form.value,
    };
  }
  loading.value = true;
  const { success, message, data } = !memberId ? await addMember(params) : await updateMember(memberId, params);
  loading.value = false;
  if (success) {
    emits("success");
  } else {
    ElMessage.error(message);
  }
  if (success && !memberId && data.data.id) {
    addEConsult(data.data.id, params);
  }
}

function change(n) {
  if (n && n[0] === "客户推荐" && form.value.referenceType !== "客户") {
    form.value.referenceType = "客户";
    form.value.reference = "";
    form.value.referenceCustomerId = "";
    form.value.referenceUserId = "";
  } else if (n && n[0] === "同事推荐" && form.value.referenceType !== "同事") {
    form.value.referenceType = "同事";
    form.value.reference = "";
    form.value.referenceCustomerId = "";
    form.value.referenceUserId = "";
  } else {
    form.value.referenceType = "";
    form.value.reference = "";
    form.value.referenceCustomerId = "";
    form.value.referenceUserId = "";
  }
}

async function chooseCoWorker() {
  const res = await selectEnterpriseContact("single");
  const { selectedUserList = [] } = res;
  if (selectedUserList && selectedUserList[0]) {
    form.value.referenceUserId = selectedUserList[0].id;
  }
}

function changeRefer(customer) {
  form.value.referenceUserId = "";
  form.value.reference = customer.name;
  form.value.referenceCustomerId = customer._id;
}

function changeReportUser(val) {
  const [teamId = "", userId = ""] = val.split("||");
  form.value.reportUserId = userId;
  form.value.reportTeamId = teamId;
}

async function visibleChange(val) {
  if (!val) return;
  await nextTick();
  const popover = document.querySelector(".report-modal-receptionPerson-select-popover");
  const el = document.querySelector(".report-modal-receptionPerson-select-popover .teleport-container");
  if (popover && !el) {
    const el = document.createElement("div");
    el.classList.add("teleport-container");
    popover.prepend(el);
  }
  await nextTick();
  showSearchInput.value = val;
}

function showExistCustomer(i) {
  emits("showExistCustomer", i);
}

async function addEConsult(customerId, params) {
  const reportUserId = form.value.reportUserId || localStorage.getItem("userId");
  const payload = {
    customerId,
    corpId: memberInfo.value.corpId,
    userId: reportUserId, // 使用选择的报备人
    registrantUserId: reportUserId, // 使用选择的报备人作为登记人
    date: dayjs().format("YYYY-MM-DD HH:mm"),
    source: params.infoSource,
    projectIds: params.projectIds,
    reportDesc: params.reportDesc === ReportRemarkTemp ? "" : params.reportDesc,
  };
  addEConsuleRecord(payload);
}

watch(
  () => props.visible,
  (n) => {
    if (n) {
      existMobileCustomer.value = { mobile: "", list: [] };
      if (staffList.value.length === 0) getStaffList();
      if (props.customer._id) {
        form.value = {
          mobile: props.customer.mobile,
          name: props.customer.name,
          sex: props.customer.sex,
          age: props.customer.age,
          reportDesc: props.customer.reportDesc,
          projectIds: props.customer.projectIds,
          customerSource: props.customer.customerSource || [],
          referenceType: props.customer.referenceType || "",
          reference: props.customer.reference || "",
          referenceCustomerId: props.customer.referenceCustomerId || "",
          referenceUserId: props.customer.referenceUserId || "",
          reportTeamId: props.customer.reportTeamId || "",
          reportUserId: props.customer.reportUserId || "",
          infoSource: props.customer.infoSource || "",
        };
        receptionPerson.value = form.value.reportTeamId && form.value.reportUserId ? `${form.value.reportTeamId}||${form.value.reportUserId}` : "";
      } else {
        form.value = {
          reportTeamId: currentTeamId.value,
          reportUserId: localStorage.getItem("userId"),
          reportDesc: ReportRemarkTemp,
          projectIds: [],
          infoSource: "",
          customerSource: [],
          referenceType: "",
          reference: "",
          referenceCustomerId: "",
          referenceUserId: "",
        };
        receptionPerson.value = form.value.reportTeamId && form.value.reportUserId ? `${form.value.reportTeamId}||${form.value.reportUserId}` : "";
      }
      keyword.value = "";
    }
  }
);
</script>

<style scoped>
:deep(.report-modal-receptionPerson-select > .select-trigger > .el-input > .el-input__wrapper > .el-input__inner) {
  display: none;
}

:deep(.report-modal-receptionPerson-select > .select-trigger > .el-input > .el-input__wrapper > .el-input__prefix) {
  flex-grow: 1;
}
</style>