<template>
  <div flex>
    <div flex-grow bg-fff common-shadow rounded-8>
      <div flex items-center justify-between p-15>
        <div font-16 font-semibold active-title-bar>新增客户总览</div>
        <div font-14>
          更新时间：{{ updateTime }}
          <span color-primary pointer @click="reload()">刷新</span>
        </div>
      </div>
      <el-row text-center border-bottom font-18 font-semibold class="pb-10px">
        <el-col :span="12">
          <div class="mb-6px">总增加客户数</div>
          <div>{{ qrcodeInfo.allAddCustomerCount }}</div>
        </el-col>
        <el-col :span="12">
          <div class="mb-6px">今日添加客户数</div>
          <div>{{ qrcodeInfo.todayAddCustomerCount }}</div>
        </el-col>
      </el-row>
    </div>
    <div flex-shrink-0 class="hidden-md-and-down w-10px"></div>
  </div>
</template>
<script setup>
import { ref } from "vue";
import dayjs from "dayjs";
const props = defineProps({
  qrcodeInfo: { type: Object, default: () => {} },
});
const emit = defineEmits(['reload']);
const updateTime = ref("");
updateTime.value = dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss");
const now = ref("");
function reload() {
  updateTime.value = dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss");
  emit("reload");
}
</script>
<style scoped>
</style>
