<template>
  <my-layout bg-fff common-shadow>
    <layout-main :scroll="false">
      <div class="source">
        <div class="item" border-right>
          <div class="header">
            <div class="flex align-center">
              <div class="line"></div>
              <div class="title">基础信息</div>
            </div>
            <div class="mr-20px pointer" main-color @click="change('baseInfo')">修改</div>
          </div>
          <div class="w-full pl-10px pt-10px">已选字段</div>
          <div class="container">
            <div class="item_title" v-for="item in corpFileds.baseInfo" :key="item.title">
              <div class="flex align-center" justify-between>
                <div>
                  <span>{{ item.name }}</span>
                  <span class="pl-10px" main-color v-if="item.required">(必选)</span>
                </div>
                <el-icon v-if="!item.required" class="pointer" @click="remove('baseInfo', item)">
                  <Close />
                </el-icon>
              </div>
            </div>
          </div>
        </div>
        <div class="item" border-right>
          <div class="header">
            <div class="flex align-center">
              <div class="line"></div>
              <div class="title">辅助信息</div>
            </div>
            <div class="mr-20px pointer" main-color @click="change('viceInfo')">修改</div>
          </div>
          <div class="w-full pl-10px pt-10px">已选字段</div>
          <div class="container">
            <div class="item_title" v-for="item in corpFileds.viceInfo" :key="item.title">
              <div class="flex align-center" justify-between>
                <div>
                  <span>{{ item.name }}</span>
                  <span class="pl-10px" main-color v-if="item.required">(必选)</span>
                </div>
                <el-icon v-if="!item.required" class="pointer" @click="remove('viceInfo', item)">
                  <Close />
                </el-icon>
              </div>
            </div>
          </div>
        </div>
        <div class="item">
          <div class="header">
            <div class="flex align-center">
              <div class="line"></div>
              <div class="title">健康信息</div>
            </div>
            <div class="mr-20px pointer" main-color @click="change('healthInfo')">修改</div>
          </div>
          <div class="w-full pl-10px pt-10px">已选字段</div>
          <div class="container">
            <div class="item_title" v-for="item in corpFileds.healthInfo" :key="item.title">
              <div class="flex align-center" justify-between>
                <div>
                  <span>{{ item.name }}</span>
                  <span class="pl-10px" main-color v-if="item.required">(必选)</span>
                </div>
                <el-icon v-if="!item.required" class="pointer" @click="remove('healthInfo', item)">
                  <Close />
                </el-icon>
              </div>
            </div>
          </div>
        </div>
      </div>
    </layout-main>
  </my-layout>
  <edit-fileds ref="editFiledsRef" @getItems="onGetItems"></edit-fileds>
</template>
<script setup>
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import { computed, ref } from "vue";
import editFileds from "./edit-fields/index.vue";
import { updateCorpInfo } from "@/api/corp.js";
import { memberStore } from "@/store/member";
import { ElMessageBox } from "element-plus";
let { corpInfo } = memberStore();
let platformFields = ref({
  baseInfo: [],
  viceInfo: [],
  healthInfo: [],
});
let corpFileds = ref({
  baseInfo: [],
  viceInfo: [],
  healthInfo: [],
});
if (corpInfo.corpFileds) {
  corpFileds.value = corpInfo.corpFileds;
}
// 修改
const editFiledsRef = ref("");
function change(type) {
  editFiledsRef.value.openDialog(platformFields.value[type], corpFileds.value[type], type);
}

function onGetItems(list, type) {
  corpFileds.value[type] = list;
  updateCorpFields();
}

async function updateCorpFields() {
  const params = {
    corpFileds: corpFileds.value,
  };
  await updateCorpInfo(params);
  memberStore().getCorpInfo();
}

async function remove(type, item) {
  const obj = {
    baseInfo: "基础信息",
    viceInfo: "辅助信息",
    healthInfo: "健康信息",
  };
  await ElMessageBox.confirm(`确认删除该${obj[type]}字段吗？`, "提示");
  corpFileds.value[type] = corpFileds.value[type].filter((i) => i.title !== item.title);
  updateCorpFields();
}
</script>

<style lang="scss" scoped>
.source {
  display: flex;
  width: 100%;
  height: 100%;

  .item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;

    .header {
      height: 50px;
      line-height: 50px;
      background: rgb(236, 244, 255);
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        height: 50px;
      }

      .line {
        width: 4px;
        height: 17px;
        background-color: #006eff;
        margin: 0 5px 0 10px;
      }
    }

    .container {
      display: flex;
      flex-wrap: wrap;
      padding: 15px;
    }

    .item_title {
      width: 100%;
      /* 将子元素的宽度设置为容器的一半，这样一行只能容纳两个元素 */
      color: #888888;
      padding-bottom: 10px;
      padding-left: 20px;
    }

    .addButton {
      margin-top: 30px;
    }

    .context-box {
      height: 50px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      flex-wrap: wrap;

      .left {
        font-size: 14px;
        padding-left: 15px;
      }

      .right {
        display: flex;
        color: #006eff;
        font-size: 14px;
        width: 130px;
      }
    }
  }
}
</style>
