<template>
  <el-dialog
    :title="'预约详情'"
    :model-value="visible"
    @close="onClose"
    :width="width"
    :destroy-on-close="true"
  >
    <div class="p-20px">
      <div class="grid grid-cols-2 gap-4">
        <div class="form-item">
          <div class="label">预约日期：</div>
          <div class="value">{{ appointment.appointmentDate }}</div>
        </div>
        <div class="form-item">
          <div class="label">预约时间：</div>
          <div class="value">{{ appointment.appointmentTime }}</div>
        </div>
        <div class="form-item">
          <div class="label">客户姓名：</div>
          <div class="value">{{ appointment.name }}</div>
        </div>
        <div class="form-item">
          <div class="label">联系方式：</div>
          <div class="value">{{ appointment.mobile }}</div>
        </div>
        <div class="form-item">
          <div class="label">到院状态：</div>
          <div class="value" :style="{ color: appointment.isArrived ? 'green' : 'red' }">
            {{ appointment.isArrived ? '已到院' : '未到院' }}
          </div>
        </div>
        <div class="form-item">
          <div class="label">咨询时间：</div>
          <div class="value">
            {{ appointment.consultTime ? dayjs(appointment.consultTime).format('YYYY-MM-DD HH:mm') : '' }}
          </div>
        </div>
        <div class="form-item col-span-2">
          <div class="label">咨询项目：</div>
          <div class="value">{{ appointment.consultProject }}</div>
        </div>
        <div class="form-item col-span-2">
          <div class="label">咨询情况：</div>
          <div class="value">{{ appointment.consultSituation }}</div>
        </div>
        <div class="form-item col-span-2">
          <div class="label">信息来源：</div>
          <div class="value">{{ appointment.infoSource }}</div>
        </div>
        <div class="form-item">
          <div class="label">所属开发：</div>
          <div class="value">
            <ww-user v-if="appointment.introducer" :openid="appointment.introducer" />
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="onClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import dayjs from 'dayjs';
import WwUser from '@/components/ww-user/index.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  width: {
    type: [String, Number],
    default: '30%',
  },
  appointment: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(['close']);

const onClose = () => {
  emit('close');
};
</script>

<style scoped>
.form-item {
  display: flex;
  margin-bottom: 12px;
}
.label {
  width: 100px;
  color: #606266;
  font-weight: 500;
  text-align: right;
  margin-right: 10px;
}
.value {
  flex: 1;
  word-break: break-all;
}
</style>
