<template>
  <my-layout>
    <layout-main class="text-14px">
      <div class="flex justify-between py-12px px-15px border-b border-gray-100 bg-white">
        <div class="label--active flex-shrink-0 mr-10px required">执行时间</div>
        <div class="text-blue-500">{{ time }}</div>
      </div>
      <div class="flex items-center py-12px px-15px mb-15px border-b border-gray-100 bg-white">
        <div class="text-gray-500 flex-shrink-0 mr-10px">计划开始后</div>
        <el-input :min="minTime" type="number" :model-value="taskForm.taskTime" class="flex-grow" @update:model-value="handleInput($event, 'taskTime')">
          <template #append>
            <el-select :model-value="taskForm.timeType" class="w-60px" @change="selectTimeType">
              <el-option v-for="opt in timeTypeList" :label="opt.label" :value="opt.value" />
            </el-select>
          </template>
        </el-input>
      </div>
      <div class="flex justify-between py-12px px-15px border-b border-gray-100 bg-white">
        <div class="label--active required">待办类型</div>
      </div>
      <div class="p-15px mb-15px bg-white">
        <el-select v-model="taskForm.eventType" filterable style="width: 100%" placeholder="请选择待办类型">
          <el-option v-for="item in eventTypeList" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </div>

      <div class="flex justify-between py-12px px-15px border-b border-gray-100 bg-white">
        <div class="label--active required">任务内容</div>
      </div>
      <div class="p-15px mb-15px bg-white">
        <el-input type="textarea" :model-value="taskForm.taskContent" resize="none" show-word-limit :autosize="{ minRows: 5, maxRows: 8 }" placeholder="请输入任务内容" :maxlength="500" @update:model-value="handleInput($event, 'taskContent')" />
      </div>
      <div class="flex justify-between py-12px px-15px border-b border-gray-100 bg-white">
        <div class="label--active">向客户发送</div>
      </div>
      <div class="p-15px border-b border-gray-100 bg-white">
        <el-input type="textarea" :model-value="taskForm.sendContent" resize="none" show-word-limit :autosize="{ minRows: 5, maxRows: 8 }" placeholder="请输入任务内容" :maxlength="3000" @update:model-value="handleInput($event, 'sendContent')" />
      </div>
      <div class="flex items-center justify-between py-12px px-15px border-b border-gray-100 bg-white" @click="showPopover()">
        <div class="text-gray-500 flex-shrink-0 mr-10px">发送附件</div>
        <div class="ml-5px w-0 flex-grow flex items-center justify-end">
          <div class="truncate text-blue-500">
            {{ taskForm.pannedEventSendFile && taskForm.pannedEventSendFile.name ? taskForm.pannedEventSendFile.name : "" }}
          </div>
          <el-icon>
            <ArrowRight />
          </el-icon>
        </div>
      </div>
      <div class="h-30px"></div>
    </layout-main>
    <layout-item>
      <div class="p-15px flex bg-white shadow-up relative z-2" common-shadow--r>
        <el-button class="flex-grow" plain @click="cancel()">取消</el-button>
        <el-button class="flex-grow ml-15px" type="primary" @click="save()" :loading="loading">保存</el-button>
      </div>
    </layout-item>
  </my-layout>
  <action-sheet :visible="showSheet" :item-list="actions" @close="hidePopover" @select="selectAction" />
</template>
<script setup>
import { onMounted, ref, computed } from "vue";
import { taskStore } from "@/store/managePlan/task";
import { memberMangePlanStore } from "@/store/managePlan/memberMangePlan";
import { storeToRefs } from "pinia";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import ActionSheet from "@/components/weui/action-sheet.vue";
import { ToDoEventType } from "@/baseData";
import dayjs from "dayjs";
import useChatBarSub from "../../useChatBarSub";
import { getRandomStr } from "@/utils";
import { ElMessage } from "element-plus";
const { taskForm } = storeToRefs(taskStore());
const { updatePlanTask, createTask } = taskStore();
const { getPlanTaskFnc, planForm, customer, taskList } = storeToRefs(memberMangePlanStore());
const eventTypeList = Object.keys(ToDoEventType).map((key) => ({ label: ToDoEventType[key], value: key }));

const minTime = ref(0);
const emits = defineEmits(["cancel", "selectFile"]);
const { sub, trigger } = useChatBarSub();
const timeTypeList = [
  { label: "天", value: "day" },
  { label: "周", value: "week" },
  { label: "月", value: "month" },
  { label: "年", value: "year" },
];
const form = {};
function cancel() {
  emits("cancel");
}

const props = defineProps({
  operationType: {
    type: String,
    default: "add",
  },
});
const time = computed(() => {
  if (!planForm.value.planExecutionTime) return "";
  const { taskTime, timeType } = taskForm.value;
  if (!taskTime) return dayjs(planForm.value.planExecutionTime).format("YYYY-MM-DD");
  if (!timeType) {
    return dayjs(planForm.value.planExecutionTime).add(taskTime, "day").format("YYYY-MM-DD");
  } else {
    return dayjs(planForm.value.planExecutionTime).add(taskTime, timeType).format("YYYY-MM-DD");
  }
});
onMounted(() => {
  if (props.operationType === "add") {
    taskForm.value = {};
  }
  taskForm.value.timeType = taskForm.value.timeType || "day";
  taskForm.value.taskTime = taskForm.value.taskTime || "0";
});
function selectTimeType(val) {
  if (val === "day") {
    minTime.value = 0;
  } else if (val === "week" || val === "month" || val === "year") {
    minTime.value = 1;
  }
  taskForm.value.timeType = val;
  form["timeType"] = val;
  if (taskForm.value.taskTime < minTime.value) {
    taskForm.value.taskTime = minTime.value;
    form["taskTime"] = minTime.value;
  }
}
function handleInput(val, key) {
  form[key] = val;
  taskForm.value[key] = val;
}
const loading = ref(false);
async function save() {
  const { taskTime, taskContent } = taskForm.value;
  if (!taskTime) {
    ElMessage.warning("请填写开始时间");
    return;
  }
  if (!taskContent) {
    ElMessage.warning("请填写任务内容");
    return;
  }
  if (props.operationType === "add") {
    if (!planForm.value._id) {
      taskForm.value["taskId"] = getRandomStr();
      taskList.value.push(taskForm.value);
      cancel();
    } else {
      loading.value = true;
      form["taskId"] = getRandomStr();
      const { taskId, taskContent, sendContent, taskTime = "0", timeType, pannedEventSendFile = {}, eventType } = form;
      const { customerId, executeTeamId, executeTeamName, executorUserId, planExecutionTime, planId, planName, _id } = planForm.value;
      const { externalUserId, name } = customer.value;
      const query = {
        corpId: localStorage.getItem("corpId"),
        taskId,
        planId,
        planName,
        creatorUserId: localStorage.getItem("userId"),
        customerId,
        customerName: name,
        sendContent,
        executeTeamId,
        executeTeamName,
        executorUserId,
        customerUserId: externalUserId,
        planExecutionTime,
        eventType,
        taskContent,
        taskTime,
        timeType,
        memberPlanId: _id,
        pannedEventSendFile,
      };
      const res = await createTask(query);
      if (res) cancel();
      loading.value = false;
    }
  } else {
    if (!planForm.value._id) {
      const index = taskList.value.findIndex((item) => item.taskId === taskForm.value.taskId);
      taskList.value[index] = taskForm.value;
    } else {
      const { taskTime, timeType } = taskForm.value;
      const { planExecutionTime } = planForm.value;
      if (timeType) {
        form["planExecutionTime"] = dayjs(planExecutionTime).add(taskTime, timeType).valueOf();
      } else {
        form["planExecutionTime"] = dayjs(planExecutionTime).add(taskTime, "day").valueOf();
      }
      await updatePlanTask(taskForm.value._id, form);
    }
    cancel();
  }
}
const actions = [{ label: "宣教文章" }, { label: "问卷调查" }, { label: "删除附件" }];
const showSheet = ref(false);
function showPopover() {
  showSheet.value = true;
}
function hidePopover() {
  showSheet.value = false;
}

function selectAction({ label }) {
  showSheet.value = false;
  if (label === "删除附件") {
    taskForm.value.pannedEventSendFile = {};
    form["pannedEventSendFile"] = "";
    return;
  }
  const eventName = `select-manage-plan-node-file_${+new Date()}`;
  sub(eventName, (data) => {
    if (data.type === "article") {
      taskForm.value.pannedEventSendFile = { ...data.data, name: data.data.title };
    } else if (data.type === "survery") {
      taskForm.value.pannedEventSendFile = { ...data.data };
    }
    form["pannedEventSendFile"] = taskForm.value.pannedEventSendFile;
  });
  emits("selectFile", {
    eventName,
    type: label === "宣教文章" ? "article" : "survery",
  });
}
</script>
<style scoped>
.label--active::before {
  content: "";
  width: 3px;
  height: calc(100% - 2px);
  background: #0094ff;
  display: inline-block;
  margin-right: 5px;
  transform: translateY(1px);
  border-radius: 1px;
}

.required::after {
  margin-left: 2px;
  content: "*";
  color: red;
}
</style>