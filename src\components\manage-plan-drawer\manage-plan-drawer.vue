<!-- 回访计划 -->
<template>
  <el-drawer :model-value="visible" title="回访计划" size="70%" style="--el-drawer-padding-primary: 0" @close="close()">
    <my-layout class="bg-[#f2f3f4]">
      <layout-item>
        <div class="p-10px flex items-center gap-4">
          <div class="flex items-center gap-4 flex-grow">
            <check-box-filter v-model="visitTypes" label="回访类型" :list="visitTypeList">
              <template #item="{ item }">
                {{ item.label }}
              </template>
            </check-box-filter>
            <check-box-filter v-model="visitStatuses" label="回访状态" :list="visitStatusList">
              <template #item="{ item }">
                {{ item.label }}
              </template>
            </check-box-filter>
          </div>
          <div class="flex items-center gap-2">
            <el-button type="primary" :icon="Plus" @click="addTask()">新增任务</el-button>
            <el-button type="primary" :icon="Plus" @click="useTemplate()">使用模板</el-button>
          </div>
        </div>
      </layout-item>
      <layout-main :scroll="false">
        <el-table v-loading="loading" border stripe class="h-full" :data="tableData">
          <el-table-column label="状态" prop="status" :width="100">
            <template #default="{ row: { status } }">
              <span :class="statusClassNames[status]">{{ statusNames[status] }}</span>
            </template>
          </el-table-column>
          <el-table-column label="回访日期" prop="plannedExecutionTime" :width="120">
            <template #default="{ row: { plannedExecutionTime } }">
              {{ plannedExecutionTime ? dayjs(plannedExecutionTime).format("YYYY-MM-DD") : "" }}
            </template>
          </el-table-column>
          <el-table-column label="回访类型" prop="type" :min-width="120">
            <template #default="{ row: { eventType } }">
              {{ ServiceType[eventType] }}
            </template>
          </el-table-column>
          <!-- <el-table-column label="回访跟进方式" prop="method" :min-width="140">
            <template #default="{ row: { executeMethod } }">
              {{ executeMethodMap[executeMethod] || "待办" }}
            </template>
          </el-table-column> -->
          <el-table-column label="回访目的" prop="taskContent" :min-width="120">
            <template #default="{ row: { taskContent } }">
              <el-tooltip :disabled="!taskContent" placement="top" effect="light" :content="taskContent" popper-class="max-w-480px">
                <div class="truncate">{{ taskContent }}</div>
                <template #content>
                  <span class="text-14px">{{ taskContent }}</span>
                </template>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="回访结果" prop="result" :min-width="240">
            <template #default="{ row }">
              <div v-if="row.status === 'processing' && row.executorUserId === memberInfo.userid" class="text-blue-500 cursor-pointer line-clamp-2 leading-5 max-h-10 overflow-hidden" @click="setAsComplete(row)" :title="row.result || '点击设为完成'">
                {{ row.result || "点击设为完成" }}
              </div>
              <div v-else-if="(row.status === 'treated' || row.status === 'cancelled') && row.executorUserId === memberInfo.userid" class="text-blue-500 cursor-pointer line-clamp-2 leading-5 max-h-10 overflow-hidden" @click="editResult(row)" :title="row.result || '点击编辑处理结果'">
                {{ row.result || "点击编辑处理结果" }}
              </div>
              <div v-else class="line-clamp-2 leading-5 max-h-10 overflow-hidden" :title="row.result">
                {{ row.result || "-" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="处理时间" prop="endTime" :width="160">
            <template #default="{ row: { endTime } }">
              {{ endTime ? dayjs(endTime).format("YYYY-MM-DD HH:mm") : "" }}
            </template>
          </el-table-column>
          <el-table-column label="回访人员" prop="executorUserId" :min-width="160">
            <template #default="{ row: { executorUserId } }">
              <ww-user v-if="executorUserId" :openid="executorUserId" />
            </template>
          </el-table-column>
          <el-table-column label="创建日期" prop="endTime" :width="160">
            <template #default="{ row: { createTime } }">
              {{ createTime ? dayjs(createTime).format("YYYY-MM-DD HH:mm") : "" }}
            </template>
          </el-table-column>
          <el-table-column label="创建人" prop="executorUserId" :min-width="160">
            <template #default="{ row: { creatorUserId } }">
              <ww-user v-if="creatorUserId" :openid="creatorUserId" />
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" prop="action" :width="160">
            <template #default="{ row, $index }">
              <div class="flex items-center">
                <el-text class="mr-10px pointer" type="primary" text size="small" @click="deal(row)">详情</el-text>
                <el-text class="pointer" v-if="row.status === 'notStart' && row.creatorUserId === memberInfo.userid" type="primary" text size="small" @click="edit($index)">编辑</el-text>
                <template v-else-if="row.status === 'processing' && row.executorUserId === memberInfo.userid">
                  <el-text class="pointer mr-10px" type="primary" text size="small" @click="setAsComplete(row)">设为完成</el-text>
                  <el-text class="pointer" type="primary" text size="small" @click="cancel(row)">取消</el-text>
                </template>
                <el-text class="pointer" v-else-if="(row.status === 'treated' || row.status === 'cancelled') && row.executorUserId === memberInfo.userid" type="primary" text size="small" @click="editResult(row)">编辑</el-text>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </layout-main>
      <!-- <layout-item>
        <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize" @handle-current-change="changePage" />
      </layout-item> -->
    </my-layout>
  </el-drawer>
  <task-drawer :type="taskDrawerType" :teamId="teamId" :memberList="teamMemberList" :customer="customer" :data="task" :visible="taskDrawerVisible" @close="taskDrawerVisible = false" @change="changeTask" />
  <result-modal :data="task" :visible="resultModalVisible" :width="width" :isSetComplete="isSetCompleteAction" @close="closeResultModalWithCleanup" @change="getList" />
  <team-plan-drawer :teamId="teamId" :visible="teamPlanDrawerVisible" @close="teamPlanDrawerVisible = false" @onSelect="selectPlan" />
  <new-plan-drawer ref="newPlanDrawerRef" :customer="customer" :teamId="teamId" :memberList="teamMemberList" :plan="currentPlan" :visible="newPlanDrawerVisible" @close="closeNewPlanDrawer" @onEdit="editTask" @change="getList" />
  <el-image-viewer v-if="showViewer" :url-list="[pic]" @close="cancelPreview()" />
  <todo-detail-drawer :visible="todoDetailDrawerVisible" :form="currentTodo" :isFromDetail="isFromDetailButton" @close="todoDetailDrawerVisible = false" @change="getList" />
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { ElMessage, ElMessageBox } from "element-plus";
import dayjs from "dayjs";
import { getCustomerTodos, setTodoStatus } from "@/api/todo";
import { getGroupmsgMsgidsByTaskIds } from "@/api/groupmsg";
import { getCustomerInfoById } from "@/api/member";
import { getCustomTeamData } from "@/api/corp";

import { ServiceType, ToDoEventType } from "@/baseData";
import { statusClassNames, statusNames, getVisitPlanStatus, executeMethodMap } from "@/baseData/visit-plan";

import useElPagination from "@/hooks/useElPagination";
import useModal from "@/hooks/useModal";
import { teamStore } from "@/store/team";
import { memberStore } from "@/store/member";
import { openChatWindow } from "@/utils/common";

import { Plus } from "@element-plus/icons-vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import newPlanDrawer from "./new-plan-drawer.vue";
import resultModal from "./result-modal.vue";
import taskDrawer from "./task-drawer.vue";
import teamPlanDrawer from "./team-plan-drawer.vue";
import todoDetailDrawer from "./todo-detail-drawer.vue";
import { CheckBoxFilter, DateRangeFilter, InputFilter, filterCustomerSource, filterInfoSource, expandFilterBox, RadioFilter } from "@/components/filter-bar";
const pic = new URL("@/assets/group-msg.png", import.meta.url).href; // 默认logo
const emits = defineEmits(["close"]);
const props = defineProps({
  customer: { type: Object, default: () => ({}) },
  teamId: { type: String, default: "" },
  visible: { type: Boolean, default: false },
});
const { currentTeamId } = storeToRefs(teamStore());
const { memberInfo } = storeToRefs(memberStore());
const currentPlan = ref({});
const loading = ref(false);
const drawerLoading = ref(false);
// 筛选相关变量
const visitTypes = ref([]); // 回访类型筛选
const visitStatuses = ref([]); // 回访状态筛选
// 筛选选项数据
const visitTypeList = computed(() => {
  return Object.keys(ToDoEventType).map((key) => ({
    label: ToDoEventType[key],
    value: key,
  }));
});
const visitStatusList = computed(() => {
  return Object.keys(statusNames).map((key) => ({
    label: statusNames[key],
    value: key,
  }));
});
const taskDrawerVisible = ref(false);
const teamPlanDrawerVisible = ref(false);
const newPlanDrawerVisible = ref(false);
const task = ref({});
const taskDrawerType = ref("");
const showViewer = ref(false);
const total = ref(0);
const newPlanDrawerRef = ref(null);
const currentTodo = ref({});
const todoDetailDrawerVisible = ref(false);
const isFromDetailButton = ref(false); // 标识是否从详情按钮进入
const isSetCompleteAction = ref(false); // 标记是否为设为完成操作
const { close: closeResultModal, show, visible: resultModalVisible, width } = useModal(640);

// 扩展 closeResultModal 以清除取消标记
const originalCloseResultModal = closeResultModal;
const closeResultModalWithCleanup = () => {
  if (task.value.isCancelAction) {
    delete task.value.isCancelAction;
  }
  originalCloseResultModal();
};
const { list, page, pageSize, changePage, changeSize } = useElPagination(getList);
const teamId = computed(() => props.teamId || currentTeamId.value);
const tableData = computed(() => {
  let dataWithStatus = list.value.map((item) => ({ ...item, status: getVisitPlanStatus(item) }));

  // 应用筛选逻辑
  // 状态筛选
  if (visitStatuses.value.length > 0) {
    dataWithStatus = dataWithStatus.filter((item) => visitStatuses.value.includes(item.status));
  }
  // 类型筛选
  if (visitTypes.value.length > 0) {
    dataWithStatus = dataWithStatus.filter((item) => {
      // 使用 eventType 字段直接匹配筛选值
      return visitTypes.value.includes(item.eventType);
    });
  }
  // 排序逻辑
  return dataWithStatus.sort((a, b) => {
    const statusPriority = {
      processing: 1,
      treated: 2,
      cancelled: 2,
      notStart: 3,
    };

    const aPriority = statusPriority[a.status] || 4;
    const bPriority = statusPriority[b.status] || 4;

    // 如果状态优先级不同，按状态排序
    if (aPriority !== bPriority) {
      return aPriority - bPriority;
    }

    // 如果状态相同，则进行时间排序
    // 已完成/已取消状态，按计划执行时间倒序排序
    if (a.status === "treated" || a.status === "cancelled") {
      // 倒序：b的时间减a的时间
      return new Date(b.plannedExecutionTime || 0) - new Date(a.plannedExecutionTime || 0);
    } else {
      // 其他状态按计划执行时间升序排序
      return new Date(a.plannedExecutionTime || 0) - new Date(b.plannedExecutionTime || 0);
    }
  });
});
const customerTeamIds = ref([]);
const canSet = computed(() => customerTeamIds.value.includes(teamId.value));
const teamMemberList = ref([]);

function changeTask(row) {
  if (taskDrawerType.value === "editManagementPlanTask") {
    newPlanDrawerRef.value.changeTask(row);
  } else if (taskDrawerType.value === "editTask") {
    getList();
  }
}
function chat(customerId, externalUserId) {
  if (customerId && externalUserId) {
    openChatWindow(customerId, externalUserId);
  }
}

function close() {
  emits("close");
}
//编辑
async function edit(index = -1) {
  if (teamMemberList.value.length === 0) {
    await getTeamMembers();
  }
  taskDrawerType.value = "editTask";
  const item = index === -1 ? { executeMethod: "todo" } : { ...tableData.value[index] };
  item.planExecutionTime = item.plannedExecutionTime;
  task.value = item;
  taskDrawerVisible.value = true;
}
//详情
function deal(row) {
  if (row.executeMethod === "groupTask") {
    showViewer.value = true;
    return;
  }
  currentTodo.value = row;
  isFromDetailButton.value = true; // 标记为从详情按钮进入
  todoDetailDrawerVisible.value = true;
}
//结果编辑
function editResult(item) {
  task.value = item;
  isSetCompleteAction.value = false; // 标记为普通编辑操作
  show();
}

// 设为完成 - 打开编辑结果对话框并标记为设置完成
function setAsComplete(item) {
  task.value = item;
  isSetCompleteAction.value = true; // 标记为设为完成操作
  show();
}

function cancel(item) {
  task.value = item;
  task.value.isCancelAction = true;
  isSetCompleteAction.value = false;
  show();
}

function selectPlan(plan) {
  currentPlan.value = { ...plan };
  newPlanDrawerVisible.value = true;
}

function closeNewPlanDrawer() {
  currentPlan.value = {};
  newPlanDrawerVisible.value = false;
}

function editTask(row = {}) {
  taskDrawerType.value = "editManagementPlanTask";
  task.value = row;
  taskDrawerVisible.value = true;
}

async function getList() {
  loading.value = true;
  const { data, success, message } = await getCustomerTodos({
    customerId: props.customer._id,
    page: 1,
    pageSize: 1000,
    teamId: teamId.value,
  });
  list.value = data && Array.isArray(data.data) ? data.data : [];
  total.value = data.total || 0;
  // 获取群发任务列表
  const taskIds = list.value.filter((item) => item.executeMethod === "groupTask").map((item) => item.taskId);
  const groupmsgResult = await getGroupmsgResult(taskIds);
  console.log("groupmsgResult", groupmsgResult);
  list.value = list.value.map((item) => {
    if (item.executeMethod === "groupTask") {
      const { sendSatus, result } = groupmsgResult[item.taskId] || 0;
      if (sendSatus) {
        item.eventStatus = "treated";
        item.result = result;
      }
    }
    return item;
  });
  if (!success) {
    ElMessage.error(message || "获取失败");
  }
  loading.value = false;
}

async function getGroupmsgResult(taskIds) {
  const { data, success, message } = await getGroupmsgMsgidsByTaskIds({ taskIds });
  if (success) {
    return data.data.reduce((acc, cur) => {
      acc[cur.taskId] = cur;
      return acc;
    }, {});
  } else {
    return [];
  }
}

function cancelPreview() {
  showViewer.value = false;
}

// 获取客户的责任人
async function getCustomerTeams(id) {
  if (!props.customer || !props.customer._id) {
    customerTeamIds.value = [];
    return;
  }
  const { data } = await getCustomerInfoById(memberInfo.value.userid, { _id: props.customer._id });
  const customer = data && Array.isArray(data.data) ? data.data[0] : null;
  if (customer && Array.isArray(customer.teamId)) {
    customerTeamIds.value = [...customer.teamId];
  } else if (customer && typeof customer.teamId === "string" && customer.teamId.trim()) {
    customerTeamIds.value = [customer.teamId];
  } else {
    customerTeamIds.value = [];
  }
}
async function getTeamMembers() {
  const params = { teamIds: [teamId.value], corpId: memberInfo.value.corpId, fields: ["memberList"] };
  const { data } = await getCustomTeamData(params);
  const team = data && Array.isArray(data.data) ? data.data[0] : null;
  teamMemberList.value = team && Array.isArray(team.memberList) ? team.memberList : [];
}

async function addTask() {
  try {
    drawerLoading.value = true;
    await judge();
    if (teamMemberList.value.length === 0) {
      await getTeamMembers();
    }
    drawerLoading.value = false;
    edit();
  } catch (e) {
    drawerLoading.value = false;
  }
}
async function useTemplate() {
  try {
    drawerLoading.value = true;
    // await judge();
    if (teamMemberList.value.length === 0) {
      await getTeamMembers();
    }
    drawerLoading.value = false;
    teamPlanDrawerVisible.value = true;
  } catch (e) {
    drawerLoading.value = false;
  }
}

async function judge() {
  if (customerTeamIds.value.length === 0) {
    await getCustomerTeams();
  }
  if (canSet.value) return true;
  ElMessage.info("客户未授权当前团队");
  return Promise.reject("客户未授权当前团队");
}

watch(
  () => props.visible,
  (n) => {
    if (n) {
      customerTeamIds.value = [];
      teamMemberList.value = []; // 重置筛选条件
      visitTypes.value = [];
      visitStatuses.value = [];
      // 重置标识
      isFromDetailButton.value = false;
      changePage(1);
    }
  }
);
</script>
<style lang="scss" scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-clamp: 2;
  overflow: hidden;
  word-break: break-all;
}
</style>