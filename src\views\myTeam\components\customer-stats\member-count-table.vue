<template>
  <div flex items-center justify-between p-15>
    <!-- <div font-semibold font-16 flex-shrink-0>新增客户趋势</div> -->
    <div flex-shrink-0>
      <el-text>排序：</el-text>
      <template v-for="(type, idx) in sortType" :key="type.key">
        <el-text pointer :type="currentSort === type.key ? 'primary' : ''" @click="changeSort(type.key)">
          {{ type.label }}
        </el-text>
        <el-divider v-if="idx < sortType.length - 1" direction="vertical" />
      </template>
    </div>
  </div>
  <el-table stripe :data="list">
    <el-table-column prop="userId" label="成员">
      <template #default="{ row: { userId } }">
        <ww-user v-if="userId" :openid="userId"></ww-user>
        <span v-else></span>
      </template>
    </el-table-column>
    <el-table-column prop="customerCount" label="新增客户数" />
    <el-table-column prop="serviceCount" label="累计服务次数" />
    <el-table-column prop="visitCount" label="累计报到人次" />
    <!-- <el-table-column prop="rate" label="客户满意度">
      <template #default="{ row }">
        <five-star color="#F56C6C" :score="4" />
      </template>
    </el-table-column> -->
    <el-table-column fixed="right" prop="action" width="80" label="操作">
      <template #default="{ row }">
        <el-text type="primary" pointer @click="show(row)" v-if="cardType !== 'visitCount'">详情</el-text>
      </template>
    </el-table-column>
  </el-table>
</template>
<script setup>
import { ref, inject, watch } from "vue";
import FiveStar from "@/components/five-star";
import WwUser from "@/components/ww-user/index.vue";
import { dates } from "./useDateTag";
import { getUsersMemberByTeamId } from "@/api/member.js";
const props = defineProps({
  currentTeam: {
    type: Object,
    default: {},
  },
  cardType: {
    type: String,
    default: "",
  },
});
const list = ref([{}]);
const currentSort = ref("customer");
const sortType = [
  { label: "按客户数", key: "customerCount" },
  { label: "按服务人次", key: "serviceCount" },
  { label: "按报到人次", key: "visitCount" },
  // { label: "按客户满意度", key: "satisfaction" },
];
function changeSort(key) {
  if (currentSort.value == key) return;
  currentSort.value = key;
  if (list.value.length > 1 && list.value.some((i) => key in i)) {
    list.value = list.value.sort((pre, next) => next[key] - pre[key]);
  }
}
watch(
  () => props.currentTeam,
  () => {
    getData();
  },
  { immediate: true }
);
watch(dates, () => {
  getData();
});

const emits = defineEmits(["show"]);
function show(row) {
  emits("show", row.userId);
}
async function getData() {
  let { data, success } = await getUsersMemberByTeamId(dates.value);
  if (success) {
    list.value = data.data;
  }
}
</script>
