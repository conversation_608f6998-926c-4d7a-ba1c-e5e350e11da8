<template>
  <div class="flex flex-col h-full rect-border-box">
    <div class="flex-shrink-0 relative mb-[1rem]">
      <title-bar title="服务满意度分析" />
      <div class="absolute top-0 right-0 text-13px flex h-full items-center">
        <div v-for="(item, idx) in typeList" :key="item.value" class="text-white cursor-pointer mr-10px hover:text-[#f9b008]" :class="[type === item.value ? 'text-[#f9b008]' : '']" @click="changeTime(item.value)">
          {{ item.label }}
        </div>
        <div v-if="viewType === 'app'" class="text-white cursor-pointer hover:text-[#f9b008]" @click="more()">
          更多
          <el-icon class="transform translate-y-2px">
            <ArrowRightBold />
          </el-icon>
        </div>
      </div>
    </div>
    <div class="flex-grow relative">
      <div class="absolute inset-0 flex">
        <div class="w-1/2" ref="chartRef"></div>
        <div class="w-1/2 flex-col flex justify-center">
          <div v-for="(i, idx) in rateList" :key="i.name" class="flex items-center text-xs text-white mb-5px">
            <el-rate :model-value="i.name" size="small" disabled :style="{ '--el-rate-fill-color': i.color }" />
            <div class="flex-grow mx-10px truncate w-0">{{ i.percent }}%</div>
            <div class="text-right flex-shrink-0">{{ i.value }}人</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { onMounted, ref } from "vue";
import titleBar from "./title-bar.vue";
import { getCorpRateStats } from "@/api/knowledgeBase";
import useRateChart from "../charts/use-rate-chart";
import dayjs from "dayjs";
import isoWeek from "dayjs/plugin/isoWeek";
import useNextMenu from "./useNextMenu";
dayjs.extend(isoWeek);
const typeList = [
  { label: "全部", value: "all" },
  { label: "今日", value: "thisDay" },
  { label: "本周", value: "thisWeek" },
  { label: "上周", value: "lastWeek" },
  { label: "本月", value: "thisMonth" },
  { label: "上月", value: "lastMonth" },
];
const props = defineProps({
  viewType: { type: String, default: "" },
});
const rateList = ref([]);
const colors = ["#03D6EF", "#1276FF", "#7114FF", "#F83818", "#FFC513", "#67C23A", "#409EFF"];
const type = ref("all");
const chartRef = ref(null);
const paint = useRateChart(chartRef, rateList, colors);
function changeTime(value) {
  if (type.value !== value) {
    type.value = value;
    getData();
  }
}
const next = useNextMenu();
function more() {
  next("RATELIST", "评价管理");
}
async function getData() {
  const params = { corpId: localStorage.getItem("corpId") };
  if (type.value === "thisWeek") {
    params.startDate = dayjs().startOf("isoWeek").format("YYYY-MM-DD");
    params.endDate = dayjs().endOf("isoWeek").format("YYYY-MM-DD");
  } else if (type.value === "lastWeek") {
    params.startDate = dayjs().subtract(1, "week").startOf("isoWeek").format("YYYY-MM-DD");
    params.endDate = dayjs().subtract(1, "week").endOf("isoWeek").format("YYYY-MM-DD");
  } else if (type.value === "thisMonth") {
    params.startDate = dayjs().startOf("month").format("YYYY-MM-DD");
    params.endDate = dayjs().endOf("month").format("YYYY-MM-DD");
  } else if (type.value === "lastMonth") {
    params.startDate = dayjs().subtract(1, "month").startOf("month").format("YYYY-MM-DD");
    params.endDate = dayjs().subtract(1, "month").endOf("month").format("YYYY-MM-DD");
  } else if (type.value === "thisDay") {
    params.startDate = dayjs().format("YYYY-MM-DD");
    params.endDate = dayjs().format("YYYY-MM-DD");
  }
  const { data } = await getCorpRateStats(params);
  const list = Array.isArray(data.data) ? data.data.sort((a, b) => b.rate - a.rate) : [];
  let total = list.reduce((total, item) => total + (typeof item.count === "number" && item.count > 0 ? item.count : 0), 0);
  total = Math.max(1, total);
  rateList.value = list.map((item, idx) => {
    const i = {
      name: item.rate,
      value: typeof item.count === "number" && item.count > 0 ? item.count : 0,
      color: colors[idx % colors.length],
    };
    const percent = Math.floor((i.value * 10000) / total) / 100;
    i.percent = percent;
    return i;
  });
  paint();
}

onMounted(() => {
  getData();
});
</script>
