import dayjs from 'dayjs';
export const statusClassNames = {
  'notStart': 'font-semibold',
  'treated': 'text-green-500',
  'processing': 'text-red-500',
  'cancelled': 'text-gray-500'
}
export const statusNames = {
  'notStart': '未开始',
  'treated': '已完成',
  'processing': '处理中',
  'cancelled': '已取消'
}
export const executeMethodMap = {
  todo: '待办',
  groupTask: '群发'
}


export function getVisitPlanStatus(row) {
  if ((row.eventStatus === 'untreated') && dayjs().isBefore(dayjs(row.plannedExecutionTime))) {
    return 'notStart'
    // 任务已触发生成待办单/群发单，但员工还未处理
  } else if (row.eventStatus === 'untreated' && dayjs().isAfter(dayjs(row.plannedExecutionTime)) && dayjs().isBefore(dayjs(row.expireTime))) {
    return 'processing'
    // 待办单成功办结，群发单成功发送；
  } else if (row.eventStatus === 'treated') {
    return 'treated'
    // 待办单已取消或已过期，群发单群发失败。
  } else if (dayjs().isAfter(row.expireTime) || row.eventStatus === 'closed') {
    return 'cancelled'
  }
  return ''
}