<template>
  <el-scrollbar class="w-full" height="100%">
    <div class="sourceItem">
      <div class="context-box" border-bottom v-for="(item, index) in inUseItem" :key="item.sourceId" @click="selectSourceItem(item, 'inUse')" :class="{ selectActive: selectSource.sourceId == item.sourceId }">
        <div class="left">{{ item.sourceName }}</div>
        <div class="right">
          <div v-if="item.type !== 'fixed'" class="item" @click.stop="remove(item, index, 'inUse')">删除</div>
          <div v-if="item.type !== 'fixed'" class="item" @click.stop="edit(item, 'inUse')">重命名</div>
          <div class="item" @click.stop="changeType(item, '是否停用?', 'stop')">停用</div>
        </div>
      </div>
    </div>
  </el-scrollbar>
  <el-button type="primary" :icon="Plus" class="addButton" :class="props.disabled ? 'opacity-0' : ''" @click="addItem">新增</el-button>
  <div class="w-full pl-10px" grew-color border-bottom>已停用</div>
  <el-scrollbar class="w-full" wrap-style="max-height:500px">
    <div class="context-box" border-bottom v-for="(item, index) in stopUseItem" :key="item.sourceId" @click="selectSourceItem(item, 'stopUse')" :class="{ selectActive: selectSource.sourceId == item.sourceId }">
      <div class="left">
        {{ item.sourceName }}
      </div>
      <div class="right">
        <div v-if="item.type !== 'fixed'" class="item" @click.stop="remove(item, index, 'stopUse')">删除</div>
        <div v-if="item.type !== 'fixed'" class="item" @click.stop="edit(item, 'stopUse')">重命名</div>
        <div class="item" @click.stop="changeType(item, '是否启用?', 'enable')">启用</div>
      </div>
    </div>
  </el-scrollbar>
  <el-dialog :title="typeAction === 'add' ? '新增来源' : '编辑来源'" v-model="visible" width="400">
    <div p-15>
      <el-input v-model="newScurce" class="select" placeholder="请输入项目名称"></el-input>
    </div>
    <template #footer>
      <div text-center>
        <el-button class="w-100px" @click="onClose">取消</el-button>
        <el-button class="w-100px" type="primary" @click="saveItem">保存</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { Plus } from "@element-plus/icons-vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { computed, ref, toRefs } from "vue";
let visible = ref(false);
let newScurce = ref("");
let typeAction = ref("");
let editIndex = -1;
let props = defineProps({
  list: { type: Array, default: () => [] },
  disabled: { type: Boolean, default: false },
  parentId: {
    type: String,
    default: "",
  },
  selectSource: {
    type: Object,
    default: {},
  },
  type: {
    type: String,
    default: "",
  },
});
let sourceObj = toRefs(props);
const $emit = defineEmits(["select-item", "update"]);
function selectSourceItem(item) {
  let index = sourceObj.list.value.findIndex((i) => i.sourceId === item.sourceId);
  $emit("select-item", {
    type: sourceObj.type.value,
    index,
  });
}
let inUseItem = computed(() => {
  return sourceObj.list.value && Array.isArray(sourceObj.list.value) && sourceObj.list.value.filter((item) => !item.disable);
});
let stopUseItem = computed(() => {
  return sourceObj.list.value.filter((item) => item.disable);
});
function addItem() {
  if (props.disabled) return;
  typeAction.value = "add";
  newScurce.value = "";
  visible.value = true;
}
function onClose() {
  visible.value = false;
}
function saveItem() {
  if (!newScurce.value) {
    return;
  }
  if (sourceObj.list.value.some((item) => item.sourceName === newScurce.value)) {
    ElMessage.warning("来源名称已存在");
    return;
  }
  if (typeAction.value === "add") {
    addSelectItem();
  } else {
    editSelectItem();
  }
  visible.value = false;
}
function addSelectItem() {
  let item = {
    sourceId: new Date().getTime(),
    disable: false,
    sourceName: newScurce.value,
    children: [],
  };
  if (props.type === "oneClass") {
    sourceObj.list.value.splice(2, 0, item);
  } else {
    sourceObj.list.value.unshift(item);
  }

  $emit("update");
  const index = sourceObj.list.value.length - 1;
  $emit("select-item", {
    type: sourceObj.type.value,
    index,
  });
}
function editSelectItem() {
  sourceObj.list.value[editIndex].sourceName = newScurce.value;
  $emit("update");
}
function remove(item) {
  let index = sourceObj.list.value.findIndex((i) => i.sourceId === item.sourceId);
  ElMessageBox.confirm("是否删除该来源").then(() => {
    visible.value = false;
    sourceObj.list.value.splice(index, 1);
    $emit("update");
    $emit("select-item", {
      type: sourceObj.type.value,
      index,
    });
  });
}
function edit(item) {
  editIndex = sourceObj.list.value.findIndex((i) => i.sourceId === item.sourceId);
  newScurce.value = sourceObj.list.value[editIndex].sourceName;
  typeAction.value = "eidt";
  visible.value = true;
}
function changeType(item, title, type) {
  let index = sourceObj.list.value.findIndex((i) => i.sourceId === item.sourceId);
  ElMessageBox.confirm(title).then(() => {
    sourceObj.list.value[index].disable = type === "stop";
    const childerList = sourceObj.list.value[index].children;
    if (Array.isArray(childerList) && childerList.length > 0) {
      sourceObj.list.value[index].children = sourceObj.list.value[index].children.map((item) => {
        item.disable = type === "stop";
        if (Array.isArray(item.children) && item.children.length > 0) {
          item.children = item.children.map((item) => {
            item.disable = type === "stop";
            return item;
          });
        }
        return item;
      });
    }
    $emit("update");
    visible.value = false;
  });
}
</script>

<style lang="scss" scoped>
.sourceItem {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  // overflow-y: auto;
  // height: 100%;
}

.selectActive {
  background: #f5f6f8;
  color: #006eff;
}

.title {
  padding: 10px 15px;
}

.header {
  text-align: center;
  height: 50px;
  line-height: 50px;
  background: rgb(236, 244, 255);
  width: 100%;
}

.addButton {
  margin-top: 30px;
}

.context-box {
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  flex-wrap: wrap;
  cursor: pointer;
  flex-shrink: 0;

  .left {
    font-size: 14px;
    padding-left: 15px;
  }

  .right {
    display: flex;
    color: #006eff;
    font-size: 14px;
    width: 130px;

    .item {
      margin-right: 10px;
    }
  }
}
</style>
