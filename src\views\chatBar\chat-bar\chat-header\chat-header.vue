<template>
  <div class="flex items-center px-15px py-12px">
    <div class="flex-shrink-0 text-14px">当前团队：</div>
    <div class="w-0 flex-grow flex items-center">
      <div class="truncate text-14px">{{ currentTeam.name }}</div>
      <div v-if="teams.length > 1" color-primary class="flex-shrink-0 ml-10px cursor-pointer text-14px" @click="visible = true">切换</div>
    </div>
    <div v-if="customerList.length" class="flex-shrink-0 ml-20px cursor-pointer transform translate-y-2px text-center" @click="show()">
      <el-icon>
        <Plus />
      </el-icon>
      <div class="text-12px">新增</div>
    </div>
    <div class="flex-shrink-0 cursor-pointer ml-15px transform translate-y-2px text-center" :class="refreshing ? 'text-blue-500' : ''" @click="reload()">
      <el-icon :class="refreshing ? 'animate-spin' : ''">
        <Refresh />
      </el-icon>
      <div class="text-12px">刷新</div>
    </div>
  </div>
  <div v-if="customerList.length" class="flex">
    <div ref="customerListRef" class="w-0 flex-grow px-15px">
      <el-scrollbar ref="scrollRef" always>
        <div class="flex flex-nowrap pb-10px">
          <div v-for="(i, idx) in customerList" :ref="(el) => (customerRef[idx] = el)" :key="`${i._id}_${idx}`" class="relative h-75px p-10px rounded-8px border flex-grow" :class="[idx > 0 ? 'ml-15px' : '', customer && customer._id === i._id ? 'text-white bg-blue-500 border-blue-500 current-customer' : 'border-blue-200']" @click="toggle(i)">
            <div class="flex items-center justify-between items-center min-h-1 whitespace-nowrap" :style="{ minWidth: `${customerCardWidth}px` }">
              <div class="flex-grow whitespace-nowrap text-16px font-semibold">
                <span @click.stop="editName(i)">{{ i.name }}</span>
                <el-icon class="transform translate-y-1px" size="14" @click.stop="editName(i)">
                  <EditPen />
                </el-icon>
              </div>
              <div v-if="i.relationship" class="flex-shrink-0 ml-10px text-12px border py-4px px-6px rounded border">
                {{ i.relationship }}
              </div>
            </div>
            <div class="flex justify-between">
              <div class="mt-10px text-12px">{{ i.sex || "" }} {{ i.sex && i.ageStr ? " | " : "" }} {{ i.ageStr }}</div>
              <div class="mt-20px text-12px" v-if="i.isConnectHis && corpInfo.isConnectHis">已绑定HIS</div>
            </div>
          </div>
          <div v-if="customerList.length > 1" class="flex-shrink-0" :style="{ width: `${width / 2}px`, height: '1px' }"></div>
        </div>
      </el-scrollbar>
    </div>
    <!-- <div class="flex-shrink-0 flex flex-col items-center justify-center text-blue-500 border-gray-200 h-75px px-10px"
      color-primary
      :class="customerList.length === 0 ?'mx-15px flex-grow rounded':'ml-15px rounded-bl-8px rounded-tl-8px'"
      @click="show()">
      <el-icon class="text-16px">
        <CirclePlus />
      </el-icon>
      <div class="mt-10px text-12px">新増</div>
    </div> -->
  </div>
  <action-sheet :item-list="itemList" :visible="visible" @select="selectTeam($event)" @close="visible = false" />
  <el-dialog v-model="modalVisible" :width="modalWidth" title="请选择建档方式" @close="close">
    <el-scrollbar style="max-height: 50vh">
      <div color-normal class="p-15px text-14px">
        <div class="font-semibold mb-10px">1、患者自己建</div>
        <!-- <div class="mb-10px leading-20px text-gray-500">点击下方按钮，发送建档链接给患者，患者自己完成档案创建。</div> -->
        <!-- <el-button class="mb-15px w-1/2 mx-auto" type="primary" @click="sendLink()">发送建档链接</el-button> -->
        <div class="mb-15px w-3/4 py-10px px-15px text-center cursor-pointer text-16px text-white bg-[#006eff] rounded" @click="sendLink()">发送建档链接</div>
        <div class="font-semibold mb-10px">2、我帮患者</div>
        <!-- <div class="mb-10px leading-20px text-gray-500">点击下方“新增档案”按钮，帮患者完成档案创建。</div> -->
        <!-- <el-button class="w-1/2 mx-auto" type="primary" @click="addCustomer()">
          <div>新増档案</div><br />
          <div class="text-12px">未在院内系统建档过的患者</div>
        </el-button> -->
        <div class="mb-15px py-10px px-15px w-3/4 cursor-pointer text-14px text-white bg-[#006eff] rounded" @click="addCustomer()">
          <div class="text-16px text-center">新患者建档</div>
          <div v-if="corpInfo.isConnectHis" class="text-13px mt-10px text-center">未在院内系统建档过的患者</div>
        </div>
        <div v-if="corpInfo.isConnectHis" class="py-10px px-15px w-3/4 cursor-pointer text-14px text-white bg-[#006eff] rounded" @click="bindCustomer('create')">
          <div class="text-16px text-center">老患者建档</div>
          <div class="text-13px mt-10px text-center">已在院内系统建档过的患者</div>
        </div>
        <!-- <div class="py-10px px-15px w-3/4 cursor-pointer text-14px text-white bg-[#006eff] rounded" @click="bindCustomer('corelation')">
          <div class="text-16px text-center">老患者关联</div>
        </div> -->
      </div>
    </el-scrollbar>
  </el-dialog>
  <edit-basic-info :visible="editVisible" :customer="currentCustomer" @refresh="refresh" @close="closeEidtModal" />
  <!-- <el-dialog v-model="nameModalVisible" :width="modalWidth" title="修改患者姓名" @close="closeNameModal">
    <div color-normal class="p-15px text-14px">
      <el-input v-model="current.name" :placeholder="placeholder" />
    </div>
    <template #footer>
      <div class="text-center">
        <el-button plain class="w-100px" @click="closeNameModal()">取消</el-button>
        <el-button type="primary" class="w-100px" @click="confirm()">确定</el-button>
      </div>
    </template>
</el-dialog> -->
</template>
<script setup>
import { computed, nextTick, ref, watch } from "vue";
import { useElementBounding } from "@vueuse/core";
import { storeToRefs } from "pinia";
import { memberStore } from "@/store/member";
import { ElMessage } from "element-plus";
import { teamStore as useTeamStore } from "@/store/team";
import useModal from "@/hooks/useModal";
import useCustomerApi from "../useCustomerApi";
import ActionSheet from "@/components/weui/action-sheet.vue";
import editBasicInfo from "./edit-basic-info.vue";

const emits = defineEmits(["addCustomer", "change", "sendLink", "changeName", "bindCustomer"]);
const props = defineProps({
  customer: { type: Object, default: () => ({}) },
  customerList: { type: Array, default: () => [] },
  refresh: { type: Function, default: () => {} },
  verifyInTeam: { type: Function, default: () => {} },
});
const teamStore = useTeamStore();
let { corpInfo, isHangzheng } = storeToRefs(memberStore());
const { getTeams } = teamStore;
const { teams, currentTeam } = storeToRefs(teamStore);
const visible = ref(false);
const { close, show, visible: modalVisible, width: modalWidth } = useModal(560);
const itemList = computed(() => teams.value.map((i) => ({ label: i.name, value: i._id, team: i })));
const customerListRef = ref(null);

const { width } = useElementBounding(customerListRef);
const customerCardWidth = computed(() => width.value - 120);
function toggle(i) {
  if (props.customer._id !== i._id) {
    emits("change", i._id);
  }
}

function sendLink() {
  emits("sendLink");
  close();
}

function addCustomer() {
  emits("addCustomer", { ...currentTeam.value });
  close();
}

function bindCustomer(type) {
  emits("bindCustomer", type);
  close();
}

function selectTeam({ team }) {
  currentTeam.value = team;
  visible.value = false;
}

const refreshing = ref(false);
async function reload() {
  if (refreshing.value) return;
  refreshing.value = true;
  const start = Date.now();
  // await getTeams();
  await Promise.all([getTeams(), props.refresh()]);
  if (Date.now() - start < 1500) await new Promise((resolve) => setTimeout(resolve, 750));
  refreshing.value = false;
}

const { close: closeEidtModal, show: showEditModal, visible: editVisible } = useModal(560);
const { update } = useCustomerApi();

const currentCustomer = ref({});
function editName(i) {
  if (props.verifyInTeam(i)) {
    currentCustomer.value = JSON.parse(JSON.stringify(i));
    showEditModal();
  } else {
    ElMessage.info({ message: "该用户未授权当前团队", duration: 1500 });
  }
}
async function confirm() {
  if (current.value.name.trim()) {
    const { success, message } = await update(current.value._id, { name: current.value.name });
    if (success) {
      ElMessage.success("修改成功");
      emits("changeName", { _id: current.value._id, name: current.value.name });
      closeNameModal();
    } else {
      ElMessage.error(message);
    }
  } else {
    ElMessage.info("请输入患者名称");
  }
}

const scrollRef = ref();
const customerRef = ref([]);
watch(
  () => props.customer,
  async (n, o) => {
    if (n && n._id && (!o || n._id !== o._id)) {
      await nextTick();
      const index = props.customerList.findIndex((i) => i._id === n._id);
      if (index >= 0) {
        const left = customerRef.value[index].offsetLeft;
        scrollRef.value.setScrollLeft(left);
      }
    }
  },
  { immediate: true }
);

defineExpose({
  toAddCustomer: show,
});
</script>
<style lang="scss" scoped>
.current-customer::after {
  bottom: -1px;
  left: calc(50% - 10px);
  border: 10px solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-bottom-color: white;
}
</style>
