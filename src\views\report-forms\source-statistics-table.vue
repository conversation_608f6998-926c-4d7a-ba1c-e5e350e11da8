<template>
  <div class="report-container">
    <div class="report-header">
      <div class="report-actions">
        <div class="filter-section">
          <div class="filter-row">
            <div class="filter-item">
              <span class="filter-label">咨询日期：</span>
              <el-date-picker v-model="consultDateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
                end-placeholder="结束日期" :shortcuts="dateShortcuts" :disabled-date="disabledDate"
                @change="handleDateChange" class="date-range" value-format="YYYY-MM-DD" />
            </div>
            <div class="filter-item" v-if="isManager">
              <check-box-filter v-model="developerUserIds" label="开发人员" :list="developerList" @clear="clearDevelopers">
                <template #item="{ item }">
                  <ww-user :openid="item.value"></ww-user>
                </template>
              </check-box-filter>
            </div>
            <div class="filter-item">
              <filter-info-source v-model="infoSource" />
            </div>
            <div class="filter-item">
              <span class="filter-label">咨询科室：</span>
              <dept-select v-model="deptId" placeholder="请选择科室" @change="handleDeptChange" class="dept-select" />
            </div>
            <div class="filter-item">
              <span class="filter-label">咨询项目：</span>
              <project-intent-select v-model="projectIntentIds" :deptId="deptId" placeholder="请选择或搜索咨询项目"
                @change="handleProjectChange" />
            </div>
            <div class="filter-actions">
              <el-button type="primary" @click="handleSearch" :loading="loading" :disabled="loading">查询</el-button>
              <el-button type="success" @click="exportToExcel" :disabled="loading">导出Excel</el-button>
              <el-button @click="resetFilters" :disabled="loading">重置</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="table-container">
      <table class="performance-table">
        <colgroup>
          <col style="width: 150px" />
          <!-- 来源 -->
          <col style="width: 120px" />
          <!-- 咨询人次 -->
          <col style="width: 100px" />
          <!-- 咨询占比 -->
          <col style="width: 120px" />
          <!-- 到诊人次 -->
          <col style="width: 120px" />
          <!-- 到诊占比 -->
          <col style="width: 120px" />
          <!-- 成交人次 -->
          <col style="width: 120px" />
          <!-- 未上门人次 -->
          <col style="width: 120px" />
          <!-- 未上门占比 -->
        </colgroup>
        <thead>
          <tr>
            <th>来源</th>
            <th>咨询人次</th>
            <th>咨询占比</th>
            <th>到诊人次</th>
            <th>到诊占比</th>
            <th>成交人次</th>
            <th>未上门人次</th>
            <th>未上门占比</th>
          </tr>
        </thead>
        <tbody>
          <template v-for="(sourceGroup, groupIndex) in sourceGroupedData" :key="groupIndex">
            <!-- 一级来源行 -->
            <tr v-for="(item, sIndex) in sourceGroup.sources" :key="`${item.sourceKey}-${sIndex}`">
              <td>{{ item.sourceName || "未知来源" }}</td>
              <td>{{ item.consultCount }}</td>
              <td>{{ formatPercentage(item.consultCount / totalConsultCount) }} %</td>
              <td>{{ item.visitCount }}</td>
              <td>{{ formatPercentage(item.visitRate) }} %</td>
              <td>{{ item.dealCount }}</td>
              <td>{{ item.consultCount - item.visitCount }}</td>
              <td>{{ formatPercentage(item.consultCount ? (item.consultCount - item.visitCount) / item.consultCount : 0)
                }} %</td>
            </tr>
          </template>
          <tr class="total-row">
            <td class="total-label">总计：</td>
            <td>{{ totalConsultCount }}</td>
            <td></td>
            <td>{{ totalVisitCount }}</td>
            <td></td>
            <td>{{ totalDealCount }}</td>
            <td>{{ totalConsultCount - totalVisitCount }}</td>
            <td></td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="footer-info">
      <!-- <span>共 {{ filteredTableData.length }} 条记录，{{ Object.keys(sourceGroupsMap).length }} 种来源</span> -->
      <span class="date-limit-info">* 查询限制为一个月内的数据</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { ElButton, ElMessage, ElDatePicker, ElSelect, ElOption } from "element-plus";
import { CheckBoxFilter, filterInfoSource } from "@/components/filter-bar";
import { storeToRefs } from "pinia";
import { staffStore } from "@/store/staff";
import { memberStore } from "@/store/member";
import { teamStore } from "@/store/team";
import ProjectIntentSelect from "@/components/benefit-management/project-intent-select.vue";
import { getEConsultProjectStatistics } from "@/api/bill-record";
import { getProjectIntentNames } from "@/api/benefitManagement";
// 导入科室相关组件和store
import DeptSelect from "@/components/dept-components/dept-select.vue";
import { useDeptStore } from "@/components/dept-components/dept-store";

// 用户筛选
const projectIntentIds = ref([]);
const selectedProjects = ref([]);
const consultDateRange = ref([]);
const infoSource = ref([]); // 添加信息来源筛选变量

// 添加科室筛选变量
const deptId = ref("");
const selectedDept = ref({});
const deptStore = useDeptStore();

// 从store获取员工列表
const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());
const { isAdmin } = storeToRefs(memberStore());
const { managerList } = storeToRefs(teamStore());
const { judgmentIsAdmin } = memberStore();

// 添加管理员判断逻辑
const isManager = computed(() => {
  return managerList.value.includes(90008) || isAdmin.value;
});

// 添加开发人员列表计算属性，只显示开发人员角色
const developerList = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("onlineCustomerService")).map((i) => ({ value: i.userid }));
});

const selectedDevelopers = ref([]);
const hasExplicitlyClearedDevelopers = ref(false);

const developerUserIds = computed({
  get() {
    if (selectedDevelopers.value.length > 0) {
      return selectedDevelopers.value;
    }
    if (hasExplicitlyClearedDevelopers.value) {
      return [];
    }
    const currentUserId = localStorage.getItem("userId");
    if (!isManager.value) {
      // 不是主管，只能看到自己
      return [currentUserId];
    } else {
      // 主管
      const inList = developerList.value.some((i) => i.value === currentUserId);
      if (inList) {
        return [currentUserId];
      } else {
        // 展示全部
        return [];
      }
    }
  },
  set(newVal) {
    selectedDevelopers.value = newVal;
    hasExplicitlyClearedDevelopers.value = false;
  },
});

function clearDevelopers() {
  selectedDevelopers.value = [];
  hasExplicitlyClearedDevelopers.value = true;
}
// 项目和科室选择变更处理
const handleProjectChange = (projects) => {
  selectedProjects.value = projects;
};

// 添加科室变化处理函数
function handleDeptChange(dept) {
  selectedDept.value = dept;
  projectIntentIds.value = []; // 清空项目选择
}

// 日期快捷选项
const dateShortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近半个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
];

// 禁用超过一个月范围的日期
const disabledDate = (time) => {
  if (!consultDateRange.value || consultDateRange.value.length === 0) {
    return false;
  }

  const startDate = new Date(consultDateRange.value[0]);
  const endDate = new Date(consultDateRange.value[1]);
  const currentDate = new Date(time);

  // 如果已选择了开始日期，限制结束日期不能超过开始日期后的31天
  if (consultDateRange.value.length === 1) {
    const oneMonthLater = new Date(startDate);
    oneMonthLater.setDate(startDate.getDate() + 31);
    return currentDate > oneMonthLater;
  }

  return false;
};

// 监听日期变化，确保不超过一个月
watch(consultDateRange, (newValue) => {
  if (newValue && newValue.length === 2) {
    const start = new Date(newValue[0]);
    const end = new Date(newValue[1]);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays > 31) {
      // 如果超过31天，将结束日期设置为开始日期后的31天
      const maxEnd = new Date(start);
      maxEnd.setDate(start.getDate() + 31);
      const formattedMaxEnd = formatDate(maxEnd);

      consultDateRange.value = [newValue[0], formattedMaxEnd];
      ElMessage.warning("查询范围已限制为一个月内");
    }
  }
});

// 格式化日期为YYYY-MM-DD
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  return `${year}-${month}-${day}`;
};

// 原始数据和筛选后的表格数据
const originalData = ref([]);
const tableData = ref([]);

async function fetchProjectStatistics() {
  try {
    // 构建查询参数 - 包含所有筛选条件
    const today = new Date();
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(today.getMonth() - 1);

    const params = {
      startDate: consultDateRange.value?.[0] || formatDate(oneMonthAgo),
      endDate: consultDateRange.value?.[1] || formatDate(today),
    };

    // 添加所有筛选条件到API参数中
    // 开发人员筛选 - 根据管理员权限处理
    if (isManager.value) {
      // 管理员可以筛选多个开发人员
      if (developerUserIds.value && developerUserIds.value.length > 0) {
        params.developerUserIds = developerUserIds.value;
      }
    } else {
      // 非管理员只能查看自己的数据
      params.developerUserIds = [localStorage.getItem("userId")];
    }

    // 信息来源筛选
    if (infoSource.value && infoSource.value.length > 0) {
      params.infoSource = infoSource.value;
    }

    if (deptId.value) {
      params.deptId = deptId.value; // 添加科室ID筛选条件
      params.projectIds = (await deptStore.fetchProjectIdsByDeptId(deptId.value)) || [];
    }

    // 项目筛选
    if (projectIntentIds.value && projectIntentIds.value.length > 0) {
      params.projectIds = projectIntentIds.value;
    }

    // 调用API获取数据
    const { data, success, message } = await getEConsultProjectStatistics(params);

    if (success) {
      // 保存原始数据
      originalData.value = data.data;

      // 处理数据：按来源分组
      const sourceGroups = {};

      data.data.forEach((record) => {
        // 获取来源信息，有可能是数组或字符串
        let sources = [];
        if (Array.isArray(record.source)) {
          sources = record.source;
        } else if (record.source) {
          sources = [record.source];
        } else {
          sources = ["未知来源"];
        }

        // 对每个来源创建记录
        sources.forEach((source) => {
          const sourceKey = source || "unknown";

          if (!sourceGroups[sourceKey]) {
            sourceGroups[sourceKey] = {
              sourceKey,
              sourceName: source || "未知来源",
              consultCount: 0,
              visitCount: 0,
              dealCount: 0,
              records: [],
              source: sourceKey,
              projectIds: [],
              deptIds: [],
            };
          }

          // 保存每个来源对应的原始记录
          sourceGroups[sourceKey].records.push(record);

          // 保存项目ID信息
          if (record.projectId && !sourceGroups[sourceKey].projectIds.includes(record.projectId)) {
            sourceGroups[sourceKey].projectIds.push(record.projectId);
          }

          // 保存projectIds数组中的项目ID
          if (Array.isArray(record.projectIds)) {
            record.projectIds.forEach((pid) => {
              if (!sourceGroups[sourceKey].projectIds.includes(pid)) {
                sourceGroups[sourceKey].projectIds.push(pid);
              }
            });
          }

          // 保存科室ID信息
          if (record.deptId && !sourceGroups[sourceKey].deptIds.includes(record.deptId)) {
            sourceGroups[sourceKey].deptIds.push(record.deptId);
          }

          // 累加统计数据
          sourceGroups[sourceKey].consultCount += 1; // 每条记录算一次咨询
          sourceGroups[sourceKey].visitCount += record.inHospitalTimes && record.inHospitalTimes.length > 0 ? 1 : 0; // 到诊人次
          sourceGroups[sourceKey].dealCount += (record.consumeRecordCount || 0) > 0 ? 1 : 0; // 成交人次
        });
      });

      // 转换为数组并计算比率
      tableData.value = Object.values(sourceGroups).map((item) => {
        // 计算到诊占比和成交占比
        const visitRate = item.consultCount > 0 ? item.visitCount / item.consultCount : 0;
        const dealRate = item.consultCount > 0 ? item.dealCount / item.consultCount : 0;

        return {
          ...item,
          visitRate,
          dealRate,
        };
      });

      console.log("获取来源统计数据成功，共 " + tableData.value.length + " 条记录");
    } else {
      ElMessage.error(message || "获取统计数据失败");
      tableData.value = [];
    }
  } catch (error) {
    ElMessage.error("获取统计数据出错：" + (error.message || error));
    tableData.value = [];
    console.error("获取统计数据出错:", error);
  }
}

// 简化后的筛选表格数据 - 直接使用API返回的结果
const filteredTableData = computed(() => {
  return tableData.value;
});

// 按来源分组的数据
const sourceGroupsMap = computed(() => {
  // 创建来源分组
  const sourceMap = {};

  filteredTableData.value.forEach((item) => {
    // 获取来源类型
    // 这里假设有一级来源、二级来源的区分逻辑
    // 简单实现，之后可根据实际数据结构调整
    const sourceType = item.sourceKey.includes("-") ? item.sourceKey.split("-")[0] : item.sourceKey;

    if (!sourceMap[sourceType]) {
      sourceMap[sourceType] = {
        sourceType,
        sources: [],
        totalConsultCount: 0,
        totalVisitCount: 0,
        totalDealCount: 0,
      };
    }

    // 添加来源数据
    sourceMap[sourceType].sources.push(item);

    // 更新来源组汇总数据
    sourceMap[sourceType].totalConsultCount += item.consultCount || 0;
    sourceMap[sourceType].totalVisitCount += item.visitCount || 0;
    sourceMap[sourceType].totalDealCount += item.dealCount || 0;
  });

  // 计算比率
  Object.values(sourceMap).forEach((group) => {
    group.totalVisitRate = group.totalConsultCount > 0 ? group.totalVisitCount / group.totalConsultCount : 0;
    group.totalDealRate = group.totalConsultCount > 0 ? group.totalDealCount / group.totalConsultCount : 0;
  });

  return sourceMap;
});

// 列表形式的来源分组数据
const sourceGroupedData = computed(() => {
  return Object.values(sourceGroupsMap.value);
});

// 计算总计数据 - 基于筛选后的数据
const totalConsultCount = computed(() => {
  return filteredTableData.value.reduce((sum, item) => sum + (item.consultCount || 0), 0);
});

const totalVisitCount = computed(() => {
  return filteredTableData.value.reduce((sum, item) => sum + (item.visitCount || 0), 0);
});

const totalVisitRate = computed(() => {
  return totalConsultCount.value > 0 ? totalVisitCount.value / totalConsultCount.value : 0;
});

const totalDealCount = computed(() => {
  return filteredTableData.value.reduce((sum, item) => sum + (item.dealCount || 0), 0);
});

const totalDealRate = computed(() => {
  return totalConsultCount.value > 0 ? totalDealCount.value / totalConsultCount.value : 0;
});

// 格式化百分比
const formatPercentage = (value) => {
  if (value === undefined || value === null || isNaN(value)) {
    return "0.00%";
  }
  return `${(value * 100).toFixed(2)}%`;
};

// 处理日期变化
const handleDateChange = () => {
  // 日期改变时，重新调用接口获取数据
  // 因为日期是主要的范围筛选条件，需要重新从服务器获取
  if (consultDateRange.value && consultDateRange.value.length === 2) {
    fetchProjectStatistics();
  }
};

// 重置筛选条件
const resetFilters = () => {
  // 设置默认日期范围为当月
  const today = new Date();
  const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
  const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

  consultDateRange.value = [formatDate(firstDayOfMonth), formatDate(lastDayOfMonth)];
  projectIntentIds.value = [];
  selectedProjects.value = [];
  infoSource.value = []; // 重置信息来源筛选
  deptId.value = ""; // 重置科室筛选
  selectedDept.value = {};

  selectedDevelopers.value = []; // 重置开发人员筛选
  // 重置后重新获取数据
  fetchProjectStatistics();
};

// 处理查询 - 直接调用API
const handleSearch = () => {
  if (consultDateRange.value && consultDateRange.value.length === 2) {
    const start = new Date(consultDateRange.value[0]);
    const end = new Date(consultDateRange.value[1]);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays > 31) {
      ElMessage.warning("查询范围已限制为一个月内");
      return;
    }

    // 获取数据
    fetchProjectStatistics();
  } else {
    ElMessage.warning("请选择咨询日期范围");
  }
};

// 导出Excel
const exportToExcel = () => {
  // 准备导出数据
  const exportData = [];

  // 添加标题信息
  exportData.push(["来源统计表"]);

  // 添加筛选条件信息
  if (consultDateRange.value && consultDateRange.value.length === 2) {
    exportData.push(["咨询日期范围", `${consultDateRange.value[0]} 至 ${consultDateRange.value[1]}`]);
  }

  if (developerUserIds.value.length > 0) {
    const developerNames = developerUserIds.value
      .map((id) => {
        const staff = staffList.value.find((s) => s.userid === id);
        return staff ? staff.anotherName || id : id;
      })
      .join(", ");
    exportData.push(["开发人员", developerNames]);
  }

  if (projectIntentIds.value.length > 0) {
    const projectsText = selectedProjects.value.map((p) => p.projectName || p.id).join(", ");
    exportData.push(["咨询项目", projectsText]);
  }

  if (infoSource.value.length > 0) {
    const infoSourceText = infoSource.value.join(", ");
    exportData.push(["信息来源", infoSourceText]);
  }

  // 添加科室信息
  if (deptId.value && selectedDept.value) {
    exportData.push(["咨询科室", selectedDept.value.deptName || deptId.value]);
  }

  exportData.push([]); // 空行

  // 添加表头
  exportData.push(["来源", "咨询人次", "咨询占比", "到诊人次", "到诊占比", "成交人次", "未上门人次", "未上门占比"]);

  // 添加按来源分组的数据
  sourceGroupedData.value.forEach((sourceGroup) => {
    // 添加每个来源的数据
    sourceGroup.sources.forEach((item) => {
      exportData.push([item.sourceName || "未知来源", item.consultCount, `${((item.consultCount / totalConsultCount) * 100).toFixed(2)}%`, item.visitCount, `${(item.visitRate * 100).toFixed(2)}%`, item.dealCount, item.consultCount - item.visitCount, `${(((item.consultCount - item.visitCount) / (item.consultCount || 1)) * 100).toFixed(2)}%`]);
    });

    // 添加小计行
    if (sourceGroup.sources.length > 1) {
      exportData.push(["小计", sourceGroup.totalConsultCount, `${((sourceGroup.totalConsultCount / totalConsultCount) * 100).toFixed(2)}%`, sourceGroup.totalVisitCount, `${(sourceGroup.totalVisitRate * 100).toFixed(2)}%`, sourceGroup.totalDealCount, sourceGroup.totalConsultCount - sourceGroup.totalVisitCount, `${(((sourceGroup.totalConsultCount - sourceGroup.totalVisitCount) / (sourceGroup.totalConsultCount || 1)) * 100).toFixed(2)}%`]);
    }
  });

  // 添加总计行
  exportData.push(["总计", totalConsultCount.value, "100.00%", totalVisitCount.value, `${(totalVisitRate.value * 100).toFixed(2)}%`, totalDealCount.value, totalConsultCount.value - totalVisitCount.value, `${(((totalConsultCount.value - totalVisitCount.value) / (totalConsultCount.value || 1)) * 100).toFixed(2)}%`]);

  // 创建工作簿和工作表
  const wb = XLSX.utils.book_new();
  const ws = XLSX.utils.aoa_to_sheet(exportData);

  // 设置列宽
  const colWidths = [
    { wch: 18 }, // 来源
    { wch: 10 }, // 咨询人次
    { wch: 10 }, // 咨询占比
    { wch: 10 }, // 到诊人次
    { wch: 10 }, // 到诊占比
    { wch: 10 }, // 成交人次
    { wch: 12 }, // 未上门人次
    { wch: 10 }, // 未上门占比
  ];

  ws["!cols"] = colWidths;

  // 合并标题单元格
  ws["!merges"] = [
    { s: { r: 0, c: 0 }, e: { r: 0, c: 7 } }, // 标题行
  ];

  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(wb, ws, "电网咨询来源表");

  // 生成Excel文件并下载
  const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
  const blob = new Blob([excelBuffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });

  // 使用当前日期作为文件名的一部分
  const now = new Date();
  const fileName = `电网咨询来源表_${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}.xlsx`;

  saveAs(blob, fileName);

  ElMessage.success("导出Excel成功");
};

onMounted(async () => {
  // 确保科室列表已加载
  await deptStore.fetchDeptList();
  await judgmentIsAdmin();

  // 加载员工列表
  if (staffList.value.length === 0) await getStaffList();

  // 设置默认日期范围为当月
  const end = new Date();
  const start = new Date(end.getFullYear(), end.getMonth(), 1);
  consultDateRange.value = [formatDate(start), formatDate(end)];
  // 初始加载数据
  fetchProjectStatistics();
});

// 监听所有筛选条件变化
watch([developerUserIds, projectIntentIds, infoSource, deptId], () => {
  if (consultDateRange.value && consultDateRange.value.length === 2) {
    fetchProjectStatistics();
  }
});
</script>

<style scoped>
.report-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
  background-color: #f5f7fa;
  overflow: hidden;
  min-width: 1200px;
}

.report-header {
  background-color: white;
  padding: 15px 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
}

.filter-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
  margin-bottom: 10px;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-label {
  white-space: nowrap;
  margin-right: 5px;
  font-size: 14px;
  color: #606266;
}

.filter-actions {
  margin-left: auto;
  display: flex;
  gap: 10px;
}

.date-range {
  min-width: 320px;
}

.dept-select {
  min-width: 180px;
}

:deep(.el-date-editor.el-input__wrapper),
:deep(.el-select) {
  width: 100%;
  flex: 1;
}

.table-container {
  flex: 1;
  margin: 15px 0 0 0;
  background-color: white;
  overflow: auto;
  position: relative;
  border-top: 1px solid #ebeef5;
  border-bottom: 1px solid #ebeef5;
  max-height: 70vh;
}

.performance-table {
  width: 100%;
  min-width: 970px;
  border-collapse: collapse;
  font-size: 14px;
  table-layout: fixed;
}

.performance-table th,
.performance-table td {
  border: 1px solid #ebeef5;
  padding: 12px 8px;
  text-align: center;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.performance-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 10;
}

.total-row {
  background-color: #ebeef5;
  font-weight: bold;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.subtotal-row {
  background-color: #f8f8f8;
  font-weight: bold;
}

.total-label {
  text-align: right;
  padding-right: 10px;
}

.footer-info {
  background-color: white;
  padding: 10px 20px;
  margin: 0;
  display: flex;
  justify-content: end;
  color: #606266;
  font-size: 14px;
  flex-shrink: 0;
  border-top: 1px solid #ebeef5;
}

.date-limit-info {
  color: #e6a23c;
  font-style: italic;
}

@media print {
  .report-container {
    background-color: white;
    height: auto;
  }

  .report-header {
    box-shadow: none;
  }

  .report-actions,
  .footer-info {
    display: none;
  }

  .table-container {
    margin: 0;
    border: none;
    overflow: visible;
    height: auto;
  }

  .performance-table th,
  .performance-table td {
    font-size: 12px;
    padding: 8px 4px;
  }
}

@media (max-width: 768px) {
  .filter-item {
    max-width: 100%;
  }

  .report-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .filters-row {
    flex-direction: column;
  }

  .filter-item {
    width: 100%;
  }

  .filter-controls {
    margin-top: 10px;
    width: 100%;
    justify-content: space-between;
  }
}

@media (min-width: 1400px) {
  .performance-table .amount {
    min-width: 120px;
    max-width: 150px;
  }
}
</style>