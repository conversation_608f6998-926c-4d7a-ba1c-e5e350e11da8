<template>
  <el-tabs v-model="activeName" class="h-full">
    <el-tab-pane class="h-full" label="服务患者" name="serviceCustomer">
      <template #label>
        <span class="px-15px">服务患者</span>
      </template>
      <service-customer-table />
    </el-tab-pane>
    <el-tab-pane class="h-full" lazy label="服务明细" name="serviceDetail">
      <template #label>
        <span class="px-15px">服务明细</span>
      </template>
      <service-detail-table :initValue="initValue"/>
    </el-tab-pane>
  </el-tabs>
</template>
<script setup>
import { ref } from 'vue';

import serviceDetailTable from './service-detail-table.vue';
import serviceCustomerTable from './service-customer-table.vue';

const activeName = ref('serviceDetail');
defineProps({
  initValue: { type: Array, default: () => [] }
})
</script>
<style lang="scss" scoped>
:deep(.el-tabs__content) {
  height: calc(100% - 40px);
}

:deep(.el-tabs__header) {
  background-color: white;
  margin-bottom: 0;
}
</style>
