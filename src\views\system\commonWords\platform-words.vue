<template>
  <el-dialog :model-value="visible" class="platform-words" title="平台常用语" :width="600" @close="close()">
    <div flex>
      <el-scrollbar flex-shrink-0 w-150 border-right :height="360">
        <div px-15 py-10 border-bottom>分类一</div>
      </el-scrollbar>
      <el-scrollbar flex-grow :height="360">
        <div flex px-15 py-10 border-bottom>
          <div flex-grow mr-10>常用语内容常用语内容常用语内容常用语内容常用语内容常用语内容</div>
          <el-text flex-shrink-0 type="primary" @click="quoto()">复制</el-text>
        </div>
      </el-scrollbar>
    </div>
  </el-dialog>
</template>
<script setup>
defineProps({
  visible: { type: Boolean, default: false }
})
const emits = defineEmits(['on-close', 'on-quoto'])
function quoto(txt = '') {
  emits('on-quoto', txt)
}
function close() {
  emits('on-close')
}
</script>
<style scoped lang="scss">
[w-150] {
  width: 150px;
}

[border-right] {
  border-right: 1px solid #eee;
}

[border-bottom] {
  border-bottom: 1px solid #eee;
}
</style>
<style scoped>
.platform-words .el-dialog__body {
  padding: 0 !important;
}
</style>