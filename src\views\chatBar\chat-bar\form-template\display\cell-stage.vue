<template>
  <div>
    {{ text }}
  </div>
</template>
<script setup>
import { computed, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import { dbStore } from "@/store/db.js";

const emits = defineEmits(['change']);
const props = defineProps({
  title: { default: '' },
  form: { type: Object, default: () => ({}) }
})
const store = dbStore();
const { stageList } = storeToRefs(store);
const { getStageList } = store;
const text = computed(() => {
  const value = props.title && props.form[props.title] ? props.form[props.title] : '';
  const stage = value ? stageList.value.find(i => i.label === value || i.value === value) : '';
  return stage ? stage.label : '';
})
onMounted(() => stageList.value.length === 0 && getStageList())

</script>
<style lang="scss" scoped></style>
