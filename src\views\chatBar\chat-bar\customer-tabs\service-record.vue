<template>
  <div v-loading="loading" class="text-14px">
    <div ref="headRef" class="pb-15px">
      <div class="flex items-center justify-between px-15px py-12px bg-white shadow-xl">
        <div class="flex items-center">
          <div class="mr-4px w-3px h-12px bg-blue-500 rounded-4px"></div>
          <div class="leading-24px">服务记录时间</div>
        </div>
        <div>
          <el-select v-model="eventType" placeholder="请选择服务类型" @change="change($event)">
            <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
      </div>
    </div>
    <el-lock-scrollbar :lock="lock" :max-height="maxScrollHeight" @scroll="handleScroll">
      <div v-if="records.length === 0" class="pt-30px">
        <empty-data class="mt-20px" :top="0" text="暂无服务记录"></empty-data>
      </div>
      <div v-for="(record, idx) in records" :key="record._id"
        :class="idx === records.length - 1 ? 'visit-node--last' : ''"
        class="relative visit-node pl-30px pb-15px mr-15px">
        <div class="bg-white shadow-md rounded-8px" @click="toggle(record)">
          <div class="pt-10px px-15px">{{ record.timeStr }}</div>
          <div class="px-15px break-all mt-10px text-14px leading-4">
            {{ record.typeStr }} {{ record.executorUserId ? '/' :''}} <ww-user v-if="record.executorUserId"
              :openid="record.executorUserId" /> {{record.fileName ? '/':''}} <span class="text-blue-500 font-semibold">
              {{record.fileName }}
            </span>
          </div>
          <div class="flex px-15px py-10px leading-21px">
            <div class="w-0 flex-grow" :class="expand[record._id] ? 'line-clamp-3 overflow-hidden' : ''">
              <content-userid-transform :content="record.taskContent"></content-userid-transform>
            </div>
            <div class="pl-10px" @click.stop="view(record)">
              <el-icon color="#006eff" class="pointer" size="16">
                <EditPen />
              </el-icon>
            </div>
          </div>
        </div>
      </div>
      <div v-if="more"
        class="pb-15px rounded-4px cursor-pointer flex items-center justify-center text-gray-400 hover:text-blue-500"
        @click="loadmore()">
        <div class="text-14px">加载更多</div>
        <el-icon>
          <ArrowDownBold />
        </el-icon>
      </div>
    </el-lock-scrollbar>

    <service-modal :width="width" :customer="props.customer" :visible="visible" :record="currentRecord"
      @change="change()" @close="close" />
    <survery-dialog isMobile :data="survery" :visible="surveryVisible" @close="closeSurvery" />
    <el-dialog v-model="detailVisible" :width="width" :title="currentRecord ? currentRecord.typeStr : ''"
      @close="closeDetailModal">
      <div class="text-14px leading-22px px-10px py-12px">
        <div>
          <span class="text-gray-500">内容：</span>
          <span color-normal><content-userid-transform
              :content="currentRecord.taskContent"></content-userid-transform></span>
        </div>
        <div v-if="currentRecord.hasArticle || currentRecord.hasSurvery"
          class="flex items-center text-14px mt-5px leading-22px">
          <div class="flex-shrink-0 text-gray-500">{{ currentRecord.hasArticle ? "文章" : "问卷" }}：</div>
          <div class="mx-10px truncate cursor-pointer text-blue-500 hover:underline" @click="viewDetail(currentRecord)">
            {{ currentRecord.pannedEventSendFile.name }}
          </div>
          <!-- <el-text class="flex-shrink-0 cursor-pointer" type="primary">查看</el-text> -->
        </div>
        <div class="leading-24px mt-5px">
          <span>处理人：</span>
          <ww-user v-if="currentRecord.executorUserId" :openid="currentRecord.executorUserId"></ww-user>
        </div>
      </div>
      <template #footer>
        <div class="text-center">
          <!-- <el-button class="w-100px" type="danger" :loading="loading" plain @click="remove()">删除</el-button> -->
          <el-button class="w-100px" @click="closeDetailModal()">关闭</el-button>
          <el-button class="w-100px" :loading="loading" type="primary" @click="edit()">编辑</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
import { computed, inject, onMounted, onUnmounted, ref, watch } from "vue";

import { storeToRefs } from "pinia";
import { useElementBounding } from "@vueuse/core";
import { ElMessage, ElMessageBox } from "element-plus";
import dayjs from "dayjs";
import { getAnswer, getDetail } from "@/api/survery";
import { getServiceRecord, removeRecord } from "@/api/todo";
import { ServiceType } from "@/baseData";
import useModal from "@/hooks/useModal";
import { memberStore } from "@/store/member";
import { isURL } from "@/utils/common";
import useLockScrollbar from "./useLockScrollbar.js";
import contentUseridTransform from "@/components/content-userid-transform/index.vue";
import elLockScrollbar from "@/components/el-lock-scrollbar/el-lock-scrollbar";
import EmptyData from "@/components/empty-data.vue";
import serviceModal from "./service-modal";
import SurveryDialog from "@/views/system/survery/dialog.vue";

const trigger = inject("side-bar-event-trigger");

const props = defineProps({
  maxHeight: { type: Number },
  customer: { type: Object, default: () => ({}) },
});
const { memberInfo } = storeToRefs(memberStore());
const headRef = ref(null);
const { height } = useElementBounding(headRef);
const maxScrollHeight = computed(() => props.maxHeight - height.value);

const typeList = [{ label: "全部", value: "ALL" }, ...Object.keys(ServiceType).map((key) => ({ value: key, label: ServiceType[key] }))];

const loading = ref(false);
const records = ref([]);
const more = ref(false);
const page = ref(1);
const eventType = ref("ALL");
const expand = ref({})
function change() {
  page.value = 1;
  getList();
}
async function getList() {
  if (loading.value) return;
  if (memberInfo.value.corpId && props.customer._id) {
    loading.value = true;
    const params = { corpId: memberInfo.value.corpId, customerId: props.customer._id };
    if (eventType.value && eventType.value !== "ALL") {
      params.eventType = eventType.value;
    }
    const { data } = await getServiceRecord(page.value, 10, params);
    const list =
      data && Array.isArray(data.data)
        ? data.data.map((i) => {
          const hasSurvery = i.eventType === "questionnaire" && i.pannedEventSendFile && i.pannedEventSendFile.surveryId;
          const hasArticle = i.pannedEventSendFile && i.pannedEventSendFile.type === "article" && isURL(i.pannedEventSendFile.url);
          const fileName = i.pannedEventSendFile && i.pannedEventSendFile.name ? i.pannedEventSendFile.name : "";
          return {
            ...i,
            hasSurvery,
            fileName,
            hasArticle,
            timeStr: i.executionTime ? dayjs(i.executionTime).format("YYYY-MM-DD HH:mm") : "",
            typeStr: ServiceType[i.eventType] || "",
          };
        })
        : [];
    more.value = data.pages > page.value;
    records.value = page.value === 1 ? list : [...records.value, ...list];
    loading.value = false;
  }
}
function loadmore() {
  if (more.value && !loading.value) {
    page.value = page.value + 1;
    getList();
  }
}

function viewDetail(record) {
  if (record.hasArticle) {
    window.open(record.pannedEventSendFile.url);
  } else {
    showSurvery(record.pannedEventSendFile.surveryId, record.pannedEventSendFile.answerId);
  }
}

const { close: closeSurvery, visible: surveryVisible, show: diaplaySurvery } = useModal();
const survery = ref(null);
const surveryLoading = ref(false);
async function showSurvery(surveryId, answerId) {
  if (surveryLoading.value) return;
  if (answerId) {
    const { data } = await getAnswer(surveryId, props.customer._id, answerId);
    if (data && data.record && data.record.submitTime) {
      survery.value = data.record;
      diaplaySurvery();
      return;
    }
  }
  const { data, message } = await getDetail(memberInfo.value.corpId, surveryId);
  if (data && data.data) {
    survery.value = data.data;
    diaplaySurvery();
  } else {
    ElMessage.error(message || "获取问卷信息失败");
  }

}

watch(
  () => props.customer._id,
  () => {
    page.value = 1;
    getList();
  },
  { immediate: true }
);

const { close, show, visible, width } = useModal();

onMounted(() => {
  trigger.$on("update-service-record", () => {
    page.value = 1;
    getList();
  });
});

onUnmounted(() => trigger.$off("update-service-record"));
const { lock, handleScroll } = useLockScrollbar();

const { close: closeDetailModal, show: showDetailModal, visible: detailVisible } = useModal();
const currentRecord = ref({});

function toggle(record){
  expand.value[record._id] = !expand.value[record._id];
}
function view(record = {}) {
  currentRecord.value = record;
  show();
}

async function remove() {
  if (loading.value) return;
  try {
    await ElMessageBox.confirm("是否删除该服务记录？", { type: "warning" });
    closeDetailModal();
    loading.value = true;
    let { success, message } = await removeRecord(currentRecord.value._id);
    if (success) {
      ElMessage.success("删除成功");
      loading.value = false;
      change();
    } else {
      ElMessage.error(message);
    }
  } catch (e) {
    loading.value = false;
  }
}
</script>
<style lang="scss" scoped>
.visit-node::before {
  content: "";
  position: absolute;
  top: 20px;
  left: 15px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transform: translateX(-50%);
  background-color: var(--yc-primary-color);
  z-index: 2;
}

.visit-node::after {
  content: "";
  position: absolute;
  top: 20px;
  left: 15px;
  width: 1px;
  height: 100%;
  border-radius: 50%;
  transform: translateX(-50%);
  background-color: var(--yc-primary-color);
  z-index: 2;
}

.visit-node--last::after {
  display: none;
}
</style>
