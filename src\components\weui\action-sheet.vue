<template>
  <teleport v-if="visible" to="body">
    <div class="weui-mask" id="iosMask"></div>
    <div class="weui-actionsheet weui-actionsheet_toggle" id="iosActionsheet">
      <div v-if="title" class="weui-actionsheet__title">
        <p class="weui-actionsheet__title-text">{{ title }}</p>
      </div>
      <div class="weui-actionsheet__menu">
        <div v-for="(i, idx) in itemList" :key="idx" class="weui-actionsheet__cell" :class="i.classnames"
          @click="select(idx)">{{i.label}}
        </div>
      </div>
      <div class="weui-actionsheet__action">
        <div class="weui-actionsheet__cell" id="iosActionsheetCancel" @click="close()">取消</div>
      </div>
    </div>
  </teleport>
</template>
<script setup>
const emits = defineEmits(['close', 'select'])
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  itemList: {
    type: Array,
    default: () => []
  },
  visible: {
    type: Boolean,
    default: false
  }
})

function select(idx) {
  emits('select', props.itemList[idx])
}

function close() {
  emits('close')
}

</script>
<style lang="scss" scoped>
.weui-mask {
  position: fixed;
  z-index: 1000;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
}

.weui-actionsheet {
  position: fixed;
  left: 0;
  bottom: constant(safe-area-inset-bottom, 0px);
  bottom: env(safe-area-inset-bottom, 0px);
  bottom: 0;
  -webkit-transform: translate(0, 100%);
  transform: translate(0, 100%);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  z-index: 5000;
  width: 100%;
  background-color: #EFEFF4;
  -webkit-transition: -webkit-transform .3s;
  transition: -webkit-transform .3s;
  transition: transform .3s;
  transition: transform .3s, -webkit-transform .3s;
}

.weui-actionsheet__title {
  position: relative;
  height: 65px;
  padding: 0 20px;
  line-height: 1.4;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  flex-direction: column;
  text-align: center;
  font-size: 14px;
  color: #888;
  background: #FCFCFD;
}

.weui-actionsheet__title:before {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}

.weui-actionsheet__title .weui-actionsheet__title-text {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.weui-actionsheet__menu {
  background-color: #FCFCFD;
}

.weui-actionsheet__action {
  margin-top: 6px;
  background-color: #FCFCFD;
}

.weui-actionsheet__cell {
  position: relative;
  padding: 10px 0;
  text-align: center;
  font-size: 18px;
}

.weui-actionsheet__cell:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid #e5e5e5;
  color: #e5e5e5;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}

.weui-actionsheet__cell:active {
  background-color: #ECECEC;
}

.weui-actionsheet__cell:first-child:before {
  display: none;
}

.weui-actionsheet_toggle {
  -webkit-transform: translate(0, 0);
  transform: translate(0, 0);
}
</style>
