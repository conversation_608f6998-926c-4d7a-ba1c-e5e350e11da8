<!-- 咨询接待 -->
<template>
  <my-layout>
    <layout-item>
      <div class="overflow-hidden mb-8px">
        <el-row :gutter="10">
          <el-col v-for="i in count" :key="i.key" :span="6">
            <div class="flex text-center rounded bg-white rounded px-20px py-16px h-80px">
              <div class="flex items-center flex-col px-10px w-3/5 h-full">
                <el-tooltip placement="bottom" effect="light">
                  <div class="w-full pt-4px truncate text-18px font-semibold">{{ i.count }}/{{ i.tradeCount }}</div>
                  <template #content>
                    <div class="text-16px font-semibold">{{ i.count }}/{{ i.tradeCount }}</div>
                  </template>
                </el-tooltip>
                <div class="w-full mt-8px text-14px truncate text-gray-500">{{ i.label }}</div>
              </div>
              <div class="flex items-center flex-col px-10px w-2/5 h-full border-l border-gray-200">
                <el-tooltip placement="bottom" effect="light">
                  <div class="w-full pt-4px truncate text-18px font-semibold">￥{{ i.tradeAmount }}</div>
                  <template #content>
                    <div class="text-16px font-semibold">￥{{ i.tradeAmount }}</div>
                  </template>
                </el-tooltip>
                <div class="w-full mt-8px text-14px truncate text-gray-500">消费总额</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="py-5px flex bg-white">
        <expand-filter-box v-model:can-expand="canExpand" :enable-expand="true" class="flex-grow" :expand="expand">
          <date-range-filter v-model:dates="searchForm.triageTimeDates" label="分诊日期" :text="getDatesText(searchForm.triageTimeDates)" />
          <date-range-filter v-model:dates="searchForm.receptionDates" label="接诊日期" :text="getDatesText(searchForm.receptionDates)" />
          <check-box-filter v-model="searchForm.receptionPersonUserIds" label="接诊人" :list="peopleList" v-if="isManager">
            <template #item="{ item }">
              <ww-user :openid="item.value"></ww-user>
            </template>
          </check-box-filter>
          <check-box-filter v-model="counselors" label="所属咨询" :list="peopleList">
            <template #item="{ item }">
              <ww-user :openid="item.value"></ww-user>
            </template>
          </check-box-filter>
          <check-box-filter v-model="introducers" label="所属开发" :list="introducerList">
            <template #item="{ item }">
              <ww-user :openid="item.value"></ww-user>
            </template>
          </check-box-filter>
          <filter-customer-source v-model="searchForm.customerSource" label="开发渠道" />
          <check-box-filter v-model="searchForm.visitStatus" label="就诊状态" :list="VisitStatus"></check-box-filter>
          <check-box-filter v-model="searchForm.consumeStatus" label="消费状态" :list="ConsumeStatus"></check-box-filter>
          <radio-filter v-model="searchForm.tradeStatus" label="成交状态" :list="TradeStatus"></radio-filter>
          <filter-customer-stage v-model="searchForm.stages" />
          <check-box-filter v-model="searchForm.consultStages" label="接诊类型" :list="ConsultStage" />
          <filter-info-source v-model="searchForm.source" label="信息来源" />
          <div class="text-gray-500 text-14px flex items-center">
            <div class="filter-label pr-5px">咨询科室:</div>
            <dept-select v-model="searchForm.deptId" placeholder="请选择科室" @change="handleDeptChange" />
          </div>
          <div class="text-gray-500 text-14px flex items-center ml-20px">
            <div class="filter-label pr-5px">咨询项目:</div>
            <project-intent-select v-model="projectIds" @change="handleProjectChange" placeholder="请选择咨询项目" type="filter" style="width: 200px" multiple :deptId="searchForm.deptId" />
          </div>
          <input-filter v-model="searchForm.name" label="客户姓名" placeholder="请输入" :width="200" />
          <input-filter v-model="searchForm.mobile" label="客户手机号" placeholder="请输入" :width="200" />
        </expand-filter-box>
        <div class="flex-shrink-0 flex items-center h-40px pr-15px">
          <el-button v-if="canExpand" text plain @click="expand = !expand">
            <span class="mr-5px">{{ expand ? "收起筛选" : "更多筛选" }}</span>
            <el-icon v-if="expand">
              <ArrowUpBold />
            </el-icon>
            <el-icon v-else>
              <ArrowDownBold />
            </el-icon>
          </el-button>
          <el-button type="primary" @click="search">查询</el-button>
          <el-button type="success" @click="exportToExcel">导出Excel</el-button>
          <el-button plain @click="reset">重置</el-button>
        </div>
      </div>
    </layout-item>
    <layout-main :scroll="false">
      <el-table border stripe height="100%" :data="list" v-loading="loading" @row-click="handleRowClick">
        <el-common-column prop="dealStatus" label="成交状态" :min-width="100">
          <template #default="{ row: { tradeAmount, dealStatus } }">
            <div v-if="dealStatus === '成交' || tradeAmount > 0" style="color: green">已成交</div>
            <div v-else style="color: red">未成交</div>
          </template>
        </el-common-column>
        <el-common-column prop="name" label="姓名" :min-width="100">
          <template #default="{ row: { name, customerId } }">
            <div @click.stop="toCustomerDetail(customerId)" main-color class="pointer">{{ name }}</div>
          </template>
        </el-common-column>
        <el-common-column prop="mobile" label="联系方式" :min-width="120">
          <template #default="{ row: { mobile } }">
            <span>{{ maskPhone(mobile, 3, 4) }}</span>
          </template>
        </el-common-column>
        <el-common-column prop="wechat" label="微信联系人" :min-width="120" classnames="w-full">
          <template #default="{ row }">
            <wx-contact-td :customerId="row.customerInfo ? row.customerInfo._id : ''" :externalUserId="row.customerInfo ? row.customerInfo.externalUserId : ''" :row="row.customerInfo" @change="getList" />
          </template>
        </el-common-column>
        <el-common-column prop="visitStatusStr" label="就诊状态" :min-width="100">
          <template #default="{ row: { visitStatusStr, visitStatus } }">
            <div :style="'color:' + VisitStatusColor[visitStatus]">
              {{ visitStatusStr }}
            </div>
          </template>
        </el-common-column>
        <el-common-column prop="counselor" label="所属咨询" :min-width="120">
          <template #default="{ row: { counselorUserId, _id } }">
            <div class="cursor-pointer hover:text-primary align-center">
              <ww-user v-if="counselorUserId" :openid="counselorUserId" />
              <el-icon color="#006eff" class="ml-3px" @click.stop="changeCounselor(_id, counselorUserId)" v-if="viewType === 'record'"><EditPen /></el-icon>
            </div>
          </template>
        </el-common-column>
        <el-common-column prop="introducer" label="所属开发" :min-width="120">
          <template #default="{ row: { introducerUserId, _id } }">
            <div class="cursor-pointer hover:text-primary align-center">
              <ww-user v-if="introducerUserId" :openid="introducerUserId" />
              <el-icon color="#006eff" class="ml-3px" @click.stop="changeIntroducer(_id, introducerUserId)" v-if="viewType === 'record'"><EditPen /></el-icon>
            </div>
          </template>
        </el-common-column>
        <el-common-column prop="receptionPersonUserId" label="接诊人员" :min-width="120">
          <template #default="{ row: { receptionPersonUserId } }">
            <ww-user :openid="receptionPersonUserId" />
          </template>
        </el-common-column>
        <el-common-column prop="consultStageStr" label="接诊类型" :min-width="90"></el-common-column>
        <el-common-column prop="srouce" label="开发渠道" :min-width="100">
          <template #default="{ row: { customerSourceStr } }">
            {{ customerSourceStr }}
          </template>
        </el-common-column>
        <el-common-column prop="srouce" label="信息来源" :min-width="120">
          <template #default="{ row: { source, _id } }">
            <div class="cursor-pointer hover:text-primary align-center">
              {{ Array.isArray(source) ? source.join("-") : "" }}
              <el-icon color="#006eff" class="ml-3px" @click.stop="changeInfoSource(_id, source)" v-if="viewType === 'record'"><EditPen /></el-icon>
            </div>
          </template>
        </el-common-column>
        <el-common-column prop="department" label="咨询科室" :min-width="120">
          <template #default="{ row: { projectDeptIds } }">
            <dept-name-transformer :dept-id="projectDeptIds" />
          </template>
        </el-common-column>
        <el-common-column prop="project" label="咨询项目" :min-width="240">
          <template #default="{ row: { projectNames } }">
            <el-tooltip :disabled="!projectNames" placement="top" effect="light" :content="projectNames" popper-class="max-w-480px">
              <span>{{ projectNames }}</span>
            </el-tooltip>
          </template>
        </el-common-column>
        <el-common-column prop="tradeAmountStr" label="成交金额" :min-width="120"></el-common-column>
        <el-common-column prop="consumeStatus" label="消费情况" :min-width="120"></el-common-column>
        <el-common-column prop="plan" label="跟进计划" :width="100">
          <template #default="{ row: { hasPlan } }">
            <td-wrapper :class="hasPlan ? 'w-full' : 'w-full text-gray-400'">{{ hasPlan ? "有" : "无" }}计划</td-wrapper>
          </template>
        </el-common-column>
        <el-common-column prop="customerComplaint" label="主诉" :min-width="200">
          <template #default="{ row: { customerComplaint } }">
            <el-popover placement="top" width="400" trigger="click" v-if="customerComplaint">
              <template #reference>
                <div class="cursor-pointer whitespace-pre-wrap leading-5 max-h-10 overflow-hidden line-clamp-2 hover:text-blue-500 transition-colors" @click.stop>
                  {{ customerComplaint }}
                </div>
              </template>
              <div class="whitespace-pre-wrap text-14px leading-6 max-h-300px overflow-y-auto">
                {{ customerComplaint }}
              </div>
            </el-popover>
            <div v-else class="text-gray-400">暂无主诉</div>
          </template>
        </el-common-column>
        <el-common-column prop="dealPlan" label="处理方案" :min-width="200">
          <template #default="{ row: { dealPlan } }">
            <el-tooltip :disabled="!dealPlan" placement="top" effect="light" :content="dealPlan" popper-class="max-w-480px">
              <span>{{ dealPlan }}</span>
            </el-tooltip>
          </template>
        </el-common-column>
        <el-common-column prop="noDealReason" label="未成交原因" :min-width="300">
          <template #default="{ row: { noDealReason } }">
            <el-tooltip :disabled="!noDealReason" placement="top" effect="light" :content="noDealReason" popper-class="max-w-480px">
              <span>{{ noDealReason }}</span>
            </el-tooltip>
          </template>
        </el-common-column>
        <el-common-column prop="triageTimeStr" label="分诊时间" :min-width="160"></el-common-column>
        <el-common-column prop="triageRemark" label="分诊备注" :min-width="200">
          <template #default="{ row: { triageRemark } }">
            <el-tooltip placement="top" effect="light" :content="triageRemark">
              <div class="truncate">{{ triageRemark }}</div>
            </el-tooltip>
          </template>
        </el-common-column>
        <el-common-column prop="receptionTimeStr" label="接诊时间" :min-width="160"></el-common-column>
        <el-table-column fixed="right" class-name="el-table-fixed-column--shadow" label="操作" width="200">
          <template #default="{ row }">
            <el-text class="cursor-pointer mr-5px" type="primary" v-if="row.visitStatus === 'visited'" @click.stop="read(row)">详情</el-text>
            <el-text class="cursor-pointer mr-5px" type="primary" @click.stop="recept(row)" v-if="props.viewType === 'reception' && row.visitStatus === 'pending' && dayjs(row.triageTime).isSame(dayjs(), 'day')">接诊</el-text>
            <el-text class="cursor-pointer mr-5px" type="primary" @click.stop="bill(row)" v-if="props.viewType === 'reception' && row.visitStatus === 'visited' && dayjs(row.triageTime).isSame(dayjs(), 'day')">开单</el-text>
            <el-text class="cursor-pointer mr-5px" type="primary" @click.stop="billDetial(row)" v-if="row.tradeAmount || row.isBilled">开单记录</el-text>
            <el-text class="cursor-pointer mr-5px" type="primary" v-if="props.viewType === 'reception'" @click.stop="toPlan(row)">回访计划</el-text>
          </template>
        </el-table-column>
      </el-table>
    </layout-main>
    <layout-item>
      <pagination :totalRow="total" :pageSize="pageSize" :currentPage="page" @handle-size-change="changeSize" @handle-current-change="changePage" />
    </layout-item>
  </my-layout>
  <record-modal :doctorList="doctorList" :visible="visible" :width="width" @close="close" :consultRecord="consultRecord" @success="updateData" @showCustomerDetail="toCustomerDetail" :viewType="viewType" />
  <search-modal v-model="searchVisible" @close="searchClose()" @consult="openReception" @addCustomer="onAddCustomer" />
  <reception-drawer v-model="receptionVisible" :customer="customer" @success="updateData" :consult="consult" @showCustomerDetail="toCustomerDetail" />
  <add-customer :visible="addCustomerVisible" @close="addCustomerVisible = false" @update="addSuccess" title="新增患者" viewType="consult" />
  <customer-detail v-if="customerDetailVisible" :enableAction="['managementPlan']" :visible="customerDetailVisible" @close="customerDetailVisible = false" :customerId="customerId" title="客户详情" @update="updateCustomerDetial" customerType="corpCustomer" />
  <bill-modal v-model="billVisible" @close="billVisible = false" :customer="billCustomer" :consultRecord="consultRecord" @success="updateData" @showCustomerDetail="toCustomerDetail" @changeName="changeName" />
  <bill-detail-modal v-model="billDetailVisible" @close="billDetailVisible = false" :consultRecord="consultRecord" @success="updateData" :viewType="viewType" />
  <component v-if="ManagePlanDrawer" :is="ManagePlanDrawer" :customer="customer" :visible="drawerVisible" @close="closeDrawer" />

  <!-- 替换原来的内联对话框为组件 -->
  <edit-introducer-dialog v-model:visible="introducerDialogVisible" :current-id="selectedIntroducerId" :introducer-list="introducerList" @submit="handleIntroducerSubmit" />

  <edit-counselor-dialog v-model:visible="counselorDialogVisible" :current-id="selectedCounselorId" :counselor-list="peopleList" @submit="handleCounselorSubmit" />

  <edit-info-source-dialog v-model:visible="infoSourceDialogVisible" :current-source="selectedInfoSource" @submit="handleInfoSourceSubmit" />
</template>
<script setup>
import { ref, onMounted, computed, watch } from "vue";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import { getConsultRecord, getConsultRecordCount, getConsultStageCount, updateConsultRecord } from "@/api/consult";
import { VisitType, ConsumeStatus, ConsultStage, VisitStatus, VisitStatusColor, TradeStatus, VisitStatusObj } from "@/baseData";
import useManagePlanDrawer from "@/composables/useManagePlanDrawer";
import useElPagination from "@/hooks/useElPagination";
import useModal from "@/hooks/useModal";
import { getCustomers } from "@/api/member";
import { memberStore } from "@/store/member";
import { staffStore } from "@/store/staff";
import { teamStore } from "@/store/team";
import { maskPhone } from "@/utils";
import addCustomer from "@/views/member/customer/add-customer.vue";
import billDetailModal from "./bill-detail-modal";
import billModal from "./bill-modal.vue";
import { CheckBoxFilter, DateRangeFilter, InputFilter, RadioFilter, expandFilterBox, filterInfoSource, filterCustomerSource } from "@/components/filter-bar";
import customerDetail from "@/views/member/detail/index.vue";
import elCommonColumn from "@/components/el-common-column/el-common-column.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import projectIntentSelect from "@/components/benefit-management/project-intent-select.vue";
import receptionDrawer from "../consult-appoint/reception-drawer.vue";
import recordModal from "./record-modal.vue";
import searchModal from "../consult-appoint/search-modal.vue";
import wxContactTd from "@/components/wx-contact-td/wx-contact-td.vue";
import DeptNameTransformer from "@/components/dept-components/dept-name-transformer.vue";
import DeptSelect from "@/components/dept-components/dept-select.vue";
import { useDeptStore } from "@/components/dept-components/dept-store";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import { ArrowUpBold, ArrowDownBold, EditPen } from "@element-plus/icons-vue";
// 导入四个新组件
import EditIntroducerDialog from "./components/edit-introducer-dialog.vue";
import EditCounselorDialog from "./components/edit-counselor-dialog.vue";
import EditInfoSourceDialog from "./components/edit-info-source-dialog.vue";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";

const props = defineProps({
  viewType: {
    type: String,
    default: "reception",
  },
});

// 所属开发修改相关
const introducerDialogVisible = ref(false);
const selectedIntroducerId = ref("");
const currentRecordId = ref("");
const introducerSubmitting = ref(false);

// 打开修改所属开发弹窗
function changeIntroducer(recordId, currentIntroducerId) {
  currentRecordId.value = recordId;
  selectedIntroducerId.value = currentIntroducerId || "";
  introducerDialogVisible.value = true;
}

// 处理所属开发提交
function handleIntroducerSubmit(data) {
  if (!currentRecordId.value) {
    ElMessage.error("记录ID不能为空");
    return;
  }

  introducerSubmitting.value = true;
  updateConsultRecord({
    id: currentRecordId.value,
    params: data,
  })
    .then(({ success }) => {
      if (success) {
        ElMessage.success("修改成功");
        introducerDialogVisible.value = false;
        getList(); // 刷新列表
      } else {
        ElMessage.error("修改失败");
      }
    })
    .catch((error) => {
      console.error("修改所属开发失败:", error);
      ElMessage.error("系统异常，请稍后重试");
    })
    .finally(() => {
      introducerSubmitting.value = false;
    });
}

// 所属咨询修改相关
const counselorDialogVisible = ref(false);
const selectedCounselorId = ref("");
const counselorSubmitting = ref(false);

// 打开修改所属咨询弹窗
function changeCounselor(recordId, currentCounselorId) {
  currentRecordId.value = recordId;
  selectedCounselorId.value = currentCounselorId || "";
  counselorDialogVisible.value = true;
}

// 处理所属咨询提交
function handleCounselorSubmit(data) {
  if (!currentRecordId.value) {
    ElMessage.error("记录ID不能为空");
    return;
  }

  counselorSubmitting.value = true;
  updateConsultRecord({
    id: currentRecordId.value,
    params: data,
  })
    .then(({ success }) => {
      if (success) {
        ElMessage.success("修改成功");
        counselorDialogVisible.value = false;
        getList(); // 刷新列表
      } else {
        ElMessage.error("修改失败");
      }
    })
    .catch((error) => {
      console.error("修改所属咨询失败:", error);
      ElMessage.error("系统异常，请稍后重试");
    })
    .finally(() => {
      counselorSubmitting.value = false;
    });
}

// 信息来源修改相关
const infoSourceDialogVisible = ref(false);
const selectedInfoSource = ref([]);
const infoSourceSubmitting = ref(false);

// 打开修改信息来源弹窗
function changeInfoSource(recordId, currentSource) {
  currentRecordId.value = recordId;
  selectedInfoSource.value = currentSource || [];
  infoSourceDialogVisible.value = true;
}

// 处理信息来源提交
function handleInfoSourceSubmit(data) {
  if (!currentRecordId.value) {
    ElMessage.error("记录ID不能为空");
    return;
  }

  infoSourceSubmitting.value = true;
  updateConsultRecord({
    id: currentRecordId.value,
    params: data,
  })
    .then(({ success }) => {
      if (success) {
        ElMessage.success("修改成功");
        infoSourceDialogVisible.value = false;
        getList(); // 刷新列表
      } else {
        ElMessage.error("修改失败");
      }
    })
    .catch((error) => {
      console.error("修改信息来源失败:", error);
      ElMessage.error("系统异常，请稍后重试");
    })
    .finally(() => {
      infoSourceSubmitting.value = false;
    });
}

const { currentTeamId, managerList } = storeToRefs(teamStore());
const customerDetailVisible = ref(false);
const loading = ref(false);
const customerId = ref("");
const list = ref([]);
const { isAdmin } = storeToRefs(memberStore());
const { visible: drawerVisible, ManagePlanDrawer, openDrawer, closeDrawer } = useManagePlanDrawer();
const isManager = computed(() => {
  return managerList.value.includes(10007) || isAdmin.value || props.viewType === "record";
});
function toCustomerDetail(id) {
  customerId.value = id;
  customerDetailVisible.value = true;
}
const searchForm = ref({
  receptionPersonUserIds: [],
  triageTimeDates: [dayjs().format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")],
  customerSource: [],
  deptId: "", // 添加科室ID字段
});
const introducers = ref([]);
const counselors = ref([]);
const deptStore = useDeptStore(); // 初始化deptStore

watch(
  () => currentTeamId.value,
  () => {
    getList();
    getStatics();
  }
);

// 监听 isManager 变化，动态更新 receptionPersonUserIds
watch(
  () => isManager.value,
  (newVal) => {
    searchForm.value.receptionPersonUserIds = newVal ? (props.viewType === "record" ? [] : [localStorage.getItem("userId")]) : [];
  },
  { immediate: true }
);
const projectIds = ref([]);
const selectedProjects = ref([]);
const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());
const peopleList = computed(() => {
  const allConsultants = staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("consultant"));
  if (isManager.value) {
    return allConsultants.map((i) => ({ value: i.userid, label: i.anotherName || i.userid }));
  } else {
    const currentUserId = localStorage.getItem("userId");
    return allConsultants.filter((i) => i.userid === currentUserId).map((i) => ({ value: i.userid, label: i.anotherName || i.userid }));
  }
});
const introducerList = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("onlineCustomerService")).map((i) => ({ value: i.userid, label: i.anotherName || i.userid }));
});

const doctorList = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("doctor")).map((i) => ({ value: i.userid }));
});

const { judgmentIsAdmin } = memberStore();

function handleProjectChange(projects) {
  selectedProjects.value = projects;
}

function getDatesText(dates) {
  return dates && Array.isArray(dates) ? dates.join(" ~ ") : "";
}
const date = ref();
const total = ref(0);
const expand = ref(false);
const canExpand = ref(false);
const { page, pageSize, changePage, changeSize } = useElPagination(getList);
const { close, show, visible, width } = useModal(640);
const { close: planClose, show: planShow, visible: planVisible, width: planWidth } = useModal(640);
const { show: receptionShow, visible: receptionVisible } = useModal();
const { show: addCustomerShow, visible: addCustomerVisible, close: addCustomerClose } = useModal();
const { close: searchClose, show: searchShow, visible: searchVisible, width: searchWidth } = useModal(1200);
const { close: closeProjectModal, show: showProjectModal, visible: projectModalVisible, width: projectModalWidth } = useModal(800);
const { close: closeBillVisible, show: showBillVisible, visible: billVisible } = useModal(800);
const { close: closeDetailVisible, show: showDetailVisible, visible: billDetailVisible } = useModal(800);
const customer = ref({});
const consultRecord = ref({});
const billCustomer = ref({});
const consult = ref({});
function openReception(item) {
  customer.value = item;
  consult.value = {};
  if (Array.isArray(item.projects)) {
    consult.value.projects = item.projects.map((i) => ({ _id: i._id, projectCateId: i.projectCateId }));
  }
  searchClose();
  receptionShow();
}
const executorUserId = ref("");
function toPlan(row) {
  customer.value = row.customerInfo;
  openDrawer();
}
function billDetial(row) {
  consultRecord.value = row;
  showDetailVisible();
}
function onAddCustomer() {
  searchClose();
  addCustomerShow();
}
async function addSuccess(id) {
  // 根据id获取数据
  let { success, data } = await getCustomers({ _id: id });
  if (success && data && data.data.length > 0) {
    customer.value = data.data[0];
    receptionShow();
  }
}

function updateCustomerDetial(e) {
  billCustomer.value = e;
  getList();
}

function changeName(name) {
  billCustomer.value.name = name;
  list.value.forEach((i) => {
    if (i.customerId === billCustomer.value._id) {
      i.name = name;
    }
  });
}

function updateData(customer) {
  if (customer && customer._id) {
    billCustomer.value = customer;
  }
  getList();
  getStatics();
}

const selectedDept = ref({});
function handleDeptChange(dept) {
  selectedDept.value = dept;
  projectIds.value = [];
}

async function getList() {
  let query = {
    page: page.value,
    pageSize: pageSize.value,
    ...searchForm.value,
  };
  if (isManager.value) {
    query.receptionPersonUserIds = Array.isArray(query.receptionPersonUserIds) && query.receptionPersonUserIds.length ? query.receptionPersonUserIds : [];
  } else {
    query.receptionPersonUserIds = [];
    query.teamId = currentTeamId.value;
  }
  if (!query.visitStatus) query.visitStatus = ["pending", "visited"];
  if (counselors.value.length) {
    query.counselors = counselors.value;
  }
  if (introducers.value.length) {
    query.introducers = introducers.value;
  }
  if (searchForm.value.deptId) {
    query.deptId = searchForm.value.deptId;
    query.projectIds = (await deptStore.fetchProjectIdsByDeptId(searchForm.value.deptId)) || [];
  }
  if (projectIds.value && projectIds.value.length > 0) {
    query.projectIds = projectIds.value;
  }
  loading.value = true;
  const { success, data } = await getConsultRecord(query);
  loading.value = false;
  if (success) {
    total.value = data.total;
    list.value = data.list.map((i) => {
      return {
        ...i,
        name: i.customerInfo.name,
        mobile: i.customerInfo.mobile,
        projectNames: Array.isArray(i.projectNames) ? i.projectNames.join(" / ") : "",
        triageTimeStr: i.triageTime ? dayjs(i.triageTime).format("YYYY-MM-DD HH:mm") : "",
        tradeTimeStr: i.triageTime ? dayjs(i.tradeTime).format("YYYY-MM-DD HH:mm") : "",
        customerSourceStr: i.customerInfo.customerSource && Array.isArray(i.customerInfo.customerSource) ? i.customerInfo.customerSource.join(" - ") : "",
        visitStatusStr: VisitStatusObj[i.visitStatus],
        visitTypeStr: VisitType.find((j) => j.value === i.visitType)?.label || "",
        consumeStatus: getConsumeStatus(i.consumeCount),
        consultStageStr: ConsultStage.find((j) => j.value === i.consultStage)?.label || "",
        receptionTimeStr: i.receptionTime ? dayjs(i.receptionTime).format("YYYY-MM-DD HH:mm") : "",
        tradeAmountStr: i.tradeAmount >= 0 ? i.tradeAmount?.toFixed(2) : "",
      };
    });
  }
}

function getConsumeStatus(count) {
  if (count === 0) {
    return "未消费";
  } else if (count === 1) {
    return "已消费";
  } else if (count > 1) {
    return "多次消费";
  }
}

function search() {
  getList();
  getStatics();
}

function reset() {
  page.value = 1;
  searchForm.value = {
    receptionPersonUserIds: managerList.value.includes(10007) ? [localStorage.getItem("userId")] : [],
    triageTimeDates: [dayjs().format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")],
    customerSource: [],
    deptId: "", // 重置科室ID
  };
  projectIds.value = [];
  selectedProjects.value = [];
  search();
}

function recept(item) {
  consultRecord.value = item;
  show();
}

function read(item) {
  consultRecord.value = item;
  show();
}
const count = ref([
  { key: "totals", label: "今日接诊总人数/消费人数", tradeAmount: 0, count: 0, tradeCount: 0 },
  { key: "firstVisit", label: "今日初诊人数/消费人数", tradeAmount: 0, count: 0, tradeCount: 0 },
  { key: "returnVisit", label: "今日复诊人数/消费人数", tradeAmount: 0, count: 0, tradeCount: 0 },
  { key: "moreConsumed", label: "今日再消费人数/消费人数", tradeAmount: 0, count: 0, tradeCount: 0 },
]);
async function getStatics() {
  let query = {
    corpId: localStorage.getItem("corpId"),
    createDates: [dayjs().format("YYYY-MM-DD"), dayjs().format("YYYY-MM-DD")],
  };
  if (isManager.value) {
    query.receptionPersonUserIds = searchForm.value.receptionPersonUserIds && Array.isArray(searchForm.value.receptionPersonUserIds) && searchForm.value.receptionPersonUserIds.length ? searchForm.value.receptionPersonUserIds : peopleList.value.map((i) => i.value);
  } else {
    query.receptionPersonUserIds = [localStorage.getItem("userId")];
    query.teamId = currentTeamId.value;
  }

  query.visitStatus = Array.isArray(searchForm.value.visitStatus) && searchForm.value.visitStatus.length ? searchForm.value.visitStatus : ["pending", "visited"];
  const { data, success } = await getConsultStageCount(query);
  if (success) {
    const visitObj = data.data;
    count.value = count.value.map((item) => {
      const i = visitObj[item.key];
      if (i) {
        item.tradeAmount = i.totalTradeAmount;
        item.count = i.visitedCount;
        item.tradeCount = i.tradeCount;
      } else {
        item.tradeAmount = 0;
        item.count = 0;
        item.tradeCount = 0;
      }
      return item;
    });
  }
}

onMounted(async () => {
  await getStaffList(true);
  await judgmentIsAdmin();
  await deptStore.fetchDeptList(); // 确保科室列表已加载
  getList();
  getStatics();
});

// 开单
async function bill(item) {
  consultRecord.value = item;
  billCustomer.value = item.customerInfo;
  showBillVisible();
}

function handleRowClick(row) {
  if (row.visitStatus === "visited") {
    read(row);
  } else if (row.visitStatus === "pending") {
    recept(row);
  }
}

// 导出Excel
async function exportToExcel() {
  if (loading.value) {
    ElMessage.warning("数据正在加载中，请稍后再试");
    return;
  }

  // 显示加载状态
  const loadingInstance = ElLoading.service({
    lock: true,
    text: '正在获取数据，请稍候...',
    background: 'rgba(0, 0, 0, 0.7)'
  });

  try {
    // 先获取总数据量
    let totalParams = {
      page: 1,
      pageSize: 1, // 只获取总数，不需要实际数据
      ...searchForm.value,
    };

    if (isManager.value) {
      totalParams.receptionPersonUserIds = Array.isArray(totalParams.receptionPersonUserIds) && totalParams.receptionPersonUserIds.length ? totalParams.receptionPersonUserIds : [];
    } else {
      totalParams.receptionPersonUserIds = [];
      totalParams.teamId = currentTeamId.value;
    }
    if (!totalParams.visitStatus) totalParams.visitStatus = ["pending", "visited"];
    if (counselors.value.length) {
      totalParams.counselors = counselors.value;
    }
    if (introducers.value.length) {
      totalParams.introducers = introducers.value;
    }
    if (searchForm.value.deptId) {
      totalParams.deptId = searchForm.value.deptId;
      totalParams.projectIds = (await deptStore.fetchProjectIdsByDeptId(searchForm.value.deptId)) || [];
    }
    if (projectIds.value && projectIds.value.length > 0) {
      totalParams.projectIds = projectIds.value;
    }

    // 获取总数据量
    const totalResult = await getConsultRecord(totalParams);
    const totalCount = totalResult.data.total;

    if (totalCount === 0) {
      loadingInstance.close();
      ElMessage.warning("暂无数据可导出");
      return;
    }

    // 计算需要分多少批
    const pageSize = 1000; // 每批1000条
    const totalPages = Math.ceil(totalCount / pageSize);
    
    loadingInstance.setText(`正在下载数据，共${totalCount}条记录，请稍候...`);

    // 分批获取所有数据
    const allData = [];
    for (let currentPage = 1; currentPage <= totalPages; currentPage++) {
      loadingInstance.setText(`正在下载数据 ${currentPage}/${totalPages} 批，共${totalCount}条记录...`);
      
      const batchParams = {
        ...totalParams,
        page: currentPage,
        pageSize: pageSize
      };

      const batchResult = await getConsultRecord(batchParams);
      if (batchResult.success && batchResult.data.list) {
        const batchData = batchResult.data.list.map((i) => {
          return {
            ...i,
            name: i.customerInfo.name,
            mobile: i.customerInfo.mobile,
            projectNames: Array.isArray(i.projectNames) ? i.projectNames.join(" / ") : "",
            triageTimeStr: i.triageTime ? dayjs(i.triageTime).format("YYYY-MM-DD HH:mm") : "",
            tradeTimeStr: i.triageTime ? dayjs(i.tradeTime).format("YYYY-MM-DD HH:mm") : "",
            customerSourceStr: i.customerInfo.customerSource && Array.isArray(i.customerInfo.customerSource) ? i.customerInfo.customerSource.join(" - ") : "",
            visitStatusStr: VisitStatusObj[i.visitStatus],
            visitTypeStr: VisitType.find((j) => j.value === i.visitType)?.label || "",
            consumeStatus: getConsumeStatus(i.consumeCount),
            consultStageStr: ConsultStage.find((j) => j.value === i.consultStage)?.label || "",
            receptionTimeStr: i.receptionTime ? dayjs(i.receptionTime).format("YYYY-MM-DD HH:mm") : "",
            tradeAmountStr: i.tradeAmount >= 0 ? i.tradeAmount?.toFixed(2) : "",
          };
        });

        allData.push(...batchData);
      }

      // 添加小延迟，避免请求过于频繁
      if (currentPage < totalPages) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    loadingInstance.setText('正在生成Excel文件...');

    // 准备导出数据
    const exportData = [];

    // 添加标题
    exportData.push(["咨询接待数据表"]);

    // 添加筛选条件信息
    if (searchForm.value.triageTimeDates && searchForm.value.triageTimeDates.length === 2) {
      exportData.push(["分诊日期范围", `${searchForm.value.triageTimeDates[0]} 至 ${searchForm.value.triageTimeDates[1]}`]);
    }

    if (searchForm.value.receptionDates && searchForm.value.receptionDates.length === 2) {
      exportData.push(["接诊日期范围", `${searchForm.value.receptionDates[0]} 至 ${searchForm.value.receptionDates[1]}`]);
    }

    if (searchForm.value.visitStatus && searchForm.value.visitStatus.length > 0) {
      const statusTexts = searchForm.value.visitStatus.map(status => {
        const statusItem = VisitStatus.find(item => item.value === status);
        return statusItem ? statusItem.label : status;
      });
      exportData.push(["就诊状态", statusTexts.join(", ")]);
    }

    if (searchForm.value.consumeStatus && searchForm.value.consumeStatus.length > 0) {
      const statusTexts = searchForm.value.consumeStatus.map(status => {
        const statusItem = ConsumeStatus.find(item => item.value === status);
        return statusItem ? statusItem.label : status;
      });
      exportData.push(["消费状态", statusTexts.join(", ")]);
    }

    if (searchForm.value.consultStages && searchForm.value.consultStages.length > 0) {
      const stageTexts = searchForm.value.consultStages.map(stage => {
        const stageItem = ConsultStage.find(item => item.value === stage);
        return stageItem ? stageItem.label : stage;
      });
      exportData.push(["接诊类型", stageTexts.join(", ")]);
    }

    exportData.push([`总计导出记录数: ${allData.length} 条`]);
    exportData.push([]); // 空行

    // 添加表头
    exportData.push([
      "成交状态",
      "姓名", 
      "联系方式",
      "就诊状态",
      "所属咨询",
      "所属开发",
      "接诊人员",
      "接诊类型",
      "开发渠道",
      "信息来源",
      "咨询科室",
      "咨询项目",
      "成交金额",
      "消费情况",
      "跟进计划",
      "主诉",
      "处理方案",
      "未成交原因",
      "分诊时间",
      "分诊备注",
      "接诊时间"
    ]);

    // 添加数据行
    allData.forEach((row) => {
      const dealStatusText = (row.tradeAmount > 0 || row.dealStatus === '成交') ? "已成交" : "未成交";
      const contactInfo = row.mobile;
      const hasPlanText = row.hasPlan ? "有计划" : "无计划";

      exportData.push([
        dealStatusText,
        row.name || "",
        contactInfo || "",
        row.visitStatusStr || "",
        "", // 所属咨询，需要通过ww-user组件获取名字，这里暂时留空
        "", // 所属开发，需要通过ww-user组件获取名字，这里暂时留空  
        "", // 接诊人员，需要通过ww-user组件获取名字，这里暂时留空
        row.consultStageStr || "",
        row.customerSourceStr || "",
        Array.isArray(row.source) ? row.source.join("-") : "",
        "", // 咨询科室，需要通过dept-name-transformer组件获取，这里暂时留空
        row.projectNames || "",
        row.tradeAmountStr || "",
        row.consumeStatus || "",
        hasPlanText,
        row.customerComplaint || "",
        row.dealPlan || "",
        row.noDealReason || "",
        row.triageTimeStr || "",
        row.triageRemark || "",
        row.receptionTimeStr || ""
      ]);
    });

    // 创建工作簿和工作表
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet(exportData);

    // 设置列宽
    const colWidths = [
      { wch: 10 }, // 成交状态
      { wch: 12 }, // 姓名
      { wch: 15 }, // 联系方式
      { wch: 10 }, // 就诊状态
      { wch: 12 }, // 所属咨询
      { wch: 12 }, // 所属开发
      { wch: 12 }, // 接诊人员
      { wch: 10 }, // 接诊类型
      { wch: 25 }, // 开发渠道
      { wch: 20 }, // 信息来源
      { wch: 15 }, // 咨询科室
      { wch: 30 }, // 咨询项目
      { wch: 12 }, // 成交金额
      { wch: 12 }, // 消费情况
      { wch: 10 }, // 跟进计划
      { wch: 30 }, // 主诉
      { wch: 30 }, // 处理方案
      { wch: 30 }, // 未成交原因
      { wch: 18 }, // 分诊时间
      { wch: 30 }, // 分诊备注
      { wch: 18 }  // 接诊时间
    ];

    ws["!cols"] = colWidths;

    // 合并标题单元格
    ws["!merges"] = [
      { s: { r: 0, c: 0 }, e: { r: 0, c: 20 } }, // 标题行
    ];

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, "咨询接待数据");

    // 生成Excel文件并下载
    const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
    const blob = new Blob([excelBuffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });

    // 使用当前日期作为文件名的一部分
    const now = new Date();
    const fileName = `咨询接待数据_${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}.xlsx`;

    saveAs(blob, fileName);

    loadingInstance.close();
    ElMessage.success(`导出Excel成功，共导出 ${allData.length} 条记录`);

  } catch (error) {
    loadingInstance.close();
    console.error('导出Excel失败:', error);
    ElMessage.error("导出Excel失败，请稍后重试");
  }
}
</script>
<style lang="scss" scoped></style>
