<template>
  <el-dialog title="选择分类" :model-value="visible" :close-on-click-modal="false" :width="width" @close="close()">
    <div v-loading="loading" class="flex h-40vh">
      <div class="flex-shrink-0 w-full border-gray-200">
        <el-scrollbar>
          <el-tree class="classify-list-side-tree" accordion ref="treeRef" :data="treeData" node-key="_id" :expand-on-click-node="false" @node-click="onNodeClick">
            <template #default="{ node, data }">
              <div class="flex-grow flex items-center text-14px py-12px px-5px border-b whitespace-normal border-gray-200">
                <div class="w-16px flex-shrink-0 box-content" :style="{ textWrap: 'wrap', paddingLeft: (data.level || 0) * 5 + 'px' }">
                  <template v-if="data.children && data.children.length">
                    <el-icon v-if="node.expanded" class="text-14px text-gray-500">
                      <CaretBottom />
                    </el-icon>
                    <el-icon v-else class="text-14px text-gray-500">
                      <CaretRight />
                    </el-icon>
                  </template>
                </div>
                <div class="w-0 flex-grow break-all text-14px leading-20px" :class="current && current._id === data._id ? 'text-blue-500' : ''">
                  {{ node.label }}
                </div>
                <div v-if="!selectWidthCate" class="flex-shrink-0 w-30px ml-10px" @click.stop="toggle(data)">
                  <el-checkbox value="Value 1" :model-value="data.value && Boolean(selectMap[data.value])" />
                </div>
              </div>
            </template>
          </el-tree>
        </el-scrollbar>
      </div>
    </div>
    <template #footer>
      <span class="block text-center">
        <el-button class="w-100px" @click="close()">取消</el-button>
        <el-button class="w-100px" type="primary" @click="dialogSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, computed, nextTick } from "vue";
import { useVModel, watchDebounced } from "@vueuse/core";
import { getProjectCateList, getProjectListByCateIds } from "@/api/project-manage";
import useClassifyList from "@/components/classify-list/useClassifyList";
import { getCateTreeData } from "@/utils/index";

const emits = defineEmits(["close", "change"]);
const props = defineProps({
  selectWidthCate: { type: Boolean, default: false },
  visible: { type: Boolean, default: false },
  width: { type: [Number, String], default: 400 },
  // 新增prop，接收初始选中的分类
  initialSelectedCategories: { type: Array, default: () => [] },
});

const cateProjectIdCache = new Map();
const current = ref({});
const loading = ref(false);
const options = {
  getList: getProjectCateList,
  format: (list) => list.map((i) => ({ ...i, value: i._id })),
  loading,
};
const { cateList, getCateList } = useClassifyList(options, false);
const selectedCates = ref([]);
const clickedCategories = ref([]);

// 从classify-list-side合并过来的变量
const treeRef = ref();
const treeExpandMap = ref({});
const treeData = ref([]);
const selections = useVModel(props, "modelValue", emits, { defaultValue: [] });
const selectMap = computed(() => {
  return selectedCates.value.reduce((acc, id) => {
    acc[id] = true;
    return acc;
  }, {});
});

const debug = ref(true); // 添加调试模式标志

// 添加一个父子关系映射
const parentNodeMap = ref({});

// 查找节点的所有父节点ID
function findAllParentIds(nodeId) {
  const parentIds = [];
  let currentId = nodeId;

  while (parentNodeMap.value[currentId]) {
    const parentId = parentNodeMap.value[currentId];
    parentIds.push(parentId);
    currentId = parentId;
  }

  return parentIds;
}

function close() {
  emits("close");
}

function onChange(data) {
  if (current.value._id !== data._id) {
    // 只设置当前高亮项，不再自动添加到选中列表
    current.value = { ...data };
  }
}

function dialogSubmit() {
  // 优化输出信息
  console.log(
    "提交选择的分类:",
    clickedCategories.value.map((c) => ({
      label: c.label,
      id: c._id,
      level: c.level || 0,
    }))
  );

  emits("change", {
    categories: clickedCategories.value,
  });
  close();
}

// 初始化选中状态
function initializeSelection() {
  if (props.initialSelectedCategories && props.initialSelectedCategories.length > 0) {
    console.log("初始化选中分类:", props.initialSelectedCategories);

    // 清空当前选中状态
    clickedCategories.value = [];
    selectedCates.value = [];

    // 收集所有需要选中的ID（包括父分类ID）
    const allIdsToSelect = [];

    // 根据初始数据设置选中状态
    props.initialSelectedCategories.forEach((category) => {
      const id = category._id || category.id;
      if (id) {
        // 查找分类数据
        const existingCategory = cateList.value.find((item) => item._id === id);
        if (existingCategory) {
          // 添加当前分类ID到选中列表
          allIdsToSelect.push(id);

          // 添加到已点击分类列表
          const categoryToAdd = {
            _id: id,
            label: existingCategory.label || category.label,
            childrenIds: existingCategory.childrenIds,
            level: existingCategory.level || category.level || 0,
          };

          if (!clickedCategories.value.some((item) => item._id === id)) {
            clickedCategories.value.push(categoryToAdd);
            console.log("回显添加分类:", categoryToAdd.label);
          }

          // 收集所有父级ID
          if (parentNodeMap.value[id]) {
            const parentIds = findAllParentIds(id);
            allIdsToSelect.push(...parentIds);

            parentIds.forEach((parentId) => {
              const parentCate = cateList.value.find((item) => item._id === parentId);
              if (parentCate && !clickedCategories.value.some((item) => item._id === parentId)) {
                clickedCategories.value.push({
                  _id: parentId,
                  label: parentCate.label,
                  childrenIds: parentCate.childrenIds,
                  level: parentCate.level || 0,
                });
                console.log("回显添加父分类:", parentCate.label);
              }
            });
          }
        }
      }
    });

    // 统一更新选中状态数组，确保UI更新
    selectedCates.value = [...new Set(allIdsToSelect)];
    console.log("最终选中的ID列表:", selectedCates.value);
  }
}

// 在onNodeClick函数后添加一个函数来展开所有父节点
function expandParentNodes() {
  // 获取所有需要展开的父节点ID
  const idsToExpand = new Set();

  selectedCates.value.forEach((id) => {
    // 获取当前ID的所有父ID
    const parentIds = findAllParentIds(id);
    parentIds.forEach((pid) => idsToExpand.add(pid));
  });

  // 展开节点
  nextTick(() => {
    const nodesMap = treeRef.value?.store?.nodesMap;
    if (nodesMap) {
      idsToExpand.forEach((id) => {
        if (nodesMap[id]) {
          nodesMap[id].expanded = true;
        }
      });

      // 更新展开状态映射
      treeExpandMap.value = Object.keys(nodesMap).reduce((acc, key) => {
        const node = nodesMap[key];
        if (node.expanded) {
          acc[key] = node.expanded;
        }
        return acc;
      }, {});
    }
  });
}

// 从classify-list-side合并的方法
function toggle(data) {
  const selected = selectMap.value[data.value];
  const ids = [data.value];

  if (Array.isArray(data.childrenIds)) {
    // 等同于classify-list-side中的props.link为true
    ids.push(...data.childrenIds);
  }

  if (selected) {
    // 取消选中时，从selectedCates和clickedCategories中移除
    selectedCates.value = selectedCates.value.filter((i) => !ids.includes(i));
    // 从clickedCategories中移除对应的分类
    clickedCategories.value = clickedCategories.value.filter((item) => !ids.includes(item._id));
    console.log("取消选择:", data.label, "ID:", data.value, "子分类IDs:", data.childrenIds);
    onCateChange(ids, "cancel");
  } else {
    // 选中时，添加到selectedCates和clickedCategories
    selectedCates.value = [...selectedCates.value, ...ids];

    // 查找并添加所有父级分类
    const parentIds = findAllParentIds(data.value);
    console.log("父级分类IDs:", parentIds);

    if (parentIds.length > 0) {
      // 将父级ID添加到已选ID列表
      selectedCates.value = [...new Set([...selectedCates.value, ...parentIds])];

      // 将父级分类添加到clickedCategories
      parentIds.forEach((parentId) => {
        const parentItem = cateList.value.find((item) => item._id === parentId);
        if (parentItem && !clickedCategories.value.some((item) => item._id === parentId)) {
          const newParentItem = {
            label: parentItem.label,
            _id: parentItem._id,
            childrenIds: parentItem.childrenIds,
            level: parentItem.level,
          };
          clickedCategories.value.push(newParentItem);
          console.log("添加父级分类:", newParentItem.label, "ID:", newParentItem._id);
        }
      });
    }

    // 将选中的分类添加到clickedCategories中
    const currentItem = { label: data.label, _id: data.value, childrenIds: data.childrenIds, level: data.level };
    if (!clickedCategories.value.some((item) => item._id === data.value)) {
      clickedCategories.value.push(currentItem);
    }

    // 如果有子分类ID且需要链接选择，也需要添加子分类
    if (Array.isArray(data.childrenIds) && data.childrenIds.length > 0) {
      data.childrenIds.forEach((childId) => {
        const childItem = cateList.value.find((item) => item._id === childId);
        if (childItem && !clickedCategories.value.some((item) => item._id === childId)) {
          const newChildItem = {
            label: childItem.label,
            _id: childItem._id,
            childrenIds: childItem.childrenIds,
            level: childItem.level,
          };
          clickedCategories.value.push(newChildItem);
        }
      });
    }

    onCateChange(ids, "add");
  }
}

function onNodeClick(data, node) {
  if (data._id !== current.value._id) {
    onChange({ label: data.label, _id: data._id, childrenIds: data.childrenIds, level: data.level });
  }

  const nodesMap = treeRef.value.store.nodesMap;
  if (!nodesMap[data._id]) return;

  if (data.children && data.children.length) {
    // 展开/折叠有子节点的分类
    nodesMap[data._id].expanded = !nodesMap[data._id].expanded;
    treeExpandMap.value = Object.keys(nodesMap).reduce((acc, key) => {
      const node = nodesMap[key];
      if (node.expanded) {
        acc[key] = node.expanded;
      }
      return acc;
    }, {});
  }
}

async function onCateChange(cateIds, type) {
  if (type === "add") {
    const newIds = cateIds.filter((i) => !cateProjectIdCache.has(i));
    if (newIds.length) {
      loading.value = true;
      const { data } = await getProjectListByCateIds({ corpId: localStorage.getItem("corpId"), cateIds: newIds });
      if (Array.isArray(data.data)) {
        newIds.forEach((cateId) => {
          const projects = data.data.filter((i) => Array.isArray(i.projectCateIdGroup) && i.projectCateIdGroup.includes(cateId));
          cateProjectIdCache.set(cateId, projects);
        });
      }
      loading.value = false;
    }
  }
}

watchDebounced(
  () => cateList.value,
  async (n) => {
    treeExpandMap.value = {};
    const tree = getCateTreeData(n);

    // 构建父子关系映射
    parentNodeMap.value = {};
    const buildParentMap = (nodes, parentId = null) => {
      if (!nodes || !nodes.length) return;

      nodes.forEach((node) => {
        if (parentId) {
          parentNodeMap.value[node._id] = parentId;
        }
        if (node.children && node.children.length) {
          buildParentMap(node.children, node._id);
        }
      });
    };

    buildParentMap(tree);

    treeData.value = tree;
    if (treeData.value.length === 0) {
      onChange({});
      return;
    }

    const currentNode = n.find((i) => i._id === current.value._id && i._id) || treeData.value[0];
    onChange({ label: currentNode.label, _id: currentNode._id, childrenIds: currentNode.childrenIds });

    await nextTick();

    // 分类数据加载完成后，初始化选中状态
    if (props.visible && props.initialSelectedCategories && props.initialSelectedCategories.length > 0) {
      initializeSelection();
      // 添加延迟确保树渲染完成后再展开节点
      setTimeout(() => {
        expandParentNodes();
      }, 100);
    }
  },
  { immediate: true }
);

watch(
  () => props.visible,
  async (n) => {
    if (!n) return;

    // 对话框打开时，如果分类数据为空则加载数据
    if (cateList.value.length === 0 && !loading.value) {
      await getCateList();
      // 数据加载完后再初始化选中状态
      if (props.initialSelectedCategories && props.initialSelectedCategories.length > 0) {
        nextTick(() => {
          initializeSelection();
          expandParentNodes(); // 展开父节点以显示选中的子节点
        });
      }
    } else if (props.initialSelectedCategories && props.initialSelectedCategories.length > 0) {
      // 如果已有分类数据且有初始选中项，直接初始化选中状态
      nextTick(() => {
        initializeSelection();
        expandParentNodes(); // 展开父节点以显示选中的子节点
      });
    } else {
      // 无初始选中项时，清空选择状态
     
      // clickedCategories.value = [];
      // selectedCates.value = [];
    }
  }
);

defineExpose({
  clear: () => {
    clickedCategories.value = [];
    selectedCates.value = [];
    current.value = {};
  },
});
</script>

<style lang="scss" scoped>
:deep(.el-tree-node__expand-icon) {
  display: none;
}

:deep(.el-tree-node__content) {
  height: auto;
  padding-left: 0 !important;
}
</style>
