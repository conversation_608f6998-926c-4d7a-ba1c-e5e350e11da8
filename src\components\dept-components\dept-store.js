import { defineStore } from "pinia";
import { getDeptList } from "@/api/dept-manage";
import { getProjectIntentList } from "@/api/benefitManagement";
import { ElMessage } from "element-plus";

export const useDeptStore = defineStore("deptStore", {
  state: () => ({
    deptList: [],
    loading: false,
    loaded: false,
  }),

  getters: {
    hasDeptData: (state) => state.loaded && state.deptList.length > 0,
  },

  actions: {
    // 获取科室列表
    async fetchDeptList() {
      // 如果已经加载过数据且有数据，则不再重复请求
      if (this.loaded && this.deptList.length > 0) {
        return this.deptList;
      }

      try {
        this.loading = true;
        const { data, success } = await getDeptList();
        if (success && data && Array.isArray(data.list)) {
          this.deptList = data.list.map((item) => ({
            id: item.deptId,
            name: item.deptName,
          }));
        } else {
          this.deptList = [];
          ElMessage.error("获取科室列表失败");
        }
      } catch (error) {
        console.error("获取科室列表失败:", error);
        ElMessage.error("获取科室列表失败");
        this.deptList = [];
      } finally {
        this.loading = false;
        this.loaded = true;
      }

      return this.deptList;
    },

    // 根据科室ID获取项目ID列表
    async fetchProjectIdsByDeptId(deptId) {
      if (!deptId) return [];
      try {
        const { data, success } = await getProjectIntentList({ deptId, pageNum: 1, pageSize: 1000 });

        if (success && data.data && Array.isArray(data.data.list)) {
          return data.data.list.map((item) => item._id);
        } else {
          return [];
        }
      } catch (error) {
        return [];
      }
    },

    // 根据ID获取科室名称
    getDeptNameById(id) {
      if (!id || !this.deptList.length) return "";

      const dept = this.deptList.find((item) => String(item.id) === String(id));
      return dept ? dept.name : "";
    },

    // 批量获取科室名称
    getDeptNamesByIds(ids, separator = "、") {
      if (!ids || !this.deptList.length) return "";

      // 确保 ids 是数组
      const idArray = Array.isArray(ids) ? ids : [ids];

      const uniqueIds = [...new Set(idArray)];
      const names = uniqueIds.map((id) => this.getDeptNameById(id)).filter(Boolean); // 过滤空名称

      return names.join(separator);
    },
  },
});
