<template>
  <my-layout v-loading="pedding" bg-fff :radius="8">
    <template v-if="memberId && (!id || (id && form._id === id))">
      <layout-item>
        <div font-18 font-semibold p-15>{{ title }}</div>
      </layout-item>
      <layout-main>
        <div px-15>
          <el-form :label-width="110" label-suffix="：">
            <el-row>
              <el-col v-for="item in formItems" :key="item.prop" v-bind="item.col">
                <el-form-item :label="item.label" :class="item.required ? 'is-required' : ''">
                  <el-input v-if="item.type === 'input'" v-model="form[item.prop]" :placeholder="item.placeholder || ''" :maxlength="item.maxlength">
                    <template v-if="item.appendText" #append>{{ item.appendText }}</template>
                  </el-input>
                  <el-input v-else-if="item.type === 'inputAmount'" :model-value="form[item.prop]" :placeholder="item.placeholder || ''" :maxlength="item.maxlength" @update:model-value="changeAmount($event, item.prop)">
                    <template v-if="item.appendText" #append>{{ item.appendText }}</template>
                  </el-input>
                  <el-input v-else-if="item.type === 'textarea'" v-model="form[item.prop]" :placeholder="item.placeholder || ''" type="textarea" :maxlength="item.maxlength" :rows="4" resize="none" show-word-limit></el-input>
                  <el-select v-else-if="item.type === 'select'" v-model="form[item.prop]" class="w-full" :placeholder="item.placeholder || ''" @change="change($event, item.prop, item.options)">
                    <el-option v-for="option in item.options" :label="option.label" :value="option.value" />
                  </el-select>
                  <el-select v-else-if="item.type === 'remoteSelect'" v-model="form[item.prop]" filterable remote reserve-keyword class="w-full" :placeholder="item.placeholder || ''" :remote-method="item.remote" :loading="item.loading" @change="change($event, item.prop, item.options)">
                    <el-option v-for="option in item.options" :label="option.label" :value="option.value" />
                  </el-select>
                  <el-date-picker v-else-if="item.type === 'date'" v-model="form[item.prop]" class="w-full" :placeholder="item.placeholder || ''" format="YYYY-MM-DD" />
                  <!-- <ww-user v-else-if="item.type === 'wwuser'" :openid="item.value"></ww-user> -->
                  <span v-else>{{ item.value || "" }}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div font-14 color-666 class="mb-20px">
            相关材料上传：
            <span color-primary>支持上传30M以下图片</span>
          </div>
          <div flex flex-wrap>
            <div relative v-for="(item, idx) in images" :key="idx + '_file'" class="imageItem ml-15px mb-15px">
              <el-image class="w-100px h-100px" :src="item.href || item.base64" :initial-index="idx" v-loading="item.loading" :previewSrcList="previewSrcList" fit="cover" />
              <div class="removeImage" color-danger @click="removeImage(idx)">删除</div>
            </div>
            <el-button v-if="images.length < 3" class="w-100px h-100px ml-15px mb-15px text-28px" type="primary" :icon="CameraFilled" plain @click="chooseImage()"></el-button>
          </div>
          <input class="w-0 h-0 invisible" type="file" accept="image/*" ref="uploadRef" @change="handleSelect($event)" />
        </div>
      </layout-main>
      <layout-item>
        <div text-center common-shadow--r py-10>
          <el-button class="w-100px" plain @click="onClose()">取消</el-button>
          <el-button class="w-100px" :loading="loading" type="primary" @click="save()">保存</el-button>
        </div>
      </layout-item>
    </template>
    <layout-main v-else :scroll="false">
      <div h-full flex flex-col items-center justify-center>
        <empty-data title="暂无病历信息" />
      </div>
    </layout-main>
  </my-layout>
</template>
<script setup>
import { computed, ref, watch, onMounted } from "vue";
import { onBeforeRouteUpdate, useRoute } from "vue-router";
import { storeToRefs } from "pinia";
import { ElMessage, ElMessageBox } from "element-plus";
import { getRandomStr, file2Base64 } from "@/utils";
import { addMedicalRecord, getMedicalRecordById, updateMedicalRecord } from "@/api/member.js";
import { updateFile } from "@/api/uploadFIle.js";
import { tagsStore } from "@/store/tags";
import { memberStore } from "@/store/member";
import { teamStore } from "@/store/team";

import dayjs from "dayjs";
import medicalRecordStore from "@/store/medical-record";
import { createServiceRecord } from "@/utils/service";
import { CameraFilled } from "@element-plus/icons-vue";
import EmptyData from "@/components/empty-data.vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import WwUser from "@/components/ww-user/index.vue";

const route = useRoute();
const store = medicalRecordStore();
const { initDisease } = store;
const { corpInfo, memberInfo } = memberStore();
const { removeTag, subTagClosed, unsubTagClosed } = tagsStore();
const { outpatientFormItems, inhospitalFormItems } = storeToRefs(store);
const { currentTeam } = storeToRefs(teamStore());
const form = ref({});
const images = ref([]);
const formItems = computed(() => {
  const items = type.value === "outpatient" ? outpatientFormItems.value : inhospitalFormItems.value;
  return items.filter((i) => {
    if (i.showProps) {
      return form.value[i.showProps] === i.showValue;
    }
    return true;
  });
});
function change(val, prop, options) {
  const opt = options.find((i) => i.value === val);
  form.value[`${prop}Label`] = opt ? opt.label : "";
}

const type = ref("outpatient");
const memberId = ref("");
const id = ref("");
const title = computed(() => (type.value === "outpatient" ? "门诊就诊摘要" : "住院就诊摘要"));
const payload = computed(() => ({ id: id.value, memberId: memberId.value, type: type.value }));
const pedding = ref(false);
watch(
  route,
  (n) => {
    if (n.name === "MEDICALRECORD") {
      id.value = route.params.id || "";
      memberId.value = route.params.memberId || "";
      type.value = ["outpatient", "inhospital"].includes(route.params.type) ? route.params.type : "outpatient";
    }
  },
  { immediate: true }
);

function changeAmount(val, prop) {
  val = val.replace(/[^0-9.]/g, "");
  // 保留两位小数
  const parts = val.split(".");
  if (parts.length > 1) {
    val = `${Number(parts[0])}.${parts[1].slice(0, 2)}`;
  } else if (parts[0].length) {
    val = `${Number(parts[0])}`;
  }
  form.value[prop] = val;
}

watch(
  payload,
  (newValue, oldValue) => {
    if (!oldValue || Object.values(newValue).join() != Object.values(oldValue).join()) {
      if (payload.value.id && payload.value.memberId) getDetail();
      else {
        form.value = {};
        images.value = [];
      }
    }
  },
  { immediate: true }
);

const uploadRef = ref();
async function chooseImage() {
  uploadRef.value.click();
}
const previewSrcList = computed(() => images.value.map((i) => i.base64 || i.href));

async function handleSelect(e) {
  const [file] = e.target.files;
  e.target.value = "";
  if (!file) return;
  if (file.size > 31457280) {
    ElMessage.info("图片大小不能超过30MB");
    return;
  }
  const key = getRandomStr();
  const base64 = await file2Base64(file);
  images.value.push({ base64, name: file.name, key, file, loading: false });
}

async function getDetail() {
  pedding.value = true;
  const {
    success,
    message,
    data: { record },
  } = await getMedicalRecordById({ _id: id.value, corpId: localStorage.getItem("corpId"), memberId: memberId.value, medicalType: type.value });
  if (success) {
    if (record.diagnosisName && record.diagnosisNameLabel) {
      initDisease(record.diagnosisNameLabel, record.diagnosisName);
    }
    form.value = record;

    const files = typeof record.files === "string" ? record.files.split(",").filter(Boolean) : [];
    images.value = files.map((i) => ({ href: i }));
  } else {
    ElMessage.error(message);
    images.value = [];
  }
  pedding.value = false;
}

function verify() {
  for (const item of formItems.value) {
    const { prop, required, label, placeholder } = item;
    if (required && ((typeof form.value[prop] === "string" && form.value[prop].trim() === "") || (!form.value[prop] && typeof form.value[prop] !== "string"))) {
      ElMessage.info(placeholder);
      return;
    }
    if (prop === "amout" && !form.value[prop] >= 0) {
      ElMessage.info(`请输入有效的${label}`);
      return;
    }
  }
  return formItems.value.reduce((m, item) => {
    if (item.prop === "creator") {
      m[item.prop] = localStorage.getItem("userId");
    } else if (item.type === "date") {
      m[item.prop] = form.value[item.prop] ? dayjs(form.value[item.prop]).valueOf() : "";
    } else if (item.type === "inputAmount") {
      m[item.prop] = form.value[item.prop] || form.value[item.prop] === 0 ? Number(form.value[item.prop]).toString() : "";
    } else {
      m[item.prop] = form.value[item.prop] || item.value || "";
    }
    if (["select", "remoteSelect"].includes(item.type)) {
      m[`${item.prop}Label`] = form.value[`${item.prop}Label`] || "";
    }
    return m;
  }, {});
}

const loading = ref(false);

async function save() {
  const params = verify();
  if (!params) return;
  loading.value = true;
  try {
    await uploadImages();
    params.corpId = localStorage.getItem("corpId");
    params.memberId = memberId.value;
    params.medicalType = type.value;
    params.files = images.value.map((i) => i.href).join();
    if (id.value) {
      params._id = id.value;
      params.userId = localStorage.getItem("userId");
    } else {
      params.creator = localStorage.getItem("userId");
    }
    const { success, message } = await (id.value ? updateMedicalRecord : addMedicalRecord)(params);
    if (success) {
      ElMessage.success(message);
      removeTag(route.fullPath);
    } else {
      ElMessage.error(message);
    }
  } catch (e) {
    ElMessage.error(e);
  }
  loading.value = false;
}

function onClose() {
  removeTag(route.fullPath);
}
async function uploadImages() {
  const fileList = images.value.filter((i) => i.base64);
  if (fileList.length) {
    fileList.forEach((i) => (i.loading = true));
    try {
      const res = await Promise.all(fileList.map((i) => updateFile(i.name, i.file, "medicalRecord")));
      fileList.forEach((item, idx) => {
        const href = res[idx].download_url;
        const index = images.value.findIndex((i) => i.key === item.key);
        if (index >= 0) {
          images.value[index] = { href };
        }
      });
    } catch (e) {
      fileList.forEach((i) => (i.loading = false));
      return Promise.reject("上传相关资料失败");
    }
  }
}

async function removeImage(idx) {
  await ElMessageBox.confirm("确定删除该图片吗？", "提示", { type: "warning" });
  images.value.splice(idx, 1);
}

onMounted(() => subTagClosed(route.fullPath, reset));
onBeforeRouteUpdate((to, from) => {
  unsubTagClosed(from.fullPath);
  subTagClosed(to.fullPath, reset);
});
function reset() {
  id.value = "";
  memberId.value = "";
  form.value = {};
  images.value = [];
}
</script>
<style scpoed>
.imageItem:hover .removeImage {
  background: rgba(0, 0, 0, 0.5);
  opacity: 1;
}

.removeImage {
  position: absolute;
  bottom: 4px;
  left: 0;
  right: 0;
  padding: 4px;
  text-align: center;
  opacity: 0;
  font-size: 12px;
  cursor: pointer;
}

.el-date-editor.el-input.w-half {
  width: 50%;
}

.el-date-editor.el-input.w-full {
  width: 100%;
}
</style>