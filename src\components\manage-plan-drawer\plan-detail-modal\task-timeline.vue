<template>
  <el-timeline v-if="taskShowList.length > 0" class="ml-5px">
    <el-timeline-item v-for="(task, index) in taskShowList" :key="index" :icon="task.icon" color="#006eff"
      :timestamp="task.key" hollow placement="top" :hide-timestamp="true">
      <div class="flex items-center">
        <div class="flex-grow mr-10px w-0 font-semibold text-14px truncate">{{ task.key }} </div>
        <div class="flex flex-shrink-0 items-center cursor-pointer" main-color @click="expand(task.expandKey)">
          <div>{{ shrinkStatus[task.expandKey] ? '展开' : '收起' }}</div>
          <el-icon :size="16">
            <ArrowDown v-if="shrinkStatus[task.expandKey]" />
            <ArrowUp v-else />
          </el-icon>
        </div>
      </div>
      <div :class="shrinkStatus[task.expandKey] ? 'hidden' : ''">
        <div v-for="(item, index) in task.list" :key="item._id" class="mt-15px">
          <div class="task_cell pt-10px">
            <div v-if="item.eventType && ToDoEventType[item.eventType]" border-bottom class="px-15px mb-10px">
              <div grew-color>类型:</div>
              <el-scrollbar wrap-style="max-height:80px">
                <div class="py-10px">{{ ToDoEventType[item.eventType] }}</div>
              </el-scrollbar>
            </div>
            <div border-bottom class="px-15px">
              <div grew-color>任务内容:</div>
              <el-scrollbar wrap-style="max-height:80px">
                <div class="py-10px">{{ item.taskContent }}</div>
              </el-scrollbar>
            </div>
            <div border-bottom class="px-15px pb-15px">
              <div grew-color class="py-10px">向联系人发送:</div>
              <el-scrollbar v-if="item.sendContent" wrap-style="max-height:80px">
                <div class="pb-10px">
                  <span grew-color>[提醒]</span>
                  {{ item.sendContent }}
                </div>
              </el-scrollbar>
              <div v-if="!item.sendContent && !item.pannedEventSendFile.name" class="pb-10px">暂无发送内容</div>
              <div class="pb-10px"
                v-if="Object.keys(item.pannedEventSendFile).length !== 0 && item.pannedEventSendFile.name">
                <span grew-color>[{{ item.pannedEventSendFile.type === "article" ? "文章" : "问卷" }}]</span>
                <span main-color class="pointer">
                  {{ item.pannedEventSendFile.name }}
                </span>
              </div>
            </div>
            <div class="flex py-5px" items-center justify-end v-if="!noEdit">
              <div v-if="!planExecutStaus">
                <el-button text type="primary" @click="editTask(item)" :icon="Edit" class="px-5px">编辑</el-button>
                <el-button text type="danger" @click="deleteTask(item, index)" class="px-5px"
                  :icon="Delete">删除</el-button>
              </div>
              <div v-else class="px-10px flex">
                <div v-if="item.events && item.events.length > 0" class="flex">
                  <div class="pr-10px" v-if="item.events[0].eventStatus !== 'untreated'">
                    {{ dayjs(item.events[0].endTime).format("YYYY-MM-DD HH:mm") }}
                  </div>
                  <div>{{ PlanTaskStatusObj[item.events[0].eventStatus] }}</div>
                </div>
                <div v-else>
                  <div v-if="item.taskStatus === 'closed'">
                    <span v-if="planForm.endTime">
                      {{ dayjs(planForm.endTime).format("YYYY-MM-DD HH:mm") }}
                    </span>
                    已取消
                  </div>
                  <div v-else>
                    <el-button text type="primary" @click="editTask(item)" :icon="Edit" class="px-5px">编辑</el-button>
                    <el-button text type="danger" @click="deleteTask(item, index)" class="px-5px"
                      :icon="Delete">删除</el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-timeline-item>
    <el-timeline-item placement="top" color="#006eff" hollow v-if="taskShowList.length > 0"
      timestamp="结束"></el-timeline-item>
  </el-timeline>
  <empty-data v-else :top="50" title="暂无任务" padding="0 0 15px" text-top="10px" :image-width="120"></empty-data>
</template>
<script setup>
import { computed, h, ref } from "vue";
import { Delete, Edit } from "@element-plus/icons-vue";
import EmptyData from "@/components/empty-data.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { PlanTaskStatusObj, ToDoEventType } from "@/baseData";
import { taskStore } from "@/store/managePlan/task";
const { removeTask, createTask, updatePlanTask } = taskStore();
import dayjs from "dayjs";
const props = defineProps({
  taskList: {
    type: Array,
    default: [],
  },
  noEdit: {
    type: Boolean,
    default: false,
  },
  planExecutStaus: {
    type: String,
    default: "",
  },
  planExecutionTime: {
    type: String,
    default: "",
  },
  planForm: {
    type: Object,
    default: {},
  },
  customer: {
    type: Object,
    default: {},
  },
});
const taskRef = ref("");
const timeTypeList = [
  { label: "天", value: "day" },
  { label: "周", value: "week" },
  { label: "月", value: "month" },
  { label: "年", value: "year" },
];
let pId = "";
const $emit = defineEmits(["saveSuccess", "updateTaskList", "editTaskSuccess"]);
const taskShowList = computed(() => {
  if (!Array.isArray(props.taskList)) return [];
  const categorizedTasks = Object.entries(
    // 按照任务时间分类
    props.taskList.reduce((acc, task) => {
      let { taskTime, timeType } = task;
      let time = taskTime;
      if (timeType === "week") time = taskTime * 7;
      if (timeType === "month") time = taskTime * 30;
      if (timeType === "year") time = taskTime * 365;
      if (!acc[time]) {
        acc[time] = [];
      }
      acc[time].push(task);
      return acc;
    }, {})
  ).map(([key, value], idx) => ({
    expandKey: `_${idx}_${+new Date()}_${Math.ceil(Math.random() * 10000)}`,
    icon: getTimelineIcon(idx + 1, value),
    key: `计划开始后: ${(!value.timeType || value.timeType == "day") && value[0].taskTime == 0 ? "当天" : value[0].taskTime + timeTypeTransform(value[0].timeType)} ${props.planExecutionTime ? "(" + dayTransform(value) + ")" : ""}`,
    list: value,
  }));
  return categorizedTasks || [];
});
function dayTransform(value) {
  const { taskTime, timeType } = value[0];
  if (!timeType) {
    return dayjs(props.planExecutionTime).add(taskTime, "day").format("YYYY-MM-DD");
  } else {
    return dayjs(props.planExecutionTime).add(taskTime, timeType).format("YYYY-MM-DD");
  }
}
const minTime = ref(0);
function selectTimeType(val) {
  if (val === "day") {
    minTime.value = 0;
  } else if (val === "week" || val === "month" || val === "year") {
    minTime.value = 1;
  }
  newTaskTimeType.value = val;
  if (newtaskTime < minTime.value) {
    newtaskTime = minTime.value;
  }
}
function timeTypeTransform(timeType) {
  if (!timeType) return "天";
  if (timeType === "day") return "天";
  if (timeType === "week") return "周";
  if (timeType === "month") return "月";
  if (timeType === "year") return "年";
}
function addTask(id) {
  pId = id;
  taskRef.value.openDialog({ type: "add", planId: id, planExecutionTime: props.planExecutionTime });
}
async function deleteTask(item) {
  await ElMessageBox.confirm(`确认删除该任务吗？`, "提示");
  if (!props.planExecutStaus || props.planExecutStaus === "untreated") {
    let list = props.taskList.filter((e) => e.taskId !== item.taskId);
    $emit("saveSuccess", list);
    return;
  }
  const { _id } = props.planForm;
  const res = await removeTask(item._id, _id);
  if (res) {
    $emit("editTaskSuccess", "remove");
  }
}
async function editTask(item) {
  const params = props.taskList.filter((e) => e.taskId === item.taskId)[0];
  taskRef.value.openDialog({ type: "edit", planId: item.planId, params, planExecutionTime: props.planExecutionTime });
}
async function addTaskSuccess(params) {
  let list = props.taskList;
  if (!props.planExecutStaus || props.planExecutStaus === "untreated") {
    const index = list.findIndex((item) => item.taskId === params.taskId);
    if (index === -1) {
      list.push(params);
    } else {
      list[index] = params;
    }
    $emit("saveSuccess", list);
  } else {
    const { taskContent, sendContent, taskTime, timeType, pannedEventSendFile, _id } = params;
    if (!_id) {
      createPlanTask(params);
      return;
    }
    const query = {
      taskContent,
      sendContent,
      taskTime,
      timeType,
      pannedEventSendFile,
    };
    if (timeType) {
      query["planExecutionTime"] = dayjs(props.planExecutionTime).add(taskTime, timeType).valueOf();
    } else {
      query["planExecutionTime"] = dayjs(props.planExecutionTime).add(taskTime, "day").valueOf();
    }
    const res = await updatePlanTask(params._id, query);
    if (res) {
      $emit("editTaskSuccess");
    }
  }
}
// 新增任务
async function createPlanTask(params) {
  const { taskId, taskContent, sendContent, taskTime, timeType, pannedEventSendFile = {} } = params;
  const { customerId, executeTeamId, executeTeamName, executorUserId, planExecutionTime, planId, planName, _id } = props.planForm;
  const { externalUserId, name } = props.customer;
  let eventType = pannedEventSendFile.type === "questionnaire" ? "questionnaire" : "ContentReminder";
  const query = {
    corpId: localStorage.getItem("corpId"),
    taskId,
    planId,
    planName,
    creatorUserId: localStorage.getItem("userId"),
    customerId,
    customerName: name,
    sendContent,
    executeTeamId,
    executeTeamName,
    executorUserId,
    customerUserId: externalUserId,
    planExecutionTime,
    eventType,
    taskContent,
    taskTime,
    timeType,
    memberPlanId: _id,
    pannedEventSendFile,
  };
  await createTask(query);
  $emit("editTaskSuccess");
}
// 调整时间
let newtaskTime = ref(""),
  changeTaskTimeVisible = ref(false), //调整时间弹窗
  newTaskTimeType = ref("day"), //时间类型
  selectTask = {}; //选中的任务
async function changePlanTime(item) {
  changeTaskTimeVisible.value = true;
  selectTask = item;
  newtaskTime.value = item.taskTime;
  newTaskTimeType.value = item.timeType || "day";
}

async function changeTaskTimedialogSubmit() {
  if (selectTask.taskTime === newtaskTime.value && selectTask.timeType === newTaskTimeType.value) {
    changeTaskTimeVisible.value = false;
    return;
  }
  if (props.planExecutStaus === "untreated") {
    let list = props.taskList;
    const index = list.findIndex((i) => i.taskId === selectTask.taskId);
    list[index].taskTime = newtaskTime.value;
    list[index].timeType = newTaskTimeType.value;
    $emit("saveSuccess", list);
    changeTaskTimeVisible.value = false;
    return;
  }

  const res = await updatePlanTask(selectTask._id, {
    taskTime: newtaskTime.value,
    timeType: newTaskTimeType.value,
    planExecutionTime: props.planExecutionTime,
  });
  if (res) {
    changeTaskTimeVisible.value = false;
    $emit("editTaskSuccess");
  }
}

const shrinkStatus = ref({});
function expand(expandKey) {
  shrinkStatus.value[expandKey] = !shrinkStatus.value[expandKey]
}

function getTimelineIcon(idx, value) {
  return h('div', {
    style: {
      position: 'absolute',
      left: 'calc(50% - 8px)',
      top: 0,
      width: '16px',
      height: '16px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: 'rgb(0, 110, 255)',
      borderRadius: '50%',
    }
  }, [
    h(
      'div',
      {
        style: {
          lineHeight: '1em',
          color: '#fff',
          fontSize: '12px',
          fontStyle: 'normal',
          transform: `scale(${idx > 9 ? 0.75 : 1})`,
        }
      }, idx)

  ])
}

defineExpose({
  addTask,
});
</script>
<style lang="scss" scoped>
:deep(.el-timeline-item__timestamp) {
  font-weight: 600;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.9);
}
</style>
<style lang="scss">
[pt-30] {
  padding-top: 30px;
}

.task_cell {
  border: 1px solid #006eff;
  border-radius: 5px;
}


.el-timeline {
  padding-left: 0;
}

.el-timeline-item__tail {
  border-left-color: #006eff;
}
</style>