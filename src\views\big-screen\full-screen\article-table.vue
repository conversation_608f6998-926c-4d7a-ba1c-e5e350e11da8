<template>
  <el-table ref="tableRef" class="screen-el-table" :data="list" height="100%" v-bind="tableConfig">
    <el-table-column align="center" prop="rank" label="排名" width="70">
      <template #default="{ row: { rank } }">
        <div class="truncate text-white">{{ rank }}</div>
      </template>
    </el-table-column>
    <el-table-column prop="title" label="文章名称">
      <template #default="{ row: { title } }">
        <div class="truncate text-white">{{ title }}</div>
      </template>
    </el-table-column>
    <el-table-column prop="userName" label="浏览量" width="100">
      <template #default="{ row: { count } }">
        <div class="truncate text-white">{{ count }}</div>
      </template>
    </el-table-column>
  </el-table>
</template>
<script setup>
import { ref } from "vue";
import useBigScreenTable from "./use-big-screen-table";
import data from "../mock-data/article-rank-data.json";
import { getArticleListReadStatsApi } from "../api";

const tableRef = ref();
const { tableConfig } = useBigScreenTable(tableRef);

const list = ref([]);
getArticleListReadStats();
async function getArticleListReadStats() {
  list.value = await getArticleListReadStatsApi();
}
</script>
<style lang="scss" scoped>
:deep(.el-table__header .el-table__cell .cell) {
  white-space: nowrap;
}

:global(.el-table.screen-el-table .el-table__header tr) {
  background-color: rgba(13, 93, 171, 0.5);
}

:global(.el-table.screen-el-table .el-table__inner-wrapper::before) {
  display: none;
}

:deep(.el-table__cell) {
  background-color: transparent !important;
}
</style>
