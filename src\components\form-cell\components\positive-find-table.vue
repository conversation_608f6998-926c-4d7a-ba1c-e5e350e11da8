<template>
  <div v-if="isRead && list.length === 0"></div>
  <div v-else>
    <el-table ref="tableRef" stripe :data="list" :max-height="300" class="w-4/5">
      <el-table-column align="center" width="80" type="index" label="序号" />
      <el-table-column property="category" :min-width="100" label="阳性发现">
        <template #default="{ row: { category },$index }">
          <div v-if="isRead">{{ category }}</div>
          <template v-else>
            <el-input v-if="editCell === `category_${$index}`" v-model="text" ref="inputRef"
              @blur="handleEdit($index,'category')" />
            <div v-else class="min-h-1em" @click="edit(category,$index,'category')">{{ category }}</div>
          </template>
        </template>
      </el-table-column>
      <el-table-column property="opinion" :min-width="100" label="处理意见">
        <template #default="{ row: { opinion },$index }">
          <div v-if="isRead">{{ opinion }}</div>
          <template v-else>
            <el-input v-if="editCell === `opinion_${$index}`" v-model="text" ref="inputRef"
              @blur="handleEdit($index,'opinion')" />
            <div v-else class="min-h-1em" @click="edit(opinion,$index,'opinion')">{{ opinion }}</div>
          </template>
        </template>
      </el-table-column>
      <el-table-column v-if="!isRead" fixed="right" width="60" property="action" label="操作">
        <template #default="{ $index,row }">
          <el-icon class="text-red-400 cursor-pointer" @click="remove($index,row)">
            <DeleteFilled />
          </el-icon>
        </template>

      </el-table-column>
    </el-table>
  </div>
  <div v-if="!isRead" class="py-10px px-15px"><el-button type="primary" plain @click="add()">新增</el-button></div>
</template>
<script setup>
import { onMounted, inject } from 'vue';
import { ElMessageBox } from 'element-plus';
import { ref, nextTick } from 'vue';

const emits = defineEmits(['change'])
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  isRead: {
    type: Boolean,
    default: true
  },
  value: {
    type: Array,
    default: () => []
  }
})
const editCell = ref('')
const text = ref('')
const inputRef = ref(null)
const list = ref([])
const tableRef = ref()
const setLayoutCol = inject('set-form-template-layout-col')
onMounted(() => {
  if (typeof setLayoutCol === 'function') setLayoutCol(props.title, 24)
})

async function edit(str, index, type) {
  await new Promise(resolve => setTimeout(resolve, 0))
  editCell.value = `${type}_${index}`
  text.value = typeof str === 'string' ? str : ''
  await nextTick()
  inputRef.value.focus()
}

function handleEdit(index, type) {
  editCell.value = ''
  list.value[index][type] = text.value
  text.value = ''
  changeList()
}

async function remove(index, row) {
  if (row.category || row.opinion) {
    await ElMessageBox.confirm('确定删除吗', '提示', { type: 'warning' })
  }
  list.value.splice(index, 1)
  changeList()

}

async function add() {
  list.value.push({ category: '', opinion: '' })
  changeList()
  await nextTick()
  tableRef.value.setScrollTop(99999)
}

function changeList() {
  const value = list.value.filter(i => i.category || i.opinion).map(i => ({
    category: typeof i.category === 'string' ? i.category : '',
    opinion: typeof i.opinion === 'string' ? i.opinion : ''
  }))
  props.title && emits('change', { title: props.title, value })
}

onMounted(() => {
  list.value = Array.isArray(props.value) ? props.value : [];
})
</script>
<style lang="scss" scoped></style>
