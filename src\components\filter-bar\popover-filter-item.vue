<template>
  <el-popover placement="bottom-start" :offset="0" :width="width" trigger="click">
    <template #reference>
      <div class="flex-shrink-0 flex items-center text-14px h-40px pr-30px cursor-pointer"
        :class="clearable ? 'group':''">
        <div class="flex-shrink-0 pl-10px text-gray-500"> {{ label }}：</div>
        <div color-normal class="text-right mx-5px truncate">{{ valueStr}}</div>
        <el-icon class="flex-shrink-0 group-hover:hidden" :size="18">
          <CaretBottom />
        </el-icon>
        <el-icon class="flex-shrink-0 text-gray-500 hidden group-hover:block" :size="16" @click.stop="clear()">
          <Close />
        </el-icon>
      </div>
    </template>
    <slot name="header"></slot>
    <el-scrollbar v-if="scroll" wrap-style="max-height: 50vh">
      <slot></slot>
    </el-scrollbar>
    <template v-else>
      <slot></slot>
    </template>
  </el-popover>
</template>
<script setup>
import { computed } from 'vue';

const props = defineProps({
  clearable: { type: Boolean, default: false },
  label: { type: String, default: '' },
  text: { type: String, default: '' },
  width: { type: [Number, String], default: 280 },
  scroll: { type: Boolean, default: true },
})
const emits = defineEmits(['clear'])
const clear = () => emits('clear')

const valueStr = computed(() => props.text || '全部');
</script>
<style lang="scss" scoped></style>
