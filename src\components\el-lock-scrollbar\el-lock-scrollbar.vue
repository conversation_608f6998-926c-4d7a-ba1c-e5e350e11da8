<template>
  <el-scrollbar v-bind="attrs" :wrap-class="wrapClass" ref="scrollBarRef" @scroll="handleScroll">
    <slot></slot>
  </el-scrollbar>
</template>
<script setup>
import { computed, ref, toRefs } from 'vue';
import { watchDebounced } from '@vueuse/core';
import useElScrollbar from '@/hooks/useElScrollbar';

const emits = defineEmits(['scroll']);
const props = defineProps({ lock: { type: Boolean, default: false }, wrapClass: { type: String, default: '' } });
const { lock } = toRefs(props);

const wrapClass = computed(() => lock.value ? `${props.wrapClass} el-scrollbar__wrap--lock` : props.wrapClass)

const scrollBarRef = ref(null);
const { atBottom, atTop, handleScroll, scrollTop } = useElScrollbar(scrollBarRef, 0);
watchDebounced([atTop, atBottom, scrollTop], ([atTop, atBottom, scrollTop]) => {
  emits('scroll', { atTop, atBottom, scrollTop });
}, { debounce: 500 })


</script>
<style>
.el-scrollbar__wrap.el-scrollbar__wrap--lock {
  overflow: hidden;
}

.el-scrollbar__wrap.el-scrollbar__wrap--lock ~ .el-scrollbar__bar {
  display: none;
  pointer-events: none;
}
</style>
