<template>
  <div bg-fff common-shadow rounded-8>
    <div flex items-center justify-between p-15 font-16 border-bottom>
      <div font-semibold active-title-bar flex-shrink-0>团队客户数据统计</div>
      <div flex items-center>
        <el-date-picker v-model="dates" type="daterange"  :unlink-panels="true"
 range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD" :disabled-date="disabledDate" @calendar-change="calendarChange" @change="updateTagDate()" />
        <el-text v-for="tag in rangeTags" :key="tag.key" pointer class="ml-10px" :type="activeTag === tag.key ? 'primary' : ''" @click="toggleTag(tag.key)">
          {{ tag.label }}
        </el-text>
      </div>
    </div>
    <stats-card :currentTeam="props.currentTeam" :cardType="cardType" @onSelectCard="selectCard"></stats-card>
  </div>
  <div bg-fff common-shadow rounded-8>
    <line-chart class="mb-15px" :dates="dates" :currentTeam="props.currentTeam" :cardType="cardType" />
    <member-count-table class="mb-15px" @show="showMemberChart($event)" :currentTeam="props.currentTeam" :cardType="cardType" />
    <customer-trend-dialog :currentTeam="props.currentTeam" :disabledFn="useDisabledDate(31)" :visible="visible" @close="visible = false" :userId="userId" :cardType="cardType" />
  </div>
</template>
<script setup>
import { ref, watch } from "vue";
import dayjs from "dayjs";
import { activeTag, dates, rangeTags, toggleTag, updateTagDate } from "./useDateTag";
import CustomerTrendDialog from "./customer-trend-dialog";
import LineChart from "./line-chart";
import MemberCountTable from "./member-count-table";
import StatsCard from "./stats-card";
const props = defineProps({
  currentTeam: {
    type: Object,
    default: {},
  },
});

const cardType = ref("increaseCount");
const selectCard = (type) => {
  cardType.value = type;
};

const userId = ref("");
const visible = ref(false);
function showMemberChart(id) {
  visible.value = true;
  userId.value = id;
}
const { calendarChange, disabledDate } = useDisabledDate(31);
function useDisabledDate(days) {
  let startDate = "";
  function calendarChange([start, end]) {
    if (!end) {
      startDate = dayjs(start);
    }
  }
  function disabledDate(date) {
    if (startDate) {
      const minDate = startDate.subtract(days, "day");
      const maxDate = startDate.add(days, "day");
      return dayjs(date).isBefore(minDate) || dayjs(date).isAfter(maxDate);
    }
    return false;
  }
  return { calendarChange, disabledDate };
}

</script>


