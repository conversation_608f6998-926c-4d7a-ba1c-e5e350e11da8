<template>
  <div class="flex items-center">
    <el-select v-model="daysType" class="flex-shrink-0 w-140px" clearable placeholder="请选择" @change="change">
      <el-option label="请选择" value="" />
      <el-option label="未到院天数" value="arrive" />
      <el-option label="从未到院患者" value="noArrive" />
      <el-option label="未服务天数" value="service" />
      <el-option label="从未服务患者" value="noService" />
    </el-select>
    <template v-if="showDays">
      <el-input class="flex-shrink-0 w-60px mx-10px" model-value="大于" readonly />
      <el-input v-model="days" class="flex-shrink-0 w-80px" @blur="handleBlur()" />
    </template>

  </div>
</template>
<script setup>
import { computed } from 'vue';
import { useVModels } from '@vueuse/core';
const props = defineProps({
  daysType: { type: String, default: '' },
  days: { type: String, default: '' }
})
const emits = defineEmits(['update:daysType', 'update:days'])
const { daysType, days } = useVModels(props, emits);
const showDays = computed(() => ['arrive', 'service'].includes(daysType.value));

function change() {
  if (!showDays.value) {
    days.value = ''
  }
}

function handleBlur() {
  days.value = parseInt(days.value) > 0 ? parseInt(days.value) + '' : '';
}

function reset() {
  daysType.value = '';
  days.value = ''
}

</script>
<style lang="scss" scoped></style>
