<template>
  <Teleport v-if="visible" to="body">
    <div class="fixed inset-0 bg-black bg-opacity-30"></div>
    <div class="fixed bottom-0 right-0 left-0 h-[75vh] flex flex-col bg-white z-2 rounded-tl-md rounded-tr-md">
      <div class="flex-shrink-0 py-15px px-15px pb-10px text-center text-16px flex items-center justify-center text-blue-500 relative border-b border-b-gray-200">
        <img class="w-24px h-24px mr-5px" src="@/assets/icons/logo.png" />
        <span>AI智能回复</span>
        <el-icon class="absolute right-15px top-15px text-16px text-black" @click="close()">
          <CloseBold />
        </el-icon>
      </div>
      <div class="flex-grow relative">
        <div class="inset-0 absolute">
          <el-scrollbar ref="scrollRef">
            <div class="px-15px pt-15px">
              <img src="@/assets/hand.png" class="w-24px h-24px mr-5px inline-block transform -translate-y-2px" alt="" />
              <span class="text-16px font-semibold leading-24px inline-block">您好！快来体验一下吧！</span>
            </div>
            <el-input v-model="allWords" type="textarea" :autosize="true" :readonly="isActive" />
          </el-scrollbar>
        </div>
      </div>
      <div class="flex-shrink-0 p-15px">
        <div class="flex justify-end" v-if="allWords">
          <!-- <div class="text-12px text-gray-400 mb-10px">可将文本内容发送至聊天框内</div> -->
          <el-button type="text" plain @click="send">发送</el-button>
        </div>
        <div class="bg-blue-500 text-white text-14px text-center py-12px rounded" :class="isActive ? 'animate-pulse' : ''" @click="generate()">
          {{ isActive ? "正在生成...." : "生成智能回复" }}
        </div>
      </div>
    </div>
    <wait-loading v-if="loading" class="z-10" text="正在思考" />
  </Teleport>
</template>
<script setup>
import { nextTick, onMounted, ref, watch } from "vue";
import { useIntervalFn } from "@vueuse/core";
import { getRecommendResult, getReplyChatRecord, batchStorageSession, getDocumentList, getModalTaskResult } from "@/api/sessionArchive";
import { ElMessage } from "element-plus";
import waitLoading from "./wait-loading.vue";
import { sendChatMessage, sendChatMessagePromise } from "@/utils/jssdk.js";
import JSEncrypt from "jsencrypt";
import { load } from "cheerio";
const emits = defineEmits(["close"]);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  externalUserId: {
    type: String,
    default: "",
  },
  publicKeys: {
    type: Array,
    default: () => [],
  },
});
const scrollRef = ref(null);
const index = ref(0);
// const words = ref("");
const allWords = ref("");
const loading = ref(false);
onMounted(async () => {
  await getDocumentkbId();
});
let kbId = "",
  isActive = ref(false);
// const { pause, resume, isActive } = useIntervalFn(
//   async () => {
//     if (index.value < allWords.value.length) {
//       words.value += allWords.value[index.value];
//       index.value++;
//       scrollRef.value.setScrollTop(99999);
//     } else {
//       pause();
//       await nextTick();
//       scrollRef.value.setScrollTop(99999);
//     }
//   },
//   100,
//   { immediate: false }
// );

function close() {
  emits("close");
}

// 获取知识库ID
async function getDocumentkbId() {
  const { data, success } = await getDocumentList();
  if (success && data.data) {
    kbId = data.data[0].kb_id;
  }
}

async function generate() {
  // if (isActive.value) pause();
  // else {
  //   words.value = "";
  //   index.value = 0;
  //   loading.value = true;
  //   await getChatUserRecord();
  //   loading.value = false;
  //   // resume();
  // }
  // words.value = "";
  allWords.value = "";
  index.value = 0;
  loading.value = true;
  await getChatUserRecord();
  loading.value = false;
}
watch(
  () => props.visible,
  async (val) => {
    if (!val && isActive.value) {
      allWords.value = "";
      // words.value = "";
      index.value = 0;

      // pause();
    }
  }
);
async function getRecommend(msgList) {
  let { data } = await getModalTaskResult({
    magList: msgList,
    kbId,
  });
  isActive.value = false;
  if (data.response_data) {
    allWords.value = data.response_data;
  }
}
async function getChatUserRecord() {
  if (!kbId) {
    ElMessage.error("未获取到知识库ID");
    return;
  }
  isActive.value = true;
  // words.value = "";
  // allWords.value = "";
  index.value = 0;
  // 先合并会话存档
  await batchStorageSession();
  const { success, data } = await getReplyChatRecord({ customerUserId: props.externalUserId, memberUserId: localStorage.getItem("userId") });
  if (success && data.data.length > 0) {
    const msgList = data.data.map((item) => {
      return {
        msgid: item.msgid,
        encrypt_info: {
          secret_key: RSA_Decrypt(item),
        },
      };
    });
    console.log(msgList);
    // 倒叙
    // msgList.reverse();
    await getRecommend(msgList);
  } else {
    isActive.value = false;
    ElMessage.error("未获取到最新聊天记录");
  }
}
// 解密操作：先 Base64 解码，再用私钥解密
function RSA_Decrypt(item) {
  // Base64 解码
  let key = props.publicKeys.find((i) => i.publicKeyVersion === item.publicKeyVersion);
  const privateKey = key ? key.privateKey : "";
  // 使用 RSA 私钥解密
  let decrypt = new JSEncrypt();
  decrypt.setPrivateKey(privateKey);
  return decrypt.decrypt(item.encryptedSecretKey);
}
function send() {
  sendChatMessage({ type: "text", desc: allWords.value });
}
</script>
<style lang="scss" scoped>
:deep(.el-textarea__inner) {
  box-shadow: none;
}
</style>
