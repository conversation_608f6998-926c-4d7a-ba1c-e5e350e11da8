import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import path from "path";
import WindiCSS from "vite-plugin-windicss";
import { createSvgIconsPlugin } from "vite-plugin-svg-icons";
import { resolve } from "path";
import fs from "fs-extra";
export default defineConfig(({ command, mode }) => {
  const { VITE_TCB_PATH } = loadEnv(mode, process.cwd());
  return {
    base: `/${VITE_TCB_PATH}/`, //相对路径
    plugins: [
      vue(),
      WindiCSS(),
      createSvgIconsPlugin({
        // 指定需要缓存的图标文件夹
        iconDirs: [path.resolve(process.cwd(), "src/assets/svg-icons")],
        // 指定symbolId格式
        symbolId: "icon-[dir]-[name]",
      }),
    ],
    server: {
      host: "0.0.0.0",
      port: "5173",
    },
    build: {
      outDir: `package/${VITE_TCB_PATH}`,
      target: ["chrome52"],
      cssTarget: ["chrome52"],
      terserOptions: {
        compress: {
          drop_console: true, // 去除 console.log 等调试信息
        },
      },
      rollupOptions: {
        output: {
          manualChunks(id) {
            if (id.includes('node_modules/vue-router')) {
              return 'vue-router';
            }  
            if (id.includes("node_modules/vue")) {
              return "vue"; // 将 vue 包打包成单独的 chunk
            }
          },
        },
      },
      cssCodeSplit: true, // 分割 CSS，避免全局 CSS 被打包成一个大文件
      minify: "terser", // 使用 terser 进行压缩
      // 启用 brotli 压缩以减少文件大小
      brotliSize: true,
    },
    resolve: {
      //文件系统路径的别名, 绝对路径
      alias: {
        "@": path.resolve(__dirname, "src"),
      },
      extensions: [".js", ".ts", ".vue", ".json"],
    },
  };
});

async function copyOldFiles(name) {
  const srcDir = resolve(__dirname, name);
  const destDir = resolve(__dirname, `oldFile`);
  // 创建目标目录
  if (!fs.existsSync(srcDir)) {
    fs.mkdirSync(srcDir, { recursive: true });
  }
  if (!fs.existsSync(destDir)) {
    fs.mkdirSync(destDir, { recursive: true });
  }
  try {
    // 复制文件
    await fs.copy(srcDir, destDir);
  } catch (err) {}
}
