<template>
  <el-table stripe height="100%" empty-text="暂无数据" :data="list">
    <el-table-column prop="userId" label="员工">
      <template #default="{ row: { userId } }">
        <ww-user v-if="userId" :openid="userId"></ww-user>
      </template>
    </el-table-column>
    <el-table-column prop="count" label="添加客户数" />
    <!-- <el-table-column prop="todayCount" label="今日新增客户数" /> -->
  </el-table>
</template>
    <script setup>
import { ref, watch, computed } from "vue";
import WwUser from "@/components/ww-user/index.vue";
const props = defineProps({
  addCustomerList: { type: Array, default: () => [] },
});
const list = computed(() => {
  if (props.addCustomerList.length === 0) return [];
  let userIdCounts =
    props.addCustomerList.length > 0 &&
    props.addCustomerList.reduce((acc, item) => {
      const userId = item.userId;
      if (!acc[userId]) {
        acc[userId] = 0;
      }
      acc[userId]++;
      return acc;
    }, {});
  let arr = Object.entries(userIdCounts);
  arr = arr.map(([userId, count]) => {
    let todayCount = 0;
    todayCount = props.addCustomerList.filter((item) => {
      return item.userId === userId && new Date(item.createTime).toDateString() === new Date().toDateString();
    }).length;
    return { userId, count, todayCount };
  });
  if (arr) {
    return arr;
  } else {
    return [];
  }
});

async function exportExcel() {
  // 创建一个工作簿
  const workbook = utils.book_new();
  let sortedTableData = myTableData.value.map((row) => {
    let sortedRow = {};
    tabBarList.value.forEach((key) => {
      sortedRow[key] = row[key] || "";
    });
    return sortedRow;
  });
  const list = sortedTableData.map(Object.values);

  const worksheetData = [tabBarList.value, ...list];
  const worksheet = utils.aoa_to_sheet(worksheetData);
  // 将工作表添加到工作簿
  utils.book_append_sheet(workbook, worksheet, "Sheet1");
  // 将工作簿转换为 ArrayBuffer 对象
  const workbookBinary = write(workbook, { bookType: "xlsx", type: "binary" });
  const workbookBuffer = new ArrayBuffer(workbookBinary.length);
  const workbookView = new Uint8Array(workbookBuffer);
  for (let i = 0; i < workbookBinary.length; i++) {
    workbookView[i] = workbookBinary.charCodeAt(i) & 0xff;
  }
  // 创建 Blob 对象
  const workbookBlob = new Blob([workbookBuffer], { type: "application/octet-stream" });
  // 使用 file-saver 库保存 Blob 对象为 Excel 文件
  saveAs(workbookBlob, "客户档案.xlsx");
}

</script>
    