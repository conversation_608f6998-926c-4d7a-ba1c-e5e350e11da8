<template>
  <my-layout>
    <layout-main>
      <el-row>
        <el-col class="hidden-lg-and-up mb-15px" :lg="8" :span="24">
          <current-team-view
            :teamId="currentTeam.teamId"
            :currentTeam="currentTeam"
          ></current-team-view>
        </el-col>
        <el-col class="mb-15px" :lg="16" :span="24">
          <count-bar
            :showToday="showToday"
            @reload="reload()"
            @toggle="toggle()"
            :currentTeam="currentTeam"
          ></count-bar>
        </el-col>
        <el-col class="hidden-md-and-down mb-15px" :lg="8" :span="24">
          <current-team-view
            :teamId="currentTeam.teamId"
            :currentTeam="currentTeam"
          ></current-team-view>
        </el-col>
      </el-row>
      <today-todo-count
        v-show="showToday"
        class="mb-15px"
        :currentTeam="currentTeam"
        :list="list"
        :commonCount="commonCount"
      />
      <customer-stats :currentTeam="currentTeam" />
    </layout-main>
  </my-layout>
</template>
<script setup>
import { provide, ref, watch } from "vue";
import CountBar from "./components/count-bar.vue";
import CurrentTeamView from "./components/current-team";
import CustomerStats from "./components/customer-stats/customer-stats";
import TodayTodoCount from "./components/today-todo-count";
import MyLayout, { LayoutMain } from "@/components/layout";
import "element-plus/theme-chalk/display.css";
import dayjs from "dayjs";
import { useRoute, useRouter } from "vue-router";
import { memberStore } from "@/store/member";
import { tagsStore } from "@/store/tags";
import { teamStore } from "@/store/team";
import { statisticsUserEventsByTeamId } from "@/api/todo.js";
import { storeToRefs } from "pinia";
const { currentTeam } = storeToRefs(teamStore());
watch(currentTeam, (newItem, oldItem) => {
  if (newItem && oldItem &&  newItem.teamId !== oldItem.teamId) {
    const { memberInfo } = memberStore();
    const { userid, roleType } = memberInfo;
    const isTeamLeader =
      newItem &&
      newItem.memberLeaderList &&
      newItem.memberLeaderList.some((i) => i == userid);
    if (!isTeamLeader && roleType !== "superAdmin") {
      router.push({ name: "WORK" });
      removeTag("WORK");
    } else {
      getUserDataList();
    }
  }
});
const showToday = ref(false);
const router = useRouter();
const { removeTag } = tagsStore();
getUserDataList();

const updateTime = ref(ref(dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss")));
provide("updateTime", updateTime);
function toggle() {
  showToday.value = !showToday.value;
}
let list = ref([]);
let commonCount = ref(0);
async function getUserDataList() {
  let { data, success } = await statisticsUserEventsByTeamId(
    currentTeam.value.teamId
  );
  if (success) {
    list.value = data.data.filter((item) => item._id !== "");
    const commonEvents = data.data.filter((item) => item._id == "")[0];
    commonCount.value = commonEvents ? commonEvents["untreatedCount"] : 0;
  }
}
</script>
