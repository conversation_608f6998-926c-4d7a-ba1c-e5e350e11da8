<template>
  <div v-if="isRead">
    {{ diagnosisNameLabel }}
  </div>
  <el-select v-else :model-value="arrayValue" filterable multiple remote reserve-keyword class="w-200px"
    :placeholder="'请输入' + item.name" :remote-method="searchDisease" @update:model-value="change($event)">
    <el-option v-for="option in options" :label="option.label" :value="option.value" :key="option.value" />
  </el-select>
</template>
<script setup>
import { memberStore } from "@/store/member";
import { watchDebounced } from "@vueuse/core";
import { getDisease } from "@/api/knowledgeBase";
import { ref, computed, watch } from "vue";
const { corpInfo } = memberStore();
const $emit = defineEmits(["change"]);
const props = defineProps({
  form: {
    type: String,
    default: {},
  },
  item: {
    type: Object,
    default: {},
  },
  value: {
    type: [String, Array],
    default: "",
  },
  isRead: {
    type: Boolean,
    default: false,
  },
});
const diseaseName = ref("");
const diseases = ref([]);
const arrayValue = computed(() => {
  if (Array.isArray(props.value)) return props.value;
  if (typeof props.value === 'string' && props.value.trim() !== '') return [props.value.trim()];
  return [];
})
const options = computed(() => {
  const valueOptions = [];
  if (typeof props.value === 'string' && props.value.trim() !== '') {
    valueOptions.push({ label: props.value.trim(), value: props.value.trim() });
  }
  if (Array.isArray(props.value)) {
    props.value.forEach((item) => {
      valueOptions.push({ label: item, value: item });
    });
  }
  return [...valueOptions.filter(i => i.label), ...diseases.value.filter(i => !valueOptions.some(o => o.label === i.label))]
})

watchDebounced(
  diseaseName,
  async () => {
    const res = await getDisease(diseaseName.value);
    diseases.value = res.data && Array.isArray(res.data.data) ? res.data.data.map((i) => ({ label: i.diseaseName, value: i.diseaseName })) : [];
  },
  { debounce: 500, maxWait: 5 * 1000 }
);

function searchDisease(e) {
  if (e.trim()) {
    diseaseName.value = e.trim();
  }
}
const diagnosisNameLabel = computed(() => arrayValue.value.join("、"));

function change(value) {
  $emit("change", {
    title: props.item.title,
    value: value,
  });
}
</script>

<style></style>