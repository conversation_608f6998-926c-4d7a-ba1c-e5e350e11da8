<template>
  <my-layout bg-fff common-shadow>
    <layout-main>
      <div title-bar p-15>配置欢迎语</div>
      <div px-15>
        <el-form :model="form" :label-width="120" label-position="left">
          <el-row>
            <el-col :span="24">
              <el-form-item label="使用员工：" prop="member">
                <el-tag v-for="(userid, idx) in (form.memberList || [])" mr-5 :type="tagType[idx % 5]" :key="userid"
                  closable @close="removeMember(idx)">
                  <ww-user :openid="userid"></ww-user>
                </el-tag>
                <el-button type="primary" size="small" :icon="Plus" plain @click="selectMember">添加成员</el-button>
              </el-form-item>
            </el-col>
            <el-col :lg="16" :span="24">
              <el-form-item label="欢迎语内容：" prop="teamTroduce">
                <el-input type="textarea" :maxlength="150" :autosize="{ minRows: 6 }" placeholder="请输入欢迎语内容"
                  v-model="form.content"></el-input>
              </el-form-item>
            </el-col>
            <el-col :lg="12" :span="24">
              <el-form-item label="附件：" prop="attachments">
                <div w-full>
                  <div mb-15>
                    <el-popover placement="top" :width="400" trigger="hover">
                      <template #reference>
                        <el-button mr-15 type="primary" :icon="Plus">从本地上传</el-button>
                      </template>
                      <div flex justify-center>
                        <el-button mr-15 plain type="primary" :icon="Plus" @click="choose('image')">图片</el-button>
                        <el-button mr-15 plain type="primary" :icon="Plus" @click="choose('video')">视频</el-button>
                        <el-button plain type="primary" :icon="Plus" @click="showModal()">网页</el-button>
                      </div>
                    </el-popover>
                    <el-button type="primary" :icon="Plus">从素材库选择</el-button>
                  </div>
                  <div v-loading="uploadLoading" element-loading-text="正在上传...">
                    <div v-for="file in fileList" :key="file.key" flex mb-10 pb-10 items-center border-bottom>
                      <div v-if="file.loading || (!file.loading && (file.href || file.base64))" v-loading="file.loading"
                        class="image-cover">
                        <img w-full h-full v-if="file.type === 'image'" :src="file.href || file.base64" />
                        <video w-full h-full v-else-if="file.type === 'video'" :src="file.href || file.base64"></video>
                      </div>
                      <template v-if="file.type === 'link'">
                        <el-icon mr-10 flex-shrink-0 :size="20" color="#006eff">
                          <Link />
                        </el-icon>
                        <el-text mr-10 truncated w-0 flex-grow>
                          <el-link :href="file.name" target="_blank" type="primary">{{ file.name }}</el-link>
                        </el-text>
                      </template>
                      <el-text v-else mr-10 truncated w-0 flex-grow>{{ file.name }}</el-text>
                      <el-icon flex-shrink-0 class="pointer" :size="20" color="#F56C6C" @click="remove(file)">
                        <CloseBold />
                      </el-icon>
                    </div>
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <input type="file" hidden :accept="inputAccept" ref="uploadRef" @change="handleSelect($event)">
      <canvas hidden ref="canvasRef"></canvas>
    </layout-main>
    <layout-item>
      <div text-center p-15 common-shadow>
        <el-button class="w-100px" type="primary" @click="save"> 保存 </el-button>
      </div>
    </layout-item>
  </my-layout>
  <el-dialog v-model="visible" title="添加网页地址" draggable :width="560">
    <div p-15> <el-input placeholder="以http/https开头" v-model="link" /></div>
    <template #footer>
      <span text-center p-15>
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="addLink()"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref, onMounted, nextTick, computed, onActivated, onDeactivated, h } from 'vue';
import { tagsStore } from "@/store/tags";
import { updateFile } from "@/api/uploadFIle.js";
import { Plus, Link, CloseBold } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus';
import { useRoute, useRouter } from 'vue-router';
import { updateWelcome, getWords } from "@/api/knowledgeBase";
import { selectEnterpriseContact } from '@/utils/jssdk';
import { getRandomStr, file2Base64 } from '@/utils'
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import WwUser from '@/components/ww-user/index.vue';

const AcceptType = {
  image: 'image/*',
  video: 'video/*'
}

const route = useRoute();
const { removeTag } = tagsStore();

const tagType = ['', 'success', 'info', 'danger', 'warning'];
const form = ref({});
let id = '';
let hasLeft = false;
onMounted(() => {
  if (route.params.id) {
    id = route.params.id;
    getDetail(route.params.id);
  }
})
onActivated(() => {
  if (!hasLeft) return;
  hasLeft = false;
  if (route.params.id && route.params.id != id) {
    id = route.params.id;
    getDetail(route.params.id);
  } else if (id && !route.params.id) {
    id = '';
    fileList.value = [];
    form.value = {}
  }
})
onDeactivated(() => hasLeft = true)

async function getDetail(id) {
  const { success, data = {}, message } = await getWords(id, localStorage.getItem("corpId"));
  if (success) {
    const { data: words } = data;
    fileList.value = Array.isArray(words.files) ? words.files : [];
    form.value = words;
  } else {
    ElMessage.error(message)
  }
}

async function selectMember() {
  const res = await selectEnterpriseContact();
  const { selectedUserList = [] } = res;
  const list = [...(form.value.memberList || []), ...(selectedUserList.map(i => i.id))];
  form.value.memberList = Array.from(new Set(list));
}
function removeMember(idx) {
  form.value.memberList.splice(idx, 1)
}

const uploadLoading = ref(false);
async function save() {
  if (!form.value.memberList || form.value.memberList.length === 0) {
    ElMessage.info('请添加使用员工')
    return
  }
  if (form.value.content.trim() === '') {
    ElMessage.info('请输入欢迎语内容')
    return
  }
  const files = fileList.value.filter(i => i.type !== 'link' && i.file && !i.href);
  if (files.length) {
    uploadLoading.loading = true;
    await uploadFiles(files)
    uploadLoading.loading = false;
  }
  const params = {
    corpId: localStorage.getItem("corpId"),
    content: form.value.content,
    memberList: form.value.memberList || [],
    files: fileList.value.map(i => ({
      key: i.key,
      href: i.href,
      type: i.type,
      name: i.name
    }))
  }
  if (id) params._id = id;
  const { success, data, message } = await updateWelcome(params)
  if (success && data.code === 'REPEATMEMBER') {
    ElMessage({
      type: 'error',
      // duration: 0,
      message: h('span', null, data.members.map(i => h(WwUser, { openid: i }, ' ')), '已配置过欢迎语')
    })
  } else if (success) {
    ElMessage.success(message);
    removeTag(route.fullPath);
  } else {
    ElMessage.error(message);
  }
}
const fileList = ref([]);
const accpet = ref('');
const uploadRef = ref(null);
const inputAccept = computed(() => {
  return AcceptType[accpet.value] || ''
})

function remove(file) {
  fileList.value = fileList.value.filter(i => i.key !== file.key)
}

async function choose(type) {
  accpet.value = type;
  await nextTick()
  uploadRef.value.click()
}

async function handleSelect(e) {
  const [file] = e.target.files;
  e.target.value = '';
  if (!file) return;
  const key = getRandomStr();
  const data = { name: file.name, key, type: accpet.value, file, loading: true };
  fileList.value.push(data);
  const base64 = await file2Base64(file);
  const index = fileList.value.findIndex(i => i.key === key);
  fileList.value[index].base64 = base64;
  fileList.value[index].loading = false;
}

const visible = ref(false);
const link = ref('');
function showModal() {
  link.value = '';
  visible.value = true
}
function addLink() {
  const val = link.value.trim();
  if (val === '') {
    ElMessage.info('请输入网页地址')
  } else if (!/^[http|https]/.test(val)) {
    ElMessage.info('请输入以http/https开头的网页地址')
  } else {
    fileList.value.push({ name: val, key: getRandomStr(), type: 'link' })
    visible.value = false
  }
}

async function uploadFiles(list) {
  for (let item of list) {
    const res = await updateFile(item.name, item.file, 'welcome');
    const index = fileList.value.findIndex(i => i.key === item.key);
    if (index >= 0) fileList.value[index].href = res.download_url
  }
}
</script>
<style scoped>
[border-bottom] {
  border-bottom: 1px solid #eee;
}

.image-cover {
  margin-right: 10px;
  width: 100px;
  height: 60px;
}
</style>

  