/**
 * API配置文件
 * 管理不同企业ID与API地址的对应关系
 */

// 企业ID与API地址的映射表
export const API_MAPPING = {
  // 杭州某医院的企业ID及其专用API地址
  wpLgjyawAAJ69XjD39GMOXp2EsYHYb3w: "https://ykt.youcan365.com/hz/",
};

// 默认API地址（当没有特殊映射时使用）
export const DEFAULT_API_URL = import.meta.env.VITE_API_URL;

/**
 * 根据企业ID获取对应的API地址
 * @param {string} corpId - 企业ID
 * @returns {string} - 对应的API地址
 */
export const getApiUrlByCorpId = (corpId) => {
  if (!corpId) corpId = localStorage.getItem("corpId");
  if (corpId && API_MAPPING[corpId] && import.meta.env.VITE_NEED_LOGIN !== "true") {
    return API_MAPPING[corpId];
  }
  return DEFAULT_API_URL;
};
