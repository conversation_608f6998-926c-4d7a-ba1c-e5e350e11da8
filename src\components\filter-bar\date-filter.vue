<template>
  <base-filter :clearable="clearable" :label="label" :text="text" :width="width" @onClick="show()" @clear="clear">
    <el-date-picker v-model="modelValue" ref="datePickerRef" value-format="YYYY-MM-DD" :popper-options="option" />
  </base-filter>
</template>
<script setup>
import { computed, ref } from 'vue';
import { useVModels } from '@vueuse/core';

import baseFilter from './base-filter-item.vue';

const props = defineProps({
  modelValue: { type: Array, default: '' },
  label: { type: String, default: '' },
  text: { type: String, default: '' },
  width: { type: [Number, String], default: 280 },
})
const emits = defineEmits(['update:modelValue']);
const { modelValue } = useVModels(props, emits);
const datePickerRef = ref();
const option = {
  modifiers: [{ name: 'flip', options: { fallbackPlacements: ['bottom'], allowedAutoPlacements: ['bottom'] } }]
}
const clearable = computed(() => Boolean(modelValue.value))

function show() {
  datePickerRef.value.handleOpen()
}

function clear() {
  modelValue.value = ''
}

</script>
<style lang="scss" scoped>
:deep(.el-date-editor--date) {
  position: absolute;
  left: 0;
  right: 0;
  top: 100%;
  height: 0;
  opacity: 0;
  overflow: hidden;
}
</style>
