<template>
  <info-card>
    <el-form :label-width="labelWidth" :size="formSize" label-position="left" label-suffix="：">
      <el-row>
        <el-col :span="24">
          <el-form-item label="客户阶段">
            <div flex items-center class="w-full">
              <div flex-grow>
                <el-text class="mr-6" size="default">{{ currentStage }}</el-text>
                <el-icon color="#006eff" size="20" class="edit-btn" @click="editStage()">
                  <Edit />
                </el-icon>
              </div>
              <el-button type="primary" text plain @click="editAllInfo()">编辑</el-button>
            </div>

          </el-form-item>
        </el-col>
        <el-col v-if="tagCol" :span="24">
          <el-form-item :label="tagCol.label">
            <customer-tag v-for="tagId in (customer.tagIds || [])" :key="idx" :tag-id="tagId" />
            <el-button class="mb-5px" type="primary" size="small" :icon="Plus" plain @click="change">添加标签</el-button>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注">
            <div>
              <el-text class="mr-6" size="default" style="white-space: pre-wrap;">
                {{ customer.notes || customer.consultationContent || '' }}
              </el-text>
              <el-icon color="#006eff" size="20" class="edit-btn" @click="edit()">
                <Edit />
              </el-icon>
            </div>
          </el-form-item>
        </el-col>
        <el-col v-for="item in formatData" :key="item.key" :span="24" :class="item.key === 'tagList' ? 'hidden' : ''">
          <el-form-item :label="item.label">
            <template v-if="item.key === 'creator'">
              <ww-user v-if="customer.creator" :openid="customer.creator" type="userName"></ww-user>
              <el-text v-if="customer.creator" size="default">（内部员工） </el-text>
              <el-text v-else size="default">客户本人 </el-text>
            </template>
            <template v-else-if="item.key === 'reference'">
              <el-text v-if="customer.referenceType" size="default" class="mr-6px"> {{ customer.referenceType }}</el-text>
              <ww-user v-if="customer.referenceUserId" :openid="customer.referenceUserId" type="userName"></ww-user>
              <el-text v-else size="default">{{ customer.reference }}</el-text>
            </template>
            <el-text v-else size="default">{{ item.value }}</el-text>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </info-card>
</template>
<script setup>
import { computed, inject } from "vue";
import { storeToRefs } from 'pinia';
import dayjs from "dayjs";
import { Plus, Edit } from '@element-plus/icons-vue';
import { configStore } from '@/store/config';
import { memberStore } from "@/store/member";
import customerTag from "@/components/customer-tag/customer-tag.vue";
import InfoCard from "../info-card";
const labelWidth = inject('labelWidth');
const formSize = inject('formSize');
const customer = inject('customer');
const emits = defineEmits(["changeTag", 'edit', 'editAll', 'editStage']);

const { stageMap, customerField } = storeToRefs(configStore());
function change() {
  emits("changeTag")
}

function edit() {
  emits("edit", customer.value.notes)
}
const { corpInfo } = memberStore();
const { corpFileds: { viceInfo } } = corpInfo;

const order = {
  intentedProject: 4,
  customerSource: 5,
  reference: 6,
  myRecommend: 7,
  createTime: 8,
  creator: 9
}

let cols = Array.isArray(viceInfo) && viceInfo.map((item) => {
  return {
    value: '',
    label: item.name,
    key: item.title,
    ...(customerField.value[item.title] || {})
  }
}).sort((a, b) => {
  const preOrder = order[a.key] || 0;
  const nextOrder = order[b.key] || 0;
  return preOrder - nextOrder
})
const formatData = computed(() => {
  return cols.map(i => {
    const item = { ...i };
    if (i.key === 'customerSource') {
      item.value = Array.isArray(customer.value.customerSource) ? customer.value.customerSource.join('、') : '';
    } else if (i.key === 'reference') {
      item.value = `${customer.value.referenceType || ''} ${customer.value.reference || ''}`;
    } else if (i.key === 'createTime') {
      item.value = customer.value.createTime ? dayjs(customer.value.createTime).format('YYYY-MM-DD HH:mm') : ''
    } else {
      item.value = customer.value[i.valueKey || i.key] || ''
    }
    return item
  })
})
const tagCol = computed(() => formatData.value.find(i => i.key === 'tagList'))


const currentStage = computed(() => stageMap.value[customer.value.customerStage] || '')

function editStage() {
  emits('editStage')
}


function editAllInfo() {
  emits('editAll')
}
</script>
<style scoped>
.edit-btn {
  cursor: pointer;
  transform: translateY(5px);
}

.mx-6 {
  margin: 0 6px;
}

.mr-6 {
  margin-right: 6px;
}

.h-32 {
  height: 32px;
}
</style>
<style>
.linear-tag.el-tag--dark.el-tag--primary {
  background-image: linear-gradient(180deg,
      #4391f5 60%,
      #70c4e8);
  border-color: transparent !important;
}
</style>