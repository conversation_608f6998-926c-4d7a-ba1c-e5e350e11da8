<template>
  <div class="mt-5px p-5px">
    <el-row>
      <el-col :span="18" cla>
        <div class="b-fff mr-5px h-full min-h-550px flex flex-col">
          <div class="relative flex-grow">
            <div class="flex text-14px align-center">
              <div class="w-full h-full">
                <div :class="!(projectList.length == 0 && selectedPackage.length === 0) ? 'self-overflow w-full h-full' : 'w-full h-full overflow-auto'">
                  <div class="flex w-full justify-between">
                    <div v-for="item in rows" :class="item.key === 'operation' ? 'operationClass header-table-color p-10px flex text-14px align-center' : 'header-table-color p-10px flex text-14px align-center item'" :style="'min-width:' + item.width + 'px'">
                      {{ item.value }}
                    </div>
                  </div>
                  <div v-if="projectList.length !== 0 || selectedPackage.length !== 0">
                    <project-rows :projectList="projectList" :rows="rows" :staffList="staffList" operationType="project" @removePorject="removePorject" />
                    <div v-for="packageItem in selectedPackage" :key="packageItem._id">
                      <package-rows :packageItem="packageItem" :staffList="staffList" @removePackage="removePackage" @inputPackageDiscount="updatePackageProjectDiscount" />
                      <div class="py-15px px-10px flex align-center" v-if="packageItem.packageType === 'free'">
                        <span>
                          <span class="pr-5px">组合形式:</span>
                          <span v-if="packageItem.projects && packageItem.projectNumber">{{ packageItem.projects.length }} 选 {{ packageItem.projectNumber }}</span>
                        </span>
                        <span class="pl-15px">必选项 {{ packageItem.mustSelectedCount }}</span>
                        <span class="pl-15px">可选项: {{ packageItem.canSelectedCount }} / {{ packageItem.projectNumber - packageItem.mustSelectedCount }}</span>
                      </div>
                      <div style="background-color: #f4f4f5; width: fit-content">
                        <project-rows :projectList="packageItem.projects" :rows="rows" :staffList="staffList" operationType="package" :packageItem="packageItem" @selectedPackageProject="updatePackageProjectDiscount" />
                      </div>
                    </div>
                  </div>
                </div>
                <div class="w-full" v-if="projectList.length == 0 && selectedPackage.length === 0">
                  <empty-data :top="120" title="暂无数据" padding="0 0 15px" text-top="10px" :image-width="120"></empty-data>
                </div>
              </div>
            </div>
          </div>
          <div class="flex justify-end b-fff align-center h-40px sticky bottom-0" border-top>
            <div class="pr-20px">
              共:
              <span>{{ projectTotal }}</span>
              项
            </div>
            <div class="pr-20px">
              原价:
              <span>¥{{ originalPrice }}</span>
            </div>
            <div class="pr-20px">
              优惠:
              <span>¥{{ totalDiscountPrice }}</span>
            </div>
            <div>
              合计：
              <span class="text-[#FC8352]">¥ {{ projectTotalPrice }}</span>
            </div>
            <el-button class="w-100px ml-10px" :loading="billLoading" type="primary" @click="confirm()">开单</el-button>
            <el-button plain class="w-100px mr-10px" type="primary" @click="close()">取消</el-button>
          </div>
        </div>
      </el-col>
      <el-col :span="6" class="b-fff">
        <div class="b-fff">
          <el-tabs v-model="currentTab">
            <el-tab-pane name="project">
              <template #label>
                <span class="px-15px">项目</span>
              </template>
              <project-package @get-project="onGetProject" @show-project="showProjectDetail" ref="projectPackageRef" />
            </el-tab-pane>
            <el-tab-pane v-loading="projectLoading" lazy name="package">
              <template #label>
                <span class="px-15px">套餐</span>
              </template>
              <classify-seach-panel height="550px" placeholder="输入套餐名称搜索" :cate-method="getProjectPackageCateList" :search-method="getPackages" :list-method="getPackages">
                <template #searchItem="{ item }">
                  <package-item :item="item" :expandMap="packageExpandMap" :selectMap="selectedPackageMap" @expand="expandPackage" @select="selectPackage" @show="showPackageDetail" />
                </template>
                <template #selectItem="{ item }">
                  <package-item :item="item" :expandMap="packageExpandMap" :selectMap="selectedPackageMap" @expand="expandPackage" @select="selectPackage" @show="showPackageDetail" />
                </template>
              </classify-seach-panel>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-col>
    </el-row>
  </div>
  <package-detail-modal :data="packageDetail" :visible="detailVisible" :width="detailWidth" @close="closeDetailModal" />
  <project-detail-modal :data="currentProject" :visible="projectDetailVisible" :width="projectDetailWidth" @close="closeProjectDetailModal" />
</template>

<script setup>
import { computed, ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { ElMessage, ElMessageBox } from "element-plus";
import { addBillRecord, batchAddFeeRecord } from "@/api/bill-record";
import { getProjectPackageCateList, getProjectPackageList, addConsumeRecord } from "@/api/member";
import { getProjectList, getDeptProjectCate, getProjectCateGroup, getProjectAllCateGroup } from "@/api/project-manage";
import useModal from "@/hooks/useModal";
import { memberStore } from "@/store/member";
import { teamStore } from "@/store/team";
import { staffStore } from "@/store/staff";
import classifySeachPanel from "@/components/classify-list/classify-search-panel.vue";
import EmptyData from "@/components/empty-data.vue";
import packageItem from "./package-item.vue";
import packageDetailModal from "@/components/package-detail-modal/package-detail-modal.vue";
import projectDetailModal from "./project-detail-modal.vue";
import projectPackage from "./project-package.vue";
import packageRows from "./package-rows";
import projectRows from "./project-rows";
import { generateRandomString } from "@/utils";
import BigNumber from "bignumber.js";
import { updateConsultRecord, addConsultBillRecord } from "@/api/consult";
const projectPackageRef = ref();
const props = defineProps({
  dept: { type: Object, default: () => ({}) },
  staff: { type: Object, default: () => ({}) },
  customer: { type: Object, default: () => ({}) },
  feeList: { type: Array, default: () => [] },
  consultRecord: { type: Object, default: () => ({}) },
  projectTypeAmountList: { type: Array, default: () => [] },
  getMatchRes: { type: Function, default: new Function() },
});
const emits = defineEmits(["close", "toBill", "onProjectsChange"]);
const { currentTeamId, currentTeamName } = storeToRefs(teamStore());
const projectList = ref([]);
const { staffList } = storeToRefs(staffStore());
const currentTab = ref("project");
const { memberInfo } = storeToRefs(memberStore());
const packageExpandMap = ref({});
const packageDetail = ref({});
const selectedPackage = ref([]);
const billLoading = ref(false);
const selectedPackageMap = computed(() => {
  return selectedPackage.value.reduce((acc, item) => {
    acc[item._id] = true;
    return acc;
  }, {});
});
const { close: closeDetailModal, show: showDetailModal, visible: detailVisible, width: detailWidth } = useModal(800);
const { close: closeProjectDetailModal, show: showProjectDetailModal, visible: projectDetailVisible, width: projectDetailWidth } = useModal(560);
const projectLoading = ref(false);
const rows = [
  {
    key: "projectType",
    value: "类型",
    width: "140",
  },

  {
    key: "projectName",
    value: "项目名称",
    width: "170",
  },
  {
    key: "price",
    value: "单价",
    width: "100",
  },
  {
    key: "number",
    value: "数量",
    width: "70",
  },
  {
    key: "discount",
    value: "折扣",
    width: "150",
  },
  {
    key: "totalPrice",
    value: "总价",
    width: "110",
  },
  // {
  //   key: "billDeptId",
  //   value: "开单科室",
  //   width: "160",
  // },
  {
    key: "treatmentDept_id",
    value: "计划治疗科室",
    width: "160",
  },
  { key: "treatmentDoctorUserId", value: "计划治疗人", width: "160" },
  {
    key: "operation",
    value: "操作",
    width: "50",
  },
];
function onGetProject(data) {
  projectList.value = projectList.value.filter((i) => data.some((item) => item.checkedKey === i.checkedKey)); // 删除取消勾选的项目
  const existIds = projectList.value.map((i) => i.checkedKey);
  for (let project of data) {
    if (!existIds.includes(project.checkedKey)) {
      let item = { ...project, number: 1, totalPrice: project.price, discount: project.discount || 10 };
      if (!project.isDiscount) item.lowestDiscount = 10;
      if (project.depts && Array.isArray(project.depts) && project.depts.length === 1) item.treatmentDept_id = project.depts[0]._id;
      if (project.billDepts && Array.isArray(project.billDepts) && project.billDepts.length === 1) item.billDeptId = project.billDepts[0]._id;

      projectList.value.push(item);
    }
  }
}

const projectTotalPrice = computed(() => {
  let projectAllPrice = projectList.value.reduce((acc, item) => {
    return acc.plus(new BigNumber(item.totalPrice));
  }, new BigNumber(0));
  let packageAllPrice = selectedPackage.value.reduce((acc, item) => {
    return acc.plus(new BigNumber(item.packageTotalPrice));
  }, new BigNumber(0));
  return projectAllPrice.plus(packageAllPrice).toFixed(2);
});

const projectTotal = computed(() => {
  let count = projectList.value.length;
  selectedPackage.value.forEach((i) => {
    i.projects?.forEach((j) => {
      if (j.selected) {
        count++;
      }
    });
  });
  return count;
});

const originalPrice = computed(() => {
  let projectAllPrice = projectList.value.reduce((acc, item) => {
    return acc.plus(new BigNumber(item.price || 0).times(item.number || 0));
  }, new BigNumber(0));
  let packageAllPrice = selectedPackage.value.reduce((acc, item) => {
    return acc.plus(new BigNumber(item.packagePrice || 0));
  }, new BigNumber(0));
  return projectAllPrice.plus(packageAllPrice).toFixed(2);
});
const totalDiscountPrice = computed(() => {
  return new BigNumber(originalPrice.value).minus(new BigNumber(projectTotalPrice.value)).toFixed(2);
});

watch(
  () => selectedPackage.value,
  (n) => {
    n.forEach((item) => {
      item.mustSelectedCount = item.projects.filter((i) => i.required).length;
      item.selectedCount = item.projects.filter((i) => i.selected).length;
      item.canSelectedCount = item.projectNumber - item.selectedCount;
    });
  },
  { deep: true }
);
function removePorject(project) {
  projectList.value = projectList.value.filter((item) => item._id !== project._id);
  projectPackageRef.value.removePorject(project?.checkedKey);
}
function getTotal(project) {
  let total = new BigNumber(0);
  if (project.price && project.number) {
    total = new BigNumber(project.price).times(new BigNumber(project.number));
  }
  if (project.discount) {
    total = total.times(new BigNumber(project.discount)).dividedBy(new BigNumber(10));
  }
  if (project.isFree) {
    total = new BigNumber(0);
  }
  return total.toFixed(2);
}
async function updatePackageProjectDiscount(packageItem) {
  if (packageItem.canSelectedCount > 0) {
    packageItem.projects.forEach((item) => {
      item.discount = "";
      item.totalPrice = new BigNumber(item.price)
        .times(new BigNumber(item.number))
        .times(new BigNumber(item.discount || 10))
        .dividedBy(new BigNumber(10))
        .toFixed(2);
    });
  } else if (packageItem.canSelectedCount === 0) {
    const packageTotalPrice = new BigNumber(packageItem.packageTotalPrice);
    const selectProject = packageItem.projects.filter((i) => i.selected);
    const projectAllPrice = selectProject.reduce((acc, item) => {
      return acc.plus(new BigNumber(item.totalPrice));
    }, new BigNumber(0));
    const discount = packageTotalPrice.minus(projectAllPrice).dividedBy(projectAllPrice);
    for (let i in selectProject) {
      let item = selectProject[i];
      const totalPrice = new BigNumber(item.totalPrice).times(discount).plus(new BigNumber(item.totalPrice));
      item.totalPrice = totalPrice.toFixed(2);
      item.discount = totalPrice.dividedBy(new BigNumber(item.number)).dividedBy(new BigNumber(item.price)).times(new BigNumber(10)).toFixed(2);
      if (item.discount < item.lowestDiscount) {
        await judgeProjectDiscount(item, packageItem);
        return;
      }
    }
  }
}
// 判断项目最低折扣
async function judgeProjectDiscount(project, packageItem) {
  if (Number(project.discount) < Number(project.lowestDiscount)) {
    try {
      await ElMessageBox.alert(`${project.projectName}项目折扣不能低于最低折扣`, "提示", {
        confirmButtonText: "确定",
        type: "warning",
      });
      result(packageItem);
    } catch (error) {
      result(packageItem);
    }
  }
  function result(item) {
    item.discount = "";
    item.packageTotalPrice = item.packagePrice;
    item.projects.forEach((i) => {
      i.discount = i.realDiscount;
      i.totalPrice = getTotal(i);
      i.selected = i.required;
    });
  }
}

function getPackageProjects() {
  let arr = [];
  selectedPackage.value.forEach((item) => {
    item.projects.forEach((i) => {
      if (i.selected) {
        arr.push({
          ...i,
          packageName: item.name,
          packageId: item._id,
          packageType: item.packageType,
          packagePrice: item.packagePrice,
          hasValidDays: item.hasValidDays,
          validDays: item.validDays,
        });
      }
    });
  });
  return arr;
}
// 去开单
async function confirm() {
  const selectProjectList = [...projectList.value, ...getPackageProjects()];
  if (!selectProjectList.length) {
    ElMessage.warning("请先选择项目");
    return;
  }
  if (!props.dept._id) {
    ElMessage.warning("请先选择开单科室");
    return;
  }
  if (!props.staff.value) {
    ElMessage.warning("请先选择开单医生");
    return;
  }
  for (let i in selectProjectList) {
    let item = selectProjectList[i];
    if (!item.number || item.number <= 0) {
      ElMessage.warning(`请输入项目${item.projectName}的数量`);
      return;
    }
    if (!item.treatmentDept_id && item.createTreatmentOrder) {
      ElMessage.warning(`请选择项目${item.projectName}的治疗科室`);
      return;
    }
  }
  for (let i in selectedPackage.value) {
    let packageItem = selectedPackage.value[i];
    const selectProjectCount = packageItem.projects.filter((j) => j.selected).length;
    if (packageItem.projectNumber !== selectProjectCount) {
      ElMessage.warning(`${packageItem.name}还有${packageItem.projectNumber - selectProjectCount}项未选`);
      return;
    }
  }
  let isNoFreeProjectList = selectProjectList.filter((i) => !i.isFree);
  if (!props.feeList.length && isNoFreeProjectList.length !== 0) {
    ElMessage.warning("请先选择关联费用单");
    return;
  }
  let billType = "";
  if (props.feeList.length > 0) {
    billType = props.feeList[0].billType;
    if (!amountMatching(isNoFreeProjectList, billType)) {
      ElMessage.warning("关联的费用单与选择项目金额不一致");
      return;
    }
  }
  const res = props.getMatchRes();
  if (!res) return;
  const map = await getDeptProjectCateNames(
    selectProjectList.map((i) => i._id),
    props.dept._id
  );
  billLoading.value = true;
  const billTime = new Date().getTime();
  const consultBillId = generateRandomString();
  for (let i in selectProjectList) {
    let item = selectProjectList[i];
    const dept = item.depts.find((i) => i._id === item.treatmentDept_id);
    const treatmentDeptName = dept?.deptName || "";
    const treatmentDeptId = dept?.deptId || "";
    const billDeptId = props.dept._id || "";
    const billDeptName = props.dept.label || "";

    const { number, price, isFree, discount, treatmentDoctorUserId, _id, projectName, projectCateId, treatmentDept_id, packageName, packageId, packageType, packagePrice, hasValidDays, validDays, totalPrice, projectType, createTreatmentOrder } = item;
    let params = {
      billType,
      customerId: props.customer._id,
      customerName: props.customer.name,
      customerPhone1: props.customer.phone1,
      customerPhone2: props.customer.phone2,
      customerPhon3: props.customer.Phone3,
      customerPhone: props.customer.phone,
      introducerRecord: props.customer.introducerRecord,
      counselorRecord: props.customer.counselorRecord,
      customerProjects: props.customer.customerProjects,
      customerSource: props.customer.customerSource,
      customerNumber: props.customer.customerNumber,
      personResponsibles: props.customer.personResponsibles,
      belongTeamId: currentTeamId.value,
      belongTeamName: currentTeamName.value,
      deductedPrice: 0,
      treatmentDeptName,
      treatmentDeptId,
      usageCount: Number(number),
      restUsageCount: Number(number),
      treatmentDoctorUserId,
      projectId: _id,
      projectName,
      treatmentDept_id,
      packageName,
      packageId,
      packageType,
      projectCateId,
      packagePrice: Number(packagePrice),
      billdCreator: localStorage.getItem("userId"),
      hasValidDays,
      validDays,
      price: Number(price),
      discount: Number(discount),
      totalPrice: Number(totalPrice),
      consultId: props.consultRecord._id,
      consultStage: props.consultRecord.consultStage,
      consultSource: props.consultRecord.source,
      visitStatus: props.consultRecord.visitStatus,
      consultCreateTime: props.consultRecord.createTime,
      receptionDoctorUserId: props.consultRecord.receptionDoctorUserId,
      consultProjects: props.consultRecord.projects,
      receptionPersonUserId: props.consultRecord.receptionPersonUserId,
      triagePersonUserId: props.consultRecord.triagePersonUserId,
      counselorUserId: props.consultRecord.counselorUserId,
      introducerUserId: props.consultRecord.introducerUserId,
      billTime,
      projectType,
      isFree,
      billDeptId,
      billDeptName,
      billDoctorId: props.staff.value,
      createTreatmentOrder,
      consultBillId,
      projectCates: map.get(_id) || [],
    };
    await addBillRecord({ params });
  }
  // 更新开单状态
  await Promise.all([updateConsultRecordTradeAmount(), addCustomerConsumeRecord({ consultBillId }), handAddConsultBillRecord({ consultBillId, billType })]);
  await batchAddBillFeeRecord({ consultBillId });
  billLoading.value = false;
  // 更新 咨询记录的成交状态和成交金额
  ElMessage.success("开单成功");
  close();
  emits("toBill");
}

// 新增本次本次开单记录
async function handAddConsultBillRecord({ consultBillId, billType }) {
  await addConsultBillRecord({
    consumeAmount: Number(projectTotalPrice.value),
    customerId: props.customer._id,
    consultBillId,
    billType,
    consultId: props.consultRecord._id,
  });
}

// 金额匹配规则设置
function amountMatching(selectProjectList, billType) {
  if (billType === "门诊") {
    const feeProjectAmountArr = feeProjectAmount(selectProjectList);
    const flag = feeProjectAmountArr.every((i) => props.projectTypeAmountList.some((j) => i.projectType == j.projectType && i.totalPrice == j.typeFee));
    return flag && feeProjectAmountArr.length === props.projectTypeAmountList.length;
  } else {
    const totalTypeFee = props.projectTypeAmountList.reduce((sum, item) => {
      return sum.plus(new BigNumber(item.typeFee));
    }, new BigNumber(0));
    const totalPrice = selectProjectList.reduce((sum, item) => {
      return sum.plus(new BigNumber(item.totalPrice));
    }, new BigNumber(0));
    console.log(totalPrice.toNumber());
    return totalTypeFee.isEqualTo(totalPrice);
  }
}

function feeProjectAmount(list) {
  const summary = {};
  list.forEach((item) => {
    const { projectType, totalPrice } = item;
    const price = new BigNumber(totalPrice);
    if (summary[projectType]) {
      summary[projectType] = summary[projectType].plus(price);
    } else {
      summary[projectType] = price;
    }
  });
  return Object.entries(summary).map(([projectType, totalPrice]) => ({
    projectType,
    totalPrice: totalPrice.toNumber(),
  }));
}

async function updateConsultRecordTradeAmount() {
  await updateConsultRecord({
    id: props.consultRecord._id,
    params: {
      isBilled: true,
      tradeAmount: Number(projectTotalPrice.value),
    },
  });
}
// 更新客户消费情况
async function addCustomerConsumeRecord({ consultBillId }) {
  await addConsumeRecord({
    consumeAmount: Number(projectTotalPrice.value),
    customerId: props.customer._id,
    consultBillId,
  });
}

async function batchAddBillFeeRecord({ consultBillId }) {
  if (props.feeList.length === 0) return;

  const list = props.feeList.map((i) => {
    return {
      ...i,
      billStatus: "billed",
      consultId: props.consultRecord._id,
      memberId: props.customer._id,
      consultBillId,
      corpId: localStorage.getItem("corpId"),
    };
  });

  // 分批处理，每批50条记录
  const batchSize = 50;
  const batches = [];

  for (let i = 0; i < list.length; i += batchSize) {
    batches.push(list.slice(i, i + batchSize));
  }

  // 串行处理每个批次，避免并发过多导致接口压力
  for (const batch of batches) {
    await batchAddFeeRecord({ list: batch });
  }
}

function close() {
  projectList.value = [];
  selectedPackage.value = [];
  projectPackageRef.value && projectPackageRef.value.clear();
}

async function getPackages({ page, pageSize, cateIds: classIds, name }) {
  const { data } = await getProjectPackageList({ page, pageSize, classIds, name, enable: true, corpId: memberInfo.value.corpId });
  const list = Array.isArray(data.list) ? data.list : [];
  const total = data.total > 0 ? data.total : 0;
  return { list, total };
}
function expandPackage(item) {
  packageExpandMap.value[item._id] = !packageExpandMap.value[item._id];
}
async function selectPackage(item) {
  if (selectedPackageMap.value[item._id]) {
    selectedPackage.value = selectedPackage.value.filter((i) => i._id !== item._id);
  } else {
    const projects = await getProjects(item.projects.map((i) => i._id));
    selectedPackage.value.push({
      ...item,
      discount: item.discount || 10,
      packageTotalPrice: item.packagePrice,
      projects: item.projects.map((i) => {
        let project = projects.find((p) => p._id === i._id) || i;
        const { depts = [] } = project;
        const treatmentDept_id = depts.length === 1 ? depts[0]._id : "";
        project.number = i.number;
        project.required = i.required;
        project.discount = i.discount || 10;
        project.projectCateId = i.projectCateId;
        // console.log(project.price);
        // console.log(project.number);
        // console.log(project.discount);
        // console.log(new BigNumber(project.price).times(new BigNumber(project.number)).times(new BigNumber(project.discount)).dividedBy(new BigNumber(10)).toFixed(2));
        return {
          ...project,
          selected: project.required || item.packageType === "fixed",
          treatmentDept_id,
          realDiscount: i.discount,
          totalPrice: new BigNumber(project.price).times(new BigNumber(project.number)).times(new BigNumber(project.discount)).dividedBy(new BigNumber(10)).toFixed(2),
        };
      }),
    });
  }
}

function removePackage(item) {
  selectedPackage.value = selectedPackage.value.filter((i) => i._id !== item._id);
}
function showPackageDetail(detail) {
  packageDetail.value = detail;
  showDetailModal();
}
async function getProjects(ids) {
  projectLoading.value = true;
  const { data } = await getProjectList({ ids, page: 1, pageSize: ids.length, corpId: memberInfo.value.corpId, showDepts: true });
  const list = Array.isArray(data.list) ? data.list : [];
  projectLoading.value = false;
  return list;
}

const currentProject = ref({});
function showProjectDetail(project) {
  currentProject.value = { ...project };
  showProjectDetailModal();
}

async function getDeptProjectCateNames(projectIds, deptId) {
  // 查询开单科室是否有对应的项目一级分类
  const { success, data, message } = await getDeptProjectCate({ deptId });
  if (!success) {
    ElMessage.error(message);
    return Promise.reject();
  }
  const ids = data && Array.isArray(data.list) ? data.list.map((i) => i._id) : [];
  if (ids.length === 0) return new Map();
  // 查询项目所属分类
  const res = await getProjectCateGroup({ projectIds });
  if (!res.success) {
    ElMessage.error(res.message);
    return Promise.reject();
  }
  const projectCateGroup = res.data && Array.isArray(res.data.data) ? res.data.data : [];
  const cateIds = projectCateGroup.reduce((acc, item) => {
    if (item && Array.isArray(item.projectCateIdGroup)) {
      return [...acc, ...item.projectCateIdGroup].filter(Boolean);
    }
    return acc;
  }, []);
  if (cateIds.length === 0) return new Map();
  const res2 = await getProjectAllCateGroup({ cateIds, firstCateIds: ids });
  if (!res2.success) {
    ElMessage.error(res2.message);
    return Promise.reject();
  }
  const cates = res2.data && Array.isArray(res2.data.list) ? res2.data.list : [];
  const cateMap = getLabelsByParentGroup(cates);
  const result = projectCateGroup.reduce((m, item) => {
    const projectCateIdGroup = Array.isArray(item.projectCateIdGroup) ? item.projectCateIdGroup : [];
    const key = projectCateIdGroup.find((i) => cateMap.get(i) && cateMap.get(i).length);
    const labels = cateMap.get(key) || [];
    m.set(item._id, labels);
    return m;
  }, new Map());
  return result;
}

function getLabelsByParentGroup(data) {
  const m = new Map();
  data.forEach((item) => {
    const cateGroup = Array.isArray(item.parents) ? item.parents : [];
    cateGroup.push({
      _id: item._id,
      label: item.label,
      level: item.level,
    });
    m.set(
      item._id,
      cateGroup.filter((i) => typeof i.level === "number").sort((a, b) => a.level - b.level)
    );
  });
  return m;
}

watch(
  [projectList, selectedPackage],
  (n) => {
    emits("onProjectsChange", n);
  },
  { immediate: true, deep: true }
);

defineExpose({
  close,
});
</script>
<style lang="scss" scoped>
.header-table-color {
  background-color: rgb(231, 241, 255);
}

.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.line-height-30px {
  line-height: 30px;
}

.item {
  flex: 1;
}

.operationClass {
  position: sticky;
  right: 0;
  box-shadow: 0 0 4px 0 rgba(54, 58, 80, 0.2);
  background-color: #fff;
}

.self-overflow {
  overflow-x: scroll;
  /* 始终显示横向滚动条 */
  overflow-y: scroll;
  /* 始终显示纵向滚动条 */
  transition: overflow-x 0.3s ease;
  /* 滑动过渡效果 */
}

/* 自定义滚动条样式 */
.self-overflow::-webkit-scrollbar {
  width: 6px;
  /* 设置纵向滚动条的宽度 */
  height: 6px;
  /* 设置横向滚动条的高度 */
}

.self-overflow::-webkit-scrollbar-track {
  background: #f1f1f1;
  /* 滚动条轨道的背景色 */
  border-radius: 10px;
  /* 滚动条轨道的圆角 */
}

.self-overflow::-webkit-scrollbar-thumb {
  background: #888;
  /* 滚动条滑块的背景色 */
  border-radius: 10px;
  /* 滚动条滑块的圆角 */
}

.self-overflow::-webkit-scrollbar-thumb:hover {
  background: #555;
  /* 滚动条滑块在悬停时的背景色 */
}
</style>