<template>
    <div v-if="isRead">
        {{ title }}
    </div>
    <el-select v-else :model-value="value" :placeholder="item.placeholder || ''" @change="change($event)" filterable
        class="w-200px">
        <el-option v-for="option in stageList" :label="option.label" :value="option.value" />
    </el-select>
</template>
<script setup>
import { getCustomerType } from "@/api/member";
import { computed, ref } from "vue"
const $emit = defineEmits(["change"]);
const stageList = ref([]);
getStage();
const props = defineProps({
    item: {
        type: Object,
        default: {}
    },
    isRead: {
        type: Boolean,
        default: false
    },
    value: {
        type: String,
        default: ''
    }
})
function change(value) {
    $emit("change", {
        title: props.item.title,
        value
    })
}
const title = computed(() => {
    if (stageList.value.length !== 0 && props.item.value) {
        const item = stageList.value.find(i => i.value === props.item.value);
        return item ? item.label : (props.item.value || '')
    }
    return ''
});
async function getStage() {
    const { data } = await getCustomerType();
    const list =
        data.data && Array.isArray(data.data) ? data.data : [];
    stageList.value = list.map((i) => ({ label: i.name, value: i.type }));
}
</script>

<style></style>