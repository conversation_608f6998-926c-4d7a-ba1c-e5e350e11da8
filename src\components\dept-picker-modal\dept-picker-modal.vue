<template>
  <el-dialog v-loading="loading" :model-value="visible" :width="width" :title="title" @close="close()">
    <div class="flex h-50vh">
      <el-scrollbar class="flex-shrink-0 w-240px border-r border-gray-200">
        <classify-list v-model="deptIds" enableSelect :data="deptList" :mult="mult" />
      </el-scrollbar>
      <el-scrollbar class="flex-grow">
        <div v-for="i in depts" :key="i.value" class="flex items-center py-10px px-15px border-b border-gray-200">
          <div class="w-0 flex-grow truncate">{{ i.label }}</div>
          <div class="text-gray-500 hover:text-red-500 cursor-pointer" @click="remove(i.value)">
            <el-icon class="flex-shrink-0" :size="14">
              <CloseBold />
            </el-icon>
          </div>
        </div>
      </el-scrollbar>
    </div>
    <template #footer>
      <div text-center>
        <el-button plain class="w-100px" type="primary" :icon="Plus" @click="close()">取消</el-button>
        <el-button class="w-100px" :loading="loading" type="primary" :icon="Plus" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { getDeptList } from "@/api/dept-manage";
import { memberStore } from "@/store/member";

import classifyList from "@/components/classify-list/classify-list-side.vue";
import useClassifyList from "@/components/classify-list/useClassifyList";

const { isAdmin, memberInfo } = storeToRefs(memberStore());
const staffDeptIds = computed(() => (memberInfo.value && Array.isArray(memberInfo.value.deptIds) ? memberInfo.value.deptIds : []));
const emits = defineEmits(["close", "change"]);
const props = defineProps({
  onlyStaffDepts: { type: Boolean, default: false },
  value: { type: Array, default: () => [] },
  visible: { type: Boolean, default: false },
  mult: { type: Boolean, default: true }, // 是否多选
  width: { type: [String, Number], default: "500px" },
  title: { type: String, default: "选择科室" },
});
const loading = ref(false);
const options = {
  getList: getDeptList,
  format: (list) => list.map((i) => ({ ...i, label: i.deptName, value: i._id })),
  loading,
};

const { cateList, getCateList } = useClassifyList(options, false);
const deptList = computed(() => {
  if (props.onlyStaffDepts && !isAdmin.value) return cateList.value.filter((i) => staffDeptIds.value.includes(i._id));
  return cateList.value;
});

const deptIds = ref([]);
const depts = computed(() => deptList.value.filter((item) => deptIds.value.includes(item.value)));
function close() {
  emits("close");
}

function confirm() {
  emits(
    "change",
    depts.value.map((i) => ({ ...i }))
  );
}

function remove(value) {
  deptIds.value = deptIds.value.filter((item) => item !== value);
}

async function getDeptsByIds(ids) {
  if (!Array.isArray(ids) || ids.length === 0) return [];
  if (deptList.value.length === 0) await getCateList();
  return deptList.value.filter((item) => ids.includes(item.value)).map((i) => ({ ...i }));
}

watch(
  () => props.visible,
  async (n) => {
    if (n) {
      if (deptList.value.length === 0) await getCateList();
      deptIds.value = Array.isArray(props.value) ? props.value : [];
    }
  }
);

defineExpose({
  getDeptsByIds,
});
</script>