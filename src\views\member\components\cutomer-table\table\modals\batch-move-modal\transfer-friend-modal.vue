<template>
  <el-dialog :model-value="visible" title="好友关系转让" :width="width" @close="close">
    <el-scrollbar wrap-style="max-height:40vh">
      <div class="p-15px text-14px leading-22px" color-normal>
        当服务客户的成员有变更时，机构可将与成员建立好友关系的客户转让给其他成员继续提供服务。
        <br />
        <br />
        转让后，客户和接替的成员将在24小时后自动成为联系人；如果客户主动拒绝，接替成员还可选择主动添加。
        <br />
        <br />
        为了保障客户不被重复打扰，如果新成员成功接替客户，则原跟进成员将无法再继续为该客户提供服务；如果新成员接替失败，则成员还可继续为客户提供服务。
        <br />
        <br />
        为了保障客户的服务体验和提升企业服务的稳定性，每位客户90天内仅可被转接2次，转接总次数无上限。
      </div>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button type="primary" plain class="w-100px" @click="close()">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  width: {
    type: [String, Number],
    default: "400px",
  },
});
const emits = defineEmits(["close"]);

function close() {
  emits("close");
}
</script>