<template>
  <el-dialog :model-value="visible" :width="width" :title="title" @close="close">
    <el-scrollbar style="max-height: 50vh;">
      <el-form class="px-15px" label-position="top">
        <el-form-item label="阳性发现">
          <template #label>
            <div class="py-10px text-14px font-semibold">阳性发现</div>
          </template>
          <el-input v-model="category" type="textarea" :autosize="{minRows: 3, maxRows:6}" :maxlength="200"
            resize="none" placeholder="请输入阳性发现"></el-input>
        </el-form-item>
        <el-form-item label="处理意见">
          <template #label>
            <div class="pt-20px pb-10px text-14px font-semibold">处理意见</div>
          </template>
          <el-input v-model="opinion" type="textarea" :autosize="{minRows: 4, maxRows:8}" resize="none" :maxlength="500"
            placeholder="请输入处理意见"></el-input>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="text-center">
        <el-button v-if="type==='add'" plain class="w-100px" @click="close()">取消</el-button>
        <el-button v-else plain type="danger" class="w-100px" @click="remove()">删除</el-button>
        <el-button type="primary" class="w-100px" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from "element-plus";

const props = defineProps({
  name: { type: String, default: '' },
  type: { type: String, default: 'add' },
  category: { type: String, default: '' },
  opinion: { type: String, default: '' },
  visible: { type: Boolean, default: false },
})
const emits = defineEmits(['change', 'close', 'remove']);
const category = ref('');
const opinion = ref('');
const title = computed(() => props.type === 'add' ? '新增' + props.name : '修改' + props.name)

function confirm() {
  if (category.value.trim() === '') {
    ElMessage.info('请输入阳性发现')
  } else if (opinion.value.trim() === '') {
    ElMessage.info('请输入处理意见')
  } else {
    emits('change', {
      category: category.value,
      opinion: opinion.value
    })
    close()
  }
}
function close() {
  emits('close')
}

async function remove() {
  await ElMessageBox.confirm('确定删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
  emits('remove')
  close()
}

watch(() => props.visible, n => {
  if (n) {
    category.value = typeof props.category === 'string' ? props.category : '';
    opinion.value = typeof props.opinion === 'string' ? props.opinion : '';

  }
})
</script>
<style lang="scss" scoped>
.tag-item {
  border: 1px solid #eee;
  margin-bottom: 12px;
}

.tag-group {
  margin-bottom: -12px;
}

.tag-item+.tag-item {
  margin-left: 12px;
}

.tag-item.active {
  background-color: var(--yc-primary-color);
  border-color: var(--yc-primary-color);
  color: #fff;
}
</style>