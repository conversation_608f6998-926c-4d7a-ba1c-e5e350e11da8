<template>
  <el-dialog :model-value="visible" title="自动建档说明" :width="width" @close="close()">
    <el-scrollbar wrap-style="max-height: calc(100vh - 360px)">
      <div class="flex mt-15px px-15px">
        支持扫码后自动为客户建档，客户微信昵称设置为姓名，可以为客户设置渠道来源，标签等信息。该场景可以应用于线下活动（如义诊）等场合，便于在人流量大时快速登记客户信息，并于活动后再进行二次补充。
      </div>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button class="w-100px" type="primary" plain @click="close">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
defineProps({
  width: { type: Number },
  visible: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(["close"]);

function close() {
  emit("close");
}
</script>