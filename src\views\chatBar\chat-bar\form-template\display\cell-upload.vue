<template>
  <div class="flex flex-wrap">
    <div v-for="i in value" class="relative w-40px h-40px mr-10px mt-10px">
      <!-- :preview-src-list="[i.url]" -->
      <el-image v-if="i.isImage" class="w-40px h-40px" :src="i.url" :zoom-rate="1.2" :max-scale="7" :min-scale="0.2"
        :initial-index="4" fit="cover" @click.stop="previewImage(i)" />
      <img v-else-if="i.isPdf" class="w-40px h-40px" src="@/assets/pdf.svg" alt="i.name" @click="previewPdf(i.url)" />
      <img v-else class="w-40px h-40px" src="@/assets/svg-icons/icon-bt-file.svg" alt="i.name" />
      <div
        class="absolute bottom-0 w-full px-5px truncate text-center bg-gray-500 bg-opacity-75 text-white text-12px leading-18px pointer-events-none">
        {{ i.name }}
      </div>
    </div>
  </div>
</template>
<script setup>
import { computed } from 'vue';
const props = defineProps({
  title: { default: '' },
  form: { type: Object, default: () => ({}) }
})

const imageExt = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico', 'jpe', 'jfif', 'pjpeg', 'pjp'];
const pdfExt = ['pdf']

const value = computed(() => Array.isArray(props.form[props.title]) ? props.form[props.title].map(i => ({
  isImage: /^image\//.test(i.type) || isTypeFile(i.name, imageExt),
  isPdf: /application\/pdf/.test(i.type) || isTypeFile(i.name, pdfExt),
  ...i
})) : [])

function isTypeFile(filename, exts = []) {
  const name = typeof filename === 'string' ? filename : '';
  const fileExtension = name.split('.').pop().toLowerCase();
  return exts.includes(fileExtension);
}

function previewPdf(url) {
  window.open(url, '_blank');
}

function previewImage(item) {
  if (/https?/.test(item.url)) {
    wx.previewImage({
      current: item.url,
      urls: [item.url]
    })
  }
}

</script>
<style lang="scss" scoped></style>
