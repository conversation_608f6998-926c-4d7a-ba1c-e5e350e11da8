<template>
  <el-dialog :model-value="visible" title="消息卡片配置：点击可编辑" :width="width" @close="close()">
    <div class="flex items-center p-10px">
      <div class="flex-grow mr-15px">
        <edit-input v-model:value="form.title" color-normal class="mb-5px text-16px font-semibold" />
        <edit-input v-model:value="form.desc" class="text-14px text-gray-500" />
      </div>
      <el-upload action="#" accept="image/*" :show-file-list="false" list-type="picture" :auto-upload="false"
        @change="changeFile">
        <template #trigger>
          <div :class="cover ? '' : 'border-1 border-dashed border-blue-500'"
            class="flex items-center justify-center w-60px h-60px text-blue-500 rounded">
            <img v-if="cover" :src="cover" class="w-60px h-60px">
            <el-icon v-else>
              <CameraFilled />
            </el-icon>
          </div>
        </template>
      </el-upload>
    </div>

    <template #footer>
      <div text-center>
        <el-button class="w-100px" @click="close">取消</el-button>
        <el-button :loading="loading" class="w-100px" type="primary" @click="confirm()"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref, watch } from 'vue';
import { CameraFilled } from '@element-plus/icons-vue';
import { ElMessage, ElLoading } from "element-plus";
import EditInput from './edit-input.vue';
import { imageToBase64 } from '@/utils';
import { updateFile } from "@/api/uploadFIle.js";

const DEFAULT_IMAGE = 'https://796f-youcan-clouddev-1-8ewcqf31dbb2b5-1317294507.tcb.qcloud.la/other/%E6%A1%A3%E6%A1%88.png?sign=ad25044f43b599712bb290e486bd5038&t=1701328162';
const emits = defineEmits(['close', 'confirm']);
const props = defineProps({
  title: { type: String, default: '' },
  width: { type: Number },
  data: { type: Object, default: () => ({}) },
  visible: { type: Boolean, default: false },
})

const form = ref({
  title: '', //H5消息标题
  desc: '', //H5消息摘要
  imgUrl: ''
});

const coverFile = ref(null)
const cover = computed(() => coverFile.value?.base64 || form.value.imgUrl || DEFAULT_IMAGE)
const loading = ref(false);

watch(() => props.visible, n => {
  if (n) {
    form.value.title = props.data.title || '在线建档';
    form.value.desc = props.data.desc || '点击链接，完成在线建档，让我们更精准的为您服务。';
    form.value.imgUrl = props.data.imgUrl || DEFAULT_IMAGE;
    coverFile.value = null;
  }
})

function close() {
  emits('close')
}

async function confirm() {
  if (!form.value.title) {
    ElMessage.error(`请输入消息标题`);
    return;
  }
  if (!form.value.desc) {
    ElMessage.error(`请输入消息描述`);
    return;
  }
  if (coverFile.value) {
    loading.value = true
    const res = await upload();
    loading.value = false
    if (!res) return;
  }
  emits('confirm', { ...form.value });
  close()
}

async function changeFile(uploadFile) {
  const { raw: file } = uploadFile; // blobUrl仅仅本地预览
  const { size, name, type } = file;
  const newFile = { file, size, name, type };
  if (/^image\//.test(type)) {
    const base64String = await imageToBase64(file);
    const base64WithPrefix = `data:${file.type};base64,${base64String}`;
    coverFile.value = { file: newFile, base64: base64WithPrefix }
  }
}

async function upload() {
  const loadingInstance = ElLoading.service({ fullscreen: true, text: '正在上传' })
  try {
    const file = coverFile.value.file;
    const res = await updateFile(`${+new Date()}_${Math.ceil(Math.random() * 100000)}_${file.name}`, file.file, localStorage.getItem('corpId'));
    if (res) {
      form.value.imgUrl = res.download_url;
      coverFile.value = null;
    } else {
      loadingInstance.close()
      ElMessage.error(`图片上传失败`);
      return false;
    }
  } catch (e) {
    loadingInstance.close()
    ElMessage.error(`图片上传失败`);
    return false;
  }
  loadingInstance.close()
  return true;
}

</script>
