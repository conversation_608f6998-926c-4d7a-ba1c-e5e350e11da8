<template>
  <div v-loading="loading" class="flex h-full bg-white">
    <div class="flex-shrink-0 w-3/10 min-w-240px max-w-320px border-r border-gray-200 h-full">
      <classify-list v-bind="componentProps" :checked-cate="current" :data="cateList" @change="changeCurrent($event)"
        @search="getCateList()"></classify-list>
    </div>
    <div class="flex-grow h-full bg-white">
      <my-layout>
        <layout-item>
          <div class="p-15px flex">
            <el-input v-model="name" class="w-160px mr-15px" placeholder="请输入名称" @keyup.enter="search"
              @input="changeName" />
            <el-select v-model="status" class="w-160px mr-15px" placeholder="所有状态" clearable>
              <el-option v-for="i in statusList" :key="i.value" :label="i.label" :value="i.value" />
            </el-select>
            <el-select v-model="eventType" clearable class="w-160px mr-15px" placeholder="所有事件类型">
              <el-option v-for="item in eventList" :label="item.label" :value="item.value" />
            </el-select>
            <el-select v-model="executeMethod" clearable class="w-160px mr-15px" placeholder="所有事件方式">
              <el-option v-for="item in executeMethodList" :label="item.label" :value="item.value" />
            </el-select>
            <el-button type="primary" @click="search()">查询</el-button>
            <el-button style="margin-left: auto" type="primary" @click="add()">新建</el-button>
          </div>
        </layout-item>
        <layout-main :scroll="false">
          <el-table height="100%" :data="list">
            <el-table-column width="240" label="名称">
              <template #default="{ row: { sopName } }">
                <el-tooltip :disabled="!sopName" :content="sopName" placement="top-start" effect="light">
                  <div class="truncate">{{ sopName || "" }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column width="100" label="状态">
              <template #default="{ row: { statusName } }">
                <div class="truncate">{{ statusName }}</div>
              </template>
            </el-table-column>
            <el-table-column width="120" label="事件类型">
              <template #default="{ row: { eventType } }">
                <div class="truncate">{{ ToDoEventType[eventType] || "" }}</div>
              </template>
            </el-table-column>
            <el-table-column width="120" label="处理类型">
              <template #default="{ row: { executeType } }">
                <div class="truncate">
                  {{ executeTypeMap[executeType] || "" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column width="120" label="处理方式">
              <template #default="{ row: { executeMethod } }">
                <div class="truncate">
                  {{ executeMethodMap[executeMethod] || "" }}
                </div>
              </template>
            </el-table-column>
            <el-table-column width="240" label="生效日期">
              <template #default="{ row: { workDate } }">
                <div class="truncate">{{ workDate || "2024-06-24 ~ 2024-06-29" }}</div>
              </template>
            </el-table-column>
            <el-table-column width="240" label="生效团队">
              <template #default="{ row: { teamNames } }">
                <el-tooltip :disabled="!teamNames" :content="teamNames" placement="top-start" effect="light">
                  <div class="truncate">{{ teamNames || "" }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column width="160" label="创建时间">
              <template #default="{ row: { createTime } }">
                <div class="truncate">{{ createTime || "" }}</div>
              </template>
            </el-table-column>
            <el-table-column width="160" label="创建人">
              <template #default="{ row: { createUserId } }">
                <div class="truncate">
                  <ww-user v-if="createUserId" :openid="createUserId" />
                </div>
              </template>
            </el-table-column>
            <el-table-column fixed="right" class-name="el-table-fixed-column--shadow" label="操作" width="220">
              <template #default="{ row }">
                <template v-if="row.status === 'unexecuted'">
                  <el-text type="primary" class="cursor-pointer" @click="edit(row)">编辑</el-text>
                  <el-text type="primary" class="cursor-pointer ml-2"
                    @click="startSop(row._id, 'startRightNow')">立即启用</el-text>
                  <el-text type="danger" class="cursor-pointer ml-2" @click="remove(row)">删除</el-text>
                </template>
                <template v-else-if="row.status === 'executing'">
                  <el-text class="cursor-pointer" type="primary" @click="toAnalysis(row)">使用分析</el-text>
                  <el-text class="cursor-pointer ml-2" type="primary" @click="edit(row)">编辑</el-text>
                  <el-text v-if="row.disabled" class="cursor-pointer ml-2" type="primary"
                    @click="startSop(row._id, 'start')">启用</el-text>
                  <el-text v-else class="cursor-pointer ml-2" type="primary" @click="stop(row._id)">停用</el-text>
                </template>
                <template v-else-if="row.status === 'closed'">
                  <el-text class="cursor-pointer" type="primary" @click="toAnalysis(row)">使用分析</el-text>
                  <el-text class="cursor-pointer ml-2" type="primary" @click="edit(row)">再次启用</el-text>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </layout-main>
        <layout-item>
          <pagination :bgWhite="false" :totalRow="total" :pageSize="pageSize" :currentPage="page"
            @handle-size-change="changeSize" @handle-current-change="changePage" />
        </layout-item>
      </my-layout>
    </div>
  </div>
  <!-- <classify-modal :id="currentSurvery.cateId" :cateTree="cateTree" :visible="cateVisible"
    :update-method="setSurveryNewCate" :width="cateModalWidth" @close="closeCateModal" /> -->
</template>
<script setup>
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { storeToRefs } from "pinia";
import { ElMessage, ElMessageBox } from "element-plus";
import dayjs from "dayjs";
import { ToDoEventType } from "@/baseData";
import useElPagination from "@/hooks/useElPagination";
import { memberStore } from "@/store/member";
import { tagsStore } from "@/store/tags";

import { addSopCate, updateSopCate, deleteSopCate, getSopCateList, sortSopCate, getSopTaskList, removeSopTask, updateSopTaskField } from "@/api/member";
import useClassifyList from "@/components/classify-list/useClassifyList";
import { statusMap, statusList, executeTypeMap, executeMethodMap, executeMethodList } from "./sop-enum";

import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import pagination from "@/components/pagination/pagination.vue";
import classifyList from "@/components/classify-list/classify-list.vue";

const router = useRouter();
const name = ref("");
const status = ref();
const eventType = ref("");
const executeMethod = ref("");
const list = ref([]);
const total = ref(0);
const loading = ref(false);
const eventList = Object.keys(ToDoEventType).map((value) => ({ value, label: ToDoEventType[value] }));
const { page, pageSize, changePage, changeSize } = useElPagination(getList);
const { memberInfo } = storeToRefs(memberStore());
const { getTagState, setTagState } = tagsStore();

const options = {
  add: addSopCate,
  remove: deleteSopCate,
  update: updateSopCate,
  sort: sortSopCate,
  getList: getSopCateList,
  callback: () => changePage(1),
  loading: loading,
};

const { cateList, current, getCateList, changeCurrent, componentProps } = useClassifyList(options, false);
// const cateMap = computed(() => cateList.value.reduce((acc, cur) => {
//   acc[cur._id] = cur.label;
//   return acc;
// }, {}))

function changeName(e) {
  if (e === "") search();
}

const currentParams = {};
function search() {
  currentParams.sopName = name.value.trim();
  currentParams.sopExecuteStatus = status.value;
  currentParams.eventType = eventType.value;
  currentParams.executeMethod = executeMethod.value;
  changePage(1);
}

async function getList() {
  loading.value = true;
  if (current.value._id) {
    const childrenIds = Array.isArray(current.value.childrenIds) ? [current.value._id, ...(current.value.childrenIds || [])] : [current.value._id];
    const {
      success,
      message,
      data = {},
    } = await getSopTaskList({
      page: page.value,
      pageSize: pageSize.value,
      classIds: childrenIds,
      ...currentParams,
    });
    const { list: tableData = [], total: count = 0, timeStamp } = data;
    list.value = Array.isArray(tableData)
      ? tableData.map((i) => {
        const executeStartTime = i.executeStartTime && dayjs(i.executeStartTime).isValid() ? dayjs(i.executeStartTime).format("YYYY-MM-DD") : "";
        const executeEndTime = i.executeEndTime && dayjs(i.executeEndTime).isValid() ? dayjs(i.executeEndTime).format("YYYY-MM-DD") : i.executeEndTime === "" ? "不限" : "";
        const teamNames = Array.isArray(i.executeTeams)
          ? i.executeTeams
            .map((i) => i.name)
            .filter(Boolean)
            .join("、")
          : "";
        let status = "";
        const disabled = i.sopEnableStatus === "disable";
        if (dayjs(timeStamp).isValid()) {
          if (i.executeStartTime > timeStamp) status = "unexecuted";
          else if (i.executeEndTime && i.executeEndTime < timeStamp) status = "closed";
          else if (i.executeStartTime) status = "executing";
        }
        let statusName = status === "closed" ? statusMap[status] : disabled ? "已停用" : statusMap[status] || "";
        return {
          ...i,
          teamNames,
          status,
          statusName,
          disabled,
          createTime: i.createTime && dayjs(i.createTime).isValid() ? dayjs(i.createTime).format("YYYY-MM-DD HH:mm") : "",
          workDate: [executeStartTime, executeEndTime].filter(Boolean).join(" ~ "),
        };
      })
      : [];
    total.value = count;
    loading.value = false;
    if (!success) ElMessage.error(message);
  } else {
    list.value = [];
    total.value = 0;
  }
}

function edit(item) {
  router.push({ name: "SOP", params: { id: item._id } });
}

function add(id = "") {
  setTagState({ cateId: current.value && current.value._id ? current.value._id : "" });
  router.push({ name: "SOP", params: { id }, state: { cateId: current.value && current.value._id ? current.value._id : "" } });
}

function toAnalysis(item) {
  router.push({ name: "SOPANALYSIS", params: { id: item._id, executeMethod: item.executeMethod } });
}
async function startSop(id, action) {
  await ElMessageBox.confirm("确定启用吗?", "提示", { type: "warning", confirmButtonText: "确定启用" });
  setSop(id, action);
}
async function stop(id) {
  await ElMessageBox.confirm("停用后，已生成任务的继续执行；后续符合执行条件的客户不再触发任务?", "提示", { type: "warning", confirmButtonText: "确定停用" });
  setSop(id, "stop");
}
async function remove({ _id }) {
  await ElMessageBox.confirm("确定删除吗?", "提示", { type: "warning" });
  const { success, message } = await removeSopTask(_id);
  if (success) {
    ElMessage.success(message);
    search();
  } else {
    ElMessage.error(message);
  }
}

async function setSop(id, action) {
  const { success, message } = await updateSopTaskField({
    corpId: memberInfo.value.corpId,
    id,
    action,
  });
  if (success) {
    ElMessage.success(message);
    search();
  } else {
    ElMessage.error(message);
  }
}

onMounted(() => {
  const state = getTagState();
  getCateList(state.cateId);
});
</script>
