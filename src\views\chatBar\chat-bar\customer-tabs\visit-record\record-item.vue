<template>
  <div v-if="setting" class="mb-10px rounded-8px bg-white shadow-xl text-14px" @click="viewDetail(record)">
    <div class="flex items-center py-12px px-12px">
      <div class="font-semibold">{{ record.date }}</div>
      <div v-if="temp.label" class="ml-10px py-4px px-6px text-white rounded-6px text-12px" :class="setting.bg">
        {{ temp.label }}
      </div>
      <div v-if="showTag" class="ml-10px py-4px px-6px text-white bg-rose-500 rounded-6px text-12px">外院</div>
    </div>
    <div class="px-12px">
      <template v-for="i in showlist" :key="i.title">
        <div v-if="i.type === 'files'" class="pb-12px">
          <div class="text-gray-500">{{ i.name }}：</div>
          <cell-upload :title="i.title" :form="record" />
        </div>
        <div v-else class="flex pb-12px">
          <div class="flex-shrink-0 text-gray-500">{{ i.name }}：</div>
          <div class="flex-grow">{{ i.value }}</div>
        </div>
      </template>
    </div>
    <div class="flex items-center justify-between p-12px border-t border-gray-100 text-12px">
      <div class="flex-shrink-0 mr-10px">创建时间：{{ createTime }}</div>
      <div v-if="createByCustomer" class="flex-shrink-0 truncate">记录人：客户本人</div>
      <div v-else class="w-0 flex-grow text-right truncate">
        记录人：
        <span v-if="record.creator">
          <ww-user :openid="record.creator" />
          （员工）
        </span>
      </div>
    </div>
  </div>
</template>
<script setup>
import { computed, toRefs } from "vue";
import dayjs from "dayjs";
import cellUpload from "../../form-template/display/cell-upload.vue";
// import WwUser from "@/components/ww-user/index.vue"

const emits = defineEmits(["viewDetail"]);
const props = defineProps({
  record: { type: Object, default: () => ({}) },
});
const config = {
  outpatient: {
    bg: "bg-amber-600",
    fields: ["corpName", "deptName", "doctor", "diagnosisName", "files"],
  },
  inhospital: {
    bg: "bg-teal-600",
    fields: ["corpName", "diagnosisName", "operation", "operationDate", "outhosDate", "files"],
  },
  healthProjectTemplate: {
    bg: "bg-yellow-500",
    fields: ["projectName", "files"],
  },
  physicalExaminationTemplate: {
    bg: "bg-green-500",
    fields: ["inspectPakageName", "files"],
  },
};
const { record } = toRefs(props);
const temp = computed(() => record.value.temp || {});
const setting = computed(() => (config[temp.value.value] ? config[temp.value.value] : { bg: "bg-blue-500", fields: [] }));
const showTag = computed(() => ["outpatient", "inhospital"].includes(temp.value.value) && record.value.corp === "其他");
const createTime = computed(() => (props.record.createTime ? dayjs(props.record.createTime).format("YYYY-MM-DD") : ""));
const createByCustomer = computed(() => props.record.ignore === "checkIn"); // 来自客户扫码签到
const showlist = computed(() => {
  const templateList = Array.isArray(temp.value.templateList) ? temp.value.templateList : [];
  // 过滤出 展示字段
  const list = setting
    ? setting.value.fields
        .map((title) => templateList.find((i) => i.title === title))
        .filter((i) => {
          if (!i) return false;
          if (!("referenceField" in i)) return true;
          // 存在关联字段 根据关联字段的值 展示
          return record.value[i.referenceField] === i.referenceValue;
        })
        .map((i) => {
          const item = { ...i };
          if (i.type === "date") {
            const date = record.value[i.title] ? dayjs(record.value[i.title]) : "";
            item.value = date && date.isValid() ? date.format(i.format || "YYYY-MM-DD") : "";
          } else {
            item.value = Array.isArray(record.value[i.title]) ? record.value[i.title].join() : record.value[i.title];
          }
          return item;
        })
    : [];
  // 根据referenceField 过滤
  return list;
});

function viewDetail(record) {
  emits("viewDetail", record);
}
</script>
<style lang="scss" scoped></style>
