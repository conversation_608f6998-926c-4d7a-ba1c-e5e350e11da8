<template>
  <div class="report-container">
    <div class="report-header">
      <div class="report-actions">
        <div class="filter-section">
          <div class="filter-row">
            <div class="filter-item">
              <span class="filter-label">咨询日期</span>

              <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" :shortcuts="dateShortcuts" :disabled-date="disabledDate" @change="handleDateChange" class="date-range" value-format="YYYY-MM-DD" />
            </div>
            <filter-info-source v-model="infoSource" />
            <div class="filter-item">
              <check-box-filter v-model="consultantFilter" label="咨询师" :list="consultantList">
                <template #item="{ item }">
                  <ww-user :openid="item.value"></ww-user>
                </template>
              </check-box-filter>
            </div>
            <div class="filter-item">
              <check-box-filter v-model="developerUserIds" label="开发人员" :list="developerList">
                <template #item="{ item }">
                  <ww-user :openid="item.value"></ww-user>
                </template>
              </check-box-filter>
            </div>
            <check-box-filter v-model="consultStages" label="咨询状态" :list="ConsultStage" />
            <radio-filter v-model="tradeStatus" label="成交状态" :list="TradeStatus"></radio-filter>
            <div class="filter-item">
              <span class="filter-label">咨询科室：</span>
              <dept-select v-model="deptId" placeholder="请选择科室" @change="handleDeptChange" class="dept-select" />
            </div>
            <div class="filter-item">
              <span class="filter-label">咨询项目：</span>
              <project-intent-select v-model="projectIntentIds" :deptId="deptId" placeholder="请选择或搜索咨询项目" @change="handleProjectChange" />
            </div>
            <div class="filter-actions">
              <el-button type="primary" @click="handleSearch" :loading="loading" :disabled="loading">查询</el-button>
              <el-button type="success" @click="exportToExcel" :disabled="loading">导出Excel</el-button>
              <el-button @click="resetFilters" :disabled="loading">重置</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="table-container" v-loading="loading" element-loading-text="正在获取数据...">
      <table class="performance-table">        <colgroup>
          <col style="width: 120px" />
          <!-- 咨询师 -->
          <col style="width: 150px" />
          <!-- 咨询项目 -->
          <col style="width: 100px" />
          <!-- 咨询人次 -->
          <col style="width: 80px" />
          <!-- 占比% -->
          <col style="width: 100px" />
          <!-- 成交人次 -->
          <col style="width: 100px" />
          <!-- 成交率 -->
          <col style="width: 100px" />
          <!-- 未成交人次 -->
          <col style="width: 120px" />
          <!-- 未成交占比% -->
          <col style="width: 120px" />
          <!-- 初诊咨询人次 -->
          <col style="width: 120px" />
          <!-- 初诊成交人次 -->
          <col style="width: 120px" />
          <!-- 复诊咨询人次 -->
          <col style="width: 120px" />
          <!-- 复诊成交人次 -->
          <col style="width: 140px" />
          <!-- 再消费咨询人次 -->
          <col style="width: 140px" />
          <!-- 再消费成交人次 -->
          <col style="width: 120px" />
          <!-- 参考金额 -->
        </colgroup>
        <thead>
          <tr>
            <th rowspan="2">咨询师</th>
            <th rowspan="2">咨询项目</th>
            <th rowspan="2">咨询人次</th>
            <th rowspan="2">占比%</th>
            <th rowspan="2">成交人次</th>
            <th rowspan="2">成交率</th>
            <th rowspan="2">未成交人次</th>
            <th rowspan="2">未成交占比%</th>
            <th colspan="2">初诊人次</th>
            <th colspan="2">复诊人次</th>
            <th colspan="2">再消费人次</th>
            <th rowspan="2">参考金额</th>
          </tr>
          <tr>
            <th>咨询人次</th>
            <th>成交人次</th>
            <th>咨询人次</th>
            <th>成交人次</th>
            <th>咨询人次</th>
            <th>成交人次</th>
          </tr>
        </thead>
        <tbody>
          <template v-for="(counselorGroup, counselorIndex) in counselorGroupedData" :key="counselorIndex">
            <!-- 项目行 -->
            <tr v-for="(item, pIndex) in counselorGroup.projects" :key="`${counselorGroup.counselorId}-${item.projectKey}-${pIndex}`">
              <td>
                <ww-user v-if="pIndex === 0" :openid="counselorGroup.counselorId"></ww-user>
              </td>
              <td>{{ item.projectName || "未知项目" }}</td>
              <td>{{ item.visitCount }}</td>
              <td>{{ formatPercentage(item.visitCount / totalVisitCount, true) }}</td>
              <td>{{ item.dealCount }}</td>
              <td>{{ formatPercentage(item.dealCount / item.visitCount, true) }}</td>
              <td>{{ item.visitCount - item.dealCount }}</td>
              <td>{{ formatPercentage((item.visitCount - item.dealCount) / item.visitCount, true) }}</td>
              <td>{{ item.firstVisitCount || 0 }}</td>
              <td>{{ item.firstDealCount || 0 }}</td>
              <td>{{ item.revisitCount || 0 }}</td>
              <td>{{ item.revisitDealCount || 0 }}</td>
              <td>{{ item.reconsumptionCount || 0 }}</td>
              <td>{{ item.reconsumptionDealCount || 0 }}</td>
              <td>{{ formatCurrency(item.totalAmount) }}</td>
            </tr>
            <!-- 咨询师小计行 -->
            <tr class="subtotal-row" v-if="counselorGroup.projects.length > 1">
              <td>小计：</td>
              <td></td>
              <td>{{ counselorGroup.totalVisitCount }}</td>
              <td>{{ formatPercentage(counselorGroup.totalVisitCount / totalVisitCount, true) }}</td>
              <td>{{ counselorGroup.totalDealCount }}</td>
              <td>{{ formatPercentage(counselorGroup.totalDealCount / counselorGroup.totalVisitCount, true) }}</td>
              <td>{{ counselorGroup.totalVisitCount - counselorGroup.totalDealCount }}</td>
              <td>{{ formatPercentage((counselorGroup.totalVisitCount - counselorGroup.totalDealCount) / counselorGroup.totalVisitCount, true) }}</td>
              <td>{{ counselorGroup.totalFirstVisitCount || 0 }}</td>
              <td>{{ counselorGroup.totalFirstDealCount || 0 }}</td>
              <td>{{ counselorGroup.totalRevisitCount || 0 }}</td>
              <td>{{ counselorGroup.totalRevisitDealCount || 0 }}</td>
              <td>{{ counselorGroup.totalReconsumptionCount || 0 }}</td>
              <td>{{ counselorGroup.totalReconsumptionDealCount || 0 }}</td>
              <td>{{ formatCurrency(counselorGroup.totalAmount) }}</td>
            </tr>
          </template>
          <tr class="total-row">
            <td colspan="2" class="total-label">合计：</td>
            <td>{{ totalVisitCount }}</td>
            <td>{{ formatPercentage(1, true) }}</td>
            <td>{{ totalDealCount }}</td>
            <td>{{ formatPercentage(totalDealCount / totalVisitCount, true) }}</td>
            <td>{{ totalVisitCount - totalDealCount }}</td>
            <td>{{ formatPercentage((totalVisitCount - totalDealCount) / totalVisitCount, true) }}</td>
            <td>{{ totalFirstVisitCount || 0 }}</td>
            <td>{{ totalFirstDealCount || 0 }}</td>
            <td>{{ totalRevisitCount || 0 }}</td>
            <td>{{ totalRevisitDealCount || 0 }}</td>
            <td>{{ totalReconsumptionCount || 0 }}</td>
            <td>{{ totalReconsumptionDealCount || 0 }}</td>
            <td>{{ formatCurrency(totalAmount) }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="footer-info">
      <span class="date-limit-info">* 查询限制为一个月内的数据</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { ElButton, ElMessage, ElDatePicker, ElSelect, ElOption } from "element-plus";
import { CheckBoxFilter, filterInfoSource, RadioFilter } from "@/components/filter-bar";
import { storeToRefs } from "pinia";
import { staffStore } from "@/store/staff";
import { memberStore } from "@/store/member";
import { teamStore } from "@/store/team";
import ProjectIntentSelect from "@/components/benefit-management/project-intent-select.vue";
import DeptSelect from "@/components/dept-components/dept-select.vue";
import { useDeptStore } from "@/components/dept-components/dept-store";
import { getProjectConsultStatistics } from "@/api/consult"; // 导入项目统计API
import { getProjectIntentNames } from "@/api/benefitManagement"; // 导入项目名称获取API
import { ConsultStage, TradeStatus } from "@/baseData";
// 用户筛选
const dateRange = ref([]);
const infoSource = ref([]);
const consultantFilter = ref([]);
const developerUserIds = ref([]);
const deptId = ref("");
const selectedDept = ref({});
const projectIntentIds = ref([]);
const selectedProjects = ref([]);
const consultStages = ref([]);
const tradeStatus = ref("");
const deptStore = useDeptStore();
// 从store获取员工列表
const { getStaffList } = staffStore();
const { staffList } = storeToRefs(staffStore());
const { isAdmin } = storeToRefs(memberStore());
const { managerList } = storeToRefs(teamStore());
const { judgmentIsAdmin } = memberStore();

// 添加开发人员列表计算属性，只显示开发人员角色
const developerList = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("onlineCustomerService")).map((i) => ({ value: i.userid }));
});

// 添加咨询师列表计算属性，只显示咨询师角色
const consultantList = computed(() => {
  return staffList.value.filter((i) => Array.isArray(i.job) && i.job.includes("consultant")).map((i) => ({ value: i.userid }));
});

// 添加科室变化处理函数
function handleDeptChange(dept) {
  selectedDept.value = dept;
  projectIntentIds.value = [];
}

// 日期快捷选项
const dateShortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近半个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 15);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
];

// 禁用超过一个月范围的日期
const disabledDate = (time) => {
  if (!dateRange.value || dateRange.value.length === 0) {
    return false;
  }

  const startDate = new Date(dateRange.value[0]);
  const endDate = new Date(dateRange.value[1]);
  const currentDate = new Date(time);

  // 如果已选择了开始日期，限制结束日期不能超过开始日期后的31天
  if (dateRange.value.length === 1) {
    const oneMonthLater = new Date(startDate);
    oneMonthLater.setDate(startDate.getDate() + 31);
    return currentDate > oneMonthLater;
  }

  return false;
};

// 监听日期变化，确保不超过一个月
watch(dateRange, (newValue) => {
  if (newValue && newValue.length === 2) {
    const start = new Date(newValue[0]);
    const end = new Date(newValue[1]);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays > 31) {
      // 如果超过31天，将结束日期设置为开始日期后的31天
      const maxEnd = new Date(start);
      maxEnd.setDate(start.getDate() + 31);
      const formattedMaxEnd = formatDate(maxEnd);

      dateRange.value = [newValue[0], formattedMaxEnd];
      ElMessage.warning("查询范围已限制为一个月内");
    }
  }
});

// 格式化日期为YYYY-MM-DD
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  return `${year}-${month}-${day}`;
};

// 格式化货币
const formatCurrency = (value) => {
  if (value === undefined || value === null || isNaN(value)) {
    return "¥0.00";
  }
  return new Intl.NumberFormat("zh-CN", { style: "currency", currency: "CNY" }).format(value);
};

// 格式化百分比
const formatPercentage = (value, noSymbol = false) => {
  if (value === undefined || value === null || isNaN(value)) {
    return noSymbol ? "0.00%" : "0.00%";
  }
  return noSymbol ? `${(value * 100).toFixed(2)}%` : `${(value * 100).toFixed(2)}%`;
};

// 原始数据和筛选后的表格数据
const originalData = ref([]);
const tableData = ref([]);
// 添加 loading 状态
const loading = ref(false);

// 总计数据
const totalVisitCount = ref(0);
const totalDealCount = ref(0);
const totalAmount = ref(0);

// 添加新的总计属性
const totalFirstVisitCount = ref(0);
const totalFirstDealCount = ref(0);
const totalRevisitCount = ref(0);
const totalRevisitDealCount = ref(0);
const totalReconsumptionCount = ref(0);
const totalReconsumptionDealCount = ref(0);

// 用于存储项目ID到名称的映射
const projectNameMap = ref({});

// 用于存储咨询师ID到姓名的映射
const counselorNameMap = ref({});

// 获取项目名称的函数
async function getProjectNames(projectIds) {
  if (!projectIds || projectIds.length === 0) return {};

  try {
    // 去重项目ID
    const uniqueProjectIds = [...new Set(projectIds.filter((id) => id))];

    if (uniqueProjectIds.length === 0) return {};

    // 调用API获取项目名称
    const { data: nameData, success: nameSuccess } = await getProjectIntentNames({ ids: uniqueProjectIds });

    if (nameSuccess && nameData && nameData.data) {
      // 将返回的数组转换为ID到名称的映射
      const nameMap = {};
      nameData.data.forEach((item) => {
        nameMap[item._id] = item.projectName;
      });

      // 保存到全局映射中，以便重用
      projectNameMap.value = { ...projectNameMap.value, ...nameMap };

      return nameMap;
    }

    // 如果API没有返回数据，使用简单的项目ID作为名称
    const fallbackMap = {};
    uniqueProjectIds.forEach((id) => {
      // 检查全局映射是否已有该项目名称
      if (projectNameMap.value[id]) {
        fallbackMap[id] = projectNameMap.value[id];
      } else {
        fallbackMap[id] = `项目${id.substring(0, 5)}`;
      }
    });

    return fallbackMap;
  } catch (error) {
    console.error("获取项目名称失败:", error);

    // 出错时使用简单的项目ID作为名称
    const errorMap = {};
    projectIds.forEach((id) => {
      if (id) errorMap[id] = `项目${id.substring(0, 5)}`;
    });

    return errorMap;
  }
}

// 获取咨询师名称映射
function getCounselorNames() {
  const nameMap = {};
  staffList.value.forEach((staff) => {
    nameMap[staff.userid] = staff.name || staff.anotherName || staff.userid;
  });
  return nameMap;
}

// 项目选择变更处理
const handleProjectChange = (projects) => {
  selectedProjects.value = projects;
};

async function fetchCounselorProjectStatistics() {
  loading.value = true;
  try {
    // 构建查询参数 - 包含所有筛选条件
    const today = new Date();
    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(today.getMonth() - 1);

    const params = {
      startDate: dateRange.value?.[0] || formatDate(oneMonthAgo),
      endDate: dateRange.value?.[1] || formatDate(today),
    };

    if (consultantFilter.value && consultantFilter.value.length > 0) {
      params.consultantFilter = consultantFilter.value;
    }
    if (developerUserIds.value && developerUserIds.value.length > 0) {
      params.developerUserIds = developerUserIds.value;
    }

    // 信息来源筛选
    if (infoSource.value && infoSource.value.length > 0) {
      params.infoSource = infoSource.value;
    }

    if (deptId.value) {
      params.deptId = deptId.value; // 添加科室ID筛选条件
      params.projectIds  = (await deptStore.fetchProjectIdsByDeptId(deptId.value)) || [];
    }

    // 项目筛选
    if (projectIntentIds.value && projectIntentIds.value.length > 0) {
      params.projectIds = projectIntentIds.value;
    }

    if (consultStages.value && consultStages.value.length > 0) {
      params.consultStages = consultStages.value;
    }

    if (tradeStatus.value) {
      params.tradeStatus = tradeStatus.value;
    }

    // 调用API获取数据
    const { data, success, message } = await getProjectConsultStatistics(params);

    if (success) {
      // 保存原始数据
      originalData.value = data.data || [];
      // 处理项目统计数据
      if (data.data && Array.isArray(data.data)) {
        // 处理数据：按咨询师和项目分组
        const counselorGroups = {};
        let totalConsultCount = 0;
        let totalSuccessCount = 0;
        let firstVisitCountTotal = 0;
        let totalFirstVisitSuccessCount = 0;
        let totalReturnVisitCount = 0;
        let totalReturnVisitSuccessCount = 0;
        let totalMoreConsumedCount = 0;
        let totalMoreConsumedSuccessCount = 0;
        let totalSuccessAmount = 0;

        // 首先计算总数，用于后续计算占比
        data.data.forEach((item) => {
          totalConsultCount += item.consultCount || 0;
          totalSuccessCount += item.successCount || 0;
          firstVisitCountTotal += item.firstVisitCount || 0;
          totalFirstVisitSuccessCount += item.firstVisitSuccessCount || 0;
          totalReturnVisitCount += item.returnVisitCount || 0;
          totalReturnVisitSuccessCount += item.returnVisitSuccessCount || 0;
          totalMoreConsumedCount += item.moreConsumedCount || 0;
          totalMoreConsumedSuccessCount += item.moreConsumedSuccessCount || 0;
          totalSuccessAmount += item.successAmount || 0;
        });

        // 修复：设置总计值，确保正确处理ref
        totalVisitCount.value = totalConsultCount;
        totalDealCount.value = totalSuccessCount;
        totalAmount.value = totalSuccessAmount;
        // 修复以下变量，避免变量名混淆导致的错误
        totalFirstVisitCount.value = firstVisitCountTotal;
        totalFirstDealCount.value = totalFirstVisitSuccessCount;
        totalRevisitCount.value = totalReturnVisitCount;
        totalRevisitDealCount.value = totalReturnVisitSuccessCount;
        totalReconsumptionCount.value = totalMoreConsumedCount;
        totalReconsumptionDealCount.value = totalMoreConsumedSuccessCount;

        // 获取所有项目ID
        const allProjectIds = data.data.map((item) => item.projectId).filter((id) => id);

        // 获取项目名称映射
        const projectNames = await getProjectNames(allProjectIds);

        // 获取咨询师名称映射
        counselorNameMap.value = getCounselorNames();

        // 按咨询师和项目分组处理数据
        data.data.forEach((item) => {
          const counselorId = item.counselorUserId || "unknown";
          const projectKey = item.projectId;
          const projectName = projectNames[projectKey] || item.projectName;

          if (!counselorGroups[counselorId]) {
            counselorGroups[counselorId] = {
              counselorId,
              counselorName: counselorNameMap.value[counselorId] || counselorId,
              projects: [],
              totalVisitCount: 0,
              totalDealCount: 0,
              totalAmount: 0,
              totalFirstVisitCount: 0,
              totalFirstDealCount: 0,
              totalRevisitCount: 0,
              totalRevisitDealCount: 0,
              totalReconsumptionCount: 0,
              totalReconsumptionDealCount: 0,
            };
          }

          // 为该咨询师添加项目数据
          const projectItem = {
            projectKey,
            projectName,
            visitCount: item.consultCount || 0,
            dealCount: item.successCount || 0,
            totalAmount: item.successAmount || 0,
            firstVisitCount: item.firstVisitCount || 0,
            firstDealCount: item.firstVisitSuccessCount || 0,
            revisitCount: item.returnVisitCount || 0,
            revisitDealCount: item.returnVisitSuccessCount || 0,
            reconsumptionCount: item.moreConsumedCount || 0,
            reconsumptionDealCount: item.moreConsumedSuccessCount || 0,
          };

          counselorGroups[counselorId].projects.push(projectItem);

          // 更新咨询师小计
          counselorGroups[counselorId].totalVisitCount += projectItem.visitCount;
          counselorGroups[counselorId].totalDealCount += projectItem.dealCount;
          counselorGroups[counselorId].totalAmount += projectItem.totalAmount;
          counselorGroups[counselorId].totalFirstVisitCount += projectItem.firstVisitCount;
          counselorGroups[counselorId].totalFirstDealCount += projectItem.firstDealCount;
          counselorGroups[counselorId].totalRevisitCount += projectItem.revisitCount;
          counselorGroups[counselorId].totalRevisitDealCount += projectItem.revisitDealCount;
          counselorGroups[counselorId].totalReconsumptionCount += projectItem.reconsumptionCount;
          counselorGroups[counselorId].totalReconsumptionDealCount += projectItem.reconsumptionDealCount;
        });

        // 转换为数组
        tableData.value = Object.values(counselorGroups);
      } else {
        tableData.value = [];
        // 重置总计数据
        totalVisitCount.value = 0;
        totalDealCount.value = 0;
        totalAmount.value = 0;
        totalFirstVisitCount.value = 0;
        totalFirstDealCount.value = 0;
        totalRevisitCount.value = 0;
        totalRevisitDealCount.value = 0;
        totalReconsumptionCount.value = 0;
        totalReconsumptionDealCount.value = 0;
      }

      console.log("获取咨询师项目统计数据成功，共 " + tableData.value.length + " 条记录");
    } else {
      ElMessage.error(message || "获取统计数据失败");
      tableData.value = [];
    }
  } catch (error) {
    ElMessage.error("获取统计数据出错：" + (error.message || error));
    tableData.value = [];
    console.error("获取统计数据出错:", error);
  } finally {
    loading.value = false;
  }
}

// 筛选表格数据
const filteredTableData = computed(() => {
  return tableData.value;
});

// 按咨询师分组的数据
const counselorGroupedData = computed(() => {
  return filteredTableData.value;
});

// 处理日期变化
const handleDateChange = () => {
  // 日期改变时，重新调用接口获取数据
  if (dateRange.value && dateRange.value.length === 2) {
    fetchCounselorProjectStatistics();
  }
};

// 重置筛选条件
const resetFilters = () => {
  // 设置默认日期范围为当月
  const today = new Date();
  const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
  const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

  dateRange.value = [formatDate(firstDayOfMonth), formatDate(lastDayOfMonth)];
  infoSource.value = [];
  consultantFilter.value = [];
  deptId.value = "";
  selectedDept.value = {};
  projectIntentIds.value = [];
  selectedProjects.value = [];
  consultStages.value = [];
  tradeStatus.value = "";

  developerUserIds.value = [];
  // 重置后重新获取数据
  fetchCounselorProjectStatistics();
};

// 处理查询
const handleSearch = () => {
  if (dateRange.value && dateRange.value.length === 2) {
    const start = new Date(dateRange.value[0]);
    const end = new Date(dateRange.value[1]);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays > 31) {
      ElMessage.warning("查询范围已限制为一个月内");
      return;
    }

    // 获取数据
    fetchCounselorProjectStatistics();
  } else {
    ElMessage.warning("请选择咨询日期范围");
  }
};

// 导出Excel
const exportToExcel = () => {
  // 准备导出数据
  const exportData = [];

  // 添加标题信息
  exportData.push(["咨询师项目统计表"]);

  // 添加筛选条件信息
  if (dateRange.value && dateRange.value.length === 2) {
    exportData.push(["咨询日期范围", `${dateRange.value[0]} 至 ${dateRange.value[1]}`]);
  }

  if (consultantFilter.value.length > 0) {
    exportData.push(["咨询师", consultantFilter.value.join(", ")]);
  }

  if (developerUserIds.value.length > 0) {
    const developerNames = developerUserIds.value
      .map((id) => {
        const staff = staffList.value.find((s) => s.userid === id);
        return staff ? staff.anotherName || id : id;
      })
      .join(", ");
    exportData.push(["开发人员", developerNames]);
  }

  if (infoSource.value.length > 0) {
    exportData.push(["信息来源", infoSource.value.join(", ")]);
  }

  if (deptId.value && selectedDept.value) {
    exportData.push(["咨询科室", selectedDept.value.deptName || deptId.value]);
  }

  if (projectIntentIds.value.length > 0) {
    const projectsText = selectedProjects.value.map((p) => p.projectName || p.id).join(", ");
    exportData.push(["咨询项目", projectsText]);
  }

  exportData.push([]); // 空行

  // 添加表头 - 两行表头
  exportData.push(["咨询师", "咨询项目", "咨询人次", "占比%", "成交情况", "", "", "", "初诊人次", "", "复诊人次", "", "再消费人次", "", "参考金额"]);

  exportData.push(["", "", "", "", "成交人次", "成功率%", "未成交人次", "未成交占比%", "咨询人次", "成交人次", "咨询人次", "成交人次", "咨询人次", "成交人次", ""]);

  // 添加按咨询师分组的数据
  counselorGroupedData.value.forEach((counselorGroup) => {
    const counselorName = counselorNameMap.value[counselorGroup.counselorId] || counselorGroup.counselorId;

    // 添加每个咨询师的项目数据
    counselorGroup.projects.forEach((item, idx) => {
      exportData.push([
        idx === 0 ? counselorName : "", // 只在第一行显示咨询师名称
        item.projectName || "未知项目",
        item.visitCount,
        formatPercentage(item.visitCount / totalVisitCount, true),
        item.dealCount,
        formatPercentage(item.dealCount / item.visitCount, true),
        item.visitCount - item.dealCount,
        formatPercentage((item.visitCount - item.dealCount) / item.visitCount, true),
        item.firstVisitCount || 0,
        item.firstDealCount || 0,
        item.revisitCount || 0,
        item.revisitDealCount || 0,
        item.reconsumptionCount || 0,
        item.reconsumptionDealCount || 0,
        item.totalAmount,
      ]);
    });

    // 添加咨询师小计行
    if (counselorGroup.projects.length > 1) {
      exportData.push(["小计", "", counselorGroup.totalVisitCount, formatPercentage(counselorGroup.totalVisitCount / totalVisitCount, true), counselorGroup.totalDealCount, formatPercentage(counselorGroup.totalDealCount / counselorGroup.totalVisitCount, true), counselorGroup.totalVisitCount - counselorGroup.totalDealCount, formatPercentage((counselorGroup.totalVisitCount - counselorGroup.totalDealCount) / counselorGroup.totalVisitCount, true), counselorGroup.totalFirstVisitCount || 0, counselorGroup.totalFirstDealCount || 0, counselorGroup.totalRevisitCount || 0, counselorGroup.totalRevisitDealCount || 0, counselorGroup.totalReconsumptionCount || 0, counselorGroup.totalReconsumptionDealCount || 0, counselorGroup.totalAmount]);
    }
  });

  // 添加总计行
  exportData.push(["合计", "", totalVisitCount.value, formatPercentage(1, true), totalDealCount.value, formatPercentage(totalDealCount.value / totalVisitCount.value, true), totalVisitCount.value - totalDealCount.value, formatPercentage((totalVisitCount.value - totalDealCount.value) / totalVisitCount.value, true), totalFirstVisitCount.value || 0, totalFirstDealCount.value || 0, totalRevisitCount.value || 0, totalRevisitDealCount.value || 0, totalReconsumptionCount.value || 0, totalReconsumptionDealCount.value || 0, totalAmount.value]);

  // 创建工作簿和工作表
  const wb = XLSX.utils.book_new();
  const ws = XLSX.utils.aoa_to_sheet(exportData);

  // 设置列宽
  const colWidths = [
    { wch: 15 }, // 咨询师
    { wch: 18 }, // 咨询项目
    { wch: 10 }, // 咨询人次
    { wch: 8 }, // 占比%
    { wch: 10 }, // 成交人次
    { wch: 10 }, // 成功率%
    { wch: 10 }, // 未成交人次
    { wch: 12 }, // 未成交占比%
    { wch: 10 }, // 初诊咨询人次
    { wch: 10 }, // 初诊成交人次
    { wch: 10 }, // 复诊咨询人次
    { wch: 10 }, // 复诊成交人次
    { wch: 10 }, // 再消费咨询人次
    { wch: 10 }, // 再消费成交人次
    { wch: 14 }, // 参考金额
  ];

  ws["!cols"] = colWidths;

  // 合并标题单元格
  ws["!merges"] = [
    { s: { r: 0, c: 0 }, e: { r: 0, c: 14 } }, // 标题行
    { s: { r: 2, c: 0 }, e: { r: 3, c: 0 } }, // 咨询师
    { s: { r: 2, c: 1 }, e: { r: 3, c: 1 } }, // 咨询项目
    { s: { r: 2, c: 2 }, e: { r: 3, c: 2 } }, // 咨询人次
    { s: { r: 2, c: 3 }, e: { r: 3, c: 3 } }, // 占比%
    { s: { r: 2, c: 4 }, e: { r: 2, c: 7 } }, // 成交情况
    { s: { r: 2, c: 8 }, e: { r: 2, c: 9 } }, // 初诊人次
    { s: { r: 2, c: 10 }, e: { r: 2, c: 11 } }, // 复诊人次
    { s: { r: 2, c: 12 }, e: { r: 2, c: 13 } }, // 再消费人次
    { s: { r: 2, c: 14 }, e: { r: 3, c: 14 } }, // 参考金额
  ];

  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(wb, ws, "咨询师项目统计表");

  // 生成Excel文件并下载
  const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });
  const blob = new Blob([excelBuffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });

  // 使用当前日期作为文件名的一部分
  const now = new Date();
  const fileName = `咨询师项目统计表_${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, "0")}${now.getDate().toString().padStart(2, "0")}.xlsx`;

  saveAs(blob, fileName);

  ElMessage.success("导出Excel成功");
};

onMounted(async () => {
  // 确保科室列表已加载
  await deptStore.fetchDeptList();

  // 加载员工列表
  if (staffList.value.length === 0) await getStaffList();

  // 设置默认日期范围为当月
  const end = new Date();
  const start = new Date(end.getFullYear(), end.getMonth(), 1);

  dateRange.value = [formatDate(start), formatDate(end)];

  // 初始加载数据
  fetchCounselorProjectStatistics();
});

// 监听所有筛选条件变化
watch([consultantFilter, developerUserIds, infoSource, deptId, projectIntentIds, consultStages, tradeStatus], () => {
  if (dateRange.value && dateRange.value.length === 2) {
    fetchCounselorProjectStatistics();
  }
});
</script>

<style scoped>
.report-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;
  background-color: #f5f7fa;
  overflow: hidden;
  min-width: 1200px;
}

.report-header {
  background-color: white;
  padding: 15px 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
}

.filter-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
  margin-bottom: 10px;
}

.filter-item {
  display: flex;
  align-items: center;
}

.filter-label {
  white-space: nowrap;
  margin-right: 5px;
  font-size: 14px;
  color: #606266;
}

.filter-actions {
  margin-left: auto;
  display: flex;
  gap: 10px;
}

.date-range {
  min-width: 320px;
}

.dept-select,
.consultant-select,
.channel-select,
.consult-status-select {
  min-width: 180px;
}

:deep(.el-date-editor.el-input__wrapper),
:deep(.el-select) {
  width: 100%;
  flex: 1;
}

.table-container {
  flex: 1;
  margin: 15px 0 0 0;
  background-color: white;
  overflow: auto;
  position: relative;
  border-top: 1px solid #ebeef5;
  border-bottom: 1px solid #ebeef5;
  max-height: 70vh;
}

.performance-table {
  width: 100%;
  min-width: 1750px; 
  border-collapse: collapse;
  font-size: 14px;
  table-layout: fixed;
}

.performance-table th,
.performance-table td {
  border: 1px solid #ebeef5;
  padding: 12px 8px;
  text-align: center;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.performance-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: bold;
  position: sticky;
  top: 0;
  z-index: 10;
}

/* 添加第二行表头的固定样式 */
.performance-table tr:nth-child(2) th {
  top: 40px; /* 根据第一行表头的高度调整 */
  position: sticky;
  z-index: 10;
  background-color: #f5f7fa;
}

.total-row {
  background-color: #ebeef5;
  font-weight: bold;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.subtotal-row {
  background-color: #f8f8f8;
  font-weight: bold;
}

.total-label {
  text-align: right;
  padding-right: 10px;
}

.footer-info {
  background-color: white;
  padding: 10px 20px;
  margin: 0;
  display: flex;
  justify-content: end;
  color: #606266;
  font-size: 14px;
  flex-shrink: 0;
  border-top: 1px solid #ebeef5;
}

.date-limit-info {
  color: #e6a23c;
  font-style: italic;
}

@media print {
  .report-container {
    background-color: white;
    height: auto;
  }

  .report-header {
    box-shadow: none;
  }

  .report-actions,
  .footer-info {
    display: none;
  }
  .table-container {
    margin: 0;
    border: none;
    overflow-x: auto;
    overflow-y: auto;
    height: auto;
    max-height: 60vh;
  }

  .performance-table th,
  .performance-table td {
    font-size: 12px;
    padding: 8px 4px;
  }
}

@media (max-width: 768px) {
  .filter-item {
    max-width: 100%;
  }

  .report-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .filters-row {
    flex-direction: column;
  }

  .filter-item {
    width: 100%;
  }

  .filter-controls {
    margin-top: 10px;
    width: 100%;
    justify-content: space-between;
  }
}

@media (min-width: 1400px) {
  .performance-table .amount {
    min-width: 120px;
    max-width: 150px;
  }
}
</style>
