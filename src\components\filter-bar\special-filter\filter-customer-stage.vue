<template>
  <check-box-filter v-model="modelValue" :label="label" :list="stageList"></check-box-filter>
</template>
<script setup>
import { onMounted } from 'vue';
import { useVModels } from '@vueuse/core';
import { storeToRefs } from "pinia";
import { configStore } from "@/store/config";

import CheckBoxFilter from '../check-box-filter.vue';

const emits = defineEmits(['update:modelValue']);
const props = defineProps({
  label: { type: String, default: '客户阶段' },
  modelValue: { type: Array, default: () => [] }
})

const config = configStore();
const { getStage } = config;
const { modelValue } = useVModels(props, emits);
const { stageList } = storeToRefs(config);
onMounted(() => stageList.value.length === 0 && getStage());
</script>
<style lang="scss" scoped></style>
