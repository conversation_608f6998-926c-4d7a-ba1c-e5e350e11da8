<template>
  <el-dialog append-to-body :model-value="visible" :width="width" :title="title" @close="close">
    <el-scrollbar wrap-class="el-dialog-body-full-scroll">
      <display-form :tempList="showlist" :form="record" />
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button class="w-100px" type="danger" plain @click="remove()">删除</el-button>
        <el-button class="w-100px" type="primary" @click="edit()">编辑</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref } from 'vue'
import { storeToRefs } from 'pinia';
import { ElMessage, ElMessageBox } from 'element-plus';
import { removeMedicalRecord } from "@/api/member.js";
import { memberStore } from "@/store/member";
import useChatBarSub from '@/views/chatBar/chat-bar/useChatBarSub';

import displayForm from '../../form-template/display/index.vue'

const props = defineProps({
  visible: { type: Boolean, default: false },
  title: { type: String, default: "" },
  width: { type: Number },
  tempList: { type: Array, default: () => [] },
  record: { type: Object, default: () => ({}) }
})
const showlist = computed(() => props.tempList.filter(i => {
  if ('referenceField' in i && typeof i.referenceField === 'string') {
    if (props.record[i.referenceField] === i.referenceValue) return true
    return false
  }
  return true
}))


const emits = defineEmits(['close', 'onRemove'])
function close() {
  emits('close')
}

const { trigger } = useChatBarSub();
function edit() {
  close()
  trigger.$invoke('editVisitRecord', props.record)
}

const { memberInfo } = storeToRefs(memberStore());
const loading = ref(false)
async function remove() {
  await ElMessageBox.confirm('确定删除该记录吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
  loading.value = true;
  const { success, message } = await removeMedicalRecord({
    corpId: memberInfo.value.corpId,
    memberId: props.record.memberId,
    medicalType: props.record.medicalType,
    _id: props.record._id,
  });
  loading.value = false;
  if (success) {
    ElMessage.success(message);
    emits("onRemove");
  } else {
    ElMessage.error(message);
  }
}

</script>
<style lang="scss" scoped></style>
