<template>
  <el-dialog :model-value="visible" title="批量共享客户" :width="width" @close="close">
    <el-form class="p-15px" label-suffix="：" :label-width="100">
      <el-text type="danger">共享客户：表示客户档案共享多个团队可见，多个团队可同时为该客户服务。</el-text>
      <el-form-item class="is-required mt-15px" label="共享团队">
        <el-select v-model="newTeamId" class="w-full" clearable placeholder="请选择负责团队">
          <el-option v-for="item in allTeams" :key="item.teamId" :label="item.name" :value="item.teamId"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item class="mb-0" label="责任人">
        <ww-user-select :list="newTeamMemberList" placeholder="请选择责任人" :value="newTeamLeader" @change="changeLeader($event)" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div text-center>
        <el-button type="primary" plain class="w-100px" @click="close()">取消</el-button>
        <el-button :loading="loading" class="w-100px" type="primary" @click="confirm()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import { computed, ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { ElMessage } from "element-plus";
import { updateMember } from "@/api/member";
import { teamStore } from "@/store/team";
import WwUserSelect from "@/components/ww-user-select";
import { transferCustomers } from "@/api/member";
const emits = defineEmits(["close", "change"]);
const props = defineProps({
  title: { type: String, default: "批量共享客户" },
  customers: { type: Object, default: () => ({}) },
  visible: { type: Boolean, default: false },
  width: { type: Number },
});

const loading = ref(false);
const { teams, allTeams, currentTeam } = storeToRefs(teamStore());

const newTeamId = ref("");
const newTeam = computed(() => allTeams.value.find((item) => item.teamId === newTeamId.value));
const newTeamLeader = ref("");
const newTeamMemberList = computed(() => (newTeam.value && Array.isArray(newTeam.value.memberList) ? newTeam.value.memberList : []));
function changeTeam() {
  newTeamLeader.value = "";
}
function changeLeader(val) {
  newTeamLeader.value = val;
}

async function confirm() {
  let query = {
    customerIds: props.customers.map((item) => item._id),
    operationType: "share",
    currentTeamId: currentTeam.value.teamId,
    currentTeamName: currentTeam.value.name,
    targetTeamId: newTeamId.value,
    targetTeamName: newTeam.value.name,
    targetUserId: newTeamLeader.value,
  };

  loading.value = true;
  await transferCustomers(query);
  emits("change");
  ElMessage.success("批量修改成功");

  loading.value = false;
}

function close() {
  emits("close");
}
watch(
  () => props.visible,
  (n) => {
    if (n) {
      newTeamId.value = "";
      newTeamLeader.value = "";
    }
  }
);
</script>