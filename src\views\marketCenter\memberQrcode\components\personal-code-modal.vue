<template>
  <el-dialog :model-value="visible" title="了解员工个人码" :width="560" @close="close()">
    <el-scrollbar wrap-style="max-height: calc(100vh - 360px)">
      <!-- <svg-icon name="qrcode" class="ml-20px mt-15px text-blue-500" :size="50"></svg-icon> -->
      <div color-normal class="my-15px px-15px">
        员工个人码主要是员工手机端展示的加好友二维码，员工可在手机端启用和设置。
      </div>
    </el-scrollbar>
    <template #footer>
      <div text-center>
        <el-button class="w-100px" type="primary" plain @click="close">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import SvgIcon from '@/components/svg-icon';

defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(["close"]);

function close() {
  emit("close");
}
</script>