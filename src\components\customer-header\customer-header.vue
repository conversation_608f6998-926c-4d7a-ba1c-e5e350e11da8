<!-- 页面顶部患者详情 -->
<template>
  <div v-if="customer && customer._id" v-loading="loading" class="bg-white">
    <div class="bg-[#ecf4ff] bg-opacity-30 px-15px py-5px">
      <div class="flex justify-between flex-wrap w-full">
        <div class="mr-10px">
          <div class="flex items-center">
            <div class="text-16px font-semibold leading-30px">{{ customer.name }}</div>
            <div class="ml-10px text-14px leading-30px">{{ customer.sex }} {{ customer.sex && age ? " | " : "" }} {{ age }}</div>
            <div v-if="birthdayStr" :title="birthdayStr">
              <svg-icon class="ml-10px flex-shrink-0" title="" name="cake" size="24"></svg-icon>
            </div>
          </div>
          <div v-if="customer.tagIds && customer.tagIds.length" class="flex flex-nowrap items-center mt-4px">
            <customer-tag v-for="tagId in customer.tagIds.slice(0, 6)" :key="tagId" :tag-id="tagId" class="mb-0" />
            <el-popover :disabled="customer.tagIds.length <= 6" placement="top-start" :width="300" trigger="click">
              <template #reference>
                <span v-if="customer.tagIds.length > 6" class="ml-10px text-gray-400 cursor-pointer">...</span>
              </template>
              <div class="flex flex-wrap">
                <customer-tag v-for="tagId in customer.tagIds" :key="`${tagId}_popover`" :tag-id="tagId" class="flex-shrink-0" />
              </div>
            </el-popover>
          </div>
        </div>
        <div class="flex-shrink-0 flex items-center text-blue-500 text-14px">
          <el-button plain class="ml-10px" size="small" type="primary" @click="bindInhosCustomer" v-if="corpInfo.isConnectHis && !customer.isConnectHis">绑定院内档案</el-button>
          <el-button plain disabled size="small" class="ml-10px cursor-default pointer-events-none" v-if="corpInfo.isConnectHis && customer.isConnectHis">已绑定院内档案</el-button>
          <span class="mr-5px cursor-pointer ml-5px" @click="handleDetail">详情</span>
          <el-icon class="cursor-pointer" @click="handleDetail">
            <DArrowRight />
          </el-icon>
        </div>
      </div>
      <el-row class="mt-5px text-14px" :gutter="16">
        <el-col :span="24" :sm="12" :md="8" :lg="4" :xl="4">
          <div class="flex items-center pr-5px">
            <div class="flex-shrink-0 leading-26px text-gray-500">备注：</div>
            <div class="truncate text-[#FC8352]" :title="customer.notes || ''">{{ customer.notes || "--" }}</div>
          </div>
        </el-col>
        <el-col :span="24" :sm="12" :md="8" :lg="5" :xl="5" v-if="isBenefitManagement">
          <div class="flex items-center pr-5px">
            <div class="flex-shrink-0 leading-26px text-gray-500">所属咨询：</div>
            <div class="truncate">
              <ww-user v-if="customer && customer.counselorRecord && Array.isArray(customer.counselorRecord)" :openid="customer.counselorRecord[customer.counselorRecord.length - 1]?.counselor"></ww-user>
              <span v-else>--</span>
            </div>
          </div>
        </el-col>
        <el-col :span="24" :sm="12" :md="8" :lg="5" :xl="5">
          <div class="flex items-center pr-5px">
            <div class="flex-shrink-0 leading-26px text-gray-500">最近服务：</div>
            <div class="truncate flex-shrink-0 min-w-0">
              {{ customer.serviceTime ? dayjs(customer.serviceTime).format("YYYY-MM-DD HH:mm") : "--" }}
            </div>
          </div>
        </el-col>
        <el-col :span="24" :sm="12" :md="8" :lg="5" :xl="5">
          <div class="flex items-center pr-5px">
            <div class="flex-shrink-0 leading-26px text-gray-500">建档时间：</div>
            <div class="truncate flex-shrink-0 min-w-0">
              {{ customer.createTime ? dayjs(customer.createTime).format("YYYY-MM-DD HH:mm") : "--" }}
            </div>
          </div>
        </el-col>
        <el-col :span="24" :sm="12" :md="8" :lg="5" :xl="5">
          <div class="flex items-center pr-5px">
            <div class="flex-shrink-0 leading-26px text-gray-500">最近下单时间：</div>
            <div class="truncate">{{ lastOrderTime || "--" }}</div>
          </div>
        </el-col>
      </el-row>
      <el-row class="mt-5px text-14px" :gutter="16">
        <el-col :span="24" :sm="12" :md="8" :lg="4" :xl="4">
          <div class="flex items-center pr-5px">
            <div class="flex-shrink-0 leading-26px text-gray-500">共享团队：</div>
            <div v-if="serviceTeams.length" class="flex items-center">
              <el-popover placement="bottom-end" :width="350" trigger="click">
                <template #reference>
                  <div class="truncate mr-4px cursor-pointer hover:text-blue-500">{{ serviceTeams.length }}个团队服务中</div>
                </template>
                <el-scrollbar wrap-style="max-height:40vh">
                  <div v-for="i in serviceTeams" :key="i.teamId" class="py-10px flex border-b border-gray-100">
                    <div class="w-160px flex-shrink-0 truncate" :title="i.name || ''">{{ i.name }}</div>
                    <div class="w-160px flex-shrink-0 truncate">
                      <span class="mx-5px">|</span>
                      责任人：
                      <ww-user v-if="i.corpUserId" :openid="i.corpUserId"></ww-user>
                    </div>
                  </div>
                </el-scrollbar>
              </el-popover>
            </div>
          </div>
        </el-col>
        <el-col :span="24" :sm="12" :md="8" :lg="5" :xl="5" v-if="isBenefitManagement">
          <div class="flex items-center pr-5px">
            <div class="flex-shrink-0 leading-26px text-gray-500">所属开发：</div>
            <div class="truncate">
              <ww-user v-if="customer && customer.introducerRecord && Array.isArray(customer.introducerRecord)" :openid="customer.introducerRecord[customer.introducerRecord.length - 1]?.introducer"></ww-user>
              <span v-else>--</span>
            </div>
          </div>
        </el-col>
        <el-col :span="24" :sm="12" :md="8" :lg="5" :xl="5">
          <div class="flex items-center pr-5px">
            <div class="flex-shrink-0 leading-26px text-gray-500">添加人：</div>
            <div>
              <el-popover placement="bottom-end" :width="420" trigger="click">
                <template #reference>
                  <el-text type="primary cursor-pointer">{{ serviceTimes.length }}人</el-text>
                </template>
                <el-scrollbar wrap-style="max-height:40vh">
                  <div v-for="i in serviceTimes" :key="i.id" class="py-8px flex items-center border-b border-gray-100">
                    <svg-icon class="mr-4px flex-shrink-0" name="wx-work" size="24"></svg-icon>
                    <div class="flex-shrink-0">
                      <ww-user v-if="i.id" :openid="i.id"></ww-user>
                    </div>
                    <div class="flex-shrink-0" v-if="i.executionTime">
                      <span class="mx-10px">|</span>
                      最近服务时间：{{ i.executionTime }}
                    </div>
                  </div>
                </el-scrollbar>
              </el-popover>
            </div>
          </div>
        </el-col>
        <el-col :span="24" :sm="12" :md="8" :lg="5" :xl="5">
          <div class="flex items-center pr-5px">
            <div class="flex-shrink-0 leading-26px text-gray-500">最近到院：</div>
            <div class="truncate">{{ lastVisitTime || "--" }}</div>
          </div>
        </el-col>
        <el-col :span="24" :sm="12" :md="8" :lg="5" :xl="5" v-if="isBenefitManagement">
          <div class="flex items-center pr-5px">
            <div class="flex-shrink-0 leading-26px text-gray-500">累计消费：</div>
            <div class="truncate">{{ totalAmount || "--" }}</div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
  <bind-member :visible="bindCustomerVisible" :list="hisCustomers" :memberId="customer._id" @bindCustomerSuccess="bindCustomer" @cancel="bindCustomerVisible = false"></bind-member>
</template>
<script setup>
import { computed, ref, toRefs, watch } from "vue";
import { storeToRefs } from "pinia";
import dayjs from "dayjs";
import { getCustomerInfoById } from "@/api/member";
import { getLastServiceTime } from "@/api/todo";
import { teamStore } from "@/store/team";
import wxContact from "@/store/wxContact";
import customerTag from "@/components/customer-tag/customer-tag";
import WwUser from "@/components/ww-user/index.vue";
import { memberStore } from "@/store/member";
import { getHisCustomerArchive } from "@/api/customerHisSync";
import bindMember from "@/views/member/components/bind-his-customer.vue";
import { ElMessage, ElMessageBox } from "element-plus";

const emits = defineEmits(["showCustomerDetail", "reload"]);

const { corpInfo } = storeToRefs(memberStore());

const isBenefitManagement = computed(() => {
  return corpInfo.value && corpInfo.value.isBenefitManagement;
});
const props = defineProps({
  customer: { type: Object, default: () => ({}) },
  customerId: { type: String, default: "" },
  request: { type: Boolean, default: false },
});
const customerInfo = ref({});
const customer = computed(() => (props.request ? customerInfo.value : props.customer));

const totalAmount = computed(() => {
  if (!customer.value.consumeRecord || !Array.isArray(customer.value.consumeRecord)) {
    return "0.00";
  }
  const sum = customer.value.consumeRecord.reduce((sum, bill) => {
    return sum + (bill.consumeAmount || 0);
  }, 0);
  return sum.toFixed(2);
});

const loading = ref(false);
const serviceTimes = ref([]);
const contactStore = wxContact();
const { contactFollowUsers } = storeToRefs(contactStore);
const { allTeams } = storeToRefs(teamStore());
const birthdayStr = computed(() => {
  if (customer.value.birthdayStamp && dayjs(customer.value.birthdayStamp).isValid()) {
    return dayjs(customer.value.birthdayStamp).format("YYYY年MM月DD日");
  }
  if (customer.value.birthday && dayjs(customer.value.birthday).isValid()) {
    return dayjs(customer.value.birthday).format("YYYY年MM月DD日");
  }
  return "";
});
const followUser = computed(() => contactFollowUsers.value[customer.value.externalUserId] || []);
const lastVisitTime = computed(() => {
  const inHospitalTimes = customer.value.inHospitalTimes;
  const lastTime = Array.isArray(inHospitalTimes) && inHospitalTimes.length > 0 ? inHospitalTimes[inHospitalTimes.length - 1] : "";
  return lastTime ? dayjs(lastTime).format("YYYY-MM-DD HH:mm") : "";
});
const personResponsibles = computed(() => (customer.value && Array.isArray(customer.value.personResponsibles) ? customer.value.personResponsibles : []));
const serviceTeams = computed(() => {
  const teamIds = customer.value.teamId ? (typeof customer.value.teamId === "string" ? [customer.value.teamId] : customer.value.teamId) : [];
  return teamIds.map((i) => {
    const item = personResponsibles.value.find((j) => j.teamId === i);
    const team = allTeams.value.find((j) => j.teamId === i);
    return { name: team ? team.name : "", teamId: i, corpUserId: item ? item.corpUserId : "" };
  });
});
const serviceTimePayload = computed(() => [customer.value._id, [...followUser.value]]);

// 绑定院内档案相关变量
const bindCustomerVisible = ref(false);
const hisCustomers = ref([]);
const hisOutpatientId = ref(""); // 存储用户输入的HIS门诊号

// 绑定院内档案
async function bindInhosCustomer() {
  // 先判断是否有身份证或客户编号
  if (!customer.value.idCard && !customer.value.customerNumber) {
    // 弹出输入框让用户输入HIS门诊号
    ElMessageBox.prompt("请输入HIS门诊号", "绑定院内档案", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      inputPlaceholder: "请输入门诊号",
      inputValidator: (value) => {
        if (!value) {
          return "门诊号不能为空";
        }
        return true;
      },
    })
      .then(({ value }) => {
        hisOutpatientId.value = value;
        getCustomerArchiveInhos("outpatientId");
      })
      .catch(() => {
        // 用户取消输入
        ElMessage.info("已取消输入");
      });
    return;
  }
  await getCustomerArchiveInhos();
  if (hisCustomers.value.length > 0) {
    bindCustomerVisible.value = true;
  } else {
    ElMessageBox.alert("his系统内未查询到客户档案!", "温馨提示", { type: "warning", confirmButtonText: "知道了" });
  }
}

// 查询HIS系统档案
async function getCustomerArchiveInhos(queryType) {
  let query = {};
  if (queryType === "outpatientId") {
    // 通过门诊号查询
    query = {
      idNo: hisOutpatientId.value,
    };
  } else if (customer.value.idCard) {
    // 通过身份证号查询
    query = {
      idCard: customer.value.idCard,
    };
  } else if (customer.value.customerNumber) {
    // 通过档案编号查询
    query = {
      idNo: customer.value.customerNumber,
    };
  }

  let { success, data } = await getHisCustomerArchive(query);
  hisCustomers.value = success ? data.list : [];

  // 如果查询成功并有结果，显示绑定弹窗
  if (success && data.list && data.list.length > 0) {
    bindCustomerVisible.value = true;
  } else {
    ElMessageBox.alert("his系统内未查询到客户档案!", "温馨提示", { type: "warning", confirmButtonText: "知道了" });
  }
}

// 绑定客户回调
function bindCustomer() {
  bindCustomerVisible.value = false;

  // 如果是request模式，重新获取客户信息
  if (props.request) {
    getCustomerInfo();
  } else {
    // 如果不是request模式，直接更新props.customer的绑定状态
    if (props.customer && typeof props.customer === "object") {
      props.customer.isConnectHis = true;
    }
  }

  // 总是触发reload事件，让父组件更新数据
  emits("reload");
}

function handleDetail() {
  emits("showCustomerDetail", customer.value);
}
async function getCustomerInfo() {
  loading.value = true;
  const { success, data, message } = await getCustomerInfoById(localStorage.getItem("userId"), { _id: props.customerId });
  customerInfo.value = data.data && data.data[0] ? data.data[0] : {};
  loading.value = false;
}

async function getServiceTimes() {
  const res = await getLastServiceTime(customer.value._id, followUser.value);
  const list = res && Array.isArray(res.data.data) ? res.data.data : [];
  serviceTimes.value = followUser.value
    ? followUser.value.map((i) => {
        const item = list.find((item) => item.id === i);
        const executionTime = item && item.executionTime ? dayjs(item.executionTime).format("YYYY-MM-DD") : "";
        return { id: i, executionTime };
      })
    : [];
}

watch(
  () => props.customerId,
  (n) => {
    if (n && props.request) {
      if (customerInfo.value._id !== n) {
        getCustomerInfo(n);
      }
    } else {
      customerInfo.value = {};
    }
  },
  { immediate: true }
);

watch(
  serviceTimePayload,
  (n) => {
    if (n[0] && n[1].length) {
      getServiceTimes();
    } else {
      serviceTimes.value = [];
    }
  },
  { immediate: true }
);
</script>
<style lang="scss" scoped></style>
