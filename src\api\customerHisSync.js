import { post } from "./axios";
async function useCustomerHisSync(data) {
  const res = await post("customerHisSync", data);
  return res;
}

export async function getHisCustomerArchive(query) {
  const res = await useCustomerHisSync({
    type: "getHisCustomerArchive",
    ...query,
  });
  return res;
}

//

export async function getHisFeeRecord(query) {
  const res = await useCustomerHisSync({
    type: "getHisFeeRecord",
    ...query,
  });
  return res;
}
