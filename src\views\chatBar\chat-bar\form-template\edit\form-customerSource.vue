<template>
  <form-cascader v-bind="filteredAttrs" :real-range="customerSourceList" @change="change" />
</template>
<script setup>
import { storeToRefs } from "pinia";
import { memberStore } from '@/store/member';
import formCascader from './form-cascader/form-cascader.vue';
import { useAttrs, computed } from "vue";

const emits = defineEmits(['change']);
const { customerSourceList } = storeToRefs(memberStore());

const attrs = useAttrs();

// 过滤掉 range 属性
const filteredAttrs = computed(() => {
  const { range, ...restAttrs } = attrs;
  return restAttrs;
});

function change(data) {
  emits('change', data);
}


</script>
<style lang="scss" scoped></style>
