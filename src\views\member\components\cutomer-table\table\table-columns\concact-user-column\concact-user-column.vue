<template>
  <el-table-column label="添加人" prop="concactUser" :width="180">
    <template #default="{row:{externalUserId,_id}}">
      <td-wrapper classnames="w-full overflow-hidden">
        <concact-user-list
          v-if="externalUserId && contactFollowUsers[externalUserId] && contactFollowUsers[externalUserId].length"
          :data="contactFollowUsers[externalUserId]" :customerId="_id" />
        <span v-else></span>
      </td-wrapper>
    </template>
  </el-table-column>
</template>
<script setup>
import { storeToRefs } from 'pinia';
import tdWrapper from '../td-wrapper.vue';
import wxContact from '@/store/wxContact';
import concactUserList from './concact-user-list.vue';
const { contactFollowUsers } = storeToRefs(wxContact());
</script>
<style lang="scss" scoped></style>
