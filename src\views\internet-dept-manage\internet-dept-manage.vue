<template>
  <div bg-fff flex class="h-full">
    <div h-full flex-shrink-0 class="w-3/10 min-w-240px max-w-320px border-r border-gray-200">
      <internet-classify-list 
        :checked-cate="current" 
        :data="cateList" 
        :custom="true"
        placeholder="输入院区或科室名称搜索" 
        label="院区/科室"
        @change="changeCurrent($event)" 
        @search="searchCategories($event)" 
        @onHandle="handle">
        <template #header>
          <div color-normal class="text-16px font-semibold mb-15px">互联网科室列表</div>
        </template>
      </internet-classify-list>
    </div>
    <div h-full flex-grow>
      <!-- 页面初始化加载状态 -->
      <div v-if="initialLoading" class="h-full flex items-center justify-center">
        <div class="flex items-center">
          <el-icon class="is-loading mr-2" size="24">
            <Loading />
          </el-icon>
          <span class="text-gray-600">正在加载科室数据...</span>
        </div>
      </div>

      <my-layout v-else-if="current">
        <layout-item>
          <div class="text-16px font-semibold px-15px pt-15px pb-5px">
            {{ current.type === 'district' ? (current.districtName || current.label) : (current.deptName || current.label) }}
          </div>
          <el-tabs v-model="activeName">
            <el-tab-pane name="staff" v-if="current.type === 'dept'">
              <template #label>
                <span class="px-15px">科室人员</span>
              </template>
            </el-tab-pane>
            <el-tab-pane name="detail">
              <template #label>
                <span class="px-15px">{{ current.type === 'district' ? '院区' : '科室' }}详情</span>
              </template>
            </el-tab-pane>
          </el-tabs>
          <div v-show="activeName === 'staff' && current.type === 'dept'" p-15 class="flex items-center justify-between">
            <div class="flex items-center">
              <el-input 
                v-model="searchKeyword" 
                placeholder="搜索姓名" 
                class="w-200px" 
                @input="handleSearchDebounced"
                clearable
              />
            </div>
                <el-button :icon="Plus" type="primary" @click="openSelectMember">添加人员</el-button>
          </div>
        </layout-item>
        
        <!-- 科室人员列表 -->
        <layout-main v-show="activeName === 'staff' && current.type === 'dept'" :scroll="false">
          <el-table stripe height="100%" :data="list" v-loading="loading">
            <el-table-column prop="anotherName" label="姓名" :min-width="100">
              <template #default="{ row }">
                <div class="truncate">{{ row.anotherName || row.name }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="deptNames" label="门诊科室" :min-width="120">
              <template #default="{ row }">
                <div class="truncate">{{ getDeptNames(row.hlwDeptIds) || '-' }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="jobName" label="职称" :min-width="100">
              <template #default="{ row }">
                <div class="truncate">{{ row.title || '-' }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="sortOrder" label="排序" :min-width="80" align="center">
              <template #default="{ row, $index }">
                <div
                  v-if="editingSortId === row.userid"
                  class="inline-flex items-center"
                >
                  <el-input 
                    v-model="editingSortValue"
                    size="small"
                    class="w-60px"
                    @blur="saveSortOrder(row, $index)"
                    @keyup.enter="saveSortOrder(row, $index)"
                    @keyup.esc="cancelSortEdit"
                  />
                </div>
                <span 
                  v-else 
                  class="cursor-pointer"
                  style="color: #374151; transition: color 0.2s;"
                  @click="editSortOrder(row, $index)"
                  @mouseenter="$event.target.style.color = '#3b82f6'"
                  @mouseleave="$event.target.style.color = '#374151'"
                >
                  {{ getSortOrderForDept(row, current._id) || ($index + 1) }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="isRecommended" label="是否推荐" :min-width="100" align="center">
              <template #default="{ row }">
                <el-icon class="text-yellow-500 cursor-pointer" v-if="row.recommended === 1" @click="toggleRecommend(row)">
                  <star-filled />
                </el-icon>
                <el-icon class="text-gray-300 cursor-pointer" v-else @click="toggleRecommend(row)">
                  <star />
                </el-icon>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="160">
              <template #default="{ row }">
                <el-text class="cursor-pointer mr-5px" type="primary" @click="viewDetail(row)">详情</el-text>
                <el-text class="cursor-pointer mr-5px" type="primary" @click="editStaff(row)">编辑</el-text>
                <el-text class="cursor-pointer" type="primary" @click="moveStaff(row)">移除</el-text>
              </template>
            </el-table-column>
          </el-table>
          

        </layout-main>
        
        <!-- 详情页面 -->
        <layout-main v-if="currentItem" v-show="activeName === 'detail'">
          <div class="px-15px">
            <el-form label-suffix="：" label-width="90" label-position="left">
              <template v-if="current.type === 'district'">
                <el-form-item label="院区名称">{{ currentItem.districtName }}</el-form-item>
                <el-form-item label="院区ID">{{ currentItem.districtId }}</el-form-item>
                <el-form-item label="院区介绍">{{ currentItem.districtDesc }}</el-form-item>
              </template>
              <template v-else>
                <el-form-item label="科室名称">{{ currentItem.deptName }}</el-form-item>
                <el-form-item label="上一级节点">
                  <span>{{ currentItem.parentName || '-' }}</span>
                </el-form-item>
                <el-form-item label="科室ID">{{ currentItem.deptId }}</el-form-item>
                <el-form-item label="科室介绍">{{ currentItem.deptDesc }}</el-form-item>
              </template>
            </el-form>
          </div>
        </layout-main>
      </my-layout>
    </div>
  </div>
  
  <!-- 模态框 -->
  <internet-modal :visible="visible" :width="width" :item="editItem" @close="close" @change="getCateList()" />
  <add-staff-modal :dept="currentItem" :visible="staffVisible" :width="staffWidth" @close="staffClose"
    @change="getList()" />
  <change-dept-modal :cateList="cateList" :staff="currentStaff" :visible="changeDeptVisible" :width="changeDeptWidth"
    @close="changeDeptClose" @change="getList()" />
  <select-member 
    ref="selectMemberRef" 
    @getSelectItems="onSelectMemberConfirm" 
    @close="onSelectMemberClose" 
  />
  <staff-detail-modal 
    :visible="staffDetailVisible"
    :staff="currentStaffDetail"
    :readonly="currentStaffDetail._readonly"
    @close="staffDetailClose"
    @update="onStaffDetailUpdate"
  />
</template>

<script setup>
import { computed, ref, onMounted, nextTick } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { debounce } from 'lodash-es';
import useModal from "@/hooks/useModal";
import { 
  getInternetDeptList, 
  addDistrict, 
  addDept, 
  deleteDistrict, 
  deleteDept, 
  updateDistrict, 
  updateDept, 
  sortInternetDeptList, 
  sortHospitalAreaList,
  getHlwHospitalMembersByDept,
  addHlwMembers,
  updateMemberRecommendStatus,
  removeHlwMemberFromDept,
  batchUpdateMembersSortOrder
} from "@/api/internet-dept-manage";

import { Plus, StarFilled, Star, Loading } from "@element-plus/icons-vue";
import useElPagination from "@/hooks/useElPagination";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import internetClassifyList from "./components/internet-classify-list.vue";
import internetModal from "./components/internet-modal.vue";
import addStaffModal from "./components/add-staff-modal.vue";
import changeDeptModal from "./components/change-dept-modal.vue";
import SelectMember from '@/components/select-member/index.vue';
import StaffDetailModal from './components/staff-detail-modal.vue';

// 模态框控制
const { close, show, visible, width } = useModal(640);
const { close: staffClose, show: staffShow, visible: staffVisible, width: staffWidth } = useModal(640);
const { close: changeDeptClose, show: changeDeptShow, visible: changeDeptVisible, width: changeDeptWidth } = useModal(640);
const selectMemberRef = ref(null);

// 员工详情弹窗控制
const { close: staffDetailClose, show: staffDetailShow, visible: staffDetailVisible, width: staffDetailWidth } = useModal(680);
const currentStaffDetail = ref({});

// 状态管理
const activeName = ref("detail");
const allStaffList = ref([]); // 存储完整的员工列表
const list = computed(() => {
  let staffList = [...allStaffList.value];
  
  // 根据当前科室使用sortOrder对象格式排序
  if (current.value && current.value.type === 'dept') {
    const currentDeptId = current.value._id;
    
    staffList = staffList.sort((a, b) => {
      const sortOrderA = getSortOrderForDept(a, currentDeptId);
      const sortOrderB = getSortOrderForDept(b, currentDeptId);
      
      if (sortOrderA > 0 && sortOrderB > 0) {
        return sortOrderA - sortOrderB;
      }
      
      if (sortOrderA > 0 && sortOrderB === 0) {
        return -1;
      }
      if (sortOrderA === 0 && sortOrderB > 0) {
        return 1;
      }
      
      const timeA = new Date(a.createTime || 0).getTime();
      const timeB = new Date(b.createTime || 0).getTime();
      return timeA - timeB;
    });
  }
  
  // 搜索过滤 - 使用corp-member字段
  if (searchKeyword.value.trim()) {
    staffList = staffList.filter(staff => {
      const name = staff.anotherName || staff.name || '';
      return name.toLowerCase().includes(searchKeyword.value.toLowerCase());
    });
  }

  // 过滤掉无效数据 - 使用corp-member字段
  staffList = staffList.filter(staff => staff && staff.userid && (staff.anotherName || staff.name));
  
  return staffList;
});
const searchKeyword = ref('');
const currentStaff = ref({});
const loading = ref(false);
const initialLoading = ref(true); // 页面初始化加载状态
const editItem = ref({});
const cateList = ref([]);
const current = ref(null);

// 排序编辑相关
const editingSortId = ref(null);
const editingSortValue = ref('');

// 防抖搜索
const handleSearchDebounced = debounce(() => {
  // 搜索逻辑通过computed实现
}, 300);

// 获取指定科室的排序值 - 使用corp-member字段
function getSortOrderForDept(member, deptId) {
  if (!member || !deptId) return 0;

  // 优先使用currentSortOrder（API返回的当前科室排序值）
  if (member.currentSortOrder && typeof member.currentSortOrder === 'number') {
    return member.currentSortOrder;
  }

  // 使用hlwSortOrder对象格式
  if (member.hlwSortOrder && typeof member.hlwSortOrder === 'object') {
    return member.hlwSortOrder[deptId] || 0;
  }

  // 兼容旧的sortOrder字段
  if (member.sortOrder && typeof member.sortOrder === 'object') {
    return member.sortOrder[deptId] || 0;
  }

  return 0;
}

// 获取分类列表
async function getCateList() {
  try {
    const result = await getInternetDeptList();

    if (result.success) {
      const rawList = result.data.list || [];

      // 直接使用后端数据，保持原有的type字段
      cateList.value = rawList.map(item => {
        const convertedItem = { ...item };

        // 根据type设置正确的label
        if (item.type === 'district') {
          // 院区数据
          convertedItem.label = item.districtName || item.label;
        } else {
          // 科室数据
          convertedItem.label = item.hlwDeptName || item.deptName || item.label;
          // 确保保留所有科室名称字段
          convertedItem.deptName = item.hlwDeptName || item.deptName;
          convertedItem.hlwDeptName = item.hlwDeptName;
        }

        return convertedItem;
      });



      if (current.value && !cateList.value.find(item => item._id === current.value._id)) {
        current.value = null;
        allStaffList.value = [];
      }
    } else {
      ElMessage.error(result.message || "获取分类列表失败");
    }
  } catch (error) {
    ElMessage.error("获取分类列表失败");
  } finally {
    // 初始化完成，隐藏加载状态
    initialLoading.value = false;
  }
}

// 切换当前选中项
function changeCurrent(item) {
  current.value = item;
  activeName.value = item.type === 'dept' ? 'staff' : 'detail';
  if (item.type === 'dept') {
    getList();
  }
}

// 搜索分类
function searchCategories(keyword) {
  // 搜索功能在组件内部实现
}

// 计算当前选中项的详细信息
const currentItem = computed(() => {
  if (!current.value || !current.value._id) return null;
  return cateList.value.find(item => item._id === current.value._id);
});

const { page, pageSize, changePage, changeSize } = useElPagination(getList);

// 处理编辑和删除
async function handle(data = {}) {
  if (data._type === 'add') {
    editItem.value = { type: 'district' };
    show();
  } else if (data._type === 'addChild') {
    editItem.value = { 
      type: 'dept', 
      parentId: data.parentId,
      parentName: data.parentName,
      parentType: data.parentType
    };
    show();
  } else if (data._type === 'edit') {
    editItem.value = data;
    show();
  } else if (data._type === 'delete') {
    await handleDelete(data);
  } else if (data.action === 'sort') {
    await handleSort(data.data);
  } else {
    editItem.value = data;
    show();
  }
}

// 处理删除操作
async function handleDelete(item) {
  try {
    const itemName = item.type === 'district' ? '院区' : '科室';
    
    await ElMessageBox.confirm(
      `确认删除${itemName}【${item.label}】吗？删除后不可恢复！`,
      `删除${itemName}`,
      {
        confirmButtonText: "确定删除",
        cancelButtonText: "取消",
        type: "warning"
      }
    );
    
    let result;
    if (item.type === 'district') {
      result = await deleteDistrict(item._id);
    } else {
      result = await deleteDept(item._id);
    }
    
    if (result.success) {
      ElMessage.success(result.message || "删除成功");
      if (current.value && current.value._id === item._id) {
        current.value = null;
      }
      getCateList();
    } else {
      ElMessage.error(result.message || "删除失败");
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error("删除失败");
    }
  }
}

// 处理排序操作
async function handleSort(sortData) {
  try {
    const firstItem = sortData[0];
    const itemInList = cateList.value.find(item => item._id === firstItem._id);
    
    let result;
    if (itemInList && itemInList.type === 'district') {
      result = await sortHospitalAreaList(sortData);
    } else {
      result = await sortInternetDeptList(sortData);
    }
    
    if (result.success) {
      ElMessage.success("排序成功");
      getCateList();
    } else {
      ElMessage.error(result.message || "排序失败");
    }
  } catch (error) {
    ElMessage.error("排序失败");
  }
}

// 获取员工列表
async function getList() {
  if (current.value && current.value._id && current.value.type === 'dept') {
    loading.value = true;
    try {
      const corpId = getCurrentCorpId();
      if (!corpId) {
        ElMessage.error('未获取到企业ID，请重新登录');
        allStaffList.value = [];
        return;
      }
      
      const result = await getHlwHospitalMembersByDept(corpId, current.value.deptId);

      if (result.success && result.data) {
        // 根据API文档，data应该直接是数组
        let memberList = Array.isArray(result.data) ? result.data : [];

        // 过滤掉无效数据 - 使用corp-member字段
        memberList = memberList.filter(member => {
          return member && member.userid && (member.anotherName || member.name);
        });
        allStaffList.value = memberList;
        searchKeyword.value = '';
      } else {
        allStaffList.value = [];
        ElMessage.error(result.message || "获取员工列表失败");
      }
    } catch (error) {
      allStaffList.value = [];
      ElMessage.error("获取员工列表失败");
    } finally {
      loading.value = false;
    }
  } else {
    allStaffList.value = [];
  }
}

// 查看详情
function viewDetail(staff) {
  currentStaffDetail.value = { ...staff, _readonly: true };
  staffDetailShow();
}

// 编辑员工
function editStaff(staff) {
  currentStaffDetail.value = { ...staff, _readonly: false };
  staffDetailShow();
}

// 移除员工
async function moveStaff(staff) {
  try {
    await ElMessageBox.confirm("确认是否把该员工移除？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    });
    
    const corpId = getCurrentCorpId();
    if (!corpId) {
      ElMessage.error('未获取到企业ID，请重新登录');
      return;
    }
    
    // 检查是否有当前选中的科室
    if (!current.value || current.value.type !== 'dept') {
      ElMessage.error('请先选择要移除员工的科室');
      return;
    }
    
    const currentDeptId = current.value.deptId;
    const result = await removeHlwMemberFromDept(staff.userid, corpId, currentDeptId);
    
    if (result.success) {
      ElMessage.success("移除成功");
      getList();
    } else {
      ElMessage.error(result.message || "移除失败");
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error("移除失败");
    }
  }
}

// 切换推荐状态
async function toggleRecommend(staff) {
  try {
    const corpId = getCurrentCorpId();
    if (!corpId) {
      ElMessage.error('未获取到企业ID，请重新登录');
      return;
    }
    
    const newStatus = staff.recommended === 1 ? 0 : 1;

    const result = await updateMemberRecommendStatus(staff.userid, corpId, newStatus);

    if (result.success) {
      staff.recommended = newStatus;
      ElMessage.success(`已${newStatus === 1 ? '设为' : '取消'}推荐`);
    } else {
      ElMessage.error(result.message || "操作失败");
    }
  } catch (error) {
    ElMessage.error("操作失败");
  }
}

// 排序编辑相关方法 - 使用corp-member字段
function editSortOrder(staff, currentIndex) {
  editingSortId.value = staff.userid;
  const currentDeptId = current.value._id;
  const currentSortOrder = getSortOrderForDept(staff, currentDeptId);
  editingSortValue.value = (currentSortOrder || (currentIndex + 1)).toString();
}

function cancelSortEdit() {
  editingSortId.value = null;
  editingSortValue.value = '';
}

async function saveSortOrder(staff, currentIndex) {
  // 防止重复调用
  if (editingSortId.value !== staff.userid) {
    return;
  }

  const newSortValue = editingSortValue.value.trim();

  if (!newSortValue) {
    cancelSortEdit();
    return;
  }

  // 检查是否为数字格式
  if (!/^\d+$/.test(newSortValue)) {
    ElMessage.error('请输入有效的正整数');
    editingSortValue.value = (currentIndex + 1).toString();
    return;
  }

  const sortNumber = parseInt(newSortValue);
  if (isNaN(sortNumber) || sortNumber <= 0) {
    ElMessage.error('排序号必须是正整数');
    editingSortValue.value = (currentIndex + 1).toString();
    return;
  }

  const maxSort = list.value.length;
  const targetIndex = Math.min(sortNumber, maxSort) - 1;

  if (targetIndex === currentIndex) {
    cancelSortEdit();
    return;
  }

  // 立即清除编辑状态，防止重复调用
  cancelSortEdit();

  try {
    await updateStaffSortOrder(staff, targetIndex, currentIndex);
    debugger
    ElMessage.success('排序更新成功');
    // updateStaffSortOrder 已经更新了本地数据，不需要重新获取
  } catch (error) {
    ElMessage.error('排序更新失败');
    // 排序失败时刷新列表，恢复原始状态
    await getList();
  }
}

async function updateStaffSortOrder(staff, targetIndex, currentIndex) {
  const corpId = getCurrentCorpId();
  const deptId = current.value.deptId;
  
  if (!corpId) {
    throw new Error('未获取到企业ID，请重新登录');
  }
  
  if (!deptId) {
    throw new Error('科室ID不能为空');
  }
  
  const currentList = [...list.value];
  const movingItem = currentList.splice(currentIndex, 1)[0];
  currentList.splice(targetIndex, 0, movingItem);
  
  const members = currentList.map((item, index) => ({
    userid: item.userid,
    sortOrder: index + 1
  }));
  
  const result = await batchUpdateMembersSortOrder(corpId, deptId, members);
  
  if (!result.success) {
    throw new Error(result.message || '排序更新失败');
  }
  
  // 更新本地数据的排序值 - 使用corp-member字段
  currentList.forEach((item, index) => {
    // 更新hlwSortOrder对象格式
    if (!item.hlwSortOrder) {
      item.hlwSortOrder = {};
    }
    item.hlwSortOrder[deptId] = index + 1;

    // 同时更新currentSortOrder字段
    item.currentSortOrder = index + 1;
  });

  // 更新全局员工列表中的数据
  allStaffList.value = allStaffList.value.map(staffItem => {
    const updatedItem = currentList.find(item => item.userid === staffItem.userid);
    return updatedItem || staffItem;
  });
}

// 打开选择成员弹窗
function openSelectMember() {
  selectMemberRef.value.openDialog([], [], false);
}

// 处理选择成员确认
async function onSelectMemberConfirm({ checkedMemberList }) {
  if (!checkedMemberList || !checkedMemberList.length) {
    ElMessage.warning('请选择要添加的人员');
    return;
  }

  if (!current.value || current.value.type !== 'dept') {
    ElMessage.error('请先选择一个科室');
    return;
  }

  try {
    const corpId = getCurrentCorpId();
    if (!corpId) {
      ElMessage.error('未获取到企业ID，请重新登录');
      return;
    }
    
    // 使用业务ID而不是MongoDB _id
    const hlwDeptId = current.value.deptId || current.value._id;

    // 构建新API格式的成员数据 - 只传递必要字段，保留用户现有的职称、擅长等信息
    const members = checkedMemberList.map(memberUserId => ({
      userid: memberUserId,
      hlwDeptId: hlwDeptId,
      sortOrder: 1, // 新成员默认排序为1
      recommended: 0 // 默认不推荐
      // 不传递 title 和 specialtyFields，让后端保留用户现有信息
    }));

    // 调用新的addHlwMembers API
    const result = await addHlwMembers(corpId, members);

    if (result.success) {
      ElMessage.success(`成功添加 ${checkedMemberList.length} 名人员`);
      getList(); // 刷新列表
    } else {
      ElMessage.error(result.message || '添加人员失败');
    }
  } catch (error) {
    ElMessage.error('添加人员失败');
  }
}

// 关闭选择成员弹窗
function onSelectMemberClose() {
  // 组件内部处理关闭
}

// 处理员工详情更新
function onStaffDetailUpdate(updatedStaff) {
  // 重新获取人员列表以确保数据一致性
  getList();
  // 不在这里显示成功消息，因为staff-detail-modal已经显示了
}

// 获取当前企业ID
function getCurrentCorpId() {
  return localStorage.getItem('corpId');
}

// 根据科室ID获取科室名称
function getDeptNames(deptIds) {
  if (!deptIds || !Array.isArray(deptIds) || deptIds.length === 0) {
    return '-';
  }



  const deptNames = deptIds.map(deptId => {
    // 兼容性支持：支持多种ID字段匹配
    const dept = cateList.value.find(item =>
      item.type === 'dept' && (
        item.hlwDeptId === deptId ||  // 优先使用hlwDeptId匹配（新标准）
        item.deptId === deptId ||     // 兼容deptId匹配
        item._id === deptId           // 兼容_id匹配
      )
    );

    if (dept) {
      return dept.deptName || dept.hlwDeptName;
    } else {
      return deptId;
    }
  }).filter(name => name);

  return deptNames.length > 0 ? deptNames.join(', ') : '-';
}

onMounted(() => {
  getCateList();
});
</script>

<style scoped lang="scss">
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.w-200px {
  width: 200px;
}

.text-yellow-500 {
  color: #f59e0b;
}

.text-gray-300 {
  color: #d1d5db;
}

.text-gray-700 {
  color: #374151;
}

.text-orange-600 {
  color: #ea580c;
}

.bg-yellow-100 {
  background-color: #fef3c7;
}

.border-yellow-200 {
  border-color: #fed7aa;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.mb-8px {
  margin-bottom: 8px;
}

.ml-20px {
  margin-left: 20px;
}

.p-15px {
  padding: 15px;
}

:deep(.layout-main) {
  position: relative;
}

:deep(.el-form-item__label) {
  text-align: left !important;
  white-space: nowrap;
  display: inline-block;
  line-height: 1.5;
}

:deep(.el-form-item__content) {
  display: inline-block;
  vertical-align: top;
  line-height: 1.5;
}

.w-60px {
  width: 60px;
}

:deep(.el-table .el-table__fixed-right) {
  box-shadow: none !important;
}

:deep(.el-table .el-table__fixed-right::before) {
  display: none !important;
}
</style>
