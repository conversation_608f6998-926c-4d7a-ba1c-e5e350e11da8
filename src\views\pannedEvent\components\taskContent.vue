<template>
  <div class="mt-10px">
    <el-form label-width="120px">
      <el-form-item label="计划事项描述:">
        {{ detial.taskContent }}
      </el-form-item>
      <el-form-item label="计划执行时间:">{{ timeTitle }} {{ detial.planTime }} 天</el-form-item>
      <el-form-item label="发送内容:">
        {{ detial.pannedSendContent }}
      </el-form-item>
      <el-form-item label="发送附件:">
        <div v-if="detial.pannedEventSendFile && detial.pannedEventSendFile.name" @click="readFile()">
          <div class="flex pointer" style="align-items: center" @click="readFile(detial.pannedEventSendFile)">
            <div style="color: #006eff">
              {{ detial.pannedEventSendFile.name }}
            </div>
            <el-icon color="#006eff">
              <Tickets />
            </el-icon>
          </div>
        </div>
      </el-form-item>
      <!-- <el-button
        class="ml-20px"
        type="primary"
        @click="edit"
        v-if="!detial.executionSatus"
        >修改任务内容</el-button
      > -->
    </el-form>
    <el-dialog v-model="dialogVisible" title="修改任务内容" draggable :width="600">
      <div class="p-20px">
        <el-form :rules="rules" :model="ruleForm" label-width="120px" ref="ruleFormRef">
          <el-form-item label="计划事项描述:">
            <el-input class="pl-10px" v-model="ruleForm.taskContent" placeholder="请输入计划事项描述" />
          </el-form-item>
          <el-form-item label="计划执行时间:" prop="planTime">
            <div>
              {{ timeTitle }}
              <el-input class="w-60px pl-10px" type="number" v-model="ruleForm.planTime" @input="changePlanTime" />
              天
            </div>
          </el-form-item>
          <el-form-item label="发送内容:" prop="pannedSendContent">
            <el-input class="w-500px" v-model="ruleForm.pannedSendContent" maxlength="100" :rows="3" show-word-limit type="textarea" placeholder="请输入文字提醒" />
          </el-form-item>
          <el-form-item label="上传附件:">
            <div v-if="pannedEventSendFile.name">
              <div class="flex pointer" style="align-items: center">
                <div class="flex" style="align-items: center" @click="readFile(pannedEventSendFile)">
                  <div style="color: #006eff">
                    {{ pannedEventSendFile.name }}
                  </div>
                  <el-icon color="#006eff">
                    <Tickets />
                  </el-icon>
                </div>

                <el-icon color="red" class="ml-10px text-14px pointer" @click="removeDetele">
                  <Delete />
                </el-icon>
              </div>
            </div>
            <el-popover placement="top" :width="300" trigger="hover" v-else>
              <template #reference>
                <el-button mr-15 type="primary" :icon="Plus">新增附件</el-button>
              </template>
              <div flex justify-center>
                <el-button mr-15 plain type="primary" :icon="Plus" @click="choose('article')">宣教文章</el-button>
                <el-button mr-15 plain type="primary" :icon="Plus" @click="choose('questionnaire')">问卷调查</el-button>
              </div>
            </el-popover>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogCancel">取消</el-button>
          <el-button type="primary" @click="dialogSubmit(ruleFormRef)">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
  <add-file ref="addFileRefs" @onGetSelectFile="getSelectFile"></add-file>
  <survery-dialog :data="currentSurvery" :visible="visible" @close="visible = false" />
</template>
<script setup>
import { computed, ref, reactive } from "vue";
import addFile from "./addFile.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { updatePannedEvent, getCustomerType } from "@/api/member";
import SurveryDialog from "@/views/system/survery/dialog.vue";
import { getDetail } from "@/api/survery";
const visible = ref(false);
const currentSurvery = ref({});
const timeTitleObj = ref({});

const emit = defineEmits(["onGetData"]);
const dialogVisible = ref(false);
const props = defineProps({
  detial: { type: Object, default: {} },
  pannedEventResult: { type: Object, default: {} },
});
const addFileRefs = ref("");
const pannedEventSendFile = ref({
  name: "",
});
const detial = computed(() => {
  return props.detial;
});
const timeTitle = computed(() => {
  return timeTitleObj.value[detial.value.customerType];
});

let ruleForm = ref({
  planTime: "",
  pannedSendContent: "",
  taskContent: "",
});
customerTypeAction();
async function customerTypeAction() {
  let { data, success } = await getCustomerType();
  if (success) {
    const list = data.data;
    list.forEach((item) => {
      timeTitleObj.value[item.type] = item.timeTitle;
    });
  }
}
const time = computed(() => {
  return 0;
});
const ruleFormRef = ref("");
let rules = reactive({
  planTime: [{ required: true, message: "请输入计划执行时间", trigger: "blur" }],
  pannedSendContent: [{ required: true, message: "请输入计划发送内容", trigger: "blur" }],
});
function changePlanTime(e) {
  if (e < 0) ruleForm.planTime = 0;
}
function choose(type) {
  addFileRefs.value.open(type, pannedEventSendFile.value);
}

async function edit() {
  dialogVisible.value = true;
  const { planTime, pannedSendContent, taskContent, pannedEventSendFile: file } = detial.value;
  pannedEventSendFile.value = file || {};
  ruleForm.value = {
    planTime,
    pannedSendContent,
    taskContent,
  };
}

function dialogCancel() {
  dialogVisible.value = false;
}

async function dialogSubmit(formEl) {
  if (!formEl) return;
  await formEl.validate(async (valid, fields) => {
    if (valid) {
      await updatePannedEventAction();
      emit("onGetData", detial.value._id);
      ElMessage.success("更新成功!");
      dialogVisible.value = false;
    }
  });
}

// 获取问卷

async function readFile(item) {
  if (item.type === "questionnaire") {
    await getSurvery(item.serveryId);
    visible.value = true;
  }
}

async function getSurvery(id) {
  const corpId = localStorage.getItem("corpId");
  const { success, data, message } = await getDetail(corpId, id);
  currentSurvery.value = data && data.data ? data.data : {};
}

async function updatePannedEventAction() {
  let params = ruleForm.value;
  if (pannedEventSendFile.value && pannedEventSendFile.value) {
    params["pannedEventSendFile"] = pannedEventSendFile.value;
  }
  await updatePannedEvent(detial.value._id, params);
}

function getSelectFile(file) {
  pannedEventSendFile.value = file;
}

// 删除附件
function removeDetele() {
  ElMessageBox.confirm("是否删除?").then(async () => {
    pannedEventSendFile.value = {};
  });
}
</script>
<style scoped></style>
