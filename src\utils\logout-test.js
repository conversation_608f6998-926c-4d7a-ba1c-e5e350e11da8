// 退出登录功能测试工具
// 用于验证退出登录是否完全清理了用户状态

export function testLogoutCleanup() {
  console.log("开始测试退出登录清理功能...");
  
  // 1. 记录退出前的状态
  const beforeLogout = {
    localStorage: { ...localStorage },
    sessionStorage: { ...sessionStorage },
    // 可以添加更多状态检查
  };
  
  console.log("退出前状态:", beforeLogout);
  
  // 2. 检查应该被清理的项目
  const shouldBeCleared = [
    // localStorage 项目
    'userId',
    'corpId',
    'currentTeamId',
    // sessionStorage 项目
    'doctorNo',
    'wx-contact-store',
    'CORPVISITPLAN_CATEID',
    'CORPVISITPLAN_PAGE'
  ];
  
  return {
    beforeLogout,
    shouldBeCleared,
    checkCleanup: () => {
      console.log("检查清理结果...");
      
      const afterLogout = {
        localStorage: { ...localStorage },
        sessionStorage: { ...sessionStorage },
      };
      
      console.log("退出后状态:", afterLogout);
      
      // 检查应该被清理的项目
      const notCleared = [];
      
      shouldBeCleared.forEach(key => {
        if (localStorage.getItem(key) !== null) {
          notCleared.push(`localStorage.${key}`);
        }
        if (sessionStorage.getItem(key) !== null) {
          notCleared.push(`sessionStorage.${key}`);
        }
      });
      
      // 检查应该保留的项目
      const shouldBePreserved = ['rememberUsername', 'lastUsername'];
      const wronglyCleared = [];
      
      shouldBePreserved.forEach(key => {
        if (beforeLogout.localStorage[key] && !localStorage.getItem(key)) {
          wronglyCleared.push(`localStorage.${key}`);
        }
      });
      
      console.log("清理结果报告:");
      console.log("- 未清理的项目:", notCleared.length > 0 ? notCleared : "无");
      console.log("- 错误清理的项目:", wronglyCleared.length > 0 ? wronglyCleared : "无");
      
      return {
        success: notCleared.length === 0 && wronglyCleared.length === 0,
        notCleared,
        wronglyCleared,
        afterLogout
      };
    }
  };
}

// 在开发环境中可以使用的测试函数
export function simulateUserData() {
  // 模拟用户登录后的数据
  localStorage.setItem('userId', 'test-user-123');
  localStorage.setItem('corpId', 'test-corp-456');
  localStorage.setItem('currentTeamId', 'test-team-789');
  localStorage.setItem('rememberUsername', 'true');
  localStorage.setItem('lastUsername', 'testuser');
  
  sessionStorage.setItem('doctorNo', 'doc-001');
  sessionStorage.setItem('wx-contact-store', JSON.stringify({ test: 'data' }));
  sessionStorage.setItem('CORPVISITPLAN_CATEID', 'plan-123');
  
  console.log("模拟用户数据已设置");
}

// 使用方式：
// 1. 在浏览器控制台中运行 simulateUserData() 模拟用户数据
// 2. 运行 const test = testLogoutCleanup() 开始测试
// 3. 执行退出登录操作
// 4. 运行 test.checkCleanup() 检查清理结果 