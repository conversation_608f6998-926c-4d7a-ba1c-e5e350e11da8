import { inject, onMounted, onUnmounted, ref, watch } from 'vue';

export default function useChatBarSub(eventName, callback) {
  const trigger = inject("side-bar-event-trigger", {});
  const name = ref('');

  onMounted(() => sub(eventName, callback));
  onUnmounted(() => name.value && trigger.$off(name.value));

  function sub(eventName, callback) {
    if (eventName && typeof callback === 'function') {
      name.value = eventName;
      trigger.$on(eventName, callback)
    }
  }

  watch(name, (newName, oldName) => {
    if (oldName) trigger.$off(oldName);
  })

  return { sub, trigger }
}