<template>
  <filter-item label="性别" :text="text" :width="100">
    <radio-group v-model="sex" v-model:text="text" :list="list" />
  </filter-item>
</template>
<script setup>
import { ref } from 'vue';
import FilterItem from './filter-item.vue';
import radioGroup from './radio-group.vue';

const list = [{ label: '男', value: '男' }, { label: '女', value: '女' }];
const sex = ref('');
const text = ref('');

function getParam() {
  return sex.value ? { sex: sex.value } : {}
}

function reset() {
  sex.value = ''
}

defineExpose({
  getParam,
  reset
})
</script>
<style lang="scss" scoped></style>
