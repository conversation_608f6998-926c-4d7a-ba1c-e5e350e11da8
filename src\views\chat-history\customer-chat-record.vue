<template>
  <div color-normal class="flex flex-col h-full text-14px bg-white rounded-t-r rounded-b-r">
    <div class="flex-shrink-0 flex justify-between px-15px py-12px">
      <div class="flex w-full">
        <div class="w-0 flex-grow truncate font-semibold text-15px flex" title="仅展示最多90天的聊天记录">
          “
          <ww-user :openid="memberUserId" v-if="memberUserId"></ww-user>
          ” 与客户 “{{ customerName }}” 的聊天记录
          <span class="font-normal text-12px text-gray-500">（仅展示最多90天的聊天记录）</span>
        </div>
      </div>
    </div>
    <div class="flex-grow relative bg-[#fefefe]">
      <div class="absolute inset-0 bg-[#fefefe]" v-loading="recordLoading">
        <div v-if="!isDev" ref="chatRecordRef" class="w-full h-full"></div>
        <DynamicScroller v-else class="h-full yc-scrollbar" :items="chatCustomerRecord" :min-item-size="54" key-field="_id">
          <template v-slot="{ item, index, active }">
            <DynamicScrollerItem :item="item" :min-item-size="100" :active="active" :data-index="index">
              <div v-if="item === 'isEmpty'" class="h-100px"></div>
              <div v-else-if="item.senderType === 2" class="dy-item max-w-4/5 py-10px px-15px">
                <div class="flex items-center">
                  <div class="flex-shrink text-14px font-semibold truncate">
                    {{ customerUserId }}
                  </div>
                  <div class="flex-shrink-0 mx-5px text-12px text-[#66CB70]">@微信</div>
                  <div class="flex-shrink-0 pl-5px text-12px">{{ item.sendTime }}</div>
                </div>
                <div class="bg-white mt-10px p-10px rounded-8px shadow-lg inline-block break-all leading-24px text-14px max-w-full">
                  {{ item.msgid }}
                </div>
              </div>
              <div v-else-if="item.senderType === 1" class="dy-item ml-auto max-w-4/5 text-right py-10px px-15px">
                <div class="flex items-center justify-end">
                  <div class="flex-shrink-0 pl-5px text-12px">{{ item.sendTime }}</div>
                  <div class="flex-shrink text-14px font-semibold truncate mx-5px">
                    <ww-user v-if="item.memberUserId" :openid="item.memberUserId"></ww-user>
                  </div>
                  <div class="flex-shrink-0 ml-5px text-12px text-orange-500">@员工</div>
                </div>
                <div class="bg-white mt-10px p-10px rounded-8px shadow-lg inline-block break-all leading-24px text-14px max-w-full">
                  {{ item.msgid }}
                </div>
              </div>
            </DynamicScrollerItem>
          </template>
        </DynamicScroller>
      </div>
    </div>
    <customer-detail v-if="customerId" :visible="customerDetailVisible" @close="customerDetailVisible = false" :customerId="customerId" title="客户详情" customerType="corpCustomer" />
  </div>
</template>
  <script setup>
import { computed, ref, onMounted, nextTick, watch } from "vue";
import { storeToRefs } from "pinia";
import * as ww from "@/libs/@wecom/jssdk/wecom.esm.js";
import { useElementBounding } from "@vueuse/core";
import { getCorpMemberChatRecord, getCorpSessionArchive } from "@/api/sessionArchive.js";
import { DynamicScroller, DynamicScrollerItem } from "vue-virtual-scroller";
import { template, style } from "./template/chatRecord.js";
import "vue-virtual-scroller/dist/vue-virtual-scroller.css";
import WwUser from "@/components/ww-user/index.vue";
import customerDetail from "@/views/member/detail/index.vue";
import wxContact from "@/store/wxContact";
import dayjs from "dayjs";
import JSEncrypt from "jsencrypt";
const props = defineProps({
  customerUserId: {
    type: String,
    default: "",
  },
  memberUserId: {
    type: String,
    default: "",
  },
  chatType: {
    type: String,
    default: "",
  },
});
let dataFrame = ref(null);
const publicKeys = ref([]);
const isDev = true;
const chatRecordRef = ref(null);
const customerId = ref("");
const customerDetailVisible = ref(false);
const { contactName } = storeToRefs(wxContact());
const chatCustomerRecord = ref([]);
const pageSize = ref(10);
const currentPage = ref(1);
const total = ref(0);
const recordLoading = ref(false);
async function toCustomerDetail(customer) {
  customerId.value = customer._id;
  await nextTick();
  customerDetailVisible.value = true;
}
const customerList = ref([]);
const containRef = ref(null);
const listRef = ref(null);
const arrowRef = ref(null);
const { width } = useElementBounding(containRef);
const { width: listWidth } = useElementBounding(listRef);
const { width: arrowWidth } = useElementBounding(arrowRef);
const showPopover = computed(() => width.value - listWidth.value - arrowWidth.value < 40);

const customerName = computed(() => {
  return props.customerUserId && contactName.value[props.customerUserId] ? contactName.value[props.customerUserId] : "";
});
onMounted(async () => {
  await nextTick();
  const factory = ww.createOpenDataFrameFactory();
  dataFrame.value = factory.createOpenDataFrame({
    el: chatRecordRef.value,
    template,
    style,
    data: {
      migList: chatCustomerRecord.value,
    },
    methods: {
      bindscrolltolower() {
        handleScroll();
      },
    },
  });
});

watch(
  () => chatCustomerRecord.value,
  async (val) => {
    if (dataFrame.value) {
      dataFrame.value.setData({
        migList: val,
      });
    }
  },
  { immediate: true }
);
function handleScroll() {
  if (chatCustomerRecord.value.length >= total.value) return;
  recordLoading.value = true;
  currentPage.value++;
  getChatUserRecord();
}
async function getChatUserRecord() {
  if (publicKeys.value.length === 0) await getCorpSessionArchiveInfo();
  const params = {
    pageSize: pageSize.value,
    page: currentPage.value,
    customerUserId: props.customerUserId,
    memberUserId: props.memberUserId,
  };
  const today = dayjs().format("YYYY-MM-DD");
  if (props.chatType === "rate") params.chatDateRange = [today, today];
  const { success, data } = await getCorpMemberChatRecord(params);
  recordLoading.value = false;
  if (success) {
    total.value = data.total;
    let list = data.data.map((item) => {
      item.secretKey = RSA_Decrypt(item);
      item.sendTime = dayjs(item.sendTime * 1000).format("MM-DD HH:mm");
      return item;
    });
    if (currentPage.value === 1) chatCustomerRecord.value = list;
    else chatCustomerRecord.value = chatCustomerRecord.value.concat(list);
  } else {
    total.value = 0;
    chatCustomerRecord.value = [];
  }
}

watch(
  [() => props.customerUserId, () => props.memberUserId],
  ([newCustomerId, newMemberId], [oldCustomerId, oldMemberId]) => {
    if (newCustomerId !== oldCustomerId || newMemberId !== oldMemberId) {
      currentPage.value = 1;
      total.value = 0;
      chatCustomerRecord.value = [];
      getChatUserRecord();
    }
  },
  { immediate: true }
);

function refreshRecord() {
  currentPage.value = 1;
  total.value = 0;
  chatCustomerRecord.value = [];
  getChatUserRecord();
}

async function RSA_Decrypt(item) {
  // Base64 解码
  let key = publicKeys.value.find((i) => i.publicKeyVersion === item.publicKeyVersion);
  const privateKey = key ? key.privateKey : "";
  // 使用 RSA 私钥解密
  let decrypt = new JSEncrypt();
  decrypt.setPrivateKey(privateKey);
  return decrypt.decrypt(item.encryptedSecretKey);
}

async function getCorpSessionArchiveInfo() {
  const { success, data } = await getCorpSessionArchive();
  if (success) {
    publicKeys.value = data.data.publicKeys;
  }
}
</script>
  <style>
iframe {
  width: 100%;
  height: 100%;
}
</style>
  