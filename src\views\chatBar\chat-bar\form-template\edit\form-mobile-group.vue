<template>
  <div class="px-15px py-6px" border-bottom>
    <el-form-item :class="row.required ? 'is-required--right' : ''" :label="row.label">
      <div class="w-full text-right">
        <el-button v-if="value.length < 3" type="primary" size="small" @click="add()">新增</el-button>
      </div>
    </el-form-item>
  </div>
  <div v-for="(item, idx) in value" class="px-15px py-6px" flex items-center border-bottom>
    <el-select class="w-90px flex-shrink-0" :model-value="item.note" @update:model-value="changeNote($event, idx)">
      <el-option v-for="i in mobileRelation" :key="idx + '_' + i.value" :label="i.label" :value="i.value" />
    </el-select>
    <el-input class="flex-grow" :model-value="item.mobile" placeholder="请输入手机号" @update:model-value="changeMobile($event, idx)"></el-input>
    <el-button class="flex-shrink-0" type="danger" text size="small" @click="remove(idx)">
      <span font-14>删除</span>
    </el-button>
  </div>
</template>
<script setup>
import { toRefs } from 'vue';
import { mobileRelation } from "@/baseData";

const emits = defineEmits(['change'])
const props = defineProps({
  row: { type: Object, default: () => ({}) },
  value: { default: () => [] }
})
const { row, value } = toRefs(props);

function add() {
  const val = [...value.value, { note: '', mobile: '' }];
  emits('change', val)
}

function changeMobile(mobile, idx) {
  const val = [...value.value];
  val[idx].mobile = mobile;
  emits('change', val)
}

function changeNote(note, idx) {
  const val = [...value.value];
  val[idx].note = note;
  emits('change', val)
}

function remove(idx) {
  const val = [...value.value];
  val.splice(idx, 1)
  emits('change', val)
}


</script>
<style scoped>
:deep(.el-select .el-input.is-focus .el-input__wrapper),
:deep(.el-select .el-input__wrapper.is-focus) {
  box-shadow: none !important;
}
</style>
