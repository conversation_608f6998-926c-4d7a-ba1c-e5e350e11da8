import { teamStore } from "@/store/team";
import { post } from "./axios";
async function useCorp(data) {
  const res = await post("corp", data);
  return res;
}

export async function getCorpMemberData() {
  const res = await useCorp({
    type: "getCorpMemberData",
    userId: localStorage.getItem("userId"),
    corpId: localStorage.getItem("corpId"),
  });
  return res;
}

export async function getCorpInfo() {
  const res = await useCorp({ type: "getCorpInfo", corpId: localStorage.getItem("corpId") });
  return res;
}

export async function getTeamList() {
  const corpId = localStorage.getItem("corpId");
  const corpUserId = localStorage.getItem("userId");
  const res = await useCorp({ type: "getTeamBymember", corpId, corpUserId });
  return res;
}

export async function getCustomTeamData(data) {
  const res = await useCorp({ type: "getCustomTeamData", ...data });
  return res;
}

export async function getTeamsByUserId(userId) {
  const corpId = localStorage.getItem("corpId");

  const res = await useCorp({
    type: "getTeamBymember",
    corpId,
    corpUserId: userId,
  });
  return res;
}

export async function getTeams(teamIds) {
  const res = await useCorp({ type: "getTeamById", teamIds });
  return res;
}

export async function getCorpTeams(page, pageSize, corpId, name, userId) {
  const res = await useCorp({
    type: "getCorpTeams",
    page,
    pageSize,
    corpId,
    name,
    userId,
  });
  return res;
}

export async function deleteCorpTeam(teamId, _id, corpId) {
  const res = await useCorp({
    type: "deleteCorpTeam",
    teamId,
    _id,
    corpId,
  });
  return res;
}

export async function getTeamById(id, teamIds) {
  const res = await useCorp({ type: "getTeamById", id, teamIds });
  return res;
}
export async function updateTeam(params) {
  const res = await useCorp({ type: "updateTeam", ...params });
  return res;
}

export async function setWork(params) {
  const res = await useCorp({ type: "setWork", ...params });
  return res;
}

export async function getWork(params) {
  const res = await useCorp({
    type: "getWork",
    teamId: teamStore().currentTeamId,
    ...params,
  });
  return res;
}

export async function removeWork(params) {
  const res = await useCorp({ type: "removeWork", ...params });
  return res;
}
export async function closeWorkTodo(params) {
  const res = await useCorp({ type: "closeWorkTodo", ...params });
  return res;
}

export async function getCorpMember(params, pageSize, page) {
  const res = await useCorp({ type: "getCorpMember", params, pageSize, page });
  return res;
}

export async function getCorpMainMember() {
  const res = await useCorp({
    type: "getCorpMainMember",
    corpId: localStorage.getItem("corpId"),
  });
  return res;
}

export async function updateCorpInfo(params) {
  const res = await useCorp({
    type: "updateCorp",
    corpId: localStorage.getItem("corpId"),
    params,
  });
  return res;
}

export async function addCorpDisease({ disease, oldDisease }) {
  const res = await useCorp({
    type: "addCorpDisease",
    corpId: localStorage.getItem("corpId"),
    disease,
    oldDisease,
  });
  return res;
}

export async function getTeamByMainLeaderUserId(params) {
  const res = await useCorp({
    type: "getTeamByMainLeaderUserId",
    corpId: localStorage.getItem("corpId"),
    userId: localStorage.getItem("userId"),
  });
  return res;
}

export async function getTeamLeaderNumByUserId() {
  const res = await useCorp({
    type: "getTeamLeaderNumByUserId",
    corpId: localStorage.getItem("corpId"),
    userId: localStorage.getItem("userId"),
  });
  return res;
}

export async function getRolesMemberList() {
  const res = await useCorp({
    type: "getRolesMemberList",
    corpId: localStorage.getItem("corpId"),
  });
  return res;
}

export async function addCorpMember(params) {
  const res = await useCorp({
    type: "addCorpMember",
    params,
  });
  return res;
}

export async function updateCorpMember(id, params) {
  const res = await useCorp({
    type: "updateCorpMember",
    id,
    corpId: localStorage.getItem("corpId"),
    params,
  });
  return res;
}

export async function getCorpMemberByUserId(userId) {
  const res = await useCorp({
    type: "getCorpMemberByUserId",
    corpId: localStorage.getItem("corpId"),
    userId,
  });
  return res;
}

export async function getSuperAdmin() {
  const res = await useCorp({
    type: "getSuperAdmin",
    corpId: localStorage.getItem("corpId"),
  });
  return res;
}
export async function getCorpMemberAndCustomorCount() {
  const res = await useCorp({
    type: "getCorpMemberAndCustomorCount",
    corpId: localStorage.getItem("corpId"),
  });
  return res;
}

export async function removeAccount(userId) {
  const res = await useCorp({
    type: "removeAccount",
    corpId: localStorage.getItem("corpId"),
    userId,
  });
  return res;
}

export async function getPackageList(page, pageSize, corp_id) {
  const res = await useCorp({
    type: "getPackageList",
    page,
    pageSize,
    corp_id,
  });
  return res;
}

export async function getNotOpenedAccount(userId) {
  const res = await useCorp({
    type: "getNotOpenedAccount",
    corpId: localStorage.getItem("corpId"),
  });
  return res;
}

export async function getOpenedAccount(userId) {
  const res = await useCorp({
    type: "getOpenedAccount",
    corpId: localStorage.getItem("corpId"),
  });
  return res;
}

export async function getCorpMemberJob() {
  const res = await useCorp({
    type: "getCorpMemberJob",
  });
  return res;
}

export async function getAllTeamByCorp() {
  const res = await useCorp({
    type: "getAllTeamByCorp",
    corpId: localStorage.getItem("corpId"),
  });
  return res;
}

// 推荐记录相关API
export async function addRecommendRecord(data) {
  const res = await useCorp({
    type: "addRecommendRecord",
    corpId: localStorage.getItem("corpId"),
    ...data
  });
  return res;
}

export async function getRecommendRecord(data) {
  const res = await useCorp({
    type: "getRecommendRecord",
    corpId: localStorage.getItem("corpId"),
    ...data
  });
  return res;
}

export async function getRecommendRecordList(data) {
  const res = await useCorp({
    type: "getRecommendRecordList",
    corpId: localStorage.getItem("corpId"),
    ...data
  });
  return res;
}

export async function updateRecommendRecord(data) {
  const res = await useCorp({
    type: "updateRecommendRecord",
    corpId: localStorage.getItem("corpId"),
    ...data
  });
  return res;
}

export async function deleteRecommendRecord(data) {
  const res = await useCorp({
    type: "deleteRecommendRecord",
    corpId: localStorage.getItem("corpId"),
    ...data
  });
  return res;
}

export async function getCorpTemplate(templateType, params) {
  const res = await useCorp({
    type: "getCorpTemplate",
    corpId: localStorage.getItem("corpId"),
  });
  return res;
}

export async function updateCorpTemplate(templateType, changeValue) {
  const res = await useCorp({
    type: "updateCorpTemplate",
    corpId: localStorage.getItem("corpId"),
    templateType,
    changeValue,
  });
  return res;
}

export async function updateCorpTemplateStatus(templateType, templateStatus) {
  const res = await useCorp({
    type: "updateCorpTemplateStatus",
    corpId: localStorage.getItem("corpId"),
    templateType,
    templateStatus,
  });
  return res;
}

export async function upDatetransferTime(userId) {
  const res = await useCorp({
    type: "upDatetransferTime",
    corpId: localStorage.getItem("corpId"),
    userId,
  });
  return res;
}
//judgeTransferTime
export async function judgeTransferTime(userId) {
  const res = await useCorp({
    type: "judgeTransferTime",
    corpId: localStorage.getItem("corpId"),
    userId,
  });
  return res;
}

export async function updateMemberRolesByTeam(params, teamId) {
  const res = await useCorp({
    type: "updateMemberRolesByTeam",
    corpId: localStorage.getItem("corpId"),
    teamId,
    params,
  });
  return res;
}

export async function removeCorpMember(id) {
  const res = await useCorp({
    type: "removeCorpMember",
    corpId: localStorage.getItem("corpId"),
    id,
  });
  return res;
}

export async function getCustomCorpInfo(data) {
  const res = await useCorp({ type: "getCustomCorpInfo", ...data });
  return res;
}

export async function getCorpTags() {
  const res = await useCorp({ type: "getCorpTags", corpId: localStorage.getItem("corpId") });
  return res;
}

export async function deleteCorpGroupTag({ groupId }) {
  const res = await useCorp({ type: "deleteCorpGroupTag", corpId: localStorage.getItem("corpId"), groupId });
  return res;
}

export async function updateCorpGroupTag(groupId, params) {
  const res = await useCorp({ type: "updateCorpGroupTag", corpId: localStorage.getItem("corpId"), groupId, ...params });
  return res;
}

export async function addCorpGroupTag(params) {
  const res = await useCorp({ type: "addCorpGroupTag", corpId: localStorage.getItem("corpId"), ...params });
  return res;
}

export async function getCustomMemberInfo(params) {
  const res = await useCorp({ type: "getCustomMemberInfo", corpId: localStorage.getItem("corpId"), ...params });
  return res;
}
export async function deleteRole(id) {
  const res = await useCorp({
    type: "deleteRole",
    corpId: localStorage.getItem("corpId"),
    id,
  });
  return res;
}

export async function getRolesList() {
  const res = await useCorp({
    type: "getRoles",
    corpId: localStorage.getItem("corpId"),
  });
  return res;
}
export async function getRolesByRoleId(roleIds) {
  const res = await useCorp({
    type: "getRolesByRoleId",
    corpId: localStorage.getItem("corpId"),
    roleIds,
  });
  return res;
}

export async function updateRole(id, params) {
  const res = await useCorp({
    type: "updateRole",
    corpId: localStorage.getItem("corpId"),
    id,
    params,
  });
  return res;
}

export async function createRole(params) {
  const res = await useCorp({
    type: "addRole",
    corpId: localStorage.getItem("corpId"),
    params,
  });
  return res;
}

export async function getCurrentTemplate(corpId, templateType) {
  const res = await useCorp({
    type: "getCurrentTemplate",
    corpId,
    templateType,
  });
  return res;
}

export async function getCorpMemberByTeamsAndJobs(data) {
  const res = await useCorp({
    type: "getCorpMemberByTeamsAndJobs",
    ...data,
  });
  return res;
}

export async function getBehaviorStatistics(data) {
  const res = await useCorp({
    type: "getBehaviorStatistics",
    ...data,
  });
  return res;
}

export async function getWechatFriends(data) {
  const res = await useCorp({
    type: "getWechatFriends",
    ...data,
  });
  return res;
}

export async function getAllCorpMember(data) {
  const res = await useCorp({
    type: "getAllCorpMember",
    ...data,
  });
  return res;
}
