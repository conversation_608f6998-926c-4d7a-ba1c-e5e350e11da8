<template>
  <div class="pb-10px border-b border-gray-200 px-15px">
    <el-input v-model.trim="keyword" :placeholder="placeholder" :prefix-icon="Search" clearable />
  </div>
  <div v-show="keyword" v-loading="loading">
    <el-scrollbar :style="scorllHeight" color-normal>
      <div v-for="(i, idx) in searchList" :key="`search_${i._id}`">
        <slot name="searchItem" :item="i" :index="idx"></slot>
      </div>
      <div v-if="searchMore"
        class="mt-10px px-10px flex items-center justify-center py-4px cursor-pointer text-gray-400 hover:text-blue-500"
        @click="loadSearchMore()">
        <div class="flex-grow h-1px bg-gray-200"></div>
        <div class="text-14px ml-10px">加载更多</div>
        <el-icon class="flex-shrink-0 ml-5px mr-10px text-14px">
          <ArrowDownBold />
        </el-icon>
        <div class="flex-grow h-1px bg-gray-200"></div>
      </div>
    </el-scrollbar>
  </div>
  <div v-show="!keyword" v-loading="loading" class="flex" :style="scorllHeight">
    <div class="flex-shrink-0 w-200px border-r border-gray-200">
      <el-scrollbar>
        <classify-list :checked-cate="current" :data="cateList" @change="changeCate($event)" />
      </el-scrollbar>
    </div>
    <el-scrollbar class="flex-grow" color-normal>
      <div v-for="(i, idx) in list" :key="i._id">
        <slot name="selectItem" :item="i" :index="idx"></slot>
      </div>
      <div v-if="more"
        class="mt-10px px-10px flex items-center justify-center py-4px cursor-pointer text-gray-400 hover:text-blue-500"
        @click="loadMore()">
        <div class="flex-grow h-1px bg-gray-200"></div>
        <div class="text-14px ml-10px">加载更多</div>
        <el-icon class="flex-shrink-0 ml-5px mr-10px text-14px">
          <ArrowDownBold />
        </el-icon>
        <div class="flex-grow h-1px bg-gray-200"></div>
      </div>
    </el-scrollbar>
  </div>
</template>
<script setup>
import { computed, onMounted, ref } from 'vue';
import { watchDebounced } from "@vueuse/core";
import useElPagination from "@/hooks/useElPagination";
import useClassifyList from "@/components/classify-list/useClassifyList";

import { Search } from "@element-plus/icons-vue";
import classifyList from "@/components/classify-list/classify-list-side.vue";

const props = defineProps({
  placeholder: { type: String, default: '请搜索...' },
  height: { type: [String, Number], default: '40vh' },
  searchMethod: { type: Function, default: () => { } },
  listMethod: { type: Function, default: () => { } },
  cateMethod: { type: Function, default: () => { } },
})

const loading = ref(false);
const current = ref({});
const keyword = ref('');
const map = ref({});
const options = {
  getList: props.cateMethod,
  callback: getList,
  loading,
};
const scorllHeight = computed(() => {
  if (typeof props.height === 'number') return { height: `${props.height}px` }
  return { height: props.height }
})

const { cateList, getCateList } = useClassifyList(options, false);
const { list, page, pageSize, changePage, more, loadMore } = useElPagination();
const { list: searchList, page: searchPage, pageSize: searchPageSize, changePage: changeSearchPage, more: searchMore, loadMore: loadSearchMore } = useElPagination();

function changeCate(data) {
  if (current.value._id !== data._id) {
    current.value = { ...data };
  }
}

async function getList() {
  loading.value = true;
  const childrenIds = Array.isArray(current.value.childrenIds) ? current.value.childrenIds : [];
  const p = {
    page: page.value,
    pageSize: pageSize.value,
    cateIds: [current.value._id, ...childrenIds],
  };
  const res = await props.listMethod(p);
  list.value = page.value === 1 ? res.list : [...list.value, ...res.list];
  more.value = list.value < res.total;
  loading.value = false;
}

async function search() {
  loading.value = true;
  const p = {
    page: searchPage.value,
    pageSize: searchPageSize.value,
    name: keyword.value.trim()
  };
  const res = await props.searchMethod(p);
  searchList.value = page.value === 1 ? res.list : [...searchList.value, ...res.list];
  searchMore.value = searchList.value < res.total;
  loading.value = false;
}

onMounted(() => {
  getCateList()
})

watchDebounced(current, (n, o) => {
  if (n && n._id) {
    page.value = 1;
    getList();
  }
}, { debounce: 500 });

watchDebounced(keyword, n => {
  if (n.trim() === "") {
    searchList.value = [];
  } else {
    loading.value = true;
    searchPage.value = 1;
    search();
  }
}, { debounce: 500 })
</script>
<style lang="scss" scoped></style>
