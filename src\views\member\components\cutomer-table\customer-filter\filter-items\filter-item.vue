<template>
  <el-popover v-if="type === 'popover'" placement="bottom-start" :offset="0" :width="width" trigger="click">
    <template #reference>
      <div class="flex-shrink-0 flex items-center text-14px h-40px pr-30px cursor-pointer">
        <div class="flex-shrink-0 pl-10px text-gray-500"> {{ label }}：</div>
        <div color-normal class="text-right mx-5px">{{ valueStr}}</div>
        <el-icon class="flex-shrink-0" :size="18">
          <CaretBottom />
        </el-icon>
      </div>
    </template>
    <slot></slot>
  </el-popover>
  <div v-else-if="type === 'base'"
    class="flex-shrink-0 relative flex items-center text-14px h-40px pr-30px cursor-pointer" :style="{width}"
    @click="onClick()">
    <div class="flex-shrink-0 pl-10px text-gray-500"> {{ label }}：</div>
    <div color-normal class="text-right mx-5px">{{ valueStr}}</div>
    <el-icon class="flex-shrink-0" :size="18">
      <CaretBottom />
    </el-icon>
    <slot></slot>
  </div>
  <div v-else-if="type === 'custom'"
    class="flex-shrink-0 relative flex items-center text-14px h-40px pr-30px" :style="{width}">
    <div class="flex-shrink-0 pl-10px text-gray-500"> {{ label }}：</div>
    <slot></slot>
  </div>
</template>
<script setup>
import { computed } from 'vue';

const emits = defineEmits(['onClick'])
const props = defineProps({
  label: { type: String, default: '' },
  type: { type: String, default: 'popover' },
  text: { type: String, default: '' },
  width: { type: [Number, String], default: 280 },
})

const valueStr = computed(() => props.text || '全部');

function onClick() {
  emits('onClick')
}
</script>
<style lang="scss" scoped></style>
