<template>
  <div flex items-center bg-fff border-bottom class="py-5px px-10px" :class="props.isSticky ? 'sticky top-0 z-1' : ''">
    <div class="flex items-center flex-grow mr-10px">
      <user-avatar class="flex-shrink-0 mr-10px" :size="40" :sex="customer.sex" />
      <div class="w-0 flex-grow">
        <div class="flex items-center py-6px">
          <div class="text-16px font-semibold mr-5px truncate">{{ customer.name }}</div>
          <div class="flex-shrink-0 flex items-center text-14px text-gray-500 divide-x divide-gray-500/50">
            <div v-if="customer.sex">{{ customer.sex }}</div>
            <div v-if="customer.age" class="ml-10px pl-10px">{{ customer.age }}岁</div>
          </div>
          <el-tag v-if="customer.relationship" class="customerTag text-12px" type="primary" size="small">
            {{ customer.relationship }}
          </el-tag>
        </div>
        <div class="flex items-center text-12px pb-6px">
          <div class="flex-shrink-0 min-w-60px text-gray-500 mr-5px">当前团队</div>
          <div class="mr-5px truncate">{{ team && team.name ? team.name : '' }}</div>
          <div v-if="teams.length > 1" class="flex-shrink-0" color-primary @click="toggleTeam()">切换</div>
        </div>

      </div>
    </div>
    <div class="pl-15px" @click="visible=true">
      <div class="px-10px py-6px pointer-events-none">
        <el-icon :size="20">
          <MoreFilled />
        </el-icon>
      </div>
    </div>
    <action-sheet :item-list="itemList" :visible="visible" @select="select($event)" @close="visible=false" />

  </div>
</template>
<script setup>
import { computed, ref, toRefs } from 'vue';
import UserAvatar from "@/components/user-avatar";
import ActionSheet from '@/components/weui/action-sheet.vue';

const emits = defineEmits(['add', 'send', 'toggle', 'toggleTeam']);
const props = defineProps({
  customer: { type: Object, default: () => ({}) },
  isSticky: { type: Boolean, default: false },
  team: { type: Object, default: () => ({}) },
  teams: { type: Array, default: () => [] }
})
const { customer, teams, team } = toRefs(props);

function toggle() {
  emits('toggle')
}

function toggleTeam() {
  emits('toggleTeam')
}

const visible = ref(false);
const itemList = [
  { label: '切换客户', fn: toggle },
  { label: '新增客户', fn: () => emits('add') },
  { label: '发送建档链接', fn: () => emits('send') },
  // { label: '转移客户', fn: toggle },
  // { label: '共享客户', fn: toggle },
]

function select(item) {
  visible.value = false
  item.fn()
}

</script>
<style>
.customerTag {
  margin-left: 6px;
  color: #F59A23;
  background-color: #FFEEE6;
  border-color: #F59A23;
}
</style>