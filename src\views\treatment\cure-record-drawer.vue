<template>
  <el-drawer :model-value="visible" title="治疗记录" size="70%" style="--el-drawer-padding-primary: 0" @close="close()">
    <my-layout v-loading="loading">
      <layout-item>
        <div class="p-15px text-14px text-gray-500">以下为“{{ treatmemtItem.customerInfo?.name }}”的治疗记录</div>
      </layout-item>
      <layout-main :scroll="false">
        <el-table stripe border class="h-full" :data="list">
          <el-table-column prop="treatmentTime" :min-width="160" label="治疗日期" />
          <el-table-column prop="projectName" :min-width="160" label="项目" />
          <el-table-column prop="treatmentDeptName" :min-width="120" label="治疗科室" />
          <el-table-column prop="treatmentDoctorUserId" :min-width="120" label="治疗医生">
            <template #default="{ row: { treatmentDoctorUserId } }">
              <ww-user :openid="treatmentDoctorUserId"></ww-user>
            </template>
          </el-table-column>
          <el-table-column prop="userid" :min-width="120" label="配台">
            <template #default="{ row: { assistantDoctors } }">
              <div class="max-w-full h-30px flex-grow">
                <div v-if="assistantDoctors && assistantDoctors.length > 0" class="max-w-full pl-10px h-30px flex items-center truncate" color-666>
                  <div v-for="(i, index) in assistantDoctors" :key="i">
                    <ww-user :openid="i"></ww-user>
                    <span v-if="index !== assistantDoctors.length - 1">、</span>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="treatmentArea" :min-width="120" label="治疗部位" />
          <el-table-column prop="deductUsageCount" :min-width="120" label="治疗数" />
          <el-table-column prop="treatmentRemark" :min-width="120" label="备注" />
          <el-table-column fixed="right" prop="treatmentDoctorUserId" :width="100" label="操作">
            <template #default="{ row }">
              <el-text class="cursor-pointer mr-5px" type="primary" @click.stop="toTreatMentRecord(row)">详情</el-text>
            </template>
          </el-table-column>
        </el-table>
      </layout-main>
    </my-layout>
  </el-drawer>
  <deduct-record :visible="treatmentRecordVisible" @close="closeTreatmentRecordVisible" :project="selectproject" @success="getList" />
</template>
<script setup>
import { ref, watch } from "vue";
import MyLayout, { LayoutMain, LayoutItem } from "@/components/layout";
import { getDeductRecord } from "@/api/treatment-record";
import deductRecord from "./deduct-record.vue";
import dayjs from "dayjs";
import useModal from "@/hooks/useModal";
const emits = defineEmits(["close", "change"]);
const props = defineProps({
  visible: { type: Boolean, default: false },
  treatmemtItem: { type: Object, default: {} },
});
const selectproject = ref({});
const list = ref([]);
const loading = ref(false);
function close() {
  emits("close");
}
const { close: closeTreatmentRecordVisible, show: showTreatmentRecordVisible, visible: treatmentRecordVisible } = useModal(800);

async function getList() {
  const { data } = await getDeductRecord({
    treatmentId: props.treatmemtItem._id,
    page: 1,
    pageSize: 1000,
    deductStatus: ["deducted"],
  });
  list.value = data.list.map((i) => {
    return {
      ...i,
      treatmentTime: dayjs(i.createTime).format("YYYY-MM-DD HH-mm"),
    };
  });
}
async function toTreatMentRecord(row) {
  selectproject.value = row;
  showTreatmentRecordVisible();
}
watch(
  () => props.visible,
  (n) => {
    if (n) {
      getList();
    }
  }
);
</script>
<style scoped>
.triangle-up {
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-bottom: 20px solid currentColor;
}
</style>
