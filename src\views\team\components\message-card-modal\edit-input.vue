<template>
  <div class="min-h-30px" @click="toggleEditing">
    <el-input v-if="isEditing" ref="inputRef" :model-value="value" @update:model-value="change" @blur="handleBlur" />
    <div v-else>{{ value }}</div>
  </div>
</template>

<script setup>
import { ref, nextTick } from 'vue';

const props = defineProps({
  value: { type: String, default: '' }
});
const emits = defineEmits(['update:value'])

const inputRef = ref(null);
const isEditing = ref(false);

function toggleEditing() {
  if (!isEditing.value) {
    isEditing.value = !isEditing.value;
    nextTick(() => inputRef.value.focus());
  }
}

function handleBlur() {
  isEditing.value = false;
}

function change(value) {
  emits('update:value', value);
}
</script>
